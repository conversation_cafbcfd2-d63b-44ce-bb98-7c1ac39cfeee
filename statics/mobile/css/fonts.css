/*
    fonts
*/

@font-face {
    font-family: 'Proxima Nova';
    src: url(../digital_flyer/fonts/proximanova-light-webfont.eot);
    src: url(../digital_flyer/fonts/proximanova-light-webfont.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/proximanova-light-webfont.woff2) format('woff2'),
         url(../digital_flyer/fonts/proximanova-light-webfont.woff) format('woff'),
         url(../digital_flyer/fonts/proximanova-light-webfont.ttf) format('truetype'),
         url(../digital_flyer/fonts/proximanova-light-webfont.svg#proxima_novalight) format('svg');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'Proxima Nova';
    src: url(../digital_flyer/fonts/proximanova-regular-webfont.eot);
    src: url(../digital_flyer/fonts/proximanova-regular-webfont.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/proximanova-regular-webfont.woff2) format('woff2'),
         url(../digital_flyer/fonts/proximanova-regular-webfont.woff) format('woff'),
         url(../digital_flyer/fonts/proximanova-regular-webfont.ttf) format('truetype'),
         url(../digital_flyer/fonts/proximanova-regular-webfont.svg#proxima_novaregular) format('svg');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Proxima Nova';
    src: url(../digital_flyer/fonts/proximanova-semibold-webfont.eot);
    src: url(../digital_flyer/fonts/proximanova-semibold-webfont.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/proximanova-semibold-webfont.woff2) format('woff2'),
         url(../digital_flyer/fonts/proximanova-semibold-webfont.woff) format('woff'),
         url(../digital_flyer/fonts/proximanova-semibold-webfont.ttf) format('truetype'),
         url(../digital_flyer/fonts/proximanova-semibold-webfont.svg#proxima_novasemibold) format('svg');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'HelveticaNeueLTStd-BdCn';
    src: url(../digital_flyer/fonts/33614A_0_0.eot);
    src: url(../digital_flyer/fonts/33614A_0_0.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/33614A_0_0.woff2) format('woff2'),
         url(../digital_flyer/fonts/33614A_0_0.woff) format('woff'),
         url(../digital_flyer/fonts/33614A_0_0.ttf) format('truetype'),
         url(../digital_flyer/fonts/33614A_0_0.svg#wf) format('svg');
}

@font-face {
    font-family: 'Titillium Web';
    src: url(../digital_flyer/fonts/titilliumweb-regular-webfont.eot);
    src: url(../digital_flyer/fonts/titilliumweb-regular-webfont.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/titilliumweb-regular-webfont.woff2) format('woff2'),
         url(../digital_flyer/fonts/titilliumweb-regular-webfont.woff) format('woff'),
         url(../digital_flyer/fonts/titilliumweb-regular-webfont.ttf) format('truetype'),
         url(../digital_flyer/fonts/titilliumweb-regular-webfont.svg#titillium_webregular) format('svg');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Titillium Web';
    src: url(../digital_flyer/fonts/titilliumweb-semibold-webfont.eot);
    src: url(../digital_flyer/fonts/titilliumweb-semibold-webfont.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/titilliumweb-semibold-webfont.woff2) format('woff2'),
         url(../digital_flyer/fonts/titilliumweb-semibold-webfont.woff) format('woff'),
         url(../digital_flyer/fonts/titilliumweb-semibold-webfont.ttf) format('truetype'),
         url(../digital_flyer/fonts/titilliumweb-semibold-webfont.svg#titillium_websemibold) format('svg');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Titillium Web';
    src: url(../digital_flyer/fonts/titilliumweb-bold-webfont.eot);
    src: url(../digital_flyer/fonts/titilliumweb-bold-webfont.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/titilliumweb-bold-webfont.woff2) format('woff2'),
         url(../digital_flyer/fonts/titilliumweb-bold-webfont.woff) format('woff'),
         url(../digital_flyer/fonts/titilliumweb-bold-webfont.ttf) format('truetype'),
         url(../digital_flyer/fonts/titilliumweb-bold-webfont.svg#titillium_webbold) format('svg');
    font-weight: 600;
    font-style: normal;
}
