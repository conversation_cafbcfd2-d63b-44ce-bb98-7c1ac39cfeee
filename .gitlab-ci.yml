stages:
- build
- security
- translations
- test
- others
- review
- release
- deploy

variables:
  IMAGE_NAME_TAG: $CI_PROJECT_NAME:$CI_COMMIT_SHORT_SHA
  GIT_DEPTH: 1
  PUBSUB_GC_PROJECT_ID: test
  FF_SCRIPT_SECTIONS: 1

workflow:
  rules:
  # disable tag pipelines
  - if: $CI_COMMIT_TAG
    when: never
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $CI_DEPLOY_FREEZE == "true"'
    when: never
  - if: '$CI_MERGE_REQUEST_EVENT_TYPE == "merge_train" && $CI_DEPLOY_FREEZE == "true"'
    when: never
  - when: always

include:
# image building
- project: "booksy/templates/ci-templates"
  ref: main
  file:
  - "image/artifact-registry-releaser.yml"
# security scans
- project: "booksy/templates/security-scanning-tools"
  ref: main
  file:
  - "ci-templates/python_backend_core.yml"
# datadog git metadata upload
- project: "booksy/templates/ci-templates"
  ref: main
  file:
  - "observability/datadog_git_metadata.yml"
# otel collector
# - project: "booksy/templates/ci-templates"
#   ref: main
#   file:
#   - "observability/otel_collector.yml"
# argocd prd apps sync
- local: deploy/production-tbd-deploy.yml
# tools deployment
- local: deploy/production-tools.yml


.rules_only_on_feature_exception_manual_release:
  rules:
  - if: $CI_PIPELINE_SOURCE != "schedule" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_COMMIT_MESSAGE =~ /manual-release/
    when: always
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SCHEDULE_DESCRIPTION =~ /batch_release/'
    when: never
  - if: "$CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS"
    when: never
  - if: "$CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH"
    when: always
  - when: never

.set_security_variables:
  rules:
    - if: '$CI_PIPELINE_SOURCE != "schedule" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_COMMIT_MESSAGE =~ /manual-release/'
      when: always
    - if: '$CI_MERGE_REQUEST_EVENT_TYPE == "merge_train" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always
    - when: never

dependency_audit:
  rules:
    - if: '$CI_PIPELINE_SOURCE != "schedule" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_COMMIT_MESSAGE =~ /manual-release/'
      when: always
    - if: '$CI_MERGE_REQUEST_EVENT_TYPE == "merge_train" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always
    - when: never

image_build:
  variables:
    KANIKOBUILDARGS: --build-arg CI_COMMIT_REF_NAME=${CI_COMMIT_REF_NAME} --build-arg CI_COMMIT_SHORT_SHA=${CI_COMMIT_SHORT_SHA} --build-arg CI_COMMIT_SHA=${CI_COMMIT_SHA} --build-arg CI_PROJECT_URL=${CI_PROJECT_URL}
    DOCKERFILE_PATH: deploy/Dockerfile
    KUBERNETES_MEMORY_REQUEST: "4Gi"
    KUBERNETES_MEMORY_LIMIT: "5Gi"

# TESTS
.booksy_tests:
  extends:
  - .rules_only_on_feature_exception_manual_release
  stage: test
  image: $ARTIFACT_REGISTRY$IMAGE_NAME_TAG
  retry:
    max: 2
    #    exit_codes: 66
  timeout: 20m
  variables:
    GIT_STRATEGY: none
    ENVIRONMENT_DOMAIN: localhost:8888
    CONF_YAML: /opt/deploy/conf/api.yaml
    BOOKSY_COUNTRY_CODE: us
    BOOKSY_VARIANT: dev
    BOOKSY_REDIS_DB: 1
    PYTEST: 1
    CI_TEST: 1
    PUBSUB_GC_PROJECT_ID: bks-gcr-dev-1-eu-w1

.booksy_checks:
  extends:
  - .rules_only_on_feature_exception_manual_release
  variables:
    PUBSUB_GC_PROJECT_ID: bks-gcr-dev-1-eu-w1
    GIT_STRATEGY: fetch
    GIT_DEPTH: 1000
  before_script:
  - git fetch --depth $GIT_DEPTH origin $CI_DEFAULT_BRANCH:$CI_DEFAULT_BRANCH


.postgresql_vars:
  variables:
    POSTGRES_DB: booksy-$BOOKSY_COUNTRY_CODE
    POSTGRES_USER: booksy-$BOOKSY_COUNTRY_CODE
    POSTGRES_PASSWORD: booksy
    POSTGRES_HOST_AUTH_METHOD: trust

app_tests:
  extends:
  - .booksy_tests
  - .postgresql_vars
  services:
  - name: ${ARTIFACT_REGISTRY}postgres:16.8
    alias: postgresql # using service in /opt/deploy/conf/api.yaml
    command: ["postgres", "-c", "max_locks_per_transaction=1024", "-c", "max_connections=800", "-c", "shared_buffers=256MB", "-c", "work_mem=16MB", "-c", "random_page_cost=1.1"]
  - name: ${ARTIFACT_REGISTRY}elasticsearch-7:elasticsearch-pytest
    alias: elasticsearch # using service in /opt/deploy/conf/api.yaml
    # command: ["bin/elasticsearch", "-Ediscovery.type=single-node"]
  variables:
    ES_JAVA_OPTS: "-Xms1g -Xmx1g"
    KUBERNETES_CPU_REQUEST: "1"
    KUBERNETES_CPU_LIMIT: "2"
    KUBERNETES_SERVICE_CPU_REQUEST: "1"
    KUBERNETES_SERVICE_CPU_LIMIT: "2"
    KUBERNETES_MEMORY_REQUEST: "3Gi"
    KUBERNETES_MEMORY_LIMIT: "3Gi"
    KUBERNETES_SERVICE_MEMORY_REQUEST: "3Gi"
    KUBERNETES_SERVICE_MEMORY_LIMIT: "3Gi"
  before_script:
  # use local redis to save ram & cpu and have better latency
  - /usr/bin/redis-server /opt/deploy/conf/redis_pytest.conf &
  # wait for elasticsearch
  - timeout 300 bash -c 'while [[ "$(curl -s -o /dev/null -w ''%{http_code}'' http://elasticsearch:9200/_cat/health)" != "200" ]]; do echo waiting for elasticsearch 1 sec && sleep 1; done' || false
  # create initial database for apps
  - PGPASSWORD=postgres /opt/.docker/postgres/init_data/init_db_payments_tests.sh $BOOKSY_COUNTRY_CODE
  - /opt/.docker/postgres/init_data/init_db_drafts_tests.sh $BOOKSY_COUNTRY_CODE
  script:
  - echo $MODE_API
  - /opt/ci_jobs/test_$MODE_API.sh
  parallel:
    matrix:
    - MODE_API: grpc_api
    - MODE_API: public_api
      PARTS: 3
      NUM: [0, 1, 2]
    - MODE_API: private_api
      PARTS: 27
      NUM: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]
  artifacts:
    when: always
    expire_in: 2 weeks
    reports:
      junit: $CI_PROJECT_DIR/report.xml
  coverage: "/^*TOTAL.*s+(d+%)$*/"
  tags:
  - tests-with-services

.migrations_tests:
  extends:
  - .booksy_tests
  - .postgresql_vars
  - .booksy_checks
  services:
  - name: ${ARTIFACT_REGISTRY}postgres:16.8
    alias: postgresql # using service in /opt/deploy/conf/api.yaml
  tags:
  - tests-with-services

check_migrations:
  extends:
  - .migrations_tests
  script:
  - python ci_jobs/check_migrations.py
  allow_failure: false

generate_migration_sql:
  rules:
  - if: $CI_MERGE_REQUEST_EVENT_TYPE == "merge_train"
    allow_failure: false
  - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
    when: never
  - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
    changes:
      paths:
        - "**/*/migrations/**/*"
      compare_to: $CI_DEFAULT_BRANCH
    allow_failure: false
  extends:
  - .migrations_tests
  script:
  - python ci_jobs/generate_migration_sql.py
  retry:
    max: 0 # Disable retries for this job
  tags:
  - check-migrations-genai

migrations_on_empty_db:
  extends:
  - .booksy_tests
  - .postgresql_vars
  - .booksy_checks
  services:
  - name: ${ARTIFACT_REGISTRY}postgres:16.8
    alias: postgresql # using service in /opt/deploy/conf/api.yaml
    command: ["postgres", "-c", "max_locks_per_transaction=1024", "-c", "max_connections=800", "-c", "shared_buffers=2GB"]
  variables:
    KUBERNETES_CPU_REQUEST: "4"
    KUBERNETES_CPU_LIMIT: "4"
    KUBERNETES_MEMORY_REQUEST: "4Gi"
    KUBERNETES_MEMORY_LIMIT: "4Gi"
  before_script:
  - sed -i "s/redis:6379/localhost:6379/g" /opt/conftest.py
  - sed -i "s/redis-email-bulk:6379/localhost:6379/g" /opt/deploy/conf/api.yaml
  - sed -i "s/redis-river:6379/localhost:6379/g" /opt/deploy/conf/api.yaml
  - sed -i "s/redis-celery-backend:6379/localhost:6379/g" /opt/deploy/conf/api.yaml
  - sed -i "s/redis-celery:6379/localhost:6379/g" /opt/deploy/conf/api.yaml
  # dzięki temu nie potrzebuje kolejnego serwisu, który zarequestuje o 3CPU+3GB RAM
  - /usr/bin/redis-server /opt/deploy/conf/redis_pytest.conf &
  script:
  - /opt/ci_jobs/check_migrate_DB.sh && kill -9 $(pgrep redis-server) || (kill -9 $(pgrep redis-server) && exit 99)
  tags:
  - tests-with-services

# CHECKS
checks:
  extends:
  - .booksy_checks
  variables:
    RUNNER_NAME: tests-without-services
  stage: test
  image: $ARTIFACT_REGISTRY$IMAGE_NAME_TAG
  script:
  - |
    if [[ $TEST == "lint-imports" ]]; then
      echo "Running $TEST"
      lint-imports
    elif [[ $TEST == "check_secret_manager.py" ]]; then
      echo "Running $TEST"
      unset GOOGLE_APPLICATION_CREDENTIALS
      unset BASE64_GOOGLE_APPLICATION_CREDENTIALS
      python ci_jobs/$TEST
    else
      echo "Running $TEST"
      python ci_jobs/$TEST
    fi
  parallel:
    matrix:
      - TEST: ["run_black.py --check","check_test_pylint.py","check_celery.py","check_celery_imports.py","check_celery_find_all_tasks_in_modules.py","check_generate_swagger.py", "check_script_runner.py"]
      - TEST: ["check_pylint.py"]
        KUBERNETES_CPU_REQUEST: "4"
        KUBERNETES_CPU_LIMIT: "4"
        KUBERNETES_MEMORY_REQUEST: "4Gi"
        KUBERNETES_MEMORY_LIMIT: "4Gi"
      - TEST: ["lint-imports"]
      - TEST: ["check_secret_manager.py"]
        RUNNER_NAME: security-tools
      - TEST: ["check_data_streaming_contracts.py"]
        RUNNER_NAME: kafka-prd-tests-eu
  allow_failure: true
  tags:
  - $RUNNER_NAME

ownership_assignment:
  extends:
  - .booksy_checks
  stage: test
  image: $ARTIFACT_REGISTRY$IMAGE_NAME_TAG
  script:
  - export GOOGLE_APPLICATION_CREDENTIALS=${CHECK_RESOURCES_GOOGLE_APPLICATION_CREDENTIALS}
  - python /opt/ci_jobs/check_resources_for_datadog.py
  tags:
  - tests-without-services

find-launchdarkly-code-refs:
  extends:
  - .rules_only_on_feature_exception_manual_release
  stage: test
  image:
    name: ${ARTIFACT_REGISTRY}launchdarkly/ld-find-code-refs:2.12.0
    entrypoint: [""]
  script:
  - ld-find-code-refs --accessToken $LD_ACCESS_TOKEN --projKey $LD_PROJECT_KEY --dir $CI_PROJECT_DIR --repoName $CI_PROJECT_NAME --repoUrl $CI_PROJECT_URL --repoType gitlab --branch $CI_COMMIT_REF_NAME --updateSequenceId $CI_PIPELINE_IID --userAgent gitlab-pipeline
  tags:
  - tests-without-services

update_translations:
  stage: translations
  image: $ARTIFACT_REGISTRY$IMAGE_NAME_TAG
  variables:
    USER_NAME: core_translations
    USER_EMAIL: <EMAIL>
    COMMIT_MSG: "feat(translations): internal translations update"
  script:
  - git config --global user.email "${USER_EMAIL}"
  - git config --global user.name "${USER_NAME}"
  - git config http.postBuffer 524288000
  - git remote set-url origin "https://gitlab-ci-token:${BKS_CORE_TRANSLATIONS_TOKEN}@${CI_SERVER_HOST}/booksy/apps/core.git"
  - python manage.py translations --download
  - git commit -am "${COMMIT_MSG}" || echo "No changes, nothing to commit!"
  - git push origin HEAD:${CI_COMMIT_REF_NAME}
  rules:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $CI_PIPELINE_SCHEDULE_DESCRIPTION =~ /update_translations/'
  tags:
  - tests-with-services


metadata_upload:
  rules:
  - if: '$CI_PIPELINE_SOURCE != "schedule" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_COMMIT_MESSAGE =~ /manual-release/'
    when: always
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SCHEDULE_DESCRIPTION =~ /batch_release/'


release:
  needs: null
  stage: release
  variables:
    YQ_VALUES_IMAGE_FILED_NAME: .core.image.tag
    YQ_VALUES_BRANCH_FILED_NAME: .core.image.branch
    PRD_ARGOCD_DEPLOYMENT_CHART_FILES: ./prd-workloads-1/Chart.yaml
    PRD_ARGOCD_DEPLOYMENT_VALUES_FILES: ./prd-workloads-1/values_prd_ar.yaml ./prd-workloads-1/values_prd_au.yaml ./prd-workloads-1/values_prd_br.yaml ./prd-workloads-1/values_prd_ca.yaml ./prd-workloads-1/values_prd_cl.yaml ./prd-workloads-1/values_prd_co.yaml ./prd-workloads-1/values_prd_mx.yaml ./prd-workloads-1/values_prd_it.yaml ./prd-workloads-1/values_prd_nl.yaml ./prd-workloads-1/values_prd_pt.yaml ./prd-workloads-1/values_prd_ie.yaml ./prd-workloads-1/values_prd_za.yaml ./prd-workloads-1/values_prd_fr.yaml ./prd-workloads-1/values_prd_es.yaml ./prd-workloads-1/values_prd_gb.yaml ./prd-workloads-1/values_prd_us.yaml ./prd-workloads-1/values_prd_pl.yaml
    HELM_CHART_VERSION: 1.0.200
  rules:
  - if: '$CI_PIPELINE_SOURCE != "schedule" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_COMMIT_MESSAGE =~ /manual-release/'
    when: manual
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SCHEDULE_DESCRIPTION =~ /batch_release/'
  when: on_success


wait_for_default_branch_pipeline:
  stage: .post
  image: ${ARTIFACT_REGISTRY}booksy-tools-merge-train-handler:v1
  timeout: 1h
  variables:
    JOB_NAME_PREFIX: "ARGOCD-API:sync"
    PYTHONUNBUFFERED: "1"
    GITLAB_TOKEN: $MERGE_TRAINS_TOKEN
    RELEASE_JOB_ACCEPT_STATUS_LIST: SUCCESS,FAILED,SKIPPED
    PREFIXED_JOB_ACCEPT_STATUS_LIST: SUCCESS,FAILED,SKIPPED
  script:
    - python /scripts/hold-until-release-or-prefixed.py
  tags:
    - merge-train-handler
  rules:
  - if: $CI_MERGE_REQUEST_EVENT_TYPE == 'merge_train'
