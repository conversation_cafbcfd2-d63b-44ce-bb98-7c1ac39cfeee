core:
  image:
    repository: us-central1-docker.pkg.dev/bks-ar-pkg/images-prd/core
  replicaCount:
    reportsApi: 3
    admin: 3

  autoscaling:
    # api:
    #   enabled: true
    #   minReplicas: 4
    #   maxReplicas: 8
    #   CPUTargetUtilizationPercentage: 50
    celeryPriority:
      enabled: true
      minReplicas: 1
      maxReplicas: 3
      cpuHpaEnabled: true
      datadogMetricsEnabled: true
      CPUTargetUtilizationPercentage: 25
      datadogTargetMetricAverageValue: 5000
    celeryPush:
      enabled: true
      minReplicas: 1
      maxReplicas: 2
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 1000
    celeryRegular:
      enabled: true
      minReplicas: 1
      maxReplicas: 3
      cpuHpaEnabled: true
      CPUTargetUtilizationPercentage: 25
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 5000
    celerySegment:
      enabled: true
      minReplicas: 1
      maxReplicas: 2
      cpuHpaEnabled: true
      CPUTargetUtilizationPercentage: 25
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 5000

  variables:
    apiUwsgiLazyApp: False
    apiUwsgiForkHooks: True
    apiUwsgiRssReload: True
    booksyVariant: live
    booksyCountryCode: ca
    booksyRedisDB: 2
    subdomainHost: booksy-subdomains-api-grpc.subdomains.svc.cluster.local:9099
    launchDarklyProxyURL: http://launch-darkly.launch-darkly.svc.cluster.local:8030
    authHost: auth-server.auth.svc.cluster.local:8010
    grpcClientProxyHost: http://grpc-ca-client-proxy.grpc-ca.svc.cluster.local:9911
    postgresql:
      masterHost: bks-prd-2-us-c1-pgcat-cross-mesh-load-balancer.svc.cluster.local
      paymentsHost: *********
      draftsHost: **********
      LBslaveHosts: bks-prd-2-us-c1-pgcat-cross-mesh-load-balancer.svc.cluster.local
      LBslaveReportsHosts: postgres-lb-reports-replica-cross-mesh.svc.cluster.local
      pgcat:
        enabled: true
    elasticsearch:
      host: elasticsearch-load-balancer-cross-mesh.svc.cluster.local
      port: 9200
      numerOfReplicas: 2
      numerOfShards: 3
    redis:
      celeryBackendHost: bks-prd-2-us-c1-v2-redis-celery.svc.cluster.local
      celeryBeatHost: bks-prd-2-us-c1-v2-redis-celery.svc.cluster.local
      celeryBrokerHost: bks-prd-2-us-c1-v2-redis-celery.svc.cluster.local
      celeryBulkCacheHost: bks-prd-2-us-c1-v2-redis-email-bulk.svc.cluster.local
      riverRedisHost: bks-prd-2-us-c1-redis-river.svc.cluster.local
      enableThrottling: true
      throttlingRedisHost: bks-prd-2-us-c1-redis-throttling.svc.cluster.local
      enableRedisFifo: true
      redisFifoHost: bks-prd-2-us-c1-v2-redis-email-bulk.svc.cluster.local
      subdomainsRedisHost: bks-prd-2-us-c1-redis-core-subdomains.svc.cluster.local
      simplifiedBookingRedisHost: bks-prd-2-us-c1-redis-simplified-booking.svc.cluster.local

    boostClaims:
      GCSProjectID: bks-prd-2-us-c1

    workloadIdentity:
      projectID: bks-prd-workloads-1
      
    captcha:
      projectID: bks-prd-workloads-1

    kafka:
      brokerBootstrapServers: seed-312ca77d.csuvtaaj0bofp9ktsi70.byoc.prd.cloud.redpanda.com:9092
      schemaRegistryUrl: https://schema-registry-8c152181.csuvtaaj0bofp9ktsi70.byoc.prd.cloud.redpanda.com:30081

      resourcesSecretManager:
        projectID: bks-kafka-prd-2-us-c1
