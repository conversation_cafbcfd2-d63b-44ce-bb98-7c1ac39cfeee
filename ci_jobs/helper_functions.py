import os
from pathlib import Path
from subprocess import check_output


def get_changed_files(with_blacklisted=False, with_migrations=False, on_main=False):
    """
    Return all changed files if files is .py and do not have test pattern.
    - on_main=True returns new files on main vs its previous commit
    """
    if not on_main:
        init_branch_commit = (
            check_output(  # nosemgrep: bandit.B603
                ['git', 'merge-base', 'HEAD', 'origin/main'], shell=False
            )
            .decode()
            .strip('\n ')
        )
    else:
        init_branch_commit = 'HEAD~1'
    command = ['git', 'diff', '--name-status', init_branch_commit]
    files = check_output(command, shell=False).decode().split('\n')  # nosemgrep: bandit.B603
    # region filter out non py files
    files = filter(
        is_py_non_migration_file if not with_migrations else is_py_file,
        files,
    )
    # endregion
    # region filter out deleted files
    files = map(lambda filepath: filepath.split('\t'), files)
    files = map(
        lambda mode_filepath: mode_filepath[-1] if mode_filepath[0] != 'D' else None,
        files,
    )
    files = filter(lambda file: file and os.path.exists(file), files)
    # endregion
    # region filter blacklisted
    if not with_blacklisted:
        files = filter(
            lambda filepath: filepath not in BLACKLISTED_FILES,
            files,
        )
    # endregion
    files = list(files)
    return files


def get_all_files(paths):
    if paths is None:
        return get_changed_files(with_blacklisted=False, with_migrations=False, on_main=False)

    all_files = list(filter(os.path.isfile, paths))

    for dir_path in filter(os.path.isdir, paths):
        for entry in os.walk(dir_path):
            all_files.extend(os.path.join(entry[0], filename) for filename in entry[2])

    return all_files


def is_test_file(file_path):
    path = Path(file_path)
    is_in_test_folder = any(
        (
            'tests' in path.parts,
            'tests_scripts' in path.parts,
        )
    )
    has_valid_suffix = path.parts[-1].startswith('test_')
    return file_path.endswith('conftest.py') or all(
        (
            path.is_file(),
            is_in_test_folder,
            has_valid_suffix,
            not file_path.endswith(".csv"),
        )
    )


def is_grpc_pb_file(file_path):
    return file_path.endswith(('_pb2.py', '_pb2_grpc.py'))


def is_py_file(file_path):
    return file_path.endswith('.py')


def is_migration_file(file_path):
    return '/migrations/' in file_path


def is_py_non_migration_file(file_path):
    return is_py_file(file_path) and not is_migration_file(file_path)


def process_error(error):
    """Print output lines for CalledProcessError"""
    for line in error.output.decode('utf-8').split('\n'):
        print(line)


def get_new_migrations():
    # Get a list of new migrations.
    on_main = os.getenv('CI_COMMIT_REF_NAME') == 'main'
    changed_files = get_changed_files(with_blacklisted=True, with_migrations=True, on_main=on_main)
    migration_files = filter(is_migration_file, changed_files)
    return list(map(clean_migration_path, migration_files))


def clean_migration_path(filepath):
    if not os.path.exists(filepath):
        return [None, None]
    filepath = (os.path.splitext(filepath)[0]).split(os.sep)
    filepath.remove('migrations')
    filepath.remove('webapps')
    if 'infrastructure' in filepath:
        filepath.remove('infrastructure')

    return filepath


# Create blacklisted
# find . -type f -name "*.py" ! -path "*/migrations/*"
# ./service/booking/customer_booking.py and ./service/booking/multibooking.py are blacklisted
# by Zbigniew Bomert
_RAW_BLACKLISTED = """
./cliapps/business/__init__.py
./cliapps/business/business_cleanup.py
./cliapps/business/business_hidden_in_search_other_category.py
./cliapps/business/business_sms_invitation.py
./cliapps/business/import_treatments.py
./cliapps/business/new_treatment_importer.py
./cliapps/business/pop_up_for_business_with_max_lead_time.py
./cliapps/business/populate_created_in_bsnss_cstmr_info.py
./cliapps/business/regenerate_deeplinks.py
./cliapps/business/set_business_timezones.py
./cliapps/business/subscriptions.py
./cliapps/business/update_payment_source.py
./cliapps/business/b_listing.py
./cliapps/customer_cards/__init__.py
./cliapps/customer_cards/import_biz_customers.py
./cliapps/customer_cards/import_customers.py
./cliapps/customer_cards/import_some_customers.py
./cliapps/customer_cards/import_versum_customers.py
./cliapps/customer_cards/send_invites_for_business.py
./cliapps/dangerous/__init__.py
./cliapps/dangerous/clean_db.py
./cliapps/dangerous/customer_booking_report.py
./cliapps/dangerous/delete_bci.py
./cliapps/dangerous/double_booking.py
./cliapps/dangerous/no_resource_bookings_report.py
./cliapps/dangerous/rename_business_categories.py
./cliapps/dangerous/replay_business_post_appointments.py
./cliapps/dangerous/replay_full_log.py
./cliapps/dangerous/replay_full_log_transactions.py
./cliapps/dangerous/trim_database.py
./cliapps/es/__init__.py
./cliapps/es/cust_reindex.py
./cliapps/es/delete_extra.py
./cliapps/es/img_reindex.py
./cliapps/es/structure_reindex.py
./cliapps/es/check.py
./cliapps/es/main.py
./cliapps/es/fix.py
./cliapps/es/indices.py
./cliapps/es/fast_reindex.py
./cliapps/es/reindex.py
./cliapps/pos/__init__.py
./cliapps/pos/pos_sms_sender.py
./cliapps/pos/validate_pos.py
./cliapps/regions/__init__.py
./cliapps/regions/average_coordinates.py
./cliapps/regions/fill_display_names.py
./cliapps/regions/geopostcodes_import.py
./cliapps/regions/import_colombia_regions.py
./cliapps/regions/import_ireland_regions.py
./cliapps/regions/import_missing_zipcodes_pl.py
./cliapps/regions/import_regions_utils.py
./cliapps/regions/match_businesses_with_regions.py
./cliapps/regions/update_lonlat.py
./cliapps/regions/import_gb_zipcodes.py
./cliapps/snippets/restore/__init__.py
./cliapps/snippets/restore/bookings_error_report.py
./cliapps/snippets/restore/broken_bookings_report.py
./cliapps/snippets/restore/clean_bookings_error_log.py
./cliapps/snippets/restore/filter_receipts_vs_db.py
./cliapps/snippets/restore/filter_timeoffs_vs_db.py
./cliapps/snippets/restore/handle_restore_receipts_files.py
./cliapps/snippets/restore/log_counter.py
./cliapps/snippets/restore/log_parser_restore.py
./cliapps/snippets/restore/log_parser_restore_receipts.py
./cliapps/snippets/restore/restore_biz_booking.py
./cliapps/snippets/restore/validate_not_created_bookings.py
./cliapps/snippets/__init__.py
./cliapps/snippets/add_bizes_to_brows_and_lashes.py
./cliapps/snippets/add_reserved_time.py
./cliapps/snippets/basic_stats.py
./cliapps/snippets/biz_registration_source_guess.py
./cliapps/snippets/corrupted_templates.py
./cliapps/snippets/create_brows_and_lashes.py
./cliapps/snippets/deactivate_service_without_resources.py
./cliapps/snippets/es_performance_test.py
./cliapps/snippets/full_log_parser.py
./cliapps/snippets/initialize_segment_business_id.py
./cliapps/snippets/json_log_to_csv.py
./cliapps/snippets/log_parser.py
./cliapps/snippets/migrate_business.py
./cliapps/snippets/migrate_deleted_subbookings.py
./cliapps/snippets/migrate_notification_history_phone_email.py
./cliapps/snippets/mp2_instagram_link_workaround.py
./cliapps/snippets/phone_numbers.py
./cliapps/snippets/print_categories.py
./cliapps/snippets/repeating_get_conflict_info.py
./cliapps/snippets/repeating_saving_time_problems.py
./cliapps/snippets/segment_identify_missing_attributes.py
./cliapps/snippets/es_simple_response_profile.py
./cliapps/snippets/send_pos_educational_mail.py
./cliapps/snippets/sync_braintree_refunded_transactions.py
./cliapps/snippets/recalculate_ranks.py
./cliapps/snippets/fix_trial_till.py
./cliapps/translators/__init__.py
./cliapps/translators/scenarios_to_onesky.py
./cliapps/translators/treatments.py
./cliapps/deploy/__init__.py
./cliapps/deploy/media_nfs_check.py
./cliapps/user/__init__.py
./cliapps/user/fillup_gender.py
./cliapps/user/token_cleanup.py
./cliapps/__init__.py
./cliapps/image/__init__.py
./cliapps/image/clean_empty_images.py
./cliapps/image/fast_s3check.py
./cliapps/image/s3check.py
./cliapps/image/s3check_photos.py
./cliapps/adyen_encrypt/encrypted_data.py
./cliapps/adyen_encrypt/__init__.py
./cliapps/config_checker.py
./cliapps/other.py
./cliapps/post_upgrade_tests.py
./cliapps/primary_category_form.py
./cliapps/scenarios_for_translators.py
./cliapps/redis_test.py
./cliapps/check_stats.py
./cliapps/cron_from_celerybeat.py
./cliapps/market_pay/invite.py
./cliapps/market_pay/notifications.py
./domain_services/booking/scripts/compare_requirements.py
./lib/booksy_sms/brazil.py
./lib/booksy_sms/evox.py
./lib/booksy_sms/routesms.py
./lib/booksy_sms/smsapi.py
./lib/booksy_sms/twilio_sms.py
./lib/booksy_sms/devel.py
./lib/booksy_sms/utils.py
./lib/booksy_sms/__init__.py
./lib/__init__.py
./lib/abc.py
./lib/admin_helpers.py
./lib/capping.py
./lib/debug.py
./lib/forms.py
./lib/gdpr_descriptions.py
./lib/language_helper.py
./lib/logs_reader.py
./lib/markup.py
./lib/pdf_render.py
./lib/post_file_encode.py
./lib/shell.py
./lib/timezone_hours.py
./lib/tokens.py
./lib/url_shortener.py
./lib/celery_tools.py
./lib/db.py
./lib/fraud.py
./lib/datetime_utils.py
./lib/jinja_renderer.py
./lib/models.py
./lib/ranges.py
./lib/time_24_hour.py
./lib/locks.py
./lib/rivers.py
./lib/spreadsheet.py
./lib/enums.py
./lib/queryset.py
./merger_grpc/proto/appointment_pb2.py
./merger_grpc/proto/appointment_pb2_grpc.py
./service/booking/customer_booking.py
./service/booking/multibooking.py
./service/management/commands/__init__.py
./service/management/commands/rm_test_businesses.py
./service/management/commands/runapi.py
./service/management/commands/s.py
./service/management/commands/show_settings.py
./service/management/commands/generate_swagger.py
./service/management/commands/makemessages.py
./service/management/__init__.py
./service/marketing/__init__.py
./service/marketing/expected_profit.py
./service/marketing/unsubscribe.py
./service/marketing/templates.py
./service/mixins/master_api.py
./service/mixins/paginator.py
./service/mixins/validation.py
./service/pos/__init__.py
./service/pos/registers.py
./service/pos/business_commissions.py
./service/pos/business_dashboard.py
./service/pos/customer_transactions.py
./service/pos/payment_methods.py
./service/pos/business_products.py
./service/pos/business_pos.py
./service/pos/business_transactions.py
./service/purchase/brain_tree/__init__.py
./service/purchase/brain_tree/business.py
./service/purchase/brain_tree/webhook.py
./service/purchase/invoices.py
./service/purchase/google/__init__.py
./service/purchase/google/business.py
./service/purchase/google/webhook.py
./service/purchase/apple/__init__.py
./service/purchase/apple/business.py
./service/purchase/apple/webhook.py
./service/purchase/__init__.py
./service/search/__init__.py
./service/search/utils.py
./service/search/region.py
./service/search/business.py
./service/test/flow_test.py
./service/experiment/__init__.py
./service/experiment/old_experiment.py
./service/feedback/__init__.py
./service/feedback/feedback.py
./service/segment/__init__.py
./service/segment/counters.py
./service/renting_venue/__init__.py
./service/renting_venue/change_details.py
./service/__init__.py
./service/marketplace/__init__.py
./service/marketplace/marketplace.py
./service/notification/__init__.py
./service/notification/webhook.py
./service/c2b_referral/__init__.py
./service/c2b_referral/serializers.py
./service/c2b_referral/get_c2b_data.py
./service/r_and_d/user_contacts.py
./service/df_creator/__init__.py
./service/df_creator/digital_flyers.py
./service/market_pay/__init__.py
./service/market_pay/account_holder.py
./service/sequencing_number/__init__.py
./service/sequencing_number/documents.py
./service/error.py
./service/exceptions.py
./service/signals.py
./service/images/__init__.py
./service/images/hints.py
./service/images/helpers.py
./service/images/serializers.py
./service/survey/__init__.py
./service/survey/survey.py
./service/models.py
./service/serializers.py
./service/dashboard.py
./service/photo.py
./service/resources.py
./service/run.py
./webapps/adyen/consts/__init__.py
./webapps/adyen/consts/currency_codes.py
./webapps/adyen/consts/oper_result.py
./webapps/adyen/__init__.py
./webapps/adyen/admin.py
./webapps/adyen/adyen_requests.py
./webapps/adyen/flow.py
./webapps/adyen/helpers.py
./webapps/adyen/models.py
./webapps/adyen/notifications.py
./webapps/adyen/reports.py
./webapps/adyen/views.py
./webapps/business/forms/business.py
./webapps/business/forms/__init__.py
./webapps/business/forms/admin.py
./webapps/business/forms/booking.py
./webapps/business/forms/fields.py
./webapps/business/forms/resource.py
./webapps/business/management/commands/__init__.py
./webapps/business/management/commands/trust_clients.py
./webapps/business/management/__init__.py
./webapps/billing/billing_cycle_switch.py
./webapps/billing/enums.py
./webapps/billing/forms.py
./webapps/billing/payment_processor.py
./webapps/billing/retry_charge.py
./webapps/billing/services/boost.py
./webapps/billing/services/charge.py
./webapps/billing/subscription_creator.py
./webapps/billing/admin_views.py
./webapps/stripe_app/services/payment_method.py
./webapps/celery/__init__.py
./webapps/celery/models.py
./webapps/celery/tasks.py
./webapps/elasticsearch/__init__.py
./webapps/elasticsearch/admin.py
./webapps/elasticsearch/apps.py
./webapps/elasticsearch/models.py
./webapps/elasticsearch/elastic.py
./webapps/elasticsearch/tasks.py
./webapps/notification/management/commands/__init__.py
./webapps/notification/management/commands/load_test_notification_history_data.py
./webapps/notification/management/commands/send_push.py
./webapps/notification/management/__init__.py
./webapps/notification/scenarios/scenarios_b_listing.py
./webapps/notification/scenarios/scenarios_noshowprotection.py
./webapps/notification/scenarios/base.py
./webapps/notification/scenarios/scenarios_access_rights.py
./webapps/notification/scenarios/scenarios_booking.py
./webapps/notification/scenarios/scenarios_booking_mixin.py
./webapps/notification/scenarios/scenarios_business.py
./webapps/notification/scenarios/scenarios_r_and_d.py
./webapps/notification/scenarios/scenarios_review.py
./webapps/notification/scenarios/scenarios_user.py
./webapps/notification/scenarios/sms_limits.py
./webapps/notification/scenarios/scenarios_account.py
./webapps/notification/scenarios/scenarios_booking_variables.py
./webapps/notification/scenarios/__init__.py
./webapps/notification/__init__.py
./webapps/notification/tasks/__init__.py
./webapps/notification/tasks/push.py
./webapps/notification/email_mocks/__init__.py
./webapps/notification/email_mocks/email_list.py
./webapps/notification/email_mocks/mock_email_params.py
./webapps/notification/email_mocks/mock_objects.py
./webapps/notification/devel.py
./webapps/notification/enums.py
./webapps/notification/forms.py
./webapps/notification/push.py
./webapps/notification/serializers.py
./webapps/notification/tools.py
./webapps/pos/enums/__init__.py
./webapps/pos/enums/bank_account_type.py
./webapps/pos/enums/compatibilities.py
./webapps/pos/enums/receipt_status.py
./webapps/pos/management/__init__.py
./webapps/pos/validators.py
./webapps/pos/apps.py
./webapps/pos/payout.py
./webapps/pos/receivers.py
./webapps/pos/serializers.py
./webapps/pos/tests/pos_refactor/pba/base.py
./webapps/pos/tests/pos_refactor/prepayment/base.py
./webapps/purchase/serializers/google.py
./webapps/purchase/serializers/__init__.py
./webapps/purchase/tasks/google.py
./webapps/purchase/tasks/mrr_reports.py
./webapps/purchase/tasks/segment.py
./webapps/purchase/tasks/webhook.py
./webapps/purchase/tasks/__init__.py
./webapps/purchase/tasks/reports.py
./webapps/purchase/tasks/apple.py
./webapps/purchase/tasks/renewing_subscription.py
./webapps/purchase/tasks/offline.py
./webapps/purchase/tasks/brain_tree.py
./webapps/purchase/__init__.py
./webapps/purchase/offline.py
./webapps/purchase/query_sets.py
./webapps/purchase/segment_singals.py
./webapps/purchase/admin.py
./webapps/purchase/braintree/__init__.py
./webapps/purchase/braintree/purchase_helpers.py
./webapps/purchase/braintree/rewards.py
./webapps/purchase/braintree/utils.py
./webapps/purchase/braintree/serializers.py
./webapps/purchase/views.py
./webapps/purchase/c2b_referral_signals.py
./webapps/purchase/models.py
./webapps/purchase/utils.py
./webapps/registrationcode/__init__.py
./webapps/registrationcode/admin.py
./webapps/registrationcode/forms.py
./webapps/registrationcode/models.py
./webapps/registrationcode/serializers.py
./webapps/registrationcode/baker_recipes.py
./webapps/statistics/management/commands/__init__.py
./webapps/statistics/management/commands/aggregate_statistics.py
./webapps/statistics/management/commands/send_statistics_report.py
./webapps/statistics/management/__init__.py
./webapps/statistics/__init__.py
./webapps/statistics/models.py
./webapps/statistics/serializers.py
./webapps/statistics/spreadsheets/__init__.py
./webapps/statistics/spreadsheets/appointments_by_hour_report.py
./webapps/statistics/spreadsheets/cash_registers_report.py
./webapps/statistics/spreadsheets/discounts_report.py
./webapps/statistics/spreadsheets/finance_summary_report.py
./webapps/statistics/spreadsheets/helpers.py
./webapps/statistics/spreadsheets/sales_and_tax_report.py
./webapps/statistics/spreadsheets/top_reports.py
./webapps/statistics/spreadsheets/transactions_reports.py
./webapps/statistics/spreadsheets/staff_working_hours_occupancy.py
./webapps/statistics/spreadsheets/sales_payout_summary_report.py
./webapps/statistics/tools.py
./webapps/statistics/reports.py
./webapps/statistics/reports_collection.py
./webapps/statistics/tasks.py
./webapps/stats_and_reports/reports/sales_reports/sales_summary.py
./webapps/subdomain/__init__.py
./webapps/subdomain/models.py
./webapps/subdomain/admin.py
./webapps/register/__init__.py
./webapps/register/admin.py
./webapps/register/models.py
./webapps/register/serializers.py
./webapps/register/tasks.py
./webapps/experiment/__init__.py
./webapps/experiment/management/__init__.py
./webapps/experiment/management/commands/__init__.py
./webapps/experiment/management/commands/export_experiment.py
./webapps/experiment/admin.py
./webapps/experiment/decorators.py
./webapps/experiment/models.py
./webapps/experiment/tasks.py
./webapps/experiment/utils.py
./webapps/feedback/__init__.py
./webapps/feedback/models.py
./webapps/feedback/serializers.py
./webapps/feedback/tasks.py
./webapps/feedback/admin.py
./webapps/feeds/google/api.py
./webapps/feeds/google/grpc_service/__init__.py
./webapps/feeds/google/ingress/bridge/__init__.py
./webapps/feeds/google/ingress/bridge/ext_maps_booking_partner_v3_pb2
./webapps/feeds/google/ingress/bridge/ext_maps_booking_partner_v3_pb2_grpc.py
./webapps/feeds/google/ingress/bridge/google_rest_api_pb2
./webapps/feeds/google/ingress/bridge/google_rest_api_pb2_grpc.py
./webapps/feeds/google/international.py
./webapps/feeds/google/itemizer.py
./webapps/feeds/google/proto_py/__init__.py
./webapps/feeds/google/proto_py/booking_service_pb2.py
./webapps/feeds/google/proto_py/booking_service_pb2_grpc.py
./webapps/feeds/google/proto_py/definitions_pb2.py
./webapps/feeds/google/proto_py/health_pb2.py
./webapps/feeds/google/proto_py/health_pb2_grpc.py
./webapps/feeds/google/proto_py/restapi_pb2.py
./webapps/feeds/google/rest_api_itemizer.py
./webapps/feeds/instagram/itemizer.py
./webapps/feeds/instagram/tasks.py
./webapps/segment/__init__.py
./webapps/segment/models.py
./webapps/segment/tasks.py
./webapps/segment/utils.py
./webapps/kill_switch/__init__.py
./webapps/kill_switch/admin.py
./webapps/kill_switch/utils.py
./webapps/admin_query_fields/templatetags/__init__.py
./webapps/admin_query_fields/templatetags/search_form_query_fields.py
./webapps/admin_query_fields/__init__.py
./webapps/admin_query_fields/models.py
./webapps/admin_query_fields/admin_helpers.py
./webapps/pattern/__init__.py
./webapps/__init__.py
./webapps/r_and_d/__init__.py
./webapps/r_and_d/utils.py
./webapps/r_and_d/models.py
./webapps/r_and_d/tasks.py
./webapps/c2b_referral/__init__.py
./webapps/c2b_referral/c2b_referral_signals.py
./webapps/c2b_referral/models.py
./webapps/c2b_referral/admin.py
./webapps/c2b_referral/tasks.py
./webapps/c2b_referral/utils.py
./webapps/partners/__init__.py
./webapps/partners/models.py
./webapps/survey/__init__.py
./webapps/survey/apps.py
./webapps/survey/admin.py
./webapps/survey/models.py
./webapps/conftest.py
./webapps/allauth_google/__init__.py
./webapps/allauth_google/account_adapter.py
./webapps/allauth_google/admin.py
./webapps/allauth_google/provider.py
./webapps/allauth_google/urls.py
./webapps/consts.py
./webapps/suit/config.py
./webapps/suit/utils.py
./webapps/suit/templatetags/suit_list.py
./webapps/suit/templatetags/suit_menu.py
./webapps/suit/templatetags/suit_tags.py
./templatetags/__init__.py
./templatetags/booksy_admin_menu.py
./deploy/envVarsFromFile.py
./manage.py
./versions.py
./wsgi.py
./.docker/empty_loop.py
./numpy_ranges/api_utils.py
./numpy_ranges/time_slots/scratch_4.py
./numpy_ranges/slotter.py
./urls.py
./conftest.py
"""
BLACKLISTED_FILES = {
    line.strip('./ ') for line in _RAW_BLACKLISTED.split('\n') if line.endswith('.py')
}
