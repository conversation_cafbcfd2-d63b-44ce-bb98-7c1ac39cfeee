from unittest.mock import patch

import elasticsearch.exceptions
import pytest

from cliapps.es.fix import main as fix
from lib.elasticsearch.consts import (
    ESDocType,
    ESIndex,
)
from lib.feature_flag.feature import UseExplicitRoutingWhenDeletingDocumentsFlag
from lib.feature_flag.old_experiment import ElasticsearchWithoutDeleteByQueryFlag
from lib.tests.utils import override_eppo_feature_flag, override_feature_flag
from webapps.business.baker_recipes import resource_recipe
from webapps.business.elasticsearch import BusinessDocument, ResourceDocument
from webapps.business.models import ResourceType


@pytest.mark.django_db
@override_eppo_feature_flag({UseExplicitRoutingWhenDeletingDocumentsFlag.flag_name: False})
def test_nothing_to_fix(clean_index_function_fixture):
    clean_index_function_fixture(ESIndex.BUSINESS)
    resource = resource_recipe.make(type=ResourceType.STAFF)
    resource.reindex(refresh_index=True)

    with (
        patch('lib.elasticsearch.document.Document.delete_extra') as mock_delete_extra,
        patch('lib.elasticsearch.document.Document.reindex') as mock_reindex,
    ):
        fix(document_types=[ESDocType.RESOURCE], delete=True, reindex=True)

    mock_delete_extra.assert_called_once_with([])
    mock_reindex.assert_called_once_with(ids=[])


@pytest.mark.django_db
@override_eppo_feature_flag({UseExplicitRoutingWhenDeletingDocumentsFlag.flag_name: True})
def test_nothing_to_fix__use_explicit_routing(clean_index_function_fixture):
    clean_index_function_fixture(ESIndex.BUSINESS)
    resource = resource_recipe.make(type=ResourceType.STAFF)
    resource.reindex(refresh_index=True)

    with (
        patch(
            'lib.elasticsearch.document.Document.delete_extra_with_routing'
        ) as mock_delete_extra_with_routing,
        patch('lib.elasticsearch.document.Document.reindex') as mock_reindex,
    ):
        fix(document_types=[ESDocType.RESOURCE], delete=True, reindex=True)

    mock_delete_extra_with_routing.assert_called_once_with([])
    mock_reindex.assert_called_once_with(ids=[])


@pytest.mark.django_db
@pytest.mark.parametrize('flag_value', [True, False])
def test_delete_extra_documents(flag_value, clean_index_function_fixture):
    index = clean_index_function_fixture(ESIndex.BUSINESS)
    business_doc = BusinessDocument(
        id=1,
        _id='business:1',
        name='Business',
        active=True,
        visible=True,
        join='business',
    )
    business_doc.save()

    resource_ids = [1, 2]
    for resource_id in resource_ids:
        ResourceDocument(
            id=resource_id,
            business_id=business_doc.id,
            active=True,
            visible=True,
            type=ResourceType.STAFF.value,
            join={
                'name': 'resource',
                'parent': business_doc.meta.id,
            },
            meta={
                'id': f'resource:{resource_id}',
                'routing': business_doc.id,
            },
        ).save()

    index.refresh()

    with (
        override_eppo_feature_flag(
            {UseExplicitRoutingWhenDeletingDocumentsFlag.flag_name: flag_value}
        ),
        override_feature_flag({ElasticsearchWithoutDeleteByQueryFlag.flag_name: flag_value}),
        patch('lib.elasticsearch.document.Document.reindex') as mock_reindex,
    ):
        fix(document_types=[ESDocType.RESOURCE], delete=True, reindex=True)

    mock_reindex.assert_called_once_with(ids=[])

    for resource_id in resource_ids:
        with pytest.raises(elasticsearch.exceptions.NotFoundError):
            ResourceDocument.get(resource_id)


@pytest.mark.django_db
@override_eppo_feature_flag({UseExplicitRoutingWhenDeletingDocumentsFlag.flag_name: True})
def test_delete_extra_documents__use_explicit_routing(clean_index_function_fixture):
    index = clean_index_function_fixture(ESIndex.BUSINESS)
    business_doc = BusinessDocument(
        id=1,
        _id='business:1',
        name='Business',
        active=True,
        visible=True,
        join='business',
    )
    business_doc.save()

    resource_ids = [1, 2]
    for resource_id in resource_ids:
        ResourceDocument(
            id=resource_id,
            business_id=business_doc.id,
            active=True,
            visible=True,
            type=ResourceType.STAFF.value,
            join={
                'name': 'resource',
                'parent': business_doc.meta.id,
            },
            meta={
                'id': f'resource:{resource_id}',
                'routing': business_doc.id,
            },
        ).save()

    index.refresh()

    with patch('lib.elasticsearch.document.Document.reindex') as mock_reindex:
        fix(document_types=[ESDocType.RESOURCE], delete=True, reindex=True)

    mock_reindex.assert_called_once_with(ids=[])

    for resource_id in resource_ids:
        with pytest.raises(elasticsearch.exceptions.NotFoundError):
            ResourceDocument.get(resource_id)


@pytest.mark.django_db
@override_eppo_feature_flag({UseExplicitRoutingWhenDeletingDocumentsFlag.flag_name: False})
def test_reindex_documents(clean_index_function_fixture):
    clean_index_function_fixture(ESIndex.BUSINESS)
    resource = resource_recipe.make(type=ResourceType.STAFF)

    with pytest.raises(elasticsearch.exceptions.NotFoundError):
        ResourceDocument.get(resource.id)

    with patch('lib.elasticsearch.document.Document.delete_extra') as mock_delete_extra:
        fix(document_types=[ESDocType.RESOURCE], delete=False, reindex=True)

    mock_delete_extra.assert_called_once_with([])

    assert ResourceDocument.get(resource.id)


@pytest.mark.django_db
@override_eppo_feature_flag({UseExplicitRoutingWhenDeletingDocumentsFlag.flag_name: True})
def test_reindex_documents__use_explicit_routing(clean_index_function_fixture):
    clean_index_function_fixture(ESIndex.BUSINESS)
    resource = resource_recipe.make(type=ResourceType.STAFF)

    with pytest.raises(elasticsearch.exceptions.NotFoundError):
        ResourceDocument.get(resource.id)

    with patch(
        'lib.elasticsearch.document.Document.delete_extra_with_routing'
    ) as mock_delete_extra_with_routing:
        fix(document_types=[ESDocType.RESOURCE], delete=False, reindex=True)

    mock_delete_extra_with_routing.assert_called_once_with([])

    assert ResourceDocument.get(resource.id)
