#!/usr/bin/env python
import argparse
import sys
from typing import List, Set, Tuple, Union

import dateutil
import django

django.setup()  # noqa
from django.conf import settings

from lib.db import READ_ONLY_DB
from lib.elasticsearch.consts import ESDocType
from webapps.business.elasticsearch import BusinessIndex
from webapps.elasticsearch.elastic import ELASTIC
from webapps.structure.elasticsearch import RegionIndex


PRIORITY = {  # default == 0
    ESDocType.BUSINESS_CATEGORY: 100,
    ESDocType.BUSINESS: 80,
    ESDocType.OPEN_HOURS: 50,
    ESDocType.RESOURCE: 40,
    ESDocType.BUSINESS_CUSTOMER: 0,
    ESDocType.USER: -1,
    ESDocType.REVIEW: -5,
    ESDocType.IMAGE: -10,
    ESDocType.IMAGE_COMMENT: -20,
    ESDocType.IMAGE_LIKE: -20,
    ESDocType.EXTERNAL_BUSINESS: -30,
    ESDocType.REGION: -50,
    ESDocType.REGION_BUSINESS_SCORE_PARTIAL: -80,
    # ESDocType.AVAILABILITY: -100,
}


def resolve_generic_document_types(document_types: Union[str, list]) -> List[str]:
    if 'ALL' in document_types:
        document_types = list(ELASTIC.documents.keys())
    elif 'BUSINESS' in document_types:
        document_types = list(
            ELASTIC.filter_documents(tuple(BusinessIndex.documents.keys())).keys()
        )
    elif 'NON-REGION' in document_types:
        document_types = list(set(ELASTIC.documents.keys()) - set(RegionIndex.documents.keys()))
    return document_types


def parse_document_types(document_types: Union[List[str], Set[str], Tuple[str]]):
    if not isinstance(document_types, (list, set, tuple)):
        # enforce tight typing
        # it is better to list allowed types
        raise RuntimeError('document_types should on of doc list, tuple, set')
    documents = ELASTIC.filter_documents(document_types)
    sorted_names = sorted(
        filter(lambda doc: ELASTIC.documents[doc].get_model() is not None, documents.keys()),
        key=lambda x: -PRIORITY.get(x, 0),
    )
    return sorted_names


def main(document_types, use_celery, last_updated=None, refresh_index=False):
    document_types = resolve_generic_document_types(document_types)
    sorted_names = parse_document_types(document_types)
    for name in sorted_names:
        print('========== reindex', name)
        ELASTIC.documents[name].reindex(
            verbose=True,
            use_celery=use_celery,
            last_updated=last_updated,
            refresh_index=refresh_index,
        )


if __name__ == "__main__":
    available_types = [name.value for name in ELASTIC.documents.keys()] + [
        'ALL',
        'BUSINESS',
        'NON-REGION',
    ]

    parser = argparse.ArgumentParser(
        description="""
            Use this script to reindex all documents of given document_type
            using single process.
            Use ALL to reindex all document types.
            Use BUSINESS to reindex all documents from business_index.
            Use NON-REGION to reindex all documents except region.
        """
    )
    parser.add_argument(
        "document_types",
        nargs='+',
        choices=available_types,
        help="type of documents to be reindexed",
    )
    parser.add_argument(
        "--use-celery", action='store_true', help='push using celery queue and workers'
    )
    parser.add_argument('--host', type=str, help='alternative ES host')
    parser.add_argument(
        '--use-master', action='store_true', help='use master database for reindexing'
    )
    parser.add_argument(
        '--last-updated',
        default=None,
        type=dateutil.parser.parse,
        help="Reindex only newer than this date",
    )
    args = parser.parse_args()
    # override ELASTIC_HOSTS setting
    if args.host is not None:
        if args.use_celery:
            print("Cannot use alternative host and celery at the same time")
            sys.exit(1)
        settings.ELASTIC_HOSTS = [args.host]
    if args.use_master and READ_ONLY_DB in settings.DATABASES:
        if args.use_celery:
            print("Cannot use master and celery at the same time")
            sys.exit(1)
        del settings.DATABASES[READ_ONLY_DB]

    main(
        document_types=args.document_types,
        use_celery=args.use_celery,
        last_updated=args.last_updated,
    )
