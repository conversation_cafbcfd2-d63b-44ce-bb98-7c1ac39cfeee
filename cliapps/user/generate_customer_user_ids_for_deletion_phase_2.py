import argparse

import django

django.setup()  # noqa

# pylint: disable=wrong-import-position
from webapps.user.models import UserInternalData

# pylint: enable=wrong-import-position


def main():
    return list(
        UserInternalData.objects.filter(
            account_deletion_execution_datetime__isnull=False,
            user__deleted__isnull=False,
        ).values_list('user_id', flat=True)
    )


if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description="""
                Script that generates list of ids that will be used for stage 2 account deletion script
            """
    )
    args = parser.parse_args()
    result = main()
    print(f'Found {len(result)} users for deletion')
    print('User IDs to pass to next commands:')
    print(*result, sep=' ')
