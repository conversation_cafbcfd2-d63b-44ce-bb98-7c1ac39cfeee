#!/usr/bin/env python3
import argparse
from datetime import timedelta
import django
from tqdm import tqdm

django.setup()  # noqa

# pylint: disable=wrong-import-position
from django.conf import settings
from lib.tools import tznow
from webapps.user.enums import AccountDeletionExecutionMethod
from webapps.user.models import User, UserInternalData
from webapps.user.utils import delete_user

# pylint: enable=wrong-import-position


def main():
    parser = argparse.ArgumentParser(
        description="""
                Script for deleting account for users that have requested that
            """
    )
    parser.add_argument(
        '--user-ids',
        type=int,
        required=True,
        nargs='+',
        help='pass all the user ids with deletion requests that should be removed',
    )
    args = parser.parse_args()
    clear_users(args.user_ids)


def clear_users(user_ids: list[int]):
    print(f"{len(user_ids)} user candidates for deletion")
    user_ids = UserInternalData.objects.filter(
        account_deletion_requested=True,
        account_deletion_execution_datetime__isnull=True,
        account_deletion_requested_datetime__lte=tznow()
        - timedelta(days=settings.DELETE_ACCOUNT_AFTER_DAYS),
        user__deleted__isnull=True,
        user_id__in=user_ids,
    ).values_list('user_id', flat=True)

    print(f"{len(user_ids)} users still apply for deletion")
    removed_user_ids = []
    not_removed_user_ids = []
    for user_id in tqdm(user_ids, total=len(user_ids)):
        user = User.objects.get(id=user_id)
        status, _error = delete_user(
            user, delete_business_user=False, method=AccountDeletionExecutionMethod.SCRIPT
        )
        if status:
            removed_user_ids.append(user_id)
        else:
            not_removed_user_ids.append(user_id)
    print(
        f"{len(removed_user_ids)} users successfully removed. "
        f"{len(not_removed_user_ids)} users with deletion request not removed"
    )
    if len(removed_user_ids) > 0:
        print("Removed users:")
        print(*removed_user_ids, sep=' ')
    if len(not_removed_user_ids) > 0:
        print("Not removed users:")
        print(*not_removed_user_ids, sep=' ')


if __name__ == '__main__':
    main()
