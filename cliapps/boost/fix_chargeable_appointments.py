import argparse
import time
from datetime import datetime

import pytz

import django
from django.db.transaction import atomic

django.setup()  # noqa

# pylint: disable=wrong-import-position

from django.db.models import Exists, OuterRef, Q

from webapps.booking.models import Appointment
from webapps.business.models import BusinessPromotion
from webapps.business.models.bci import BusinessCustomerInfo

_START_DATETIME = datetime(2023, 4, 1, tzinfo=pytz.utc)


def get_clients_requiring_fixing(
    business_id_from,
    business_id_to,
    first_appointment_created_gte=_START_DATETIME,
):
    originally_first_appointment_list = list(
        Appointment.objects.filter(
            type=Appointment.TYPE.CUSTOMER,
            source__chargeable=True,
            business_id__gte=business_id_from,
            business_id__lte=business_id_to,
            booked_for__client_type=BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW,
            booked_for__first_appointment__payable=False,
            booked_for__first_appointment__created__gte=first_appointment_created_gte,
        )
        .order_by('booked_for', 'created')
        .distinct('booked_for')
        .values_list('id', flat=True)
    )

    return list(
        Appointment.objects.filter(id__in=originally_first_appointment_list)
        .annotate(
            was_promoted_then=Exists(
                BusinessPromotion.objects.filter(
                    business_id=OuterRef('business_id'),
                    promotion_start__lte=OuterRef('created'),
                ).filter(Q(promotion_end__gte=OuterRef('created')) | Q(promotion_end__isnull=True))
            )
        )
        .filter(was_promoted_then=False)
        .values_list('booked_for', flat=True)
    )


@atomic
def fix_first_appointments(client_list):
    # Get a list of first appointments that should be chargeable
    first_appointments = (
        Appointment.objects.filter(
            booked_for_id__in=client_list,
            status__in=[
                Appointment.STATUS.FINISHED,
                Appointment.STATUS.ACCEPTED,
            ],
        )
        .order_by('booked_for', 'booked_till', 'id')
        .distinct('booked_for')
        .values('booked_for', 'id')
    )

    # Prepare mapping
    client_appointment_map = {item['booked_for']: item['id'] for item in first_appointments}

    # Update appointments that should be chargeable but are not
    Appointment.objects.filter(id__in=client_appointment_map.values(), chargeable=False).update(
        chargeable=True
    )

    # Update appointments that should not be chargeable but are
    Appointment.objects.filter(
        booked_for_id__in=client_list, chargeable=True, payable=False
    ).exclude(id__in=client_appointment_map.values()).update(chargeable=False)

    # Update clients' first appointments when necessary
    clients_to_update = []
    clients = BusinessCustomerInfo.objects.filter(id__in=client_appointment_map.keys())
    for client in clients:
        if client.first_appointment_id != client_appointment_map[client.id]:
            client.first_appointment_id = client_appointment_map[client.id]
            clients_to_update.append(client)

    BusinessCustomerInfo.objects.bulk_update(clients_to_update, ['first_appointment_id'])


def find_clients_requiring_fixing(
    business_id_from, business_id_to, first_appointment_created_gte=_START_DATETIME, step=10
):
    iteration_start = business_id_from
    number_of_all_results = 0

    while iteration_start <= business_id_to:
        start_time = time.time()

        iteration_stop = min(iteration_start + step - 1, business_id_to)

        clients_requiring_fixing = get_clients_requiring_fixing(
            iteration_start, iteration_stop, first_appointment_created_gte
        )
        number_of_clients_requiring_fixing = len(clients_requiring_fixing)
        print(
            f'Businesses {iteration_start} to {iteration_stop}:'
            f' {number_of_clients_requiring_fixing}'
        )
        number_of_all_results += number_of_clients_requiring_fixing
        iteration_start += step

        print(f'  {time.time() - start_time:.2f} seconds')
        print(f'  {number_of_all_results} so far')

        yield clients_requiring_fixing


def run(business_id_from, business_id_to, first_appointment_created_gte=_START_DATETIME, step=10):
    for clients_list in find_clients_requiring_fixing(
        business_id_from,
        business_id_to,
        first_appointment_created_gte,
        step,
    ):
        start_time = time.time()
        fix_first_appointments(clients_list)
        print(f'Finished fixing {len(clients_list)} BCIs - {time.time() - start_time:.2f} seconds')


def get_parser() -> argparse.ArgumentParser:
    parser_ = argparse.ArgumentParser()
    parser_.add_argument("--business-id-from", type=int, required=True)
    parser_.add_argument("--business-id-to", type=int, required=True)
    parser_.add_argument("--step", type=int, default=10)
    parser_.add_argument("--dry-run", action="store_true")

    return parser_


if __name__ == "__main__":
    parser = get_parser()
    args = parser.parse_args()

    if args.dry_run:
        all_clients_found_num = sum(
            len(clients_list)
            for clients_list in find_clients_requiring_fixing(
                args.business_id_from, args.business_id_to, step=args.step
            )
        )
        print(f'Found {all_clients_found_num}')
    else:
        run(args.business_id_from, args.business_id_to, step=args.step)
