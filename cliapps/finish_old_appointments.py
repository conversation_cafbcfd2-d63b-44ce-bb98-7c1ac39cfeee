import argparse
from datetime import UTC, datetime
from itertools import chain, groupby
from operator import itemgetter

import dateutil

from tqdm import tqdm
import django

django.setup()
# pylint: disable=wrong-import-position
from lib.tools import tznow, chunker
from webapps.booking import tasks

# pylint: enable=wrong-import-position


def finish_old_appointments_bulk(dry_run: bool, modified_after: datetime | None = None) -> None:
    queryset = tasks.get_appointments_to_close_query(
        end_till=tznow(),
        modified_after=modified_after,
    )
    appointments_to_finish_map = {
        status: list(chain.from_iterable(row["appointment_ids"] for row in rows))
        for status, rows in groupby(
            sorted(queryset, key=itemgetter("status_to_set")), itemgetter("status_to_set")
        )
    }
    for status, appointment_ids in appointments_to_finish_map.items():
        print(f"{status}: {len(appointment_ids)}")
    if dry_run:
        return
    size = 100
    for status, appointment_ids in appointments_to_finish_map.items():
        for ids_batch in tqdm(chunker(appointment_ids, size), total=len(appointment_ids) // size):
            tasks.bulk_finish_appointments(
                list(ids_batch),
                status,
                accepted_old_statuses=tasks.BULK_FINISH_ACCEPTED_STATUSES,
            )


def get_parser() -> argparse.ArgumentParser:
    parser_ = argparse.ArgumentParser()
    parser_.add_argument("--dry-run", action="store_true")
    parser_.add_argument(
        "--modified-after",
        default=None,
        type=dateutil.parser.parse,
        help="Update only appointments updated/ended after that date",
    )

    return parser_


if __name__ == "__main__":
    parser = get_parser()
    args = parser.parse_args()
    _modified_after = args.modified_after
    _modified_after.astimezone(UTC)
    finish_old_appointments_bulk(
        dry_run=args.dry_run,
        modified_after=args.modified_after,
    )
