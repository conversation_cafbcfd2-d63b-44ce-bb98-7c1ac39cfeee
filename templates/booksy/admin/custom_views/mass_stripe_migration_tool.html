{% extends "admin/custom_views/generic_form_template.html" %}

{% block alert_info %}
<ol>
    <li>Filling form
        <ul>
            <li>Download template file: <br>
                <form action="" class="form-horizontal">
                    <button type="submit" name="download_template" class="btn btn-success">
                        <i class="icon-download icon-white"></i> Download template CSV
                    </button>
                </form>
                <p>Please note that only files in <strong>csv</strong> format will be accepted. Max 5000 records in one batch</p>
            </li>
            <li>Fill file fields <strong>according</strong> to the template. First Row must have the same name of columns as templates:
                <ul>
                    <li><code>customer_id_old</code>(string) - Old stripe customer id </li>
                    <li><code>source_id_old</code>(string) Old payment method id</li>
                    <li><code>customer_id_new</code>(string) - New stripe customer id </li>
                    <li><code>source_id_new</code>(string) New payment method id</li>
                </ul>
            </li>
        </ul>
    </li>
</ol>
{% endblock alert_info %}
