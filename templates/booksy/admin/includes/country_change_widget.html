<script>
    $(document).ready(function () {
        var selector = '#countryCodeForm select[name=country_code]';
        $(selector).on('change', function () {
            window.location = $(selector).val();
        });
    });
</script>
<form id="countryCodeForm" action="/" method="get" style="display: inline;">
  <div style="display: inline;">
    <select name="country_code">
      {% for country in COUNTRIES %}
        <option value="{{ country.url }}"{% if API_COUNTRY == country.code %} selected="selected"{% endif %}>{{ country.label }}</option>
      {% endfor %}
    </select>
  </div>
</form>
