<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//ENhttp://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
  <title>{% block email_title %}{% endblock email_title %}</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  {% block extrahead %}{% endblock %}
</head>
<body bgcolor="#f9f9f9" leftmargin="0" marginwidth="20" topmargin="0" marginheight="20" offset="0">
<center>
  <table bgcolor="#fff" border="0" cellpadding="0" cellspacing="0" style="max-width: 500px; width: 100%;">
    <!--HEADER-->
  {% block header %}
    <tr>
      <td>
        <a href="{{ BOOKSY_URL }}">
          <img src="{{ STATIC_FULL_URL }}scenarios/{{ HEADER }}" alt="Booksy">
        </a>
      </td>
    </tr>

  {% endblock header %}
    <!--HEADER-->

    <!--CONTENT-->
    {% block email_broad_content %}{% endblock email_broad_content %}
    <!--CONTENT-->

    <!--FOOTER-->
    <tr>
      <td style="background-color:{{ BG_COLOR }};padding:20px 20px 20px 20px">
        <p style="font-size: 24px; line-height: 1.2; text-align: center; word-break: break-word; font-family: Arial, Helvetica Neue, Helvetica, sans-serif; mso-line-height-alt: 29px;color: #ffffff;margin: 0;">
          <span style="font-size: 24px;">
            <strong>
                {{ FOOTER_DATA['first_line'] | safe }}<br> {# nosemgrep: template-unescaped-with-safe #}
            </strong>
          </span>
          {% if 'second_line' in FOOTER_DATA %}
            <span style="font-size: 14px;">
              {{ FOOTER_DATA['second_line'] | safe }}<br> {# nosemgrep: template-unescaped-with-safe #}
            </span>
          {% endif %}
        </p>
          <div style="text-align:center;padding: 20px 10px 10px 10px;">
          <a href="{{ IOS_APP_URL }}" target="_blank" title="{{ _("Booksy for iOS") }}" style="margin-right:5px; text-decoration: none;">
            <img alt="{{ _("Booksy for iOS") }}" title="{{ _("Booksy for iOS") }}" height="40" width="135" src="{{ STATIC_FULL_URL }}scenarios/{{ FOOTER_DATA['apple_image_url'] }}" style="vertical-align: middle; text-decoration: none;"></a>
          <a href="{{ ANDROID_APP_URL }}" target="_blank" title="{{ _("Booksy for Android") }}">
            <img alt="{{ _("Booksy for Android") }}" title="{{ _("Booksy for Android") }}" height="40" width="135" src="{{ STATIC_FULL_URL }}scenarios/{{ FOOTER_DATA['google_image_url'] }}" style="vertical-align: middle; text-decoration: none;"></a>
        </div>
        {% if 'invite_links' in FOOTER_DATA %}
          <div style="text-align:center;padding: 0 10px 10px 10px;">
            {% for item in FOOTER_DATA['invite_links'] %}
              <a href="{{ item['url'] }}" target="_blank" style="padding: 10px 5px 10px 5px; text-decoration: none;">
                <img alt="{{ item['name'] }}" height="32" title="{{ item['name'] }}" width="32" src="{{ item['image_url'] }}" style="vertical-align: middle; text-decoration: none;"></a>
            {% endfor %}
          </div>
        {% endif %}
        <p style="font-size: 11px; line-height: 1.2; word-break: break-word; margin: 0px; text-align: center;color:#ffffff;font-family:Arial,Helvetica,sans-serif;">
          {% if 'support_email' in FOOTER_DATA %}
            <a rel="noopener" style="color:#ffffff;text-decoration:none" href="mailto:{{ FOOTER_DATA['support_email'] }}" target="_blank">{{ FOOTER_DATA['support_email'] }}</a>
            <br>
          {% endif %}
          {% if 'support_phone' in FOOTER_DATA %}
            {{ FOOTER_DATA['support_phone'] }}<br>
          {% endif %}
          {% if 'unsubscribe' not in FOOTER_DATA %}
            <span style="text-decoration:none;margin-top:20px">
              <a rel="noopener" style="color:#ffffff" href="__UNSUBSCRIBE_URL__" target="_blank">{{ _("Unsubscribe") }}</a>
            </span>
          {% else %}
            <span style="text-decoration:none;margin-top:20px">
              {{ FOOTER_DATA['unsubscribe'] | safe }} {# nosemgrep: template-unescaped-with-safe #}
            </span>
          {% endif %}
        </p>
          {% if FOOTER_DATA['gdpr'] %}
            <p style="margin: 20px 0 0 0;font-size: 8px; line-height: 1.2; word-break: break-word; color:#ffffff;font-family:Arial,Helvetica,sans-serif;text-align:justify;text-align-last:center;">{{ FOOTER_DATA['gdpr'] | safe }}</p> {# nosemgrep: template-unescaped-with-safe #}
          {% endif %}
        </div>
      </td>
    </tr>
    <!--FOOTER-->
  </table>
</center>
</body>
</html>
