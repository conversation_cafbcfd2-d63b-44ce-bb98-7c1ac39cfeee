"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.descriptor_pb2
import google.protobuf.internal.extension_dict
import google.protobuf.message
import sys

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class ServicePrice_deprecated(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VALUE_FIELD_NUMBER: builtins.int
    PRICE_TYPE_FIELD_NUMBER: builtins.int
    DISCOUNT_FIELD_NUMBER: builtins.int
    value: builtins.str
    price_type: builtins.str
    discount: builtins.str
    def __init__(
        self,
        *,
        value: builtins.str = ...,
        price_type: builtins.str = ...,
        discount: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["discount", b"discount", "price_type", b"price_type", "value", b"value"]) -> None: ...

global___ServicePrice_deprecated = ServicePrice_deprecated

class ServicePrice(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VALUE_FIELD_NUMBER: builtins.int
    PRICE_TYPE_FIELD_NUMBER: builtins.int
    DISCOUNT_FIELD_NUMBER: builtins.int
    value: builtins.int
    """minor unit price"""
    price_type: builtins.str
    discount: builtins.int
    """minor unit price"""
    def __init__(
        self,
        *,
        value: builtins.int = ...,
        price_type: builtins.str = ...,
        discount: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["discount", b"discount", "price_type", b"price_type", "value", b"value"]) -> None: ...

global___ServicePrice = ServicePrice

class DispatchContext(google.protobuf.message.Message):
    """
    DispatchContext and HistoryContext are fields to be used in History Messages.
    They must be used in every Message Type used for History Project
    with the number declared as `required_number`.

    Every History Message must declare `history_model` option as well.

    message SomeHistoryMessage {
    option (booksy.types.history_model) = 'my_model';
    ...
    DispatchContext dispatch_context = 2000;
    HistoryContext history_context = 2001;
    }
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OPERATOR_TYPE_FIELD_NUMBER: builtins.int
    USER_ID_FIELD_NUMBER: builtins.int
    USER_EMAIL_FIELD_NUMBER: builtins.int
    SUPERUSER_EMAIL_FIELD_NUMBER: builtins.int
    PARTNER_UUID_FIELD_NUMBER: builtins.int
    PARTNER_NAME_FIELD_NUMBER: builtins.int
    FORWARDED_IP_FIELD_NUMBER: builtins.int
    FINGERPRINT_FIELD_NUMBER: builtins.int
    UAGENT_FIELD_NUMBER: builtins.int
    X_VERSION_FIELD_NUMBER: builtins.int
    REFERER_FIELD_NUMBER: builtins.int
    ENDPOINT_FIELD_NUMBER: builtins.int
    BOOKING_SOURCE_FIELD_NUMBER: builtins.int
    TASK_ID_FIELD_NUMBER: builtins.int
    TASK_NAME_FIELD_NUMBER: builtins.int
    EXTRA_DATA_FIELD_NUMBER: builtins.int
    operator_type: builtins.str
    """operator"""
    user_id: builtins.int
    user_email: builtins.str
    superuser_email: builtins.str
    partner_uuid: builtins.str
    partner_name: builtins.str
    forwarded_ip: builtins.str
    """request metadata"""
    fingerprint: builtins.str
    uagent: builtins.str
    x_version: builtins.str
    referer: builtins.str
    endpoint: builtins.str
    """"""
    booking_source: builtins.str
    task_id: builtins.str
    task_name: builtins.str
    extra_data: builtins.str
    """other json"""
    def __init__(
        self,
        *,
        operator_type: builtins.str = ...,
        user_id: builtins.int = ...,
        user_email: builtins.str = ...,
        superuser_email: builtins.str = ...,
        partner_uuid: builtins.str = ...,
        partner_name: builtins.str = ...,
        forwarded_ip: builtins.str = ...,
        fingerprint: builtins.str = ...,
        uagent: builtins.str = ...,
        x_version: builtins.str = ...,
        referer: builtins.str = ...,
        endpoint: builtins.str = ...,
        booking_source: builtins.str = ...,
        task_id: builtins.str = ...,
        task_name: builtins.str = ...,
        extra_data: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["booking_source", b"booking_source", "endpoint", b"endpoint", "extra_data", b"extra_data", "fingerprint", b"fingerprint", "forwarded_ip", b"forwarded_ip", "operator_type", b"operator_type", "partner_name", b"partner_name", "partner_uuid", b"partner_uuid", "referer", b"referer", "superuser_email", b"superuser_email", "task_id", b"task_id", "task_name", b"task_name", "uagent", b"uagent", "user_email", b"user_email", "user_id", b"user_id", "x_version", b"x_version"]) -> None: ...

global___DispatchContext = DispatchContext

class HistoryContext(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COUNTRY_CODE_FIELD_NUMBER: builtins.int
    BUSINESS_ID_FIELD_NUMBER: builtins.int
    MODEL_ID_FIELD_NUMBER: builtins.int
    EVENT_TIME_FIELD_NUMBER: builtins.int
    EVENT_TYPE_FIELD_NUMBER: builtins.int
    country_code: builtins.str
    business_id: builtins.int
    model_id: builtins.int
    event_time: builtins.int
    event_type: builtins.str
    def __init__(
        self,
        *,
        country_code: builtins.str = ...,
        business_id: builtins.int = ...,
        model_id: builtins.int = ...,
        event_time: builtins.int = ...,
        event_type: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["business_id", b"business_id", "country_code", b"country_code", "event_time", b"event_time", "event_type", b"event_type", "model_id", b"model_id"]) -> None: ...

global___HistoryContext = HistoryContext

HISTORY_MODEL_FIELD_NUMBER: builtins.int
REQUIRED_NUMBER_FIELD_NUMBER: builtins.int
history_model: google.protobuf.internal.extension_dict._ExtensionFieldDescriptor[google.protobuf.descriptor_pb2.MessageOptions, builtins.str]
required_number: google.protobuf.internal.extension_dict._ExtensionFieldDescriptor[google.protobuf.descriptor_pb2.MessageOptions, builtins.int]
