# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: booksy/pubsub/appointment/history.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from booksy import types_pb2 as booksy_dot_types__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'booksy/pubsub/appointment/history.proto\x1a\x12\x62ooksy/types.proto\"\xd6\x0b\n\x12\x41ppointmentHistory\x12\x16\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x12\x13\n\x0b\x62usiness_id\x18\x02 \x01(\r\x12.\n\x08\x63ustomer\x18\x03 \x01(\x0b\x32\x1c.AppointmentHistory.Customer\x12)\n\x05total\x18\x04 \x01(\x0b\x32\x1a.booksy.types.ServicePrice\x12\x13\n\x0b\x62ooked_from\x18\x05 \x01(\x03\x12\x13\n\x0b\x62ooked_till\x18\x06 \x01(\x03\x12\x33\n\x0bsubbookings\x18\x07 \x03(\x0b\x32\x1e.AppointmentHistory.SubBooking\x12\x30\n\trepeating\x18\x08 \x01(\x0b\x32\x1d.AppointmentHistory.Repeating\x12\x30\n\ttraveling\x18\t \x01(\x0b\x32\x1d.AppointmentHistory.Traveling\x12\x15\n\rcustomer_note\x18\n \x01(\t\x12\x15\n\rbusiness_note\x18\x0b \x01(\t\x12\x1c\n\x14\x62usiness_secret_note\x18\x0c \x01(\t\x12\x0e\n\x06status\x18\r \x01(\t\x12\x38\n\x10\x64ispatch_context\x18\xd0\x0f \x01(\x0b\x32\x1d.booksy.types.DispatchContext\x12\x36\n\x0fhistory_context\x18\xd1\x0f \x01(\x0b\x32\x1c.booksy.types.HistoryContext\x1aK\n\x08\x43ustomer\x12\x13\n\x0b\x63ustomer_id\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05\x65mail\x18\x03 \x01(\t\x12\r\n\x05phone\x18\x04 \x01(\t\x1a\xf6\x01\n\x10SimpleSubBooking\x12\x15\n\rsubbooking_id\x18\x01 \x01(\x03\x12\x13\n\x0b\x62ooked_from\x18\x02 \x01(\x03\x12\x13\n\x0b\x62ooked_till\x18\x03 \x01(\x03\x12\x12\n\nstaffer_id\x18\x04 \x01(\r\x12\x14\n\x0c\x61ppliance_id\x18\x05 \x01(\r\x12\x1a\n\x12service_variant_id\x18\x06 \x01(\r\x12\x14\n\x0cservice_name\x18\x07 \x01(\t\x12\x12\n\nautoassign\x18\x08 \x01(\x08\x12\x31\n\rservice_price\x18\t \x01(\x0b\x32\x1a.booksy.types.ServicePrice\x1a\xae\x02\n\nSubBooking\x12\x15\n\rsubbooking_id\x18\x01 \x01(\x03\x12\x13\n\x0b\x62ooked_from\x18\x02 \x01(\x03\x12\x13\n\x0b\x62ooked_till\x18\x03 \x01(\x03\x12\x12\n\nstaffer_id\x18\x04 \x01(\r\x12\x14\n\x0c\x61ppliance_id\x18\x05 \x01(\r\x12\x1a\n\x12service_variant_id\x18\x06 \x01(\r\x12\x14\n\x0cservice_name\x18\x07 \x01(\t\x12\x12\n\nautoassign\x18\x08 \x01(\x08\x12<\n\x0e\x63ombo_children\x18\t \x03(\x0b\x32$.AppointmentHistory.SimpleSubBooking\x12\x31\n\rservice_price\x18\n \x01(\x0b\x32\x1a.booksy.types.ServicePrice\x1at\n\tRepeating\x12\x14\n\x0crepeating_id\x18\x01 \x01(\r\x12\x13\n\x0brepeat_type\x18\x02 \x01(\t\x12\x10\n\x08\x65nd_type\x18\x03 \x01(\t\x12\x13\n\x0brepeat_till\x18\x04 \x01(\x03\x12\x15\n\rrepeat_number\x18\x05 \x01(\x05\x1a\xa8\x01\n\tTraveling\x12\r\n\x05price\x18\x01 \x01(\x05\x12\x16\n\x0e\x61\x64\x64ress_line_1\x18\x02 \x01(\t\x12\x16\n\x0e\x61\x64\x64ress_line_2\x18\x03 \x01(\t\x12\x18\n\x10\x61partment_number\x18\x04 \x01(\t\x12\x0c\n\x04\x63ity\x18\x05 \x01(\t\x12\x0f\n\x07zipcode\x18\x06 \x01(\t\x12\x10\n\x08latitude\x18\x07 \x01(\x02\x12\x11\n\tlongitude\x18\x08 \x01(\x02:\x0f\x8a\xb5\x18\x0b\x41ppointmentb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'booksy.pubsub.appointment.history_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _APPOINTMENTHISTORY._options = None
  _APPOINTMENTHISTORY._serialized_options = b'\212\265\030\013Appointment'
  _APPOINTMENTHISTORY._serialized_start=64
  _APPOINTMENTHISTORY._serialized_end=1558
  _APPOINTMENTHISTORY_CUSTOMER._serialized_start=623
  _APPOINTMENTHISTORY_CUSTOMER._serialized_end=698
  _APPOINTMENTHISTORY_SIMPLESUBBOOKING._serialized_start=701
  _APPOINTMENTHISTORY_SIMPLESUBBOOKING._serialized_end=947
  _APPOINTMENTHISTORY_SUBBOOKING._serialized_start=950
  _APPOINTMENTHISTORY_SUBBOOKING._serialized_end=1252
  _APPOINTMENTHISTORY_REPEATING._serialized_start=1254
  _APPOINTMENTHISTORY_REPEATING._serialized_end=1370
  _APPOINTMENTHISTORY_TRAVELING._serialized_start=1373
  _APPOINTMENTHISTORY_TRAVELING._serialized_end=1541
# @@protoc_insertion_point(module_scope)
