"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class TransactionSucceeded(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRANSACTION_ID_FIELD_NUMBER: builtins.int
    APPOINTMENT_ID_FIELD_NUMBER: builtins.int
    BCI_ID_FIELD_NUMBER: builtins.int
    SUBTOTAL_FIELD_NUMBER: builtins.int
    CHARGE_DATE_FIELD_NUMBER: builtins.int
    BUSINESS_ID_FIELD_NUMBER: builtins.int
    GIFT_CARDS_IDS_FIELD_NUMBER: builtins.int
    APPOINTMENT_STATUS_FIELD_NUMBER: builtins.int
    IS_BOOST_APPOINTMENT_FIELD_NUMBER: builtins.int
    transaction_id: builtins.int
    appointment_id: builtins.int
    bci_id: builtins.int
    subtotal: builtins.str
    charge_date: builtins.int
    business_id: builtins.int
    appointment_status: builtins.str
    is_boost_appointment: builtins.bool
    @property
    def gift_cards_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        transaction_id: builtins.int = ...,
        appointment_id: builtins.int = ...,
        bci_id: builtins.int = ...,
        subtotal: builtins.str = ...,
        charge_date: builtins.int = ...,
        business_id: builtins.int = ...,
        gift_cards_ids: collections.abc.Iterable[builtins.str] | None = ...,
        appointment_status: builtins.str | None = ...,
        is_boost_appointment: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_appointment_status", b"_appointment_status", "appointment_status", b"appointment_status"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_appointment_status", b"_appointment_status", "appointment_id", b"appointment_id", "appointment_status", b"appointment_status", "bci_id", b"bci_id", "business_id", b"business_id", "charge_date", b"charge_date", "gift_cards_ids", b"gift_cards_ids", "is_boost_appointment", b"is_boost_appointment", "subtotal", b"subtotal", "transaction_id", b"transaction_id"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_appointment_status", b"_appointment_status"]) -> typing.Literal["appointment_status"] | None: ...

global___TransactionSucceeded = TransactionSucceeded
