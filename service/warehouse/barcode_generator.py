from bo_obs.datadog.enums import BooksyTeams
from service.tools import (
    RequestHandler,
    session,
)
from webapps.warehouse.serializers.other import (
    CommodityBarcodeGeneratorSerializer,
)


class CommodityBarcodeGeneratorHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Return new generated Barcode by type
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: type
                  type: string
                  paramType: query
                  enum:
                      - "code39"
                      - "code128"
                      - "ean8"
                      - "ean13"
                      - "itf14"
                      - "msi"
                      - "codabar"
                  required: true
            type: BarcodeGeneratorResponse
        :swagger
        """
        self.business_with_reception(business_id, __only='id')
        url_data = self._prepare_get_arguments()
        serializer = CommodityBarcodeGeneratorSerializer(data=url_data)
        self.validate_serializer(serializer)
        self.finish_with_json(
            200,
            {
                'generated_barcode': serializer.data,
            },
        )
