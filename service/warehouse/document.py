from django.contrib.postgres.search import SearchVector
from django.db.models import Subquery, CharField, OuterRef, Q
from django.utils.translation import gettext as _
from rest_framework import status

from bo_obs.datadog.enums import BooksyTeams
from service.mixins.paginator import PaginatorMixin
from service.tools import session, RequestHandler, json_request
from webapps.sequencing_number.models import SequenceRecord
from webapps.warehouse.models import (
    BaseWarehouseDocument,
    WarehouseDocumentType,
)
from webapps.warehouse.serializers.other import (
    BaseWarehouseDocumentSearchSerializer,
    GenericWarehouseDocumentResponseSerializer,
    GenericWarehouseDocumentSerializer,
)
from webapps.warehouse.tasks import send_email_to_supplier_task

# The full text query is normalized and separated into search tokens by RDBMS.
# For performance concerns we don't want to allow too long queries.
MAX_FULLTEXT_SEARCH_QUERY_LENGTH = 100


class GenericDocumentsHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    def get(self, business_id, document_id):
        """
        swagger:
            summary: Retrieve warehouse document
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: document_id
                  type: integer
                  paramType: path
                  required: true
        """
        business = self.business_with_reception(business_id)
        document = self.get_object_or_404(
            BaseWarehouseDocument.objects.from_business(business_id),
            id=document_id,
            deleted__isnull=True,
        )
        serializer = GenericWarehouseDocumentResponseSerializer(
            instance=document,
            context={'business': business},
        )
        self.finish_with_json(status.HTTP_200_OK, serializer.data)

    @session(login_required=True)
    def delete(self, business_id, document_id):
        """
        swagger:
            summary: Delete warehouse document
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: document_id
                  type: integer
                  paramType: path
                  required: true
        """
        business = self.business_with_reception(business_id)
        document = self.get_object_or_404(
            BaseWarehouseDocument.objects.from_business(business_id),
            id=document_id,
            deleted__isnull=True,
        )
        if document.type == WarehouseDocumentType.PZ:
            self.quick_error(
                ('not allowed', 'deleted'),
                _('External reception document cannot be deleted'),
            )

        serializer = GenericWarehouseDocumentSerializer(
            instance=document,
            context={
                'business': business,
                'operator_id': self.user.id,
            },
        )

        try:
            serializer.delete()
        except ValueError as err:
            self.quick_error(
                ('not allowed', 'deleted'),
                format(err),
            )
        self.finish_with_json(status.HTTP_200_OK, {})

    @session(login_required=True)
    @json_request
    def put(self, business_id, document_id):
        """
        swagger:
            summary: Update warehouse document
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: document_id
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  paramType: body
        """
        business = self.business_with_reception(business_id)
        document = self.get_object_or_404(
            BaseWarehouseDocument.objects.from_business(business_id),
            id=document_id,
            deleted__isnull=True,
        )
        serializer = GenericWarehouseDocumentSerializer(
            instance=document,
            data=self.data,
            context={
                'business': business,
                'operator_id': self.user.id,
                'edit': True,
            },
        )
        self.validate_serializer(serializer)
        res_document = serializer.save()
        # <editor-fold desc="early_finish section">
        if hasattr(res_document, 'notify_supplier') and res_document.notify_supplier:
            send_email_to_supplier_task.delay(res_document.id)
        # </editor-fold>
        self.finish_with_json(status.HTTP_200_OK, {})


class GenericDocumentsListHandler(PaginatorMixin, RequestHandler):
    """This handler is a single point of contact used for returning all
    warehouse documents, both the WarehouseDocument instances (WZ, RW, MM
    documents) as well as Supply instances (PZ, ORD documents) and
    stocktaking documents (INW documents)
    """

    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: List all warehouse documents
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: page
                  type: integer
                  paramType: query
                  required: false
                - name: per_page
                  type: integer
                  paramType: query
                  required: false
                - name: type
                  description: Search by document type
                  enum: [WZ, RW, MM, PZ, ORD, INW]
                  type: string
                  paramType: query
                  required: false
                - name: issue_date__gte
                  description: Search documents issued since date
                  type: string
                  paramType: query
                  required: false
                - name: issue_date__lte
                  description: Search documents issued to date
                  type: string
                  paramType: query
                  required: false
                - name: query
                  description: Search documents by number or commodity name
                  type: string
                  paramType: query
                  required: false
        """
        business = self.business_with_reception(business_id)
        self.parse_page_values_from_get()
        query_params = self._prepare_get_arguments()

        qs = (
            BaseWarehouseDocument.objects.from_business(business)
            .filter(
                deleted__isnull=True,
            )
            .select_related(
                'supply__business',
                'warehousedocument__warehouse__business',
                'stocktakingdocument__warehouse__business',
            )
            .prefetch_related(
                'supply__rows',
                'warehousedocument__rows',
                'stocktakingdocument__rows',
            )
            .exclude(
                warehousedocument__hidden=True,
            )
        )
        search_serializer = BaseWarehouseDocumentSearchSerializer(
            data=query_params,
        )
        self.validate_serializer(search_serializer)
        filter_params = search_serializer.data
        query = filter_params.pop('query', None)

        if filter_params:
            qs = qs.filter(**filter_params)
        if query:
            query = query[:MAX_FULLTEXT_SEARCH_QUERY_LENGTH]
            qs = (
                qs.annotate(
                    # See: BaseWarehouseDocument.number property
                    assigned_number=Subquery(
                        SequenceRecord.objects.filter(
                            business=business,
                            type=OuterRef('type'),
                            related_document_id=OuterRef('id'),
                        )
                        .annotate_assigned_number()
                        .values('assigned_number')[:1],
                        output_field=CharField(),
                    ),
                    search=SearchVector(
                        'warehousedocument__rows__commodity_name',
                        'supply__rows__commodity_name',
                        'stocktakingdocument__rows__commodity_name',
                    ),
                )
                .filter(
                    Q(manually_assigned_number__istartswith=query)
                    | Q(assigned_number__istartswith=query)
                    | Q(search=query)
                )
                .distinct('issue_date', 'id')
            )

        # BEWARE: distinct() and order_by() sometimes interfere with each other.
        # In this case fields listed in distinct() and order_by() must be the
        # same and must be listed in the same order. See "Notes" in:
        # https://docs.djangoproject.com/en/2.0/ref/models/querysets/#distinct

        documents_count = qs.count()
        documents = qs.order_by('-issue_date', '-id')[self.offset : self.limit]

        serializer = GenericWarehouseDocumentSerializer(
            instance=documents,
            many=True,
            context={'business': business},
        )
        self.finish_with_json(
            status.HTTP_200_OK,
            {
                'documents': serializer.data,
                'per_page': self.per_page,  # pylint: disable=no-member
                'documents_count': documents_count,
            },
        )

    @json_request
    @session(login_required=True)
    def post(self, business_id):
        """
        swagger:
            summary: Create warehouse document
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  paramType: body
        """
        business = self.business_with_reception(business_id)
        serializer = GenericWarehouseDocumentSerializer(
            data=self.data,
            context={
                'business': business,
                'language': self.language,
                'operator_id': self.user.id,
            },
        )
        self.validate_serializer(serializer)
        try:
            document = serializer.save()
        except ValueError as err:
            self.quick_error(
                ('not allowed', 'create'),
                format(err),
            )
        # <editor-fold desc="early_finish section">
        if hasattr(document, 'notify_supplier') and document.notify_supplier:
            send_email_to_supplier_task.delay(document.id)
        # </editor-fold>
        self.finish_with_json(status.HTTP_201_CREATED, {})
