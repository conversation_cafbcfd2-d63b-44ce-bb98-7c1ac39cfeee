from bo_obs.datadog.enums import BooksyTeams
from lib.tools import (
    tznow,
)
from service.tools import (
    RequestHandler,
    json_request,
    session,
)
from webapps.pos.elasticsearch.commodities import CommodityDocument
from webapps.warehouse.models import Commodity, CommodityCategory
from webapps.warehouse.serializers.other import CommodityDeleteIdListSerializer


class CommodityDeleteHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    @json_request
    def delete(self, business_id):
        """
        swagger:
            summary: Delete commodities
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  description: List of commodities Id's
                  type: CommoditiesListDeleteRequest
                  paramType: body
        :swagger
        """
        business = self.business_with_reception(business_id, __only='id')

        serializer = CommodityDeleteIdListSerializer(
            data=self.data,
            context={
                'business': business,
            },
        )
        self.validate_serializer(serializer)

        commodities = Commodity.objects.filter(
            business__id=business_id,
            id__in=serializer.data.get('commodities') or [],
        )
        to_reindex = set()
        to_reindex = to_reindex.union(
            commodities.values_list('category', flat=True),
            commodities.values_list('extra_categories', flat=True),
        )
        commodities.update(
            deleted=tznow(),
        )
        CommodityDocument.reindex(commodities.values_list('id', flat=True))
        CommodityCategory.reindex_all_subcategories(to_reindex)
        self.finish_with_json(200, {})
