from bo_obs.datadog.enums import BooksyTeams
from lib.tools import get_object_or_404
from service.tools import (
    json_request,
    RequestHandler,
    session,
)
from webapps.warehouse.models import (
    Barcode,
)
from webapps.warehouse.serializers.other import (
    CommodityBarcodeDetailsSerializer,
)


class CommodityBarcodeDetailsHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Return Barcode details
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: id
                  type: integer
                  paramType: query
                  required: true
            type: BarcodeDetailsResponse
        :swagger
        """
        self.business_with_staffer(business_id, __only='id')
        url_data = self._prepare_get_arguments()
        object_id = url_data.get('id')
        barcode = self.get_object_or_404(
            Barcode,
            business__id=business_id,
            deleted__isnull=True,
            id=object_id,
        )
        serializer = CommodityBarcodeDetailsSerializer(instance=barcode)
        self.finish_with_json(
            200,
            {
                'barcode': serializer.data,
            },
        )

    @session(login_required=True)
    def delete(self, business_id):
        """
        swagger:
            summary: Delete barcode
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: commodity_id
                  type: integer
                  paramType: query
                  required: true
        :swagger
        """
        self.business_with_reception(business_id, __only='id')
        url_data = self._prepare_get_arguments()
        object_id = url_data.get('id')
        barcode = get_object_or_404(
            Barcode,
            business__id=business_id,
            id=object_id,
        )
        barcode.soft_delete()
        self.finish_with_json(200, {})

    @session(login_required=True)
    @json_request
    def put(self, business_id):
        """
        swagger:
            summary: Let edit barcode
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: id
                  type: integer
                  paramType: query
                  required: true
                - name: body
                  type: BarcodeDetailsRequest
                  paramType: body
            type: BarcodeDetailsResponse
        :swagger
        """
        self.business_with_reception(business_id, __only='id')
        url_data = self._prepare_get_arguments()
        object_id = url_data.get('id')
        barcode = get_object_or_404(
            Barcode,
            business__id=business_id,
            id=object_id,
        )
        self.data['business'] = business_id
        serializer = CommodityBarcodeDetailsSerializer(
            instance=barcode,
            data=self.data,
        )
        self.validate_serializer(serializer)

        serializer.save()
        self.finish_with_json(
            200,
            {
                'barcode': serializer.data,
            },
        )

    @session(login_required=True)
    @json_request
    def post(self, business_id):
        """
        swagger:
            summary: Let create barcode
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  type: BarcodeDetailsRequest
                  paramType: body
            type: BarcodeDetailsResponse
        :swagger
        """
        self.business_with_reception(business_id, __only='id')
        self.data['business'] = business_id
        serializer = CommodityBarcodeDetailsSerializer(data=self.data)
        self.validate_serializer(serializer)
        serializer.save()
        self.finish_with_json(
            200,
            {
                'barcode': serializer.data,
            },
        )
