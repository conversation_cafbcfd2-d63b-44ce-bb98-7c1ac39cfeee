from bo_obs.datadog.enums import BooksyTeams
from service.tools import (
    Request<PERSON><PERSON><PERSON>,
    session,
)
from webapps.business.models import Service
from webapps.pos.models import Transaction
from webapps.warehouse.models import (
    WarehouseDocument,
    WarehouseDocumentRow,
    WarehouseDocumentType,
)
from webapps.warehouse.serializers.other import (
    CommodityConsumptionRequestSerializer,
    CommodityConsumptionSerializer,
)


class CommodityConsumptionHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: List document rows
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: service_variant_id
                  type: array
                  items:
                     type: integer
                  paramType: query
                  required: true
                - name: business_customer_info_id
                  type: integer
                  paramType: query
                  required: true
            type: CommodityConsumptionResponse
        :swagger
        swaggerModels:
            FormulaCommodity:
                id: FormulaCommodity
                properties:
                    id:
                        type: integer
                        description: commodity id
                    name:
                        type: string
                        description: commodity name
                    archived:
                        type: boolean
                    total_pack_capacity:
                        type: integer
            CommodityConsumption:
                id: CommodityConsumption
                properties:
                    warehouse:
                        type: integer
                    count:
                      type: float
                    commodity:
                        type: FormulaCommodity
                    commodity:
                        type: array
                        items:
                            type: FormulaCommodity
                    booked_from:
                        type: string
                    transaction:
                        type: integer
            CommodityConsumptionResponse:
                id: CommodityConsumptionResponse
                properties:
                    previous_usage:
                        type: array
                        items:
                            type: CommodityConsumption
        :swaggerModels
        """
        self.business_with_staffer(business_id, __only='id')
        request_serializer = CommodityConsumptionRequestSerializer(
            data=self._prepare_get_arguments(['service_variant_id']),
        )
        data = self.validate_serializer(request_serializer)
        service_variant_ids = data['service_variant_id']

        services = Service.objects.filter(
            service_variants__id__in=service_variant_ids,
        )

        services_ids = services.values_list('id', flat=True)

        transactions = Transaction.objects.filter(
            customer_card__id=data['business_customer_info_id'],
        ).distinct()
        for service in services:
            transactions = transactions.filter(
                rows__service_variant__service=service,
            )
        all_transactions_services = Transaction.objects.filter(
            id__in=transactions.all().values_list('id', flat=True),
        ).values_list('rows__service_variant__service__id', flat=True)
        excluded_services = set(all_transactions_services) - set(services_ids)
        transactions = transactions.exclude(
            rows__service_variant__service__id__in=excluded_services,
        )

        documents = WarehouseDocument.objects.filter(
            type=WarehouseDocumentType.RW,
            transaction__in=transactions,
        )
        documents_rows = WarehouseDocumentRow.objects.filter(
            document__in=documents,
        )
        serializer = CommodityConsumptionSerializer(
            instance=documents_rows,
            many=True,
        )
        self.finish_with_json(
            200,
            {
                'previous_usage': serializer.data,
            },
        )
