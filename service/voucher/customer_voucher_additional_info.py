from bo_obs.datadog.enums import BooksyTeams
from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.response import Response

from drf_api.base_views import BaseBooksyNoSessionApiView
from webapps.voucher.models import VoucherAdditionalInfo
from webapps.voucher.order_serializers import (
    VoucherAdditionalInfoSerializer,
)


class CustomerVoucherAdditionalInfoView(BaseBooksyNoSessionApiView):
    booksy_teams = (BooksyTeams.NEW_FINANCIAL_SERVICES,)

    @staticmethod
    def get_serializer(*args, **kwargs):
        return VoucherAdditionalInfoSerializer(*args, **kwargs)

    def get(self, request, business_pk):
        additional_info = get_object_or_404(
            VoucherAdditionalInfo,
            pos__business_id=business_pk,
        )
        serializer = self.get_serializer(
            instance=additional_info,
        )
        return Response(
            status=status.HTTP_200_OK,
            data=serializer.data,
        )
