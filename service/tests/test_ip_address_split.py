import pytest
from service.tools import UserRequestIPMixin


@pytest.mark.parametrize(
    'ip_address, expected_value',
    (
        pytest.param(
            '***********, **************, ************',
            '***********',
            id='parse list',
        ),
        pytest.param(
            '***********',
            '***********',
            id='parse single address',
        ),
    ),
)
def test_split_ip_address(ip_address, expected_value):
    result = UserRequestIPMixin.parse_ip_address(ip_address)
    assert expected_value == result
