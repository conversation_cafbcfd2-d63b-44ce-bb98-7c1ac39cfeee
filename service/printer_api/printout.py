from rest_framework import status

from bo_obs.datadog.enums import BooksyTeams
from service.pos.business_transactions import BaseTransactionHandlerMixin
from service.tools import Request<PERSON>and<PERSON>, session
from webapps.printer_api.models import Printout
from webapps.printer_api.serializers import PrintoutSerializer


class PrintoutHandler(
    Request<PERSON><PERSON><PERSON>,
    BaseTransactionHandlerMixin,
):  # pylint: disable=too-many-ancestors
    """Saves request for Printout for Printer API"""

    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    def post(self, business_id, transaction_id):
        """
        swagger:
            summary: Set printout request for Printer API
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: transaction_id
                  type: integer
                  paramType: path
                  description: Transaction id
            type: PrintoutResponse
        :swagger
        """
        business = self.business_with_staffer(business_id)
        printout = Printout.objects.filter(business=business, transaction_id=transaction_id).first()
        if printout:
            serializer = PrintoutSerializer(instance=printout)
        else:
            serializer = PrintoutSerializer(
                data={
                    'business': business.id,
                    'transaction': transaction_id,
                    'operator': self.user.id,
                }
            )
            self.validate_serializer(serializer)
            serializer.save()

        self.finish_with_json(status.HTTP_200_OK, serializer.data)
