from datetime import datetime

import pytest
import pytz
from django.conf import settings
from freezegun import freeze_time
from mock import patch

from lib.tools import id_to_external_api
from service.tests import dict_assert, BaseAsyncHTTPTest
from webapps.business.models import Business
from webapps.consts import WEB
from webapps.segment.consts import UserRoleEnum
from webapps.segment.enums import DeviceTypeName, BooksyAppVersions, InviteImportSource


@pytest.mark.django_db
class CeleryTaskStatusHandlerTests(BaseAsyncHTTPTest):
    url = '/celery/task/{}/'

    @freeze_time(datetime(2021, 9, 21, 10, 15, tzinfo=pytz.UTC))
    @patch('lib.tagmanager.client.GTMClient._request_api')
    def test_get(self, request_api_mock, business_import_json_customers=None):
        from webapps.business.tasks import business_import_json_customers

        self.biz_booking_src.name = WEB
        self.biz_booking_src.save()

        task_result = business_import_json_customers.delay(
            business_id=self.business.id,
            data={
                'customers': [
                    {
                        'first_name': '<PERSON>',
                        'last_name': '<PERSON>',
                        'phone': '+***********',
                        'email': '<EMAIL>',
                    },
                ]
            },
            invite=True,
            import_=True,
            user_id=self.user.id,
            firebase_analytics_data={
                'client_id': '304810080.1611830900',
                'booking_source_id': self.biz_booking_src.id,
                'import_source': InviteImportSource.ADDRESS_BOOK,
            },
        )
        assert task_result.task_id
        assert task_result.status == 'SUCCESS'

        url = self.url.format(task_result.task_id)

        with patch(
            'service.other.celery.CeleryTaskStatusHandler.get_task_result',
            return_value=task_result,
        ):
            resp = self.fetch(url, method='GET')
        assert request_api_mock.call_count == 1
        dict_assert(
            request_api_mock.call_args_list[0][1],
            {
                'endpoint': '/p/collect',
                'method': 'post',
                'payload': {
                    'events': [
                        {
                            'name': 'Invite_Process_Completed',
                            'params': {
                                'email_invites_sent': 0,
                                'event_date': '20210921',
                                'import_source': 'address_book',
                                'postponed': False,
                                'sms_invites_sent': 1,
                                'total_invites_sent': 1,
                                'email': self.business.owner.email,
                            },
                        }
                    ],
                    'user_id': id_to_external_api(self.business.owner.id),
                    'user_properties': {
                        'business_id': {'value': id_to_external_api(self.business.id)},
                        'country': {'value': settings.API_COUNTRY},
                        'offer_type': {'value': Business.Package.UNKNOWN.label},
                        'business_phone': {'value': ''},
                        'app_version': {'value': BooksyAppVersions.B30},
                        'device_type': {'value': DeviceTypeName.DESKTOP},
                        'user_role': {'value': UserRoleEnum.OWNER},
                        'logged_in_user_id': {'value': id_to_external_api(self.user.id)},
                    },
                },
            },
        )
        assert resp.json
        assert resp.json['task_id'] == task_result.task_id
        assert resp.json['is_ready']
        assert resp.json['is_successful']
        assert resp.json['status'] == 'SUCCESS'
