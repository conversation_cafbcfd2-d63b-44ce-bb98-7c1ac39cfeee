#!/usr/bin/env python
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.db import transaction

from lib.feature_flag.feature.customer import NotificationBadgesFlag
from service.exceptions import ServiceError
from service.other.serializers import IOSBadgeSerilizer
from service.tools import Request<PERSON><PERSON><PERSON>, json_request, session
from webapps.notification.models import <PERSON><PERSON>ver, UserNotification
from webapps.notification.push import notification_receivers_list
from webapps.notification.tasks.push import send_push_notification
from webapps.user.models import User


class PushTestHandler(RequestHandler):
    """DEPRECATED Send test push.

    swagger:
        summary: >
            DEPRECATED. Can be used only on test environments.
            Send test push
        notes: >
            Example <tt>push_data</tt>:
            <pre>{"alert": "...", "type": "booking", "args": [booking_id]}</pre>
            <pre>{"alert": "...", "type": "business.add_review",
            "args": [business_id]}</pre>
        parameters:
          - name: body
            paramType: body
            type: PushTestRequest
    :swagger

    swaggerModels:
        PushTestRequest:
            id: PushTestRequest
            required:
              - user_email
              - push_type
              - push_data
            properties:
                user_email:
                    type: string
                    description: >
                        Email identifying user to who push will be sent.
                push_type:
                    type: string
                    description: |
                        "B" if notification should be sent to Business app,
                        "C" for Customer app.
                push_data:
                    type: PushTestData
                    description: Content of push - what will be sent to mobile
        PushTestData:
            id: PushTestData
            required:
              - alert
            properties:
                alert:
                    type: string
                    description: |
                        Text to be displayed on notification bar and in popup.
                        This was only field until recently.
                type:
                    type: string
                    description: >
                        To which screen push notification should redirect
                        after being opened.
                args:
                    type: array
                    items:
                        type: integer
                    description: Additional info for <tt>type</tt>.
    """

    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)

    @session()
    @json_request
    def post(self):
        if settings.LIVE_DEPLOYMENT:
            self.deprecation_error()
            return
        response = {}

        user = User.objects.get(email=self.data['user_email'])
        response['user_id'] = user.id
        response['user_email'] = user.email

        receivers = notification_receivers_list(
            user_id=user.id, customer=(self.data.get('push_type', 'C') == 'C')
        )
        response['receivers'] = receivers

        push_data = self.data['push_data']
        alert = push_data['alert']
        if push_data.get('type') is not None:
            target = (push_data['type'],) + tuple(push_data.get('args', ()))
        else:
            target = ()

        send_push_notification(
            receivers=receivers,
            alert=alert,
            target=target,
            use_celery=False,
        )

        self.set_status(200)
        self.set_header("Content-Type", "application/json")
        self.finish(response)


class NotificationBadgesHandler(RequestHandler):
    """Access current user's profile notification badges count.
    swagger:
      type: NotificationBadge
    :swagger

    swaggerModels:
      NotificationBadge:
        id: NotificationBadge
        description: iOS device notification badges count
        required:
          - token
        properties:
          badge:
            type: integer
            description: Notification badges count
          token:
            type: string
            description: Device identifier
    """

    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)

    def validate_v2(self, is_required_badge=False):
        serializer = IOSBadgeSerilizer(data=self.data, is_required_badge=is_required_badge)
        return self.validate_serializer(serializer)

    def _get_user_notifications_qset(self, token):
        user_notifications = UserNotification.objects.filter(
            type=UserNotification.PUSH_NOTIFICATION,
            recievers__device=Reciever.IOS,
            recievers__identifier=token,
            profile__user_id=self.user.id,
            profile__profile_type=self.profile_type,
        )
        return user_notifications

    def _update_user_notifications(self, token: str, badge: int):
        qset = self._get_user_notifications_qset(token)
        with transaction.atomic():
            updated = (
                qset.select_for_update(skip_locked=True, of=('self',))
                .exclude(badge=badge)
                .update(badge=badge)
            )

        if not updated:
            raise ServiceError(
                errors=[
                    {
                        'code': 'invalid',
                        'field': 'token',
                        'description': 'Nothing to update.',
                    }
                ]
            )

    def _update_user_notifications_v2(self, token: str, badge: int):
        qset = self._get_user_notifications_qset(token)
        with transaction.atomic():
            ids = list(
                qset.exclude(badge=badge)
                .select_for_update(skip_locked=True, of=('self',))
                .values_list('id', flat=True)
            )
            if ids:
                UserNotification.objects.filter(id__in=ids).update(badge=badge)

        if not ids:
            raise ServiceError(
                errors=[
                    {
                        'code': 'invalid',
                        'field': 'token',
                        'description': 'Nothing to update.',
                    }
                ]
            )

    def validate(self):
        serializer = IOSBadgeSerilizer(data=self.data)
        validated_data = self.validate_serializer(serializer)

        user_notifications = UserNotification.objects.filter(
            type=UserNotification.PUSH_NOTIFICATION,
            recievers__device=Reciever.IOS,
            recievers__identifier=validated_data['token'],
            profile__user_id=self.user.id,
            profile__profile_type=self.profile_type,
        )
        if not user_notifications.exists():
            raise ServiceError(
                errors={
                    'code': 'invalid',
                    'field': 'token',
                    'description': 'Device not found.',
                }
            )
        return user_notifications, validated_data

    def finish_with_ret(self, token, badge):
        ret = {
            'token': token,
            'badge': badge,
        }
        self.finish(ret)

    @session(login_required=True)
    @json_request
    def delete(self):
        """
        swagger:
          summary: Reset (set to zero) current user's notification badge count
        """
        if flag_data_ver := NotificationBadgesFlag().get('version'):
            validated_data = self.validate_v2()
            if flag_data_ver == 'one_query':
                self._update_user_notifications(token=validated_data['token'], badge=0)
            else:
                self._update_user_notifications_v2(token=validated_data['token'], badge=0)
            self.finish_with_ret(badge=0, token=validated_data['token'])
            return

        user_notifications, validated_data = self.validate()
        user_notifications.update(badge=0)
        self.finish_with_ret(badge=0, token=validated_data['token'])

    @session(login_required=True)
    @json_request
    def put(self):
        """
        swagger:
          summary: Set current user's notification badge count
          parameters:
            - name: body
              paramType: body
              type: NotificationBadge
        """
        if flag_data_ver := NotificationBadgesFlag().get('version'):
            validated_data = self.validate_v2(is_required_badge=True)
            if flag_data_ver == 'one_query':
                self._update_user_notifications(
                    token=validated_data['token'], badge=validated_data['badge']
                )
            else:
                self._update_user_notifications_v2(
                    token=validated_data['token'], badge=validated_data['badge']
                )
            self.finish_with_ret(badge=validated_data['badge'], token=validated_data['token'])
            return

        user_notifications, validated_data = self.validate()

        user_notifications.update(badge=validated_data['badge'])
        self.finish_with_ret(validated_data['badge'], validated_data['token'])

    @session(login_required=True)
    @json_request
    def get(self):
        """
        swagger:
          summary: Get current user's notification badge count
        """
        if NotificationBadgesFlag().get('version'):
            validated_data = self.validate_v2()
            qset = self._get_user_notifications_qset(validated_data['token'])
            if not (notification := qset.first()):
                raise ServiceError(
                    errors=[
                        {
                            'code': 'invalid',
                            'field': 'token',
                            'description': 'Device not found.',
                        }
                    ]
                )
            token = (
                notification.recievers.filter(device=Reciever.IOS)
                .values_list('identifier', flat=True)
                .last()
            )
            self.finish_with_ret(
                badge=notification.badge,
                token=token,
            )
            return

        user_notifications, _ = self.validate()
        notification = user_notifications.first()
        token = notification.recievers.filter(device=Reciever.IOS).last().identifier
        self.finish_with_ret(
            badge=notification.badge,
            token=token,
        )
