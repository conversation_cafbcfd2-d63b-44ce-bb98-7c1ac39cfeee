from django.core.files.base import ContentFile
from django.core.paginator import EmptyPage, Paginator

from service.tools import <PERSON><PERSON><PERSON>and<PERSON>

from webapps.photo.serializers import UploadPhotoSerializer


def get_file_from_request_files(request):
    try:
        image_data = list(request.files.values())[0][0]
        # Note that Web-biz does not send filename in request, so we must set
        # a fake filename so as to pass the validation
        return ContentFile(image_data['body'], 'fake-name.jpg')
    except IndexError:
        return None


class BasePhotoUploadHandler(RequestHandler):

    def save_photo(self, context):
        serializer = UploadPhotoSerializer(
            data={
                'photo': get_file_from_request_files(self.request),
            },
            context=context,
        )
        self.validate_serializer(serializer)
        serializer.save()
        return serializer


class PaginatorPage:

    def __init__(self, items, per_page: int, page: int) -> None:
        self.paginator = Paginator(items, per_page)
        self.page_number = page

    @property
    def page_results(self) -> list:
        try:
            page = self.paginator.page(self.page_number)
            return list(page.object_list)
        except EmptyPage:
            return []

    @property
    def per_page(self) -> int:
        return self.paginator.per_page

    @property
    def count(self) -> int:
        return self.paginator.count


def get_image_map():
    return {
        'aesthetic_medicine': ['aesthetic_medicine.jpg'],
        'barbers': ['barbers_n.jpg', 'barbers_1.jpg', 'barbers_2.jpg'],
        'brows__lashes': ['brows__lashes.jpg'],
        'day_spa': ['day_spa.jpg'],
        'dental': ['dental.jpg'],
        'dietician': ['dietician.jpg'],
        'hair_salons': [
            'hair_salons_n.jpg',
            'hair_salons_1.jpg',
            'hair_salons_2.jpg',
        ],
        'make-up': ['make-up.jpg'],
        'nail_salons': [
            'nail_salons_n.jpg',
            'nail_salons_1.jpg',
            'nail_salons_2.jpg',
            'nail_salons_3.jpg',
        ],
        'personal_trainers': ['personal_trainers.jpg'],
        'podiatry': ['podiatry.jpg'],
        'skin_care': ['skin_care.jpg'],
        'therapy': ['therapy.jpg'],
    }
