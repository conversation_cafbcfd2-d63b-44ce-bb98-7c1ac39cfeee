import datetime

import pytest
import pytz
from django.core import mail
from model_bakery import baker
from model_bakery.recipe import Recipe
from rest_framework import status

from service.tests import (
    BaseAsyncHTTPTest,
    dict_assert,
)

from webapps.business.models import Business, Resource
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.invoicing.models import Buyer, CustomerInvoice, InvoiceItem, Seller
from webapps.invoicing.serializers.invoice import CustomerInvoiceStatusEnum
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User


@pytest.mark.django_db
class InvoiceListHandlerTests(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/invoicing/invoice/'

    def setUp(self):
        super().setUp()
        self.inv_from_business = Recipe(
            CustomerInvoice,
            business=self.business,
            buyer_name='Buyer name',
        )

        self.invoice_a = self.inv_from_business.make()
        self.invoice_b = self.inv_from_business.make(number='INV14')
        self.invoice_c = baker.make(CustomerInvoice)
        self.invoice_d = self.inv_from_business.make(
            corrected_invoice=self.invoice_b,
            number="COR2",
        )

    def test_get(self):
        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert len(resp.json['invoices']) == 2
        assert resp.json['items_count'] == 2

        ids = [x['id'] for x in resp.json['invoices']]
        assert self.invoice_a.id in ids
        assert self.invoice_b.id in ids
        assert self.invoice_c.id not in ids

        # On invoices list we do not want to serialize invoice items
        assert 'items' not in resp.json['invoices'][0]

    def test_get_search_query(self):
        invoice_a = self.inv_from_business.make(
            internal_description='title-a',
            buyer_tax_id_number='tax-a',
        )
        invoice_b = self.inv_from_business.make(
            internal_description='title-b',
            buyer_tax_id_number='tax-b',
        )
        url = self.url.format(business_id=self.business.id)

        resp = self.fetch(url, method='GET', args={'query': 'title'})
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['invoices']]
        assert invoice_a.id in ids
        assert invoice_b.id in ids

        resp = self.fetch(url, method='GET', args={'query': 'title-a'})
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['invoices']]
        assert invoice_a.id in ids
        assert invoice_b.id not in ids

        resp = self.fetch(url, method='GET', args={'query': 'tax-b'})
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['invoices']]
        assert invoice_a.id not in ids
        assert invoice_b.id in ids

        resp = self.fetch(url, method='GET', args={'query': 'INV14'})
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert len(resp.json['invoices']) == 1
        invoice = resp.json['invoices'][0]
        assert invoice['id'] == self.invoice_b.id
        assert invoice['correction_invoice']['id'] == self.invoice_d.id
        dict_assert(
            invoice['correction_invoice'],
            {
                "id": self.invoice_d.id,
                "number": self.invoice_d.number,
            },
        )

        resp = self.fetch(url, method='GET', args={'query': 'COR'})
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert len(resp.json['invoices']) == 1
        invoice = resp.json['invoices'][0]
        assert invoice['id'] == self.invoice_b.id
        assert invoice['correction_invoice']['id'] == self.invoice_d.id

    @pytest.mark.freeze_time(datetime.datetime(2019, 11, 22, 1, tzinfo=pytz.UTC))
    def test_get_filter_status(self):
        paid_invoice = baker.make(
            CustomerInvoice,
            business=self.business,
            is_paid=True,
        )
        unpaid_not_overdue = baker.make(
            CustomerInvoice,
            business=self.business,
            is_paid=False,
            payment_term=datetime.date(2019, 11, 30),
        )
        unpaid_overdue = baker.make(
            CustomerInvoice,
            business=self.business,
            is_paid=False,
            payment_term=datetime.date(2019, 11, 20),
        )
        url = self.url.format(business_id=self.business.id)

        resp = self.fetch(
            url,
            method='GET',
            args={'status': CustomerInvoiceStatusEnum.ALL},
        )
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['invoices']]
        assert paid_invoice.id in ids
        assert unpaid_not_overdue.id in ids
        assert unpaid_overdue.id in ids

        resp = self.fetch(
            url,
            method='GET',
            args={'status': CustomerInvoiceStatusEnum.PAID},
        )
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['invoices']]
        assert paid_invoice.id in ids
        assert unpaid_not_overdue.id not in ids
        assert unpaid_overdue.id not in ids

        resp = self.fetch(
            url,
            method='GET',
            args={'status': CustomerInvoiceStatusEnum.UNPAID},
        )
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['invoices']]
        assert paid_invoice.id not in ids
        assert unpaid_not_overdue.id in ids
        assert unpaid_overdue.id in ids

        resp = self.fetch(
            url,
            method='GET',
            args={'status': CustomerInvoiceStatusEnum.OVERDUE},
        )
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['invoices']]
        assert paid_invoice.id not in ids
        assert unpaid_not_overdue.id not in ids
        assert unpaid_overdue.id in ids

    @pytest.mark.freeze_time(datetime.datetime(2019, 11, 22, 1, tzinfo=pytz.UTC))
    def test_get_filter_by_correction_status(self):
        """When invoice has unpaid or overdue correction it should be returned"""
        paid_inv_with_paid_cor = baker.make(
            CustomerInvoice,
            business=self.business,
            is_paid=True,
        )
        baker.make(
            CustomerInvoice,
            business=self.business,
            corrected_invoice=paid_inv_with_paid_cor,
            is_paid=True,
        )
        paid_inv_with_unpaid_not_overdue_cor = baker.make(
            CustomerInvoice,
            business=self.business,
            is_paid=True,
        )
        baker.make(
            CustomerInvoice,
            business=self.business,
            corrected_invoice=paid_inv_with_unpaid_not_overdue_cor,
            is_paid=False,
            payment_term=datetime.date(2019, 11, 30),  # not overdue
        )
        paid_inv_with_overdue_cor = baker.make(
            CustomerInvoice,
            business=self.business,
            is_paid=True,
        )
        baker.make(
            CustomerInvoice,
            business=self.business,
            corrected_invoice=paid_inv_with_overdue_cor,
            is_paid=False,
            payment_term=datetime.date(2019, 11, 20),  # overdue
        )
        url = self.url.format(business_id=self.business.id)

        resp = self.fetch(url, method='GET', args={'status': 'all'})
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['invoices']]
        assert paid_inv_with_paid_cor.id in ids
        assert paid_inv_with_unpaid_not_overdue_cor.id in ids
        assert paid_inv_with_overdue_cor.id in ids

        resp = self.fetch(url, method='GET', args={'status': 'unpaid'})
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['invoices']]
        assert paid_inv_with_paid_cor.id not in ids
        assert paid_inv_with_unpaid_not_overdue_cor.id in ids
        assert paid_inv_with_overdue_cor.id in ids

        resp = self.fetch(url, method='GET', args={'status': 'overdue'})
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['invoices']]
        assert paid_inv_with_paid_cor.id not in ids
        assert paid_inv_with_unpaid_not_overdue_cor.id not in ids
        assert paid_inv_with_overdue_cor.id in ids

    def test_post(self, **kwargs):
        staffer = baker.make(
            Resource, business=self.business, type=Resource.STAFF, name='Jan Kowalski'
        )
        baker.make(Seller, business=self.business)
        bci = baker.make(BusinessCustomerInfo, business=self.business)
        buyer = baker.make(Buyer, customer=bci, email='<EMAIL>')

        valid_data = {
            "issuing_staffer": staffer.id,
            "number": "FV/2019/1/1",
            "title": "Some title",
            "issue_date": "2019-10-23",
            "sale_date": "2019-10-23",
            "payment_term": "2019-10-23",
            "payment_method": "cash",
            "discount_amount": "20.00",
            "send_email_to_buyer": True,
            "buyer": buyer.id,
            "items": [
                {
                    "name": "Some commodity",
                    "quantity": 2,
                    "unit_symbol": "szt.",
                    "net_unit_price": "10.00",
                    "gross_unit_price": "12.30",
                    "tax_rate": "23.00",
                }
            ],
        }
        valid_data |= kwargs.get('additional_fields', {})

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_data)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json
        assert len(mail.outbox) == 1
        assert mail.outbox[0].subject == 'Invoice no. FV/2019/1/1'

    def test_basic_staffer_has_no_access(self):
        user = baker.make(User)
        baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            staff_user=user,
        )
        self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        url = self.url.format(business_id=self.business.id)

        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_404_NOT_FOUND

        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_404_NOT_FOUND


@pytest.mark.django_db
class InvoiceDetailsHandlerTests(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/invoicing/invoice/{invoice_id}'  # pylint: disable=line-too-long

    def setUp(self):
        super().setUp()
        self.buyer = baker.make(Buyer, email='<EMAIL>')
        self.invoice_a = baker.make(
            CustomerInvoice,
            business=self.business,
            buyer=self.buyer,
        )
        self.invoice_item_a1 = baker.make(InvoiceItem, invoice=self.invoice_a)

        self.business_b = baker.make(Business)
        self.invoice_b = baker.make(CustomerInvoice, business=self.business_b)

    def test_get(self):
        url = self.url.format(
            business_id=self.business.id,
            invoice_id=self.invoice_a.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['id'] == self.invoice_a.id
        assert resp.json['buyer_email'] == self.buyer.email

        # Retrieving invoice from other business should fail
        url = self.url.format(
            business_id=self.business_b.id,
            invoice_id=self.invoice_b.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_400_BAD_REQUEST

    def test_delete(self):
        url = self.url.format(
            business_id=self.business.id,
            invoice_id=self.invoice_a.id,
        )
        resp = self.fetch(url, method='DELETE')
        assert resp.code == status.HTTP_200_OK
        assert not CustomerInvoice.objects.filter(id=self.invoice_a.id).exists()
        assert CustomerInvoice.all_objects.filter(id=self.invoice_a.id).exists()

        # Invoice items should get soft deleted too
        assert not InvoiceItem.objects.filter(invoice_id=self.invoice_a.id).exists()
        assert InvoiceItem.all_objects.filter(invoice_id=self.invoice_a.id).exists()

        # It should not be possible to delete invoice from other business
        url = self.url.format(
            business_id=self.business_b.id,
            invoice_id=self.invoice_b.id,
        )
        resp = self.fetch(url, method='DELETE')
        assert resp.code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.usefixtures('seller_details')
    @pytest.mark.usefixtures('buyer_details')
    def test_put(self, **kwargs):
        staffer = baker.make(Resource, business=self.business, type=Resource.STAFF)
        bci = baker.make(BusinessCustomerInfo, business=self.business)
        buyer = baker.make(Buyer, customer=bci)
        seller = baker.make(Seller, business=self.business)

        invoice_item_a2 = baker.make(InvoiceItem, invoice=self.invoice_a)

        data = {
            "issuing_staffer": staffer.id,
            "issuing_staffer_name": "Jan Kowalski",
            "number": "FV/2019/1/1",
            "internal_description": "Some title",
            "issue_date": "2019-10-23",
            "sale_date": "2019-10-23",
            "payment_term": "2019-10-23",
            "payment_method": "cash",
            "discount_amount": "20.00",
            "seller": seller.id,
            "buyer": buyer.id,
            **self.seller_details,
            **self.buyer_details,
            "items": [
                {
                    # This item should get updated
                    "id": self.invoice_item_a1.id,
                    "name": "Some commodity",
                    "quantity": 2,
                    "unit_symbol": "szt.",
                    "net_unit_price": "10.00",
                    "tax_rate": "23.00",
                },
                {
                    # This is new item that should be added
                    "name": "New item",
                    "quantity": 2,
                    "unit_symbol": "szt.",
                    "net_unit_price": "15.93",
                    "tax_rate": "23.00",
                },
                # The invoice_item_a2 is missing so it should get deleted
            ],
        }

        data |= kwargs.get('additional_fields', {})

        url = self.url.format(
            business_id=self.business.id,
            invoice_id=self.invoice_a.id,
        )
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_200_OK, resp
        assert resp.json

        self.invoice_a.refresh_from_db()
        assert self.invoice_a.internal_description == 'Some title'
        assert self.invoice_a.issue_date == datetime.date(2019, 10, 23)
        assert self.invoice_a.discount_amount == 2000  # minor units

        self.invoice_item_a1.refresh_from_db()
        assert self.invoice_item_a1 in self.invoice_a.items.all()
        assert self.invoice_item_a1.name == 'Some commodity'
        assert self.invoice_item_a1.net_unit_price == 1000  # minor units

        assert invoice_item_a2 not in self.invoice_a.items.all()

        new_item = self.invoice_a.items.filter(name='New item').first()
        assert new_item
        assert new_item.net_unit_price == 1593  # minor units

        # Updating invoice from other business should be forbidden
        url = self.url.format(
            business_id=self.business_b.id,
            invoice_id=self.invoice_b.id,
        )
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_400_BAD_REQUEST

    def test_basic_staffer_has_no_access(self):
        user = baker.make(User)
        baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            staff_user=user,
        )
        self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        url = self.url.format(
            business_id=self.business.id,
            invoice_id=self.invoice_a.id,
        )

        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_404_NOT_FOUND

        resp = self.fetch(url, method='PUT', body={})
        assert resp.code == status.HTTP_404_NOT_FOUND

        resp = self.fetch(url, method='DELETE')
        assert resp.code == status.HTTP_404_NOT_FOUND


@pytest.mark.django_db
class InvoiceDetailsHandlerForCorrectionTests(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/invoicing/invoice/{invoice_id}'

    def setUp(self):
        super().setUp()
        self.invoice = baker.make(CustomerInvoice, business=self.business)
        self.invoice_item1 = baker.make(InvoiceItem, invoice=self.invoice, net_unit_price=500)
        self.invoice_item2 = baker.make(InvoiceItem, invoice=self.invoice, net_unit_price=500)

        self.correcting_invoice = baker.make(
            CustomerInvoice,
            business=self.business,
            corrected_invoice=self.invoice,
            number='COR123',
        )
        self.correcting_invoice_item1 = baker.make(
            InvoiceItem,
            invoice=self.correcting_invoice,
            net_unit_price=500,
        )
        self.correcting_invoice_item2 = baker.make(
            InvoiceItem,
            invoice=self.correcting_invoice,
            net_unit_price=500,
        )

    @pytest.mark.usefixtures('seller_details')
    @pytest.mark.usefixtures('buyer_details')
    def test_put_with_zero_quantity(self, **kwargs):
        staffer = baker.make(
            Resource,
            business=self.business,
            type=Resource.STAFF,
        )
        bci = baker.make(BusinessCustomerInfo, business=self.business)
        buyer = baker.make(Buyer, customer=bci)
        seller = baker.make(Seller, business=self.business)

        data = {
            "issuing_staffer": staffer.id,
            "issuing_staffer_name": "Jan Kowalski",
            "number": "FV/2019/1/1",
            "internal_description": "Some title",
            "issue_date": "2019-10-23",
            "sale_date": "2019-10-23",
            "payment_term": "2019-10-23",
            "payment_method": "cash",
            "discount_amount": "20.00",
            "seller": seller.id,
            "buyer": buyer.id,
            **self.seller_details,
            **self.buyer_details,
            "items": [
                {
                    "id": self.correcting_invoice_item1.id,
                    "name": "Some commodity",
                    "quantity": 0,  # 0 should be allowed for a correcting invoice
                    "unit_symbol": "szt.",
                    "net_unit_price": "10.00",
                    "tax_rate": "23.00",
                },
                {
                    "id": self.correcting_invoice_item2.id,
                    "name": "Some commodity",
                    "quantity": 0,  # 0 should be allowed for a correcting invoice
                    "unit_symbol": "szt.",
                    "net_unit_price": "10.00",
                    "tax_rate": "23.00",
                },
            ],
        }

        data |= kwargs.get('additional_fields', {})

        url = self.url.format(
            business_id=self.business.id,
            invoice_id=self.correcting_invoice.id,
        )
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_200_OK

    def test_basic_staffer_has_no_access(self):
        user = baker.make(User)
        baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            staff_user=user,
        )
        self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        url = self.url.format(
            business_id=self.business.id,
            invoice_id=self.correcting_invoice.id,
        )

        resp = self.fetch(url, method='PUT', body={})

        assert resp.code == status.HTTP_404_NOT_FOUND


@pytest.mark.django_db
class CorrectionHandlerTests(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/invoicing/invoice/{invoice_id}/correction'  # pylint: disable=line-too-long

    def setUp(self):
        super().setUp()
        self.invoice_a = baker.make(CustomerInvoice, business=self.business)
        self.invoice_item_a1 = baker.make(InvoiceItem, invoice=self.invoice_a, net_unit_price=500)
        self.invoice_item_a2 = baker.make(InvoiceItem, invoice=self.invoice_a, net_unit_price=500)
        self.invoice_cor_a = baker.make(
            CustomerInvoice,
            business=self.business,
            corrected_invoice=self.invoice_a,
            number='COR123',
        )

        self.invoice_b = baker.make(CustomerInvoice, business=self.business)
        self.invoice_item_b1 = baker.make(InvoiceItem, invoice=self.invoice_b, net_unit_price=500)
        self.invoice_item_b2 = baker.make(InvoiceItem, invoice=self.invoice_b, net_unit_price=500)

    def test_get(self):
        url = self.url.format(
            business_id=self.business.id,
            invoice_id=self.invoice_a.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['id'] == self.invoice_cor_a.id

        # Retrieving correction from invoice without correction
        url = self.url.format(
            business_id=self.business.id,
            invoice_id=self.invoice_b.id,
        )
        resp = self.fetch(url, method='GET')
        assert not resp.json.get('id')

    def test_post(self, **kwargs):
        staffer = baker.make(
            Resource, business=self.business, type=Resource.STAFF, name='Jan Kowalski'
        )
        baker.make(Seller, business=self.business)
        bci = baker.make(BusinessCustomerInfo, business=self.business)
        buyer = baker.make(Buyer, customer=bci, email='<EMAIL>')

        valid_data = {
            "issuing_staffer": staffer.id,
            "title": "Some title",
            "issue_date": "2019-10-23",
            "sale_date": "2019-10-23",
            "payment_term": "2019-10-23",
            "payment_method": "cash",
            "discount_amount": "20.00",
            "buyer": buyer.id,
            "items": [
                {
                    "name": self.invoice_item_b1.name,
                    "quantity": 5,
                    "unit_symbol": "szt.",
                    "net_unit_price": "10.00",
                    "tax_rate": "23.00",
                },
                {
                    "name": self.invoice_item_b2.name,
                    "quantity": 0,  # 0 should be allowed for a correcting invoice
                    "unit_symbol": "szt.",
                    "net_unit_price": "15.00",
                    "tax_rate": "8.00",
                },
                {
                    "name": "Some commodity",
                    "quantity": 2,
                    "unit_symbol": "szt.",
                    "net_unit_price": "10.00",
                    "tax_rate": "23.00",
                },
            ],
        }
        valid_data |= kwargs.get('additional_fields', {})
        url = self.url.format(
            business_id=self.business.id,
            invoice_id=self.invoice_b.id,
        )
        resp = self.fetch(url, method='POST', body=valid_data)
        assert resp.code == status.HTTP_201_CREATED

    def test_basic_staffer_has_no_access(self):
        user = baker.make(User)
        baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            staff_user=user,
        )
        self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        url = self.url.format(
            business_id=self.business.id,
            invoice_id=self.invoice_a.id,
        )

        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_404_NOT_FOUND

        resp = self.fetch(url, method='POST', body={})
        assert resp.code == status.HTTP_404_NOT_FOUND


@pytest.mark.django_db
class InvoicePdfPreviewHandlerTests(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/invoicing/invoice/{invoice_id}/preview/'  # pylint: disable=line-too-long

    def setUp(self):
        super().setUp()
        self.invoice = baker.make(CustomerInvoice, business=self.business)
        self.invoice_item = baker.make(InvoiceItem, invoice=self.invoice)

    def test_get(self):
        url = self.url.format(
            business_id=self.business.id,
            invoice_id=self.invoice.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.headers['Content-Type'] == 'application/pdf'

    def test_basic_staffer_has_no_access(self):
        user = baker.make(User)
        baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            staff_user=user,
        )
        self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        url = self.url.format(
            business_id=self.business.id,
            invoice_id=self.invoice.id,
        )

        resp = self.fetch(url, method='GET')

        assert resp.code == status.HTTP_404_NOT_FOUND


@pytest.mark.django_db
class NextNumberHandlerTests(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/invoicing/invoice/next_number/'  # pylint: disable=line-too-long

    def setUp(self):
        super().setUp()

        baker.make(Seller, business=self.business, invoice_prefix='INV', correction_prefix='AKTZ')

        self.invoice = baker.make(
            CustomerInvoice,
            business=self.business,
            issue_date=datetime.date(2019, 10, 23),
        )
        baker.make(
            CustomerInvoice,
            business=self.business,
            issue_date=datetime.date(2019, 10, 24),
        )
        baker.make(
            CustomerInvoice,
            business=self.business,
            corrected_invoice=self.invoice,
            issue_date=datetime.date(2019, 10, 25),
        )

    def test_get(self):
        url = (self.url + '?issue_date={issue_date}').format(
            business_id=self.business.id, issue_date='2019-10-30'
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['number'] == 'INV/2019/10/3'

        resp = self.fetch(url + '&for_correction=True', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['number'] == 'AKTZ/2019/10/2'

    def test_basic_staffer_has_no_access(self):
        user = baker.make(User)
        baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            staff_user=user,
        )
        self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        url = self.url.format(business_id=self.business.id)

        resp = self.fetch(url, method='GET')

        assert resp.code == status.HTTP_404_NOT_FOUND
