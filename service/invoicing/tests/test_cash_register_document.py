import datetime
from decimal import Decimal

import pytest
from model_bakery import baker
from model_bakery.recipe import Recipe
from rest_framework import status

from service.tests import BaseAsyncHTTPTest
from webapps.business.models import Business
from webapps.invoicing.baker_recipes import (
    buyer_recipe,
    customer_recipe,
    seller_recipe,
    staffer_recipe,
)
from webapps.invoicing.models import (
    CashRegisterDocument,
    CashRegisterDocumentItem,
    CashRegisterDocumentType,
)
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import POS, PaymentType
from webapps.register.models import Register, RegisterOperation


@pytest.mark.django_db
class CashRegisterDocumentListHandlerTests(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/invoicing/cash_register_document/'  # pylint: disable=line-too-long

    def tearDown(self):
        RegisterOperation.all_objects.all().delete()
        Register.all_objects.all().delete()
        super().tearDown()

    def test_get(self):
        url = self.url.format(business_id=self.business.id)

        cash_receipt_a, cash_receipt_b = baker.make(
            CashRegisterDocument, business=self.business, _quantity=2
        )
        cash_receipt_c = baker.make(CashRegisterDocument)

        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert len(resp.json['results']) == 2
        assert resp.json['items_count'] == 2

        ids = [x['id'] for x in resp.json['results']]
        assert cash_receipt_a.id in ids
        assert cash_receipt_b.id in ids
        assert cash_receipt_c.id not in ids

        # On cash register receipts list we do not want to serialize items
        assert 'items' not in resp.json['results'][0]

    def test_get_with_search(self):
        url = self.url.format(business_id=self.business.id)
        recipe = Recipe(CashRegisterDocument, business=self.business)
        kw_a = recipe.make(type=CashRegisterDocumentType.KW, number='a')
        kp_a = recipe.make(type=CashRegisterDocumentType.KP, number='a2')
        kw_b = recipe.make(type=CashRegisterDocumentType.KW, number='b')

        resp = self.fetch(f'{url}?query=a', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['results']]
        assert kw_a.id in ids
        assert kp_a.id in ids
        assert kw_b.id not in ids

        resp = self.fetch(f'{url}?type=KW', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['results']]
        assert kw_a.id in ids
        assert kp_a.id not in ids
        assert kw_b.id in ids

        resp = self.fetch(f'{url}?type=KW&query=a', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        ids = [x['id'] for x in resp.json['results']]
        assert kw_a.id in ids
        assert kp_a.id not in ids
        assert kw_b.id not in ids

    def test_post(self):
        pos = baker.make(POS, business=self.business)
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.CASH)
        cash_register = baker.make(Register, pos=pos, is_open=True)
        staffer = staffer_recipe.make(business=self.business)
        seller_recipe.make(business=self.business)
        buyer = buyer_recipe.make(
            customer=customer_recipe.make(business=self.business),
        )

        valid_data = {
            "issuing_staffer": staffer.id,
            "number": "KW/2020/1",
            "type": "KW",
            "issue_date": "2019-10-23",
            "place_of_issue": "Warsaw",
            "buyer": buyer.id,
            "cash_register": cash_register.id,
            "items": [
                {
                    "name": "Some item",
                    "amount": "21.59",
                    "account": "****************",
                }
            ],
        }
        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_data)
        assert resp.code == status.HTTP_201_CREATED, resp.json
        assert resp.json
        cash_register_document = CashRegisterDocument.objects.filter(id=resp.json['id']).first()
        assert cash_register_document
        assert cash_register_document.total_amount == Decimal('21.59')
        assert cash_register_document.register_operation
        assert cash_register_document.register_operation.amount == Decimal('-21.59')

        valid_data['number'] = 'KW/2020/2'
        valid_data['note'] = 'This is note'
        resp = self.fetch(url, method='POST', body=valid_data)
        assert resp.code == status.HTTP_201_CREATED, resp.json
        assert resp.json
        cash_register_document = CashRegisterDocument.objects.filter(id=resp.json['id']).first()
        assert cash_register_document.register_operation.note == valid_data['note']

    def test_post_invalid(self):
        pos = baker.make(POS, business=self.business)
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.CASH)
        cash_register = baker.make(Register, pos=pos, is_open=True)
        staffer = staffer_recipe.make(business=self.business)

        valid_data = {
            "type": "KP",
            "number": None,
            "buyer_name": "",
            "issue_date": "2021-02-26",
            "issuing_staffer": staffer.id,
            "items": [{"name": None, "amount": None, "account": ""}],
            "cash_register": cash_register.id,
        }

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_data)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json['errors'] == [
            {
                "field": "non_field_errors",
                "description": "Business does not have " "seller details configured",
                "code": "invoice_business_details",
            }
        ]


@pytest.mark.django_db
class CashRegisterDocumentDetailsHandlerTests(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/invoicing/cash_register_document/{id}/'

    def setUp(self):
        super().setUp()

        pos = baker.make(POS, business=self.business)
        payment_type = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.CASH)
        cash_register = baker.make(Register, pos=pos, is_open=True)
        self.document_a = baker.make(
            CashRegisterDocument,
            business=self.business,
            register_operation=baker.make(
                RegisterOperation, payment_type=payment_type, register=cash_register, note='aaa'
            ),
        )
        self.item_a = baker.make(CashRegisterDocumentItem, cash_register_document=self.document_a)

        self.business_b = baker.make(Business)
        self.document_b = baker.make(CashRegisterDocument, business=self.business_b)

    def test_get(self):
        url = self.url.format(
            business_id=self.business.id,
            id=self.document_a.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['id'] == self.document_a.id
        self.document_a.refresh_from_db()
        assert self.document_a.register_operation.note == 'aaa'

        # Retrieving invoice from other business should fail
        url = self.url.format(
            business_id=self.business_b.id,
            id=self.document_b.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_400_BAD_REQUEST

    def test_delete(self):
        url = self.url.format(
            business_id=self.business.id,
            id=self.document_a.id,
        )
        resp = self.fetch(url, method='DELETE')
        assert resp.code == status.HTTP_200_OK

        assert not CashRegisterDocument.objects.filter(
            id=self.document_a.id,
        ).exists()
        assert CashRegisterDocument.all_objects.filter(
            id=self.document_a.id,
        ).exists()

        # It should not be possible to delete cash receipt from other business
        url = self.url.format(
            business_id=self.business_b.id,
            id=self.document_a.id,
        )
        resp = self.fetch(url, method='DELETE')
        assert resp.code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.usefixtures('seller_details')
    @pytest.mark.usefixtures('buyer_details')
    def test_put(self):
        staffer = staffer_recipe.make(business=self.business)
        buyer = buyer_recipe.make(
            customer=customer_recipe.make(business=self.business),
        )
        item_a2 = baker.make(CashRegisterDocumentItem, cash_register_document=self.document_a)

        data = {
            "issuing_staffer": staffer.id,
            "type": "KW",
            "issuing_staffer_name": "Jan Kowalski",
            "number": "KW/1",
            "place_of_issue": "Some place",
            "issue_date": "2019-10-23",
            "buyer": buyer.id,
            **self.seller_details,
            **self.buyer_details,
            "items": [
                {
                    # This item should get updated
                    "id": self.item_a.id,
                    "name": "Some commodity",
                    "amount": "26.89",
                    "account": "0000",
                },
                {
                    # This is new item that should be added
                    "name": "New item",
                    "amount": "33.11",
                    "account": "1111",
                },
                # The item_a2 is missing so it should get deleted
            ],
            'note': 'note',
        }

        url = self.url.format(
            business_id=self.business.id,
            id=self.document_a.id,
        )
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_200_OK, resp
        assert resp.json

        self.document_a.refresh_from_db()
        assert self.document_a.number == data['number']
        assert self.document_a.issue_date == datetime.date(2019, 10, 23)
        assert self.document_a.total_amount == Decimal('60')
        assert self.document_a.register_operation.note == 'note'

        self.item_a.refresh_from_db()
        assert self.item_a in self.document_a.items.all()
        assert self.item_a.name == 'Some commodity'
        assert self.item_a.amount == 2689  # minor units

        assert item_a2 not in self.document_a.items.all()

        new_item = self.document_a.items.filter(name='New item').first()
        assert new_item
        assert new_item.amount == 3311  # minor units

        # Updating receipt from other business should be forbidden
        url = self.url.format(
            business_id=self.business_b.id,
            id=self.document_b.id,
        )
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_400_BAD_REQUEST


@pytest.mark.django_db
class CashRegiserDocumentPdfPreviewHandlerTests(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/invoicing/cash_register_document/{id}/preview/'

    def test_get(self):
        document = baker.make(CashRegisterDocument, business=self.business)
        baker.make(CashRegisterDocumentItem, cash_register_document=document, _quantity=3)

        url = self.url.format(business_id=self.business.id, id=document.id)
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.headers['Content-Type'] == 'application/pdf'
