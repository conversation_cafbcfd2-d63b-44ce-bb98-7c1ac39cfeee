import logging
import typing as t
from collections import OrderedDict

from django.conf import settings
from lib.elasticsearch.consts import ESDocType
from lib.feature_flag.feature import OptimizeBListingsFlag
from lib.feature_flag.killswitch import DisableBListingsFlag
from lib.searchables.response import SerializerResponse
from service.search.mixins import SearchModificationMixin
from webapps.business.searchables.business import BusinessSearchable
from webapps.business.searchables.serializers.business import BusinessHitSerializer

log = logging.getLogger('booksy.es_debug')


class SearchResults(t.NamedTuple):
    businesses: SerializerResponse
    modifications: list
    data: OrderedDict
    b_listings: t.Optional[SerializerResponse] = None


class SearchKwargs(t.NamedTuple):
    page: int
    per_page: int
    serializer: BusinessHitSerializer


class SearchEngine(SearchModificationMixin):
    DEFAULT_RANK_CONDITION = 4

    @classmethod
    def search(cls, data, search_kwargs, searchable=BusinessSearchable):
        response, modifications, data = cls.search_businesses(data, search_kwargs, searchable)

        b_listings = None
        if data.get('include_b_listing') == 1:
            if OptimizeBListingsFlag():
                try:
                    b_listings_count = int(
                        response.aggregations['b_listings']['b_listings']['value']
                    )
                except KeyError:
                    b_listings_count = 0
                b_listings = cls.search_extra_b_listing_v2(
                    data,
                    search_kwargs,
                    biz_count=response.hits.total.value,
                    b_listings_count=b_listings_count,
                )
            else:
                b_listings = cls.search_extra_b_listing(
                    data,
                    search_kwargs,
                    biz_count=response.hits.total.value,
                )

        return SearchResults(
            businesses=response, modifications=modifications, data=data, b_listings=b_listings
        )

    @classmethod
    def search_businesses(
        cls,
        data: OrderedDict,
        search_kwargs: SearchKwargs,
        searchable: t.Type[BusinessSearchable] = BusinessSearchable,
    ) -> tuple[SerializerResponse, list, OrderedDict]:
        """Main search for business listing.

        It makes a search as specified in form data.
        If the results are not satisfactory, the search parameters
        are modified and search is repeated.

        Returns: (res, modifications, data)
        res - final search results
        modifications - list of modifications to original search params
                        that have been made
        data - data after modifications
        """
        start = (search_kwargs.page - 1) * search_kwargs.per_page
        params = {
            'from_': start,
            'size': search_kwargs.per_page,
            'no_thumbs': data.get('no_thumbs', False),
        }

        data['rank_condition'] = cls.DEFAULT_RANK_CONDITION
        if data.pop('explain_mode', None):
            params['explain'] = True

        if data.get('sort_order'):
            # After main sort parameter we need, sort by score
            # Comment from docs:
            # ' When sorting on a field, scores are not computed.
            #   By setting track_scores to true,
            #   scores will still be computed and tracked.'
            params['track_scores'] = True

        # TODO: Remove when UTT2 will be fully operational
        if data['in_experiment']:
            apply_utt2_search_keys(data)

        search_query = searchable(
            ESDocType.BUSINESS,
            serializer=search_kwargs.serializer,
        ).params(**params)
        response = search_query.execute(data)
        # SEARCH MODIFICATIONS (due to insufficient results)
        modifications = []
        # widen search if 'location_id' search has not enough results
        if cls._should_widen_search(data, response, search_kwargs.per_page):
            log.debug('Widen search')
            data, modification = cls._prepare_widen_search(data, response, search_kwargs.per_page)
            # rerun search
            response = search_query.execute(data)
            modifications.append(modification)

        return response, modifications, data

    @classmethod
    def search_extra_b_listing(cls, data, search_kwargs, biz_count):
        offset, size = cls.calculate_offset(biz_count, search_kwargs.page, search_kwargs.per_page)
        searchable = BusinessSearchable(
            ESDocType.BUSINESS,
            serializer=search_kwargs.serializer,
        ).params(
            from_=offset,
            size=size,
            no_thumbs=data.get('no_thumbs', False),
        )

        if DisableBListingsFlag():
            return searchable.none()

        data = data.copy()
        if data.get('sort_order') not in settings.EXTBIZ_SORT_ORDER_CHOICES:
            data['sort_order'] = settings.DEFAULT_SORT_ORDER
        data['only_b_listings'] = True

        return searchable.execute(data)

    @classmethod
    def search_extra_b_listing_v2(cls, data, search_kwargs, biz_count, b_listings_count=None):
        if not DisableBListingsFlag():
            offset, size = cls.calculate_offset(
                biz_count,
                search_kwargs.page,
                search_kwargs.per_page,
            )
            b_listings_count = b_listings_count or 0
        else:
            offset, size = 0, 0
            b_listings_count = 0

        searchable = BusinessSearchable(
            ESDocType.BUSINESS,
            serializer=search_kwargs.serializer,
        ).params(
            from_=offset,
            size=size,
            no_thumbs=data.get('no_thumbs', False),
        )

        if size == 0 or b_listings_count == 0:
            return searchable.none(total_hits=b_listings_count)

        data = data.copy()
        if data.get('sort_order') not in settings.EXTBIZ_SORT_ORDER_CHOICES:
            data['sort_order'] = settings.DEFAULT_SORT_ORDER
        data['only_b_listings'] = True

        return searchable.execute(data)

    @staticmethod
    def calculate_offset(biz_count: int, page: int, per_page: int) -> tuple[int, int]:
        """
        Return pagination params for elasticsearch for b-listings.

        We want to fill-in listing of regular businesses with b-listings up to a full page.
        If user asks for a later page, paginate b-listings respecting initial page break.
        """
        last_page = biz_count // per_page + 1
        last_page_elem_num = biz_count % per_page
        if page < last_page:
            offset = 0
            size = 0  # we only search for the count
        elif page == last_page:
            offset = 0
            size = per_page - last_page_elem_num
        else:
            offset = ((page - last_page) * per_page) - last_page_elem_num
            size = per_page
        return offset, size


def apply_utt2_search_keys(data: t.Dict[str, t.Any]) -> None:
    for key in ['category', 'treatment', 'gender']:
        if key in data:
            value = data.pop(key)
            data[f'{key}_utt'] = value
