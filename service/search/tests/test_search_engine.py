from unittest.mock import patch

import pytest
from django.conf import settings
from django.test import override_settings

from lib.elasticsearch.consts import ESIndex
from lib.feature_flag.feature import OptimizeBListingsFlag
from lib.feature_flag.killswitch import DisableBListingsFlag
from lib.tests.utils import override_eppo_feature_flag
from service.mixins.validation import ValidationMixin
from service.search.search_engine import SearchEngine, SearchKwargs
from service.search.serializers import BusinessSearchSerializer
from webapps.business.searchables.business import BusinessSearchable
from webapps.business.searchables.serializers import (
    BusinessSearchHitSerializer,
)
from webapps.business.searchables.tests.utils import (
    create_business_with_params,
)
from webapps.structure.models import Region


GEO_CENTER_OF_THE_COUNTRY = {'latitude': 39.820223, 'longitude': -98.6673045}  # US
GEO_SEARCH_CENTER = {'latitude': 27.9949, 'longitude': -82.5033}

BUSINESS_DATA = [
    {'location': (27.9949, -82.5032), 'quantity': 1},  # <1 km from GEO_SEARCH_CENTER
    {'location': (28.053178, -82.455821), 'quantity': 1},  # 8 km from GEO_SEARCH_CENTER
    {'location': (27.968926, -82.3481699), 'quantity': 4},  # 15.5 km from GEO_SEARCH_CENTER
    {
        # 8.07 km from GEO_CENTER_OF_THE_COUNTRY and >31km from GEO_SEARCH_CENTER
        'location': (39.8445980, -98.578215),
        'quantity': 5,
    },
    {'location': (27.9949, -82.5032), 'quantity': 2, 'is_b_listing': True},
    {'location': (27.968926, -82.3481699), 'quantity': 1, 'is_b_listing': True},
]


def is_none_response(response) -> bool:
    """
    Return whether we skipped elasticsearch request and returned result using `Searchable.none()`.
    """
    return (
        response is not None
        and response.took == 0
        and not response.hits.hits
        and not hasattr(response, '_shards')
    )


@pytest.fixture
def create_regions(db, clean_index_module_fixture):
    clean_index_module_fixture(ESIndex.REGION)

    country = Region(
        name=settings.COUNTRY_CONFIG.name,
        type=Region.Type.COUNTRY,
        latitude=GEO_CENTER_OF_THE_COUNTRY['latitude'],
        longitude=GEO_CENTER_OF_THE_COUNTRY['longitude'],
    )
    country.save()
    country.reindex(refresh_index=True)

    with patch('service.search.mixins.get_operating_country', return_value=country):
        yield


@pytest.fixture(scope='module')
def create_businesses(clean_index_module_fixture):
    index = clean_index_module_fixture(ESIndex.BUSINESS)

    next_business_id = 1
    for data in BUSINESS_DATA:
        for _ in range(data['quantity']):
            create_business_with_params(
                {
                    'id': next_business_id,
                    'business_location': {
                        'coordinate': {
                            'lat': data['location'][0],
                            'lon': data['location'][1],
                        }
                    },
                    'is_b_listing': data.get('is_b_listing', False),
                }
            )
            next_business_id += 1

    index.refresh()


@pytest.mark.django_db
@override_settings(ES_WIDEN_SEARCH_RESULTS_THRESHOLD=3)
@pytest.mark.usefixtures('create_regions', 'create_businesses')
@pytest.mark.parametrize(
    'expected_radius, expected_business_ids',
    [
        (18000, {1, 2, 3, 4, 5, 6}),
    ],
)
def test_radius_modification_in_the_near_me_search(expected_radius, expected_business_ids):
    data = {
        'location_geo': f"{GEO_SEARCH_CENTER['latitude']},{GEO_SEARCH_CENTER['longitude']}",
    }

    serializer = BusinessSearchSerializer(data=data)
    data = ValidationMixin().validate_serializer(serializer)

    search_kwargs = SearchKwargs(page=1, per_page=20, serializer=BusinessSearchHitSerializer())

    searchable = BusinessSearchable
    search_results = SearchEngine.search(data, search_kwargs, searchable)

    assert search_results.modifications[0]['values']['distance_radius'] == expected_radius
    assert search_results.businesses.hits.total.value == len(expected_business_ids)
    assert {business['id'] for business in search_results.businesses} == expected_business_ids


@pytest.mark.django_db
@pytest.mark.usefixtures('create_regions', 'create_businesses')
@override_eppo_feature_flag({OptimizeBListingsFlag.flag_name: True})
@override_settings(ES_WIDEN_SEARCH_RESULTS_THRESHOLD=3)
def test_radius_modification_in_the_near_me_search_with_b_listings():
    """
    Ensure that we widen the search radius based on the total of real businesses,
    not counting b-listings.
    """
    serializer = BusinessSearchSerializer(
        data={
            'location_geo': f"{GEO_SEARCH_CENTER['latitude']},{GEO_SEARCH_CENTER['longitude']}",
            'include_b_listing': 1,
        }
    )
    assert serializer.is_valid(), serializer.errors

    search_kwargs = SearchKwargs(page=1, per_page=20, serializer=BusinessSearchHitSerializer())
    search_results = SearchEngine.search(serializer.validated_data, search_kwargs)

    expected_radius = 18000
    expected_business_ids = {1, 2, 3, 4, 5, 6}
    assert search_results.modifications[0]['values']['distance_radius'] == expected_radius
    assert search_results.businesses.hits.total.value == len(expected_business_ids)
    assert {business['id'] for business in search_results.businesses} == expected_business_ids


@pytest.mark.django_db
@pytest.mark.parametrize(
    'biz_count, page, per_page, expected_offset, expected_size',
    [
        (0, 1, 20, 0, 20),
        (1, 1, 20, 0, 19),
        (2, 1, 20, 0, 18),
        (20, 1, 20, 0, 0),
        (60, 1, 20, 0, 0),
        (61, 1, 20, 0, 0),
        (62, 1, 20, 0, 0),
        (60, 2, 20, 0, 0),
        (61, 2, 20, 0, 0),
        (62, 2, 20, 0, 0),
        (60, 4, 20, 0, 20),
        (61, 4, 20, 0, 19),
        (62, 4, 20, 0, 18),
        (60, 5, 20, 20, 20),
        (61, 5, 20, 19, 20),
        (62, 5, 20, 18, 20),
    ],
)
def test_calculate_offset(biz_count, page, per_page, expected_offset, expected_size):
    offset, size = SearchEngine.calculate_offset(biz_count, page, per_page)

    assert (offset, size) == (expected_offset, expected_size)


@pytest.mark.django_db
@pytest.mark.usefixtures('create_regions', 'create_businesses')
@override_eppo_feature_flag(
    {
        DisableBListingsFlag.flag_name: False,
        OptimizeBListingsFlag.flag_name: False,
    }
)
def test_include_b_listing__enabled():
    serializer = BusinessSearchSerializer(
        data={
            'location_geo': f"{GEO_SEARCH_CENTER['latitude']},{GEO_SEARCH_CENTER['longitude']}",
            'include_b_listing': 1,
        }
    )
    assert serializer.is_valid(), serializer.errors

    search_kwargs = SearchKwargs(
        page=1,
        per_page=20,
        serializer=BusinessSearchHitSerializer(),
    )
    results = SearchEngine.search(serializer.validated_data, search_kwargs)

    assert all(hit['_source']['is_b_listing'] is False for hit in results.businesses.hits.hits)
    assert all(hit['_source']['is_b_listing'] is True for hit in results.b_listings.hits.hits)
    assert not is_none_response(results.b_listings)
    assert results.b_listings.hits.total == {'value': 3, 'relation': 'eq'}


@pytest.mark.django_db
@pytest.mark.usefixtures('create_regions', 'create_businesses')
@override_eppo_feature_flag({DisableBListingsFlag.flag_name: True})
def test_include_b_listing__disabled():
    serializer = BusinessSearchSerializer(
        data={
            'location_geo': f"{GEO_SEARCH_CENTER['latitude']},{GEO_SEARCH_CENTER['longitude']}",
            'include_b_listing': 1,
        }
    )
    assert serializer.is_valid(), serializer.errors

    search_kwargs = SearchKwargs(
        page=1,
        per_page=20,
        serializer=BusinessSearchHitSerializer(),
    )
    results = SearchEngine.search(serializer.validated_data, search_kwargs)

    # Despite requested `include_b_listing`, with kill-switch enabled expect to pretend
    # there are no b-listings.
    assert all(hit['_source']['is_b_listing'] is False for hit in results.businesses.hits.hits)
    assert is_none_response(results.b_listings)
    assert results.b_listings.hits.total == {'value': 0, 'relation': 'eq'}


@pytest.mark.django_db
@pytest.mark.usefixtures('create_regions', 'create_businesses')
@override_eppo_feature_flag(
    {
        DisableBListingsFlag.flag_name: False,
        OptimizeBListingsFlag.flag_name: True,
    }
)
def test_include_b_listing__optimized():
    serializer = BusinessSearchSerializer(
        data={
            'location_geo': f"{GEO_SEARCH_CENTER['latitude']},{GEO_SEARCH_CENTER['longitude']}",
            'include_b_listing': 1,
        }
    )
    assert serializer.is_valid(), serializer.errors

    # Ask for the first page of listing where no b-listings should be displayed.
    # Expect b-listings NOT to be fetched from elasticsearch, but b-listings count to be included
    # as frontends use it for pagination.
    page_1_results = SearchEngine.search(
        serializer.validated_data,
        SearchKwargs(
            page=1,
            per_page=5,
            serializer=BusinessSearchHitSerializer(),
        ),
    )
    assert all(
        hit['_source']['is_b_listing'] is False for hit in page_1_results.businesses.hits.hits
    )
    assert is_none_response(page_1_results.b_listings)
    assert len(page_1_results.b_listings) == 0
    assert page_1_results.b_listings.hits.total == {'value': 3, 'relation': 'eq'}

    # Fetch b-listings once the end of businesses have been reached.
    page_2_results = SearchEngine.search(
        serializer.validated_data,
        SearchKwargs(
            page=2,
            per_page=5,
            serializer=BusinessSearchHitSerializer(),
        ),
    )
    assert all(
        hit['_source']['is_b_listing'] is False for hit in page_2_results.businesses.hits.hits
    )
    assert all(
        hit['_source']['is_b_listing'] is True for hit in page_2_results.b_listings.hits.hits
    )
    assert not is_none_response(page_2_results.b_listings)
    assert len(page_2_results.businesses.hits.hits) == 1
    assert len(page_2_results.b_listings.hits.hits) == 3
    assert page_2_results.b_listings.hits.total == {'value': 3, 'relation': 'eq'}
