import pytest
from model_bakery import baker
from service.search.mixins import ExtendCategoryMixin

from webapps.business.models.category import BusinessCategory


@pytest.mark.django_db
@pytest.mark.random_failure  # https://booksy.atlassian.net/browse/PY-194
def test_extend_category_main():

    main_category = baker.make(BusinessCategory, type=BusinessCategory.CATEGORY)
    subcat = baker.make(BusinessCategory, type=BusinessCategory.CATEGORY, parent=main_category)
    baker.make(BusinessCategory, type=BusinessCategory.CATEGORY)

    search_data = {'category': [main_category.id]}

    ExtendCategoryMixin.extend_categories(search_data)

    assert main_category.id in search_data['category']
    assert subcat.id in search_data['category']


@pytest.mark.django_db
def test_extend_category_sub():

    main_category = baker.make(BusinessCategory, type=BusinessCategory.CATEGORY)
    subcat = baker.make(BusinessCategory, type=BusinessCategory.CATEGORY, parent=main_category)
    baker.make(BusinessCategory, type=BusinessCategory.CATEGORY)

    search_data = {'category': [subcat.id]}

    ExtendCategoryMixin.extend_categories(search_data)

    assert main_category.id not in search_data['category']
    assert subcat.id in search_data['category']


@pytest.mark.django_db
def test_extend_category_empty():
    main_category = baker.make(BusinessCategory, type=BusinessCategory.CATEGORY)
    baker.make(BusinessCategory, type=BusinessCategory.CATEGORY, parent=main_category)
    baker.make(BusinessCategory, type=BusinessCategory.CATEGORY)

    search_data = {'category': []}

    ExtendCategoryMixin.extend_categories(search_data)

    assert search_data['category'] == []
