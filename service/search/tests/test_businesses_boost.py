import pytest
from model_bakery import baker
from rest_framework import status

import settings
from lib.elasticsearch.consts import ESIndex
from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.boost import (
    BoostScoreRandomizationCustomExperiment,
    BoostScoreRandomizationExperiment,
)
from lib.tests.utils import (
    override_eppo_feature_flag,
)
from service.tests import BaseAsyncHTTPTest
from webapps.business.models import Business
from webapps.elasticsearch.elastic import ELASTIC
from webapps.kill_switch.models import KillSwitch
from webapps.search_engine_tuning.models import BusinessTuning


@pytest.mark.django_db
class BusinessBoostSearchHandlerTestCase(BaseAsyncHTTPTest):
    url = '/customer_api/businesses/?location_id={}'

    def setUp(self):
        super().setUp()
        baker.make(KillSwitch, name=KillSwitch.System.UTT2_BACKEND, is_killed=True)
        region = self.business.region
        region.latitude = 40.6331
        region.longitude = -89.3985
        region.save()
        region.reindex(refresh_index=True)

        self._prepare_boost_business()
        self._index_business()

    def _prepare_boost_business(self):
        baker.make(BusinessTuning, business_id=self.business.id, hidden_on_web=False)
        self.business.boost_status = Business.BoostStatus.ENABLED
        self.business.save()

    def _index_business(self):
        index = ELASTIC.indices[ESIndex.BUSINESS]
        business_document = self.business.get_document()
        business_document['promoted'] = True
        business_document['mp_promotion'] = 0.35
        business_document['promotion_boost'] = 1.0
        business_document.save()
        index.refresh()

    def _do_search(self):
        region_id = self.business.region_id
        headers = self.get_headers(self.url)
        headers['X-FINGERPRINT'] = 'test-fingerprint'

        response = self.fetch(path=self.url.format(region_id), method='GET', headers=headers)
        self.assertEqual(response.code, status.HTTP_200_OK)

        return response.json

    def test_calculate_boost_default(self):
        body = self._do_search()

        self.assertEqual(body['businesses_count'], 1)

        business = body['businesses'][0]
        self.assertEqual(business['id'], self.business.id)
        self.assertEqual(business['promoted'], True)
        self.assertGreaterEqual(business['score'], 3701)

    @override_eppo_feature_flag(
        {
            BoostScoreRandomizationExperiment.flag_name: ExperimentVariants.CONTROL,
            BoostScoreRandomizationCustomExperiment.flag_name: {},
        }
    )
    def test_calculate_boost_using_commission(self):
        body = self._do_search()

        self.assertEqual(body['businesses_count'], 1)

        business = body['businesses'][0]
        self.assertEqual(business['id'], self.business.id)
        self.assertEqual(business['promoted'], True)
        self.assertGreaterEqual(business['score'], 3701)

    @override_eppo_feature_flag(
        {
            BoostScoreRandomizationExperiment.flag_name: ExperimentVariants.VARIANT_A,
            BoostScoreRandomizationCustomExperiment.flag_name: {
                ExperimentVariants.VARIANT_A: {
                    's_1': 1.0,
                    's_2': 1.0,
                    'weight': 1.0,
                }
            },
        }
    )
    def test_calculate_boost_without_commission(self):
        body = self._do_search()

        self.assertEqual(body['businesses_count'], 1)

        business = body['businesses'][0]
        self.assertEqual(business['id'], self.business.id)
        self.assertEqual(business['promoted'], True)
        assert business['score'] == pytest.approx(3.0)

    @override_eppo_feature_flag(
        {
            BoostScoreRandomizationExperiment.flag_name: ExperimentVariants.VARIANT_A,
            BoostScoreRandomizationCustomExperiment.flag_name: {},
        }
    )
    def test_calculate_boost_without_commission_and_default_settings(self):
        body = self._do_search()

        self.assertEqual(body['businesses_count'], 1)

        business = body['businesses'][0]
        self.assertEqual(business['id'], self.business.id)
        self.assertEqual(business['promoted'], True)
        self.assertGreaterEqual(business['score'], settings.BOOST.SCORE_1)

    @override_eppo_feature_flag(
        {
            BoostScoreRandomizationExperiment.flag_name: ExperimentVariants.VARIANT_B,
            BoostScoreRandomizationCustomExperiment.flag_name: {
                ExperimentVariants.VARIANT_B: {
                    's_1': 1.0,
                    's_2': 1.0,
                    'weight': 1.0,
                }
            },
        }
    )
    def test_randomize_boost_with_linear_function(self):
        body = self._do_search()

        self.assertEqual(body['businesses_count'], 1)

        business = body['businesses'][0]
        self.assertEqual(business['id'], self.business.id)
        self.assertEqual(business['promoted'], True)
        self.assertGreaterEqual(business['score'], 1.0)
        self.assertLessEqual(business['score'], 3.0)

    @override_eppo_feature_flag(
        {
            BoostScoreRandomizationExperiment.flag_name: ExperimentVariants.VARIANT_B,
            BoostScoreRandomizationCustomExperiment.flag_name: {},
        }
    )
    def test_randomize_boost_with_linear_function_and_default_settings(self):
        body = self._do_search()

        self.assertEqual(body['businesses_count'], 1)

        business = body['businesses'][0]
        self.assertEqual(business['id'], self.business.id)
        self.assertEqual(business['promoted'], True)
        self.assertGreaterEqual(business['score'], settings.BOOST.SCORE_1)

    @override_eppo_feature_flag(
        {
            BoostScoreRandomizationExperiment.flag_name: ExperimentVariants.VARIANT_C,
            BoostScoreRandomizationCustomExperiment.flag_name: {
                ExperimentVariants.VARIANT_C: {
                    's_1': 1.0,
                    's_2': 1.0,
                    's_3': 1.0,
                    's_4': 0.0,
                    'weight': 1.0,
                }
            },
        }
    )
    def test_randomize_boost_with_exponential_function(self):
        body = self._do_search()

        self.assertEqual(body['businesses_count'], 1)

        business = body['businesses'][0]
        self.assertEqual(business['id'], self.business.id)
        self.assertEqual(business['promoted'], True)
        self.assertGreaterEqual(business['score'], 1.0)
        self.assertLessEqual(business['score'], 5.72)  # 3 + e^1

    @override_eppo_feature_flag(
        {
            BoostScoreRandomizationExperiment.flag_name: ExperimentVariants.VARIANT_C,
            BoostScoreRandomizationCustomExperiment.flag_name: {},
        }
    )
    def test_randomize_boost_with_exponential_function_and_default_settings(self):
        body = self._do_search()

        self.assertEqual(body['businesses_count'], 1)

        business = body['businesses'][0]
        self.assertEqual(business['id'], self.business.id)
        self.assertEqual(business['promoted'], True)
        self.assertGreaterEqual(business['score'], settings.BOOST.SCORE_1)
