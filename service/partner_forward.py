import functools
from abc import ABC, abstractmethod

from typing import Type

from webapps.business.context import get_current_business_context
from webapps.public_partners.models import PartnerPermissionBusiness


class PartnerForward(ABC):
    def __init__(self):
        self.business_context = get_current_business_context()
        if self.business_context is None:
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                'BusinessContext not activated'
            )

    @abstractmethod
    def forward(self, handler, *args, **kwargs): ...

    def should_forward(self):
        return PartnerPermissionBusiness.objects.filter(
            business=self.business_context.business_id,
            partner__merger_forward=True,
        ).exists()


def partner_forward(forward_view_class: Type[PartnerForward]):
    """
    Decorate methods which maybe should be forwarded to partner.
    """

    def decorator(method):
        @functools.wraps(method)
        def wrapper(handler, *args, **kwargs):
            forward_view = forward_view_class()
            if forward_view.should_forward():
                if forward_view_class.__name__ == 'BookAgainForward':
                    return method(handler, *args, **kwargs)
                return forward_view.forward(handler, *args, **kwargs)

            return method(handler, *args, **kwargs)

        return wrapper

    return decorator
