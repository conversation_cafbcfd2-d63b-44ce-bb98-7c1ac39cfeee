import subprocess

from django.core.management.base import BaseCommand

from lib.lokalise.utils import get_locale_full_path


class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        locale_path = get_locale_full_path('en')
        args = ['msgattrib', '--clear-fuzzy', '--empty', locale_path, '-o', locale_path]
        subprocess.check_output(args, shell=False)  # nosemgrep: bandit.B603
        self.stdout.write(self.style.SUCCESS('Fuzzy marked messages removed from en file'))
