from decimal import Decimal

from rest_framework import serializers
from rest_framework.fields import empty

from django.db.models import Q
from django.utils.translation import gettext as _

from lib.tools import tznow

from webapps.billing.enums import SubscriptionStatus
from webapps.billing.models import BillingSubscription


class RetryChargeRequestSerializer(serializers.Serializer):
    """
    Used by service.billing.purchase.AsyncRetryChargeHandler to validate
    request data.
    """

    subscription_id = serializers.IntegerField()

    def __init__(self, business_id, instance=None, data=empty, **kwargs):
        self.business_id = business_id
        super().__init__(instance=instance, data=data, **kwargs)

    def validate(self, attrs):
        subscription = BillingSubscription.objects.filter(
            Q(Q(date_expiry__isnull=True) | Q(date_expiry__gte=tznow())),
            id=attrs['subscription_id'],
            business_id=self.business_id,
            status=SubscriptionStatus.BLOCKED,
            balance__gt=Decimal(0),
            business__has_new_billing=True,
        )
        if not subscription.exists():
            raise serializers.ValidationError(
                _('Your subscription has expired or has a wrong status/balance')
            )
        return attrs
