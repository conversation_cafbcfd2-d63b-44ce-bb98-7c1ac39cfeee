from django.conf import settings
from rest_framework import status

from lib.serializers import PaginatorSerializer, safe_get
from service.billing.utils import billing_access
from service.tools import RequestHandler, session
from webapps.billing.models import (
    BillingCycle,
)
from webapps.billing.serializers import (
    InvoiceDetailsSerializer,
    InvoiceListHeaderSerializer,
)

from webapps.navision.serializers_helper import get_invoice_details_for_billing_history


class BillingListInvoicesHandler(RequestHandler):
    @session(login_required=True, api_key_required=True)
    @billing_access
    def get(self, business_id):
        """swagger:
          summary: Get billing history list. Ordered descending by date_end
          parameters:
            - name: business_id
              description: ID of the business.
              type: integer
              paramType: path
              required: True
            - name: page
              description: number of page in pagination
              paramType: query
              type: integer
            - name: per_page
              description: number of records per page
              paramType: query
              type: integer
          type: BillingHistoryListResponse
        :swagger

        swaggerModels:
            BillingHistoryListResponse:
                id: BillingHistoryListResponse
                properties:
                    invoices:
                        type: array
                        items:
                            type: InvoiceHeader

            InvoiceHeader:
                id: InvoiceHeader
                properties:
                    invoice_id:
                        type: integer
                    invoice_number:
                        type: string
                    status:
                        type: string
                        description:
                    date_start:
                        type: string
                    date_end:
                        type: string
                    total_gross_amount:
                        type: string

        """
        business = self.business_with_owner(business_id)

        data = self._prepare_get_arguments()
        serializer = PaginatorSerializer(data=data)
        data = self.validate_serializer(serializer)

        result = InvoiceListHeaderSerializer(
            business,
            offset=data['offset'],
            limit=data['per_page'],
        ).data
        self.finish_with_json(status.HTTP_200_OK, result)


class BillingInvoiceDetailsHandler(RequestHandler):
    @session(login_required=True, api_key_required=True)
    @billing_access
    def get(self, business_id, invoice_id):
        """swagger:
          summary: Get billing history details.
          parameters:
            - name: business_id
              description: ID of the business.
              type: integer
              paramType: path
              required: True
            - name: invoice_id
              description: ID of the invoice.
              type: integer
              paramType: path
              required: True
          type: BillingHistoryResponse
        :swagger

        swaggerModels:
            BillingHistoryResponse:
                id: BillingHistoryResponse
                properties:
                    seller:
                        type: object
                    buyer:
                        type: BusinessBuyer
                    invoice:
                        type: InvoiceDetails

            InvoiceDetails:
                id: InvoiceDetails
                properties:
                    invoice_id:
                        type: integer
                    invoice_number:
                        type: string
                    status:
                        type: string
                        description:
                            SUCCESS of FAILED based on transaction status
                    date_start:
                        type: string
                    date_end:
                        type: string
                    total_gross_amount:
                        type: string
                        description:
                            Price with discounts included
                    total_discount:
                        type: string
                    issue_date:
                        type: string
                    sale_date:
                        type: string
                    payment_method:
                        type: string
                        description:
                            C for Credit Card, P for PayPal, V for Venmo
                    invoice_items:
                        type: object
                        description:
                            invoice items split to SAAS, SMS and ADDITIONAL
                        properties:
                            SAAS:
                                type: array
                                items:
                                    type: InvoiceItem
                            SMS:
                                type: array
                                items:
                                    type: InvoiceItem
                            STAFFERS:
                                type: array
                                items:
                                    type: InvoiceItem
                            ADDITIONAL:
                                type: array
                                items:
                                    type: InvoiceItem

            InvoiceItem:
                id: InvoiceItem
                properties:
                    name:
                        type: string
                    quantity:
                        type: integer
                    total_gross_amount:
                        type: string
                        description:
                            Price with discounts included
                    base_gross_amount:
                        type: string
                        description:
                            Price with no discounts included
                    usage_from:
                        type: string
                    usage_to:
                        type: string
                    product_type:
                        type: string
        """
        # get buyer details
        business = self.business_with_owner(business_id)
        result = {
            'seller': settings.BOOKSY_SELLER_INVOICE_DATA,
        }
        buyer = safe_get(business, ['buyer'])
        buyer_data = get_invoice_details_for_billing_history(buyer)
        result['buyer'] = buyer_data

        billing_cycle = self.get_object_or_404(
            BillingCycle.objects.select_related('subscription'),
            business_id=business_id,
            id=invoice_id,
        )
        result['invoice'] = InvoiceDetailsSerializer(billing_cycle).data
        self.finish_with_json(status.HTTP_200_OK, result)
