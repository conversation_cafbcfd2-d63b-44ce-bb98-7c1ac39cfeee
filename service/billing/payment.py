from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from tornado import web

from service.billing.serializers import RetryChargeRequestSerializer
from service.billing.utils import billing_access
from service.tools import RequestHandler, json_request, session
from webapps.billing.models import (
    BillingBusinessSettings,
    BillingPaymentMethod,
)
from webapps.billing.serializers import PaymentMethodWithCreditCardSerializer
from webapps.billing.tasks import retry_charge_task


class BillingPaymentMethodHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_CONVERSION,)

    @session(login_required=True, api_key_required=True)
    @json_request
    @billing_access
    def get(self, business_id):
        """swagger:
          summary: Get current payment method details for business.
          parameters:
            - name: business_id
              description: ID of the business.
              type: integer
              paramType: path
              required: True
          type: BillingPaymentMethodResponse
        :swagger

        swaggerModels:
            BillingPaymentMethodResponse:
                id: BillingPaymentMethod
                properties:
                    payment_method_type:
                        type: string
                        enum:
                            - C
                            - P
                            - V
                        description: C is for credit card, P for PayPal,
                                 V for Venmo.
                        required: true
                    token:
                        type: string
                        description: unique identifier from payment processor
                        required: true
                    credit_card:
                        type: CreditCard
                        required: False

            CreditCard:
                id: CreditCard
                description: Saved credit card info with billing address details
                properties:
                    cardholder_name:
                        type: string
                    card_type:
                        type: string
                        enum:
                            - A
                            - V
                            - M
                            - E
                            - U
                        description:
                            A - American Express,
                            V - Visa,
                            M - Mastercard,
                            E - Maestro,
                            U - Unknown
                    bin:
                        type: string
                        description: first 5 digits of credit card number
                    last_4:
                        type: string
                        description: last 4 digits of credit card number
                    masked_number:
                        type: string
                    expiration_date:
                        type: string
                    country_of_issuance:
                        type: string
                        description: Country of issuance for credit card
                    street_address:
                        type: string
                    extended_address:
                        type: string
                    postal_code:
                        type: string
                        description: Billing address - postal code
                    locality:
                        type: string
                        description: Billing address - City
                    country_name:
                        type: string
                        description: Billing address - Country

        """
        business_settings, _ = BillingBusinessSettings.objects.get_or_create(
            business_id=business_id
        )

        payment_method = (
            BillingPaymentMethod.objects.filter(
                business_id=business_id,
                default=True,
                payment_processor=business_settings.payment_processor,
            )
            .order_by('-id')
            .first()
        )
        if payment_method is None:
            raise web.HTTPError(status.HTTP_404_NOT_FOUND)

        serializer = PaymentMethodWithCreditCardSerializer(instance=payment_method)
        self.finish_with_json(status.HTTP_200_OK, serializer.data)


class AsyncRetryChargeHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_CONVERSION,)

    @session(login_required=True, api_key_required=True)
    @json_request
    @billing_access
    def post(self, business_id):
        """swagger:
          summary: Retry charge for subscription.
          parameters:
            - name: business_id
              description: ID of the business.
              type: integer
              paramType: path
              required: True
            - name: subscription_id
              type: integer
              paramType: body
              required: True
          type: TaskIDResponse
        :swagger

        swaggerModels:
            TaskIDResponse:
                id: TaskIDResponse
                properties:
                    task_id:
                        type: string
        """
        serializer = RetryChargeRequestSerializer(
            data=self.data,
            business_id=business_id,
        )
        self.validate_serializer(serializer)
        task = retry_charge_task.delay(
            business_id=business_id,
            subscription_id=serializer.validated_data['subscription_id'],
            operator_id=self.user.id,
        )
        ret = {
            'task_id': task.id,
        }
        self.finish_with_json(status.HTTP_202_ACCEPTED, ret)
