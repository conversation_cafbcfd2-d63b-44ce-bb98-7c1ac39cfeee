from datetime import timedel<PERSON>
from decimal import Decimal

import pytest
from django.test import override_settings
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status

from country_config import Country
from lib.tools import tznow
from service.billing.tests import BillingEndpointTestCase
from settings.billing import SMS_COST_ALERT_DEFAULT_LEVEL
from webapps import consts
from webapps.billing.enums import (
    DiscountType,
    PaymentProcessorType,
    ProductType,
    PurchaseFlowAuthorization,
)
from webapps.billing.models import (
    BillingBusinessOffer,
    BillingBusinessSettings,
    BillingOfflineMigration,
    BillingProduct,
    BillingProductOffer,
    BillingProductOfferItem,
)
from webapps.business.enums import BoostPaymentSource
from webapps.business.models import Business
from webapps.business.models.business_change import BusinessChange
from webapps.kill_switch.models import KillSwitch
from webapps.navision.models.settings import NavisionSettings
from webapps.navision.models.tax_rate import TaxGroup
from webapps.purchase.models import SubscriptionBuyer
from webapps.structure.baker_recipes import bake_region_graphs
from webapps.structure.models import Region


@pytest.mark.django_db
class TestBillingBusinessSettingsHandler(BillingEndpointTestCase):
    url = '/business_api/me/businesses/{business_id}/billing/settings/?'

    def setUp(self):
        super().setUp()
        self.business_settings = baker.make(
            BillingBusinessSettings,
            business=self.business,
            sms_cost_alert_level=3,
        )

    @parameterized.expand(BillingEndpointTestCase.billing_access_defaults)
    def test_access(self, scenario, response_codes):
        self._test_access__scenarios(
            scenario=scenario,
            response_codes=response_codes,
            request_method='GET',
        )

    def test_access_wrong_business_status(self):
        self._test_access__wrong_business_status('GET')

    @override_settings(USE_VAT_REGISTERED_INSTEAD_OF_INVOICE_TYPE=True)
    def test_buyer_does_not_exist(self):
        NavisionSettings.objects.update(enable_invoice_details_editing=True)
        self.assertIsNone(self.business.buyer)
        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)

    @override_settings(USE_VAT_REGISTERED_INSTEAD_OF_INVOICE_TYPE=True)
    def test_buyer_exists_and_vat_registered(self):
        NavisionSettings.objects.update(enable_invoice_details_editing=True)
        self.business.buyer = baker.make(SubscriptionBuyer, vat_registered=True)
        self.business.save()
        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=3,
                payment_processor=PaymentProcessorType.STRIPE,
                default_tax_group=None,
                gross_pricing_enabled=True,
                offline_migration_popup=False,
                offline_migration_allowed=False,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                offline_migration_reminder=False,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )

    @override_settings(
        API_COUNTRY=Country.ES,
    )
    def test_gross_pricing_is_enabled_for_stripe_customers_in_spain(self):
        NavisionSettings.objects.update(
            region=baker.make(Region, type=Region.Type.COUNTRY, name='España'),
            enable_invoice_details_editing=True,
        )
        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=3,
                payment_processor=PaymentProcessorType.STRIPE,
                default_tax_group=None,
                gross_pricing_enabled=True,
                offline_migration_popup=False,
                offline_migration_allowed=False,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                offline_migration_reminder=False,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )

    @override_settings(
        API_COUNTRY=Country.ES,
    )
    def test_gross_pricing_is_disabled_for_braintree_customers_in_spain(self):
        NavisionSettings.objects.update(
            region=baker.make(Region, type=Region.Type.COUNTRY, name='España'),
            enable_invoice_details_editing=True,
        )
        self.business_settings.payment_processor = PaymentProcessorType.BRAINTREE
        self.business_settings.save()

        response = self.fetch(self.formatted_url, method='GET')

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=3,
                payment_processor=PaymentProcessorType.BRAINTREE,
                default_tax_group=None,
                gross_pricing_enabled=False,
                offline_migration_popup=False,
                offline_migration_allowed=False,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                offline_migration_reminder=False,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )

    def test_get_200_existing_settings(self):
        self.business_settings.payment_processor = PaymentProcessorType.BRAINTREE
        self.business_settings.save()

        response = self.fetch(self.formatted_url, method='GET')

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=3,
                payment_processor=PaymentProcessorType.BRAINTREE,
                default_tax_group=None,
                gross_pricing_enabled=True,
                offline_migration_popup=False,
                offline_migration_allowed=False,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                offline_migration_reminder=False,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )

    def test_get_default_tax_group(self):
        country = baker.make(
            Region,
            type=Region.Type.COUNTRY,
            name='Country',
        )
        city = baker.make(
            Region,
            type=Region.Type.CITY,
            name='City',
        )
        zipcode = baker.make(
            Region,
            type=Region.Type.ZIP,
            name='02700',
        )
        bake_region_graphs(country, city, zipcode)

        TaxGroup.objects.create(
            name='Some Group',
            accounting_group='Some Accounting Group',
            region=country,
        )
        self.business_settings.payment_processor = PaymentProcessorType.BRAINTREE
        self.business_settings.save()
        TaxGroup.find_by_zipcode(self.business.zip)

        response = self.fetch(self.formatted_url, method='GET')

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=3,
                payment_processor=PaymentProcessorType.BRAINTREE,
                gross_pricing_enabled=True,
                default_tax_group='Some Group',
                offline_migration_popup=False,
                offline_migration_allowed=False,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                offline_migration_reminder=False,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )

    def test_gross_pricing_is_disabled_when_kill_switch_is_killed(self):
        zipcode = baker.make(Region, name='08701', type=Region.Type.ZIP)
        city = baker.make(Region, name='Lakewood', type=Region.Type.CITY)
        state = baker.make(Region, name='New Jersey', type=Region.Type.STATE)
        bake_region_graphs(state, city, zipcode)
        self.business.region = zipcode
        self.business.save(update_fields=['region'])

        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)

        self.assertTrue(response.json['gross_pricing_enabled'])

        KillSwitch.objects.get_or_create(
            name=KillSwitch.System.NAVISION_USE_TAX_RATE_TABLE,
            defaults={
                'is_killed': True,
            },
        )

        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)

        self.assertFalse(response.json['gross_pricing_enabled'])

    def test_get_200_creates_default_settings(self):
        self.business.billing_settings.delete()
        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=0,
                payment_processor=PaymentProcessorType.STRIPE,
                default_tax_group=None,
                gross_pricing_enabled=True,
                offline_migration_popup=False,
                offline_migration_allowed=False,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                offline_migration_reminder=False,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )
        self.assertEqual(BillingBusinessSettings.objects.count(), 1)
        business_settings = BillingBusinessSettings.objects.first()
        self.assertEqual(business_settings.sms_cost_alert_level, SMS_COST_ALERT_DEFAULT_LEVEL)

    def test_gross_pricing_is_enabled_in_new_jersey(self):
        zipcode = baker.make(Region, name='08701', type=Region.Type.ZIP)
        city = baker.make(Region, name='Lakewood', type=Region.Type.CITY)
        state = baker.make(Region, name='New Jersey', type=Region.Type.STATE)
        bake_region_graphs(state, city, zipcode)

        self.business.region = zipcode
        self.business.save(update_fields=['region'])

        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=3,
                payment_processor=PaymentProcessorType.STRIPE,
                gross_pricing_enabled=True,
                default_tax_group=None,
                offline_migration_popup=False,
                offline_migration_allowed=False,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                offline_migration_reminder=False,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )

    def test_gross_pricing_is_enabled_in_arkansas(self):
        zipcode = baker.make(Region, name='72712', type=Region.Type.ZIP)
        city = baker.make(Region, name='Bentonville', type=Region.Type.CITY)
        state = baker.make(Region, name='Arkansas', type=Region.Type.STATE)
        bake_region_graphs(state, city, zipcode)

        self.business.zipcode = '72712'
        self.business.save(update_fields=['zipcode'])

        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=3,
                payment_processor=PaymentProcessorType.STRIPE,
                gross_pricing_enabled=True,
                default_tax_group=None,
                offline_migration_popup=False,
                offline_migration_allowed=False,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                offline_migration_reminder=False,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )

    def test_post_create(self):
        body = dict(
            sms_cost_alert_level=7,
        )
        response = self.fetch(self.formatted_url, method='POST', body=body)
        self.assertEqual(response.code, 201)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=7,
                payment_processor=PaymentProcessorType.STRIPE,
                default_tax_group=None,
                gross_pricing_enabled=True,
                offline_migration_popup=False,
                offline_migration_allowed=False,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                offline_migration_reminder=False,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )
        self.assertEqual(BillingBusinessSettings.objects.count(), 1)
        business_settings = BillingBusinessSettings.objects.first()
        self.assertEqual(business_settings.sms_cost_alert_level, 7)
        self.assertEqual(business_settings.payment_processor, PaymentProcessorType.STRIPE)

    def test_post_update(self):
        body = dict(
            sms_cost_alert_level=7,
        )
        response = self.fetch(self.formatted_url, method='POST', body=body)
        self.assertEqual(response.code, 201)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=7,
                payment_processor=PaymentProcessorType.STRIPE,
                default_tax_group=None,
                gross_pricing_enabled=True,
                offline_migration_popup=False,
                offline_migration_allowed=False,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                offline_migration_reminder=False,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )
        self.business_settings.refresh_from_db()
        self.assertEqual(self.business_settings.sms_cost_alert_level, 7)

    def test_post_cant_update_payment_processor(self):
        body = dict(
            sms_cost_alert_level=7,
            payment_processor=PaymentProcessorType.BRAINTREE.value,
            default_tax_group=None,
            gross_pricing_enabled=False,
        )
        response = self.fetch(self.formatted_url, method='POST', body=body)
        self.assertEqual(response.code, 201)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=7,
                payment_processor=PaymentProcessorType.STRIPE,
                default_tax_group=None,
                gross_pricing_enabled=True,
                offline_migration_popup=False,
                offline_migration_allowed=False,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                offline_migration_reminder=False,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )
        self.business_settings.refresh_from_db()
        self.assertEqual(self.business_settings.sms_cost_alert_level, 7)
        self.assertEqual(self.business_settings.payment_processor, PaymentProcessorType.STRIPE)

    @parameterized.expand(((True, False), (False, True)))
    def test_get__offline_migration(self, is_killed, expected):
        KillSwitch.objects.get_or_create(
            name=KillSwitch.System.BILLING_OFFLINE_MIGRATION,
            defaults={
                'is_killed': is_killed,
            },
        )
        self.business.payment_source = Business.PaymentSource.OFFLINE
        self.business.save()
        baker.make(
            BillingOfflineMigration,
            business=self.business,
            migration_campaign_valid_to=tznow() + timedelta(days=1),
        )

        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=3,
                payment_processor=PaymentProcessorType.STRIPE,
                default_tax_group=None,
                gross_pricing_enabled=True,
                offline_migration_popup=expected,
                offline_migration_allowed=expected,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                offline_migration_reminder=False,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )

    @parameterized.expand(((True, True), (False, False)))
    def test_get__offline_migration_reminder(self, aborted_migration_reminder, expected):
        KillSwitch.objects.get_or_create(
            name=KillSwitch.System.BILLING_OFFLINE_MIGRATION,
            defaults={'is_killed': False},
        )
        self.business.payment_source = Business.PaymentSource.OFFLINE
        # self.business.owner = self.user if user_is_owner else baker.make(User)
        self.business.save()
        baker.make(
            BillingOfflineMigration,
            business=self.business,
            migration_campaign_valid_to=tznow() + timedelta(days=1),
            reminder=aborted_migration_reminder,
        )

        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertDictEqual(
            response.json,
            dict(
                sms_cost_alert_level=3,
                payment_processor=PaymentProcessorType.STRIPE,
                default_tax_group=None,
                gross_pricing_enabled=True,
                offline_migration_popup=True,
                offline_migration_allowed=True,
                offline_migration_reminder=expected,
                purchase_flow=PurchaseFlowAuthorization.AUTHORIZE_WITH_CARD,
                boost_offline_migration_required=False,
                offline_migration_lockout_counter=None,
                offline_migration_discount_offer_display=False,
                offline_migration_discount_offer_percent=None,
                offline_migration_discount_offer_period=None,
            ),
        )

    def test_get__boost_offline_migration(self):
        kill_switch, _ = KillSwitch.objects.get_or_create(
            name=KillSwitch.System.BILLING_OFFLINE_MIGRATION,
            defaults={'is_killed': False},
        )
        self.business.payment_source = Business.PaymentSource.OFFLINE
        self.business.enable_boost_availability()
        self.business.boost_status = Business.BoostStatus.ENABLED
        self.business.boost_payment_source = BoostPaymentSource.OFFLINE
        self.business.save()
        baker.make(
            BillingOfflineMigration,
            business=self.business,
            migration_campaign_valid_to=tznow() + timedelta(days=1),
            reminder=False,
        )

        response = self.fetch(self.formatted_url, method='GET')

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertTrue(response.json['boost_offline_migration_required'])

        kill_switch.is_killed = True
        kill_switch.save()

        response = self.fetch(self.formatted_url, method='GET')

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertFalse(response.json['boost_offline_migration_required'])

    @override_settings(API_COUNTRY=Country.PL)
    def test_get__offline_migration_hard_switch_fields(self):
        self.business.payment_source = Business.PaymentSource.OFFLINE
        self.business.status = Business.Status.OVERDUE
        self.business.overdue_till = tznow() + timedelta(days=11)
        self.business.save()

        saas = baker.make(BillingProduct, product_type=ProductType.SAAS)
        offer = baker.make(BillingProductOffer, default=False)
        baker.make(BillingBusinessOffer, business=self.business, offer_id=offer.id)
        baker.make(
            BillingProductOfferItem,
            offer_id=offer.id,
            product_id=saas.id,
            active=True,
            discount_type=DiscountType.PERCENTAGE,
            discount_frac=Decimal('0.5'),
            discount_duration=1,
        )

        baker.make(
            BillingOfflineMigration,
            business=self.business,
            migration_campaign_valid_to=tznow() + timedelta(days=1),
        )

        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertTrue(response.json['offline_migration_discount_offer_display'])
        self.assertEqual(response.json['offline_migration_discount_offer_percent'], 50)
        self.assertEqual(response.json['offline_migration_discount_offer_period'], 1)
        self.assertEqual(response.json['offline_migration_lockout_counter'], 10)

    @override_settings(API_COUNTRY=Country.PL)
    def test_get__offline_migration_no_offer(self):
        self.business.payment_source = Business.PaymentSource.OFFLINE
        self.business.save()

        baker.make(
            BillingOfflineMigration,
            business=self.business,
            migration_campaign_valid_to=tznow() + timedelta(days=1),
        )

        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertFalse(response.json['offline_migration_discount_offer_display'])
        self.assertEqual(response.json['offline_migration_discount_offer_percent'], None)
        self.assertEqual(response.json['offline_migration_discount_offer_period'], None)
        self.assertEqual(response.json['offline_migration_lockout_counter'], None)


@pytest.mark.django_db
class TestBillingBusinessAccessHandler(BillingEndpointTestCase):
    url = '/business_api/me/businesses/{business_id}/billing/grant_access_if_possible/?'

    @parameterized.expand(BillingEndpointTestCase.billing_access_defaults)
    def test_access(self, scenario, response_codes):
        self._test_access__scenarios(
            scenario=scenario,
            response_codes=response_codes,
            request_method='POST',
            body={},
        )

    def test_access_wrong_business_status(self):
        self._test_access__wrong_business_status('POST', body={})

    def test_post_with_migration(self):
        self.business.status = Business.Status.TRIAL
        self.business.has_new_billing = False
        self.business.payment_source = Business.PaymentSource.UNKNOWN
        self.business.registration_source = baker.make(
            'booking.BookingSources',
            name=consts.FRONTDESK,
        )
        self.business.save()

        response = self.fetch(self.formatted_url, method='POST', body={})
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertEqual(response.json, dict(has_access=True))

        self.business.refresh_from_db()

        self.assertEqual(
            self.business.payment_source,
            Business.PaymentSource.BRAINTREE_BILLING,
        )
        self.assertTrue(self.business.has_new_billing)

        change = BusinessChange.objects.filter(business=self.business).order_by('-id').first()
        self.assertIn('BillingBusinessAccessHandler', change.metadata)

    def test_post_without_migration(self):
        self.business.has_new_billing = True
        self.business.payment_source = Business.PaymentSource.BRAINTREE_BILLING
        self.business.save()
        updated = self.business.updated

        response = self.fetch(self.formatted_url, method='POST', body={})
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertEqual(response.json, dict(has_access=True))

        self.business.refresh_from_db()
        self.assertEqual(self.business.updated, updated)

    def test_post_without_access(self):
        self.business.status = Business.Status.TRIAL
        self.business.has_new_billing = False
        self.business.payment_source = Business.PaymentSource.BRAINTREE
        self.business.registration_source = baker.make(
            'booking.BookingSources',
            name=consts.FRONTDESK,
        )
        self.business.save()
        updated = self.business.updated

        response = self.fetch(self.formatted_url, method='POST', body={})
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertEqual(response.json, dict(has_access=False))

        self.business.refresh_from_db()
        self.assertEqual(self.business.updated, updated)

    def test_post_with_param_force(self):
        self.business.status = Business.Status.CHURNED
        self.business.has_new_billing = False
        self.business.payment_source = Business.PaymentSource.UNKNOWN
        self.business.registration_source = baker.make(
            'booking.BookingSources',
            name=consts.FRONTDESK,
        )
        self.business.save()
        url = self.formatted_url + 'force=true'
        response = self.fetch(url, method='POST', body={})
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertEqual(response.json, dict(has_access=True))

        self.business.refresh_from_db()

        self.assertEqual(
            self.business.payment_source,
            Business.PaymentSource.BRAINTREE_BILLING,
        )
        self.assertTrue(self.business.has_new_billing)

        change = BusinessChange.objects.filter(business=self.business).order_by('-id').first()
        self.assertIn('BillingBusinessAccessHandler', change.metadata)

    def test_put(self):
        response = self.fetch(self.formatted_url, method='PUT', body={})
        self.assertEqual(response.code, 405)

    def test_get(self):
        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, 405)

    def test_patch(self):
        response = self.fetch(self.formatted_url, method='PATCH', body={})
        self.assertEqual(response.code, 405)
