from dateutil.relativedelta import relativedelta
from model_bakery import baker

from service.tests import BaseAsyncHTTPTest
from webapps.billing.enums import PaymentProcessorType
from webapps.billing.models import BillingBusinessSettings
from webapps.business.models import Business, Resource
from webapps.navision.baker_recipes import navision_settings_recipe
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User


def gen_func():
    return relativedelta(months=1)


baker.generators.add('lib.interval.fields.IntervalField', gen_func)


class BillingEndpointTestCase(BaseAsyncHTTPTest):
    url = ''
    billing_access_defaults = [
        ('owner_access', [200, 201, 202]),
        ('manager_access', [200, 201, 202]),
        ('not_authenticated', [403]),
        ('authenticated_as_other_business', [404]),
        ('other_staff_members_access', [404]),
    ]
    billing_access_owner_only = [
        ('owner_access', [200, 201, 202]),
        ('manager_access', [404]),
        ('not_authenticated', [403]),
        ('authenticated_as_other_business', [404]),
        ('other_staff_members_access', [404]),
    ]

    def setUp(self, **kwargs):
        super().setUp(**kwargs)
        navision_settings_recipe.make()
        self.business.has_new_billing = True
        self.business.save()
        self.formatted_url = self.url.format(business_id=self.business.id)

    def _mock_staff_member_login(self, access_level):
        user = baker.make(
            User,
            first_name='Ryan',
            last_name='Gosling',
            email=f'test.testowy+{access_level}@test.com',
        )
        baker.make(
            Resource,
            visible=True,
            type=Resource.STAFF,
            business=self.business,
            staff_access_level=access_level,
            staff_user=user,
            staff_email=user.email,
        )
        self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

    def _mock_other_business_login(self):
        business = baker.make(Business)
        user = baker.make(
            User,
            first_name='James',
            last_name='Dean',
            email='<EMAIL>',
        )
        baker.make(
            Resource,
            visible=True,
            type=Resource.STAFF,
            business=business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
            staff_user=user,
            staff_email=user.email,
        )
        self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

    def _test_access(self, request_method, response_codes, **kwargs):
        resp = self.fetch(self.formatted_url, method=request_method, **kwargs)
        self.assertIn(resp.code, response_codes)

    def _test_owner_access(self, request_method, response_codes, **kwargs):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self._test_access(
            request_method=request_method,
            response_codes=response_codes,
            **kwargs,
        )

    def _test_manager_access(self, request_method, response_codes, **kwargs):
        self._mock_staff_member_login(Resource.STAFF_ACCESS_LEVEL_MANAGER)
        self._test_access(
            request_method=request_method,
            response_codes=response_codes,
            **kwargs,
        )

    def _test_not_authenticated(self, request_method, response_codes, **kwargs):
        self.user.delete_all_user_sessions()
        self._test_access(
            request_method=request_method,
            response_codes=response_codes,
            **kwargs,
        )

    def _test_authenticated_as_other_business(
        self,
        request_method,
        response_codes,
        **kwargs,
    ):
        self._mock_other_business_login()
        self._test_access(
            request_method=request_method,
            response_codes=response_codes,
            **kwargs,
        )

    def _test_other_staff_members_access(
        self,
        request_method,
        response_codes,
        **kwargs,
    ):
        not_allowed_access_levels = set(Resource.STAFF_ACCESS_LEVELS_ALL) - {
            Resource.STAFF_ACCESS_LEVEL_MANAGER,
            Resource.STAFF_ACCESS_LEVEL_OWNER,
        }
        for access_level in not_allowed_access_levels:
            self._mock_staff_member_login(access_level)
            self._test_access(
                request_method=request_method,
                response_codes=response_codes,
                **kwargs,
            )

    def _test_access__scenarios(
        self,
        scenario,
        request_method,
        response_codes,
        **kwargs,
    ):
        """
        Helper method to simplify "access' tests" from child classes. Usage:

            @parameterized.expand(
                BillingEndpointTestCase.billing_access_defaults
            )
            def test_access(self, scenario, response_codes):
                self._test_access__scenarios(
                    scenario=scenario,
                    response_codes=response_codes,
                    request_method='GET',
                )
        """
        self.assertIn(
            scenario,
            [
                'owner_access',
                'manager_access',
                'not_authenticated',
                'authenticated_as_other_business',
                'other_staff_members_access',
            ],
        )
        if scenario == 'owner_access':
            # allowed
            self._test_owner_access(
                request_method=request_method,
                response_codes=response_codes,
                **kwargs,
            )
        elif scenario == 'manager_access':
            # allowed
            self._test_manager_access(
                request_method=request_method,
                response_codes=response_codes,
                **kwargs,
            )
        elif scenario == 'not_authenticated':
            # not allowed
            self._test_not_authenticated(
                request_method=request_method,
                response_codes=response_codes,
                **kwargs,
            )
        elif scenario == 'authenticated_as_other_business':
            self._test_authenticated_as_other_business(
                request_method=request_method,
                response_codes=response_codes,
                **kwargs,
            )
        elif scenario == 'other_staff_members_access':
            self._test_other_staff_members_access(
                request_method=request_method,
                response_codes=response_codes,
                **kwargs,
            )

    def _test_access__wrong_business_status(self, request_method, **kwargs):
        wrong_statuses = [
            Business.Status.SETUP,
            Business.Status.BLOCKED,
        ]
        for status in wrong_statuses:
            self.business.status = status
            self.business.save()

            self._test_access(
                request_method=request_method,
                response_codes=[401, 400],
                **kwargs,
            )
