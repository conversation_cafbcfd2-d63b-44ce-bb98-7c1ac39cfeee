import random
import pytest
from model_bakery import baker

from service.tests import BaseAsyncHTTPTest
from webapps.business.models import (
    Business,
)
from webapps.sequencing_number.models import (
    SequenceRecord,
)
from webapps.sequencing_number.enums import SALES_DOCUMENT


@pytest.mark.django_db
class BusinessDocumentsListHandlerTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/sequencing_number/{business_id}/records/?'

    def test_get_documents(self):
        documents = self._create_documents(10)

        url = self.url.format(
            business_id=self.business.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == 200
        assert len(resp.json['sequence_records']) == 10
        assert resp.json['sequence_records'][0] == {
            u'business': self.business.id,
            u'sequence_number': documents[-1].sequence_number,
            u'id': documents[-1].id,
            u'prefix': None,
            u'related_document_id': documents[-1].related_document_id,
            u'type': SALES_DOCUMENT,
        }

    def test_get_documents_empty(self):
        business = baker.make(Business)
        url = self.url.format(
            business_id=business.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == 200
        assert len(resp.json['sequence_records']) == 0
        assert resp.json['sequence_records'] == []

    def _create_documents(self, number_of_documents):
        documents = []
        for _ in range(0, number_of_documents):
            document = baker.prepare(
                SequenceRecord,
                business=self.business,
                sequence_number=random.randint(1, 1000),
                type=SALES_DOCUMENT,
                related_document_id=random.randint(1, 1000),
            )
            document.save(
                checked_before_save=True,
                ignore_duplicate=True,
            )
            documents.append(document)
        return documents
