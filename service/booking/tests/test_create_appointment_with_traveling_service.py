import datetime
from decimal import Decimal

import pytest
from dateutil.relativedelta import relativedelta
from mock import patch
from model_bakery import baker
from rest_framework import status

from service.tests import BaseAsyncHTTPTest
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    SubbookingServiceVariantMode as SVMode,
)
from webapps.booking.models import Appointment
from webapps.booking.tests.test_appointment_base import BaseTestAppointment
from webapps.business.enums import ComboType, PriceType
from webapps.business.models import (
    Business,
    ComboMembership,
    Service,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.elasticsearch.elastic import ELASTIC
from webapps.pos.enums import PaymentTypeEnum, compatibilities
from webapps.pos.models import (
    POS,
    PaymentMethod,
    PaymentType,
    TaxRate,
    Transaction,
)
from webapps.pos.provider.fake import _CARDS
from webapps.user.enums import AuthOriginEnum


@pytest.fixture(scope='module', autouse=True)
def disable_annoying():
    with (
        patch('webapps.notification.scenarios.dispatch_scenario_task'),
        patch('webapps.notification.tasks.execute_task'),
        patch('webapps.search_engine_tuning.tasks.update_user_tuning_task'),
    ):
        yield


class CreateAppointmentWithTravelingService(BaseAsyncHTTPTest, BaseTestAppointment):

    def setUp(self):
        super().setUp()
        self.customer_url = f'/customer_api/me/appointments/business/{self.business.id}/'
        self.business_url = f'/business_api/me/businesses/{self.business.id}/appointments/'
        self.checkout_url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'
        self.pos = baker.make(
            POS,
            business=self.business,
            active=True,
            pay_by_app_status=POS.PAY_BY_APP_ENABLED,
        )
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.SPLIT)
        assert self.business.pos_pay_by_app_enabled is True

        baker.make(
            TaxRate,
            pos=self.pos,
            rate=17,
            default_for_service=True,
        )
        baker.make(
            'business.TravelingToClients',
            business=self.business,
            traveling_only=True,
            price=Decimal('12.50'),
            price_type=PriceType.STARTS_AT,
        )
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()
        traveling_service = baker.make(Service, business=self.business, is_traveling_service=True)
        self.traveling_service_variant = baker.make(
            ServiceVariant,
            service=traveling_service,
            duration=relativedelta(minutes=15),
            price=25,
            type=PriceType.FIXED,
        )
        baker.make(
            ServiceVariantPayment,
            service_variant=self.traveling_service_variant,
            payment_amount=8,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
        )
        local_service = baker.make(Service, business=self.business, is_traveling_service=False)
        self.local_service_variant = baker.make(
            ServiceVariant,
            service=local_service,
            duration=relativedelta(minutes=15),
        )
        traveling_service.add_staffers([self.staffer])
        local_service.add_staffers([self.staffer])

        self.bci = baker.make(
            'business.BusinessCustomerInfo',
            business=self.business,
            first_name=self.staffer.name,
            last_name='Customer',
            cell_phone=self.staffer.staff_cell_phone,
            user=self.staffer.staff_user,
            address_line_1='address 1',
            address_line_2='',
            city='New York',
            zipcode='12345',
        )
        self.bci.reindex()
        ELASTIC.indices['business_customer'].refresh()

        self.card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )

        # make staffer session to allow both customer_api and business_api
        # access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        self.session = self.business.owner.create_session(
            origin=AuthOriginEnum.BOOKSY, fingerprint=''
        )

    def get_booking_body(self, service_variant_id, delta=None):
        booked_from = self._dt_from_hour(datetime.time(12, 0))
        if delta:
            booked_from = booked_from + datetime.timedelta(minutes=delta)
        booked_till = booked_from + datetime.timedelta(minutes=15)
        return {
            'booked_from': booked_from.isoformat(),
            'booked_till': booked_till.isoformat(),
            'appliance_id': None,
            'staffer_id': self.staffer.id,
            'service_variant': {
                'id': service_variant_id,
                'mode': SVMode.VARIANT,
            },
            'type': Appointment.TYPE.BUSINESS,
        }

    def get_appointment_body(self, subbookings):
        return {
            'customer': {'mode': ACMode.CUSTOMER_CARD, 'id': self.bci.id},
            'subbookings': subbookings,
            'dry_run': False,
            'recurring': False,
            compatibilities.COMPATIBILITIES: {
                compatibilities.PREPAYMENT: True,
            },
        }

    def get_appointment_body_from_appointment(self, appointment):
        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': self.bci.id,
            },
            'subbookings': appointment['subbookings'],
            'dry_run': False,
            'overbooking': True,  # ignore working hours
            '_notify_about_reschedule': False,
            compatibilities.COMPATIBILITIES: {
                compatibilities.PREPAYMENT: True,
            },
        }
        if '_version' in appointment:
            body['_version'] = appointment['_version']
        if 'traveling' in appointment:
            body['traveling'] = appointment['traveling']
        if 'bci_agreements' in appointment:
            body['bci_agreements'] = {
                agreement['name']: agreement['value'] for agreement in appointment['bci_agreements']
            }
        for booking in body['subbookings']:
            booking['appliance_id'] = None
        return body


@pytest.mark.django_db
class TestCreateAppointmentWithTravelingService(CreateAppointmentWithTravelingService):
    def test_create_error_multi_booking(self):
        """Customer cannot mix traveling and local services"""
        body = self.get_appointment_body(
            [
                self.get_booking_body(
                    self.traveling_service_variant.id,
                ),
                self.get_booking_body(
                    self.local_service_variant.id,
                    delta=15,
                ),
            ]
        )

        resp = self.fetch(self.customer_url, method='POST', body=body)
        assert resp.code == 400
        assert resp.json['errors'][0]['code'] == 'traveling_mixed', resp.json

        body = self.get_appointment_body(
            [
                self.get_booking_body(
                    self.local_service_variant.id,
                ),
                self.get_booking_body(
                    self.traveling_service_variant.id,
                    delta=15,
                ),
            ]
        )

        resp = self.fetch(self.customer_url, method='POST', body=body)
        assert resp.code == 400
        assert resp.json['errors'][0]['code'] == 'traveling_mixed', resp.json

    def test_create_business_traveling_booking(self):
        # dry run
        body = self.get_appointment_body([self.get_booking_body(self.local_service_variant.id)])

        body['dry_run'] = True
        body['overbooking'] = False
        body['traveling'] = {}

        resp = self.fetch(self.business_url, method='POST', body=body)
        assert resp.code == 201, resp
        appointment = resp.json['appointment']
        assert appointment['status'] == Appointment.STATUS.ACCEPTED
        assert appointment['traveling']['formatted_price'] == '$12.50'
        from pprint import pprint

        pprint(appointment)

        # save
        body = self.get_appointment_body_from_appointment(appointment)
        body['dry_run'] = False
        body['overbooking'] = False
        body['traveling']['apartment_number'] = ''
        # body['traveling'] = dict(
        #     address_line_1='address 1',
        #     address_line_2='address 2',
        #     city='City',
        #     zipcode='12345',
        # )
        # body['recurring'] = True
        # body['payment_method'] = self.card.id
        resp = self.fetch(self.business_url, method='POST', body=body)
        assert resp.code == 201, resp
        appointment = resp.json['appointment']

    def test_create_customer_traveling_booking(self):
        # dry run
        body = self.get_appointment_body([self.get_booking_body(self.traveling_service_variant.id)])

        body['dry_run'] = True
        body['traveling'] = dict(address_line_1='')

        resp = self.fetch(self.customer_url, method='POST', body=body)
        assert resp.code == 201, resp
        appointment = resp.json['appointment']
        assert appointment['status'] == Appointment.STATUS.UNCONFIRMED
        assert appointment['traveling']['formatted_price'] == '$12.50+'

        # save
        body = self.get_appointment_body_from_appointment(appointment)
        body['dry_run'] = False
        body['traveling'] = dict(
            address_line_1='address 1',
            address_line_2='address 2',
            apartment_number='',
            city='City',
            zipcode='12345',
        )
        body['recurring'] = True
        body['payment_method'] = self.card.id
        resp = self.fetch(self.customer_url, method='POST', body=body)
        assert resp.code == 201, resp
        appointment = resp.json['appointment']
        from pprint import pprint

        pprint(appointment)

        assert appointment['status'] == Appointment.STATUS.UNCONFIRMED
        assert appointment['total'] == '$37.50+'  # 25 + 12.50
        assert appointment['payment_info']['transaction_info']['total'] == '$37.50'
        assert appointment['actions']['accept'] is False

        # check price editing
        body = self.get_appointment_body_from_appointment(appointment)
        body['traveling']['price'] = '14.80'
        resp = self.fetch(
            f"{self.business_url}single/{appointment['appointment_id']}/", method='PUT', body=body
        )
        assert resp.code == 200
        appointment = resp.json['appointment']
        print(appointment)
        assert appointment['traveling']['price'] == '14.80'
        assert appointment['traveling']['formatted_price'] == '$14.80'
        assert appointment['payment_info']['transaction_info']['total'] == '$39.80'

    def test_create_business_traveling_booking_with_invalid_info(self):
        body = self.get_appointment_body([self.get_booking_body(self.traveling_service_variant.id)])
        body['traveling'] = None

        resp = self.fetch(self.business_url, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST, resp

    def test_create_business_traveling_combo_booking_with_invalid_info(self):
        # dry run
        traveling_combo_service = baker.make(
            Service,
            business=self.business,
            combo_type=ComboType.SEQUENCE,
        )
        traveling_service_variant_2 = baker.make(
            ServiceVariant,
            service=self.traveling_service_variant.service,
            duration=relativedelta(minutes=30),
            price=45,
            type=PriceType.FIXED,
        )
        traveling_combo_service_variant = baker.make(
            ServiceVariant,
            service=traveling_combo_service,
            duration=relativedelta(minutes=15),
            price=25,
            type=PriceType.FIXED,
        )
        ComboMembership.objects.bulk_create(
            [
                ComboMembership(
                    combo=traveling_combo_service_variant,
                    child=self.traveling_service_variant,
                    order=1,
                ),
                ComboMembership(
                    combo=traveling_combo_service_variant,
                    child=traveling_service_variant_2,
                    order=2,
                ),
            ]
        )
        self.staffer.add_services(
            [traveling_service_variant_2.service.id, traveling_combo_service_variant.service.id]
        )
        body = self.get_appointment_body(
            [self.get_booking_body(traveling_combo_service_variant.id)]
        )

        body['dry_run'] = True
        body['overbooking'] = False
        body['traveling'] = None

        resp = self.fetch(self.business_url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED, resp
        appointment = resp.json['appointment']

        # save
        body = self.get_appointment_body_from_appointment(appointment)
        body['dry_run'] = False
        body['overbooking'] = False
        body['traveling'] = None
        resp = self.fetch(self.business_url, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST, resp

    def test_change_customer_on_traveling_booking(self):
        # remove prepayment for service
        self.traveling_service_variant.payment.delete()
        # create with walk-in
        body = self.get_appointment_body([self.get_booking_body(self.traveling_service_variant.id)])

        body['customer'] = dict(mode=ACMode.WALK_IN)
        body['dry_run'] = False
        body['recurring'] = False
        body['overbooking'] = True  # ignore working hours
        body['_notify_about_reschedule'] = False
        body['traveling'] = dict(
            price='14.80',
            address_line_1='address 1',
            address_line_2='address 2',
            apartment_number='',
            city='City',
            zipcode='12345',
        )

        resp = self.fetch(self.business_url, method='POST', body=body)
        assert resp.code == 201, resp
        appointment = resp.json['appointment']
        assert appointment['customer']['mode'] == ACMode.WALK_IN

        # # check customer editing
        body = self.get_appointment_body_from_appointment(appointment)
        body['dry_run'] = True
        body['customer'] = {
            'mode': ACMode.CUSTOMER_CARD,
            'id': self.bci.id,
            'detailed_walkin': None,
            'email': None,
            'invite': None,
            'name': None,
            'phone': None,
        }
        body['traveling']['price'] = '42.50'
        resp = self.fetch(
            f"{self.business_url}single/{appointment['appointment_id']}/", method='PUT', body=body
        )
        assert resp.code == 200
        appointment = resp.json['appointment']
        assert appointment['customer']['id'] == self.bci.id
        assert appointment['traveling']['price'] == '42.50'

        # save
        body = self.get_appointment_body_from_appointment(appointment)
        resp = self.fetch(
            f"{self.business_url}single/{appointment['appointment_id']}/", method='PUT', body=body
        )

        # also check checkout
        booking_id = appointment['subbookings'][0]['id']
        body = dict(
            dry_run=True,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            bookings=[dict(booking_id=booking_id)],
            compatibilities=dict(
                prepayment=True,
                new_checkout=True,
                split=True,
            ),
            booking=booking_id,
        )
        resp = self.fetch(self.checkout_url, method='POST', body=body)
        assert resp.code == 201
        transaction = resp.json['transaction']
        assert transaction['rows'][1]['type'] == 'T'
        assert transaction['rows'][1]['item_price'] == 42.5

    def test_create_local_booking(self):
        body = self.get_appointment_body([self.get_booking_body(self.local_service_variant.id)])
        resp = self.fetch(self.customer_url, method='POST', body=body)
        assert resp.code == 201
        appointment = resp.json['appointment']
        assert appointment['status'] == Appointment.STATUS.ACCEPTED
        assert appointment['traveling'] is None

    def test_create_correct_multi_booking(self):
        """Customer cannot mix traveling and local services"""
        body = self.get_appointment_body(
            [
                self.get_booking_body(
                    self.traveling_service_variant.id,
                ),
                self.get_booking_body(
                    self.traveling_service_variant.id,
                    delta=15,
                ),
            ]
        )
        body['payment_method'] = self.card.id
        body['traveling'] = dict(
            address_line_1='a1',
            city='city',
            zipcode='12345',
        )
        body['dry_run'] = True

        resp = self.fetch(self.customer_url, method='POST', body=body)
        assert resp.code == 201
        appointment = resp.json['appointment']
        assert appointment['status'] == Appointment.STATUS.UNCONFIRMED
