from datetime import timedelta

import pytest
from dateutil.relativedelta import relativedelta
from model_bakery import baker

from lib.test_utils import (
    create_subbooking,
    increase_appointment_next_id,
)
from service.booking.tests import (
    get_before_and_after,
    get_booking_date,
)
from service.tests import BaseAsyncHTTPTest
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    RepeatEndType,
    RepeatType,
    SubbookingServiceVariantMode as SVMode,
)
from webapps.booking.models import (
    Appointment,
    BookingResource,
    RepeatingBooking,
    SubBooking,
)
from webapps.business.models import (
    PriceType,
    Resource,
    Service,
    ServiceAddOn,
    ServiceAddOnUse,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo


@pytest.mark.django_db
class EditOneInRepeatingBookingTestCase(BaseAsyncHTTPTest):

    def setUp(self):
        increase_appointment_next_id()
        return super().setUp()

    @staticmethod
    def generate_url(booking, business_id):
        url = f'/business_api/me/businesses/{business_id}' f'/appointments/single/{booking.id}/'
        return url

    def run_test(self, status, from_, edited_index, check_disconnected):
        from_before, from_after = from_
        service = baker.make(Service, business=self.business)
        variants = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            _quantity=2,
        )

        staffers = baker.make(Resource, type=Resource.STAFF, business=self.business, _quantity=2)
        service.add_staffers(staffers)

        appliances = baker.make(
            Resource, type=Resource.APPLIANCE, business=self.business, _quantity=2
        )
        service.add_appliances(appliances)

        repeat_number = 10
        repeating = baker.make(
            RepeatingBooking,
            repeat=RepeatType.EVERY_DAY,
            end_type=RepeatEndType.AFTER_N_BOOKINGS,
            repeat_number=repeat_number,
        )
        bcis = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            _quantity=2,
        )

        bookings = []
        for i in range(repeat_number):
            booking, *_ = create_subbooking(
                business=self.business,
                booking_kws=dict(
                    booked_for=bcis[0],
                    updated_by=self.user,
                    source=self.customer_booking_src,
                    booked_from=from_before + timedelta(days=i),
                    booked_till=from_before + timedelta(days=i, hours=1),
                    type=Appointment.TYPE.BUSINESS,
                    status=status,
                    autoassign=True,
                    service_variant=variants[0],
                    repeating=repeating,
                ),
            )
            bookings.append(booking)

            BookingResource(subbooking=booking, resource=staffers[0]).save()
            BookingResource(subbooking=booking, resource=appliances[0]).save()

            # just in case
            assert booking.staffer == staffers[0]
            assert booking.appliance == appliances[0]
            assert booking.appointment.booked_for == bcis[0]
            assert booking.service_variant == variants[0]

        booked_from = from_after
        booked_till = from_after + timedelta(hours=1)

        body = {
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': bcis[1].id,
            },
            'dry_run': False,
            'subbookings': [
                {
                    'booked_from': booked_from.isoformat(),
                    'booked_till': booked_till.isoformat(),
                    'appliance_id': appliances[1].id,
                    'staffer_id': staffers[1].id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': variants[1].id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            '_version': bookings[
                edited_index
            ].appointment._version,  # pylint: disable=protected-access
            'overbooking': True,  # ignore working hours
            '_notify_about_reschedule': False,
        }

        url = self.generate_url(bookings[edited_index], self.business.id)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body

        booking = SubBooking.objects.get(id=bookings[edited_index].id)
        assert booking.appointment.status == status
        assert booking.staffer == staffers[1]
        assert booking.appliance == appliances[1]
        assert booking.service_variant == variants[1]
        assert booking.appointment.repeating is None

        edited_id = booking.id
        all_bookings = list(
            SubBooking.objects.filter(
                id__in=(b.id for b in bookings),
            ).order_by('id')
        )

        new_repeating = RepeatingBooking.objects.order_by('id').last()
        assert new_repeating.id != repeating.id

        disconnected = remove_negative_index(check_disconnected, repeat_number)
        for i, booking in enumerate(all_bookings):
            assert booking.appointment.repeating_id == (
                None if i in disconnected else new_repeating.id
            )
            if booking.id == edited_id:
                continue

            assert booking.appointment.status == status
            assert booking.staffer == staffers[0]
            assert booking.appliance == appliances[0]
            assert booking.service_variant == variants[0]

        for i in sorted(disconnected, reverse=True):
            assert all_bookings.pop(i).appointment.repeating_id is None
        for booking in all_bookings:
            assert booking.appointment.repeating_id is not None

    def test_edit_booking_in_future_first(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            get_before_and_after(),
            edited_index=0,
            check_disconnected=[0],
        )

    def test_edit_booking_in_future_second(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            get_before_and_after(),
            edited_index=1,
            check_disconnected=[0, 1],
        )

    def test_edit_booking_in_future_middle(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            get_before_and_after(),
            edited_index=4,
            check_disconnected=[0, 4],
        )

    def test_edit_booking_in_future_one_before_last(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            get_before_and_after(),
            edited_index=-2,
            check_disconnected=[0, -2],
        )

    def test_edit_booking_in_future_last(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            get_before_and_after(),
            edited_index=-1,
            check_disconnected=[0, -1],
        )

    def test_edit_booking_in_past_first(self):
        from_before, from_after = get_before_and_after()
        self.run_test(
            Appointment.STATUS.FINISHED,
            (
                from_before - timedelta(days=365),
                from_after - timedelta(days=365),
            ),
            edited_index=0,
            check_disconnected=[0],
        )

    def test_edit_booking_in_past_second(self):
        from_before, from_after = get_before_and_after()
        self.run_test(
            Appointment.STATUS.FINISHED,
            (
                from_before - timedelta(days=365),
                from_after - timedelta(days=365),
            ),
            edited_index=1,
            check_disconnected=[0, 1],
        )

    def test_edit_booking_in_past_middle(self):
        from_before, from_after = get_before_and_after()
        self.run_test(
            Appointment.STATUS.FINISHED,
            (
                from_before - timedelta(days=365),
                from_after - timedelta(days=365),
            ),
            edited_index=4,
            check_disconnected=[0, 4],
        )

    def test_edit_booking_in_past_one_before_last(self):
        from_before, from_after = get_before_and_after()
        self.run_test(
            Appointment.STATUS.FINISHED,
            (
                from_before - timedelta(days=365),
                from_after - timedelta(days=365),
            ),
            edited_index=-2,
            check_disconnected=[0, -2],
        )

    def test_edit_booking_in_past_last(self):
        from_before, from_after = get_before_and_after()
        self.run_test(
            Appointment.STATUS.FINISHED,
            (
                from_before - timedelta(days=365),
                from_after - timedelta(days=365),
            ),
            edited_index=-1,
            check_disconnected=[0, -1],
        )


class EditOneInRepeatingBookingUrlUidTestCase(EditOneInRepeatingBookingTestCase):

    @staticmethod
    def generate_url(booking, business_id):
        url = (
            f'/business_api/me/businesses/{business_id}' f'/appointments/{booking.appointment_id}/'
        )
        return url


@pytest.mark.django_db
class EditFromOneToEndInRepeatingBookingTestCase(BaseAsyncHTTPTest):

    def setUp(self):
        increase_appointment_next_id()
        return super().setUp()

    # pylint: disable=too-many-statements
    def run_test(self, status, from_before, edited_index, check_disconnected):
        service = baker.make(Service, business=self.business)
        variants = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            _quantity=2,
        )

        staffers = baker.make(Resource, type=Resource.STAFF, business=self.business, _quantity=2)
        service.add_staffers(staffers)

        appliances = baker.make(
            Resource, type=Resource.APPLIANCE, business=self.business, _quantity=2
        )
        service.add_appliances(appliances)
        bcis = baker.make(
            BusinessCustomerInfo,
            _quantity=2,
            business=self.business,
        )
        repeat_number = 10
        repeating = baker.make(
            RepeatingBooking,
            repeat=RepeatType.EVERY_DAY,
            end_type=RepeatEndType.AFTER_N_BOOKINGS,
            repeat_number=repeat_number,
        )

        addon_1 = baker.make(
            ServiceAddOn,
            name='addon 1',
            business=self.business,
            price=12.50,
            price_type=PriceType.FIXED,
            services=[service],
            duration=relativedelta(minutes=30),
            max_allowed_quantity=10,
        )
        addon_2 = baker.make(
            ServiceAddOn,
            name='addon 2',
            business=self.business,
            price=14.50,
            price_type=PriceType.FIXED,
            services=[service],
            duration=relativedelta(minutes=15),
            max_allowed_quantity=10,
        )

        bookings = []
        for i in range(repeat_number):
            booking, *_ = create_subbooking(
                business=self.business,
                booking_kws=dict(
                    booked_for=bcis[0],
                    updated_by=self.user,
                    source=self.customer_booking_src,
                    booked_from=from_before + timedelta(days=i),
                    booked_till=from_before + timedelta(days=i, hours=1),
                    type=Appointment.TYPE.BUSINESS,
                    status=status,
                    autoassign=True,
                    service_variant=variants[0],
                    repeating=repeating,
                ),
            )
            bookings.append(booking)

            addon_use_1 = addon_1.clone(quantity=1)
            addon_use_1.subbooking = booking
            addon_use_1.save()

            addon_use_2 = addon_2.clone(quantity=1)
            addon_use_2.subbooking = booking
            addon_use_2.save()

            BookingResource(subbooking=booking, resource=staffers[0]).save()
            BookingResource(subbooking=booking, resource=appliances[0]).save()

            # just in case
            assert booking.staffer == staffers[0]
            assert booking.appliance == appliances[0]
            assert booking.appointment.booked_for == bcis[0]
            assert booking.service_variant == variants[0]

        body = {
            '_update_future_bookings': True,
            'customer': {
                'mode': ACMode.CUSTOMER_CARD,
                'id': bcis[1].id,
            },
            'subbookings': [
                {
                    'booked_from': bookings[edited_index].booked_from.isoformat(),
                    'booked_till': bookings[edited_index].booked_till.isoformat(),
                    'appliance_id': appliances[1].id,
                    'staffer_id': staffers[1].id,
                    'service_variant': {
                        'mode': SVMode.VARIANT,
                        'id': variants[1].id,
                    },
                    'type': Appointment.TYPE.BUSINESS,
                    'addons': [
                        {'id': addon_1.id, 'quantity': 0},
                        {'id': addon_2.id, 'quantity': 2},
                    ],
                }
            ],
            'repeating': {
                '_version': repeating._version,  # pylint: disable=protected-access
                'id': repeating.id,
            },
            'dry_run': False,
            '_version': bookings[
                edited_index
            ].appointment._version,  # pylint: disable=protected-access
            'overbooking': True,  # ignore working hours
            '_notify_about_reschedule': False,
        }
        # pylint: disable=consider-using-f-string
        url = '/business_api/me/businesses/{}/appointments/single/{}/'.format(
            self.business.id, bookings[edited_index].id
        )

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body

        assert ServiceAddOnUse.all_objects.count() == 20
        assert resp.json['appointment']['subbookings'][0]['addons'][0]['quantity'] == 2
        assert len(resp.json['appointment']['subbookings'][0]['addons']) == 1

        all_bookings = list(
            SubBooking.objects.filter(id__in=(b.id for b in bookings)).order_by('id')
        )
        order = {b.id: i for i, b in enumerate(bookings)}

        disconnected = remove_negative_index(check_disconnected, repeat_number)
        for booking in all_bookings[:edited_index]:
            i = order[booking.id]
            assert booking.appointment.status == status
            assert booking.staffer == staffers[0]
            assert booking.appliance == appliances[0]
            assert booking.service_variant == variants[0]
            if i not in disconnected:
                assert booking.appointment.repeating == repeating

        new_repeating = RepeatingBooking.objects.order_by('id').last()
        for booking in all_bookings[edited_index:]:
            i = order[booking.id]
            assert booking.appointment.status == status
            assert booking.staffer == staffers[1]
            assert booking.appliance == appliances[1]
            assert booking.service_variant == variants[1]
            if i not in disconnected:
                assert booking.appointment.repeating == new_repeating

        for i in sorted(disconnected, reverse=True):
            assert all_bookings.pop(i).appointment.repeating_id is None
        for booking in all_bookings:
            assert booking.appointment.repeating_id is not None

    def test_edit_booking_in_future_first(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            get_booking_date(),
            edited_index=0,
            check_disconnected=[],
        )
        assert ServiceAddOnUse.objects.count() == 10

    def test_edit_booking_in_past_first(self):
        self.run_test(
            Appointment.STATUS.FINISHED,
            get_booking_date() - timedelta(days=365),
            edited_index=0,
            check_disconnected=[],
        )
        assert ServiceAddOnUse.objects.count() == 10

    def test_edit_booking_in_future_second(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            get_booking_date(),
            edited_index=1,
            check_disconnected=[0],
        )
        assert ServiceAddOnUse.objects.count() == 11

    def test_edit_booking_in_past_second(self):
        self.run_test(
            Appointment.STATUS.FINISHED,
            get_booking_date() - timedelta(days=365),
            edited_index=1,
            check_disconnected=[0],
        )
        assert ServiceAddOnUse.objects.count() == 11

    def test_edit_booking_in_future_middle(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            get_booking_date(),
            edited_index=3,
            check_disconnected=[],
        )
        assert ServiceAddOnUse.objects.count() == 13

    def test_edit_booking_in_past_middle(self):
        self.run_test(
            Appointment.STATUS.FINISHED,
            get_booking_date() - timedelta(days=365),
            edited_index=3,
            check_disconnected=[],
        )
        assert ServiceAddOnUse.objects.count() == 13

    def test_edit_booking_in_future_one_before_last(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            get_booking_date(),
            edited_index=-2,
            check_disconnected=[],
        )
        assert ServiceAddOnUse.objects.count() == 18

    def test_edit_booking_in_past_one_before_last(self):
        self.run_test(
            Appointment.STATUS.FINISHED,
            get_booking_date() - timedelta(days=365),
            edited_index=-2,
            check_disconnected=[],
        )
        assert ServiceAddOnUse.objects.count() == 18

    def test_edit_booking_in_future_last(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            get_booking_date(),
            edited_index=-1,
            check_disconnected=[],
        )
        assert ServiceAddOnUse.objects.count() == 19

    def test_edit_booking_in_past_last(self):
        self.run_test(
            Appointment.STATUS.FINISHED,
            get_booking_date() - timedelta(days=365),
            edited_index=-1,
            check_disconnected=[],
        )


def remove_negative_index(indexes, list_count):
    return {(i + list_count) % list_count for i in indexes}
