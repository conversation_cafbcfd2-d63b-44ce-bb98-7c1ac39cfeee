import json

import mock
import pytest
from django.db import IntegrityError
from model_bakery import baker
from rest_framework import status

from lib.test_utils import create_subbooking
from service.tests import BaseAsyncHTTPTest
from webapps.booking.tests.test_appointment_base import BaseTestAppointment
from webapps.business.baker_recipes import business_recipe
from webapps.business.models import Business
from webapps.family_and_friends.tests.utils import TestFamilyAndFriendsMixin
from webapps.user.models import User
from webapps.business.models.bci import BusinessCustomerInfo, BCIPatientFile
from webapps.business_related.enums import (
    ClaimLogReasons,
    ClaimLogStatuses,
)
from webapps.business_related.models import ClaimLog
from webapps.consents.models import Consent, ConsentForm
from webapps.kill_switch.models import KillSwitch


@pytest.mark.django_db
class BaseCustomerAppointmentClaimHandlerTestCase(BaseAsyncHTTPTest):
    url = "/customer_api/me/appointments/{}/claim/?"


class CustomerAppointmentClaimHandlerTestCase(
    BaseCustomerAppointmentClaimHandlerTestCase, BaseTestAppointment
):
    def test_post_200_with_bci_without_user(self):
        business = baker.make(Business)
        common_cell_phone = '***********'
        logged_in_user = User.objects.filter(
            id=self.logged_in_user_id,
        ).first()
        logged_in_user.cell_phone = common_cell_phone
        logged_in_user.save()
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            cell_phone=common_cell_phone,
            user=None,
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK

        session_user = self.session.get_user()

        appointment.booked_for.refresh_from_db()
        assert appointment.booked_for.user == session_user
        bci.refresh_from_db()
        assert bci.user == session_user

        assert ClaimLog.objects.all().count() == 1
        claim_log = ClaimLog.objects.filter(new_user=session_user).first()
        assert claim_log
        assert claim_log.status == ClaimLogStatuses.CLAIMED

    def test_post_200_with_bci_without_user_killswitch_killed(self):
        baker.make(
            KillSwitch,
            name=KillSwitch.System.CLAIM_APPOINTMENTS_MERGE_BCI,
            is_killed=True,
        )

        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK

        session_user = self.session.get_user()

        appointment.booked_for.refresh_from_db()
        assert appointment.booked_for.user is None
        bci.refresh_from_db()
        assert bci.user is None

        assert ClaimLog.objects.all().count() == 1
        claim_log = ClaimLog.objects.filter(new_user=session_user).first()
        assert claim_log
        assert claim_log.status == ClaimLogStatuses.PROPOSED

    def test_post_200_with_bci_without_user_different_phone_number(self):
        session_user = self.session.get_user()
        session_user.cell_phone = '***********'
        session_user.save()

        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
            cell_phone='+1***********',
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK

        appointment.booked_for.refresh_from_db()
        assert appointment.booked_for.user is None  # nothing changed
        bci.refresh_from_db()
        assert bci.user is None  # nothing changed

        assert ClaimLog.objects.all().count() == 1
        claim_log = ClaimLog.objects.filter(new_user=session_user).first()
        assert claim_log
        assert claim_log.status == ClaimLogStatuses.PROPOSED

    def test_post_200_with_bci_without_user_same_phone_number(self):
        session_user = self.session.get_user()
        session_user.cell_phone = '***********'
        session_user.save()

        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
            cell_phone='***********',
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK

        appointment.booked_for.refresh_from_db()
        assert appointment.booked_for.user == session_user
        bci.refresh_from_db()
        assert bci.user == session_user

        assert ClaimLog.objects.all().count() == 1
        claim_log = ClaimLog.objects.filter(new_user=session_user).first()
        assert claim_log
        assert claim_log.status == ClaimLogStatuses.CLAIMED

    def test_post_200_with_bci_without_user_same_phone_number_killswitch(self):
        baker.make(
            KillSwitch,
            name=KillSwitch.System.CLAIM_APPOINTMENTS_MERGE_BCI,
            is_killed=True,
        )
        session_user = self.session.get_user()
        session_user.cell_phone = '***********'
        session_user.save()

        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
            cell_phone='***********',
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK

        appointment.booked_for.refresh_from_db()
        assert appointment.booked_for.user is None
        bci.refresh_from_db()
        assert bci.user is None

        assert ClaimLog.objects.all().count() == 1
        claim_log = ClaimLog.objects.filter(new_user=session_user).first()
        assert claim_log
        assert claim_log.status == ClaimLogStatuses.PROPOSED

    def test_post_200_with_same_user(self):
        session_user = self.session.get_user()

        business = baker.make(Business)
        appointment_bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=session_user,
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': appointment_bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == appointment_bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK  # same booked_for.user so no difference
        assert ClaimLog.objects.all().count() == 0

    def test_post_200_empty_card_and_another_card_with_session_user(self):
        session_user = self.session.get_user()

        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=session_user,
            cell_phone='***********',
        )
        appointment_bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
            cell_phone='***********',
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': appointment_bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == appointment_bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK  # same booked_for.user so no difference
        assert ClaimLog.objects.filter(
            target_bci=bci,
            source_bci=appointment_bci,
            merge_reason=ClaimLogReasons.SECRET,
            status=ClaimLogStatuses.MERGED,
        )
        assert ClaimLog.objects.all().count() == 1
        appointment.refresh_from_db()
        assert appointment.booked_for == bci

    def test_post_200_merge_bci_with_patient_file(self):
        session_user = self.session.get_user()

        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=session_user,
        )
        appointment_bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
        )
        baker.make(BCIPatientFile, bci=appointment_bci)
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': appointment_bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == appointment_bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK  # same booked_for.user so no difference
        assert (
            ClaimLog.objects.filter(
                target_bci=bci,
                source_bci=appointment_bci,
                merge_reason=ClaimLogReasons.SECRET,
                status=ClaimLogStatuses.REFUSED,
            ).count()
            == 1
        )
        appointment.refresh_from_db()
        assert appointment.booked_for != bci  # not merged because of BCIPatientFile

    def test_post_200_empty_card_another_card_with_user_plus_same_phones0(self):
        session_user = self.session.get_user()

        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=session_user,
            cell_phone='***********',
        )
        appointment_bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
            cell_phone='***********',
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': appointment_bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == appointment_bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK  # same booked_for.user so no difference
        assert ClaimLog.objects.filter(
            target_bci=bci,
            source_bci=appointment_bci,
            merge_reason=ClaimLogReasons.SECRET,
            status=ClaimLogStatuses.MERGED,
        )
        assert ClaimLog.objects.all().count() == 1
        appointment.refresh_from_db()
        assert appointment.booked_for == bci

    def test_post_200_empty_card_another_card_with_user_plus_diff_phones0(self):
        session_user = self.session.get_user()

        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=session_user,
            cell_phone='***********',
        )
        appointment_bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
            cell_phone='***********',
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': appointment_bci,
            },
        )
        appointment = booking.appointment
        # assert appointment.booked_for is None

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK  # same booked_for.user so no difference
        assert ClaimLog.objects.filter(
            target_bci=bci,
            source_bci=appointment_bci,
            merge_reason=ClaimLogReasons.SECRET,
            status=ClaimLogStatuses.PROPOSED,
        )
        assert ClaimLog.objects.all().count() == 1
        appointment.refresh_from_db()
        assert appointment.booked_for == appointment_bci

    def test_post_200_empty_card_another_card_with_user_plus_diff_phones1(self):
        session_user = self.session.get_user()
        session_user.cell_phone = '234234234'
        session_user.save()

        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=session_user,
        )
        appointment_bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
            cell_phone='***********',
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': appointment_bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for.user is None

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK  # same booked_for.user so no difference
        assert ClaimLog.objects.filter(
            target_bci=bci,
            source_bci=appointment_bci,
            merge_reason=ClaimLogReasons.SECRET,
            status=ClaimLogStatuses.PROPOSED,
        )
        assert ClaimLog.objects.all().count() == 1
        appointment.refresh_from_db()
        assert appointment.booked_for == appointment_bci

    def test_post_200_empty_card_another_card_with_user_plus_same_phones(self):
        session_user = self.session.get_user()

        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=session_user,
            cell_phone='***********',
        )
        appointment_bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
            cell_phone='***********',
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': appointment_bci,
            },
        )
        appointment = booking.appointment
        # assert appointment.booked_for is None

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK  # same booked_for.user so no difference
        assert ClaimLog.objects.filter(
            target_bci=bci,
            source_bci=appointment_bci,
            merge_reason=ClaimLogReasons.SECRET,
            status=ClaimLogStatuses.MERGED,
        )
        assert ClaimLog.objects.all().count() == 1
        appointment.refresh_from_db()
        assert appointment.booked_for == bci

    def test_post_200_diff_phone_with_consents(self):
        session_user = self.session.get_user()

        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=session_user,
            cell_phone='***********',
        )
        appointment_bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
            cell_phone='***********',
        )
        consent_form = baker.make(
            ConsentForm,
            business=business,
        )
        consent = baker.make(
            Consent,
            form=consent_form,
            customer=appointment_bci,
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': appointment_bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == appointment_bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK  # same booked_for.user so no difference
        merge_log = ClaimLog.objects.filter(
            target_bci=bci,
            source_bci=appointment_bci,
            merge_reason=ClaimLogReasons.SECRET,
            status=ClaimLogStatuses.PROPOSED,
        ).first()
        assert merge_log
        assert ClaimLog.objects.all().count() == 1
        appointment.refresh_from_db()
        assert appointment.booked_for == appointment_bci
        source_relations = json.loads(merge_log.source_model_related_objects)
        assert set(source_relations.keys()).issuperset(
            {
                'Appointment',
                'BCIAttachedFile',
                'BCIPhoto',
                'BusinessCustomerInfoHistory',
                'BusinessCustomerInfoTag',
                'Buyer',
                'Consent',
                'Transaction',
                'Unsubscribed',
                'Voucher',
                'VoucherChangeLog',
                'VoucherOrder',
            }
        )
        assert 'ClaimLog' not in set(source_relations.keys())
        assert str(appointment.id) in source_relations['Appointment']
        assert str(consent.pk) in source_relations['Consent']

    def test_post_400_no_secret(self):
        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                # no secret because just no
            },
        )

        assert resp.code == 400
        assert resp.json == {
            'errors': [
                {
                    'field': 'secret',
                    'description': 'This field is required.',
                    'code': 'required',
                }
            ],
        }

    def test_post_400_bad_secret(self):
        business = baker.make(Business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            user=None,
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={'secret': 'jhasbjkdsabjkbhadsbadsfbadssdkbjasdfbjsakfbsadfbsdafbjka'},
        )

        assert resp.code == 400
        assert resp.json == {
            'errors': [
                {
                    'field': 'secret',
                    'description': 'Invalid secret',
                    'code': 'invalid',
                }
            ],
        }

    def test_post_200_no_claim_no_booked_for(self):
        business = baker.make(Business)
        booking, _business, __ = create_subbooking(
            business=business,
        )
        appointment = booking.appointment
        assert appointment.booked_for is None

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_200_OK

    def test_retry_claiming_in_case_of_integrity_error(self):
        """This test "reproduces" a race condition, where two requests were creating a BCI for
        the same business and user pair. It makes sure that operation is retried if there was an
        IntegrityError.

        The first request is /customer_api/me/likes/bulk_create/ (also retried in case of errors)
        The other is /customer_api/me/appointments/{id})/claim/.
        """
        business = baker.make(Business)
        common_cell_phone = '***********'
        logged_in_user = User.objects.filter(
            id=self.logged_in_user_id,
        ).first()
        logged_in_user.cell_phone = common_cell_phone
        logged_in_user.save()
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            cell_phone=common_cell_phone,
            user=None,
        )
        booking, *_ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == bci

        url = self.url.format(appointment.id)

        with mock.patch(
            'webapps.business_customer_info.bci_merge.BusinessCustomerInfo.save',
            side_effect=[IntegrityError, None],
        ):
            resp = self.fetch(
                url,
                method='POST',
                body={
                    'secret': str(booking.appointment.secret),
                },
            )
        # IntegrityError was caught and the operation was retried
        assert resp.code == status.HTTP_200_OK


class CustomerAppointmentClaimHandlerForFamilyAndFriendsTestCase(
    BaseCustomerAppointmentClaimHandlerTestCase, TestFamilyAndFriendsMixin
):
    def test_dont_claim_family_and_friends(self):
        business = business_recipe.make()
        parent_bci, member_bci = self.create_inactive_member_bcis(business=business)
        appointment = self.create_appointment(parent_bci, member_bci)

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(appointment.secret),
            },
        )
        assert resp.code == status.HTTP_200_OK
        assert not ClaimLog.objects.exists()

        member_bci.refresh_from_db()
        assert member_bci.user is None


class CustomerAppointmentClaimHandlerLoggedOutTestCase(
    BaseCustomerAppointmentClaimHandlerTestCase,
    BaseTestAppointment,
):
    url = "/customer_api/me/appointments/{}/claim/?"

    def get_headers(self, path):
        headers = super().get_headers(path)
        headers.pop('X-ACCESS-TOKEN')
        return headers

    def test_post_403_logged_out(self):
        business = baker.make(Business)
        common_cell_phone = '***********'
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            cell_phone=common_cell_phone,
            user=None,
        )
        booking, _business, __ = create_subbooking(
            business=business,
            booking_kws={
                'booked_for': bci,
            },
        )
        appointment = booking.appointment
        assert appointment.booked_for == bci

        url = self.url.format(appointment.id)

        resp = self.fetch(
            url,
            method='POST',
            body={
                'secret': str(booking.appointment.secret),
            },
        )

        assert resp.code == status.HTTP_403_FORBIDDEN
