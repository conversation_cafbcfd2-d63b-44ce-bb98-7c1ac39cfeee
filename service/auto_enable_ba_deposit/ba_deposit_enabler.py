from typing import cast

from django.db.models import QuerySet

from lib.db import (
    READ_ONLY_DB,
    using_db_for_reads,
)
from lib.feature_flag.feature.payment import AutoBADepositParams
from service.auto_enable_ba_deposit.can_enable_ba_deposit_checker import (
    CanEnableBADepositCheckerParams,
    CanEnableBADepositChecker,
)
from webapps.business.models.models import Business
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import POS, PaymentType
from webapps.pos.serializers import POSChangeLogSerializer
from webapps.stripe_integration.enums import StripeAccountStatus
from webapps.user.tools import get_system_user


class BADepositEnabler:
    def __init__(self, params: CanEnableBADepositCheckerParams):
        self.checker = CanEnableBADepositChecker(params)

    @classmethod
    def try_enable_kip(cls, pos_ids: list[int], is_dry_run: bool = True) -> list[int]:
        enabled_ids = []
        for pos_id in pos_ids:
            if cls.__create_kip_payment_method(pos_id, is_dry_run):
                enabled_ids.append(pos_id)
        return enabled_ids

    @classmethod
    def try_enable_ba_deposit(cls, businesses_ids: list[int], is_dry_run: bool = True) -> list[int]:
        enabled_ids = []
        for business_id in businesses_ids:
            if cls.__enable_ba_deposit(business_id, is_dry_run):
                enabled_ids.append(business_id)
        return enabled_ids

    @staticmethod
    @using_db_for_reads(READ_ONLY_DB)
    def get_poses_to_try_enable_ba_deposit() -> QuerySet[POS]:
        """
        Get all businesses that should be considered for enabling BA Deposit.
        """
        poses_to_be_enabled = POS.objects.filter(
            ba_deposit_enabled=False,
            stripe_accounts__status=StripeAccountStatus.VERIFIED,
            business__isnull=False,
            business__status__in=[Business.Status.PAID, Business.Status.OVERDUE],
            business__active=True,
        )
        return poses_to_be_enabled

    @staticmethod
    def get_enable_ba_deposit_checker_params() -> CanEnableBADepositCheckerParams | None:
        flag_params: dict = cast(AutoBADepositParams.flag_type, AutoBADepositParams())

        if len(flag_params) == 0:
            return None

        try:
            return CanEnableBADepositCheckerParams(
                min_payouts_count=flag_params['min_payouts_count'],
                min_payments_amount=flag_params['min_payments_amount'],
                min_payments_count=flag_params['min_payments_count'],
                min_customer_appointments=flag_params['min_finished_customer_appointments_count'],
            )
        except KeyError:
            return None

    @staticmethod
    def __enable_ba_deposit(business_id: int, is_dry_run: bool = True) -> bool:
        if is_dry_run:
            print(f"Dry run - BA deposit WILL NOT be enabled for {business_id}.")
            return False

        pos = POS.objects.filter(business_id=business_id).first()
        pos.ba_deposit_enabled = True
        pos.save()

        serializer = POSChangeLogSerializer(instance=pos)
        pos.log_changes(operator_id=get_system_user().id, data=serializer.data)
        return True

    @staticmethod
    def __create_kip_payment_method(pos_id: int, dry_run: bool = True) -> None:
        """
        Creates or update (if it was soft deleted) PaymentType object for KIP payment. Reorders
        PaymentTypes so KiP order is equal to 3.
        """
        if not dry_run:
            kip_payment_type, created = PaymentType.all_objects.get_or_create(
                pos_id=pos_id,
                code=PaymentTypeEnum.KEYED_IN_PAYMENT,
                defaults={'enabled': True, 'available': True, 'order': 3},
            )

            if created:
                existing_payments = list(
                    PaymentType.objects.filter(pos=pos_id)
                    .exclude(code=PaymentTypeEnum.KEYED_IN_PAYMENT)
                    .order_by('order')[:3]
                )

                for i, payment in enumerate(existing_payments):
                    payment.order = i

                PaymentType.objects.bulk_update(existing_payments, ['order'])

            if kip_payment_type.deleted:
                kip_payment_type.deleted = None
                kip_payment_type.enabled = True
                kip_payment_type.available = True
                kip_payment_type.order = 3
                kip_payment_type.save(update_fields=['enabled', 'available', 'deleted', 'order'])
