from itertools import cycle
from model_bakery.recipe import Recipe, foreign_key

from webapps.business.models.models import Business
from webapps.market_pay.models import AccountHolder
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import StripeAccountHolderSettings
from webapps.pos.models import POS
from webapps.stripe_integration.enums import StripeAccountStatus
from webapps.stripe_integration.models import StripeAccount


def create_test_poses() -> tuple[list[POS], list[POS]]:
    active_business_recipe = Recipe(
        Business, active=True, status=cycle((Business.Status.PAID, Business.Status.OVERDUE))
    )
    businesses = active_business_recipe.make(_quantity=4)
    to_activate_poses = []

    for biz in businesses:
        to_activate_pos_recipe = Recipe(
            POS,
            business_id=biz.id,
        )
        pos = to_activate_pos_recipe.make()
        to_activate_poses.append(pos)
        to_activate_account_holder_recipe = Recipe(
            AccountHolder,
            pos=pos,
        )
        to_activate_account_holder_recipe.make()
        to_activate_stripe_account = Recipe(
            StripeAccount, status=StripeAccountStatus.VERIFIED, pos=pos
        )
        to_activate_stripe_account.make()
        business_wallet, _ = PaymentGatewayPort.get_or_create_business_wallet(
            business_id=biz.id, statement_name='statement name,'
        )
        to_activate_stripe_accounts_recipe = Recipe(
            StripeAccountHolderSettings,
            pba_fees_accepted=True,
            account_holder_id=business_wallet.account_holder_id,
        )
        to_activate_stripe_accounts_recipe.make()

    not_active_business_recipe = Recipe(
        Business,
        active=False,
        status=cycle((Business.Status.SUSPENDED, Business.Status.TRIAL, Business.Status.BLOCKED)),
    )
    not_to_activate_pos_recipe = Recipe(
        POS,
        business=foreign_key(not_active_business_recipe, one_to_one=True),
    )
    not_active_poses = not_to_activate_pos_recipe.make(_quantity=6)
    return to_activate_poses, not_active_poses
