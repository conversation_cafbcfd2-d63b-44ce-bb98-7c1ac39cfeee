from collections import OrderedDict, defaultdict
from datetime import date, datetime, timedelta
from enum import Enum
from logging import getLogger
from typing import Any, Dict, Iterable, List, Optional, Tuple, Type, Union

import elasticsearch_dsl as dsl
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.db.models.expressions import F, Q, Subquery, Window
from django.db.models.functions import FirstValue
from django.utils.translation import gettext as _
from elasticsearch import ConnectionTimeout
from elasticsearch_dsl import AttrDict
from rest_framework import status

from lib.db import READ_ONLY_DB, on_operational_error, using_db_for_reads
from lib.elasticsearch.consts import ES_BASIC_DISTANCE, ESDocType
from lib.feature_flag.adapter import UserData
from lib.feature_flag.consts import CUSTOMER_BOOKING_SOURCE_TO_CLIENT_APP
from lib.feature_flag.enums import AppDomains, ClientApp, ExperimentVariants, SubjectType
from lib.feature_flag.experiment.customer import (
    S4UGalleriesOrderExperiment,
    VistedLikedSelected4UExperiment,
)
from lib.feature_flag.feature.customer import (
    CustomerMyBooksySugestedForYouGalleryFlag,
    SelectedForYouExcludeBoostFlag,
    SelectedForYouSearchableV2Flag,
    VistedLikedSelected4UFlag,
)
from lib.fields.date_time_interval import date_f, parse_es_datetime_input
from lib.geocoding.base import GeocodingError
from lib.geocoding.built_in import BuiltInGeoCoder
from lib.searchables.common import IdsSearchable
from lib.searchables.response import SerializerResponse
from lib.searchables.searchables import MissingValueException, Searchable, V
from lib.segment_analytics.enums import EventType
from lib.tools import sget_v2, tznow
from lib.x_version_compatibility.utils import get_client_version, parse_x_version
from service.search.search_engine import apply_utt2_search_keys
from service.tools import AnalyticsTokensMixin, RequestHandler, ViewListItemEventMixin, session
from webapps.booking.enums import AppointmentStatus
from webapps.booking.models import Appointment
from webapps.booking.my_booksy import BookingBox
from webapps.booking.serializers.my_booksy import (
    BookingBoxSerializer,
    BusinessesGallerySerializer,
    FavoritesVisitedSerializer,
    MyBooksyGalleriesSerializer,
    MyBooksyGalleriesSerializerV2,
    MyBooksySerializer,
    WelcomeCategoriesSerializer,
)
from webapps.boost.tools import get_promoted_labels
from webapps.business.business_categories.cache import (
    BusinessCategoryCache,
    CategoryCache,
    CategoryWithIconV2Cache,
    SubcategoryCache,
    TreatmentCache,
)
from webapps.business.enums import CustomData
from webapps.business.models import Business
from webapps.business.models.category import BusinessCategory
from webapps.business.searchables import business as business_searchables
from webapps.business.searchables.business import BusinessTermsWithSingleImagesSearchable
from webapps.business.searchables.business.faceting import CategoryFacetingSearchable
from webapps.business.searchables.business.galleries.selected_for_you import (
    SelectedForYouSearchable,
    SelectedForYouSearchableV2,
    SelectedForYouSearchableV3,
)
from webapps.business.searchables.business.search_engine import VisibleBusinessSearchable
from webapps.business.searchables.serializers import (
    BusinessMapHitSerializer,
    BusinessWithSingleImagesHitSerializer,
    ResourceHitSerializer,
)
from webapps.experiment_v3.exp import NewOnBooksyGalleryExperiment, RecommendedGalleryNameExperiment
from webapps.images.enums import BusinessCategoryIconVersion
from webapps.kill_switch.models import KillSwitch
from webapps.pop_up_notification.models import GenericPopUpNotificationModel
from webapps.pop_up_notification.utils import customer_notification_serialized_data
from webapps.pos.models import Transaction
from webapps.pos.serializers import ReceiptPaymentRowsSerializer
from webapps.reviews.models import Review
from webapps.segment.tasks import analytics_customer_app_opened_task
from webapps.segment.utils import should_trigger_customer_event_by_geo
from webapps.structure.models import get_categories_by_region
from webapps.user.const import Gender
from webapps.user.searchables.serializers.user import UserSerializer
from webapps.utt.cache import (
    get_categories_all_utt,
    get_category_utt_by_id,
    get_treatment_utt_by_id,
)
from webapps.utt.tools import is_in_experiment

logger = getLogger('booksy.es_timeout')
GalleryType = List[Dict[str, Union[str, Dict]]]
GeoLocationType = Dict[str, float]


class MyBooksy(RequestHandler):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)
    FAVORITED_BUSINESSES_LIMIT: int = 30
    BOOKMARKED_STAFFERS_PER_BUSINESS_LIMIT: int = 5

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._user_data = None

    @session(optional_login=True)
    def get(self):
        """My Booksy page.

        swagger:
            summary: MyBooksy
            type: MyBooksyResponse
            parameters:
                - name: gender
                  description: >
                        Gender of the anonymous user ('M'|'F'|'R').
                        Will be ignored for logged user.
                  required: False
                  paramType: query
                  type: string
                  enum:
                    - M
                    - F
                    - R
                - name: geo_location
                  description: >
                    latitude,longitude of a geo point used to calculate distance
                    from business.<br>
                    Ex. "40.75,-73.99" <br>
                  paramType: query
                  required: False
                  type: string
                - name: notification_id
                  description: notification id
                  paramType: query
                  type: int
                - name: no_thumbs
                  type: boolean
                  paramType: query
                - name: category_icon_version
                  description: value 2.1 for version 2 icon, any other value for version 1 icon
                  type: string
                  paramType: query
                  required: False
                  defaultValue: 1
                  enum:
                    - 1
                    - 2
                    - 2.1
        :swagger

        swaggerModels:
            MyBooksyResponse:
                id: MyBooksyResponse
                description: My Booksy for logged in user
                properties:
                    pop_up_notifications:
                        type: array
                        items:
                            type: MyBooksyNotification
                    booking_box:
                        type: array
                        description: Not implemented docs yet
                    prepayment_notifiactions:
                        type: array
                        description: Not implemented docs yet
                    favorites_visited:
                        type: array
                        items:
                            type: FavoritesVisited
                    main_categories:
                        type: array
                        items:
                            type: CategoryDetails

            FavoritesVisited:
                id: FavoritesVisited
                description: Last Visited or Favorites Businesses
                properties:
                    business:
                        type: BusinessInCustomerAppointment
                        description: ...with additional field 'bookmarked'
                    bookmarked_staffers:
                        description: >
                            business bookmarked staffers.
                            Empty if appointment provided.
                        type: array
                        items:
                            type: ResourceDetails
                            description: ...with additional field 'bookmarked'
                    appointment:
                        type: AppointmentDetails
                        description: last appointment booked with given staffer
        """
        data = self._prepare_get_arguments()
        utt2_experiment = False
        if self.user:
            utt2_experiment = is_in_experiment(self.user.id)
            if self.user.gender:
                data['gender'] = self.user.gender

        serializer = MyBooksySerializer(data=data)
        data = self.validate_serializer(serializer)

        if data['category_icon_version'] == BusinessCategoryIconVersion.V2_1:
            categories = CategoryWithIconV2Cache.get_all(gender=data.get('gender'))
        elif utt2_experiment:
            categories = get_categories_all_utt(data.get('gender'))
        else:
            categories = CategoryCache.get_all(gender=data.get('gender'))

        ret = {
            'booking_box': None,
            'favorites_visited': None,
            'main_categories': categories,
            'subcategories': SubcategoryCache.get_all(),
            'pop_up_notifications': [],
            'prepayment_notification': None,
        }
        if self.user:
            booking_box = BookingBox(self.user)
            ret['booking_box'] = self._get_booking_box_data(booking_box)
            # pylint: disable=unsubscriptable-object
            last_booking_service_id = (box := ret['booking_box']) and box['service'].get('id')
            # pylint: enable=unsubscriptable-object
            ret['favorites_visited'] = self.favorites_visited(
                self.user.id,
                data.get('geo_location'),
                booking_box,
                no_thumbs=data.get('no_thumbs', False),
                last_booking_service_id=last_booking_service_id,
                gender=data.get('gender'),
            )
            ret['pop_up_notifications'] = self.get_pop_up_notifications(data.get('notification_id'))
            ret['prepayment_notification'] = self.get_prepayment_notification(ret['booking_box'])

        self.finish_with_json(200, ret)

    @property
    def user_data(self) -> Dict:
        user_data = {}
        if self.user:
            if self._user_data is None:
                res = (
                    IdsSearchable(ESDocType.USER, serializer=UserSerializer())
                    .params(size=1)
                    .execute({'ids': [self.user.id]})
                )
                if res.hits.total.value:
                    self._user_data = res[0].to_dict()
                else:
                    self._user_data = {}
            user_data = self._user_data
        return user_data

    def _is_new_user(self):
        user_data = self.user_data
        return user_data == {} or not (
            user_data.get('businesses') or user_data.get('last_booking_location')
        )

    @on_operational_error(return_value=None)
    @using_db_for_reads(READ_ONLY_DB)
    def _get_booking_box_data(self, box: BookingBox):
        booking_box = box.get_serialized_appointment()
        return booking_box

    @using_db_for_reads(READ_ONLY_DB)
    def _get_selected_4_you_one_business(self, excluded_ids, gender, geo_location, service_id):
        categories_ids = None
        qset = BusinessCategory.objects.filter(services=service_id).values_list(
            'parent_id', flat=True
        )
        if service_id and (category_id := qset.last()):
            categories_ids = [category_id]
        if not categories_ids:
            qset = Business.objects.filter(id__in=excluded_ids, primary_category__isnull=False)
            if not (categories_ids := list({*qset.values_list('primary_category', flat=True)})):
                return

        # ids from ES calculated by search tuning
        # categories_ids = self.user_data.get('categories', [])
        data = {
            'min_treatment_count': 1,
            'business_categories': categories_ids,
            'excluded': excluded_ids,
        }
        if gender:
            data['gender'] = gender
        if geo_location:
            data['location_geo'] = geo_location

        if SelectedForYouExcludeBoostFlag():
            data['is_boost'] = False
            searchable = SelectedForYouSearchableV3(
                ESDocType.BUSINESS,
                serializer=BusinessWithSingleImagesHitSerializer,
            )
        else:
            searchable = SelectedForYouSearchableV2(
                ESDocType.BUSINESS,
                serializer=BusinessWithSingleImagesHitSerializer,
            )
        if result := searchable.params(size=1).search(data).execute():
            return result[0].to_dict() | {'is_selected_for_you': True}

    @on_operational_error(return_value=None)
    @using_db_for_reads(READ_ONLY_DB)
    def favorites_visited(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        self, user_id, geo_location, booking_box, no_thumbs, last_booking_service_id, gender
    ):
        # region important appointments
        excluded_business_ids = self.get_bad_reviewed_business_ids(user_id)
        important_appointment_data = self.get_important_appointments_data(
            booking_box, excluded_business_ids
        )
        # endregion important appointments

        # region bookmarks
        bookmarked_businesses = [
            business
            for business in self.user_data.get('businesses', [])
            if (business.get('id') and business['id'] not in important_appointment_data)
        ]
        bookmarked_businesses_ids = [business['id'] for business in bookmarked_businesses]
        bookmarked_business_staffers = self.get_bookmarked_staffers(bookmarked_businesses)
        # endregion bookmarks

        # optimization for bulk business get from ElasticSearch
        unique_business_ids = set(important_appointment_data.keys()).union(
            bookmarked_businesses_ids
        )
        visible_businesses = self.get_serialized_businesses(
            unique_business_ids,
            geo_location,
            no_thumbs=no_thumbs,
        )

        # get businesses based on appointments - `booking_per_business`
        instances = [
            {'business': visible_businesses[business_id], 'appointment': appointment_data}
            for business_id, appointment_data in important_appointment_data.items()
            if business_id in visible_businesses
        ]
        # add businesses based on bookmarks (business likes + staffer likes)
        instances += [
            {
                'business': visible_businesses[business_id],
                'bookmarked_staffers': bookmarked_business_staffers[business_id],
            }
            for business_id in bookmarked_businesses_ids
            if business_id in visible_businesses
        ]
        instances = sorted(
            instances,
            key=lambda x: x['business']['is_b_listing'],
        )
        self._inject_selected_4_you_business(
            gender,
            geo_location,
            instances,
            last_booking_service_id,
            user_id,
            list(visible_businesses.keys()),
            excluded_business_ids,
        )
        return FavoritesVisitedSerializer(
            instances[: self.FAVORITED_BUSINESSES_LIMIT],
            many=True,
            context={
                'businesses_from_bookmark_ids': set(
                    business['id'] for business in bookmarked_businesses if business['bookmarked']
                ),
            },
        ).data

    def _inject_selected_4_you_business(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        self,
        gender,
        geo_location,
        instances,
        last_booking_service_id,
        user_id,
        visible_businesses_ids,
        excluded_businesses_ids,
    ):
        if flag_data := VistedLikedSelected4UFlag():
            if min_version := flag_data.get(self.request.booking_source.name):
                min_version = parse_x_version(min_version)

            is_version_ok = not min_version or get_client_version(self.request) >= min_version
            geo_location = geo_location or self.user_data.get('last_booking_location')
            if visible_businesses_ids and is_version_ok and geo_location:
                app = {
                    'iPhone': 'ios-cust',
                    'Android': 'android-cust',
                    'Web': 'web-customer-2019',
                }.get(self.request.booking_source.name, 'core-api')
                experiment_variant = VistedLikedSelected4UExperiment(
                    UserData(
                        subject_key=user_id,
                        subject_type=SubjectType.USER_ID,
                        is_experiment=True,
                        app_domain=AppDomains.CUSTOMER,
                        app=app,
                    )
                )
                if experiment_variant == ExperimentVariants.VARIANT_A:
                    if selected_4_you_business := self._get_selected_4_you_one_business(
                        excluded_ids=visible_businesses_ids + excluded_businesses_ids,
                        gender=gender,
                        geo_location=geo_location,
                        service_id=last_booking_service_id,
                    ):
                        instances.insert(1, {'business': selected_4_you_business})

    @staticmethod
    def get_bad_reviewed_business_ids(user_id: int) -> List[int]:
        """
        Return bad reviewed business_ids

        :param user_id: User to check.
        :return: List of ids of last updated reviews.
        """
        review_ids = (
            Review.objects.filter(
                user_id=user_id,
            )
            .annotate(
                latest_updated_review_id=Window(
                    expression=FirstValue('id'),
                    order_by=F('updated').desc(),
                    partition_by=[F('business_id')],
                )
            )
            .values('latest_updated_review_id')
        )

        return list(
            Review.objects.filter(
                id__in=Subquery(review_ids),
                rank__lt=2,
            ).values_list(
                'business_id',
                flat=True,
            )
        )

    @staticmethod
    def importance(appointment_data, now):
        timeshift = now + timedelta(days=365 * 10)
        if appointment_data['booked_till'] >= now:
            return [now - appointment_data['booked_from'], now - appointment_data['created']]
        return [appointment_data['booked_from'] - timeshift, now - appointment_data['created']]

    @classmethod
    def get_important_appointments_data(
        cls, booking_box: BookingBox, excluded_business_ids: list[int]
    ) -> OrderedDict[int, dict]:
        now = tznow()
        data = booking_box.appointments_data
        candidates_appointments = {}
        for appointment_data in data:
            if appointment_data['booked_from'] < now - timedelta(days=6 * 30):
                break
            if appointment_data['status'] in [
                Appointment.STATUS.FINISHED,
                Appointment.STATUS.ACCEPTED,
                Appointment.STATUS.UNCONFIRMED,
            ]:
                business_id = appointment_data['business_id']
                if business_id in excluded_business_ids:
                    continue
                if business_id not in candidates_appointments or (
                    cls.importance(appointment_data, now)
                    > cls.importance(candidates_appointments[business_id], now)
                ):
                    candidates_appointments[business_id] = appointment_data
        acceptable_business_ids = Business.objects.filter(
            (Q(reviews_rank_avg__gte=3) | Q(reviews_rank_avg__isnull=True)),
            id__in=candidates_appointments.keys(),
            active=True,
            visible=True,
        ).values_list('id', flat=True)

        candidates_appointments = {
            business_id: data
            for business_id, data in candidates_appointments.items()
            if business_id in acceptable_business_ids
        }
        final_candidate_appointments = OrderedDict()
        for business_id, data in sorted(
            candidates_appointments.items(), key=lambda x: x[1]['created'], reverse=True
        )[: cls.FAVORITED_BUSINESSES_LIMIT]:
            final_candidate_appointments[business_id] = data

        return final_candidate_appointments

    @classmethod
    def get_bookmarked_staffers(cls, businesses: List[Dict]) -> Dict[int, List[int]]:
        bookmarked_staffers_ids = set(
            staffer_id
            for business in businesses
            for staffer_id in business.get('bookmarked_staffers', [])[
                : cls.BOOKMARKED_STAFFERS_PER_BUSINESS_LIMIT
            ]
        )
        if bookmarked_staffers_ids:
            bookmarked_staffers = (
                IdsSearchable(
                    ESDocType.RESOURCE,
                    serializer=ResourceHitSerializer,
                )
                .params(
                    size=len(bookmarked_staffers_ids),
                )
                .execute({'ids': bookmarked_staffers_ids})
            )
        else:
            bookmarked_staffers = []
        bookmarked_business_staffers = defaultdict(list)
        for staffer in bookmarked_staffers:
            bookmarked_business_staffers[staffer.business.id].append(staffer.to_dict())
        return bookmarked_business_staffers

    @staticmethod
    def get_serialized_businesses(business_ids, geo_location, no_thumbs=False):
        """Get bulk business from ElasticSearch.

        :param business_ids: iterable fo ints.
        :param geo_location: dict with ES formatted dict of coordinates
        :param no_thumbs: generate images thumbnails
        :return: Where key is business_id and value
                is serialized info(BusinessWithSingleImagesHitSerializer)
        :rtype: dict
        """
        data = {'ids': business_ids}
        if geo_location:
            data['location_geo'] = geo_location
        result = (
            BusinessTermsWithSingleImagesSearchable(
                ESDocType.BUSINESS,
                serializer=BusinessWithSingleImagesHitSerializer,
            )
            .params(
                size=len(business_ids),
                no_thumbs=no_thumbs,
            )
            .search(
                data,
            )
            .execute()
        )
        return {doc.id: doc.to_dict() for doc in result}

    def get_pop_up_notifications(self, notification_id=None):
        if not self.user:
            return []

        non_business_notifications = [
            GenericPopUpNotificationModel.TYPE_FAMILY_AND_FRIENDS_INVITATION,
            GenericPopUpNotificationModel.TYPE_FAMILY_AND_FRIENDS_INVITATION_RESPONSE,
            GenericPopUpNotificationModel.TYPE_FAMILY_AND_FRIENDS_UNLINK,
            GenericPopUpNotificationModel.TYPE_WHAT_IS_NEW_FAMILY_AND_FRIENDS,
        ]
        show_without_business = Q(notification_type__in=non_business_notifications)

        notification_qs = (
            GenericPopUpNotificationModel.objects.exclude(
                business__custom_data__has_key=CustomData.DISABLE_NOTIFICATIONS_TO_CLIENTS,
                **{
                    f'business__custom_data__{CustomData.DISABLE_NOTIFICATIONS_TO_CLIENTS}': True,
                },
            )
            .filter(
                Q(
                    business__active=True,
                    business__visible=True,
                )
                | Q(show_without_business)
            )
            .filter(
                notification_type__in=(GenericPopUpNotificationModel.CUSTOMER_NOTIFICATIONS),
                user=self.user,
                valid_till__gt=tznow(),
                valid_since__lte=tznow(),
                used=False,
            )
            .order_by(
                'valid_since',
            )
        )
        if notification_id:
            notification_qs = notification_qs.filter(id=notification_id)
        notification = self._check_popup_additional_conditions(notification_qs)

        return (
            [{notification.notification_type: customer_notification_serialized_data(notification)}]
            if notification
            else []
        )

    @staticmethod
    def _check_popup_additional_conditions(notification_qs, max_iter=5):
        for notification in notification_qs[:max_iter]:
            return notification

        return GenericPopUpNotificationModel.objects.none().first()

    @staticmethod
    def get_prepayment_notification(booking_box: BookingBoxSerializer.data) -> dict | None:
        if not (
            booking_box
            and booking_box.get('transaction_id')
            and booking_box.get('status') == AppointmentStatus.PENDING_PAYMENT
        ):
            return

        transaction = Transaction.objects.get(id=booking_box['transaction_id'])
        pr_data = ReceiptPaymentRowsSerializer(
            instance=transaction.payment_rows.last(),
            context={
                'pos': transaction.pos,
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
            },
        ).data

        message = {
            'header': _('Confirm your appointment'),
            'subheader': _(
                'Your upcoming booking with {business_name} requires a deposit to confirm.'
            ).format(business_name=sget_v2(booking_box, ['business', 'name'])),
            'paragraph': _(
                'Make a deposit of {prepayment_total} now to secure your appointment '
                'and avoid cancellation.'
            ).format(prepayment_total=pr_data['amount_text']),
        }
        buttons = {
            'confirm_button_text': _('Pay now'),
            'cancel_button_text': _('Remind me later'),
        }
        return {
            'message': message,
            'buttons': buttons,
            'appointment_uid': booking_box.get('appointment_uid'),
            'transaction_id': transaction.id,
        }


# region deprecated since August 2019 (task 50776)
class WelcomeCategoriesSearchable(Searchable):
    class Meta:
        response_class = business_searchables.BusinessSearchableResponse

    active_businesses = VisibleBusinessSearchable()
    limit_gender = business_searchables.GenderCategorySearchable()
    category_facet = CategoryFacetingSearchable()
    # experiment
    # location = business_searchables.BusinessLocationSearchable()
    location = dsl.query.GeoDistance(
        distance=V('distance_radius', settings.ES_DEFAULT_RADIUS),
        business_location__coordinate=V('get_location_geo'),
    )

    @staticmethod
    def get_location_geo(data):
        if 'latitude' not in data or 'longitude' not in data:
            raise MissingValueException()
        return f"{data['latitude']},{data['longitude']}"


class MyBooksyWelcomeCategories(RequestHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)
    MIN_BUSINESS_IN_CATEGORY = 3
    MIN_BUSINESS_SUM_IN_CATEGORIES = 10

    @session(login_required=False)
    def get(self):
        """
        swagger:
            summary: Categories for Welcome to Location popup
            type: MyBooksyWelcomeCategoriesResponse
            parameters:
                - name: latitude
                  type: number
                  paramType: query
                  required: true
                - name: longitude
                  type: number
                  paramType: query
                  required: true
                - name: gender
                  description: gender of the anonymous user ('M'|'F')
                  paramType: query
                  type: string
        :swagger

        swaggerModels:
            MyBooksyWelcomeCategoriesResponse:
                id: MyBooksyWelcomeCategoriesResponse
                properties:
                    welcome_categories_by_location:
                        type: array
                        description: list of categories ids
        """

        args = self._prepare_get_arguments()

        serializer = WelcomeCategoriesSerializer(data=args)
        self.validate_serializer(serializer)
        data = dict(serializer.validated_data)

        # pylint: disable=unsupported-assignment-operation
        if data.get('gender') == Gender.Male.value:
            data['min_female_weight'] = 0
            data['max_female_weight'] = 50
        if data.get('gender') == Gender.Female.value:
            data['min_female_weight'] = 50
            data['max_female_weight'] = 100

        data['category'] = [c['id'] for c in CategoryCache.get_all()]
        # pylint: enable=unsupported-assignment-operation

        resp = (
            WelcomeCategoriesSearchable(
                ESDocType.BUSINESS,
                serializer=BusinessMapHitSerializer,
            )
            .params(
                size=0,
                _source=False,
            )
            .execute(data)
        )
        categories = resp.aggregations.categories.categories
        # filter min businesses
        categories = [c for c in categories if c.doc_count > self.MIN_BUSINESS_IN_CATEGORY]
        # filter sum businesses
        categories = (
            [c.key for c in categories]
            if sum(c.doc_count for c in categories) > self.MIN_BUSINESS_SUM_IN_CATEGORIES
            else []
        )

        ret = {'welcome_categories_by_location': categories}
        self.finish_with_json(200, ret)


class MyBooksyTopRated(RequestHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)

    @session(login_required=False)
    def get(self):
        """
        swagger:
            summary: Top Rated block of My Booksy page.
            type: MyBooksyTopRatedResponse
            parameters:
                - name: latitude
                  type: number
                  paramType: query
                - name: longitude
                  type: number
                  paramType: query
                - name: gender
                  description: gender of the anonymous user ('M'|'F')
                  paramType: query
                  type: string
                - name: include_reviews
                  description: Include most useful reviews for
                               featured businesses. Please note that reviews is
                               queried from db with all prefetch,
                               that could be computationally expensive.
                  paramType: query
                  required: False
                  type: integer
                  enum:
                    - 0
                    - 1
                  defaultValue: 0
        :swagger

        swaggerModels:
            MyBooksyTopRatedResponse:
                id: MyBooksyTopRatedResponse
                properties:
                    top_rated_pros:
                        type: array
                    featured:
                        type: array
                    best_in:
                        type: array
                    top_reviews:
                        type: array
                        items:
                            type: TopReviewResponse
        """

        args = self._prepare_get_arguments()

        # parse data - TODO: why there is no serializer used?
        geo_location = self.get_location_geo(args)
        gender = args.get('gender')

        # build response
        businesses_with_prmotions = self.businesses_with_promotions(geo_location, gender)
        excluded_business_ids = [b.id for b in businesses_with_prmotions]
        ret = {
            'best_in': self.get_near_me_businesses(
                geo_location=geo_location,
                gender=gender,
                excluded_business_ids=excluded_business_ids,
            ),
            'featured': businesses_with_prmotions,
            'main_category_display': self.main_category_display(geo_location) or [],
            # DEPRECATED since 18-06-2019. Key left for backward compatibility.
            'top_rated_pros': [],
            # DEPRECATED since 03-07-2019. Key left for backward compatibility.
            'top_reviews': [],
        }

        self.finish_with_json(200, ret)

    @staticmethod
    def get_location_geo(data):
        geo_location = None
        lat = data.get('latitude')
        lon = data.get('longitude')
        if lat is not None and lon is not None:
            # elastic geopoint format
            geo_location = {
                'lat': float(lat),
                'lon': float(lon),
            }
        return geo_location

    @staticmethod
    @on_operational_error(return_value=None)
    @using_db_for_reads(READ_ONLY_DB)
    def main_category_display(geo_location):
        """Get non-empty categories in user location.

        :param geo_location: a location of the user
        :return: a list of categories or None

        """
        if not geo_location:
            return

        try:
            region_es = BuiltInGeoCoder.get_city_region(geo_location['lat'], geo_location['lon'])
            if not region_es:
                return

        except GeocodingError:
            return

        region_id = region_es.id
        return get_categories_by_region(region_id)

    @staticmethod
    def top_rated_pros(geo_location, gender):
        """Search for top rated professionals in user location.

        DEPRECATED: Removed form usage due to https://pm.booksy.net/issues/54025
        :param geo_location: a location of the user
        :param gender: gender of the user
        :return: a list of staffers

        """
        query = {
            'gender': gender,
        }
        if geo_location:
            query['location_geo'] = geo_location
            query['distance_radius'] = 5000
            query['distance_unit'] = 'km'

        searchable = business_searchables.ResourceSearchSearchable(
            ESDocType.RESOURCE,
        ).params(size=20)
        staffers = searchable.execute(query)

        if staffers.hits.total.value < 100 and geo_location:
            query['distance_radius'] = 50000
            staffers = searchable.execute(query)

        return staffers.hits

    @staticmethod
    def businesses_with_promotions(geo_location, gender):
        """
        Search for business with active promotions.

        :param geo_location: a location of the user
        :param gender: gender of the user
        :return: a list of businesses

        """
        now = tznow()
        end = now + timedelta(hours=72)
        available_for = f'{date_f(now)}-{date_f(end)}'

        data = {
            'gender': gender,
            'available_for': parse_es_datetime_input(available_for),
            'sort_order': 'score',
        }
        if geo_location:
            data['location_geo'] = geo_location

        searchable = (
            business_searchables.ProfitablePromotionsSearchable(
                ESDocType.BUSINESS,
                serializer=BusinessMapHitSerializer,
            )
            .params(size=10)
            .search(data)
        )
        businesses = searchable.execute()
        return list(businesses)

    @staticmethod
    def get_near_me_businesses(
        geo_location, gender, excluded_business_ids, distance_radius=ES_BASIC_DISTANCE
    ):
        """Search for businesses in location excluding featured ones.

        :param geo_location: a location of the user
        :param gender: gender of the user
        :param excluded_business_ids: a list of businesses ids to exclude
        :param distance_radius: the radius within which businesses
            will be searched
        :return: city name and a list of nearest businesses
        :rtype: dict
        """
        if geo_location is None:
            return None

        data = {
            'gender': gender,
            'location_geo': geo_location,
            'distance_radius': distance_radius,
        }

        if excluded_business_ids:
            data['excluded'] = excluded_business_ids

        places = (
            business_searchables.NearMeSearchable(
                ESDocType.BUSINESS,
                serializer=BusinessMapHitSerializer,
            )
            .params(size=20)
            .search(data)
            .execute()
        )

        if places.hits.total.value > 2:
            return {'city': None, 'places': places.hits}


# endregion deprecated since August 2019 (task 50776)


class GalleriesType(Enum):
    NEAR_ME = 'near_me'
    AVAILABLE_TODAY = 'available_today'
    SPECIAL_OFFERS = 'special_offers'
    RECOMMENDED = 'recommended'
    BOOKING_REACTIVATION = 'booking_reactivation'
    NEW_ON_BOOKSY = 'new_on_booksy'
    SELECTED_FOR_YOU_GALLERY = 'selected_for_you_gallery'


class MyBooksyGalleries(  # pylint: disable=too-many-ancestors
    ViewListItemEventMixin,
    MyBooksy,
    AnalyticsTokensMixin,
):
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT, BooksyTeams.CUSTOMER_SEARCH)

    @session(optional_login=True)
    def get(self):  # pylint: disable=too-many-statements, too-many-branches
        """
        swagger:
            summary: Returns galleries shown in customer app homepage.
            type: MyBooksyGalleriesResponse
            parameters:
                - name: geo_location
                  description:
                    latitude,longitude of a geo point where we look for
                    businesses. <br>
                    Ex. "40.75,-73.99" <br>
                  paramType: query
                  required: False
                  type: string
                - name: gender
                  description: gender of the anonymous user ('M'|'F'|'R')
                  paramType: query
                  type: string
                  enum:
                    - 'M'
                    - 'F'
                    - 'R'
                  required: False
                - name: galleries
                  description: Define which galleries needs to be returned.
                    If parameter not provided all galleries.
                  paramType: query
                  type: integer
                  allowMultiple: True
                  required: False
                - name: min_gallery_businesses
                  description:
                    Minimum businesses per gallery (default 2)
                  paramType: query
                  required: False
                  type: integer
                - name: max_gallery_businesses
                  description:
                    Maximum businesses per gallery (default 10)
                  paramType: query
                  required: False
                  type: integer
                - name: no_thumbs
                  type: boolean
                  required: False
                  paramType: query
                - name: categories_ids
                  description: Define which categories will be used in selected for you galleries
                    if user is not logged in.
                    (used with flag Feature_CustomerMyBooksySugestedForYouGallery)
                  paramType: query
                  type: integer
                  allowMultiple: True
                  required: False
        :swagger

        swaggerModels:
           MyBooksyGalleriesResponse:
                id: MyBooksyGalleriesResponse
                properties:
                    1:
                        type: array
                        items:
                            type: MyBooksyBusiness
                    2:
                        type: array
                        items:
                            type: MyBooksyBusiness
                    3:
                        type: array
                        items:
                            type: MyBooksyBusiness
                    4:
                        type: array
                        items:
                            type: MyBooksyBusiness
                    5:
                        type: array
                        items:
                            type: MyBooksyBusiness
                    6:
                        type: array
                        items:
                            type: MyBooksyBusiness
                    7:
                        type: array
                        items:
                            type: MyBooksyBusiness

           MyBooksyBusiness:
                id: MyBooksyBusiness
                description: Business object in gallery
                properties:
                    id:
                      type: integer
                      description: internal Business ID
                    name:
                      type: string
                      description: name of the Business
                    location:
                      type: BusinessLocation
                      description: location of a Business
                    active:
                        type: boolean
                        description: is Business active
                    visible:
                        type: boolean
                        description: is Business publicly visible
                    review_count:
                        type: integer, optional
                        description: number of reviews
                    reviews_stars:
                        type: number
                        description: avearage of star reviews of Business
                    images:
                        type: BusinessImages
                        description: a container for business images
                    primary_category:
                        type: integer
                        description: >
                          ID of primary category
                          (one of chosen ones by business)
                    reviews:
                        description: allways empty field
                        type: array
        :swaggerModels
        """

        args = self._prepare_get_arguments()
        if self.user and self.user.gender:
            args['gender'] = self.user.gender

        if flag_data := CustomerMyBooksySugestedForYouGalleryFlag():
            serializer = MyBooksyGalleriesSerializerV2(data=args)
        else:
            serializer = MyBooksyGalleriesSerializer(data=args)
        data = self.validate_serializer(serializer)

        new_on_booksy_gallery_experiment = NewOnBooksyGalleryExperiment(
            relation_id=self.fingerprint,
            user_pseudo_id=self.user_pseudo_id,
            geo_location=data.get('geo_location'),
        )

        # region preparing data
        gender = data.get('gender')
        geo_location = data.get('geo_location', self.user_data.get('last_booking_location'))
        excluded_business_ids = self.get_excluded_business_ids()

        content_type = None
        if self.user:
            content_type = BusinessCategory.CATEGORY
            self.trigger_segment_analytics(
                geo_location=data.get('geo_location', None),
                user_id=self.user and self.user.id,
            )

        user_categories = self.get_categories_for_user(content_type)

        additional_kwargs = {
            # for experiments
            'user': self.user,
            'is_my_booksy_api': True,
            'new_on_booksy_gallery_experiment': new_on_booksy_gallery_experiment,
        }

        galleries_generator = GalleriesGenerator(
            per_gallery_min=data.get('min_gallery_businesses'),
            per_gallery_max=data.get('max_gallery_businesses'),
            user_categories=user_categories,
            excluded_business_ids=excluded_business_ids,
            content_type=content_type,
            no_thumbs=data.get('no_thumbs', False),
            additional_kwargs=additional_kwargs,
        )
        # endregion preparing data

        # region building response

        # galleries order
        galleries_list = []

        if new_on_booksy_gallery_experiment.is_enabled():
            galleries_list.append(GalleriesType.NEW_ON_BOOKSY)

        if not self.api_key or not self.api_key.lower().startswith('web'):
            galleries_list.append(GalleriesType.SPECIAL_OFFERS)
        galleries_list.append(GalleriesType.RECOMMENDED)

        galleries_kwargs = {
            'geo_location': geo_location,
            'gender': gender,
        }

        now = tznow()
        last_unfinished_booking = self.user_data.get('last_unfinished_booking_date')
        if (
            KillSwitch.alive(KillSwitch.System.BOOKINGS_REACTIVATION)
            and last_unfinished_booking
            and now - timedelta(minutes=10) < last_unfinished_booking
        ):
            galleries_list.insert(0, GalleriesType.BOOKING_REACTIVATION)
            galleries_kwargs.update(
                {
                    f'last_unfinished_booking_{key}': val
                    for key, val in self.user_data.get('last_unfinished_booking_data', {}).items()
                }
            )

        if self._show_availability_galleries():
            galleries_list.extend(
                [
                    GalleriesType.AVAILABLE_TODAY,
                    GalleriesType.NEAR_ME,
                ]
            )
        else:
            galleries_list.append(GalleriesType.NEAR_ME)

        if flag_data:
            defaults = {
                'is_required_user': True,
            }
            flag_data = defaults | flag_data
            if geo_location and (not flag_data['is_required_user'] or self.user):
                galleries_list.append(GalleriesType.SELECTED_FOR_YOU_GALLERY)
                galleries_kwargs['categories_ids'] = data['categories_ids']

                if data['categories_ids'] or (
                    self.user and self.user.customerfavoritecategory_set.exists()
                ):
                    self._update_gallery_order_experiment(
                        galleries_list,
                        self.fingerprint,
                        self.request.booking_source.name,
                    )

            # adding content
            types_len = len(GalleriesType)
            galleries = data['galleries'] or list(range(1, types_len + 1))
        else:
            # adding content
            galleries = data['galleries'] or list(range(1, 7))

        selected_galleries = galleries_generator.select_galleries(
            galleries_list,
            galleries,
        )
        business_galleries = {
            ii: gallery
            for g_indices, method in selected_galleries
            for ii, gallery in zip(
                g_indices, getattr(galleries_generator, method)(**galleries_kwargs)
            )
        }

        ret = {
            ii: gallery for ii, gallery in business_galleries.items() if ii in galleries and gallery
        }
        # endregion building response

        self.trigger_view_item_list_events(ret)

        for gallery in ret.values():
            if gallery.get('businesses'):
                gallery['promoted_labels'] = get_promoted_labels()

        self.finish_with_json(status.HTTP_200_OK, ret)

    @staticmethod
    def _update_gallery_order_experiment(
        galleries_list,
        fingerprint,
        booking_source,
    ):
        user_data = UserData(
            is_experiment=True,
            subject_key=fingerprint,
            subject_type=SubjectType.FINGERPRINT,
            app=CUSTOMER_BOOKING_SOURCE_TO_CLIENT_APP.get(booking_source, ClientApp.DEFAULT),
            app_domain=AppDomains.CUSTOMER,
        )
        if S4UGalleriesOrderExperiment(user_data) == ExperimentVariants.VARIANT_A:
            galleries_list.remove(GalleriesType.SELECTED_FOR_YOU_GALLERY)
            galleries_list.insert(0, GalleriesType.SELECTED_FOR_YOU_GALLERY)

    @staticmethod
    def _show_availability_galleries():
        return KillSwitch.alive(KillSwitch.System.AVAILABLE_TODAY)

    def get_excluded_business_ids(self):
        return [business['id'] for business in self.user_data.get('businesses', [])]

    def get_categories_for_user(self, categories_type):
        categories_to_add = []
        if self.user_data and categories_type:
            key = 'categories' if categories_type == BusinessCategory.CATEGORY else 'treatments'
            if is_in_experiment(self.user.id):
                key = f'{key}_utt'
            categories_to_add = list(filter(None, (elem for elem in self.user_data.get(key, []))))
        return categories_to_add

    def trigger_segment_analytics(self, geo_location: Dict[str, float], user_id: int = None):
        if geo_location is None:
            return

        if KillSwitch.alive(KillSwitch.MarTech.CUSTOMER_APP_OPENED):
            latitude = geo_location.get('lat')
            longitude = geo_location.get('lon')
            firebase_auth = self.firebase_auth_dict
            if (
                should_trigger_customer_event_by_geo(latitude, longitude, user_id=user_id)
                and firebase_auth
            ):
                analytics_customer_app_opened_task.delay(
                    user_id=self.user.id,
                    geo_location=geo_location,
                    auth_data=firebase_auth,
                    context={
                        'event_type': EventType.USER,
                        'session_user_id': self.user.id,
                        'source_id': self.booking_source.id,
                    },
                )

    def trigger_view_item_list_events(self, galleries_data):
        for gallery_data in galleries_data.values():
            self._trigger_view_item_list_event(
                item_list_id=gallery_data['type'],
                item_list_name=gallery_data['label'],
                items=[
                    {
                        'item_id': business['id'],
                        'index': index,
                        'creative_slot': str(business['meta']['score']),
                    }
                    for index, business in enumerate(gallery_data['businesses'])
                ],
            )


class BusinessesGalleryHandler(  # pylint: disable=too-many-ancestors
    ViewListItemEventMixin,
    AnalyticsTokensMixin,
    MyBooksy,
):
    booksy_teams = (BooksyTeams.CUSTOMER_SEARCH,)

    @session(optional_login=True)
    def get(self):
        """
        swagger:
            summary: Returns gallery of businesses.
            type: BusinessesGalleryResponse
            parameters:
                - name: location_geo
                  description:
                    latitude,longitude of a geo point where we look for
                    businesses. <br>
                    Ex. "40.75,-73.99" <br>
                  paramType: query
                  required: False
                  type: string
                - name: location_id
                  description: Region ID of businesses
                  paramType: query
                  required: False
                  type: integer
                - name: gender
                  description: gender of the anonymous user ('M'|'F'|'R')
                  paramType: query
                  type: string
                  enum:
                    - 'M'
                    - 'F'
                    - 'R'
                  required: False
                - name: category
                  description: Category ID of businesses
                  paramType: query
                  required: False
                  type: integer
                  allowMultiple: True
                - name: treatment
                  description: treatment Category ID of businesses
                  paramType: query
                  required: False
                  type: integer
                  allowMultiple: True
                - name: min_gallery_businesses
                  description:
                    Minimum businesses per gallery (default 2)
                  paramType: query
                  required: False
                  type: integer
                - name: max_gallery_businesses
                  description:
                    Maximum businesses per gallery (default 10)
                  paramType: query
                  required: False
                  type: integer
                - name: no_thumbs
                  type: boolean
                  paramType: query
                  required: False
                - name: listing_id
                  description: UUID 4 id of requested gallery
                  type: string
                  paramType: query
                  required: False
        :swagger

        swaggerModels:
           BusinessesGalleryResponse:
                id: BusinessesGalleryResponse
                properties:
                    businesses:
                        type: array
                        items:
                            type: BusinessesGalleryBusiness
           BusinessesGalleryBusiness:
                id: BusinessesGalleryBusiness
                description: Business object in gallery
                properties:
                    id:
                      type: integer
                      description: internal Business ID
                    name:
                      type: string
                      description: name of the Business
                    location:
                      type: BusinessLocation
                      description: location of a Business
                    active:
                        type: boolean
                        description: is Business active
                    visible:
                        type: boolean
                        description: is Business publicly visible
                    review_count:
                        type: integer, optional
                        description: number of reviews
                    reviews_stars:
                        type: number
                        description: avearage of star reviews of Business
                    images:
                        type: BusinessImages
                        description: a container for business images
                    primary_category:
                        type: integer
                        description: >
                          ID of primary category
                          (one of chosen ones by business)
                    reviews:
                        description: allways empty field
                        type: array
        :swaggerModels
        """
        args = self._prepare_get_arguments()
        if self.user and self.user.gender:
            args['gender'] = self.user.gender

        serializer = BusinessesGallerySerializer(data=args)
        data = self.validate_serializer(serializer)
        # TODO: Remove when UTT2 will be fully operational
        if self.user and is_in_experiment(self.user.id):
            apply_utt2_search_keys(data)

        galleries_generator = GalleriesGenerator(
            per_gallery_min=data.get('min_gallery_businesses'),
            per_gallery_max=data.get('max_gallery_businesses'),
            no_thumbs=data.pop('no_thumbs', False),
            additional_kwargs={'user': self.user},
        )
        if all(elem not in data for elem in ['location_geo', 'location_id']):
            location_geo = self.user_data.get('last_booking_location')
            if location_geo:
                data['location_geo'] = location_geo

        if self.api_key and self.api_key.lower().startswith('web'):
            label = _('Recommended')
            gallery_type = GalleriesType.RECOMMENDED
            searchable = business_searchables.RecommendedSearchBusinessSearchable
        else:
            now = tznow()
            end = now + timedelta(hours=72)
            available_for = f'{date_f(now)}-{date_f(end)}'

            data['available_for'] = parse_es_datetime_input(available_for)
            data['sort_order'] = 'score'

            label = _('Special Offers')
            gallery_type = GalleriesType.SPECIAL_OFFERS
            searchable = business_searchables.BusinessesGallerySearchable

        businesses = galleries_generator.get_businesses(
            label=label,
            gallery_type=gallery_type,
            searchable=searchable,
            data=data,
        )

        ret = businesses[0] if businesses else {}

        if ret.get('businesses'):
            ret['promoted_labels'] = get_promoted_labels()

        self.trigger_view_item_list_event(
            gallery_type=gallery_type,
            label=label,
            businesses=ret.get('businesses', []),
            listing_id=args.get('listing_id'),
        )
        self.finish_with_json(status.HTTP_200_OK, ret)

    def trigger_view_item_list_event(
        self, gallery_type, label, businesses, listing_id: Optional[str] = None
    ):
        extra_parameters = {}
        if listing_id is not None:
            extra_parameters['listing_id'] = listing_id

        self._trigger_view_item_list_event(
            item_list_id=f'Search_Gallery_{gallery_type.value}',
            item_list_name=label,
            items=[
                {
                    'item_id': business['id'],
                    'index': index,
                    'creative_slot': str(business['meta']['score']),
                }
                for index, business in enumerate(businesses)
            ],
            extra_parameters=extra_parameters,
            allow_empty=True,
        )


class GalleriesGenerator:
    serializer = BusinessMapHitSerializer
    personalized_galleries = {
        GalleriesType.NEAR_ME: True,
        GalleriesType.AVAILABLE_TODAY: True,
        GalleriesType.SPECIAL_OFFERS: False,
        GalleriesType.RECOMMENDED: False,
        GalleriesType.BOOKING_REACTIVATION: False,
        GalleriesType.NEW_ON_BOOKSY: False,
        GalleriesType.SELECTED_FOR_YOU_GALLERY: False,
    }
    personalized_galleries_method = {
        GalleriesType.NEAR_ME: 'get_near_me_businesses',
        GalleriesType.AVAILABLE_TODAY: 'get_available_today_businesses',
        GalleriesType.SPECIAL_OFFERS: 'get_special_offers_businesses',
        GalleriesType.RECOMMENDED: 'get_recommended_businesses',
        GalleriesType.BOOKING_REACTIVATION: ('get_booking_reactivation_businesses'),
        GalleriesType.NEW_ON_BOOKSY: 'get_new_on_booksy_businesses',
        GalleriesType.SELECTED_FOR_YOU_GALLERY: 'get_selected_for_you_businesses',
    }

    # pylint: disable=too-many-arguments, too-many-positional-arguments
    def __init__(
        self,
        per_gallery_min: int = MyBooksyGalleriesSerializer.PER_GALLERY_MIN,
        per_gallery_max: int = MyBooksyGalleriesSerializer.PER_GALLERY_MAX,
        user_categories: Iterable[int] = None,
        excluded_business_ids: Optional[Iterable[int]] = None,
        additional_kwargs: Optional[Dict[str, Any]] = None,
        content_type: str = None,
        no_thumbs: bool = False,
        experiment: bool = False,
    ):
        """
        Generator was separated to be easily used in multiple endpoints.

        :param per_gallery_min: minimal number of selected businesses
        :type per_gallery_min: int
        :param per_gallery_max: maximal number of selected businesses
        :type per_gallery_max: int
        :param user_categories: list of categories to for additional searches
        :type user_categories: list of integer
        :param excluded_business_ids: a list of businesses ids
            that will not appear in result list of businesses, defaults to None
        :type excluded_business_ids: list, optional
        :param content_type: flag for order of galleries
        :type content_type: str
        """
        self.per_gallery_min = per_gallery_min
        self.per_gallery_max = per_gallery_max
        self.user_categories = user_categories or []
        self._categories_num = len(self.user_categories) or 1
        self.excluded_business_ids = excluded_business_ids or []
        self.content_type = content_type
        self.no_thumbs = no_thumbs
        self.experiment = experiment
        self.additional_kwargs = additional_kwargs or {}
        self.user = self.additional_kwargs.get('user')

    def select_galleries(
        self, galleries_to_show: Iterable[GalleriesType], indices: Iterable[int]
    ) -> Iterable[Tuple[List[int], str]]:
        i = 1
        indices = set(indices)
        selected_galleries = []
        for gallery in galleries_to_show:
            _max = i + self._categories_num
            index = list(range(i, _max)) if self.personalized_galleries[gallery] else [i]
            i += len(index)
            if set(index) & indices:
                selected_galleries.append((index, self.personalized_galleries_method[gallery]))
        return selected_galleries

    # region galleries definition
    def get_near_me_businesses(
        self,
        geo_location: GeoLocationType,
        gender: str,
        **__,
    ) -> GalleryType:
        """Returns list of businesses in the area of given geo location."""
        if geo_location is None:
            return []

        data = {
            'gender': gender,
            'location_geo': geo_location,
            'distance_radius': ES_BASIC_DISTANCE,
        }

        return self.get_businesses(
            label=_('Near You'),
            gallery_type=GalleriesType.NEAR_ME,
            searchable=business_searchables.NearMeSearchable,
            data=data,
        )

    def get_selected_for_you_businesses(
        self,
        geo_location: GeoLocationType,
        gender: str,
        categories_ids: list[int],
        **__,
    ) -> GalleryType:

        if self.user:
            qset = self.user.customerfavoritecategory_set
            categories_ids = list(qset.values_list('category_id', flat=True)) or categories_ids

        if not categories_ids:
            return []

        flag_data = CustomerMyBooksySugestedForYouGalleryFlag()
        data = {
            'gender': gender,
            'location_geo': geo_location,
            'min_treatment_count': 1,
            'business_categories': categories_ids,
            'max_km_distance': flag_data.get('max_km_distance', 50),
            'max_active_days': flag_data.get('max_active_days', 300),
        }
        if businesses := self.get_businesses(
            label=_('For You'),
            gallery_type=GalleriesType.SELECTED_FOR_YOU_GALLERY,
            searchable=(
                SelectedForYouSearchableV2
                if SelectedForYouSearchableV2Flag()
                else SelectedForYouSearchable
            ),
            data=data,
        ):
            for business in businesses[0]['businesses']:
                business['is_selected_for_you'] = True
        else:
            logger.warning(
                'Empty response for seleted_4_you searchable in galleries',
                extra={'min_size': self.per_gallery_min, 'max_size': self.per_gallery_max} | data,
            )

        return businesses

    def get_available_today_businesses(
        self,
        geo_location: GeoLocationType,
        gender: str,
        **__,
    ) -> GalleryType:
        """Returns list of businesses in the area of given geo location."""
        if geo_location is None:
            return []

        label = _('Available today')
        now = tznow()
        if now.hour >= 21:
            now += timedelta(days=1)
            label = _('Available tomorrow')

        data = {
            'sort_order': 'score',
            'location_geo': geo_location,
            'available_for': parse_es_datetime_input(date_f(now)),
            'gender': gender,
        }

        return self.get_businesses(
            label=label,
            gallery_type=GalleriesType.AVAILABLE_TODAY,
            searchable=business_searchables.AvailableTodaySearchable,
            data=data,
        )

    def get_special_offers_businesses(
        self,
        geo_location: GeoLocationType,
        gender: str,
        **__,
    ) -> GalleryType:
        """Returns list of businesses with special offers."""
        now = tznow()
        end = now + timedelta(hours=72)
        available_for = f'{date_f(now)}-{date_f(end)}'

        data = {
            'gender': gender,
            'available_for': parse_es_datetime_input(available_for),
            'sort_order': 'score',
        }
        if geo_location:
            data['location_geo'] = geo_location

        return self.get_businesses(
            label=_('Special Offers'),
            gallery_type=GalleriesType.SPECIAL_OFFERS,
            searchable=business_searchables.ProfitablePromotionsSearchable,
            data=data,
        )

    def get_recommended_businesses(
        self,
        geo_location: GeoLocationType,
        gender: str,
        **__,
    ) -> GalleryType:
        """Returns list of businesses with special offers."""
        data = {
            'gender': gender,
            'sort_order': 'score',
        }
        if geo_location:
            data['location_geo'] = geo_location

        galleries_data = self.get_businesses(
            label=_('Recommended'),
            gallery_type=GalleriesType.RECOMMENDED,
            searchable=business_searchables.RecommendedSearchBusinessSearchable,
            data=data,
        )

        # region experiment
        if self.user and galleries_data and self.additional_kwargs.get('is_my_booksy_api'):
            new_label = RecommendedGalleryNameExperiment(self.user.id).get_experimental_value()
            if new_label is not None:
                for gallery_data in galleries_data:
                    gallery_data['label'] = new_label
        # endregion experiment

        return galleries_data

    def get_booking_reactivation_businesses(
        self,
        last_unfinished_booking_location_geo: GeoLocationType,
        last_unfinished_booking_treatment: List,
        last_unfinished_booking_available_for: Dict[str, Union[date, datetime]],
        **__,
    ) -> GalleryType:
        """Returns list of businesses with special offers."""
        data = {
            'location_geo': last_unfinished_booking_location_geo,
            'treatment': last_unfinished_booking_treatment,
            'available_for': last_unfinished_booking_available_for,
        }
        treatment_name = TreatmentCache.get_by_id(last_unfinished_booking_treatment[0])['full_name']

        return self.get_businesses(
            label=_('{treatment_name} available right away').format(treatment_name=treatment_name),
            gallery_type=GalleriesType.BOOKING_REACTIVATION,
            searchable=(business_searchables.BookingReactivationGallerySearchable),
            data=data,
        )

    def get_new_on_booksy_businesses(
        self,
        geo_location: Optional[GeoLocationType],
        gender: str,
        **__,
    ) -> GalleryType:
        galleries_data = []
        experiment = self.additional_kwargs.get('new_on_booksy_gallery_experiment')

        if geo_location is not None:
            data = {
                'location_geo': geo_location,
                'gender': gender,
                'sort_order': 'score',
            }

            if experiment:
                now = tznow()
                end = now + timedelta(days=experiment.parameters.available_in_next_x_days)
                available_for = f'{date_f(now)}-{date_f(end)}'

                data.update(
                    {
                        'active_for_max_x_days': experiment.parameters.active_for_max_x_days,
                        'excluded': experiment.parameters.excluded_business_ids,
                        'min_treatment_count': experiment.parameters.min_treatment_count,
                        'available_for': parse_es_datetime_input(available_for),
                    }
                )
                if experiment.parameters.only_boost is True:
                    data['only_boost'] = True

            galleries_data = self.get_businesses(
                label=_('New on Booksy'),
                gallery_type=GalleriesType.NEW_ON_BOOKSY,
                searchable=business_searchables.NewOnBooksySearchable,
                data=data,
            )

        if not galleries_data and experiment:
            experiment.record_gallery_not_generated()

        return galleries_data

    # endregion galleries definition

    # region gallery filling
    def get_businesses(
        self,
        label: str,
        gallery_type: GalleriesType,
        searchable: Type[Searchable],
        data: Optional[Dict] = None,
    ) -> GalleryType:
        """
        Search for businesses based on given query..

        :param label: title of gallery exposed to customer
        :type label: str
        :param gallery_type: describe booking source (for bookings started
            from that gallery) for measurements reasons
        :type gallery_type: service.customer.my_booksy.GalleriesType
        :param searchable: a searchable class object for businesses index
        :type searchable: lib.searchables.searchables.Searchable
        :param data: values for keys in query
        :type data: dict, optional
        :return: List of serialized businesses objects.
        :rtype: list
        """
        data = data or {}
        query_key = None
        if self.personalized_galleries[gallery_type] and self.user_categories:
            if self.content_type == BusinessCategory.CATEGORY:
                query_key = 'top_in_category'
                data['user_categories'] = self.user_categories
            elif self.content_type == BusinessCategory.TREATMENT:
                query_key = 'top_in_treatment'
                data['user_treatments'] = self.user_categories

        if self.excluded_business_ids:
            if 'excluded' in data:
                data['excluded'].extend(self.excluded_business_ids)
            else:
                data['excluded'] = self.excluded_business_ids

        if self.user:
            data['_user'] = self.user

        query_size = self.per_gallery_max

        response = []
        query = self._get_query(searchable, query_size, data)
        result = self._get_result(query, data, gallery_type)
        if result is not None:
            if self.personalized_galleries[gallery_type]:
                response = self.get_categorized_response(query, query_key, result)

            if not any(response) and result.hits.total.value >= self.per_gallery_min:
                response = [(None, result.hits)]
        return [
            (
                {
                    'businesses': businesses[1],
                    'label': self._get_gallery_name(label, businesses[0]),
                    'type': gallery_type.value,
                }
                if businesses
                else None
            )
            for businesses in response
        ]

    def _get_query(self, searchable, query_size, data):
        return (
            searchable(
                ESDocType.BUSINESS,
                serializer=self.serializer,
            )
            .params(
                size=query_size,
                no_thumbs=self.no_thumbs,
            )
            .search(data)
        )

    @staticmethod
    def _get_result(query, data, gallery_type):
        query._source = set(query._source)
        query._source.remove('*')
        query._source = list(query._source)
        try:
            result = query.execute()
        except ConnectionTimeout:
            logger.info('%s: %s', gallery_type, data)
            result = None
        return result

    def get_categorized_response(self, query, query_key, result):
        response = [None] * self._categories_num
        category_businesses = self._parse_categories_businesses(
            # pylint: disable=protected-access
            result,
            query_key,
            query._sort,
        )
        for i, c_id in enumerate(self.user_categories):
            if c_id in category_businesses:
                response[i] = (
                    self._get_category_name(c_id),
                    category_businesses[c_id],
                )
        return response

    def _parse_categories_businesses(
        self, res: AttrDict, key: str, query_sort: Optional[List] = None
    ) -> Dict[int, Dict]:
        category_businesses = {}
        if self.user_categories and hasattr(res, 'aggregations') and key in res.aggregations:
            serializer = self.serializer()
            for category_bucket in res.aggregations[key]['buckets']:
                if category_bucket['doc_count'] >= self.per_gallery_min:
                    category_businesses[category_bucket['key']] = SerializerResponse.hits_(
                        serializer, category_bucket['categories'].hits.to_dict(), query_sort or {}
                    )[1]
        return category_businesses

    def _get_category_name(self, category_id: int) -> str:
        function = (
            (
                get_category_utt_by_id
                if self.content_type == BusinessCategory.CATEGORY
                else get_treatment_utt_by_id
            )
            if self.experiment
            else BusinessCategoryCache.get_by_id
        )
        category = function(category_id)
        return category['name'] if category else None

    @staticmethod
    def _get_gallery_name(label: str, category_name: str) -> str:
        return f'{category_name} {label.lower()}' if category_name is not None else label

    # endregion gallery filling
