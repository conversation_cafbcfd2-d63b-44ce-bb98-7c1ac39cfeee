from datetime import <PERSON><PERSON><PERSON>

import mock
import pytest
from model_bakery import baker

from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.tools import get_by_id
from lib.test_utils import create_subbooking
from lib.tools import tznow
from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import Appointment
from webapps.business.baker_recipes import (
    bci_recipe,
    business_recipe,
    category_recipe,
    resource_recipe,
    service_category_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
    treatment_recipe,
)
from webapps.images.models import Image
from webapps.reviews.models import Review
from webapps.search_engine_tuning.models import UserTuning
from webapps.user.enums import AuthOriginEnum


@pytest.fixture(scope='function')
def disable_annoying_task_or_signals_():
    """overwrite fixture which disabled assign_treatments_task"""


@pytest.mark.django_db
@mock.patch(
    'service.customer.my_booksy.is_in_experiment',
    lambda *a, **kw: False,
)
class MyBooksyHandlerTestVisitedCase(BaseAsyncHTTPTest):
    url = '/customer_api/my_booksy/'

    def setUp(self):
        super().setUp()
        self.category = category_recipe.make()
        self._prepare_business(self.business)
        self.bci = bci_recipe.make(business=self.business)

    def _prepare_business(self, business, promoted=False):
        business.active = True
        business.visible = True
        business.categories.add(self.category)
        business.primary_category = self.category
        business.save()
        if promoted:
            type(business).objects.filter(id=business.id).update(
                boost_status=business.BoostStatus.ENABLED
            )
            business.refresh_from_db()
            assert (
                business.boosted == promoted
            ), "The logic in the save method can overwrite boost_status"

        service = service_recipe.make(
            business=business,
            active=True,
            is_available_for_customer_booking=True,
            treatment=treatment_recipe.make(parent=self.category),
            service_category=service_category_recipe.make(business=business),
        )
        service_variant_recipe.make(
            service=service,
            active=True,
        )
        # add staffer to assure that business visible in elasticsearch
        service.add_staffers([staffer_recipe.make(business=business)])
        service.save()

        # add one image that business is searchable see
        # business.BusinessTermsWithSingleImagesSearchable
        # in service/customer/my_booksy.py:415
        baker.make(
            Image,
            business=business,
            is_cover_photo=True,
        )
        resource_recipe.make(business=business)
        business.reindex(refresh_index=True)

    def _create_booking(self, bci, delta=None, service_variant_id=None):
        now = tznow()
        if delta is None:
            delta = timedelta(days=3)
        booking, *_ = create_subbooking(
            business=bci.business,
            booking_kws=dict(
                booked_for=bci,
                status=Appointment.STATUS.FINISHED,
                source=self.customer_booking_src,
                booked_from=now - delta - timedelta(minutes=60),
                booked_till=now - delta,
                autoassign=True,
                updated_by=self.user,
            ),
        )
        if service_variant_id is not None:
            type(booking).objects.filter(id=booking.id).update(
                service_variant_id=service_variant_id
            )
        return booking

    def _get_response(self, user):
        # ensure that business is visible
        doc = get_by_id(self.business.id, ESDocType.BUSINESS)
        assert doc is not None
        # mock property _is_new_user to not create UserTuning
        # fetch response
        with mock.patch(
            'service.customer.my_booksy.MyBooksy._is_new_user',
            return_value=False,
        ):
            self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
            response = self.fetch(self.url)
        assert response.code == 200
        return response

    def _create_review(self, booking, rank):
        baker.make(
            Review,
            user=self.bci.user,
            business=self.business,
            subbooking=booking,
            rank=rank,
        )
        self.business.reviews_rank_avg = 4
        self.business.save()

    def _add_negative_review(self):
        booking = self._create_booking(self.bci)
        self._create_review(booking, 1)
        response = self._get_response(self.bci.user)
        visited = response.json.get('favorites_visited')
        assert visited is not None
        # asert business with  bad review is not response
        assert len(visited) == 0

    def test_add_negative_review(self):
        self._add_negative_review()

    def test_add_negative_then_positive(self):
        self._add_negative_review()
        # add positive review
        user = self.bci.user
        booking = self._create_booking(self.bci, timedelta(days=1))
        self._create_review(booking, 5)
        # get response
        response = self._get_response(user)
        visited = response.json.get('favorites_visited')
        assert len(visited) == 1
        assert visited[0]['business']['id'] == self.business.id

    def test_selected_for_you_with_appointment(self):
        user = self.bci.user
        bci_2 = bci_recipe.make(user=user)
        self._create_booking(self.bci, timedelta(days=2))
        business_2 = bci_2.business
        self._prepare_business(business_2)
        service_variant_id = business_2.services.last().service_variants.values_list(
            'id', flat=True
        )[0]
        self._create_booking(bci_2, timedelta(days=1), service_variant_id=service_variant_id)
        self._set_user_data_in_elastic(user)
        business_selected4u = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_selected4u)

        response = self._get_response(user)

        visited = response.json['favorites_visited']
        assert len(visited) == 3
        assert visited[0]['business'].get('is_selected_for_you') is None
        assert {visited[0]['business']['id'], visited[2]['business']['id']} == {
            self.business.id,
            business_2.id,
        }
        assert visited[1]['business']['id'] == business_selected4u.id
        assert visited[1]['business']['is_selected_for_you'] is True

    def test_selected_for_you_only_liked(self):
        user = self.bci.user
        bci_2 = bci_recipe.make(user=user, bookmarked=True)
        business_2 = bci_2.business
        self._prepare_business(business_2)
        business_selected4u = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_selected4u)
        self._set_user_data_in_elastic(user)
        lat, lon = business_selected4u.latitude, business_selected4u.longitude
        self.url += f'?geo_location={lat},{lon}'

        response = self._get_response(user)

        visited = response.json['favorites_visited']
        assert len(visited) == 2
        assert visited[0]['business'].get('is_selected_for_you') is None
        assert visited[0]['business']['id'] == business_2.id
        assert visited[1]['business']['id'] == business_selected4u.id
        assert visited[1]['business']['is_selected_for_you'] is True

    def test_selected_for_you_only_liked_with_boost_business(self):
        user = self.bci.user
        bci_2 = bci_recipe.make(user=user, bookmarked=True)
        business_2 = bci_2.business
        self._prepare_business(business_2, promoted=True)
        business_selected4u = business_recipe.make(
            active_from=tznow(),
        )
        self._prepare_business(business_selected4u, promoted=True)
        self._set_user_data_in_elastic(user)
        lat, lon = business_selected4u.latitude, business_selected4u.longitude
        self.url += f'?geo_location={lat},{lon}'

        response = self._get_response(user)

        visited = response.json['favorites_visited']
        assert len(visited) == 1  # boost business is not allowed
        assert visited[0]['business'].get('is_selected_for_you') is None
        assert visited[0]['business']['id'] == business_2.id

    @staticmethod
    def _set_user_data_in_elastic(user):
        UserTuning.update_multiple_tunings([user.id])
        user.reindex(refresh_index=True)
