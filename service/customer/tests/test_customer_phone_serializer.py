import pytest

from lib.tests.test_booksy_sms_whitelist import PHONE_NUMBERS
from service.customer.serializers import BooksyCustomerRegistrationPhoneSerializer


@pytest.mark.django_db
@pytest.mark.parametrize(
    'phone_number, result',
    [
        (PHONE_NUMBERS['polish'], True),
        (PHONE_NUMBERS['japanese'], True),
        (PHONE_NUMBERS['ugandan'], False),
        ('not a number', False),
    ],
)
def test_customer_registration_phone_serializer_error_message(phone_number, result):
    serializer = BooksyCustomerRegistrationPhoneSerializer(
        data={'cell_phone': phone_number},
        context={'abuse_fingerprint': 'foo'},
    )
    assert serializer.is_valid() == result
