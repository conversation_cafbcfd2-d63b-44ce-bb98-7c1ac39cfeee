import pytest
from mock import patch


@pytest.fixture(scope='module', autouse=True)
def disable_apple_tasks_fixture():
    targets = [
        'webapps.user.tasks.apple.authorize_apple_user_task',
    ]
    patchers = []
    for target in targets:
        patcher = patch(
            target=target,
        )
        patchers.append(patcher)
        patcher.start()
    yield
    for patcher in patchers:
        patcher.stop()
