import uuid
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from unittest.mock import Mock, patch

import pytest
from django.conf import settings
from django.core import mail
from django.test import override_settings
from django.utils.translation import gettext as _
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status

from country_config import Country
from lib import jinja_renderer
from lib.feature_flag.feature.booksy_pay import (
    BooksyPayRefundShortStatusFlag,
)
from lib.french_certification.entities import FiscalReceiptEntity
from lib.test_utils import create_test_reciever
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from service.pos.tests.common import POSTestsMixin
from service.tests import BaseAsyncHTTPTest
from settings.es_countries.languages import ENGLISH
from webapps.booking.enums import AppointmentStatus
from webapps.booking.models import Appointment
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.booking.tests.utils import create_appointment
from webapps.invoicing.tests.common import french_certification_enabled
from webapps.notification.models import NotificationHistory
from webapps.notification.models import UserNotification
from webapps.pos.baker_recipes import payment_type_recipe
from webapps.pos.enums import (
    PaymentTypeEnum,
    receipt_status,
)
from webapps.pos.models import (
    POS,
    PaymentRow,
    PaymentType,
    Receipt,
    TaxRate,
    Transaction,
    TransactionRow,
    TransactionTip,
)
from webapps.pos.tip_calculations import SimpleTip
from webapps.pos.tools import get_receipt_data
from webapps.structure.models import Region
from webapps.user.models import User, UserProfile
from webapps.voucher.enums import VoucherType
from webapps.voucher.models import Voucher, VoucherTemplate


@pytest.mark.django_db
class CustomerTransactionHandlerTestCase(
    POSTestsMixin,
    BaseAsyncHTTPTest,
):
    def test_list_get_with_filtering(self):
        self._create_transactions_and_receipts(
            [
                # should be returned by status_type filter = call_for_payment_actions
                receipt_status.CALL_FOR_PAYMENT,
                receipt_status.CALL_FOR_PREPAYMENT,
                receipt_status.CALL_FOR_DEPOSIT,
                # not included by status_type filter = call_for_payment_actions
                receipt_status.PAYMENT_SUCCESS,
                receipt_status.DEPOSIT_AUTHORISATION_AWAITING,
            ]
        )

        url = '/customer_api/me/transactions/?stardust=true&status_type=call_for_payment_actions'
        resp = self.fetch(
            url,
            method='GET',
        )

        assert resp.code == 200
        assert resp.json['count'] == 3

    def _create_transactions_and_receipts(self, receipt_statuses):
        operator = baker.make(User)
        pos = self.create_pos(self.business)
        bci = self.create_business_customer_info(self.business)
        for receipt_status_code in receipt_statuses:
            txn = baker.make(
                Transaction,
                customer_card=bci,
                pos=pos,
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
                operator=operator,
                customer=self.user,
                total=123.45,
            )
            receipt = baker.make(
                Receipt,
                transaction=txn,
                status_code=receipt_status_code,
                already_paid=123.45,
            )
            txn.latest_receipt = receipt
            txn.save()


@pytest.mark.django_db
class CustomerTransactionDetailsHandlerTestCase(POSTestsMixin, BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        self.user.email = '<EMAIL>'
        self.user.cell_phone = '+***********'
        self.user.save()
        self.region = baker.make(Region, type='zip', name='B')
        baker.make(
            BusinessCustomerInfo,
            user=self.user,
        )
        baker.make(
            UserProfile,
            profile_type=UserProfile.Type.CUSTOMER,
            user=self.user,
        )

    @staticmethod
    def get_path_transaction_details(transaction_id: int) -> str:
        return f'/customer_api/me/transactions/{transaction_id}/'

    def test_list_get(self):
        operator = baker.make(User)

        pos = self.create_pos(self.business)
        self.create_default_tax_rate_20(pos)
        _, service_variant = self.create_service_and_service_variant(
            self.business,
        )
        bci = self.create_business_customer_info(self.business)
        self.create_cash_payment_type(pos)

        txn = baker.make(
            Transaction,
            customer_card=bci,
            pos=pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            operator=operator,
            customer=self.user,
            total=123.45,
        )
        baker.make(
            TransactionTip,
            transaction=txn,
            amount=0,
        )
        receipt = baker.make(
            Receipt,
            transaction=txn,
            status_code=receipt_status.CALL_FOR_PAYMENT,
            already_paid=123.45,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            TransactionRow,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
            transaction=txn,
            service_variant=service_variant,
        )

        url = '/customer_api/me/transactions/'
        resp = self.fetch(
            url,
            method='GET',
        )

        assert resp.code == 200

    def test__get(self):
        operator = baker.make(User)

        pos = self.create_pos(self.business)
        self.create_default_tax_rate_20(pos)
        _, service_variant = self.create_service_and_service_variant(
            self.business,
        )
        bci = self.create_business_customer_info(self.business)
        self.create_cash_payment_type(pos)

        txn = baker.make(
            Transaction,
            customer_card=bci,
            pos=pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            operator=operator,
            customer=self.user,
            total=123.45,
        )
        baker.make(
            TransactionTip,
            transaction=txn,
            amount=0,
        )
        receipt = baker.make(
            Receipt,
            transaction=txn,
            status_code=receipt_status.CALL_FOR_PAYMENT,
            already_paid=123.45,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            TransactionRow,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
            transaction=txn,
            service_variant=service_variant,
        )

        url = self.get_path_transaction_details(txn.id)
        resp = self.fetch(url, method='GET')

        assert resp.code == 200
        assert resp.json['transaction_merchant_account'] == settings.ADYEN_MERCHANT_ACCOUNT

    @french_certification_enabled()
    @patch('webapps.pos.adapters.FiscalReceiptAdapter', autospec=True)
    def test_transaction_has_fiscal_receipt_data(self, fiscal_receipt_mock):
        mocked_receipt_number = ************
        fiscal_receipt_mock.last_fiscal_receipt_by_basket_id = Mock(
            return_value=FiscalReceiptEntity(
                basket_id=uuid.uuid4(),
                type='',
                business_id=-1,
                number=mocked_receipt_number,
            )
        )
        txn = self._create_transaction()
        url = self.get_path_transaction_details(txn.id)

        resp = self.fetch(url, method='GET')

        assert resp.code == 200
        transaction_data = resp.json['transaction']
        receipt_data = transaction_data['receipts'][0]
        assert transaction_data['is_id_hidden'] is True
        assert receipt_data['disclaimer'] == _(
            'The above confirmation of sale is not a fiscal receipt.'
        )
        assert receipt_data['receipt_number'] == str(mocked_receipt_number)
        assert receipt_data['id'] is None
        assert receipt_data['is_send_email_hidden'] is True

    def test_get_force_stripe_pba(self):
        operator = baker.make(User)

        pos = self.create_pos(self.business)
        pos.pos_refactor_stage2_enabled = True
        pos._force_stripe_pba = True  # pylint: disable=protected-access
        pos.save()

        self.create_default_tax_rate_20(pos)
        _, service_variant = self.create_service_and_service_variant(
            self.business,
        )
        bci = self.create_business_customer_info(self.business)
        self.create_cash_payment_type(pos)

        txn = baker.make(
            Transaction,
            customer_card=bci,
            pos=pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            operator=operator,
            customer=self.user,
            total=123.45,
        )
        baker.make(
            TransactionTip,
            transaction=txn,
            amount=0,
        )
        receipt = baker.make(
            Receipt,
            transaction=txn,
            status_code=receipt_status.CALL_FOR_PAYMENT,
            already_paid=123.45,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            TransactionRow,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
            transaction=txn,
            service_variant=service_variant,
        )

        url = self.get_path_transaction_details(txn.id)
        resp = self.fetch(url, method='GET')

        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertEqual(resp.json['transaction_merchant_account'], settings.ADYEN_MERCHANT_ACCOUNT)
        self.assertTrue(resp.json['transaction']['force_stripe_pba'])

    def test_get_stardust_hack(self):
        operator = baker.make(User)

        pos = self.create_pos(self.business)
        self.create_default_tax_rate_20(pos)
        _, service_variant = self.create_service_and_service_variant(
            self.business,
        )
        bci = self.create_business_customer_info(self.business)
        pba = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=pos)

        txn = baker.make(
            Transaction,
            customer_card=bci,
            pos=pos,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            operator=operator,
            customer=self.user,
            total=123.45,
        )
        receipt = baker.make(
            Receipt,
            transaction=txn,
            status_code=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            already_paid=0,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            receipt=receipt,
            status=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            amount=100.00,
            payment_type=pba,
        )

        baker.make(
            TransactionRow,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
            transaction=txn,
            service_variant=service_variant,
        )

        url = self.get_path_transaction_details(txn.id)
        resp = self.fetch(url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['transaction']['receipts'][0]['already_paid'] == '$100.00'

    def test_get_receipt_for_pending_prepayment(self):
        operator = baker.make(User)

        pos = self.create_pos(self.business)
        self.create_default_tax_rate_20(pos)
        _, service_variant = self.create_service_and_service_variant(
            self.business,
        )
        bci = self.create_business_customer_info(self.business)
        pba = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=pos)

        appointment = baker.make(
            Appointment,
            business=self.business,
            booked_for=bci,
            status=Appointment.STATUS.PENDING_PAYMENT,
        )
        txn = baker.make(
            Transaction,
            customer_card=bci,
            pos=pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            operator=operator,
            customer=self.user,
            total=123.45,
            appointment=appointment,
        )
        receipt = baker.make(
            Receipt,
            transaction=txn,
            status_code=receipt_status.CALL_FOR_PREPAYMENT,
            already_paid=0,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            receipt=receipt,
            status=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            amount=100.00,
            payment_type=pba,
        )

        baker.make(
            TransactionRow,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
            transaction=txn,
            service_variant=service_variant,
        )

        url = self.get_path_transaction_details(txn.id)
        resp = self.fetch(url, method='GET')

        assert resp.code == status.HTTP_200_OK
        assert len(resp.json['transaction']['receipts']) == 1

        appointment.status = Appointment.STATUS.CANCELED
        appointment.save()
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert len(resp.json['transaction']['receipts']) == 0

    def _create_transaction(self):
        operator = baker.make(User)

        pos = self.create_pos(self.business)
        self.create_default_tax_rate_20(pos)
        _, service_variant = self.create_service_and_service_variant(
            self.business,
        )
        bci = self.create_business_customer_info(self.business)
        self.create_cash_payment_type(pos)

        txn = baker.make(
            Transaction,
            customer_card=bci,
            pos=pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            operator=operator,
            customer=self.user,
            total=123.45,
        )
        baker.make(
            TransactionTip,
            transaction=txn,
            amount=0,
        )
        receipt = baker.make(
            Receipt,
            transaction=txn,
            status_code=receipt_status.CALL_FOR_PAYMENT,
            already_paid=123.45,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            TransactionRow,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
            transaction=txn,
            service_variant=service_variant,
        )
        return txn


@pytest.mark.django_db
class CustomerTransactionLastReceiptHandlerTestCase(POSTestsMixin, BaseAsyncHTTPTest):

    def setUp(self):
        super().setUp()
        self.user.email = '<EMAIL>'
        self.user.cell_phone = '+***********'
        self.user.save()
        self.bci = baker.make(BusinessCustomerInfo, business=self.business, user=self.user)

        self.region = baker.make(Region, type='zip', name='B')
        baker.make(
            BusinessCustomerInfo,
            user=self.user,
        )
        baker.make(
            UserProfile,
            profile_type=UserProfile.Type.CUSTOMER,
            user=self.user,
        )

        self.pos = self.create_pos(self.business)
        self.create_default_tax_rate_20(self.pos)
        _, self.service_variant = self.create_service_and_service_variant(
            self.business,
        )
        self.create_cash_payment_type(self.pos)
        self.appointment = create_appointment(
            business=self.business,
            booked_for=self.bci,
        )
        self.txn = baker.make(
            Transaction,
            appointment=self.appointment,
            customer_card=self.create_business_customer_info(self.business),
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            operator=baker.make(User),
            customer=self.user,
            total=123.45,
        )
        baker.make(
            TransactionTip,
            transaction=self.txn,
            amount=0,
        )
        self.receipt = baker.make(
            Receipt,
            transaction=self.txn,
            status_code=receipt_status.PAYMENT_SUCCESS,
            already_paid=123.45,
        )
        self.txn.latest_receipt = self.receipt
        self.txn.save()
        baker.make(
            TransactionRow,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
            transaction=self.txn,
            service_variant=self.service_variant,
        )
        self.url = self.get_last_receipt_url(self.txn.id)

    @staticmethod
    def _assert_contains_expected_keys(response_keys):
        expected_keys = {
            'id',
            'status_type',
            'created',
            'payment_type',
            'payment_rows',
            'remaining_unformatted',
            'total',
            'already_paid',
            'note',
            'receipt_number',
            'status_code',
            'card_type',
            'card_last_digits',
            'pnref',
            'provider',
            'short_status',
            'short_status_label',
            'short_status_description',
        }
        assert expected_keys.issubset(response_keys)

    @staticmethod
    def get_last_receipt_url(transaction_id: int) -> str:
        return f'/customer_api/me/transactions/{transaction_id}/last_receipt'

    def test_get_last_receipt(self):
        resp = self.fetch(self.url, method='GET')

        assert resp.code == 200
        receipt_keys = resp.json['receipt'].keys()
        self._assert_contains_expected_keys(receipt_keys)

    @french_certification_enabled()
    @patch('webapps.pos.adapters.FiscalReceiptAdapter', autospec=True)
    def test_get_last_receipt_french_certification(self, fiscal_receipt_mock):
        mocked_receipt_number = ************
        self.receipt.receipt_number = mocked_receipt_number
        fiscal_receipt_mock.last_fiscal_receipt_by_basket_id = Mock(
            return_value=FiscalReceiptEntity(
                basket_id=uuid.uuid4(),
                type='',
                business_id=-1,
                number=mocked_receipt_number,
            )
        )

        resp = self.fetch(self.url, method='GET')

        assert resp.code == 200
        receipt_data = resp.json['receipt']
        self._assert_contains_expected_keys(receipt_data.keys())
        assert receipt_data['disclaimer'] == _(
            'The above confirmation of sale is not a fiscal receipt.'
        )
        assert receipt_data['receipt_number'] == str(mocked_receipt_number)
        assert receipt_data['id'] is None
        assert receipt_data['is_send_email_hidden'] is True

    @parameterized.expand(
        [
            (True, 'refund_requested', tznow()),
            (True, 'refund_overdue', tznow() + timedelta(days=31)),
            (False, 'success', tznow()),
            (False, 'success', tznow() + timedelta(days=31)),
        ],
    )
    def test_refund_statuses(self, flag_value, expected_short_status, now):
        bp_payment_type = payment_type_recipe.make(
            code=PaymentTypeEnum.BOOKSY_PAY,
            pos=self.pos,
        )
        receipt = baker.make(
            Receipt,
            payment_type=bp_payment_type,
            transaction=self.txn,
            status_code=receipt_status.PAYMENT_SUCCESS,
        )
        self.txn.latest_receipt = receipt
        self.txn.save()

        self.appointment.status = AppointmentStatus.CANCELED
        self.appointment.updated_by = self.bci.user
        self.appointment.save()

        with (
            override_eppo_feature_flag({BooksyPayRefundShortStatusFlag.flag_name: flag_value}),
            freeze_time(now),
        ):
            resp = self.fetch(self.url, method='GET')

        assert resp.code == 200
        receipt = resp.json['receipt']
        self._assert_contains_expected_keys(receipt.keys())
        assert receipt['short_status'] == expected_short_status


@pytest.mark.django_db
class CustomerTransactionSendReceiptHandler(POSTestsMixin, BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        self.user.email = '<EMAIL>'
        self.user.cell_phone = '+***********'
        self.user.save()
        self.region = baker.make(Region, type='zip', name='B')
        baker.make(
            BusinessCustomerInfo,
            user=self.user,
        )
        baker.make(
            UserProfile,
            profile_type=UserProfile.Type.CUSTOMER,
            user=self.user,
        )

        operator = baker.make(User)

        self.pos = baker.make(
            POS,
            business=self.business,
            active=True,
            commissions_enabled=True,
            tips_enabled=True,
        )
        baker.make(TaxRate, pos=self.pos, default_for_service=True, rate=20)
        _, service_variant = self.create_service_and_service_variant(
            self.business,
        )
        bci = self.create_business_customer_info(self.business)
        self.pba = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=self.pos)

        self.txn = baker.make(
            Transaction,
            customer_card=bci,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            operator=operator,
            customer=self.user,
            subtotal=Decimal('100'),
            total=Decimal('123.45'),
            taxed_subtotal_services=Decimal('60'),
            taxed_subtotal_products=Decimal('62.45'),
            discounted_subtotal_services=Decimal('108'),
            discounted_subtotal_products=Decimal('127'),
            subtotal_services=Decimal('108'),
            subtotal_products=Decimal('127'),
        )
        baker.make(
            TransactionTip,
            transaction=self.txn,
            amount=Decimal('0'),
            rate=Decimal('0'),
            type=SimpleTip.TIP_TYPE__PERCENT,
        )
        receipt = baker.make(
            Receipt,
            transaction=self.txn,
            status_code=receipt_status.CALL_FOR_PAYMENT,
            payment_type=self.pba,
            already_paid=Decimal('0'),
        )
        self.txn.latest_receipt = receipt
        self.txn.save()

        payment_row = PaymentRow.create_with_status(
            receipt=receipt,
            status=receipt_status.CALL_FOR_PAYMENT,
            payment_type=self.pba,
            amount=Decimal('123.45'),
        )
        payment_row.save()

        self.transaction_row = baker.make(
            TransactionRow,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
            transaction=self.txn,
            service_variant=service_variant,
        )

    def test_post(self):
        create_test_reciever(
            self.user.id,
            token='<EMAIL>',
            reciever_type=UserNotification.EMAIL_NOTIFICATION,
            profile_type=UserProfile.Type.BUSINESS,
            business=self.business,
        )
        url = f'/customer_api/me/transactions/{self.txn.id}/send_receipt/'

        body = {'email': '<EMAIL>'}

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 200
        assert (
            mail.outbox[0].from_email
            == f'"{self.txn.pos.business.name}" <{settings.NO_REPLY_EMAIL}>'
        )
        assert mail.outbox[0].reply_to == [settings.NO_REPLY_EMAIL]

    def test_generating_email_body(self):
        template_args, _receipt_number = get_receipt_data(
            self.pos, self.txn, NotificationHistory.SENDER_BUSINESS, ENGLISH
        )

        sjr = jinja_renderer.ScenariosJinjaRenderer()
        body = sjr.render(
            scenario_name='receipt',
            template_name='receipt',
            language=ENGLISH,
            template_args=template_args,
            extension='html',
            default=(),
        )

        assert body

    @parameterized.expand(
        [
            (Country.PL,),
            (Country.US,),
            (Country.GB,),
            (Country.FR,),
        ]
    )
    def test_get_receipt_data_tax_part_service_payment_confirmation(
        self,
        country,
    ):
        with override_settings(API_COUNTRY=country):
            template_args, _ = get_receipt_data(
                self.pos, self.txn, NotificationHistory.SENDER_BUSINESS, ENGLISH
            )

        assert template_args['show_taxes_info'] is True

    @parameterized.expand(
        [
            (Country.PL, VoucherType.MEMBERSHIP, True),
            (Country.US, VoucherType.MEMBERSHIP, True),
            (Country.GB, VoucherType.MEMBERSHIP, True),
            (Country.FR, VoucherType.MEMBERSHIP, True),
            (Country.PL, VoucherType.PACKAGE, True),
            (Country.US, VoucherType.PACKAGE, True),
            (Country.GB, VoucherType.PACKAGE, True),
            (Country.FR, VoucherType.PACKAGE, True),
            (Country.PL, VoucherType.EGIFT_CARD, True),
            (Country.US, VoucherType.EGIFT_CARD, True),
            (Country.GB, VoucherType.EGIFT_CARD, True),
            (Country.FR, VoucherType.EGIFT_CARD, False),
        ]
    )
    def test_get_receipt_data_remove_tax_part_from_gc_confirmation_migration_mode(
        self,
        country,
        voucher_type,
        tax_info_included_expected,
    ):
        voucher_template = baker.make(VoucherTemplate, pos=self.pos, type=voucher_type)
        voucher = baker.make(
            Voucher,
            pos=self.pos,
            voucher_template=voucher_template,
        )

        self.transaction_row.type = TransactionRow.TRANSACTION_ROW_TYPE__VOUCHER
        self.transaction_row.voucher = voucher
        self.transaction_row.save()

        with override_settings(API_COUNTRY=country):
            template_args, _ = get_receipt_data(
                self.pos, self.txn, NotificationHistory.SENDER_BUSINESS, ENGLISH
            )

        assert template_args['show_taxes_info'] is tax_info_included_expected
