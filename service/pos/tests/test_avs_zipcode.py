from dataclasses import asdict, dataclass

import pytest
from django.test import override_settings
from model_bakery import baker
from rest_framework.exceptions import ValidationError

from service.tests import BaseAsyncHTTPTest
from webapps.adyen.models import (
    Cardholder,
)
from webapps.pos.serializers import PaymentMethodCreateSerializer


@dataclass
class BillingAddress:
    # Adyen sends notification with fields written in camelCase and this
    # dataclass serves as a wrapper for them. That's why we have to disable
    # pylint here.
    # pylint: disable=invalid-name
    postalCode: str
    country: str = 'ZZ'
    city: str = 'NA'
    houseNumberOrName: str = 'NA'
    street: str = 'NA'
    stateOrProvince: str = 'NA'
    # pylint: enable=invalid-name


class AVSZipCodeCheckTestCase(BaseAsyncHTTPTest):
    """
    Test cases whether system properly rejects payment methods without properly
    formatted zip codes.

    The current implementation should:
    * reject any new payment card that is added with filled in `billing_address`
    argument, but with `postalCode` not matching our zip code regex
    * accept any card that has properly filled in `billing_address` and
    `postalCode` values
    * accept any card that has no `billing_address` associated with it
    """

    @pytest.mark.django_db
    @override_settings(
        POS__DEFAULT_PAYMENT_PROVIDER='adyen_ee',
        MARKET_PAY_AVS_ENABLED=True,
        AVS_ZIPCODE_REGEXP=r'[0-9]{5}',
    )
    def test_wrong_zip_code_provided(self):
        baker.make(
            Cardholder,
            shopper_reference='default_reference',
            email=self.user.email,
        )
        try:
            PaymentMethodCreateSerializer().validate(
                dict(
                    encrypted_data='Some encrypted data, not parsed by us',
                    billing_address=asdict(BillingAddress(postalCode='1234')),
                )
            )
        except ValidationError:
            pass
        else:
            raise Exception('Should raise an error when validating zip code.')

    @pytest.mark.django_db
    @override_settings(
        POS__DEFAULT_PAYMENT_PROVIDER='adyen_ee',
        MARKET_PAY_AVS_ENABLED=True,
        AVS_ZIPCODE_REGEXP=r'[0-9]{5}',
    )
    def test_accepted_without_billing_address(self):
        baker.make(
            Cardholder,
            shopper_reference='default_reference',
            email=self.user.email,
        )
        try:
            PaymentMethodCreateSerializer(context={'user': self.user}).validate(
                dict(
                    encrypted_data='Some encrypted data, not parsed by us',
                )
            )
        except ValidationError:
            raise Exception('Should allow empty billing address')
        else:
            pass

    @pytest.mark.django_db
    @override_settings(
        POS__DEFAULT_PAYMENT_PROVIDER='adyen_ee',
        MARKET_PAY_AVS_ENABLED=True,
        AVS_ZIPCODE_REGEXP=r'[0-9]{5}',
    )
    def test_accepted_good_zip_code(self):
        baker.make(
            Cardholder,
            shopper_reference='default_reference',
            email=self.user.email,
        )
        try:
            PaymentMethodCreateSerializer(context={'user': self.user}).validate(
                dict(
                    encrypted_data='Some encrypted data, not parsed by us',
                    billing_address=asdict(BillingAddress(postalCode='12345')),
                )
            )
        except ValidationError:
            raise Exception('Should allow proper zip code')
        else:
            pass
