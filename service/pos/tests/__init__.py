from model_bakery import baker

from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import (
    PaymentType,
    Receipt,
    Transaction,
    PaymentRow,
    TransactionTip,
)
from webapps.pos.tip_calculations import SimpleTip


def create_receipt(
    pos,
    booking,
    status,
    txn_type=Transaction.TRANSACTION_TYPE__PAYMENT,
    payment_type_code=PaymentTypeEnum.CASH,
):
    """
    Create transaction and receipt for given booking

    Args:
        pos: pos object
        booking: booking object
        status: receipt's status
        txn_type: transaction type
        payment_type_code: payment type code
    """
    txn = booking.appointment.transactions.last()
    if not txn:
        txn = baker.make(
            Transaction,
            appointment_id=booking.appointment_id,
            transaction_type=txn_type,
            pos=pos,
            total=99.99,
        )
        baker.make(
            TransactionTip,
            rate=0,
            amount=0,
            type=SimpleTip.TIP_TYPE__PERCENT,
            transaction=txn,
        )

    payment_type = PaymentType.objects.get_or_create(
        code=payment_type_code,
        pos=pos,
    )[0]

    receipt = baker.make(
        Receipt,
        status_code=status,
        transaction=txn,
        payment_type=payment_type,
        already_paid=99.99,
    )
    txn.latest_receipt = receipt
    txn.save()

    pr = PaymentRow.create_with_status(
        receipt=receipt,
        payment_type=payment_type,
        amount=txn.total,
    )
    pr.save()

    return txn
