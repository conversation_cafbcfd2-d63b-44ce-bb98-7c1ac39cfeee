#!/usr/bin/env python
from django.conf import settings
from django.utils.translation import gettext as _

import lib.tools


class PaginatorMixin:
    """DEPRECATED use PaginatorSerializer.
    Mix it with RequestHandler. Handles parsing of
    page/per_page values & their errors."""

    page_name = 'page'
    per_page_name = 'per_page'
    default_per_page_setting = 'PER_PAGE'
    max_per_page = 1000

    def parse_page_values_from_get(self):
        args = self._prepare_get_arguments()

        page = args.get(self.page_name, 1)
        per_page = args.get(self.per_page_name, getattr(settings, self.default_per_page_setting))

        lib.tools.quick_assert(
            str(page).isdigit() and int(page) > 0,
            ('invalid', 'validation', self.page_name),
            _('Page number is invalid'),
        )
        page = int(page)

        lib.tools.quick_assert(
            str(per_page).isdigit(),
            ('invalid', 'validation', self.per_page_name),
            _('Per page number is invalid'),
        )
        per_page = min(int(per_page), self.max_per_page)

        setattr(self, self.page_name, page)
        setattr(self, self.per_page_name, per_page)
        return page, per_page

    @property
    def offset(self):
        page = getattr(self, self.page_name)
        per_page = getattr(self, self.per_page_name)

        return (page - 1) * per_page

    @property
    def limit(self):
        return self.offset + getattr(self, self.per_page_name)
