from itertools import cycle
from unittest.mock import patch

from django.test import TransactionTestCase
from model_bakery.recipe import Recipe, foreign_key
import pytest


from webapps.business.models.models import Business
from webapps.pos.models import POS
from service.auto_enable_fast_payouts.tasks import try_enable_fast_payouts_in_batches_task
from lib.feature_flag.feature.payment import AutoFastPayoutsParams
from lib.tests.utils import override_eppo_feature_flag


@pytest.mark.django_db(transaction=True)
class TasksTests(TransactionTestCase):
    databases = ['default', 'payments-database']

    @patch('service.auto_enable_fast_payouts.tasks.try_enable_fast_payouts_task')
    def test_try_enable_fast_payouts_in_batches_task__start_correct_size_batched_tasks(
        self, batch_task
    ):
        # with
        active_business_recipe = Recipe(
            Business, active=True, status=cycle((Business.Status.PAID, Business.Status.OVERDUE))
        )
        not_active_business_recipe = Recipe(
            Business,
            active=False,
            status=cycle(
                (Business.Status.SUSPENDED, Business.Status.TRIAL, Business.Status.BLOCKED)
            ),
        )
        to_activate_pos_recipe = Recipe(
            POS,
            fast_payouts_visible=False,
            fast_payouts_admin_blocked=True,
            fast_payouts_merchant_enabled=False,
            active=True,
            business=foreign_key(active_business_recipe, one_to_one=True),
        )
        not_to_activate_pos_recipe = Recipe(
            POS, business=foreign_key(not_active_business_recipe, one_to_one=True)
        )
        to_activate_pos_recipe.make(_quantity=3)
        not_to_activate_pos_recipe.make(_quantity=6)
        # when
        with override_eppo_feature_flag(
            {
                AutoFastPayoutsParams.flag_name: {
                    "batch_size": 2,
                }
            }
        ):
            try_enable_fast_payouts_in_batches_task()
        # then
        calls = batch_task.delay.call_args_list

        self.assertIsInstance(calls[0].args[0], list)
        self.assertEqual(len(calls[0].args[0]), 2)
        self.assertIsInstance(calls[0].args[0][0], int)
        self.assertIsInstance(calls[0].args[0][1], int)

        self.assertIsInstance(calls[1].args[0], list)
        self.assertEqual(len(calls[1].args[0]), 1)
        self.assertIsInstance(calls[0].args[0][0], int)
