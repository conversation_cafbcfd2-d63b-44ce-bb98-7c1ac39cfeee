import json

import pytest
from model_bakery import baker

from service.tests import BaseAsyncHTTPTest
from webapps.notification.models import NotificationSchedule, ScheduleState


@pytest.mark.django_db
class TestAppleSubscriptionPurchaseHandler(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/subscriptions/apple/submit/?'

    def test_post__new_billing(self):
        self.business.has_new_billing = True
        self.business.save()

        response = self.fetch(self.url.format(self.business.id), method='POST', body={})

        self.assertEqual(response.code, 403)


@pytest.mark.django_db
class TestAppleSubscriptionValidationHandler(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/subscriptions/apple/validate/?'

    def test_post__new_billing(self):
        self.business.has_new_billing = True
        self.business.save()

        response = self.fetch(self.url.format(self.business.id), method='POST', body={})

        self.assertEqual(response.code, 403)


@pytest.mark.django_db
class TestAppleSubscriptionTaskStatusHandler(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/subscriptions/apple/task/{}'

    def test_get__business_mismatch_error(self):
        task = baker.make(
            NotificationSchedule,
            task_id=self.business.id,
            parameters={'business_id': str(self.business.id)},
            state=ScheduleState.SUCCESS,
            result=json.dumps(
                {
                    "result": {
                        "exception": "BusinessMismatchError (business_id=5, business_id_set={4})",
                        "state": ScheduleState.FAILURE,
                    },
                }
            ),
        )
        response = self.fetch(self.url.format(self.business.id, task.id), method='GET')

        self.assertEqual(response.code, 400)
        self.assertDictEqual(
            response.json,
            {
                'errors': [
                    {
                        'description': 'This Apple ID account is bound to another business.',
                        'field': 'non_field_errors',
                        'code': 'business_mismatch_error',
                    },
                ],
            },
        )
