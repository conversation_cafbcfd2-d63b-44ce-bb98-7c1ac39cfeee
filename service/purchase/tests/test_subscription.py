import datetime
from decimal import Decimal

from unittest.mock import call
import pytest
from dateutil.relativedelta import relativedelta
from freezegun import freeze_time
from segment.analytics import Client

from mock import patch
from mock import MagicMock
from model_bakery import baker
from pytz import UTC

from lib.deeplink.consts import BranchIOAppTypes
from lib.deeplink.branchio.client import BranchIOClient
from lib.facebook.enums import EventName
from lib.tools import id_to_external_api, tznow
from service.tests import BaseAsyncHTTPTest
from webapps.billing.enums import (
    DiscountType,
    ProductType,
    SubscriptionStatus,
)
from webapps.billing.models import (
    BillingProduct,
    BillingSubscribedProduct,
    BillingSubscription,
)
from webapps.booking.models import BookingSources
from webapps.business.models import Business, Resource
from webapps.consts import FRONTDESK
from webapps.purchase.models import (
    Invoice,
    PurchaseRequest,
    Subscription,
    SubscriptionListing,
    SubscriptionRequest,
    SubscriptionTransaction,
)
from webapps.segment.utils import post_first_paid_status_action
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User


def gen_func():
    return relativedelta(months=1)


baker.generators.add('lib.interval.fields.IntervalField', gen_func)


@pytest.mark.django_db
@patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
class TestSubscriptionsListingHandler(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/subscriptions/listing/'

    def setUp(self):
        super().setUp()

        self.url = self.url.format(business_id=self.business.id)

    def test_response(self):
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertIn('subscriptions', response.json)
        self.assertIn('sms', response.json)
        self.assertIn('errors', response.json)
        self.assertIsInstance(response.json['subscriptions'], list)
        self.assertIsInstance(response.json['errors'], dict)

        # ensure 200 status is returned despite invalid offer or promotion
        response = self.fetch(self.url + '?offer_id=0', method='GET')
        self.assertEqual(response.code, 200)
        self.assertListEqual(list(response.json['errors'].keys()), ['offer_id'])

        response = self.fetch(self.url + '?promo_id=0', method='GET')
        self.assertEqual(response.code, 200)
        self.assertListEqual(list(response.json['errors'].keys()), ['promo_id'])

    def test_response_for_demo_business(self):
        """
        Demo accounts should not use subscriptions. Expect empty result.
        """
        self.business.status = Business.Status.DEMO
        self.business.save(update_fields=['status'])

        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertIn('subscriptions', response.json)
        self.assertIn('sms', response.json)
        self.assertListEqual(list(response.json['subscriptions']), [])

    def test_url_token_session(self):
        """
        Check if can return results without a full login,
        just by giving "token" param.
        """
        self.session = None

        purchase_request = PurchaseRequest.objects.create(
            business_id=self.business.id,
            source=Business.PaymentSource.BRAINTREE,
        )
        url = f'{self.url}?token={purchase_request.token}'
        headers = {
            'Content-Type': self.content_type,
            'X-API-Country': self.get_x_api_country(),
            'X-API-KEY': 'biz_key',
        }

        response = self.fetch(url, method='GET', headers=headers)
        self.assertEqual(response.code, 200)

        purchase_request.delete()
        response = self.fetch(url, method='GET', headers=headers)
        self.assertEqual(response.code, 403)


@pytest.mark.django_db
class TestSubscriptionListingAliasHandler(TestSubscriptionsListingHandler):
    """
    Seems /subscription_listing/ was renamed to /subscriptions/listing/.

    Test both.
    """

    url = '/business_api/me/businesses/{business_id}/subscription_listing/'

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @patch.object(BranchIOClient, 'track_event')
    @patch('lib.tagmanager.client.GTMClient._request_api')
    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    def test_analytics_first_paid_status_achieved(
        self,
        analytics_gtm_mock,
        analytics_branchio_mock,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        self.business.status = Business.Status.TRIAL

        baker.make(
            Subscription,
            source=Business.PaymentSource.ITUNES,
            business_id=self.business.id,
            start=datetime.datetime(2018, 9, 1, tzinfo=UTC),
            expiry=datetime.datetime(2018, 9, 30, tzinfo=UTC),
        )
        self.business.save()

        response = self.fetch(self.url, method='GET', extra_headers=_martech_extra_headers())

        self.assertEqual(response.code, 200)
        self.assertIn('subscriptions', response.json)
        self.assertIn('sms', response.json)
        self.assertIn('errors', response.json)
        self.assertIsInstance(response.json['subscriptions'], list)
        self.assertIsInstance(response.json['errors'], dict)

        self.business.status = Business.Status.PAID
        self.business.save()

        context_data = {
            'business_id': self.business.id,
        }
        post_first_paid_status_action(self.business.id, context_data=context_data)
        assert analytics_track_mock.call_count == 2
        assert analytics_identify_mock.call_count == 2
        assert analytics_gtm_mock.call_count == 1
        assert analytics_branchio_mock.call_count == 1

        assert analytics_gtm_mock.call_args_list[0][1]['payload']['events'][0]['name'] == (
            '1st_Paid_Status_Achieved'
        )

    @patch.object(BranchIOClient, 'track_event')
    def test_branchio_analytics_first_paid_status_achieved(
        self,
        analytics_branchio_mock,
    ):
        self.business.has_new_billing = True
        self.business.save()
        post_first_paid_status_action(
            self.business.id,
            context_data={
                'business_id': self.business.id,
            },
        )
        assert analytics_branchio_mock.call_args_list[0] == call(
            event_name='1st_Paid_Status_Achieved',
            event_data={
                'country': 'us',
                'user_role': 'Owner',
                'business_id': id_to_external_api(self.business.id),
                'user_id': id_to_external_api(self.user.id),
                'email': '<EMAIL>',
                'primary_category': None,
                'business_admin_status': 'PAID',
                'fingerprint': 'UNKNOWN',
                'business_phone': '',
                'subscription_payment_method': 'Booksy Billing',
            },
            user_data={'developer_identity': self.business.owner_id},
            app_type=BranchIOAppTypes.BUSINESS,
        )

    @patch('webapps.segment.utils.timestamp')
    @patch('lib.facebook.service.FacebookEventService.send_basic_event_for_business')
    def test_business_facebook_conversion_event_task(
        self,
        mock_facebook_event,
        mock_timestamp,
    ):
        mock_timestamp.return_value = 1633790000
        post_first_paid_status_action(
            self.business.id,
        )
        mock_facebook_event.assert_called_once_with(
            event_name=EventName.FIRST_PAID_STATUS_ACHIEVED,
            business_id=self.business.id,
            event_time=1633790000,
        )

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @patch('lib.tagmanager.client.GTMClient._request_api')
    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    def test_analytics_first_paid_status_achieved_no_pseudo_id(
        self,
        analytics_gtm_mock,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        self.business.status = Business.Status.TRIAL

        baker.make(
            Subscription,
            source=Business.PaymentSource.ITUNES,
            business_id=self.business.id,
            start=datetime.datetime(2018, 9, 1, tzinfo=UTC),
            expiry=datetime.datetime(2018, 9, 30, tzinfo=UTC),
        )
        self.business.save()

        response = self.fetch(self.url, method='GET')

        self.assertEqual(response.code, 200)
        self.assertIn('subscriptions', response.json)
        self.assertIn('sms', response.json)
        self.assertIn('errors', response.json)
        self.assertIsInstance(response.json['subscriptions'], list)
        self.assertIsInstance(response.json['errors'], dict)

        self.business.status = Business.Status.PAID
        self.business.save()

        context_data = {
            'business_id': self.business.id,
        }
        post_first_paid_status_action(self.business.id, context_data=context_data)
        assert analytics_track_mock.call_count == 2
        assert analytics_identify_mock.call_count == 2
        assert analytics_gtm_mock.call_count == 0


@pytest.mark.django_db
class TestSubscriptionsHandler(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/subscriptions/'

    def setUp(self):
        super().setUp()

        now = tznow()

        self.url = self.url.format(business_id=self.business.id)

        self.product = baker.make(SubscriptionListing)

        with freeze_time(now) as current_time:
            self.subscription_1 = baker.make(
                Subscription,
                business=self.business,
                product=self.product,
                source=Business.PaymentSource.BRAINTREE,
            )
            current_time.move_to(now + datetime.timedelta(days=30))

            self.subscription_2 = baker.make(
                Subscription,
                business=self.business,
                product=self.product,
                source=Business.PaymentSource.BRAINTREE,
            )
            current_time.move_to(now + datetime.timedelta(days=60))

            self.other_subscription = baker.make(
                Subscription,
                business=baker.make(Business, owner=self.user),
                product=self.product,
                source=Business.PaymentSource.BRAINTREE,
            )

    @staticmethod
    def _list_subscription_ids(response):
        return list(
            map(lambda data: data['id'], response.json['subscriptions']),
        )

    def test_request(self):
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertIsInstance(response.json, dict)
        self.assertListEqual(
            self._list_subscription_ids(response), [self.subscription_2.id, self.subscription_1.id]
        )
        self.assertIn('mobile_billing_subscription_url', response.json)
        self.assertIn('mobile_billing_subscription_urls', response.json)
        response = self.fetch(self.url + '?limit=1', method='GET')
        self.assertEqual(response.code, 200)
        self.assertListEqual(self._list_subscription_ids(response), [self.subscription_2.id])

    @pytest.mark.freeze_time(datetime.datetime(2021, 3, 15, tzinfo=UTC))
    def test_request_new_billing(self):
        self.business.has_new_billing = True
        self.business.save()
        # 2 paid staffers
        baker.make(Resource, business_id=self.business.id, type=Resource.STAFF)
        baker.make(Resource, business_id=self.business.id, type=Resource.STAFF)
        postpaid_sms = baker.make(
            BillingProduct,
            product_type=ProductType.POSTPAID_SMS,
        )
        staffer_saas = baker.make(
            BillingProduct,
            product_type=ProductType.STAFFER_SAAS,
        )
        saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
        )
        subscription = baker.make(
            BillingSubscription,
            business_id=self.business.id,
            date_start=datetime.datetime(2021, 1, 20, tzinfo=UTC),
            date_expiry=datetime.datetime(2021, 10, 20, tzinfo=UTC),
            next_billing_date=datetime.datetime(2021, 3, 20, tzinfo=UTC),
            paid_through_date=datetime.datetime(2021, 3, 20, tzinfo=UTC),
            currency='USD',
            status=SubscriptionStatus.ACTIVE,
        )
        baker.make(
            BillingSubscribedProduct,
            subscription_id=subscription.id,
            product_id=postpaid_sms.id,
            product_type=ProductType.POSTPAID_SMS,
            date_start=subscription.date_start,
        )
        baker.make(
            BillingSubscribedProduct,
            subscription_id=subscription.id,
            product_id=staffer_saas.id,
            product_type=ProductType.STAFFER_SAAS,
            date_start=subscription.date_start,
            # Price will be later calculated for 2
            quantity=1,
            unit_price=Decimal('10.00'),
            discount_type=DiscountType.FIXED,
            discount_amount=Decimal('5.00'),
            discounted_price=Decimal('5.00'),
            free_staff_qty=1,
            max_qty=9,
            sms_amount=25,
        )
        # Saas is always present in subscription
        baker.make(
            BillingSubscribedProduct,
            subscription_id=subscription.id,
            product_id=saas.id,
            product_type=ProductType.SAAS,
            quantity=1,
            sms_add_on_id=postpaid_sms.id,
            staff_add_on_id=staffer_saas.id,
            date_start=subscription.date_start,
            name='Best plan ever',
            discount_type=DiscountType.FIXED,
            discount_amount=Decimal('50.00'),
            unit_price=Decimal('149.00'),
            total_price=Decimal('149.00'),
            final_price=Decimal('99.00'),
            discounted_price=Decimal('99.00'),
            discount_granted=Decimal('50.00'),
            currency='USD',
            sms_amount=200,
        )

        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertIsInstance(response.json, dict)
        self.assertListEqual(
            self._list_subscription_ids(response),
            [subscription.id, self.subscription_2.id, self.subscription_1.id],
        )
        sub_new_billing = response.json['subscriptions'][0]
        self.assertEqual(
            sub_new_billing,
            {
                'id': subscription.id,
                'product': {
                    'name': 'Best plan ever',
                    'display_price': '$109.00/month',
                },
                'source': Business.PaymentSource.BRAINTREE_BILLING.value,
                'start': '2021-01-20T00:00:00Z',
                'expiry': '2021-10-20T00:00:00Z',
                'renewing': True,
                'active': True,
            },
        )
        response = self.fetch(self.url + '?limit=1', method='GET')
        self.assertEqual(response.code, 200)
        self.assertListEqual(
            self._list_subscription_ids(response),
            [subscription.id],
        )

    def test_braintree_payment(self):
        with patch('braintree.Customer'):
            response = self.fetch(self.url + '?braintree_payment=true', method='GET')
        self.assertEqual(response.code, 200)
        self.assertDictEqual(
            response.json['payment_method'],
            {
                'type': 'unknown',
                'image_url': None,
                'display': None,
            },
        )


@pytest.mark.django_db
class TestInvoiceListHandlerHandler(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/subscriptions/invoice/'

    def setUp(self):
        super().setUp()
        self.url = self.url.format(business_id=self.business.id)
        self.product = baker.make(SubscriptionListing)
        self.subscription = baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.BRAINTREE,
        )
        self.transaction = baker.make(
            SubscriptionTransaction,
            business=self.business,
            subscription=self.subscription,
            state=SubscriptionTransaction.State.CHARGED,
            charged_on=tznow() - datetime.timedelta(days=30),
        )
        self.invoice = baker.make(Invoice, transaction=self.transaction)

    @staticmethod
    def _list_invoice_ids(response):
        return list(
            map(lambda data: data['id'], response.json['invoices']),
        )

    def test_request(self):
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertIsInstance(response.json, dict)

        self.assertListEqual(
            self._list_invoice_ids(response),
            [self.invoice.id],
        )


@pytest.mark.django_db
class TestInvoiceEmailHandler(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/subscriptions/invoice/send/{invoice_id}/'

    def setUp(self):
        super().setUp()

        self.product = baker.make(SubscriptionListing)
        self.subscription = baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.BRAINTREE,
        )
        self.transaction = baker.make(
            SubscriptionTransaction,
            business=self.business,
            subscription=self.subscription,
            state=SubscriptionTransaction.State.CHARGED,
            charged_on=tznow(),
        )
        self.invoice = baker.make(Invoice, transaction=self.transaction)
        self.url = self.url.format(business_id=self.business.id, invoice_id=self.invoice.id)

    def test_send_invoice(self):
        with patch('webapps.purchase.models.Invoice.send') as mock_send:
            response = self.fetch(self.url, method='POST', body='')
            self.assertEqual(response.code, 201)
            self.assertEqual(mock_send.call_count, 1)


@pytest.mark.django_db
class TestOfflineSubscriptionRequestHandler(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/subscriptions/offline/request'

    def setUp(self):
        super().setUp()
        self.url = self.url.format(business_id=self.business.id)

        self.business.status = Business.Status.TRIAL
        self.business.payment_source = Business.PaymentSource.OFFLINE
        self.business.save()

    def test_get__not_requested(self):
        baker.make(  # not related business
            SubscriptionRequest,
            subscription_type=SubscriptionRequest.SupportedSubscriptionType.OFFLINE,
        )

        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertFalse(response.json['already_requested'])

    def test_get__requested_braintree(self):
        baker.make(
            SubscriptionRequest,
            business=self.business,
            subscription_type=Business.PaymentSource.BRAINTREE,
        )

        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertFalse(response.json['already_requested'])

    def test_get__requested_offline(self):
        baker.make(
            SubscriptionRequest,
            business=self.business,
            subscription_type=SubscriptionRequest.SupportedSubscriptionType.OFFLINE,
        )
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertTrue(response.json['already_requested'])

    def test_post__not_offline(self):
        self.business.payment_source = Business.PaymentSource.BRAINTREE
        self.business.save()

        response = self.fetch(self.url, method='POST', body='')
        self.assertEqual(response.code, 404)

    def test_post__not_trial(self):
        self.business.status = Business.Status.PAID
        self.business.save()

        response = self.fetch(self.url, method='POST', body='')
        self.assertEqual(response.code, 404)

    def test_access_manager_post(self):
        user = baker.make(User)
        baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
            staff_user=user,
            staff_email=user.email,
        )
        self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        response = self.fetch(self.url, method='POST', body='')
        self.assertEqual(response.code, 201)
        response = self.fetch(self.url, method='GET')
        self.assertEqual(response.code, 200)

    def test_post__offline_new_request(self):
        self.assertFalse(SubscriptionRequest.objects.count())

        response = self.fetch(self.url, method='POST', body='')
        self.assertEqual(response.code, 201)

        instance = SubscriptionRequest.objects.get()
        self.assertEqual(instance.business, self.business)
        self.assertEqual(instance.subscription_type, Business.PaymentSource.OFFLINE)

    def test_post__offline_another_request(self):
        baker.make(
            SubscriptionRequest,
            business=self.business,
            subscription_type=SubscriptionRequest.SupportedSubscriptionType.OFFLINE,
        )

        response = self.fetch(self.url, method='POST', body='')
        self.assertEqual(response.code, 201)

        self.assertEqual(SubscriptionRequest.objects.count(), 1)


def _martech_extra_headers():
    frontdesk_source, _ = BookingSources.objects.get_or_create(
        app_type=BookingSources.BUSINESS_APP,
        name=FRONTDESK,
        defaults={
            'api_key': 'frontdesk-test-api-key',
        },
    )
    return {'X-User-Pseudo-ID': 'TEST.1234', 'X-API-KEY': frontdesk_source.api_key}
