from webapps.business.enums import (
    FeatureStatus,
    FeatureLabel,
    FeatureStatusColor,
)

from service.business.feature_status.serializers import (
    ServicePromotionsStatusSerializer,
)


def test_service_promotions_not_running():
    instance = {
        'label': FeatureLabel.INACTIVE,
        'status': FeatureStatus.INACTIVE,
        'status_color': FeatureStatusColor.GRAY,
    }

    serializer = ServicePromotionsStatusSerializer(instance=instance)
    expected_status = {
        'label': 'Inactive',
        'status': 'inactive',
        'status_color': 'gray',
    }
    assert serializer.data == expected_status


def test_service_promotions_running():

    instance = {
        'appointments_count': 2,
        'estimated_revenue': '12.34',
        'label': FeatureLabel.RUNNING,
        'promotion_types_count': 2,
        'status': FeatureStatus.ACTIVE,
        'status_color': FeatureStatusColor.GREEN,
    }
    serializer = ServicePromotionsStatusSerializer(instance=instance)
    expected_status = {
        'appointments_count': 2,
        'estimated_revenue': '$12.34',
        'label': 'Running',
        'promotion_types_count': 2,
        'status': 'active',
        'status_color': 'green',
    }
    assert serializer.data == expected_status
