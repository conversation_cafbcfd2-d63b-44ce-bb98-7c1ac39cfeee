import datetime

import pytest
import pytz
from freezegun.api import freeze_time
from model_bakery import baker

from lib.feature_flag.feature.promotions import ServicePromotionsAggerationOptimisationFlag
from lib.tests.utils import override_eppo_feature_flag
from service.business.feature_status.invite_customers import (
    invite_customers_status,
)
from service.tests import BaseAsyncHTTPTest
from webapps.business.models import Resource
from webapps.business.models.bci import BusinessCustomerInfo


@pytest.mark.django_db
@override_eppo_feature_flag({ServicePromotionsAggerationOptimisationFlag.flag_name: True})
class BusinessFeaturesStatusHandlerTests(BaseAsyncHTTPTest):
    def test_not_invited(self):
        status_ = invite_customers_status(self.business, Resource.STAFF_ACCESS_LEVEL_OWNER)
        self.assertDictEqual(
            status_,
            {
                'status': 'inactive',
                'status_color': 'gray',
                'label': 'Not invited',
            },
        )

    @freeze_time(datetime.datetime(2020, 5, 2, 12, 0, tzinfo=pytz.UTC))
    def test_invited(self):
        bci: BusinessCustomerInfo = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            email='<EMAIL>',
        )
        bci.invite()
        self.business.refresh_from_db()

        status_ = invite_customers_status(self.business, Resource.STAFF_ACCESS_LEVEL_OWNER)
        self.assertDictEqual(
            status_,
            {
                'status': 'active',
                'status_color': 'green',
                'label': 'Clients invited',
                'invited_count': 1,
                'last_invite_date': '2020-05-02',
            },
        )

    @freeze_time(datetime.datetime(2022, 5, 2, 12, 0, tzinfo=pytz.UTC))
    def test_invited_more_then_year_ago(self):
        bci: BusinessCustomerInfo = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            email='<EMAIL>',
        )
        with freeze_time(datetime.datetime(2020, 3, 15, 12)):
            bci.invite()
        self.business.refresh_from_db()

        status_ = invite_customers_status(self.business, Resource.STAFF_ACCESS_LEVEL_OWNER)
        self.assertDictEqual(
            status_,
            {
                'status': 'inactive',
                'status_color': 'gray',
                'label': 'Not invited',
            },
        )

    @freeze_time(datetime.datetime(2020, 5, 2, 12, 0, tzinfo=pytz.UTC))
    def test_multiple_invited(self):
        count = 4
        for i in range(count):
            bci = baker.make(
                BusinessCustomerInfo,
                business=self.business,
                email=f'test#{i}@booksy.com',
            )
            bci.invite()

        self.business.refresh_from_db()

        status_ = invite_customers_status(self.business, Resource.STAFF_ACCESS_LEVEL_OWNER)
        self.assertDictEqual(
            status_,
            {
                'status': 'active',
                'status_color': 'green',
                'label': 'Clients invited',
                'invited_count': count,
                'last_invite_date': '2020-05-02',
            },
        )

    def test_customer_to_invite(self):
        self.bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            email='<EMAIL>',
            invited=False,
            user_id=None,
            deleted=None,
            visible_in_business=True,
        )
        self.bci.reindex(refresh_index=True)
        status_ = invite_customers_status(self.business, Resource.STAFF_ACCESS_LEVEL_OWNER)
        self.assertDictEqual(
            status_,
            {
                'status': 'inactive',
                'status_color': 'orange',
                'label': '1 Client to invite',
            },
        )
