from unittest.mock import patch

import pytest
from rest_framework import status

from lib.test_utils import create_test_reciever, create_test_push_token
from lib.tools import str_to_sha3_512
from service.tests import BaseAsyncHTTPTest
from webapps.notification.models import UserNotification, Reciever
from webapps.user.models import UserProfile


@pytest.mark.django_db
@patch('webapps.notification.tools.MAX_RECEIVERS_PER_TYPE', 1)
@patch('webapps.notification.tools.TRUNCATE_RECEIVERS_TO', 1)
class BusinessEmailReceiverTestCase(BaseAsyncHTTPTest):
    url_email = '/business_api/me/businesses/{}/notifications/email/{}/'
    url_ios = '/business_api/me/businesses/{}/notifications/ios/{}/'

    def test_should_not_delete_excess_email_receivers(self):
        for i in range(2):
            create_test_reciever(
                self.user.id,
                token=f'test+{i}@booksy.com',
                reciever_type=UserNotification.EMAIL_NOTIFICATION,
                profile_type=UserProfile.Type.BUSINESS,
                business=self.business,
            )
        assert Reciever.objects.filter(business=self.business).count() == 2

        url = self.url_email.format(self.business.id, '<EMAIL>')
        resp = self.fetch(url, method='PUT', body={})

        assert resp.code == 200
        assert Reciever.objects.filter(business=self.business).count() == 3

    def test_should_not_delete_email_receivers_when_deleting_excess_push_receiver(self):
        for i in range(3):
            create_test_reciever(
                self.user.id,
                token=f'test+{i}@booksy.com',
                reciever_type=UserNotification.EMAIL_NOTIFICATION,
                profile_type=UserProfile.Type.BUSINESS,
                business=self.business,
            )
            create_test_reciever(
                self.user.id,
                device=Reciever.IOS,
                business=self.business,
                reciever_type=UserNotification.PUSH_NOTIFICATION,
                profile_type=UserProfile.Type.BUSINESS,
            )

        qs = UserNotification.objects.filter(
            profile=self.user.profiles.get(profile_type=UserProfile.Type.BUSINESS)
        )
        email_notification = qs.get(type=UserNotification.EMAIL_NOTIFICATION)
        push_notification = qs.get(type=UserNotification.PUSH_NOTIFICATION)
        assert email_notification.recievers.count() == 3
        assert push_notification.recievers.count() == 3

        token = create_test_push_token(Reciever.IOS)
        url = self.url_ios.format(self.business.id, token)  # trigger delete excess push receivers
        resp = self.fetch(url, method='PUT', body={})
        assert resp.code == status.HTTP_200_OK

        assert email_notification.recievers.count() == 3
        assert push_notification.recievers.count() == 2

    def test_delete_specific_email_receiver(self):
        session_hash = str_to_sha3_512(self.session.session_key)

        receiver1 = create_test_reciever(
            self.user.id,
            token='<EMAIL>',
            reciever_type=UserNotification.EMAIL_NOTIFICATION,
            profile_type=UserProfile.Type.BUSINESS,
            business=self.business,
            session_hash=session_hash,
        )
        receiver2 = create_test_reciever(
            self.user.id,
            token='<EMAIL>',
            reciever_type=UserNotification.EMAIL_NOTIFICATION,
            profile_type=UserProfile.Type.BUSINESS,
            business=self.business,
            session_hash=session_hash,
        )

        url = self.url_email.format(self.business.id, receiver1.identifier)
        resp = self.fetch(url, method='DELETE')
        self.assertEqual(resp.code, status.HTTP_200_OK)

        receiver1.refresh_from_db()
        self.assertIsNotNone(receiver1.deleted)

        receiver2.refresh_from_db()
        self.assertIsNone(receiver2.deleted)
