import pytest
from model_bakery import baker
from rest_framework import status

from lib.baker_utils import get_or_create_booking_source
from lib.elasticsearch.consts import ESIndex
from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import BookingSources
from webapps.business.models import Business
from webapps.consts import WEB, INTERNAL
from webapps.elasticsearch.elastic import ELASTIC
from webapps.kill_switch.models import KillSwitch
from webapps.search_engine_tuning.models import BusinessTuning


@pytest.mark.usefixtures('clean_index_class_fixture')
@pytest.mark.django_db
class BusinessDetailsHandlerBaseTestCase(BaseAsyncHTTPTest):
    url = '/customer_api/businesses/{}/?'

    def setUp(self):
        super().setUp()
        baker.make(KillSwitch, name=KillSwitch.System.SKIP_HIDDEN_ON_WEB, is_killed=True)

    def get_customer_booking_source(self):
        return get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            name=WEB,
            api_key='customer_key',
        )

    def make_request(self, business):
        response = self.fetch(self.url.format(business.id), method='GET')
        return response

    @staticmethod
    def run_indexing(business):
        index = ELASTIC.indices[ESIndex.BUSINESS]
        business_document = business.get_document()
        business_document.save()
        index.refresh()


@pytest.mark.usefixtures('clean_index_class_fixture')
class BusinessDetailsHandlerWithFiltering(BusinessDetailsHandlerBaseTestCase):
    def test_business_details_without_search_tuning(self):
        self.run_indexing(self.business)
        response = self.make_request(self.business)

        self.assertEqual(response.code, status.HTTP_200_OK)
        body = response.json
        self.assertEqual(body['business']['id'], self.business.id)

    def test_business_details_on_web(self):
        baker.make(BusinessTuning, business_id=self.business.id, hidden_on_web=False)
        self.run_indexing(self.business)
        response = self.make_request(self.business)

        self.assertEqual(response.code, status.HTTP_200_OK)
        body = response.json
        self.assertEqual(body['business']['id'], self.business.id)

    def test_business_details_hidden_on_web(self):
        baker.make(BusinessTuning, business_id=self.business.id, hidden_on_web=True)
        self.run_indexing(self.business)
        response = self.make_request(self.business)

        self.assertEqual(response.code, status.HTTP_404_NOT_FOUND)


@pytest.mark.usefixtures('clean_index_class_fixture')
class BusinessDetailsHandlerWithBListingFiltering(BusinessDetailsHandlerBaseTestCase):
    def setUp(self):
        super().setUp()
        self.business.status = Business.Status.B_LISTING
        self.business.save()

    def test_b_listing_business_details_on_web(self):
        baker.make(BusinessTuning, business_id=self.business.id, hidden_on_web=False)
        self.run_indexing(self.business)
        response = self.make_request(self.business)

        self.assertEqual(response.code, status.HTTP_200_OK)
        body = response.json
        self.assertEqual(body['business']['id'], self.business.id)

    def test_b_listing_business_details_hidden_on_web(self):
        baker.make(BusinessTuning, business_id=self.business.id, hidden_on_web=True)
        self.run_indexing(self.business)
        response = self.make_request(self.business)

        self.assertEqual(response.code, status.HTTP_404_NOT_FOUND)


@pytest.mark.usefixtures('clean_index_class_fixture')
class BusinessDetailsHandlerWithoutFiltering(BusinessDetailsHandlerBaseTestCase):
    def get_customer_booking_source(self):
        return get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            name=INTERNAL,
            api_key='customer_key',
        )

    def test_business_details_with_other_booking_source(self):
        baker.make(BusinessTuning, business_id=self.business.id, hidden_on_web=False)
        self.run_indexing(self.business)
        response = self.make_request(self.business)

        self.assertEqual(response.code, status.HTTP_200_OK)
        body = response.json
        self.assertEqual(body['business']['id'], self.business.id)

    def test_hidden_business_details_with_other_booking_source(self):
        baker.make(BusinessTuning, business_id=self.business.id, hidden_on_web=True)
        self.run_indexing(self.business)
        response = self.make_request(self.business)

        self.assertEqual(response.code, status.HTTP_200_OK)
        body = response.json
        self.assertEqual(body['business']['id'], self.business.id)
