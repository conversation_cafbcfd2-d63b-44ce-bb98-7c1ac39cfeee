import pytest
from mock import patch, PropertyMock
from rest_framework import status
from segment.analytics import Client

from service.tests import BaseAsyncHTTPTest, dict_assert, get_cache_format
from webapps.booking.models import BookingSources
from webapps.consts import LIBRARY_ELEARNING
from webapps.segment.consts import UserRoleEnum
from webapps.user.const import FORCED_LAST_NAME


@pytest.mark.django_db
class BusinessAccountHandlerTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/?'

    def test_200_get_details(self):
        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK
        resp_json = resp.json
        resp_json['account'].pop('id')
        assert resp_json['account'] == {
            'about_me': None,
            'active': True,
            'address_line_1': '',
            'address_line_2': '',
            'apartment_number': '',
            'apple_user_uuid': None,
            'city': None,
            'zipcode': None,
            'birthday': None,
            'cell_phone': '',
            'email': '<EMAIL>',
            'facebook_id': None,
            'first_name': self.user.first_name,
            'gender': None,
            'google_id': None,
            'home_phone': '',
            'language': 'en',
            'last_name': self.user.last_name,
            'latitude': None,
            'longitude': None,
            'payment_auto_accept': False,
            'photo': None,
            'work_phone': '',
        }

    @patch.object(Client, 'identify')
    def test_200_change(self, analytics_identify_mock):
        body = {
            "active": False,
            "first_name": self.user.first_name,
            "last_name": self.user.last_name,
            "latitude": 0,
            "longitude": 0,
            "about_me": "",
            "gender": "M",
            "birthday": "",
            "cell_phone": "",
            "work_phone": "",
            "home_phone": "***********",
            "email": "<EMAIL>",
            "facebook_id": "",
            'photo': 'asfadfafasfasfasfaf',
            'language': 'pl_US',  # polish in the United States
        }
        resp = self.fetch(self.url, body=body, method='PUT')
        assert resp.code == status.HTTP_200_OK
        resp_json = resp.json
        resp_json['account'].pop('id')
        assert resp_json['account'] == {
            'about_me': None,
            'active': True,
            'address_line_1': '',
            'address_line_2': '',
            'apartment_number': '',
            'apple_user_uuid': None,
            'city': None,
            'zipcode': None,
            'birthday': None,
            'cell_phone': '',
            'email': '<EMAIL>',
            'facebook_id': None,
            'first_name': self.user.first_name,
            'gender': 'M',
            'google_id': None,
            'home_phone': '(*************',
            'language': 'pl',
            'last_name': self.user.last_name,
            'latitude': None,
            'longitude': None,
            'payment_auto_accept': False,
            'photo': None,
            'work_phone': '',
        }
        assert analytics_identify_mock.call_count == 1
        dict_assert(
            analytics_identify_mock.call_args_list[0][1]['traits'],
            {
                'email': '<EMAIL>',
                'locale': 'pl',
                'user_role': UserRoleEnum.OWNER,
            },
        )

    def test_200_not_modifiable_fields(self):
        body = {
            "active": False,
            "first_name": self.user.first_name,
            "last_name": self.user.last_name,
            "address_line_1": "blah blah blah blah",
            "address_line_2": "hlab hlab hlab hlab",
            "apartment_number": "123",
            'city': None,
            "zipcode": "10001",  # is in schema, not present after change
            "latitude": 0,
            "longitude": 0,
            "about_me": "bla bla bla",
            "gender": "M",
            "birthday": "",
            "cell_phone": "",
            "work_phone": "",
            "home_phone": "",
            "email": "<EMAIL>",
            "facebook_id": "",
        }
        resp = self.fetch(self.url, body=body, method='PUT')
        assert resp.code == status.HTTP_200_OK
        resp_json = resp.json
        resp_json['account'].pop('id')
        assert resp_json['account'] == {
            'about_me': None,  # not possible to change
            'active': True,
            'address_line_1': '',  # not possible to change
            'address_line_2': '',  # not possible to change
            'apartment_number': '',  # not possible to change
            'apple_user_uuid': None,
            'city': None,
            'zipcode': None,
            'birthday': None,
            'cell_phone': '',
            'email': '<EMAIL>',
            'facebook_id': None,
            'first_name': self.user.first_name,
            'gender': 'M',
            'google_id': None,
            'home_phone': '',
            'language': 'en',
            'last_name': self.user.last_name,
            'latitude': None,
            'longitude': None,
            'payment_auto_accept': False,
            'photo': None,
            'work_phone': '',
        }
        assert self.user.profiles.all()[0].profile_type == 'B'

    def test_200_no_name_and_surname(self):
        body = {
            "active": False,
            "first_name": "",
            "last_name": "",
            "latitude": 0,
            "longitude": 0,
            "about_me": "",
            "gender": "M",
            "birthday": "",
            "cell_phone": "",
            "work_phone": "",
            "home_phone": "***********",
            "email": "<EMAIL>",
            "facebook_id": "",
        }
        resp = self.fetch(self.url, body=body, method='PUT')
        assert resp.code == status.HTTP_200_OK
        resp_json = resp.json
        assert not resp_json['account']['first_name']
        assert not resp_json['account']['last_name']

    def test_400_email_may_not_be_blank_error(self):
        body = {
            "active": False,
            "first_name": self.user.first_name,
            "last_name": self.user.last_name,
            "latitude": 0,
            "longitude": 0,
            "about_me": "",
            "gender": "M",
            "birthday": "",
            "cell_phone": "",
            "work_phone": "",
            "home_phone": "***********",
            "email": "",
            "facebook_id": "",
        }
        resp = self.fetch(self.url, body=body, method='PUT')
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json == {
            'errors': [
                {
                    'code': 'blank',
                    'description': 'This field may not be blank.',
                    'field': 'email',
                }
            ]
        }

    def test_400_no_email_error(self):
        body = {
            "active": False,
            "first_name": self.user.first_name,
            "last_name": self.user.last_name,
            "latitude": 0,
            "longitude": 0,
            "about_me": "",
            "gender": "M",
            "birthday": "",
            "cell_phone": "",
            "work_phone": "",
            "home_phone": "***********",
            "email": None,
            "facebook_id": "",
        }
        resp = self.fetch(self.url, body=body, method='PUT')
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json == {
            'errors': [
                {
                    'code': 'null',
                    'description': 'This field may not be null.',
                    'field': 'email',
                }
            ]
        }

    def test_update_name_with_html(self):
        html = '<a href="url">Click</a>'
        response = self.fetch(
            self.url,
            method='PUT',
            body={
                'last_name': html,
                'first_name': html,
            },
        )
        assert response.code == status.HTTP_400_BAD_REQUEST
        assert response.json == {
            'errors': [
                {
                    'code': 'invalid',
                    'description': 'Invalid first name.',
                    'field': 'first_name',
                },
                {
                    'code': 'invalid',
                    'description': 'Invalid last name.',
                    'field': 'last_name',
                },
            ],
        }

    def test_update_double_name(self):
        response = self.fetch(
            self.url,
            method='PUT',
            body={
                'last_name': 'Last name',
                'first_name': 'First name',
            },
        )
        assert response.code == status.HTTP_200_OK

        response = self.fetch(
            self.url,
            method='PUT',
            body={
                'last_name': 'Last-Name',
                'first_name': 'First-Name',
            },
        )
        assert response.code == status.HTTP_200_OK

        response = self.fetch(
            self.url,
            method='PUT',
            body={
                'last_name': 'Mr. Żołądź',
                'first_name': 'First.Name',
            },
        )
        assert response.code == status.HTTP_200_OK

    def test_update_language_no_last_name(self):
        self.business.owner.last_name = FORCED_LAST_NAME
        self.business.owner.save()

        response = self.fetch(
            self.url,
            method='PUT',
            body={'language': 'pl_PL'},
        )

        assert response.code == status.HTTP_200_OK, response.json


@pytest.mark.django_db
class BusinessAccountHandlerElearningRateLimitTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/?'
    api_key = 'booksy-library-zlHnoT80-hwnL-6EjC-aJu4-uJjhvMhXYy9T'

    def setUp(self):
        BookingSources.objects.create(
            **{
                'name': LIBRARY_ELEARNING,
                'app_type': 'B',
                'api_key': self.api_key,
            }
        )
        return super().setUp()

    @patch(
        'service.mixins.throttling.BooksyRateThrottle.rate',
        new_callable=PropertyMock(
            return_value='1/minute',
        ),
    )
    @patch('service.mixins.throttling.is_throttle_whitelist_ip', return_value=False)
    @patch(
        'service.mixins.throttling.BooksyRateThrottle.cache_format',
        new_callable=PropertyMock(return_value=get_cache_format()),
    )
    def test_api_key_rate_limit(self, cache_format_mock, ip_mock, rate_mock):
        assert self.fetch(self.url).code == 200
        assert self.fetch(self.url).code == 429
        extra_headers = {'X-API-KEY': self.api_key}
        assert self.fetch(self.url, extra_headers=extra_headers).code == 200
        assert self.fetch(self.url, extra_headers=extra_headers).code == 200
