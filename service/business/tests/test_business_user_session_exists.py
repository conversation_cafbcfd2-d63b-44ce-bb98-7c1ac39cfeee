from datetime import datetime

import mock
import pytest
from rest_framework import status

from service.tests import BaseAsyncHTTPTest
from webapps.session.booksy_auth import BooksyAuthValidationError
from webapps.session.booksy_auth import BooksyAuthServiceException
from webapps.session.booksy_auth.session_store import BooksyAuthSessionStore
from webapps.session.booksy_auth.pb2.auth_pb2 import (
    SessionExistsResponse,
)  # pylint: disable=no-name-in-module
from webapps.user.enums import AuthOriginEnum


@pytest.mark.usefixtures('switch_on_new_login_fixture')
@pytest.mark.django_db
# ENSURE that session is not expired in _patched_login_response
@pytest.mark.freeze_time('2021-11-01')
class BusinessAccountSessionStatusHandlerTestCase(BaseAsyncHTTPTest):
    """Test check different scenarios for
        service.tools.RequestHandler#check_access_token
    This handler was chosen because it has no logic inside and
    requires login

    """

    url = '/business_api/me/active/'

    def setUp(self):
        super().setUp()
        # Use new_session_key attribute in get_headers
        self.new_session_key = 'auth_session_key'

    def tearDown(self):
        BooksyAuthSessionStore()._delete_cache(
            session_key=self.new_session_key
        )  # pylint: disable=protected-access

    def get_headers(self, path):
        # Override base class get_headers
        # from service.tests.BaseAsyncHTTPTest#get_headers
        headers = super().get_headers(path)
        headers['X-ACCESS-TOKEN'] = self.new_session_key
        return headers

    def _get_response_with_patch(self, **kwargs):
        path = 'webapps.session.booksy_auth.BooksyAuthClient._make_request'
        with mock.patch(path, **kwargs) as patched_session_exists:
            response = self.fetch(
                self.url,
                method='GET',
            )
            return response, patched_session_exists

    def _assert_called_with_new_session_key(self, patched):
        patched.assert_called_once()
        # args = funcion_name, message_type, data
        result = patched.call_args[0][2]['session_key']
        assert result == self.new_session_key
        self.assertEqual(result, self.new_session_key)

    def test_check_session_existing_user(self):
        response, patched_session_exists = self._get_response_with_patch(
            return_value=SessionExistsResponse(
                session_exists=True,
                # local id for user
                country_user_id=self.user.id,
                # under user_id in booksy auth database
                user_id='user_uuid',
                expired=datetime(2021, 12, 1, 11, 12, 0).isoformat(),
                origin=AuthOriginEnum.BOOKSY,
            )
        )
        self.assertEqual(response.code, status.HTTP_200_OK)
        self._assert_called_with_new_session_key(patched_session_exists)

    def test_check_session_none_existing_user_local(self):
        response, patched_session_exists = self._get_response_with_patch(
            return_value=SessionExistsResponse(
                session_exists=True,
                # local id for user
                country_user_id=3_000_000,
                # under user_id id in booksy auth database
                user_id='user_uuid',
            )
        )
        self.assertEqual(response.code, status.HTTP_403_FORBIDDEN)
        self._assert_called_with_new_session_key(patched_session_exists)

    def test_check_session_none_existing_user_global(self):
        response, patched_session_exists = self._get_response_with_patch(
            side_effect=BooksyAuthValidationError(
                errors=dict(
                    user_doesnt_exist=['User doesn\'t  exists'],
                ),
            )
        )
        self.assertEqual(response.code, status.HTTP_403_FORBIDDEN)
        self._assert_called_with_new_session_key(patched_session_exists)

    def _test_check_session_booksy_auth_down(self, status_code):
        response, patched_session_exists = self._get_response_with_patch(
            side_effect=BooksyAuthServiceException('foo', 'bar'),
        )
        assert response.code == status_code
        self._assert_called_with_new_session_key(patched_session_exists)

    @pytest.mark.xfail  # TODO: remove after manual tests
    def test_check_session_booksy_auth_down_failure(self):
        self._test_check_session_booksy_auth_down(status.HTTP_403_FORBIDDEN)

    @pytest.mark.xfail  # TODO: remove after manual tests
    def test_check_session_booksy_auth_down_success(self):
        self.new_session_key = self.session.session_key
        self._test_check_session_booksy_auth_down(status.HTTP_200_OK)

    def test_check_session_request_errors(self):
        response, patched_session_exists = self._get_response_with_patch(
            side_effect=BooksyAuthValidationError(
                errors=dict(
                    validation=['session_key is required'],
                )
            )
        )
        self.assertEqual(response.code, status.HTTP_403_FORBIDDEN)
        self._assert_called_with_new_session_key(patched_session_exists)
