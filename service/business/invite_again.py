from bo_obs.datadog.enums import BooksyTeams
from django.utils.translation import gettext_lazy as _

from lib.db import (
    retry_on_sync_error,
)
from service.business.serializers.invite_again import (
    InviteAgainInviteSerializer,
    InviteAgainSearchSerializer,
)
from service.business.utils import InviteAgainImportCacheMixin
from service.tools import AnalyticsTokensMixin, RequestHandler, json_request, session
from webapps.business.models import Resource
from webapps.business.tasks import (
    invite_again_existing_customers_task,
)
from webapps.segment.serializers import InviteAnalyticsRequestSerializer


class InviteAgainBusinessCustomerInfoListHandler(
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    InviteAgainImportCacheMixin,
    AnalyticsTokensMixin,
):
    # pylint: disable=too-many-ancestors
    """Handler responsible for creation and listing of
    invite again BusinessCustomers."""

    booksy_teams = (BooksyTeams.PROVIDER_MARKETING, BooksyTeams.PROVIDER_ONBOARDING)
    default_per_page_setting = 'BUSINESS_CUSTOMER_INFOS_PER_PAGE'

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Get list of BusinessCustomers possible to invite again.
            type: InviteAgainListing
            parameters:
                - name: business_id
                  description: Business ID
                  type: integer
                  paramType: path
                - name: query
                  description: Search by full name, cell phone number or email
                  type: string
                  paramType: query
                - name: page
                  type: integer
                  paramType: query
                  description: page to load
                  defaultValue: 1
                - name: per_page
                  type: integer
                  paramType: query
                  description: number of items per page
                  defaultValue: 20
        :swagger
        """
        business = self.business_with_staffer(business_id)
        request_data = self._prepare_get_arguments(list_values=['tags'])
        request_data['business_id'] = business.id

        serializer = InviteAgainSearchSerializer(
            data=request_data,
            context={
                'business': business,
            },
        )
        data = self.validate_serializer(serializer)

        page = data.pop('page')
        per_page = data.pop('per_page')
        start = per_page * (page - 1)
        size = per_page

        res = serializer.search(
            data=data,
            access_level=self.access_level,
            start=start,
            size=size,
        )

        # pylint: disable=duplicate-code
        response = {
            'customers': res.hits,
            'count': res.hits.total.value,
            'page': page,
            'per_page': per_page,
            'import_in_progress': self.get_import_in_progress(business_id),
            'invite_again_in_progress': self.get_invite_again_in_progress(
                business_id,
            ),
        }
        # pylint: enable=duplicate-code

        self.finish_with_json(200, response)

    @session(login_required=True)
    @json_request
    @retry_on_sync_error
    def post(self, business_id):
        """
        swagger:
            summary: Get list of BusinessCustomers possible to invite again.
            parameters:
                - name: business_id
                  description: Business ID
                  type: integer
                  paramType: path
                - name: query
                  description: Search query
                  type: string
                  paramType: body
                - name: invite_all
                  description: Search by full name, cell phone number or email
                  type: string
                  paramType: body
                - name: bcis
                  description: List of client card ids to invite
                  type: array
                  items:
                    type: integer
                  paramType: body
            type: InviteAgainListingInviteResponse
        :swagger
        """
        business = self.business_with_staffer(business_id)
        if not business.active:
            self.finish_with_json(
                400,
                {
                    'error': _('Your subscription has expired.'),
                },
            )
            return
        serializer = InviteAgainInviteSerializer(
            data=self.data,
            context={
                'business': business,
                'access_level': self.access_level,
            },
        )
        validated_data = self.validate_serializer(serializer)
        task_id = None
        if bcis := validated_data.get('bcis'):
            self.set_invite_again_in_progress(business_id)
            task_result = invite_again_existing_customers_task.delay(
                bcis=bcis,
                business_id=business_id,
                invite_all=validated_data.get('invite_all'),
                logged_in_user_id=self.user.id,
                firebase_analytics_data=InviteAnalyticsRequestSerializer.parse_analytics_data(
                    booking_source_id=self.booking_source.id,
                    data=self.extend_data_with_firebase_auth(self.data),
                ),
                staffer_invite=(self.access_level in Resource.STAFF_ACCESS_LEVELS_NOT_OWNER),
            )
            if task_result:
                task_id = task_result.task_id
        self.finish_with_json(
            200,
            {
                'invite_count': len(bcis or []),
                'task_id': task_id,
            },
        )
