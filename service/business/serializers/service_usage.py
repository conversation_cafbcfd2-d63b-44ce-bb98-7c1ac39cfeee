from rest_framework import serializers


class ServiceVariantVoucherInfoSerializer(serializers.Serializer):

    id = serializers.IntegerField(read_only=True)
    active = serializers.BooleanField(read_only=True)


class ServiceVoucherUsageInfoSerializer(serializers.Serializer):

    voucher_template_id = serializers.IntegerField(read_only=True)
    service_variants = ServiceVariantVoucherInfoSerializer(many=True)
