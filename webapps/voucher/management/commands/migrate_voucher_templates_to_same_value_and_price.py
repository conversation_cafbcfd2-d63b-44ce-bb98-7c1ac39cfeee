from django.core.management import BaseCommand

from lib.tools import chunker
from webapps.voucher.models import VoucherTemplate
from webapps.voucher.tasks import migrate_vouchers_templates_to_same_value_and_price


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument('--async', action='store_true')
        parser.add_argument('--dry-run', action='store_true')

    def handle(self, *args, **opts):
        business_ids = VoucherTemplate.objects.filter(
            deleted__isnull=True,
        ).values_list('pos__business_id', flat=True)
        for businesses_ids_chunk in chunker(business_ids, 100):
            if opts.get('async'):
                migrate_vouchers_templates_to_same_value_and_price.delay(
                    businesses_ids=businesses_ids_chunk,
                    dry_run=opts.get('dry_run'),
                )
            else:
                migrate_vouchers_templates_to_same_value_and_price.run(
                    businesses_ids=businesses_ids_chunk,
                    dry_run=opts.get('dry_run'),
                )
