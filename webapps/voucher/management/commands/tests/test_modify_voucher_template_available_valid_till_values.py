from dateutil.relativedelta import relativedelta
from django.test import TestCase, override_settings
from model_bakery import baker
from parameterized import parameterized

from country_config import Country
from lib.tools import tznow
from webapps.business.baker_recipes import (
    business_recipe,
)
from webapps.pos.baker_recipes import pos_recipe
from webapps.voucher.baker_recipes import voucher_template_recipe
from webapps.voucher.enums import OrderingVoucherTemplateValidTill
from webapps.voucher.management.commands.modify_voucher_template_available_valid_till_values import (  # pylint: disable=line-too-long
    Command,
)
from webapps.voucher.models import (
    VoucherOrder,
    Voucher,
    VoucherTemplate,
)


class TestChangeValidTillVoucherTemplateCommand(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        business = business_recipe.make()
        cls.pos = pos_recipe.make(business=business)

    @parameterized.expand(
        (
            (
                OrderingVoucherTemplateValidTill.DAYS_30.value,
                OrderingVoucherTemplateValidTill.MONTHS_6.value,
            ),
            (
                OrderingVoucherTemplateValidTill.DAYS_90.value,
                OrderingVoucherTemplateValidTill.MONTHS_6.value,
            ),
            (
                OrderingVoucherTemplateValidTill.END_OF_MONTH.value,
                OrderingVoucherTemplateValidTill.MONTHS_6.value,
            ),
            (
                OrderingVoucherTemplateValidTill.END_OF_YEAR.value,
                OrderingVoucherTemplateValidTill.YEAR_1.value,
            ),
            (
                OrderingVoucherTemplateValidTill.UNLIMITED.value,
                OrderingVoucherTemplateValidTill.MONTHS_24.value,
            ),
            (
                OrderingVoucherTemplateValidTill.MONTHS_6.value,
                OrderingVoucherTemplateValidTill.MONTHS_6.value,
            ),
            (
                OrderingVoucherTemplateValidTill.MONTHS_9.value,
                OrderingVoucherTemplateValidTill.MONTHS_9.value,
            ),
            (
                OrderingVoucherTemplateValidTill.YEAR_1.value,
                OrderingVoucherTemplateValidTill.YEAR_1.value,
            ),
            (
                OrderingVoucherTemplateValidTill.MONTHS_24.value,
                OrderingVoucherTemplateValidTill.MONTHS_24.value,
            ),
        )
    )
    @override_settings(API_COUNTRY=Country.FR)
    def test_change_valid_till_inside_fr(self, original_valid_till, expected_valid_till):
        no_change_choices = [
            OrderingVoucherTemplateValidTill.MONTHS_6.value,
            OrderingVoucherTemplateValidTill.MONTHS_9.value,
            OrderingVoucherTemplateValidTill.YEAR_1.value,
            OrderingVoucherTemplateValidTill.MONTHS_24.value,
        ]
        vt1 = voucher_template_recipe.make(
            pos=self.pos,
            name=f'Voucher {original_valid_till}',
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            valid_till=original_valid_till,
        )

        assert vt1.active
        assert VoucherTemplate.objects.filter(valid_till=original_valid_till, active=True)

        Command().handle()

        vt1.refresh_from_db()
        if original_valid_till in no_change_choices:
            assert VoucherTemplate.objects.filter(valid_till=original_valid_till, active=True)
            assert vt1.active
            assert not vt1.deleted
            assert VoucherTemplate.objects.filter(name=vt1.name).count() == 1
            assert (
                VoucherTemplate.objects.filter(name=vt1.name, active=True).first().valid_till
                == expected_valid_till
            )
        else:
            assert not VoucherTemplate.objects.filter(valid_till=original_valid_till, active=True)
            assert not vt1.active
            assert not vt1.deleted
            assert VoucherTemplate.objects.filter(name=vt1.name).count() == 2
            assert (
                VoucherTemplate.objects.filter(name=vt1.name, active=True).first().valid_till
                == expected_valid_till
            )

    @override_settings(API_COUNTRY=Country.US)
    def test_change_valid_till_outside_fr(self):
        #   only UNLIMITED case is tested because that is the only case which behaviour is
        #   different than inside FR for, the rest valid_till values behaves like inside FR
        vt1 = voucher_template_recipe.make(
            pos=self.pos,
            name='Voucher UNLIMITED',
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            valid_till=OrderingVoucherTemplateValidTill.UNLIMITED.value,
        )

        assert vt1.active
        assert VoucherTemplate.objects.filter(
            valid_till=OrderingVoucherTemplateValidTill.UNLIMITED.value, active=True
        )

        Command().handle()

        vt1.refresh_from_db()
        assert VoucherTemplate.objects.filter(
            valid_till=OrderingVoucherTemplateValidTill.UNLIMITED.value, active=True
        )
        assert vt1.active
        assert not vt1.deleted
        assert VoucherTemplate.objects.filter(name=vt1.name).count() == 1
        assert (
            VoucherTemplate.objects.filter(name=vt1.name, active=True).first().valid_till
            == OrderingVoucherTemplateValidTill.UNLIMITED.value
        )

    def test_change_templates_valid_till_value_and_skip_updating_related_orders(self):
        vt4 = voucher_template_recipe.make(
            pos=self.pos,
            name='Voucher 30 days',
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            valid_till=OrderingVoucherTemplateValidTill.DAYS_30.value,
        )
        vo4 = baker.make(VoucherOrder, voucher_template=vt4)

        assert vt4.active
        assert VoucherTemplate.objects.filter(active=True)

        Command().handle()

        vt4.refresh_from_db()
        vo4.refresh_from_db()
        assert not vt4.active
        assert not vt4.deleted
        assert VoucherTemplate.objects.filter(name=vt4.name).count() == 2
        assert (
            VoucherTemplate.objects.filter(name=vt4.name, active=True).first().valid_till
            == OrderingVoucherTemplateValidTill.MONTHS_6.value
        )
        # not finished order template stick with the inactive templates after script execution
        assert vo4.voucher_template == vt4
        assert vo4.voucher_template.valid_till == OrderingVoucherTemplateValidTill.DAYS_30.value

    def test_change_templates_valid_till_value_but_skip_updating_orders_and_vouchers(self):
        vt5 = voucher_template_recipe.make(
            pos=self.pos,
            name='Voucher 30 days',
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            valid_till=OrderingVoucherTemplateValidTill.DAYS_30.value,
        )
        vo5 = baker.make(
            VoucherOrder,
            voucher_template=vt5,
            voucher=baker.make(
                Voucher,
                pos=self.pos,
                voucher_template=vt5,
                valid_till=tznow().date() + relativedelta(days=30),
            ),
        )
        voucher5 = vo5.voucher

        assert vt5.active
        assert VoucherTemplate.objects.filter(active=True)

        Command().handle()

        vt5.refresh_from_db()
        vo5.refresh_from_db()
        voucher5.refresh_from_db()
        assert not vt5.active
        assert not vt5.deleted
        assert VoucherTemplate.objects.filter(name=vt5.name).count() == 2
        assert (
            VoucherTemplate.objects.filter(name=vt5.name, active=True).first().valid_till
            == OrderingVoucherTemplateValidTill.MONTHS_6.value
        )
        # already sold voucher should not change template and stick with previous one
        assert vo5.voucher_template == vt5
        assert voucher5.valid_till == tznow().date() + relativedelta(days=30)

    def test_change_valid_till_failed_because_not_gift_card(self):
        vt6 = voucher_template_recipe.make(
            pos=self.pos,
            name='Package 30 days',
            type=Voucher.VOUCHER_TYPE__PACKAGE,
            valid_till=OrderingVoucherTemplateValidTill.DAYS_30.value,
        )

        assert vt6.active
        assert VoucherTemplate.objects.filter(active=True)

        Command().handle()

        vt6.refresh_from_db()
        assert VoucherTemplate.objects.filter(active=True)
        assert vt6.active
        assert not vt6.deleted
        assert VoucherTemplate.objects.filter(name=vt6.name).count() == 1
        assert (
            VoucherTemplate.objects.filter(name=vt6.name).first().valid_till
            == OrderingVoucherTemplateValidTill.DAYS_30.value
        )
