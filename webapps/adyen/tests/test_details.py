import pytest
import requests
import responses
from django.conf import settings
from model_bakery import baker
from simplejson import JSONDecodeError

from webapps.adyen import flow
from webapps.adyen.models import (
    Auth,
    Card,
    Cardholder,
)
from webapps.adyen.tests import (
    mock_details_success,
    mock_details_empty,
    mock_details_fail,
)


@pytest.mark.django_db
@responses.activate
def test_adyen_details_success():
    mock_details_success()

    card = baker.make(Card, cardholder=baker.make(Cardholder))
    baker.make(Auth, psp_reference='8514685931206172', card=card)

    card = baker.make(Card, cardholder=baker.make(Cardholder))
    baker.make(Auth, psp_reference='8514685937695014', card=card)

    result = flow.details('qwertyuioplkjhgfdswertyu')

    assert result is True

    auth = Auth.objects.get(psp_reference='8514685931206172')
    assert auth.card.expiry_month == 8
    assert auth.card.expiry_year == 2018
    assert auth.card.cardholder.name == '<PERSON>'
    assert auth.card.brand == 'bijcard'
    assert auth.card.bin_num == '510008'
    assert auth.card.last_4_digits == '3332'
    assert auth.card.recurr_detail_ref == '8414685931212874'

    auth = Auth.objects.get(psp_reference='8514685937695014')
    assert auth.card.expiry_month == 8
    assert auth.card.expiry_year == 2018
    assert auth.card.cardholder.name == 'John Doe'
    assert auth.card.brand == 'mc'
    assert auth.card.bin_num == '510029'
    assert auth.card.last_4_digits == '2909'
    assert auth.card.recurr_detail_ref == '8314685937691650'


@responses.activate
def test_adyen_details_fail():
    mock_details_fail()
    with pytest.raises(JSONDecodeError):
        flow.details('qwertyuioplkjhgfdswertyu')


@responses.activate
@pytest.mark.django_db
@pytest.mark.parametrize(
    'exc',
    [
        requests.exceptions.HTTPError,
        requests.exceptions.ConnectionError,
        requests.exceptions.ConnectTimeout,
        requests.exceptions.ReadTimeout,
        requests.exceptions.RequestException,
    ],
)
def test_http_errs(exc):
    responses.add(responses.POST, settings.ADYEN_DETAILS_URL, exc())

    card = baker.make(Card, cardholder=baker.make(Cardholder))
    baker.make(Auth, psp_reference='8514685937695014', card=card)

    with pytest.raises(exc):
        flow.details('qwertyuioplkjhgfdswertyu')


@pytest.mark.django_db
@responses.activate
def test_adyen_details_empty():
    mock_details_empty()
    result = flow.details('3cc618ba389d471995f0168c849c6243')
    assert result is True
