# Generated by Django 2.0.13 on 2019-10-23 08:24

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('adyen', '0012_use_marketpay'),
    ]

    operations = [
        migrations.CreateModel(
            name='AdyenRequestLog',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True)),
                (
                    'request',
                    django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=dict),
                ),
                (
                    'response',
                    django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=dict),
                ),
            ],
        ),
    ]
