import csv
import logging
import io

import requests
from webapps.pos.models import Transaction
from django.conf import settings

log = logging.getLogger('booksy.adyen_report_parser')


def get_report(loc):
    """
    Download report.

    Args:
        loc: Report url.
    """
    report_file = requests.get(
        loc,
        auth=(settings.ADYEN_REPORT_USER, settings.ADYEN_REPORT_PASS),
        timeout=60.0,
    )
    return io.StringIO(report_file.text)


def parse_settlement_detail_report_csv(report):
    """
    Parse csv settlement detail report.

    Args:
        report: csv report file.
    """
    settled_refs = []
    reader = csv.DictReader(report)
    for row in reader:
        if row['Type'] == 'Settled':
            settled_refs.append(row['Merchant Reference'])

    txns = Transaction.objects.filter(receipts__pnref__in=settled_refs)
    txns.update(ready_for_settle=True)

    if txns.count() < len(settled_refs):
        refs_in_db = txns.values_list('receipts__pnref', flat=True)
        missing_refs = [r for r in settled_refs if r not in refs_in_db]
        log.warning('Settled txns not found: %s', missing_refs)
