import logging

from datetime import timed<PERSON><PERSON>
from typing import List, Iterable

from django.db.models import Count
from django.utils.translation import gettext_lazy as _

from country_config import Country
from lib.celery_tools import celery_task, post_transaction_task
from lib.db import using_db_for_reads, READ_ONLY_DB
from lib.email import send_email
from lib.jinja_renderer import ScenariosJinjaRenderer
from lib.tools import tznow
from webapps.admin_extra.notifications import BulkTransferFundSummary
from webapps.adyen.typing import FundTransferData
from webapps.pos.provider.typing import AuthorisationResult


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def booksy_adyen_watchdog(recipients: List[str]):
    """
    Celery periodic task health-checking Booksy-Adyen integration, sending
    email to provided `recipients` email list.

    The content of the sent email includes:
    * number of received AdyenNotifictions per notification event_code value
    * number of registered payouts

    It is up to email receivers to deduct from the email contents if there are
    any problems with our payment provider integration.
    """
    from django.conf import settings
    from webapps.adyen.models import Notification
    from webapps.market_pay.models import Payout
    from webapps.user.models import UserProfile, User

    allowed_api_countries = (
        Country.US,
        Country.PL,
        Country.GB,
        Country.IE,
        Country.BR,
        Country.ES,
        Country.ZA,
    )
    if settings.API_COUNTRY not in allowed_api_countries:
        return

    yesterday = tznow().date() - timedelta(days=1)

    adyen_notifications_count_per_type = list(
        Notification.objects.filter(
            created__date=yesterday,
        )
        .values(
            'event_code',
        )
        .annotate(
            notification_count=Count('id'),
        )
        .order_by(
            'event_code',
            'notification_count',
        )
        .values_list(
            'event_code',
            'notification_count',
        )
    )

    payouts_count = Payout.objects.filter(
        created__date=yesterday,
    ).count()

    for recipient in User.objects.filter(email__in=recipients):
        sjr = ScenariosJinjaRenderer()
        body = sjr.render(
            scenario_name='adyen_report',
            template_name='adyen_integration_daily_report',
            language=recipient.get_language(UserProfile.Type.BUSINESS),
            template_args={
                'api_country': settings.API_COUNTRY,
                'recipient_name': recipient.first_name,
                'report_date': yesterday,
                'adyen_notifications': adyen_notifications_count_per_type,
                'payouts_count': payouts_count,
            },
        )
        send_email(
            to_addr=recipient.email,
            subject=_('Daily Adyen integration report'),
            body=body,
        )


@post_transaction_task
def capture_authorized_transaction_task(
    payment_card_type: str,
    payment_card_last_digits: str,
    payment_provider_codename: str,
    transaction_id: int,
    auth_result: AuthorisationResult,
):
    from webapps.pos.enums import receipt_status
    from webapps.pos.models import Transaction
    from webapps.pos.provider import get_payment_provider
    from webapps.pos.models import PaymentRow

    transaction = Transaction.objects.get(id=transaction_id)
    provider = get_payment_provider(
        codename=payment_provider_codename,
        txn=transaction,
    )

    try:
        provider.make_capture(
            payment_card_type=payment_card_type,
            payment_card_last_digits=payment_card_last_digits,
            payment_row=transaction.latest_receipt.payment_rows.get(
                status__in=(
                    receipt_status.AUTHORIZED_STATUSES + receipt_status.PENDING_3DS_STATUSES
                ),
            ),
            auth_result=auth_result,
        )
    except PaymentRow.DoesNotExist as e:
        log = logging.getLogger('booksy.adyen_flow')
        error_msg = (
            f'capture_authorized_transaction_task: transaction_id: {transaction.id},'
            f'latest_receipt_id:  {transaction.latest_receipt_id}, ex: {e}'
        )
        log.error(error_msg)

        raise e


@celery_task
def handle_report_task(report_location):
    from webapps.adyen.reports import (
        get_report,
        parse_settlement_detail_report_csv,
    )

    log = logging.getLogger('booksy.adyen_notifications')
    log.info('Getting %s report...', report_location)
    report = get_report(report_location)

    log.info('Parsing %s report...', report_location)
    parse_settlement_detail_report_csv(report)


@celery_task(time_limit=31 * 60, soft_time_limit=30 * 60)
def bulk_transfer_funds(
    operator_id: int,
    transfer_funds_data: Iterable[FundTransferData],
) -> None:
    from webapps.admin_extra.views.adyen import ManualTransferFundView
    from webapps.user.models import User

    errors = []
    fund_transfers = []
    for entry_line, entry in enumerate(transfer_funds_data, 2):
        try:
            fund_transfers.append(
                ManualTransferFundView.send_manual_fund_transfer(
                    source_account_code=entry['source_account_code'],
                    destination_account_code=entry['destination_account_code'],
                    source_account_description=entry['source_account_description'],
                    destination_account_description=entry['destination_account_description'],
                    amount=entry['amount'],
                )
            )
        except Exception as e:  # pylint: disable=broad-except
            errors.append(f'Error when sending FundTransfer ' f'for line {entry_line}: {e}')
    BulkTransferFundSummary(
        event_sender=None,
        user=User.objects.get(id=operator_id),
        successful_fund_transfer_count=len(fund_transfers),
        successful_fund_transfer_ids=[ft.id for ft in fund_transfers],
        errors=errors,
    ).send()


@celery_task
def cancel_outdated_3ds_bookings() -> None:
    """
    This task searches for bookings that have their related transactions
    "stuck" in one of the 3DSecure-related statuses, and cancels them.
    """
    from webapps.adyen.consts import oper_result
    from webapps.adyen.consts.timeouts import THREE_D_SECURE_TIMEOUT
    from webapps.adyen.enums import ThreeDSecureReceiptStatuses
    from webapps.adyen.helpers import cancel_appointment_after_failed_payment
    from webapps.pos.enums import receipt_status
    from webapps.pos.models import Transaction, PaymentRow, PaymentRowChange

    three_d_statuses = ThreeDSecureReceiptStatuses.all_values()

    for transaction in Transaction.objects.filter(
        latest_receipt__status_code__in=three_d_statuses,
        deleted__isnull=True,
        updated__lt=tznow() - timedelta(seconds=THREE_D_SECURE_TIMEOUT),
    ):
        payment_row: PaymentRow = transaction.latest_receipt.payment_rows.filter(
            status__in=three_d_statuses,
        ).first()
        new_payment_row_status = ThreeDSecureReceiptStatuses.failed_flow_statuses[
            payment_row.status
        ]
        payment_row.update_status(
            status=new_payment_row_status,
            oper_result=oper_result.FAILED,
            marketpay_splits=payment_row.get_marketpay_splits(),
            log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
            log_note='Failed due to 3DSecure operation timeout',
        )
        if new_payment_row_status == receipt_status.PREPAYMENT_FAILED:
            cancel_appointment_after_failed_payment(transaction)
