import pytest

from webapps.family_and_friends.baker_recipes import member_with_relations
from webapps.family_and_friends.models import MemberProfile
from webapps.family_and_friends.serializers.member import UnlinkSerializer


@pytest.fixture(name='member_profile')
def make_member_with_parent_and_child() -> MemberProfile:
    return member_with_relations.make()


def make_context(profile: MemberProfile) -> dict:
    return {
        'member_profile': profile,
    }


@pytest.mark.django_db
def test_invalid_without_member_or_parent(member_profile):
    serializer = UnlinkSerializer(member_profile, {})
    assert not serializer.is_valid()


@pytest.mark.django_db
def test_invalid_both_member_and_parent(member_profile):
    serializer = UnlinkSerializer(
        member_profile,
        data={
            'parent_id': member_profile.parents.first().id,
            'member_id': member_profile.members.first().id,
        },
    )
    assert not serializer.is_valid()


@pytest.mark.django_db
def test_valid_parent(member_profile):
    expected_parent = member_profile.parents.first()
    serializer = UnlinkSerializer(
        member_profile,
        data={'parent_id': expected_parent.id},
    )

    assert serializer.is_valid(), serializer.errors
    assert serializer.validated_data['parent'] == expected_parent


@pytest.mark.django_db
def test_valid_member(member_profile):
    expected_member = member_profile.members.first()
    serializer = UnlinkSerializer(
        member_profile,
        data={'member': expected_member.id},
    )
    assert serializer.is_valid()
    assert serializer.validated_data['member'] == expected_member


@pytest.mark.django_db
def test_invalid_parent(member_profile):
    serializer = UnlinkSerializer(
        member_profile,
        data={'parent_id': member_profile.parents.last().id + 1234},
    )
    assert not serializer.is_valid()


@pytest.mark.django_db
def test_invalid_member(member_profile):
    serializer = UnlinkSerializer(
        member_profile,
        data={'member': member_profile.members.last().id + 1234},
    )

    assert not serializer.is_valid()
