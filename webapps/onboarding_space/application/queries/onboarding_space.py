from v2.domain.interfaces.settings import Settings<PERSON>rovider
from v2.infrastructure.translation import Translator
from webapps.onboarding_space.application.repositories import OnboardingSpaceAbstractRepository
from webapps.onboarding_space.application.translations import TRANSLATION_MAP
from webapps.onboarding_space.domain.dtos import (
    OnboardingSpaceStepsDTO,
    OnboardingStepDTO,
)
from webapps.onboarding_space.domain.models import BusinessContext
from webapps.onboarding_space.domain.ports import (
    OnboardingSpaceAppointmentsPort,
    OnboardingSpaceBusinessPort,
)
from webapps.onboarding_space.domain.services.onboarding_space import (
    OnboardingSpaceAbstractService,
    PROFILE_SETUP_STEPS,
    CUSTOMER_ENGAGEMENT_STEPS,
)


# pylint: disable=too-many-arguments, too-many-positional-arguments
class OnboardingSpaceQueriesService:
    """Service for querying onboarding space data"""

    CONTEXT = 'onboarding_space'

    def __init__(
        self,
        repository: OnboardingSpaceAbstractRepository,
        service: OnboardingSpaceAbstractService,
        appointment_adapter: OnboardingSpaceAppointmentsPort,
        business_adapter: OnboardingSpaceBusinessPort,
        translator: <PERSON><PERSON>,
        settings_provider: SettingsProvider,
    ):
        self.repository = repository
        self.service = service
        self.appointment_adapter = appointment_adapter
        self.business_adapter = business_adapter
        self.translator = translator
        self.settings_provider = settings_provider

    def _translate_steps(self, steps: list[OnboardingStepDTO]) -> list[OnboardingStepDTO]:
        translated_steps = []
        for step in steps:
            if translation_map := TRANSLATION_MAP.get(step.field_text):
                translated_field_text = self.translator.gettext_plural(
                    singular=translation_map['singular'],
                    plural=translation_map['plural'],
                    number=step.value,
                    context=self.CONTEXT,
                ).format(number=step.value)
            else:
                translated_field_text = self.translator.gettext(step.field_text, self.CONTEXT)
            translated_name = self.translator.gettext(step.name, self.CONTEXT)
            translated_steps.append(
                OnboardingStepDTO(
                    name=translated_name,
                    target_type=step.target_type,
                    is_completed=step.is_completed,
                    value=step.value,
                    field_text=translated_field_text,
                    color=step.color,
                )
            )
        return translated_steps

    def get_onboarding_space_by_business(self, business_id: int) -> OnboardingSpaceStepsDTO:
        onb_space = self.repository.get(business_id)

        if onb_space is None:
            return OnboardingSpaceStepsDTO(
                show_space=False,
                force_redirect=False,
                is_experiment=False,
                progress_percent=0,
                profile_setup_steps=[],
                client_engagement_steps=[],
            )
        business_category = self.business_adapter.get_business_category_name(business_id)
        business_context = BusinessContext(
            business_category=business_category,
            country_code=self.settings_provider.get_settings().api_country,
        )

        onb_completion = self.service.check_completion(onb_space.steps, business_context)
        appointment_count = (
            self.appointment_adapter.get_number_of_customer_appointments_for_business(
                business_id, onb_space.active_from
            )
        )

        profile_setup_steps = self.service.generate_steps(
            [step for step in onb_space.steps if step.type in PROFILE_SETUP_STEPS], business_context
        )
        profile_setup_steps = self._translate_steps(profile_setup_steps)

        client_engagement_steps = self.service.generate_steps(
            [step for step in onb_space.steps if step.type in CUSTOMER_ENGAGEMENT_STEPS],
            business_context,
        )
        client_engagement_steps = self._translate_steps(client_engagement_steps)

        return OnboardingSpaceStepsDTO(
            show_space=self.service.check_show_space(onb_space, onb_completion, appointment_count),
            force_redirect=self.service.check_force_redirect(onb_space, onb_completion),
            is_experiment=True,
            progress_percent=onb_completion.percentage_completed,
            profile_setup_steps=profile_setup_steps,
            client_engagement_steps=client_engagement_steps,
        )
