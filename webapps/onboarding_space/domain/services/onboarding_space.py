import datetime
from abc import abstractmethod, ABC
from dataclasses import dataclass

from lib.tools import tznow
from webapps.onboarding_space.domain.dtos import OnboardingStepDTO
from webapps.onboarding_space.domain.enums import (
    ProfileSetupStepsEnum,
    CompletionColorsEnum,
    TextTemplatesEnum,
    CountryCode,
    BusinessCategoryEnum,
)

from webapps.onboarding_space.domain.models import (
    OnboardingSpaceProgress,
    Step,
    ColorCodedTextDTO,
    BusinessContext,
)
from webapps.onboarding_space.domain.services.settings import (
    FORCE_REDIRECT_DAYS_AFTER_MRC,
    DAYS_SHOW_SPACE_FROM_MRC,
    BOOKING_LIMIT_TO_HIDE_SPACE,
    COMPLETION_PERCENT,
    INVITE_LIMIT_TO_HIDE_SPACE,
    STEP_TO_TARGET_MAPPING,
    PORTFOLIO_THRESHOLD_COLOR_CONFIG,
    SERVICE_THRESHOLD_COLOR_CONFIG,
)
from webapps.onboarding_space.domain.types import ThresholdConfig

PROFILE_REGISTRATION_COMPLETED_STEP = Step(ProfileSetupStepsEnum.REGISTRATION_COMPLETED, True)

PROFILE_SETUP_STEPS = {
    ProfileSetupStepsEnum.ADD_COVER_PHOTO,
    ProfileSetupStepsEnum.ADD_PORTFOLIO,
    ProfileSetupStepsEnum.ADD_SERVICES,
}

CUSTOMER_ENGAGEMENT_STEPS = {
    ProfileSetupStepsEnum.IMPORT_AND_INVITE,
    ProfileSetupStepsEnum.SHARE_PROFILE,
    ProfileSetupStepsEnum.CREATE_POST,
}


@dataclass(frozen=True)
class OnboardingSpaceCompletion:
    """Container for onboarding space completion data
    It should verify integer based steps completion and return overall completion percentage
    """

    percentage_completed: int


class OnboardingSpaceAbstractService(ABC):
    @staticmethod
    @abstractmethod
    def check_force_redirect(
        onboarding_space: OnboardingSpaceProgress,
        onboarding_space_completion: OnboardingSpaceCompletion,
    ) -> bool:
        raise NotImplementedError

    @staticmethod
    @abstractmethod
    def check_show_space(
        onboarding_space: OnboardingSpaceProgress,
        onboarding_space_completion: OnboardingSpaceCompletion,
        appointment_count: int,
    ) -> bool:
        raise NotImplementedError

    @abstractmethod
    def check_completion(
        self, steps: list[Step], business_context: BusinessContext
    ) -> OnboardingSpaceCompletion:
        raise NotImplementedError

    @abstractmethod
    def generate_steps(
        self, steps: list[Step], business_context: BusinessContext
    ) -> list[OnboardingStepDTO]:
        raise NotImplementedError


class OnboardingSpaceDomainService(OnboardingSpaceAbstractService):

    @staticmethod
    def check_show_space(
        onboarding_space: OnboardingSpaceProgress,
        onboarding_space_completion: OnboardingSpaceCompletion,
        appointment_count: int,
    ) -> bool:

        if tznow() < onboarding_space.active_from + datetime.timedelta(
            days=DAYS_SHOW_SPACE_FROM_MRC
        ):
            return True

        return (
            appointment_count < BOOKING_LIMIT_TO_HIDE_SPACE
            or onboarding_space_completion.percentage_completed < COMPLETION_PERCENT
            or onboarding_space.import_and_invite_count < INVITE_LIMIT_TO_HIDE_SPACE
        )

    @staticmethod
    def check_force_redirect(
        onboarding_space: OnboardingSpaceProgress,
        onboarding_space_completion: OnboardingSpaceCompletion,
    ) -> bool:
        """Checks if user should be force redirected to the onboarding space. For first phase of
        experiment business logic that assumes that on MRC+0 only second use of app should be
        redirected will be handled using frontends/cache"""
        if onboarding_space_completion.percentage_completed == COMPLETION_PERCENT:
            return False

        if tznow().date() in [
            (onboarding_space.active_from + datetime.timedelta(days=days_to_add)).date()
            for days_to_add in FORCE_REDIRECT_DAYS_AFTER_MRC
        ]:
            return True
        return False

    def check_completion(
        self, steps: list[Step], business_context: BusinessContext
    ) -> OnboardingSpaceCompletion:
        """
        Checks completion, for each step with yellow/gray color it adds 10%, for green 15%.
        Ensures a minimum percentage of 10% that represents registration completeness, even if no
        steps are completed.
        Maximum percentage is 100%.
        """
        percentage_completed = 10
        for step in steps:
            color_coded_text = self.get_value_and_color_for_step(step, business_context)
            if color_coded_text.color in [CompletionColorsEnum.YELLOW, CompletionColorsEnum.GRAY]:
                percentage_completed += 10
            elif color_coded_text.color == CompletionColorsEnum.GREEN:
                percentage_completed += 15

        return OnboardingSpaceCompletion(percentage_completed)

    @staticmethod
    def _get_color_based_on_threshold(
        business_context: BusinessContext, thresholds: ThresholdConfig, value: int
    ) -> CompletionColorsEnum:
        thresholds_for_business_category = (
            thresholds[business_context.business_category]
            if business_context.business_category in thresholds
            else thresholds[BusinessCategoryEnum.OTHERS]
        )
        thresholds_values = (
            thresholds_for_business_category[business_context.country_code]
            if business_context.country_code in thresholds_for_business_category
            else thresholds_for_business_category[CountryCode.ROW]
        )

        if value < thresholds_values[CompletionColorsEnum.RED]:
            return CompletionColorsEnum.RED
        if value <= thresholds_values[CompletionColorsEnum.GRAY]:
            return CompletionColorsEnum.GRAY
        return CompletionColorsEnum.GREEN

    @staticmethod
    def get_value_and_color_for_step(
        step: Step, business_context: BusinessContext
    ) -> ColorCodedTextDTO:

        match step.type:
            case ProfileSetupStepsEnum.IMPORT_AND_INVITE:
                color = (
                    CompletionColorsEnum.RED
                    if step.value < 3
                    else (
                        CompletionColorsEnum.YELLOW
                        if step.value < 15
                        else CompletionColorsEnum.GREEN
                    )
                )
                text = TextTemplatesEnum.NUMBER_OF_INVITES

            case ProfileSetupStepsEnum.CREATE_POST:
                color = (
                    CompletionColorsEnum.RED
                    if step.value == 0
                    else (
                        CompletionColorsEnum.YELLOW
                        if step.value == 1
                        else CompletionColorsEnum.GREEN
                    )
                )
                text = TextTemplatesEnum.NUMBER_OF_POSTS

            case ProfileSetupStepsEnum.SHARE_PROFILE:
                color = CompletionColorsEnum.GREEN if step.value else CompletionColorsEnum.RED
                text = "SHARED" if step.value else "NOT SHARED"

            case ProfileSetupStepsEnum.ADD_COVER_PHOTO:
                color = CompletionColorsEnum.GREEN if step.value else CompletionColorsEnum.RED
                text = "ADDED" if step.value else "NOT ADDED"

            case ProfileSetupStepsEnum.ADD_PORTFOLIO:
                color = OnboardingSpaceDomainService._get_color_based_on_threshold(
                    business_context, PORTFOLIO_THRESHOLD_COLOR_CONFIG, step.value
                )
                text = TextTemplatesEnum.NUMBER_OF_PORTFOLIO_PHOTOS

            case ProfileSetupStepsEnum.ADD_SERVICES:
                color = OnboardingSpaceDomainService._get_color_based_on_threshold(
                    business_context, SERVICE_THRESHOLD_COLOR_CONFIG, step.value
                )
                text = TextTemplatesEnum.NUMBER_OF_SERVICES

            case _:
                color = CompletionColorsEnum.RED
                text = str(step.value)

        return ColorCodedTextDTO(text, color)

    def generate_steps(
        self,
        steps: list[Step],
        business_context: BusinessContext,
    ) -> list[OnboardingStepDTO]:
        """
        Generates a list of OnboardingStepDTO objects based on the given steps.
        """
        result = []
        for step in steps:
            color_coded_text = self.get_value_and_color_for_step(step, business_context)
            dto = OnboardingStepDTO(
                name=step.type.label,
                target_type=STEP_TO_TARGET_MAPPING[step.type],
                is_completed=color_coded_text.color == CompletionColorsEnum.GREEN,
                value=int(step.value),
                field_text=color_coded_text.field_text,
                color=color_coded_text.color,
            )
            result.append(dto)
        return result
