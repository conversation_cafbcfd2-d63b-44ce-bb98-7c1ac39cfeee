from dataclasses import dataclass


@dataclass(frozen=True)
class OnboardingStepDTO:
    """Represents a step required to complete the business profile."""

    name: str
    target_type: str
    is_completed: bool
    value: int
    field_text: str
    color: str


@dataclass(frozen=True)
class OnboardingSpaceStepsDTO:
    """Domain model representing the Onboarding Space process."""

    show_space: bool
    force_redirect: bool
    is_experiment: bool
    progress_percent: int
    profile_setup_steps: list[OnboardingStepDTO]
    client_engagement_steps: list[OnboardingStepDTO]
