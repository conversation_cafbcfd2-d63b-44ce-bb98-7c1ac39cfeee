import json

from django.contrib import admin, messages
from django.shortcuts import redirect
from django.template.defaultfilters import truncatechars
from django.urls import re_path as url
from django.urls import reverse
from django.utils.html import escape, format_html
from django import forms
from django.core.exceptions import ValidationError

from lib.admin_helpers import BaseModelAdmin, NoAddMixin, NoDelMixin, ReadOnlyTabular, admin_link
from webapps.b2b_referral.admin import ArchiveModelReadOnlyMixin
from webapps.business.history_retriever import HistoryRetriever
from webapps.message_blast.enums import MessageBlastDateType
from webapps.message_blast.events import send_message_blast
from webapps.message_blast.forms import BlockedPhoneNumberForm
from webapps.message_blast.models import (
    BlockedPhoneNumber,
    CommonMessageBlastTemplate,
    MessageBlast,
    MessageBlastCategoryImage,
    MessageBlastGroup,
    MessageBlastImage,
    MessageBlastTemplate,
    MessageBlastTemplateHistory,
    SMSBlastMarketingConsent,
    MBSpamAnalyzerSettings,
)
from webapps.user.tools import get_user_from_django_request


class CommonMessageBlastTemplateForm(forms.ModelForm):
    class Meta:
        model = CommonMessageBlastTemplate
        fields = '__all__'

    def clean(self):
        cleaned_data = super().clean()
        date_day = cleaned_data.get('date_day')
        date_monday = cleaned_data.get('date_monday')
        date_month = cleaned_data.get('date_month')
        date_type = cleaned_data.get('date_type')

        if date_type == MessageBlastDateType.STRICT and (not date_day or not date_month):
            raise ValidationError(
                "You can't set 'date_type' as STRICT without 'date_month' and 'date_day'"
            )
        if date_type == MessageBlastDateType.FIRST_MONDAY and (not date_monday or not date_month):
            raise ValidationError(
                "You can't set 'date_type' as FIRST_MONDAY without 'date_month' and 'date_monday'"
            )

        return cleaned_data


class MessageBlastGroupAdmin(ArchiveModelReadOnlyMixin, NoAddMixin, BaseModelAdmin):
    list_display = ['title', 'order', 'internal_name', 'parent_group']


admin.site.register(MessageBlastGroup, MessageBlastGroupAdmin)


class CommonMessageBlastTemplateAdmin(
    ArchiveModelReadOnlyMixin,
    NoDelMixin,
    NoAddMixin,
    BaseModelAdmin,
):
    list_display = [
        'id',
        'name',
        'internal_name',
        'automated_status',
        'group',
        'link_to_group',
        'order',
    ]

    search_fields = [
        'child_templates__business__id',
    ]

    list_filter = [
        'internal_name',
        'automated_status',
    ]

    raw_id_fields = [
        'image',
    ]

    form = CommonMessageBlastTemplateForm

    def get_queryset(self, request):
        """Shows only base class."""
        return super().get_queryset(request).filter(messageblasttemplate__isnull=True)


admin.site.register(CommonMessageBlastTemplate, CommonMessageBlastTemplateAdmin)


class MessageBlastTemplateHistoryAdmin:
    @staticmethod
    def changes(obj):
        return HistoryRetriever(MessageBlastTemplateHistory).get_changes(obj)


class MessageBlastTemplateAdmin(
    ArchiveModelReadOnlyMixin,
    NoAddMixin,
    NoDelMixin,
    MessageBlastTemplateHistoryAdmin,
    BaseModelAdmin,
):
    readonly_fields = [
        'created',
        'updated',
        'deleted',
        'group',
        'link_to_group',
        'common_template',
        'business',
        'changes',
    ]

    list_display = [
        'id',
        'name',
        'internal_name',
        'automated_status',
        'link_to_group',
        'group',
        'order',
        'common_template',
        'business_id',
    ]

    search_fields = [
        'business__id',
    ]

    list_filter = [
        'internal_name',
        'automated_status',
    ]

    raw_id_fields = [
        'image',
    ]

    def __init__(self, *args, **kwargs):
        self.create_message_blast_btn = None
        super().__init__(*args, **kwargs)

    def get_form(self, request, obj=None, change=False, **kwargs):
        self.create_message_blast_btn = self.get_create_message_blast_btn(obj)
        return super().get_form(request, obj, **kwargs)

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<message_blast_template_id>\d+)/create_message_blast/$',
                self.admin_site.admin_view(self.create_message_blast),
                name='create_message_blast',
            ),
        ]
        return additional_urls + urls

    @staticmethod
    def get_create_message_blast_btn(obj):
        if obj is None:
            return ''
        return (
            '<a href="%s" class="btn">%s</a>',
            reverse('admin:create_message_blast', args=(obj.id,)),
            'Create message blast',
        )

    @staticmethod
    def create_message_blast(request, message_blast_template_id):
        message_blast_template = MessageBlastTemplate.objects.get(id=message_blast_template_id)
        operator_id = get_user_from_django_request(request).id
        metadata = {'endpoint': 'MessageBlastTemplateAdmin'}
        message_blast_template.create_blast(
            bcis=[1],
            _history={
                'operator': operator_id,
                'metadata': metadata,
                'handler': {'handler': 'admin_create_message_blast'},
            },
        )
        messages.success(request, 'Message blast was created.')
        return redirect(request.META.get('HTTP_REFERER', '/'))


admin.site.register(MessageBlastTemplate, MessageBlastTemplateAdmin)


class MessageBlastCategoryImageAdmin(
    ArchiveModelReadOnlyMixin,
    BaseModelAdmin,
):
    raw_id_fields = [
        'image',
    ]

    list_display = ['id', 'image_id', 'message_blast_group', 'category', 'template_internal_name']


admin.site.register(MessageBlastCategoryImage, MessageBlastCategoryImageAdmin)


class MessageBlastImageAdmin(
    ArchiveModelReadOnlyMixin,
    BaseModelAdmin,
):
    raw_id_fields = [
        'image',
    ]

    list_display = [
        'id',
        'image_id',
        'category',
        'template_internal_name',
        'order',
    ]

    search_fields = [
        'image_id',
    ]

    list_filter = [
        'category',
        'template_internal_name',
    ]


admin.site.register(MessageBlastImage, MessageBlastImageAdmin)


class MessageBlastAdmin(NoAddMixin, BaseModelAdmin):
    readonly_fields = [
        'created',
        'updated',
        'deleted',
        'sent',
        'channel_priority',
        'estimated_sms_cost',
        'title',
        'body',
        'body_short',
        'image',
        'out_of_sms',
        'template',
        'business',
        'with_agreements',
        'recipients_filter',
        'number_of_bcis',
        'sent_by',
        'date_hour',
    ]

    exclude = [
        'blast_send_type',
    ]

    list_display = [
        'id',
        'created',
        'business_id',
        'bcis',
        'number_of_bcis',
        'internal_name',
        'sent',
    ]

    search_fields = [
        '=business__id',
    ]

    list_filter = [
        'internal_name',
        'sent',
    ]

    def __init__(self, *args, **kwargs):
        self.send_message_blast_btn = None
        self.see_message_blast_bcis = None
        super().__init__(*args, **kwargs)

    def get_form(self, request, obj=None, change=False, **kwargs):
        self.send_message_blast_btn = self.get_send_message_blast_btn(obj)
        self.see_message_blast_bcis = self.see_message_blast_bcis_btn(obj)
        return super().get_form(request, obj, **kwargs)

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<message_blast_id>\d+)/send_message_blast/$',
                self.admin_site.admin_view(self.send_message_blast),
                name='send_message_blast',
            ),
        ]
        return additional_urls + urls

    @staticmethod
    def get_send_message_blast_btn(obj):
        if obj is None:
            return ''
        return (
            '<a href="%s" class="btn">%s</a>',
            reverse('admin:send_message_blast', args=(obj.id,)),
            'Send message blast',
        )

    @staticmethod
    def see_message_blast_bcis_btn(obj):
        if obj is None:
            return ''
        return format_html(
            '<a href="{0}?id__in={1}">{2}</a>',
            reverse(
                'admin:business_businesscustomerinfo_changelist',
            ),
            ','.join([str(bci_id) for bci_id in obj.bcis]),
            "List of customer cards",
        )

    @staticmethod
    def send_message_blast(request, message_blast_id):
        message_blast = MessageBlast.objects.get(id=message_blast_id)
        send_message_blast.send(message_blast)
        messages.success(request, 'Message blast sent.')

        return redirect(request.META.get('HTTP_REFERER', '/'))

    @staticmethod
    def business_id(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj.business), obj.business)

    @staticmethod
    def number_of_bcis(obj):
        return len(obj.bcis)

    @staticmethod
    def recipients_filter(obj):
        return format_html(
            '<pre><code>{}</code></pre>', json.dumps(obj.recipients_filter, indent=4)
        )


admin.site.register(MessageBlast, MessageBlastAdmin)


class BlockedPhoneNumberAdmin(BaseModelAdmin, ArchiveModelReadOnlyMixin):
    form = BlockedPhoneNumberForm

    list_display = ['id', 'created', 'deleted', 'cell_phone', 'service']

    list_filter = [
        'service',
    ]

    search_fields = [
        '=cell_phone',
    ]

    def delete_model(self, request, obj):
        """Does not delete row. Soft delete."""
        obj.soft_delete()
        obj.save()

        return obj


admin.site.register(BlockedPhoneNumber, BlockedPhoneNumberAdmin)


class SMSBlastMarketingConsentAdmin(ReadOnlyTabular, BaseModelAdmin):
    list_display = [
        'id',
        'business_link',
        'customer_info_link',
        'cell_phone',
        'consented',
        'consented_at',
    ]
    readonly_fields = [
        'business',
        'bci',
        'cell_phone',
        'consented',
        'consented_at',
        'created',
        'updated',
        'deleted',
    ]
    list_filter = [
        'consented',
        'consented_at',
    ]
    search_fields = [
        '=business__id',
        '=bci__id',
        '=cell_phone',
    ]
    list_select_related = True

    def business_link(self, obj):
        return obj.business.admin_id_link

    def customer_info_link(self, obj):
        if obj.bci is None:
            return '-'
        return format_html(
            '<a href="{}">{}</a>', admin_link(obj.bci), truncatechars(escape(obj.bci), 100)
        )


admin.site.register(SMSBlastMarketingConsent, SMSBlastMarketingConsentAdmin)


class MBSpamAnalyzerSettingsAdmin(BaseModelAdmin):

    list_display = [
        'id',
        'name',
        'ai_model',
        'explanation_for_ai',
        'ai_params',
        'explanation_for_response',
        'active',
    ]

    fieldsets = [
        (
            'Name',
            {
                'fields': ('name',),
                'description': '<div class="help">' 'Just name' '</div>',
            },
        ),
        (
            'Active',
            {
                'fields': ('active',),
                'description': '<div class="help"  style="color: green">'
                'Only last, active model will be used'
                '</div>',
            },
        ),
        (
            'Explanation Message (prompt)',
            {
                'fields': ('explanation_for_ai',),
                'description': '<div class="help" style="color: green">'
                'Explanation for AI how to check message'
                '</div>',
            },
        ),
        (
            'AI Config',
            {
                'fields': ('ai_params',),
                'description': "<div class='help' style='color: green'>"
                "Config. Please follow naming-convention <a href='https://"
                "cloud.google.com/vertex-ai/docs/reference/rpc/"
                "google.cloud.aiplatform.v1beta1#generationconfig'>doc</a>. "
                "Now allowed ONLY: ['candidate_count', 'stop_sequences', 'max_output_tokens',"
                " 'temperature','top_p','top_k','response_mime_type','response_schema',"
                "'presence_penalty','frequency_penalty'] "
                "Params related to objects are not allowed yet."
                "</div>",
            },
        ),
        (
            'AI Model',
            {
                'fields': ('ai_model',),
                'description': '<div class="help">' 'Model name for generation' '</div>',
            },
        ),
        (
            'Response Explanation',
            {
                'fields': ('explanation_for_response',),
                'description': "<div class='help' style='color: green'>"
                "Explanation of expected response from AI "
                "Please modify ONLY if you know what you are doing."
                "</div>",
            },
        ),
    ]


admin.site.register(MBSpamAnalyzerSettings, MBSpamAnalyzerSettingsAdmin)
