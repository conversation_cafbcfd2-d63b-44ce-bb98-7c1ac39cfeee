# pylint: disable=unused-argument
from datetime import datetime
from unittest.mock import patch

import freezegun
import pytest
from django.test import override_settings
from freezegun import freeze_time
from pytz import UTC

import settings.sms
from country_config import Country
from lib.feature_flag.feature import (
    ForceSmsReasonableHoursFlag,
)
from lib.tests.utils import override_feature_flag
from webapps.message_blast.enums import MessageBlastInternalNames
from webapps.message_blast.schedules import MessageBlastSendingSchedules
from webapps.message_blast.tools import get_reasonable_datetime


def test_message_blast_sending_schedule():
    assert MessageBlastSendingSchedules.get('wrong_key') is None

    assert list(MessageBlastSendingSchedules.keys()) == [
        MessageBlastInternalNames.WELCOME_NEW_CLIENT,
        MessageBlastInternalNames.INFORM_ABOUT_OTHER_SERVICES,
        MessageBlastInternalNames.PROMOTE_GIFT_CARDS,
        MessageBlastInternalNames.INVITE_FOR_VISIT,
        MessageBlastInternalNames.REINVITE_FREE_SERVICE,
        MessageBlastInternalNames.REINVITE_DISCOUNT,
        MessageBlastInternalNames.HAPPY_BIRTHDAY,
    ]


def test_message_blast_sending_schedule_default():
    for schedule in MessageBlastSendingSchedules.values():
        assert schedule.get_default_value()


def test_message_blast_sending_schedule_values():
    schedules = MessageBlastSendingSchedules.get(MessageBlastInternalNames.WELCOME_NEW_CLIENT)
    schedules_values = [schedule.number for schedule in schedules]
    assert (
        MessageBlastSendingSchedules.get(MessageBlastInternalNames.WELCOME_NEW_CLIENT).get_values()
        == schedules_values
    )


def test_message_blast_sending_schedule_exists():
    schedules = MessageBlastSendingSchedules.get(MessageBlastInternalNames.WELCOME_NEW_CLIENT)
    schedule_values_exists = [1, 2, 3, 5, 10, 24]
    for value in schedule_values_exists:
        assert schedules.exists(value) is True
    schedule_values_non_exists = [4, 6, 11, 25]
    for value in schedule_values_non_exists:
        assert schedules.exists(value) is False


@override_feature_flag(
    {
        ForceSmsReasonableHoursFlag.flag_name: True,
    }
)
@override_settings(
    API_COUNTRY=Country.FR,
    SMS_SETTINGS_PER_COUNTRY=settings.sms.SMS_SETTINGS_PER_COUNTRY,
)
@pytest.mark.parametrize(
    'schedule_datetime, result_date',
    [
        # below UTC time is used !!! - so +1 for Fr
        ([2023, 12, 25, 17, 0], [2023, 12, 26]),  # France has only 25.12 day off
        ([2023, 11, 5, 17, 0], [2023, 11, 6]),  # Sunday
        ([2023, 11, 6, 22, 5], [2023, 11, 7]),  # late evening
        ([2023, 11, 6, 4, 45], [2023, 11, 6]),  # early morning
        # sending time is close to "night start" in Fr 22:00 and there is a chance that all SMS
        # will not be sent within 5 min, so it takes next day:
        ([2023, 11, 6, 20, 55], [2023, 11, 7]),
    ],
)
@patch('webapps.message_blast.tools.is_time_adjust', return_value=True)
@patch('webapps.message_blast.tools.get_till_night_shift_minutes', return_value=60)
def test_get_reasonable_datetime(
    get_till_night_shift_minutes,
    is_time_adjust,
    schedule_datetime,
    result_date,
):
    with freeze_time(datetime(*schedule_datetime, tzinfo=UTC)):
        res = get_reasonable_datetime()
        assert res.date() == freezegun.api.FakeDate(*result_date)
