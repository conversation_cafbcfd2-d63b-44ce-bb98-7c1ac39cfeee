import pytest

from django.urls import reverse
from model_bakery import baker
from rest_framework import status

from webapps.admin_extra.tests import DjangoTestCase
from webapps.business.models import (
    Business,
)
from webapps.message_blast.enums import MessageBlastTimeType
from webapps.message_blast.models import (
    BlockedPhoneNumber,
    MessageBlast,
)
from webapps.notification.enums import NotificationService


class TestMessageBlastAdmin(DjangoTestCase):
    @pytest.mark.random_failure  # https://booksy.atlassian.net/browse/PY-2339
    def test_booking_count(self):
        self.login_admin()
        message_blast = baker.make(
            MessageBlast,
            business=baker.make(Business),
            date_hour=MessageBlastTimeType.EVENING,
            bcis=[1, 2, 3],
            recipients_filter={'sht': 'sth'},
        )

        url = reverse('admin:message_blast_messageblast_change', args=[message_blast.id])
        resp = self.client.get(url)
        content = resp.content.decode('utf-8')

        assert content is not None


class TestBlockedPhoneNumber(DjangoTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.url_add = reverse('admin:message_blast_blockedphonenumber_add')
        cls.cell_phone = '+***********'

    def setUp(self):
        super().setUp()
        self.login_admin()

    def test_add_blocked_phone_number_requires_service(self):
        post_data = {
            'cell_phone': self.cell_phone,
        }
        response = self.client.post(self.url_add, post_data, follow=True)
        form = response.context['adminform'].form
        self.assertFalse(form.is_valid())
        self.assertDictEqual(form.errors, {'service': ['This field is required.']})

    def test_add_blocked_phone_number(self):
        self.assertFalse(BlockedPhoneNumber.objects.filter(cell_phone=self.cell_phone).exists())

        post_data = {
            'cell_phone': self.cell_phone,
            'service': NotificationService.TELNYX,
        }
        response = self.client.post(self.url_add, post_data, follow=True)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(BlockedPhoneNumber.objects.filter(cell_phone=self.cell_phone).exists())

    def test_add_same_phone_number_different_service(self):
        baker.make(
            BlockedPhoneNumber,
            cell_phone=self.cell_phone,
            service=NotificationService.EVOX,
        )

        post_data = {
            'cell_phone': self.cell_phone,
            'service': NotificationService.TELNYX,
        }

        response = self.client.post(self.url_add, post_data, follow=True)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(
            BlockedPhoneNumber.objects.filter(
                cell_phone=self.cell_phone, service=NotificationService.TELNYX
            ).exists()
        )
        self.assertTrue(
            BlockedPhoneNumber.objects.filter(
                cell_phone=self.cell_phone, service=NotificationService.EVOX
            ).exists()
        )
