# pylint: skip-file
from webapps.message_blast.enums import (
    MessageBlastGroupEnum,
    MessageBlastInternalNames,
    MessageBlastTemplateStatus,
)

MX_MESSAGE_BLAST_SPECIAL_OCCASIONS = [
    dict(
        name='Agradece la primera visita de tu cliente',
        description='Enviado a los clientes dos horas después de su primera visita',
        title='¡Gracias por venir!',
        body=(
            "Esperamos que estés satisfecho con la experiencia que tuviste en {business_name}."
            "Estamos muy contentos de que vinieras.\n\n"
            "Visita {subdomain} para ver los demás servicios que ofrecemos y programar tu próxima cita.\n\n"
            "¡Esperamos volver a verte pronto!\n"
            "{business_name}"
        ),
        body_short=(
            "¡Esperamos que hayas disfrutado de tu experiencia en {business_name}! "
            "Estamos muy contentos de que hayas venido. Visita {subdomain} para programar tu próxima cita."
        ),
        image=None,
        recommended=True,
        order=1,
        group__internal_name=MessageBlastGroupEnum.FIRST_IMPRESSION,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT,
    ),
    dict(
        name='Informar a nuevos clientes sobre otros servicios',
        description='Enviado a los clientes 21 días después de su primera visita',
        title='Otros de nuestros servicios que te encantarán',
        body=(
            "¿Sabías que ofrecemos una gran varidad de servicios? ¡Seguro que querrás probarlos!\n\n"
            "Ve a {subdomain}  para verlos y reserva tu próxima cita.\n\n"
            "¡Nos vemos pronto!\n"
            "{business_name}"
        ),
        body_short=(
            "Ve a {subdomain} para consultar todos nuestros servicios y programar tu próxima cita. ¡Te esperamos!"
        ),
        image=None,
        recommended=True,
        order=2,
        group__internal_name=MessageBlastGroupEnum.FIRST_IMPRESSION,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_OTHER_SERVICES,
    ),
    dict(
        name='Informar a los nuevos clientes sobre las tarjetas de regalo',
        description=(
            'Enviado a los clientes 30 días después de su primera visita si ha creado al menos una Tarjeta de regalo'
        ),
        title='¡Regala nuestras tarjetas de regalo!',
        body=(
            "¿Necesitas un regalo para una ocasión especial? ¡Estás de suerte porque en {business_name} ofrecemos tarjetas de regalo!\n\n"
            "Visita {subdomain} y ve a la sección de tarjetas de regalo para comprarlas y regalar.\n\n"
            "¡Nos vemos pronto!\n"
            "{business_name}"
        ),
        body_short=(
            "¿Necesitas un regalo para una ocasión especial? Estás de suerte porque en {business_name} "
            "ofrecemos tarjetas de regalo. Ve a {subdomain} para comprarlas."
        ),
        image=None,
        recommended=True,
        order=3,
        group__internal_name=MessageBlastGroupEnum.FIRST_IMPRESSION,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.PROMOTE_GIFT_CARDS,
    ),
    dict(
        name='Invitar a otra visita',
        description=(
            'Enviado a los clientes 21 días después de su última visita si no tienen citas futuras en sus calendarios'
        ),
        title='¡Es hora de tu próxima cita!',
        body=(
            "¿Nos extrañas tanto como nosotros te extrañamos a ti?\n\n"
            "¡Visita {subdomain} para reservar tu próxima cita cuando más te convenga!\n\n"
            "¡Te esperamos!\n"
            "{business_name}"
        ),
        body_short=(
            "¿Nos extrañas tanto como nosotros te extrañamos a ti? ¡Visita {subdomain} s para reservar tu próxima cita!"
        ),
        image=None,
        recommended=True,
        order=1,
        group__internal_name=MessageBlastGroupEnum.REACTIVATE,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.INVITE_FOR_VISIT,
    ),
    dict(
        name='Invitar de nuevo y ofrecer un servicio gratuito',
        description=('Enviado a clientes que no te han visitado en los últimos 90 días'),
        title='¡Tenemos un regalo para ti en tu próxima cita!',
        body=(
            "Vamos a consentirte… porque sí.\n\n"
            "Visita {subdomain} y reserva tu próxima cita ¡con regalo! "
            "Agregaremos un [AÑADIR SERVICIO] gratis si presentas este mensaje a tu llegada.\n\n"
            "¡Esperamos verte pronto!\n"
            "{business_name}"
        ),
        body_short=(
            "¡Déjanos consentirte! Visita {subdomain} y reserva una cita. "
            "Presenta este mensaje a tu llegada para obtener un [AÑADIR SERVICIO] gratis."
        ),
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.REACTIVATE,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.REINVITE_FREE_SERVICE,
    ),
    dict(
        name='Invitar de nuevo y ofrecer un descuento',
        description=('Enviado a clientes que no te han visitado en los últimos 45 días'),
        title='Una oferta especial porque... ¡Te extrañamos!',
        body=(
            "¡Tu próxima cita tiene premio!\n\n"
            "Visita {subdomain} para reservar una cita con nosotros. "
            "Aplicaremos un [AÑADIR DESCUENTO]% de descuento si presentas "
            "este mensaje al finalizar tu compra.\n\n"
            "¡Esperamos verte pronto!\n"
            "{business_name}"
        ),
        body_short=(
            "¡Un descuento para ti! Reserva una cita ahora y presenta este "
            "mensaje para recibir tu [AÑADIR DESCUENTO]% de descuento.\n"
            "Reserva en: {subdomain}"
        ),
        image=None,
        recommended=False,
        order=3,
        group__internal_name=MessageBlastGroupEnum.REACTIVATE,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.REINVITE_DISCOUNT,
    ),
    dict(
        name='Ofrecer un descuento de cumpleaños',
        description=('Enviado a clientes que celebrarán un cumpleaños en los próximos 7 días'),
        title='¡Aquí tienes tu regalo de cumpleaños!',
        body=(
            "¡Feliz cumpleaños!\n\n"
            "Desde {business_name} queremos desearte un feliz cumpleaños y te "
            "regalamos un [AÑADIR DESCUENTO]% de descuento para tu próxima cita.\n\n"
            "Ve a {subdomain} para reservar ahora.\n\n"
            "¡Esperamos que tengas el mejor de los días!\n"
            "{business_name}"
        ),
        body_short=(
            "¡Feliz cumpleaños! Te regalamos por tu cumpleaños un [AÑADIR DESCUENTO]% "
            "de descuento para tu próxima cita. Resérvala ahora en {subdomain}!"
        ),
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HAPPY_BIRTHDAY,
    ),
    #  Links
    # This group of Message Blasts will be fulfilled in the coming weeks
    # We do not want to present it now
    # dict(
    #     name='Ocasiones Especiales',
    #     description=(
    #         'Mensajes en días festivos enviados a los clientes para fomentar las reservas y más'
    #     ),
    #     title='',
    #     body='',
    #     body_short='',
    #     image=None,
    #     recommended=False,
    #     order=2,
    #     group__internal_name=MessageBlastGroupEnum.SPECIAL_OCCASIONS,
    #     automated_status=None,
    #     link_to_group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
    #     internal_name=MessageBlastInternalNames.MORE_OCCASIONS_LINK,
    # ),
    dict(
        name="Personaliza tu mensaje",
        description=(
            'Envía mensajes a tus clientes en tiempo real. Elige entre nuestras plantillas o crea una propia.'
        ),
        title='',
        body='',
        body_short='',
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.YOUR_OWN_MESSAGES,
        automated_status=None,
        link_to_group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        internal_name=MessageBlastInternalNames.ONE_TIME_MESSAGE_LINK,
    ),
    # This group of Message Blasts will be fulfilled in the coming weeks
    # We do not want to present it now
    # dict(
    #     name='XXX',
    #     description='XXX',
    #     title='',
    #     body='',
    #     body_short='',
    #     image=None,
    #     recommended=False,
    #     order=2,
    #     group__internal_name=MessageBlastGroupEnum.YOUR_OWN_MESSAGES,
    #     automated_status=None,
    #     link_to_group__internal_name=\
    #         MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
    #     internal_name=MessageBlastInternalNames.SPECIAL_MESSAGE_BLASTS_LINK,
    # ),
    # Special Occasions
    # One time message
    dict(
        name='Informar a los clientes de citas disponibles',
        description='',
        title='Citas de última hora disponibles',
        body=(
            "¡Eres el primero en saberlo! Tenemos algunas citas disponibles para reservar el [FECHA]."
            "Reserva ahora tu cita en {subdomain}\n\n"
            "¡Esperamos verte pronto!\n"
            "{business_name}"
        ),
        body_short=(
            "¡Eres el primero en saberlo! Tenemos algunas citas disponibles para reservar el [FECHA]."
            " Reserva ahora tu cita en {subdomain}"
        ),
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_NEW_OPENING,
    ),
    dict(
        name='Informar a los clientes de los próximos días / vacaciones',
        description='',
        title='Próximos días cerrados',
        body=(
            "En {business_name} cerraremos los días [AÑADIR FECHA] porque estaremos de descanso.\n\n"
            "Si necesitas nuestros servicios antes o después de estas fechas, ve a {subdomain} y reserva tu cita cuanto antes.\n\n"
            "¡Nos vemos pronto!\n"
            "{business_name}"
        ),
        body_short=(
            "Los días [AÑADIR FECHA] estaremos cerrados por descanso. "
            "Si necesitas programar una cita antes o después de estas fechas, ve a {subdomain} y reserva cuanto antes."
        ),
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_TIME_OFF,
    ),
    dict(
        name="Informar a los clientes de nuevos servicios",
        description='',
        title='¡Tenemos nuevos servicios para ti!',
        body=(
            "¡Hemos agregado nuevos servicios que pueden interesarte! Ve a {subdomain} para descubrirlos y reserva una cita con nosotros.\n\n"
            "¡Esperamos verte pronto!\n"
            "{business_name}"
        ),
        body_short=(
            "¡Hemos agregado nuevos servicios que pueden interesarte! "
            "Ve a {subdomain} para ver la lista completa y reservar tu próxima cita."
        ),
        image=None,
        recommended=False,
        order=3,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_NEW_SERVICES,
    ),
    dict(
        name='Invitar clientes a un evento',
        description='',
        title='¡Estás invitado/a!',
        body=(
            "{business_name} te invita a nuestro próximo evento. "
            "Únete a nuestro evento [AÑADIR NOMBRE DEL EVENTO] el [AÑADIR FECHA EVENTO] para pasarla genial.\n\n"
            "[AÑADIR DETALLES DE EVENTO]\n\n"
            "¡Esperamos verte allí!\n"
            "{business_name}"
        ),
        body_short=(
            "{business_name} te invita a nuestro próximo evento "
            "[AÑADIR NOMBRE EVENTO] el [AÑADIR FECHA EVENTO].\n"
            "¡Esperamos verte pronto!"
        ),
        image=None,
        recommended=False,
        order=4,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INVITE_TO_EVENT,
    ),
    dict(
        name='Pide a los clientes una referencia',
        description='',
        title='¡Recomendarnos a un amigo tiene premio!',
        body=(
            "Recomienda {business_name} a un amigo y gana un descuento para los dos. "
            "Dile a tu amigo que reserve su cita en {subdomain} y que nos diga tu nombre cuando acuda a su cita.\n\n"
            "Recomiéndanos ahora y ahorra en tu próxima cita.\n"
            "¡Cuantos más sean, mejor!\n"
            "{business_name}"
        ),
        body_short=("¡Recomienda {business_name} a un amigo y gana un descuento para los dos! "),
        image=None,
        recommended=False,
        order=5,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.ASK_FOR_REFERRAL,
    ),
    dict(
        name='Informar a los clientes de los cambios de precios.',
        description='',
        title='¡Reserve antes de que aumenten los precios!',
        body=(
            "Queríamos informarte que nuestros precios aumentarán pronto. "
            "¡Dirígete a {subdomain} para reservar antes de que suban!\n\n"
            "¡Apreciamos a los clientes como tú y esperamos verte pronto!\n"
            "{business_name}"
        ),
        body_short=(
            "Queríamos informarte que nuestros precios aumentarán pronto. "
            "¡Dirígete a {subdomain} para reservar antes de que suban!"
        ),
        image=None,
        recommended=False,
        order=6,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_PRICE_CHANGES,
    ),
    dict(
        name='Escribe tu propio mensaje desde cero',
        description='',
        title='',
        body='',
        body_short='',
        image=None,
        recommended=False,
        order=7,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.OWN_MESSAGE,
    ),
]
