# pylint: skip-file
from webapps.message_blast.content.base_en import EN_COMMON_BLASTS_TEMPLATES

from webapps.message_blast.enums import (
    MessageBlastDateType,
    MessageBlastGroupEnum,
    MessageBlastInternalNames,
    MessageBlastTemplateStatus,
)

IE_MESSAGE_BLAST_SPECIAL_OCCASIONS = [
    *EN_COMMON_BLASTS_TEMPLATES,
    # Special Occasions
    dict(
        name='Promote Holiday Bookings',
        description='Send to all clients • Second Monday in November',
        title='Get On Our Books This Holiday Season',
        body=(
            "The holidays are just around the corner and our books are filling "
            "up quickly. Visit {subdomain} to make an appointment before "
            "the festivities begin!\n\n"
            "Wishing you a wonderful season.\n\n"
            "See you soon,\n"
            "{business_name}"
        ),
        body_short=(
            "The holidays are just around the corner and our books are filling "
            "up quickly. Visit {subdomain} to make an appointment before "
            "the festivities begin!"
        ),
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HOLIDAY_BOOKING,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=11,
        date_monday=2,
    ),
    dict(
        name='Encourage Spring Bookings',
        description='Send to all clients • March 21st',
        title='Freshen Up For Spring',
        body=(
            "It's official, spring has arrived and we're here to help you "
            "recover from what winter left behind. Visit {subdomain} to make "
            "an appointment so that you can freshen up and feel better!\n\n"
            "See you soon!\n"
            "{business_name}"
        ),
        body_short=(
            "Spring has arrived and we're here to help you recover from what "
            "winter left behind. Visit {subdomain} to make an appointment."
        ),
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.SPRING,
        date_type=MessageBlastDateType.STRICT,
        date_month=3,
        date_day=21,
    ),
    dict(
        name='Sell Father\'s Day Gift Cards',
        description='Send to all clients • Second Monday in June',
        title='Say Thanks to Dad',
        body=(
            "Show your dad how much you appreciate him with a gift card to "
            "{business_name} and let him spend it how he chooses. Visit "
            "{subdomain} and navigate to Gift Cards to purchase.\n\n"
            "Happy Father's Day to all the dads out there!\n\n"
            "All the best,\n"
            "{business_name}"
        ),
        body_short=(
            "Show your dad how much you appreciate him with a gift card to "
            "{business_name}. Visit {subdomain} to purchase."
        ),
        image=None,
        recommended=False,
        order=3,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.FATHERS_DAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=6,
        date_monday=2,
    ),
    dict(
        name='Sell Mother\'s Day Gift Cards',
        description='Send to all clients • 21st Feb',
        title='A Gift for Mom',
        body=(
            "Are you looking for the perfect Mother's Day gift? Get her a "
            "gift card from {business_name} and let her spend it how she "
            "chooses. Visit {subdomain} and navigate to Gift Cards "
            "to purchase.\n\n"
            "Happy Mother's Day to all of the moms out there!\n\n"
            "All the best,\n"
            "{business_name}"
        ),
        body_short=(
            "Looking for the perfect Mother's Day gift? Get her a gift card "
            "from {business_name}. Visit {subdomain} to purchase."
        ),
        image=None,
        recommended=False,
        order=4,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.MOTHERS_DAY,
        date_type=MessageBlastDateType.STRICT,
        date_month=2,
        date_monday=21,
    ),
    dict(
        name='Promote Halloween Services',
        description='Send to all clients • First Monday in October',
        title='Take Halloween To The Next Level',
        body=(
            "Want to take your Halloween costume to the next level? Let us "
            "help you get the ghoulish look that you're going for. Visit "
            "{subdomain} to book before the witches and warlocks take them "
            "all.\n\n"
            "See you soon,\n"
            "{business_name}"
        ),
        body_short=(
            "Let us help you get the look you're going for this Halloween. "
            "Visit {subdomain} to book before the witches and warlocks take "
            "them all."
        ),
        image=None,
        recommended=False,
        order=5,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HALLOWEEN,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=10,
        date_monday=1,
    ),
    dict(
        name='Get New Year Bookings',
        description='Send to all clients • First Monday of the year',
        title='New Year, New You?',
        body=(
            "The New Year has arrived and you deserve to welcome it with a "
            "whole new you.\n\n"
            "Visit {subdomain} to book an appointment and celebrate your "
            "fresh start. We can't wait to see you!\n\n"
            "Happy New Year!\n"
            "{business_name}"
        ),
        body_short=(
            "The New Year has arrived and you deserve to welcome it with a "
            "whole new you. Visit {subdomain} to book and celebrate your "
            "fresh start."
        ),
        image=None,
        recommended=False,
        order=6,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.NEW_YEAR,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=1,
        date_monday=1,
    ),
    dict(
        name='Sell Holiday Gift Cards',
        description='Send to all clients • First Monday in December',
        title='Simplify Your Holiday Shopping',
        body=(
            "Want to simplify your holiday shopping this year? Pick up a "
            "couple of gift cards and let your loved ones choose how they "
            "want to apply them.\n\n"
            "Visit {subdomain} and navigate to Gift Cards to purchase.\n\n"
            "Happy Holidays!\n"
            "{business_name}"
        ),
        body_short=(
            "Simplify your holiday shopping this year and pick up a couple of "
            "gift cards from {business_name}. Visit {subdomain} to "
            "purchase."
        ),
        image=None,
        recommended=False,
        order=7,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HOLDAY_GIFTCARD,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=12,
        date_monday=1,
    ),
    dict(
        name='Sell Valentine\'s Day Gift Cards',
        description='Send to all clients • First Monday in February',
        title='Pamper Your Valentine',
        body=(
            "Valentine's Day is just around the corner. Grab a gift card for "
            "that special someone so that they can enjoy a little "
            "pampering.\n\n"
            "Visit {subdomain} and navigate to Gift Cards to purchase.\n\n"
            "Warmly,\n"
            "{business_name}"
        ),
        body_short=(
            "Suprise your Valentine with a gift card from {business_name}. "
            "Visit {subdomain} to purchase."
        ),
        image=None,
        recommended=False,
        order=8,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.VALENTINES_DAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=2,
        date_monday=1,
    ),
    dict(
        name='Promote Back To School Services',
        description='Send to all clients • First Monday in August',
        title='Schedule Your Back To School Appointments',
        body=(
            "Back to school season is upon us! If your kids need a refreshed "
            "look after a summer of fun, visit {subdomain} to book their "
            "appointments today.\n\n"
            "See you soon!\n"
            "{business_name}"
        ),
        body_short=(
            "Back to school season is here! If your kids need a refreshed look "
            "after summer visit {subdomain} to book their appointments."
        ),
        image=None,
        recommended=False,
        order=9,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.BACK_TO_SCHOOL,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=8,
        date_monday=1,
    ),
]
