from django.contrib import admin

from lib.admin_helpers import BaseModelAdmin, NoAddDelMixin, ReadOnlyFieldsMixin
from webapps.utt import models


class TreatmentInline(NoAddDelMixin, admin.TabularInline):
    model = models.Treatment
    classes = ['collapse']
    extra = 0
    can_delete = False

    fields = readonly_fields = ['id', 'name', 'internal_name', 'level', 'parent']
    ordering = ('id',)


class CategoryAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, BaseModelAdmin):
    list_display = (
        'id',
        'internal_name',
        'name',
    )
    inlines = (TreatmentInline,)
    hide_keyword_field = True
