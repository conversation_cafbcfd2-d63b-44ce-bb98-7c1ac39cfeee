from datetime import datetime, timezone

import pytest
from mock import patch
from model_bakery import baker

from lib.gcs_dataset.tools import create_google_client_mock
from webapps.business import baker_recipes as br
from webapps.utt.models import TreatmentPredictor, TreatmentPredictorData
from webapps.utt.tests import baker_recipe as tr

ONE_NAME = ['Beard shaving']
ONE_NAME_TOKENS = {'beard', 'shav'}
MULTIPLE_NAMES = ["hair color", "permanent hair color", "discoloration"]
MULTIPLE_NAMES_TOKENS = {'discolor', 'hair', 'color', 'perm'}


@pytest.mark.parametrize(
    ('businesses_primary_category', 'expected'),
    [
        pytest.param(
            [1, 1, 1, 2, 3, 2, 1, 4],
            {1: 0.5, 2: 0.25, 3: 0.125, 4: 0.125},
            id='Simple',
        ),
        pytest.param(
            [],
            {},
            id='Empty',
        ),
        pytest.param(
            [None, 1, 1, 1, None, 2, None],
            {1: 0.75, 2: 0.25},
            id='Empty categories',
        ),
    ],
)
def test__calculate_distribution(businesses_primary_category, expected):
    assert TreatmentPredictor.calculate_distribution(businesses_primary_category) == expected


@pytest.mark.parametrize(
    ('names', 'expected'),
    [
        pytest.param(
            ONE_NAME,
            ONE_NAME_TOKENS,
            id='One name',
        ),
        pytest.param(
            [],
            set(),
            id='Empty',
        ),
        pytest.param(
            MULTIPLE_NAMES,
            MULTIPLE_NAMES_TOKENS,
            id='Multiple names',
        ),
    ],
)
def test__get_treatment_name_tokens(names, expected):
    assert TreatmentPredictor.get_treatment_name_tokens(names) == expected


@pytest.mark.parametrize(
    ('names', 'top_number', 'expected'),
    [
        pytest.param(
            ONE_NAME,
            2,
            ONE_NAME_TOKENS,
            id='One name',
        ),
        pytest.param(
            [],
            2,
            set(),
            id='Empty',
        ),
        pytest.param(
            MULTIPLE_NAMES,
            2,
            {'hair', 'color'},
            id='Multiple names short',
        ),
        pytest.param(
            MULTIPLE_NAMES,
            25,
            MULTIPLE_NAMES_TOKENS,
            id='Multiple names short - all',
        ),
    ],
)
def test__calculate_services_name_tokens(names, expected, top_number):
    assert TreatmentPredictor.get_top_services_name_tokens(names, top_number) == expected


def prepare_data():
    old_categories = [
        br.category_recipe.make(
            category_utt=tr.CATEGORY_RECIPE.make(),
        )
        for _ in range(3)
    ]
    new_treatment = tr.TREATMENT_1_RECIPE.make(
        name='Hair Color',
    )
    old_treatments = [
        br.treatment_recipe.make(
            parent=old_categories[cat_ii],
            name=name,
            treatment_utt=treatment,
        )
        for cat_ii, name, treatment in [
            (0, "hair color", new_treatment),
            (0, "permanent hair color", new_treatment),
            (1, "discoloration", new_treatment),
            (2, "bunny bunnies", None),  # another old treatment
        ]
    ]
    businesses = [
        br.business_recipe.make(
            primary_category=old_categories[cat_ii],
            categories=[old_categories[sub_cat_ii] for sub_cat_ii in sub_cats],
        )
        for cat_ii, sub_cats in [
            (0, [1, 2]),
            (1, [0]),
            (1, []),
            (2, [1]),
        ]
    ]
    for biz_ii, treatment_ii, name in [
        (0, 0, 'Hair colouring'),
        (0, 1, 'The hair service'),
        (1, 3, 'bunny'),  # this service should be ignored
        (2, 2, 'Back to black'),
        (3, 2, 'Hair colouring'),
    ]:
        br.service_recipe.make(
            name=name,
            description='and the',
            note='',
            treatment=old_treatments[treatment_ii],
            business=businesses[biz_ii],
        )
    return new_treatment, old_categories


def assert_predictor_data(result, categories):
    assert result.bpc_distribution == {
        categories[0].category_utt_id: 0.5,
        categories[1].category_utt_id: 0.25,
        categories[2].category_utt_id: 0.25,
    }
    assert result.bsc_distribution == {
        categories[1].category_utt_id: 0.6,
        categories[2].category_utt_id: 0.4,
    }
    assert result.name_tokens == {'discolor', 'perm', 'color', 'hair'}
    assert result.service_name_tokens == {'hair', 'colour', 'black', 'servic', 'back'}


@pytest.mark.django_db
def test__train():
    new_treatment, old_categories = prepare_data()

    predictor = baker.make(
        TreatmentPredictor,
        treatment=new_treatment,
    )
    result = predictor.train()
    assert_predictor_data(result, old_categories)


@pytest.mark.django_db
@pytest.mark.freeze_time(datetime(2020, 2, 12, 15, 5, tzinfo=timezone.utc))
@patch(
    'google.cloud.storage.Client.from_service_account_json',
    return_value=create_google_client_mock(),
)
def test__generate_predictors(client_mock):
    old_categories = prepare_data()[1]

    result = TreatmentPredictor.generate_predictors()[0]

    assert_predictor_data(TreatmentPredictorData.from_dict(result), old_categories)
    client_mock.assert_called_once()
    client = client_mock()
    client.bucket_.blob.assert_called_with(
        blob_name='predictors/predictors_2020-02-12T15:05_us.json'
    )
    client.blob_upload_from_string.assert_called_once()
