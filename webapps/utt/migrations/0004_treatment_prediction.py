# Generated by Django 2.0.13 on 2020-02-27 08:27

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0264_business_utt_fields'),
        ('utt', '0003_treatment_data'),
    ]

    operations = [
        migrations.CreateModel(
            name='TreatmentPredictor',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'treatment',
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        primary_key=True,
                        related_name='predictor',
                        serialize=False,
                        to='utt.Treatment',
                    ),
                ),
                (
                    'name_tokens',
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.Char<PERSON>ield(max_length=30), default=list, size=None
                    ),
                ),
                (
                    'bpc_distribution',
                    django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=dict),
                ),
                (
                    'bsc_distribution',
                    django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=dict),
                ),
                (
                    'service_name_tokens',
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=30), default=list, size=None
                    ),
                ),
                ('version', models.CharField(default='artificial', max_length=19)),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ServiceTreatmentPrediction',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'top_guesses',
                    django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=dict),
                ),
                ('version', models.CharField(default='artificial', max_length=19)),
                (
                    'service',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='treatment_prediction',
                        to='business.Service',
                    ),
                ),
                (
                    'treatment',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='predictions',
                        to='utt.Treatment',
                    ),
                ),
            ],
            options={
                'ordering': ['-service_id'],
                'permissions': (('edit_treatments', 'Can edit Treatments'),),
                'verbose_name': 'Prediction',
                'verbose_name_plural': 'Predictions',
            },
        ),
    ]
