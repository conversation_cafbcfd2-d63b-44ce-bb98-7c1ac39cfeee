import datetime
import typing as t
from collections import defaultdict, Counter
from typing import Iterable

from django.db.models import Count, F

from lib.rivers import pop_document, River, bump_document
from lib.tools import tznow
from webapps.booking.models import SubBooking, Appointment
from webapps.business.models import Business, Service
from webapps.search_engine_tuning.consts import LAST_BOOKINGS_PERIOD
from webapps.utt.models import TreatmentPredictor, PredictorException
from webapps.utt.tasks import calculate_predictions_confirmation_priority


class TreatmentAssignmentUTT2:
    @classmethod
    def assign_services_treatment(cls) -> None:
        batch_length = 100
        service_ids = pop_document(River.ASSIGN_SERVICE_TREATMENT, batch_length)
        try:
            # checks if TreatmentPredictor data exists for the given region
            # if not exists: pops ids from queue and ends task
            # if exists: assign services to treatment
            _ = TreatmentPredictor.get_predictor_data()
        except PredictorException:
            while service_ids:
                service_ids = pop_document(
                    River.ASSIGN_SERVICE_TREATMENT,
                    batch_length,
                )
        else:
            while service_ids:
                service_ids = [int(service_id) for service_id in service_ids]
                TreatmentPredictor.assign_services_treatment(service_ids)
                cls.update_businesses_treatments_by_service_ids(service_ids)
                calculate_predictions_confirmation_priority.delay(service_ids)
                service_ids = pop_document(
                    River.ASSIGN_SERVICE_TREATMENT,
                    batch_length,
                )

    @classmethod
    def update_businesses_treatments_by_service_ids(
        cls,
        service_ids: Iterable[int],
        update_es: bool = True,
    ) -> None:
        business_ids = list(
            Business.objects.filter(
                services__id__in=service_ids,
            )
            .values_list('id', flat=True)
            .distinct()
            .iterator()
        )
        cls.update_businesses_treatments(business_ids, update_es)

    @classmethod
    def update_businesses_treatments(
        cls,
        business_ids: Iterable[int],
        update_es: bool = True,
    ) -> None:
        business_categories = cls.select_business_categories(business_ids)

        businesses = (
            Business.objects.prefetch_related('services')
            .filter(
                id__in=business_ids,
            )
            .distinct()
            .iterator()
        )

        for business in businesses:
            categories_ids = business_categories.get(business.id, set())
            if (
                business.primary_category is not None
                and business.primary_category.category_utt is not None
            ):
                categories_ids.add(business.primary_category.category_utt)
            business.categories_utt.set(categories_ids)

            treatment_ids = set(
                filter(
                    None,
                    business.services.values_list('treatment_utt', flat=True),
                )
            )
            business.treatments_utt.set(treatment_ids)
        if update_es:
            bump_document(River.BUSINESS, list(business_ids))

    @classmethod
    def select_business_categories(cls, business_ids: Iterable[int]) -> t.Dict[int, t.Set[int]]:
        bookings_data = (
            SubBooking.objects.prefetch_related(
                'service_variant__service__treatment__category',
            )
            .filter(
                created__gte=tznow() - datetime.timedelta(days=LAST_BOOKINGS_PERIOD),
                appointment__type=Appointment.TYPE.CUSTOMER,
                service_variant__service__active=True,
                service_variant__service__treatment_utt__isnull=False,
                appointment__business_id__in=business_ids,
            )
            .values(
                'appointment__business_id',
                category=F('service_variant__service__treatment_utt__category'),
            )
            .annotate(count=Count(['appointment__business_id', 'category']))
            .values_list(
                'appointment__business_id',
                'category',
                'count',
            )
            .order_by('appointment__business_id', 'category')
            .iterator()
        )

        category_bookings = defaultdict(Counter)
        for business_id, category_id, count in bookings_data:
            category_bookings[business_id][category_id] += count

        business_services_categories = (
            Service.objects.prefetch_related('treatment_utt')
            .filter(
                active=True,
                business_id__in=business_ids,
                treatment_utt__isnull=False,
            )
            .values(
                'business_id',
                category=F('treatment_utt__category'),
            )
            .annotate(count=Count(['business_id', 'category']))
            .values_list(
                'business_id',
                'category',
                'count',
            )
            .order_by('business_id', 'category')
            .iterator()
        )

        businesses_categories = {}
        current_business_id = 0
        category_services = Counter()
        for business_id, category_id, count in business_services_categories:
            if business_id != current_business_id:
                businesses_categories[current_business_id] = cls._select_categories(
                    category_services=category_services,
                    category_bookings=category_bookings[current_business_id],
                )
                category_services = Counter({category_id: count})
                current_business_id = business_id
            else:
                category_services[category_id] += count

        businesses_categories[current_business_id] = cls._select_categories(
            category_services=category_services,
            category_bookings=category_bookings[current_business_id],
        )
        return businesses_categories

    @staticmethod
    def _select_categories(
        category_services: Counter,
        category_bookings: Counter,
    ) -> t.Set[int]:
        total_bookings = sum(category_bookings.values()) or 1
        total_services = sum(category_services.values()) or 1
        return set(
            category_id
            for category_id, count in category_services.items()
            if (
                (
                    category_bookings[category_id] > 4
                    and category_bookings[category_id] * 100 // total_bookings >= 4
                )
                or count * 100 // total_services >= 20
            )
        )
