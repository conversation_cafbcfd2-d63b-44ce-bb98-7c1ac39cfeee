from django.db import migrations, models
import django.db.models.deletion


def set_staffer(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    RegisterOperation = apps.get_model('register', 'RegisterOperation')

    for i in RegisterOperation.objects.using(db_alias):
        i.operator = i.register.opened_by
        i.save()


def set_type(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    RegisterOperation = apps.get_model('register', 'RegisterOperation')

    cash_in_operations = (
        RegisterOperation.objects.using(db_alias)
        .filter(amount__gte=0, receipt_id__isnull=True)
        .update(type='I')
    )

    cash_out_operations = (
        RegisterOperation.objects.using(db_alias)
        .filter(amount__lt=0, receipt_id__isnull=True)
        .update(type='O')
    )

    transactions = (
        RegisterOperation.objects.using(db_alias).filter(receipt_id__isnull=False).update(type='T')
    )


def noop(apps, schema_editor):
    pass


def delete_open_close(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    RegisterOperation = apps.get_model('register', 'RegisterOperation')

    RegisterOperation.objects.using(db_alias).filter(type__in=['A', 'C']).delete()


class Migration(migrations.Migration):
    atomic = False

    dependencies = [
        ('register', '0005_sharedregisters_feature'),
    ]

    operations = [
        migrations.RunPython(set_staffer, reverse_code=noop),
        migrations.RunPython(set_type, reverse_code=delete_open_close),
    ]
