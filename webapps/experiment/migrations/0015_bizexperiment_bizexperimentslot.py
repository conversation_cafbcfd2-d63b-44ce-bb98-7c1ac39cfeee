# Generated by Django 1.11.7 on 2018-02-21 14:39
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0123_auto_20180221_1439'),
        ('experiment', '0014_merge_20180221_1439'),
    ]

    operations = [
        migrations.CreateModel(
            name='BizExperiment',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('name', models.Char<PERSON>ield(max_length=64, unique=True)),
                ('fixed_percent', models.IntegerField(blank=True, null=True)),
                ('group_size', models.IntegerField(blank=True, null=True)),
                ('disabled_for_all', models.BooleanField(default=False)),
                ('enabled_for_all', models.BooleanField(default=False)),
                ('biz_after', models.DateTimeField(blank=True, null=True)),
                ('biz_before', models.DateTimeField(blank=True, null=True)),
                (
                    'experiment',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to='experiment.Experiment',
                    ),
                ),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BizExperimentSlot',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                (
                    'biz_experiment',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='slots',
                        to='experiment.BizExperiment',
                    ),
                ),
                (
                    'business',
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='biz_experiment_slot',
                        to='business.Business',
                    ),
                ),
            ],
        ),
    ]
