# https://github.com/PyCQA/pylint/issues/4987
# pylint: disable=no-name-in-module
import collections
from typing import Generator

import pytest
from model_bakery import baker

from webapps.business.enums import StaffAccessLevels
from webapps.business.models import Business, Resource
from webapps.session.protobuf.core.get_user_access_level_info_pb2 import (
    AccessLevel,
    GetUserAccessLevelInfoRequest,
)
from webapps.session.protobuf.core.get_user_access_level_info_pb2_grpc import (
    GetUserAccessLevelInfoStub,
)
from webapps.user.baker_recipes import user_recipe


@pytest.fixture(name='data')
def make_base_data():
    Data = collections.namedtuple('Data', ('user', 'business', 'resource'))
    user = user_recipe.make()
    business = baker.make(
        Business,
        active=True,
        visible=True,
        status=Business.Status.PAID,
        address='Some address',
        country_code='US',
    )
    resource = baker.make(
        Resource,
        business=business,
        staff_user=user,
        type=Resource.STAFF,
        active=True,
        deleted=None,
        staff_access_level=StaffAccessLevels.STAFF,
    )

    return Data(user, business, resource)


@pytest.fixture(name='data_owner')
def make_base_data_owner_without_resource():
    Data = collections.namedtuple('Data', ('user', 'business'))
    user = user_recipe.make()
    business = baker.make(
        Business,
        active=True,
        visible=True,
        status=Business.Status.PAID,
        address='Some address',
        country_code='US',
        owner=user,
    )

    return Data(user, business)


@pytest.fixture(name='access_level_info_stub')
def make_access_level_info_stub(test_channel) -> Generator[GetUserAccessLevelInfoStub, None, None]:
    yield GetUserAccessLevelInfoStub(test_channel)


@pytest.mark.grpc_api
@pytest.mark.django_db
def test_user_exists_with_resource(access_level_info_stub, data):
    request = GetUserAccessLevelInfoRequest(
        business_id=data.business.id,
        user_id=data.user.id,
    )
    response = access_level_info_stub.GetUserAccessLevelInfo(request)
    assert response.access_level == AccessLevel.staff
    assert response.business_id == data.business.id
    assert response.user_id == data.user.id


@pytest.mark.grpc_api
@pytest.mark.django_db
def test_user_exists_with_owner(access_level_info_stub, data_owner):
    request = GetUserAccessLevelInfoRequest(
        business_id=data_owner.business.id,
        user_id=data_owner.user.id,
    )
    response = access_level_info_stub.GetUserAccessLevelInfo(request)
    assert response.access_level == AccessLevel.owner
    assert response.business_id == data_owner.business.id
    assert response.user_id == data_owner.user.id
