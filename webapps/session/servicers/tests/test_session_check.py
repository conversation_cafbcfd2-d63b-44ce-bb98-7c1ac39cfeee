# https://github.com/PyCQA/pylint/issues/4987
# pylint: disable=no-name-in-module
"""Tests for session checking using grpc."""


from typing import Generator

import pytest
from django.contrib.sessions.models import Session

from webapps.session.protobuf.core.check_session_pb2_grpc import (
    CheckSessionExistsStub,
)
from webapps.user.baker_recipes import user_recipe
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User


@pytest.fixture(name='user_session')
def make_user_session() -> Generator[Session, None, None]:
    user: User = user_recipe.make()
    session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
    yield session


@pytest.fixture(name='session_stub')
def make_session_stub(test_channel) -> Generator[CheckSessionExistsStub, None, None]:
    yield CheckSessionExistsStub(test_channel)


# @pytest.mark.grpc_api
# @pytest.mark.django_db
# def test_user_session_exists(session_stub, user_session):
#     request = CheckSessionExistsRequest(session_key=user_session.session_key)
#     response = session_stub.CheckSessionExists(request)
#     assert response.session_exists is True
#
#
# @pytest.mark.grpc_api
# @pytest.mark.django_db
# def test_user_session_does_not_exists(session_stub, user_session):
#     invalid_session_key = user_session.session_key[:-1]
#     with pytest.raises(FakeRpcError) as e:
#        session_stub.CheckSessionExists(CheckSessionExistsRequest(session_key=invalid_session_key))
#        assert e.details == 'Session does not exists'
