# https://github.com/PyCQA/pylint/issues/4987
# pylint: disable=no-name-in-module
import grpc
from google.protobuf.any_pb2 import Any

from django.core.exceptions import ObjectDoesNotExist
from webapps.business.models import Resource, Business
from webapps.session.protobuf.core.get_user_access_level_info_pb2 import (
    GetUserAccessLevelInfoResponse,
)
from webapps.session.protobuf.core.get_user_access_level_info_pb2_grpc import (
    GetUserAccessLevelInfoServicer,
)


class GetUserAccessLevelInfoService(GetUserAccessLevelInfoServicer):

    def GetUserAccessLevelInfo(self, request, context):
        business_id = request.business_id
        user_id = request.user_id
        try:
            access_level = (
                Resource.objects.filter(
                    active=True,
                    deleted__isnull=True,
                    staff_user_id=user_id,
                    business_id=business_id,
                )
                .values_list('staff_access_level', flat=True)
                .get()
            )
        except ObjectDoesNotExist:
            # resource does not exists, check if user is owner
            if Business.objects.filter(id=business_id, owner_id=user_id, active=True).exists():
                access_level = Resource.STAFF_ACCESS_LEVEL_OWNER

        if access_level is not None:
            return GetUserAccessLevelInfoResponse(
                user_id=user_id,
                business_id=business_id,
                access_level=access_level,
            )
        context.set_code(grpc.StatusCode.NOT_FOUND)
        context.set_details('Resource does not exist for such business')
        return Any()
