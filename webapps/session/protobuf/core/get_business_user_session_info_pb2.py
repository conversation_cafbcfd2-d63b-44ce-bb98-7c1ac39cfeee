# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: get_business_user_session_info.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n$get_business_user_session_info.proto\x12\x1eget_business_user_session_info\"M\n!GetBusinessUserSessionInfoRequest\x12\x13\n\x0bsession_key\x18\x01 \x01(\t\x12\x13\n\x0b\x62usiness_id\x18\x02 \x01(\x05\"\xbb\x01\n\"GetBusinessUserSessionInfoResponse\x12\x0f\n\x07user_id\x18\x01 \x01(\x05\x12\x13\n\x0b\x62usiness_id\x18\x02 \x01(\x05\x12\x14\n\x0c\x63ountry_code\x18\x03 \x01(\t\x12\x41\n\x0c\x61\x63\x63\x65ss_level\x18\x04 \x01(\x0e\x32+.get_business_user_session_info.AccessLevel\x12\x16\n\x0etime_zone_name\x18\x05 \x01(\t*M\n\x0b\x41\x63\x63\x65ssLevel\x12\t\n\x05owner\x10\x00\x12\x0b\n\x07manager\x10\x01\x12\r\n\treception\x10\x02\x12\x0c\n\x08\x61\x64vanced\x10\x03\x12\t\n\x05staff\x10\x04\x32\xc2\x01\n\x1aGetBusinessUserSessionInfo\x12\xa3\x01\n\x1aGetBusinessUserSessionInfo\x12\x41.get_business_user_session_info.GetBusinessUserSessionInfoRequest\x1a\x42.get_business_user_session_info.GetBusinessUserSessionInfoResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'get_business_user_session_info_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ACCESSLEVEL']._serialized_start=341
  _globals['_ACCESSLEVEL']._serialized_end=418
  _globals['_GETBUSINESSUSERSESSIONINFOREQUEST']._serialized_start=72
  _globals['_GETBUSINESSUSERSESSIONINFOREQUEST']._serialized_end=149
  _globals['_GETBUSINESSUSERSESSIONINFORESPONSE']._serialized_start=152
  _globals['_GETBUSINESSUSERSESSIONINFORESPONSE']._serialized_end=339
  _globals['_GETBUSINESSUSERSESSIONINFO']._serialized_start=421
  _globals['_GETBUSINESSUSERSESSIONINFO']._serialized_end=615
# @@protoc_insertion_point(module_scope)
