# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from webapps.session.protobuf.core import get_business_user_session_info_pb2 as get__business__user__session__info__pb2


class GetBusinessUserSessionInfoStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetBusinessUserSessionInfo = channel.unary_unary(
                '/get_business_user_session_info.GetBusinessUserSessionInfo/GetBusinessUserSessionInfo',
                request_serializer=get__business__user__session__info__pb2.GetBusinessUserSessionInfoRequest.SerializeToString,
                response_deserializer=get__business__user__session__info__pb2.GetBusinessUserSessionInfoResponse.FromString,
                )


class GetBusinessUserSessionInfoServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetBusinessUserSessionInfo(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_GetBusinessUserSessionInfoServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetBusinessUserSessionInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBusinessUserSessionInfo,
                    request_deserializer=get__business__user__session__info__pb2.GetBusinessUserSessionInfoRequest.FromString,
                    response_serializer=get__business__user__session__info__pb2.GetBusinessUserSessionInfoResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'get_business_user_session_info.GetBusinessUserSessionInfo', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class GetBusinessUserSessionInfo(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetBusinessUserSessionInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/get_business_user_session_info.GetBusinessUserSessionInfo/GetBusinessUserSessionInfo',
            get__business__user__session__info__pb2.GetBusinessUserSessionInfoRequest.SerializeToString,
            get__business__user__session__info__pb2.GetBusinessUserSessionInfoResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
