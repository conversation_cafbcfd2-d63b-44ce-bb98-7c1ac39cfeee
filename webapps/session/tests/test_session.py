from datetime import datetime, timedelta
from unittest.mock import patch

import pytest
from django.contrib.sessions.models import Session
from django.core.cache import cache
from django.test import override_settings
from django.utils import timezone
from model_bakery import baker
from model_bakery.random_gen import gen_email, gen_integer, gen_string, gen_uuid
from rest_framework import status

from country_config import Country
from lib.datetime_utils import TZ_UTC
from lib.feature_flag.feature import AuthPasswordChangeRequiredFlag
from lib.feature_flag.feature.business import LoginUserRetryFlag
from lib.tests.utils import override_feature_flag, override_eppo_feature_flag
from service.tests import BaseAsyncHTTPTest
from webapps.registrationcode.models import RegistrationCode
from webapps.session.booksy_auth import (
    BooksyAuthClient,
    BooksyAuthValidationError,
    BooksyAuthTimeoutException,
)

# pylint: disable=no-name-in-module
from webapps.session.booksy_auth.pb2.auth_pb2 import (
    CreateUserResponse,
    LoginUserResponse,
    SessionExistsResponse,
)

# pylint: enable=no-name-in-module
from webapps.session.booksy_auth.session_store import BooksyAuthSessionStore
from webapps.session.ports import SessionMediator, get_session
from webapps.session.session_store import SessionStore  # it is only for test purpouse  # NOQA
from webapps.session.utils import get_user
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User, UserSessionCache
from webapps.user.serializers import BooksyAuthUserSerializer


@pytest.mark.django_db
@pytest.mark.usefixtures('switch_on_new_login_fixture')
def test_user_not_assigned_to_any_session():
    temp_session = get_session()
    temp_session.create()

    user1 = temp_session.get_user()
    user2 = get_user(user_id=None)

    assert user1 is None
    assert user2 is None
    temp_session.delete()
    temp_session.clear_get_user_cache()


@pytest.mark.django_db
@pytest.mark.usefixtures('switch_on_new_login_fixture')
@patch('webapps.session.booksy_auth.BooksyAuthClient._make_request')
def test_session_exist_doesnt_send_request_without_token(send_grpc_mock):
    with pytest.raises(BooksyAuthValidationError) as error:
        BooksyAuthClient.session_exists(access_token=None)

    assert send_grpc_mock.call_count == 0
    assert error.value.errors == [{'session_key': ['This field may not be blank.']}]


@pytest.mark.django_db
@pytest.mark.freeze_time('2021-11-01')
@pytest.mark.usefixtures('switch_on_new_login_fixture')
@patch(
    'webapps.session.booksy_auth.BooksyAuthClient._make_request',
    return_value=CreateUserResponse(
        user_id=str(gen_uuid()),
        session_key=str(gen_uuid()),
        is_created=True,
        expired=datetime(2021, 12, 1, 11, 12, tzinfo=TZ_UTC).isoformat(),
    ),
)
def test_create_user_with_session_in_auth(create_user_mock):
    user = User.objects.create(
        email=gen_email(),
        first_name=gen_string(max_length=30),
        last_name=gen_string(60),
        password=gen_string(max_length=60),
    )
    user = User.booksy_auth_objects.get(id=user.id)  # add prefetches
    data = BooksyAuthUserSerializer().to_representation(user)

    response = BooksyAuthClient.create_user_with_session(data, AuthOriginEnum.BOOKSY, 'fingerprint')

    assert create_user_mock.call_count == 1
    assert response.is_created


# Tests below will be removed after removing booksy_auth killswitch
@pytest.mark.django_db
@pytest.mark.usefixtures('switch_on_new_login_fixture')
@patch(
    'webapps.session.booksy_auth.grpc_client.BooksyAuthClient._make_request',
    return_value=SessionExistsResponse(
        **{
            'session_exists': False,
            'country_user_id': 0,
            'user_id': '',
            'expired': '',
        }
    ),
)
def test_session_doesnt_exist_in_auth_nor_in_core(make_request_mock):
    store = BooksyAuthSessionStore()
    assert not store.exists(session_key='abcd')
    assert make_request_mock.call_count == 1
    assert not Session.objects.count()
    assert not UserSessionCache.objects.count()


def check_if_session_for_user_is_in_cache(user_id: int) -> bool:
    value_in_redis = None
    for key in cache.keys('*'):
        value = cache.get(key)
        if isinstance(value, dict) and 'user_id' in value.keys() and value['user_id'] == user_id:
            value_in_redis = value
    value_keys = value_in_redis.keys()
    return 'origin' in value_keys and 'session_expire_date' in value_keys


@pytest.mark.django_db
@pytest.mark.usefixtures('switch_on_new_login_fixture')
class BusinessAccountCreateWithAuthTestCase(BaseAsyncHTTPTest):
    USERNAME = gen_string(max_length=100)
    EMAIL = gen_email()
    USER_ID = gen_integer(min_int=100_000, max_int=1_000_000)
    SESSION_KEY = str(gen_uuid()).replace('-', '')
    PASSWORD = gen_string(max_length=100)

    def _get_user_create_payload(self):
        return {
            'username': 'testowiec_profesjonalny',
            'email': '<EMAIL>',
            'first_name': 'Testowiec',
            'last_name': 'Testerski',
            'password': '123456789_strong_password',
        }

    def test_create_user_creates_session_locally(self):
        cache.clear()
        body = self._get_user_create_payload()
        baker.make(RegistrationCode, code='********')

        resp = self.fetch('/business_api/account/?', body=body, method='POST')

        assert resp.code == status.HTTP_201_CREATED
        session_key = resp.json['access_token']
        assert session_key.startswith('mock_s_')
        user = User.objects.get(email=body['email'])
        assert UserSessionCache.objects.filter(user=user, session_id=session_key).count() == 1
        assert Session.objects.filter(session_key=session_key).count() == 1
        assert check_if_session_for_user_is_in_cache(user_id=user.id)

    def test_login_user_creates_session_locally(self):
        cache.clear()
        user = User(id=self.USER_ID, email=self.EMAIL, username=self.USERNAME)
        user.set_password(self.PASSWORD)
        user.save()

        resp = self.fetch(
            '/business_api/account/login/',
            body=dict(username=self.USERNAME, email=self.EMAIL, password=self.PASSWORD),
            method='POST',
        )

        assert resp.code == status.HTTP_200_OK
        session_key = resp.json['access_token']
        assert UserSessionCache.objects.filter(user=user, session_id=session_key).count() == 1
        assert Session.objects.filter(session_key=session_key).count() == 1
        assert check_if_session_for_user_is_in_cache(user_id=user.id)

    @override_eppo_feature_flag({LoginUserRetryFlag.flag_name: True})
    @patch.object(BooksyAuthClient, '_make_request')
    def test_login_user_retry(self, make_request_mock):
        def mock_generator(fails=2):
            for _ in range(fails):
                yield False
            yield True

        mock = mock_generator()

        def make_request_func(*args, **kwargs):
            if not next(mock):
                raise BooksyAuthTimeoutException(status='failed', errors=[])
            return LoginUserResponse(
                account_exists=True,
                superuser_email='',
                country_user_id=self.USER_ID,
                password_change_required=False,
                session_key=self.SESSION_KEY,
                expired=(timezone.now() + timedelta(days=60)).isoformat(),
            )

        make_request_mock.side_effect = make_request_func

        cache.clear()
        user = User(id=self.USER_ID, email=self.EMAIL, username=self.USERNAME)
        user.set_password(self.PASSWORD)
        user.save()

        resp = self.fetch(
            '/business_api/account/login/',
            body=dict(username=self.USERNAME, email=self.EMAIL, password=self.PASSWORD),
            method='POST',
        )

        assert resp.code == status.HTTP_200_OK
        assert make_request_mock.call_count == 3
        session_key = resp.json['access_token']
        assert UserSessionCache.objects.filter(user=user, session_id=session_key).count() == 1
        assert Session.objects.filter(session_key=session_key).count() == 1
        assert check_if_session_for_user_is_in_cache(user_id=user.id)

    @override_feature_flag({AuthPasswordChangeRequiredFlag: True})
    @override_settings(API_COUNTRY=Country.US)
    @patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient._make_request',
        return_value=LoginUserResponse(
            account_exists=True,
            country_user_id=USER_ID,
            password_change_required=True,
        ),
    )
    def test_login_user_password_change_required(self, make_request_mock):
        user = User.objects.create(
            id=self.USER_ID,
            email=self.EMAIL,
            username=self.USERNAME,
            cell_phone='***********',
        )

        resp = self.fetch(
            '/business_api/account/login/',
            body=dict(username=self.USERNAME, email=self.EMAIL, password=self.PASSWORD),
            method='POST',
        )

        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json == dict(
            errors=[
                dict(
                    code='not_valid',
                    type='validation',
                    description='Your password was reset: please check your email',
                    field='password',
                ),
            ],
        )
        assert make_request_mock.call_count == 1
        assert UserSessionCache.objects.filter(user=user).count() == 0

    def test_logout_user_removes_session_locally(self):
        session_key = self.session.session_key
        cache_key_with_prefix = SessionStore.cache_key_prefix + session_key
        cache.set(
            cache_key_with_prefix,
            {'user_id': self.USER_ID, 'origin': 'B', 'session_expire_date': '2022-10-08T17:06:56'},
        )
        headers = {
            'Content-Type': 'application/json; charset=UTF-8',
            'X-API-Country': 'us',
            'X-API-KEY': 'biz_key',
            'X-ACCESS-TOKEN': session_key,
        }
        args = {
            'auth_username': self.USERNAME,
            'auth_password': self.PASSWORD,
            'auth_mode': 'basic',
        }

        resp = self.fetch(
            '/business_api/account/logout/', headers=headers, args=args, skip_basic_auth=True
        )

        self.assertEqual(resp.code, 200)
        assert not Session.objects.filter(session_key=session_key).exists()
        assert not cache.get(cache_key_with_prefix)


@pytest.mark.django_db
@pytest.mark.parametrize('county_code, expected_suffix', [('US', 'us'), ('PL', 'pl')])
def test_session_suffix(county_code, expected_suffix):
    with override_settings(API_COUNTRY=county_code):
        session = SessionStore()
        session.save()

    assert session.session_key.endswith(expected_suffix)


@pytest.mark.django_db
@pytest.mark.usefixtures('switch_on_new_login_fixture')
@patch('webapps.user.tasks.sync.sync_user_booksy_auth_task')
def test_delete_all_user_sessions(sync_user_mock):
    cache.clear()
    user = baker.make(User)
    origin = AuthOriginEnum.BOOKSY

    for _ in range(2):
        user.create_session(origin=origin, fingerprint='')

    session_keys = list(Session.objects.values_list('session_key', flat=True))

    assert len(session_keys) == 2
    # pylint: disable=protected-access
    assert len(SessionStore()._cache.keys(SessionStore().cache_key_prefix + '*')) == 2

    BooksyAuthClient.delete_all_user_sessions(user.id)

    assert Session.objects.count() == 0
    assert len(SessionStore()._cache.keys(SessionStore().cache_key_prefix + '*')) == 0
    # pylint: enable=protected-access


@pytest.mark.django_db
@pytest.mark.usefixtures('switch_on_new_login_fixture')
def test_session_mediator_load():
    session_empty_from_auth = SessionMediator(
        session_key='not_existing_key',
        new_login_turned_on=False,
    ).get_session()
    assert not list(session_empty_from_auth.keys())

    session_empty_locally = SessionMediator(
        session_key='not_existing_key',
        new_login_turned_on=True,
    ).get_session()
    assert not list(session_empty_locally.keys())

    user = baker.make(User)
    session_key = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='').session_key

    session_existing_locally = SessionMediator(
        session_key=session_key,
        new_login_turned_on=False,
    ).get_session()
    sorted_keys = sorted(session_existing_locally.keys())
    assert sorted_keys == ['origin', 'session_expire_date', 'superuser', 'user_id']
    assert session_existing_locally['user_id'] == user.id

    session_existing_from_auth = SessionMediator(
        session_key=session_key,
        new_login_turned_on=True,
    ).get_session()
    assert sorted(session_existing_from_auth.keys()) == [
        'booksy_auth_user_id',
        'origin',
        'session_expire_date',
        'superuser',
        'user_id',
    ]
    assert session_existing_from_auth['user_id'] == user.id


@pytest.mark.django_db
@pytest.mark.usefixtures('switch_on_new_login_fixture')
def test_session_mediator_get_user():
    user = baker.make(User)
    session_key = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='').session_key
    session_from_auth = SessionMediator(
        session_key=session_key,
        new_login_turned_on=True,
    ).get_session()
    session_locally = SessionMediator(
        session_key=session_key,
        new_login_turned_on=False,
    ).get_session()

    assert session_from_auth.get_user() == user
    assert session_locally.get_user() == user

    user.email = 'new_email'
    user.save(update_fields=['email'])

    assert session_locally.get_user().email == user.email
    assert session_from_auth.get_user().email == user.email


@pytest.mark.django_db
@pytest.mark.usefixtures('switch_on_new_login_fixture')
def test_session_cached_user_after_delete():
    user = baker.make(User)
    session_key = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='').session_key
    session_from_auth = SessionMediator(
        session_key=session_key,
        new_login_turned_on=True,
    ).get_session()

    assert session_from_auth.get_user() == user

    session_from_auth.delete()
    assert session_from_auth.get_user() is None
