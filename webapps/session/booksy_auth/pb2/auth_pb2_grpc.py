# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from webapps.session.booksy_auth.pb2 import auth_pb2 as booksy__auth_dot_protos_dot_auth__pb2


class AuthStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.sync_user = channel.unary_unary(
                '/Auth/sync_user',
                request_serializer=booksy__auth_dot_protos_dot_auth__pb2.SyncUserRequest.SerializeToString,
                response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.SyncUserResponse.FromString,
                )
        self.create_user = channel.unary_unary(
                '/Auth/create_user',
                request_serializer=booksy__auth_dot_protos_dot_auth__pb2.CreateUserRequest.SerializeToString,
                response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.CreateUserResponse.FromString,
                )
        self.login_user = channel.unary_unary(
                '/Auth/login_user',
                request_serializer=booksy__auth_dot_protos_dot_auth__pb2.LoginUserRequest.SerializeToString,
                response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.LoginUserResponse.FromString,
                )
        self.logout_user = channel.unary_unary(
                '/Auth/logout_user',
                request_serializer=booksy__auth_dot_protos_dot_auth__pb2.LogoutUserRequest.SerializeToString,
                response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.LogoutUserResponse.FromString,
                )
        self.session_exists = channel.unary_unary(
                '/Auth/session_exists',
                request_serializer=booksy__auth_dot_protos_dot_auth__pb2.SessionExistsRequest.SerializeToString,
                response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.SessionExistsResponse.FromString,
                )
        self.create_session = channel.unary_unary(
                '/Auth/create_session',
                request_serializer=booksy__auth_dot_protos_dot_auth__pb2.CreateSessionRequest.SerializeToString,
                response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.CreateSessionResponse.FromString,
                )
        self.delete_account = channel.unary_unary(
                '/Auth/delete_account',
                request_serializer=booksy__auth_dot_protos_dot_auth__pb2.DeleteAccountRequest.SerializeToString,
                response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.DeleteCountryAccountResponse.FromString,
                )
        self.apple_login = channel.unary_unary(
                '/Auth/apple_login',
                request_serializer=booksy__auth_dot_protos_dot_auth__pb2.AppleLoginRequest.SerializeToString,
                response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.AppleLoginResponse.FromString,
                )
        self.delete_all_user_sessions = channel.unary_unary(
                '/Auth/delete_all_user_sessions',
                request_serializer=booksy__auth_dot_protos_dot_auth__pb2.DeleteAllUserSessionsRequest.SerializeToString,
                response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.DeleteAllUserSessionsResponse.FromString,
                )
        self.bulk_sync_users = channel.unary_unary(
                '/Auth/bulk_sync_users',
                request_serializer=booksy__auth_dot_protos_dot_auth__pb2.BulkSyncUsersRequest.SerializeToString,
                response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.BulkSyncUsersResponse.FromString,
                )
        self.bulk_sync_sessions = channel.unary_unary(
                '/Auth/bulk_sync_sessions',
                request_serializer=booksy__auth_dot_protos_dot_auth__pb2.BulkSyncSessionsRequest.SerializeToString,
                response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.BulkCreateSessionResponse.FromString,
                )
        self.get_otp_code = channel.unary_unary(
            '/OTP/generate_otp_code',
            request_serializer=booksy__auth_dot_protos_dot_auth__pb2.GenerateOTPRequest.SerializeToString,
            response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.GenerateOTPResponse.FromString,
        )
        self.verify_otp_code = channel.unary_unary(
            '/OTP/verify_otp_code',
            request_serializer=booksy__auth_dot_protos_dot_auth__pb2.VerifyOTPRequest.SerializeToString,
            response_deserializer=booksy__auth_dot_protos_dot_auth__pb2.VerifyOTPResponse.FromString,
        )


class AuthServicer(object):
    """Missing associated documentation comment in .proto file."""

    def sync_user(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def create_user(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def login_user(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def logout_user(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def session_exists(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def create_session(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def delete_account(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def apple_login(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def delete_all_user_sessions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def bulk_sync_users(self, request, context):
        """TODO: REMOVE after full sync
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def bulk_sync_sessions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AuthServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'sync_user': grpc.unary_unary_rpc_method_handler(
                    servicer.sync_user,
                    request_deserializer=booksy__auth_dot_protos_dot_auth__pb2.SyncUserRequest.FromString,
                    response_serializer=booksy__auth_dot_protos_dot_auth__pb2.SyncUserResponse.SerializeToString,
            ),
            'create_user': grpc.unary_unary_rpc_method_handler(
                    servicer.create_user,
                    request_deserializer=booksy__auth_dot_protos_dot_auth__pb2.CreateUserRequest.FromString,
                    response_serializer=booksy__auth_dot_protos_dot_auth__pb2.CreateUserResponse.SerializeToString,
            ),
            'login_user': grpc.unary_unary_rpc_method_handler(
                    servicer.login_user,
                    request_deserializer=booksy__auth_dot_protos_dot_auth__pb2.LoginUserRequest.FromString,
                    response_serializer=booksy__auth_dot_protos_dot_auth__pb2.LoginUserResponse.SerializeToString,
            ),
            'logout_user': grpc.unary_unary_rpc_method_handler(
                    servicer.logout_user,
                    request_deserializer=booksy__auth_dot_protos_dot_auth__pb2.LogoutUserRequest.FromString,
                    response_serializer=booksy__auth_dot_protos_dot_auth__pb2.LogoutUserResponse.SerializeToString,
            ),
            'session_exists': grpc.unary_unary_rpc_method_handler(
                    servicer.session_exists,
                    request_deserializer=booksy__auth_dot_protos_dot_auth__pb2.SessionExistsRequest.FromString,
                    response_serializer=booksy__auth_dot_protos_dot_auth__pb2.SessionExistsResponse.SerializeToString,
            ),
            'create_session': grpc.unary_unary_rpc_method_handler(
                    servicer.create_session,
                    request_deserializer=booksy__auth_dot_protos_dot_auth__pb2.CreateSessionRequest.FromString,
                    response_serializer=booksy__auth_dot_protos_dot_auth__pb2.CreateSessionResponse.SerializeToString,
            ),
            'delete_account': grpc.unary_unary_rpc_method_handler(
                    servicer.delete_account,
                    request_deserializer=booksy__auth_dot_protos_dot_auth__pb2.DeleteAccountRequest.FromString,
                    response_serializer=booksy__auth_dot_protos_dot_auth__pb2.DeleteCountryAccountResponse.SerializeToString,
            ),
            'apple_login': grpc.unary_unary_rpc_method_handler(
                    servicer.apple_login,
                    request_deserializer=booksy__auth_dot_protos_dot_auth__pb2.AppleLoginRequest.FromString,
                    response_serializer=booksy__auth_dot_protos_dot_auth__pb2.AppleLoginResponse.SerializeToString,
            ),
            'delete_all_user_sessions': grpc.unary_unary_rpc_method_handler(
                    servicer.delete_all_user_sessions,
                    request_deserializer=booksy__auth_dot_protos_dot_auth__pb2.DeleteAllUserSessionsRequest.FromString,
                    response_serializer=booksy__auth_dot_protos_dot_auth__pb2.DeleteAllUserSessionsResponse.SerializeToString,
            ),
            'bulk_sync_users': grpc.unary_unary_rpc_method_handler(
                    servicer.bulk_sync_users,
                    request_deserializer=booksy__auth_dot_protos_dot_auth__pb2.BulkSyncUsersRequest.FromString,
                    response_serializer=booksy__auth_dot_protos_dot_auth__pb2.BulkSyncUsersResponse.SerializeToString,
            ),
            'bulk_sync_sessions': grpc.unary_unary_rpc_method_handler(
                    servicer.bulk_sync_sessions,
                    request_deserializer=booksy__auth_dot_protos_dot_auth__pb2.BulkSyncSessionsRequest.FromString,
                    response_serializer=booksy__auth_dot_protos_dot_auth__pb2.BulkCreateSessionResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'Auth', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Auth(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def sync_user(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/Auth/sync_user',
            booksy__auth_dot_protos_dot_auth__pb2.SyncUserRequest.SerializeToString,
            booksy__auth_dot_protos_dot_auth__pb2.SyncUserResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def create_user(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/Auth/create_user',
            booksy__auth_dot_protos_dot_auth__pb2.CreateUserRequest.SerializeToString,
            booksy__auth_dot_protos_dot_auth__pb2.CreateUserResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def login_user(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/Auth/login_user',
            booksy__auth_dot_protos_dot_auth__pb2.LoginUserRequest.SerializeToString,
            booksy__auth_dot_protos_dot_auth__pb2.LoginUserResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def logout_user(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/Auth/logout_user',
            booksy__auth_dot_protos_dot_auth__pb2.LogoutUserRequest.SerializeToString,
            booksy__auth_dot_protos_dot_auth__pb2.LogoutUserResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def session_exists(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/Auth/session_exists',
            booksy__auth_dot_protos_dot_auth__pb2.SessionExistsRequest.SerializeToString,
            booksy__auth_dot_protos_dot_auth__pb2.SessionExistsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def create_session(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/Auth/create_session',
            booksy__auth_dot_protos_dot_auth__pb2.CreateSessionRequest.SerializeToString,
            booksy__auth_dot_protos_dot_auth__pb2.CreateSessionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def delete_account(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/Auth/delete_account',
            booksy__auth_dot_protos_dot_auth__pb2.DeleteAccountRequest.SerializeToString,
            booksy__auth_dot_protos_dot_auth__pb2.DeleteCountryAccountResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def apple_login(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/Auth/apple_login',
            booksy__auth_dot_protos_dot_auth__pb2.AppleLoginRequest.SerializeToString,
            booksy__auth_dot_protos_dot_auth__pb2.AppleLoginResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def delete_all_user_sessions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/Auth/delete_all_user_sessions',
            booksy__auth_dot_protos_dot_auth__pb2.DeleteAllUserSessionsRequest.SerializeToString,
            booksy__auth_dot_protos_dot_auth__pb2.DeleteAllUserSessionsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def bulk_sync_users(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/Auth/bulk_sync_users',
            booksy__auth_dot_protos_dot_auth__pb2.BulkSyncUsersRequest.SerializeToString,
            booksy__auth_dot_protos_dot_auth__pb2.BulkSyncUsersResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def bulk_sync_sessions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/Auth/bulk_sync_sessions',
            booksy__auth_dot_protos_dot_auth__pb2.BulkSyncSessionsRequest.SerializeToString,
            booksy__auth_dot_protos_dot_auth__pb2.BulkCreateSessionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
