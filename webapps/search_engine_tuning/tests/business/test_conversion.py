import pytest
from django.test.utils import override_settings

from country_config.enums import Country
from lib.gcs_dataset import ConversionDataset
from lib.gcs_dataset.tools import BooksyHashDatasetMock
from webapps.business.models import Business
from webapps.search_engine_tuning.models import BusinessTuning
from webapps.search_engine_tuning.tests.tools import (
    create_business,
)


EXPECTED_CONVERSION = [-15.0, 0.0, 0.1, 100.0, 110.0]


@pytest.fixture(scope='function')
def create_data():
    for _ in range(len(EXPECTED_CONVERSION)):
        create_business()
    yield


def mocked_content(*_, **__):
    business_ids = (
        Business.objects.filter(active=True, deleted__isnull=True, visible=True)
        .values_list(
            'id',
            flat=True,
        )
        .order_by('id')
    )
    data = [
        f'{business_id},1,{conversion}'
        for business_id, conversion in zip(business_ids, EXPECTED_CONVERSION)
    ]

    return '\n'.join(['id,views,conversion'] + data)


@override_settings(API_COUNTRY=Country.US)
@pytest.mark.django_db
@pytest.mark.usefixtures('create_data')
def test_business_conversion():
    businesses = Business.objects.filter(
        active=True,
        deleted__isnull=True,
        visible=True,
    ).order_by('id')
    business_ids = [business.id for business in businesses]
    with BooksyHashDatasetMock(ConversionDataset, mocked_content()):
        BusinessTuning.update_multiple_tunings(
            ids=business_ids, fields=['conversion'], update_es=False
        )
    for business, expected_conversion in zip(businesses, EXPECTED_CONVERSION):
        assert business.has_tuning, 'Data: {}'.format(business)
        expected_conversion = max(expected_conversion, 0)
        expected = min(2000 * 1.33 * expected_conversion, 2000)
        assert business.search_tuning.conversion == expected, 'Expected: {}; Actual: {}'.format(
            expected, business.search_tuning.conversion
        )
