# Generated by Django 2.0.13 on 2020-03-24 10:08

from django.db import migrations


def migrate_old_data(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    Category = apps.get_model("utt", "Category")
    Treatment = apps.get_model("utt", "Treatment")
    CategoryTuning = apps.get_model("search_engine_tuning", "CategoryTuning")
    TreatmentTuning = apps.get_model("search_engine_tuning", "TreatmentTuning")

    categories_old = dict(
        Treatment.objects.using(db_alias)
        .filter(
            type='C',
        )
        .values_list('internal_name', 'id')
    )

    categories = Category.objects.using(db_alias).iterator()
    old2new = {}

    for category in categories:
        old2new[categories_old[category.internal_name]] = category.id

    for treatment_tuning in TreatmentTuning.objects.using(db_alias).iterator():
        CategoryTuning(
            created=treatment_tuning.created,
            updated=treatment_tuning.updated,
            category_id=old2new[treatment_tuning.treatment_id],
            last_xbookings=treatment_tuning.last_xbookings,
        ).save(using=db_alias)


class Migration(migrations.Migration):

    dependencies = [
        ('search_engine_tuning', '0020_add_category_tuning'),
        ('utt', '0007_data_migration'),
    ]

    operations = [
        migrations.RunPython(migrate_old_data, reverse_code=migrations.RunPython.noop),
    ]
