#!/usr/bin/env python
import os

from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.contrib.staticfiles.storage import staticfiles_storage
from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from lib.feature_flag.feature import ExtraAddressInCustomerConsentFlag
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business_customer_info.serializers.customer_agreements import (
    BCIAgreements,
    BusinessClientAgreementsSerializer,
    ConsentsBusinessClientSerializer,
)
from webapps.user.serializers import (
    CustomerAgreementsSerializer,
    get_default_business_customer_agreements,
)


class BusinessCustomerAgreementsView(BaseBooksySessionGenericAPIView):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)
    permission_classes = (IsAuthenticated,)
    serializer_class = CustomerAgreementsSerializer

    def get(self, request, business_pk):
        """
        Return descriptions for business customer agreements
        """

        path_to_file_template = 'pdf/gdpr/{}'
        country_file_path = path_to_file_template.format(
            settings.BOOKSY_AGREEMENT_INFORMATION_TEMPLATE.format(settings.API_COUNTRY)
        )
        absolute_path_file = os.path.join(
            settings.PROJECT_PATH,
            'statics',
            country_file_path,
        )
        if not os.path.exists(absolute_path_file):
            country_file_path = path_to_file_template.format(
                settings.BOOKSY_AGREEMENT_INFORMATION_DEFAULT,
            )

        if ExtraAddressInCustomerConsentFlag():
            business = get_object_or_404(Business.objects.select_related('region'), id=business_pk)
            business_address = business.get_location_name(with_city=True)
        else:
            business = get_object_or_404(Business, id=business_pk)
            business_address = None

        data = {
            'user_agreements': get_default_business_customer_agreements(
                business.name, business_address
            ),
            'doc_url': staticfiles_storage.url(country_file_path),
        }
        serializer = self.serializer_class(data)
        return Response(serializer.data)


class BusinessClientAgreementsView(BaseBooksySessionGenericAPIView):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)
    permission_classes = (IsAuthenticated,)
    serializer_class = BusinessClientAgreementsSerializer

    def get(self, request, *args, **kwargs):
        agreement_data = self._get_agreement_data()
        serializer = self.serializer_class(instance=agreement_data)
        return Response(serializer.data)

    def put(self, request, *args, **kwargs):
        agreement_data = self._get_agreement_data()
        if not agreement_data.customer_info:
            return Response(
                status=status.HTTP_404_NOT_FOUND,
                data={'message': 'Customer not found'},
            )

        serializer = ConsentsBusinessClientSerializer(
            instance=agreement_data.customer_info, data=request.data
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        serializer = ConsentsBusinessClientSerializer(instance=agreement_data.customer_info)
        return Response(serializer.data)

    def _get_agreement_data(self):
        business = get_object_or_404(Business, id=self.kwargs['business_pk'])
        bci = BusinessCustomerInfo.find_for_user(self.request.user, business)
        return BCIAgreements(customer_info=bci, business_info=business)
