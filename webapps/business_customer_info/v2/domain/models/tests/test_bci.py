from webapps.business_customer_info.v2.domain.models.bci import (
    Customer,
    CustomerParams,
)


class TestBusinessCustomerInfoModel:
    def test_can_instantiate_model_with_id_and_params(self):
        attributes = {
            '_id': 1,
            'params': CustomerParams(
                cell_phone='+***********',
                email='<EMAIL>',
                first_name='Adam',
                full_name='Adam Tester',
                last_name='Tester',
            ),
        }
        expected_attributes = {
            'cell_phone': '+***********',
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'full_name': 'Adam Tester',
            'id': 1,
            'last_name': 'Tester',
        }

        bci = Customer(**attributes)

        assert bci.__dict__ == expected_attributes

    def test_can_create_model_without_id(self):
        params = CustomerParams(
            cell_phone='+***********',
            email='<EMAIL>',
            first_name='Bary',
            full_name='Bary Stary',
            last_name='Stary',
        )
        expected_attributes = {
            'cell_phone': '+***********',
            'email': '<EMAIL>',
            'first_name': 'Bary',
            'full_name': 'Bary Stary',
            'id': None,
            'last_name': 'Stary',
        }

        bci = Customer.create(params=params)

        assert bci.__dict__ == expected_attributes

    def test_merge_with_id(self):
        bci1 = Customer(
            _id=1,
            params=CustomerParams(
                cell_phone='+***********',
                email='<EMAIL>',
                first_name='Adam',
                full_name='Adam Tester',
                last_name='Tester',
            ),
        )
        bci2 = Customer(
            _id=2,
            params=CustomerParams(
                cell_phone='+12345678902',
                email='',
                first_name='Bary',
                full_name='Bary Stary',
                last_name='Stary',
            ),
        )

        merged_bci = Customer.merge(bci1, bci2)

        assert merged_bci.id == 1
        assert merged_bci.full_name == 'Bary Stary'
        assert merged_bci.first_name == 'Bary'
        assert merged_bci.last_name == 'Stary'
        assert merged_bci.cell_phone == '+12345678902'
        assert merged_bci.email == '<EMAIL>'

    def test_merge_without_id(self):
        bci1 = Customer(
            _id=1,
            params=CustomerParams(
                cell_phone='+***********',
                email='<EMAIL>',
                first_name='Adam',
                full_name='Adam Tester',
                last_name='Tester',
            ),
        )
        bci2 = Customer.create(
            params=CustomerParams(
                cell_phone='+12345678902',
                email='',
                first_name='Bary',
                full_name='Bary Stary',
                last_name='Stary',
            )
        )

        merged_bci = Customer.merge(bci1, bci2)

        assert merged_bci.id == 1
        assert merged_bci.full_name == 'Bary Stary'
        assert merged_bci.first_name == 'Bary'
        assert merged_bci.last_name == 'Stary'
        assert merged_bci.cell_phone == '+12345678902'
        assert merged_bci.email == '<EMAIL>'

    def test_merge_creates_new_object(self):
        bci1 = Customer(
            _id=1,
            params=CustomerParams(
                cell_phone='+***********',
                email='<EMAIL>',
                first_name='Adam',
                full_name='Adam Tester',
                last_name='Tester',
            ),
        )
        bci2 = Customer.create(
            params=CustomerParams(
                cell_phone='+12345678902',
                email='',
                first_name='Bary',
                full_name='Bary Stary',
                last_name='Stary',
            )
        )

        merged_bci = Customer.merge(bci1, bci2)

        assert id(bci1) != id(merged_bci)
        assert id(bci2) != id(merged_bci)
