from abc import ABC, abstractmethod

from lib.abc import ThreadSafeSingletonMeta
from webapps.business_customer_info.v2.domain.models.bci import Customer
from webapps.segment.v2.application.facades.invite_analytics import SentInvitationsCounters


class BusinessCustomerInfoAbstractRepository(metaclass=ThreadSafeSingletonMeta):
    @abstractmethod
    def get_all(self, business_id: int) -> list[Customer]: ...

    @abstractmethod
    def gets_id_by_import_uid(self, business_id: int, import_uid: str) -> list[int]: ...

    @abstractmethod
    def add_or_update_many(
        self, business_id: int, bcis: list[Customer], import_uid: str
    ) -> None: ...

    # TO BE REMOVED WITHIN BOA-2636
    @abstractmethod
    def bci_invite_facade(
        self, ids: list[int], user_id: int, is_staffer_invite: bool
    ) -> SentInvitationsCounters: ...


class BusinessCustomerImportLogAbstractRepository(metaclass=ThreadSafeSingletonMeta):
    @abstractmethod
    def add_contacts_import(
        self,
        business_id: int,
        imported_count: int,
        import_uid: str,
        device: str,
    ) -> None: ...


class BCIRepository(ABC):
    @abstractmethod
    def get_number_of_invited_bcis(self, filter_params: 'BCIInvitedFilter') -> int: ...
