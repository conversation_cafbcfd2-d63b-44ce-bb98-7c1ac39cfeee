# pylint: disable=redefined-outer-name
from datetime import datetime
from unittest.mock import Mock

import pytest
from django.utils import timezone
from model_bakery import baker

from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business_customer_info.baker_recipes import bci_with_user_recipe, user_recipe
from webapps.business_customer_info.v2.domain.bci.constants import CLIENT_TYPE__UNKNOWN
from webapps.business_customer_info.v2.application.future_domain_services.bookmark import (
    BookmarkServiceImpl,
    BookmarkServiceConfig,
)


@pytest.fixture
def user():
    return user_recipe.make()


@pytest.fixture
def analytic_service():
    return Mock()


@pytest.fixture
def config(user):
    return BookmarkServiceConfig(user=user, now=timezone.make_aware(datetime(2024, 1, 1)))


@pytest.fixture
def bookmark_service(analytic_service, config):
    return BookmarkServiceImpl(analytic_service=analytic_service, config=config)


@pytest.mark.django_db
def test_categorize_business_and_bci_by_status_handles_no_businesses(bookmark_service):
    business_ids = set()
    result = bookmark_service.categorize_business_and_bci_by_status(business_ids)
    assert result == ({'skipped': [], 'updated': [], 'created': [], 'no_such_business': []}, [])


@pytest.mark.django_db
def test_categorize_business_and_bci_by_status_handles_existing_bookmarks(bookmark_service):
    bci_with_user = bci_with_user_recipe.make(bookmarked=True, user=bookmark_service.config.user)
    bci_without_user = bci_with_user_recipe.make(
        bookmarked=False, user=bookmark_service.config.user
    )
    business_ids = {bci_with_user.business.id, bci_without_user.business.id}
    result = bookmark_service.categorize_business_and_bci_by_status(business_ids)
    assert result == (
        {
            'skipped': [bci_with_user.business.id],
            'updated': [bci_without_user.business.id],
            'created': [],
            'no_such_business': [],
        },
        [bci_without_user.id],
    )


@pytest.mark.django_db
def test_create_bookmarks_creates_new_bookmarks(bookmark_service):
    biz_categorized = {'skipped': [], 'updated': [], 'created': [], 'no_such_business': []}
    duplicates_business_ids = set()
    biz1 = baker.make(Business)
    biz2 = baker.make(Business)
    business_ids = {biz1.id, biz2.id}
    created_bci_ids, updated_biz_categorized = bookmark_service.create_bookmarks(
        biz_categorized, business_ids, duplicates_business_ids
    )
    assert len(created_bci_ids) == 2
    assert updated_biz_categorized['created'] == [biz1.id, biz2.id]


@pytest.mark.django_db
def test_find_or_create_bci_with_duplicate_check_creates_new_bci(bookmark_service):
    business = baker.make(Business)
    bci, created = bookmark_service.find_or_create_bci_with_duplicate_check(
        business, use_duplicate=False, duplicate=None
    )
    assert created is True
    assert bci.user == bookmark_service.config.user


@pytest.mark.django_db
def test_find_or_create_bci_with_duplicate_check_uses_duplicate(bookmark_service):
    business = baker.make(Business)
    duplicate = bci_with_user_recipe.make(business=business, user=bookmark_service.config.user)
    bci, created = bookmark_service.find_or_create_bci_with_duplicate_check(
        business, use_duplicate=True, duplicate=duplicate
    )
    assert created is False
    assert bci == duplicate


@pytest.mark.django_db
def test_update_bookmarks_updates_existing_bookmarks(bookmark_service):
    bci1 = bci_with_user_recipe.make(user=bookmark_service.config.user, bookmarked=False)
    bci2 = bci_with_user_recipe.make(user=bookmark_service.config.user, bookmarked=False)
    bookmark_service.update_bookmarks([bci1.id], [bci2.id])
    bci1.refresh_from_db()
    bci2.refresh_from_db()
    assert bci1.bookmarked is True
    assert bci2.bookmarked is True


@pytest.mark.django_db
def test_update_client_type_for_bookmarked_updates_client_type(bookmark_service):
    bci = bci_with_user_recipe.make(
        client_type=CLIENT_TYPE__UNKNOWN, user=bookmark_service.config.user
    )
    bookmark_service.update_client_type_for_bookmarked({bci.business_id}, [bci.id])
    bci.refresh_from_db()
    assert bci.client_type == BusinessCustomerInfo.CLIENT_TYPE__BUSINESS_SUB_DEP
