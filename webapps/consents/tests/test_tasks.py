# pylint: skip-file

import datetime
from contextlib import contextmanager

from django.test import TestCase as DjangoTestCase
from freezegun import freeze_time
from mock import patch

from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.baker_recipes import booking_source_recipe
from webapps.booking.enums import AppointmentStatus, WhoMakesC<PERSON>e as Who, AppointmentType
from webapps.booking.models import Appointment, SubBooking
from webapps.business.baker_recipes import (
    bci_recipe,
    business_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
)
from webapps.consents.models import Consent, ConsentForm
from webapps.consents.tasks import (
    create_consents_task,
    dismiss_consents_task,
    update_appointment_consents_task,
)
from webapps.pos.baker_recipes import default_pos_recipe


@contextmanager
def delay_update_appointment_consents_task():
    try:
        with patch.object(update_appointment_consents_task, 'delay') as mock_delay:
            yield
    finally:
        # run delayed calls
        for call in mock_delay.call_args_list:
            update_appointment_consents_task.delay(*call.args, **call.kwargs)


class TestCreateConsentsTask(DjangoTestCase):

    @classmethod
    def setUpTestData(cls):
        cls.business = business_recipe.make()
        cls.service = service_recipe.make(business=cls.business)
        cls.service_variant = service_variant_recipe.make(service=cls.service)
        cls.staffer = staffer_recipe.make(business=cls.business)
        cls.booking_source = booking_source_recipe.make()
        cls.customer = bci_recipe.make(business=cls.business)

        cls.consent_form = ConsentForm.objects.create(business=cls.business)
        cls.consent_form.services.add(cls.service)

    @patch.object(Consent, 'render_pdf', return_value=None)
    def test_booking_before_form_created(self, *mocks):
        appointment = Appointment(
            business=self.business,
            booked_for=self.customer,
            source=self.booking_source,
            status=Appointment.STATUS.FINISHED,
            updated_by=self.business.owner,
        )
        booking = SubBooking(
            booked_from=self.consent_form.created - datetime.timedelta(hours=3),
            booked_till=self.consent_form.created - datetime.timedelta(hours=2),
            service_variant=self.service_variant,
            autoassign=True,
            appointment=appointment,
        )
        with delay_update_appointment_consents_task():
            appointment.make_appointment(
                [booking],
                who_makes_change=Who.BUSINESS,
                overbooking=True,
            )

        create_consents_task.run()

        self.assertEqual(Consent.objects.filter(form=self.consent_form).count(), 0)

    @patch.object(Consent, 'render_pdf', return_value=None)
    def test_booking_after_form_created(self, *mocks):
        appointment = Appointment(
            source=self.booking_source,
            status=Appointment.STATUS.FINISHED,
            updated_by=self.business.owner,
            business=self.business,
            booked_for=self.customer,
        )
        booking = SubBooking(
            booked_from=self.consent_form.created + datetime.timedelta(hours=2),
            booked_till=self.consent_form.created + datetime.timedelta(hours=3),
            service_variant=self.service_variant,
            autoassign=True,
            appointment=appointment,
        )
        with delay_update_appointment_consents_task():
            appointment.make_appointment(
                [booking],
                who_makes_change=Who.BUSINESS,
                overbooking=True,
            )

        create_consents_task.run()

        self.assertEqual(Consent.objects.filter(form=self.consent_form).count(), 1)


class TestUpdateAppointmentConsentsTask(DjangoTestCase):

    @classmethod
    def setUpTestData(cls):
        cls.business = business_recipe.make()
        cls.service = service_recipe.make(business=cls.business)
        cls.service_variant = service_variant_recipe.make(service=cls.service)
        cls.booking_source = booking_source_recipe.make()
        cls.customer = bci_recipe.make(business=cls.business)

        cls.consent_form = ConsentForm.objects.create(business=cls.business)
        cls.consent_form.services.add(cls.service)

        default_pos_recipe.make()

    @patch.object(Consent, 'render_pdf', return_value=None)
    def test_during_make_booking(self, *mocks):
        appointment = Appointment(
            business=self.business,
            booked_for=self.customer,
            source=self.booking_source,
            status=Appointment.STATUS.ACCEPTED,
            updated_by=self.business.owner,
        )
        booking = SubBooking(
            booked_from=self.consent_form.created + datetime.timedelta(hours=1),
            booked_till=self.consent_form.created + datetime.timedelta(hours=2),
            service_variant=self.service_variant,
            autoassign=True,
            appointment=appointment,
        )
        appointment.make_appointment(
            [booking],
            who_makes_change=Who.BUSINESS,
            overbooking=True,
        )

        self.assertEqual(Consent.objects.filter(form=self.consent_form).count(), 1)

    @patch.object(Consent, 'render_pdf', return_value=None)
    def test_skipping(self, *mocks):
        other_service = service_recipe.make(business=self.business)
        other_service_variant = service_variant_recipe.make(service=other_service)
        appointment = Appointment(
            business=self.business,
            booked_for=self.customer,
            source=self.booking_source,
            status=Appointment.STATUS.ACCEPTED,
            updated_by=self.business.owner,
        )
        booking = SubBooking(
            booked_from=self.consent_form.created - datetime.timedelta(hours=3),
            booked_till=self.consent_form.created - datetime.timedelta(hours=2),
            service_variant=other_service_variant,
            autoassign=True,
            appointment=appointment,
        )
        appointment.make_appointment(
            [booking],
            who_makes_change=Who.BUSINESS,
            overbooking=True,
        )

        self.assertEqual(Consent.objects.filter(form=self.consent_form).count(), 0)

    @patch.object(Consent, 'render_pdf', return_value=None)
    def test_required_per_appointment_single_booking(self, *mocks):
        self.consent_form.required_per_appointment = True
        self.consent_form.save()

        appointment_1 = Appointment(
            business=self.business,
            booked_for=self.customer,
            source=self.booking_source,
            status=Appointment.STATUS.ACCEPTED,
            updated_by=self.business.owner,
        )
        booking_1 = SubBooking(
            booked_from=self.consent_form.created + datetime.timedelta(hours=1),
            booked_till=self.consent_form.created + datetime.timedelta(hours=2),
            service_variant=self.service_variant,
            autoassign=True,
            appointment=appointment_1,
        )
        appointment_1.make_appointment(
            [booking_1],
            who_makes_change=Who.BUSINESS,
            overbooking=True,
        )
        consent_1 = Consent.objects.get(form=self.consent_form)

        # expect second booking to have a new consent
        appointment_2 = Appointment(
            business=self.business,
            booked_for=self.customer,
            source=self.booking_source,
            status=Appointment.STATUS.ACCEPTED,
            updated_by=self.business.owner,
        )
        booking_2 = SubBooking(
            booked_from=self.consent_form.created + datetime.timedelta(hours=2),
            booked_till=self.consent_form.created + datetime.timedelta(hours=3),
            service_variant=self.service_variant,
            autoassign=True,
            appointment=appointment_2,
        )
        appointment_2.make_appointment(
            [booking_2],
            who_makes_change=Who.BUSINESS,
            overbooking=True,
        )
        consent_2 = Consent.objects.exclude(uuid=consent_1.uuid).get(
            form=self.consent_form,
        )

        self.assertEqual(consent_1.subbooking_id, None)
        self.assertEqual(consent_1.appointment_id, booking_1.appointment_id)

        self.assertEqual(consent_2.subbooking_id, None)
        self.assertEqual(consent_2.appointment_id, booking_2.appointment_id)

    @patch.object(Consent, 'render_pdf', return_value=None)
    def test_required_per_appointment_multi_booking(self, *render):
        self.consent_form.required_per_appointment = True
        self.consent_form.save()

        consent_form_1 = self.consent_form

        service_2 = service_recipe.make(business=self.business)
        service_variant_2 = service_variant_recipe.make(service=service_2)
        consent_form_2 = ConsentForm.objects.create(
            business=self.business, required_per_appointment=True
        )
        consent_form_2.services.add(service_2)

        for _iteration in range(2):
            appointment = Appointment(
                business=self.business,
                booked_for=self.customer,
                source=self.booking_source,
                status=Appointment.STATUS.ACCEPTED,
                updated_by=self.business.owner,
                type=AppointmentType.BUSINESS,
            )
            booking_1 = SubBooking(
                booked_from=self.consent_form.created + datetime.timedelta(hours=1 + _iteration),
                booked_till=self.consent_form.created
                + datetime.timedelta(hours=1 + _iteration, minutes=30),
                service_variant=self.service_variant,
                autoassign=True,
                appointment=appointment,
            )

            booking_2 = SubBooking(
                booked_from=self.consent_form.created
                + datetime.timedelta(hours=1 + _iteration, minutes=30),
                booked_till=self.consent_form.created
                + datetime.timedelta(hours=1 + _iteration, minutes=60),
                service_variant=service_variant_2,
                autoassign=True,
                appointment=appointment,
            )

            appointment = AppointmentWrapper([booking_1, booking_2])
            appointment.save(
                to_delete=[],
                overbooking=True,
                who_makes_change=Who.BUSINESS,
                update_future=False,
            )

            self.assertEqual(Consent.objects.filter(form=consent_form_1).count(), _iteration + 1)
            self.assertEqual(Consent.objects.filter(form=consent_form_2).count(), _iteration + 1)

            consent_1 = Consent.objects.filter(form=consent_form_1).latest()
            self.assertEqual(consent_1.subbooking_id, None)
            self.assertEqual(consent_1.appointment_id, appointment.id)

            consent_2 = Consent.objects.filter(form=consent_form_2).latest()
            self.assertEqual(consent_2.subbooking_id, None)
            self.assertEqual(consent_2.appointment_id, appointment.id)


class TestDismissConsentsTask(DjangoTestCase):

    @classmethod
    def setUpTestData(cls):
        cls.business = business_recipe.make()
        cls.service = service_recipe.make(business=cls.business)
        cls.service_variant = service_variant_recipe.make(service=cls.service)
        cls.booking_source = booking_source_recipe.make()
        cls.customer = bci_recipe.make(business=cls.business)

        cls.consent_form_1 = ConsentForm.objects.create(business=cls.business)
        cls.consent_form_2 = ConsentForm.objects.create(
            business=cls.business,
            required_per_appointment=True,
        )

    def setUp(self):
        super().setUp()

        self.consent_form_1.services.add(self.service)

    def assert_preserved(self, consent):
        self.assertTrue(
            Consent.all_objects.filter(uuid=consent.uuid, deleted__isnull=True).exists()
        )

    def assert_dismissed(self, consent):
        self.assertTrue(
            Consent.all_objects.filter(uuid=consent.uuid, deleted__isnull=False).exists()
        )

    def _test_booking_accepted(self, status):
        consent = self.consent_form_1.get_or_create_consent(self.customer, notify=False)

        appointment = Appointment(
            business=self.business,
            booked_for=self.customer,
            source=self.booking_source,
            status=status,
            updated_by=self.business.owner,
        )
        booking = SubBooking(
            booked_from=consent.created + datetime.timedelta(hours=2),
            booked_till=consent.created + datetime.timedelta(hours=3),
            service_variant=self.service_variant,
            autoassign=True,
            appointment=appointment,
        )
        appointment.make_appointment(
            [booking],
            who_makes_change=Who.BUSINESS,
            overbooking=True,
        )

        with freeze_time(consent.updated + datetime.timedelta(days=1)):
            dismiss_consents_task.run()
            self.assert_preserved(consent)

    @patch.object(Consent, 'render_pdf', return_value=None)
    def test_booking_accepted(self, *mocks):
        self._test_booking_accepted(AppointmentStatus.ACCEPTED)
        self._test_booking_accepted(AppointmentStatus.FINISHED)

    def _test_booking_cancelled(self, status):
        consent = self.consent_form_1.get_or_create_consent(self.customer, notify=False)

        appointment = Appointment(
            business=self.business,
            booked_for=self.customer,
            source=self.booking_source,
            status=status,
            updated_by=self.business.owner,
        )
        booking = SubBooking(
            booked_from=consent.created + datetime.timedelta(hours=2),
            booked_till=consent.created + datetime.timedelta(hours=3),
            service_variant=self.service_variant,
            autoassign=True,
            appointment=appointment,
        )
        appointment.make_appointment(
            [booking],
            who_makes_change=Who.BUSINESS,
            overbooking=True,
        )

        with freeze_time(consent.updated + datetime.timedelta(days=1)):
            dismiss_consents_task.run()
            self.assert_dismissed(consent)

    @patch.object(Consent, 'render_pdf', return_value=None)
    def test_booking_cancelled(self, *mocks):
        self._test_booking_cancelled(Appointment.STATUS.CANCELED)
        self._test_booking_cancelled(Appointment.STATUS.NOSHOW)
        self._test_booking_cancelled(Appointment.STATUS.DECLINED)
        self._test_booking_cancelled(Appointment.STATUS.REJECTED)

    @patch.object(Consent, 'render_pdf', return_value=None)
    def test_not_required_for_service(self, *mocks):
        self.consent_form_1.services.clear()

        consent = self.consent_form_1.get_or_create_consent(self.customer, notify=False)
        appointment = Appointment(
            business=self.business,
            booked_for=self.customer,
            source=self.booking_source,
            status=Appointment.STATUS.ACCEPTED,
            updated_by=self.business.owner,
        )
        booking = SubBooking(
            booked_from=consent.created + datetime.timedelta(hours=2),
            booked_till=consent.created + datetime.timedelta(hours=3),
            service_variant=self.service_variant,
            autoassign=True,
            appointment=appointment,
        )
        appointment.make_appointment(
            [booking],
            who_makes_change=Who.BUSINESS,
            overbooking=True,
        )

        with freeze_time(consent.updated + datetime.timedelta(days=1)):
            dismiss_consents_task.run()
            self.assert_dismissed(consent)

    @patch.object(Consent, 'render_pdf', return_value=None)
    def test_signed(self, *mocks):
        consent = self.consent_form_1.get_or_create_consent(self.customer, notify=False)

        consent.signed = consent.updated
        consent.save()

        # with no booking
        with freeze_time(consent.updated + datetime.timedelta(days=1)):
            dismiss_consents_task.run()
            self.assert_preserved(consent)

        # with booking
        appointment = Appointment(
            business=self.business,
            booked_for=self.customer,
            source=self.booking_source,
            status=Appointment.STATUS.CANCELED,
            updated_by=self.business.owner,
        )
        booking = SubBooking(
            booked_from=consent.created + datetime.timedelta(hours=2),
            booked_till=consent.created + datetime.timedelta(hours=3),
            service_variant=self.service_variant,
            autoassign=True,
            appointment=appointment,
        )
        appointment.make_appointment(
            [booking],
            who_makes_change=Who.BUSINESS,
            overbooking=True,
        )

        with freeze_time(consent.updated + datetime.timedelta(days=1)):
            dismiss_consents_task.run()
            self.assert_preserved(consent)
