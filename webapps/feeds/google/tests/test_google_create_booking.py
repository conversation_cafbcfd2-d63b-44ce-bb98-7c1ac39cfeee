# pylint: disable=astroid-error,unused-argument
from datetime import timedelta, datetime

import pytest
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.test import TestCase
from google.protobuf.json_format import ParseDict
from mock import patch
from model_bakery import baker
from pytz import UTC
from segment.analytics import Client

from lib.deeplink.branchio.client import BranchIOClient
from lib.facebook.enums import EventName
from lib.facebook.service import FacebookEventService
from lib.feature_flag.feature import (
    BranchIOCustomerBookingTrackingFlag,
    FacebookAnalyticsCustomerBookingTrackingFlag,
)
from lib.feature_flag.feature.booking import TrackAppointmentCreatedAnalytics
from lib.test_utils import (
    create_staffer,
    create_appliance,
    create_subbooking,
)
from lib.tests.utils import override_eppo_feature_flag, override_feature_flag
from lib.tools import datetime_to_timestamp
from lib.tools import tznow, external_api_to_id, id_to_external_api
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.booking.models import Appointment
from webapps.booking.models import BookingSources, BookingResource
from webapps.booking.models import SubBooking
from webapps.business.models import (
    Business,
    Service,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.business.models.bci import (
    BusinessCustomerInfo,
    BCIFromContact,
)
from webapps.consts import GOOGLE
from webapps.feeds.google.ingress.bridge import (
    ext_maps_booking_partner_v3_pb2 as booking_service_pb2,
)
from webapps.feeds.models import GoogleBooking
from webapps.kill_switch.models import KillSwitch
from webapps.segment.consts import UserRoleEnum
from webapps.segment.enums import DeviceTypeName
from webapps.user.models import User
from webapps.zoom.models import ZoomBusinessCredentials, ZoomMeeting
from webapps.zoom.tests.client import ZoomTestClient


@pytest.mark.django_db
class GoogleFeedsCreateBookingTestCase(BaseAsyncHTTPTest, TestCase):
    url = '/google_feeds/v3/CreateBooking/?'

    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()
        self.service = baker.make(Service, business=self.business)
        self.staffer = create_staffer(self.business, service=self.service, calendar=True)
        self.appliance = create_appliance(self.business, service=self.service, calendar=True)
        self.service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=self.service,
            time_slot_interval=relativedelta(minutes=15),
        )
        self.service_variant.add_staffers([self.staffer])
        self.source = baker.make(
            BookingSources,
            name=GOOGLE,
        )

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @pytest.mark.freeze_time(datetime(2018, 10, 15, 12, tzinfo=UTC))
    def test_create_booking_missing_params(self, analytics_track_mock, analytics_identify_mock):
        google_booking = baker.make(
            GoogleBooking,
            lease_expiration_time=tznow() + timedelta(hours=1),
            merchant_id=self.business.id,
            service_id=self.service_variant.id,
            staffer_id=self.staffer.id,
            booked_from=(tznow() + relativedelta(hours=3)).replace(
                minute=0, second=0, microsecond=0
            ),
            booked_till=(tznow() + relativedelta(hours=3)).replace(
                minute=15, second=0, microsecond=0
            ),
        )
        body = {
            'lease_id': google_booking.lease_id,
            'email': '<EMAIL>',
            'family_name': 'Welcome',
            'given_name': 'Booksy',
            'request_uuid': 'd8613f3556f24974b3260159db73213d',
            'telephone': '***********',
        }
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 400
        assert resp.json['grpc_error_status'] == 'INVALID_ARGUMENT'

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @pytest.mark.freeze_time(datetime(2018, 10, 15, 12, tzinfo=UTC))
    def test_create_booking_existing_idempotency_token(
        self, analytics_track_mock, analytics_identify_mock
    ):
        token = '123456'
        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        booked_till = (tznow() + relativedelta(hours=3)).replace(minute=15, second=0, microsecond=0)
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws={
                'booked_from': booked_from,
                'booked_till': booked_till,
                'updated_by': self.user,
                'deleted': tznow(),
                'status': Appointment.STATUS.ACCEPTED,
            },
        )
        google_booking = baker.make(
            GoogleBooking,
            lease_expiration_time=tznow() + timedelta(hours=1),
            merchant_id=self.business.id,
            service_id=self.service_variant.id,
            staffer_id=self.staffer.id,
            booking_id=booking.id,
            booked_from=booked_from,
            booked_till=booked_till,
            idempotency_token=token,
        )
        body = {
            'slot': {
                'merchant_id': f'dev-us-{self.business.id}',
                'service_id': f'dev-us-{self.service_variant.id}',
                'start_sec': '1601388000',
                'duration_sec': '1800',
                'resources': {'staff_id': f'dev-us-{self.staffer.id}'},
            },
            'idempotency_token': token,
        }
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 200
        booking_data = resp.json
        response = booking_service_pb2.CreateBookingResponse()
        response_obj = ParseDict(js_dict=booking_data, message=response)
        booking_id = external_api_to_id(response_obj.booking.booking_id).get('business_id')
        assert SubBooking.objects.filter(id=booking_id).count() == 1
        booking = SubBooking.objects.get(id=booking_id)
        assert booking.booked_from == google_booking.booked_from
        assert booking.booked_till == google_booking.booked_till

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @pytest.mark.freeze_time(datetime(2018, 10, 15, 12, tzinfo=UTC))
    def test_create_booking_non_staffer(self, analytics_track_mock, analytics_identify_mock):
        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(self.service_variant.id),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.appliance.id)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': 'Booksy',
                'family_name': 'Welcome',
                'telephone': '***********',
                'email': '<EMAIL>',
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '12330',
        }
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 200
        assert resp.json['bookingFailure']['cause'] == 'SLOT_UNAVAILABLE'

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @pytest.mark.freeze_time(datetime(2018, 10, 15, 12, tzinfo=UTC))
    def test_create_booking_prepay_service(self, analytics_track_mock, analytics_identify_mock):
        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            time_slot_interval=relativedelta(minutes=15),
        )
        baker.make(
            ServiceVariantPayment,
            service_variant=service_variant,
            payment_amount=10,
        )
        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(service_variant.id),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.staffer.id)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': 'Booksy',
                'family_name': 'Welcome',
                'telephone': '***********',
                'email': '<EMAIL>',
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '12330',
        }
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 200
        assert resp.json['bookingFailure']['cause'] == 'SLOT_UNAVAILABLE'

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @pytest.mark.freeze_time(datetime(2018, 10, 15, 12, tzinfo=UTC))
    @override_eppo_feature_flag({TrackAppointmentCreatedAnalytics.flag_name: True})
    def test_create_booking_with_staffer_and_appliance(
        self, analytics_track_mock, analytics_identify_mock
    ):
        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(self.service_variant.id),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.staffer.id)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': 'Booksy',
                'family_name': 'Welcome',
                'telephone': '***********',
                'email': '<EMAIL>',
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '1233',
        }

        with self.captureOnCommitCallbacks(execute=True):
            resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 200
        booking_data = resp.json
        response = booking_service_pb2.CreateBookingResponse()
        response_obj = ParseDict(js_dict=booking_data, message=response)
        booking_id = external_api_to_id(response_obj.booking.booking_id).get('business_id')
        assert SubBooking.objects.filter(id=booking_id).count() == 1
        booking = SubBooking.objects.get(id=booking_id)
        assert GoogleBooking.objects.filter(booking_id=booking_id).count() == 1
        google_booking = GoogleBooking.objects.get(booking_id=booking_id)
        assert booking.booked_from == google_booking.booked_from
        assert booking.booked_from == booked_from
        assert booking.booked_till == google_booking.booked_till
        diff = int((booking.booked_till - booking.booked_from).total_seconds())
        assert diff_in_seconds == diff
        assert booking.staffer == self.staffer
        assert booking.appliance == self.appliance

        appointment = booking.appointment

        assert appointment.booked_for.first_appointment_id == (booking.appointment_id)
        assert appointment.booked_for.client_type == (
            appointment.booked_for.CLIENT_TYPE__GOOGLE_PARTNER
        )
        assert not ZoomMeeting.objects.filter(
            business=self.business,
            appointment_id=booking.appointment_id,
        ).exists()
        assert analytics_track_mock.call_count == 4
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Appointment_Created',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'appointment_id': id_to_external_api(booking.appointment_id),
                    'booksy_pay_eligible': False,
                    'booksy_pay_available': False,
                },
            },
        )

        dict_assert(
            analytics_track_mock.call_args_list[1][1],
            {
                'event': 'CB_Created_For_Customer',
                'user_id': None,
                'properties': {
                    'country': settings.API_COUNTRY,
                    'device_type': DeviceTypeName.UNKNOWN,
                    'email': '<EMAIL>',
                    # 'booked_from': '2018-10-15T15:00:00+00:00',
                    'staff_id': [id_to_external_api(self.staffer.id)],
                    'service_price': [],
                    'service_id': [
                        id_to_external_api(self.service_variant.service.id),
                    ],
                    'service_name': [self.service_variant.service.name],
                    'category_id': [],
                    'category_name': [],
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': None,
                    'energy_booking': appointment.get_chargeable(),
                    'booking_score': 0,
                    'source': 'GoogleFeeds',
                    'appointment_id': id_to_external_api(appointment.id),
                    'appointment_type': 'Single',
                    'booked_from': '2018-10-15T15:00:00+00:00',
                    'business_postal_code': self.business.zip,
                    'focus_area': None,
                    'is_xCB': None,
                    'is_xCB_xCategory': None,
                    'urban_area': None,
                    'urban_subarea': None,
                },
            },
        )

        dict_assert(
            analytics_track_mock.call_args_list[2][1],
            {
                'event': 'CB_Created_For_Business',
                'properties': {
                    'staff_id': [id_to_external_api(self.staffer.id)],
                    'service_price': [],
                    'service_id': [
                        id_to_external_api(self.service_variant.service.id),
                    ],
                    # 'business_id': self.business.id,
                    'country': settings.API_COUNTRY,
                    'email': '<EMAIL>',
                    # 'booked_from': '2018-10-15T15:00:00+00:00',
                    'service_name': [self.service_variant.service.name],
                    'category_id': [],
                    'category_name': [],
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': None,
                    'energy_booking': appointment.get_chargeable(),
                    'booking_score': 0,
                    'appointment_id': id_to_external_api(appointment.id),
                    'appointment_type': 'Single',
                    'booked_from': '2018-10-15T15:00:00+00:00',
                    'business_postal_code': self.business.zip,
                    'focus_area': None,
                    'urban_area': None,
                    'urban_subarea': None,
                    'customer_id': None,
                    'client_from_invite': False,
                },
            },
        )

        dict_assert(
            analytics_track_mock.call_args_list[3][1],
            {'event': '1st_CB_Created_For_Business'},
        )

        assert analytics_identify_mock.call_count == 3
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'traits': {
                    'email': '<EMAIL>',
                    'CB_count': 1,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[1][1],
            {
                'traits': {
                    'user_role': UserRoleEnum.OWNER,
                    'email': self.business.owner.email,
                    'business_CB_count': 1,
                },
            },
        )

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @pytest.mark.freeze_time(datetime(2018, 10, 15, 12, tzinfo=UTC))
    @override_eppo_feature_flag({TrackAppointmentCreatedAnalytics.flag_name: True})
    def test_create_booking(self, analytics_track_mock, analytics_identify_mock):
        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(self.service_variant.id),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.staffer.id)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': 'Booksy',
                'family_name': 'Welcome',
                'telephone': '***********',
                'email': '<EMAIL>',
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '1233',
        }

        with self.captureOnCommitCallbacks(execute=True):
            resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 200
        booking_data = resp.json
        response = booking_service_pb2.CreateBookingResponse()
        response_obj = ParseDict(js_dict=booking_data, message=response)
        booking_id = external_api_to_id(response_obj.booking.booking_id).get('business_id')
        assert SubBooking.objects.filter(id=booking_id).count() == 1
        booking = SubBooking.objects.get(id=booking_id)
        assert GoogleBooking.objects.filter(booking_id=booking_id).count() == 1
        google_booking = GoogleBooking.objects.get(booking_id=booking_id)
        assert booking.booked_from == google_booking.booked_from
        assert booking.booked_from == booked_from
        assert booking.booked_till == google_booking.booked_till
        assert booking.is_staffer_requested_by_client
        diff = int((booking.booked_till - booking.booked_from).total_seconds())
        assert diff_in_seconds == diff
        appointment = booking.appointment

        assert appointment.booked_for.first_appointment_id == (booking.appointment_id)
        assert appointment.booked_for.client_type == (
            appointment.booked_for.CLIENT_TYPE__GOOGLE_PARTNER
        )
        assert not ZoomMeeting.objects.filter(
            business=self.business,
            appointment_id=booking.appointment_id,
        ).exists()

        assert analytics_track_mock.call_count == 4
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Appointment_Created',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'appointment_id': id_to_external_api(booking.appointment_id),
                    'booksy_pay_eligible': False,
                    'booksy_pay_available': False,
                },
            },
        )

        dict_assert(
            analytics_track_mock.call_args_list[1][1],
            {
                'event': 'CB_Created_For_Customer',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'device_type': DeviceTypeName.UNKNOWN,
                    'email': '<EMAIL>',
                    # 'booked_from': '2018-10-15T15:00:00+00:00',
                    'staff_id': [id_to_external_api(self.staffer.id)],
                    'service_price': [],
                    'service_id': [
                        id_to_external_api(self.service_variant.service.id),
                    ],
                    'service_name': [self.service_variant.service.name],
                    'category_id': [],
                    'category_name': [],
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': None,
                    'energy_booking': appointment.get_chargeable(),
                    'booking_score': 0,
                    'source': 'GoogleFeeds',
                },
            },
        )

        dict_assert(
            analytics_track_mock.call_args_list[2][1],
            {
                'event': 'CB_Created_For_Business',
                'properties': {
                    'country': settings.API_COUNTRY,
                    # 'device': None,
                    'email': self.business.owner.email,
                    'customer_id': None,
                    # 'booked_from': '2018-10-15T15:00:00+00:00',
                    'staff_id': [id_to_external_api(self.staffer.id)],
                    'service_price': [],
                    'service_id': [id_to_external_api(self.service_variant.service.id)],
                    'service_name': [self.service_variant.service.name],
                    'category_id': [],
                    'category_name': [],
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': None,
                    'is_xCB': appointment.is_crossing(),
                    'is_xCB_xCategory': None,
                    'energy_booking': appointment.get_chargeable(),
                    'booking_score': 0,
                    'client_from_invite': False,
                },
            },
        )
        dict_assert(
            analytics_track_mock.call_args_list[3][1],
            {'event': '1st_CB_Created_For_Business'},
        )

        assert analytics_identify_mock.call_count == 3
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'traits': {
                    'email': '<EMAIL>',
                    'CB_count': 1,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[1][1],
            {
                'traits': {
                    'user_role': UserRoleEnum.OWNER,
                    'email': self.business.owner.email,
                    'business_CB_count': 1,
                },
            },
        )

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @pytest.mark.freeze_time(datetime(2018, 10, 15, 12, tzinfo=UTC))
    def test_create_booking_martech_analytics_killswitch(
        self, analytics_track_mock, analytics_identify_mock
    ):
        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.MARTECH_ANALYTICS,
            is_killed=True,
        )
        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(self.service_variant.id),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.staffer.id)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': 'Booksy',
                'family_name': 'Welcome',
                'telephone': '***********',
                'email': '<EMAIL>',
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '1234',
        }

        with self.captureOnCommitCallbacks(execute=True):
            resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 200
        booking_data = resp.json
        response = booking_service_pb2.CreateBookingResponse()
        response_obj = ParseDict(js_dict=booking_data, message=response)
        booking_id = external_api_to_id(response_obj.booking.booking_id).get('business_id')
        assert SubBooking.objects.filter(id=booking_id).count() == 1
        booking = SubBooking.objects.get(id=booking_id)
        assert GoogleBooking.objects.filter(booking_id=booking_id).count() == 1
        google_booking = GoogleBooking.objects.get(booking_id=booking_id)
        assert booking.booked_from == google_booking.booked_from
        assert booking.booked_till == google_booking.booked_till
        appointment = booking.appointment
        assert appointment.booked_for.first_appointment_id == (booking.appointment_id)
        assert appointment.booked_for.client_type == (
            appointment.booked_for.CLIENT_TYPE__GOOGLE_PARTNER
        )
        assert not ZoomMeeting.objects.filter(
            business=self.business,
            appointment=booking.appointment,
        ).exists()

        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @override_feature_flag(
        {
            BranchIOCustomerBookingTrackingFlag.flag_name: True,
        }
    )
    @patch.object(BranchIOClient, 'track_event')
    @pytest.mark.freeze_time(datetime(2018, 10, 15, 12, tzinfo=UTC))
    def test_create_booking_branchio_analytics(
        self,
        analytics_track_mock,
    ):
        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(self.service_variant.id),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.staffer.id)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': 'Booksy',
                'family_name': 'Welcome',
                'telephone': '***********',
                'email': '<EMAIL>',
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '1234',
        }

        with self.captureOnCommitCallbacks(execute=True):
            resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 200
        booking_data = resp.json
        response = booking_service_pb2.CreateBookingResponse()
        response_obj = ParseDict(js_dict=booking_data, message=response)
        booking_id = external_api_to_id(response_obj.booking.booking_id).get('business_id')
        assert SubBooking.objects.filter(id=booking_id).count() == 1
        booking = SubBooking.objects.get(id=booking_id)
        assert GoogleBooking.objects.filter(booking_id=booking_id).count() == 1
        google_booking = GoogleBooking.objects.get(booking_id=booking_id)
        assert booking.booked_from == google_booking.booked_from
        assert booking.booked_from == booked_from
        assert booking.booked_till == google_booking.booked_till
        assert booking.is_staffer_requested_by_client
        diff = int((booking.booked_till - booking.booked_from).total_seconds())
        assert diff_in_seconds == diff
        appointment = booking.appointment

        assert appointment.booked_for.first_appointment_id == (booking.appointment_id)
        assert appointment.booked_for.client_type == (
            appointment.booked_for.CLIENT_TYPE__GOOGLE_PARTNER
        )
        assert not ZoomMeeting.objects.filter(
            business=self.business,
            appointment_id=booking.appointment_id,
        ).exists()

        assert analytics_track_mock.call_count == 1
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {'event_name': '1st_CB_Created_For_Business'},
        )

    @override_feature_flag(
        {
            FacebookAnalyticsCustomerBookingTrackingFlag: True,
        }
    )
    @patch.object(FacebookEventService, 'send_event_with_custom_data')
    @pytest.mark.freeze_time(datetime(2018, 10, 15, 12, tzinfo=UTC))
    def test_create_booking_facebook_analytics(
        self,
        facebook_send_event_mock,
    ):
        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(self.service_variant.id),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.staffer.id)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': 'Booksy',
                'family_name': 'Welcome',
                'telephone': '***********',
                'email': '<EMAIL>',
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '1234',
        }

        with self.captureOnCommitCallbacks(execute=True):
            resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 200
        booking_data = resp.json
        response = booking_service_pb2.CreateBookingResponse()
        response_obj = ParseDict(js_dict=booking_data, message=response)
        booking_id = external_api_to_id(response_obj.booking.booking_id).get('business_id')
        assert SubBooking.objects.filter(id=booking_id).count() == 1
        booking = SubBooking.objects.get(id=booking_id)
        assert GoogleBooking.objects.filter(booking_id=booking_id).count() == 1
        google_booking = GoogleBooking.objects.get(booking_id=booking_id)
        assert booking.booked_from == google_booking.booked_from
        assert booking.booked_from == booked_from
        assert booking.booked_till == google_booking.booked_till
        assert booking.is_staffer_requested_by_client
        diff = int((booking.booked_till - booking.booked_from).total_seconds())
        assert diff_in_seconds == diff
        appointment = booking.appointment

        assert appointment.booked_for.first_appointment_id == (booking.appointment_id)
        assert appointment.booked_for.client_type == (
            appointment.booked_for.CLIENT_TYPE__GOOGLE_PARTNER
        )
        assert not ZoomMeeting.objects.filter(
            business=self.business,
            appointment_id=booking.appointment_id,
        ).exists()

        facebook_send_event_mock.assert_called_once()
        assert (
            facebook_send_event_mock.call_args.kwargs['event_name']
            == EventName.FIRST_CB_CREATED_FOR_BUSINESS
        )
        assert facebook_send_event_mock.call_args.kwargs['event_time'] == 1539604800

        property_keys = [
            'business_postal_code',
            'booked_from',
            'appointment_type',
            'business_id',
            'appointment_id',
            'customer_id',
            'is_xCB',
            'is_xCB_xCategory',
            'energy_booking',
            'business_name',
            'business_primary_category',
            'family_and_friends_role',
            'source',
            'category_id',
            'category_name',
            'service_id',
            'service_name',
            'service_price',
            'staff_id',
            'booking_score',
            'urban_area',
            'urban_subarea',
            'focus_area',
            'email',
        ]
        assert all(p in facebook_send_event_mock.call_args.kwargs['data'] for p in property_keys)
        assert facebook_send_event_mock.call_args.kwargs['user_data'] is None

    def test_google_grpc_blocked_business_customer_info(self):
        baker.make(
            User,
            email='<EMAIL>',
            cell_phone='784591838',
            first_name='Maciej',
            last_name='Gebarski',
        )
        bci, _c = BusinessCustomerInfo.get_or_create_from_contact(
            BCIFromContact(
                business=self.business,
                customer_email='<EMAIL>',
            ),
        )
        bci.blacklisted = True
        bci.save()

        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(self.service_variant.id),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.staffer.id)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': 'Maciej',
                'family_name': 'Gebarski',
                'telephone': '784591838',
                'email': '<EMAIL>',
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '1235',
        }

        resp = self.fetch(self.url, method='POST', body=body)
        assert resp.code == 200
        assert resp.json['bookingFailure']['cause'] == 'USER_OVER_BOOKING_LIMIT'

    def test_create_booking_invalid_argument(self):
        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(self.service_variant.id),
                'start_sec': start,
                'duration_sec': duration,  # no staffer id
            },
            'user_information': {
                'user_id': '0',
                'given_name': 'Booksy',
                'family_name': 'Welcome',
                'telephone': '***********',
                'email': '<EMAIL>',
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '1236',
        }
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 400
        assert resp.json['grpc_error_status'] == 'INVALID_ARGUMENT'

    def test_create_booking_invalid_staffer(self):
        self.staffer.remove_services([self.service_variant.service.id])

        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(self.service_variant.id),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.staffer.id)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': 'Booksy',
                'family_name': 'Welcome',
                'telephone': '***********',
                'email': '<EMAIL>',
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '1236',
        }
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 200
        assert resp.json['bookingFailure']['cause'] == 'SLOT_UNAVAILABLE'

    def test_create_booking_non_existing_data(self):
        self.staffer.remove_services([self.service_variant.service.id])

        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(self.service_variant.id + 1),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.staffer.id + 1)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': 'Booksy',
                'family_name': 'Welcome',
                'telephone': '***********',
                'email': '<EMAIL>',
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '1236',
        }
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 400
        assert resp.json['grpc_error_status'] == 'INVALID_ARGUMENT'

    @pytest.mark.freeze_time(datetime(2018, 10, 15, 12, tzinfo=UTC))
    def test_create_booking_already_exists(self):
        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        params = {
            'status': Appointment.STATUS.ACCEPTED,
            'source': self.source,
            'booked_from': booked_from,
            'booked_till': booked_from + relativedelta(hours=1),
            'business': self.business,
            'updated_by': self.user,
            'service_variant': self.service_variant,
        }
        booking = create_subbooking(
            business=self.business,
            booking_kws=params,
        )[0]
        baker.make(
            BookingResource,
            subbooking=booking,
            resource=self.staffer,
        )
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(self.service_variant.id),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.staffer.id)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': 'Booksy',
                'family_name': 'Welcome',
                'telephone': '***********',
                'email': '<EMAIL>',
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '1233',
        }
        resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 200
        assert resp.json['bookingFailure']['cause'] == 'SLOT_UNAVAILABLE'

    @pytest.mark.freeze_time(datetime(2018, 10, 15, 12, tzinfo=UTC))
    def test_create_booking_with_deleted_customer(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            visible_in_business=False,
            email='<EMAIL>',
            cell_phone='123123123',
        )

        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(self.service_variant.id),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.staffer.id)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': bci.first_name,
                'family_name': bci.last_name,
                'telephone': bci.cell_phone,
                'email': bci.email,
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '1237',
        }

        with self.captureOnCommitCallbacks(execute=True):
            resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 200
        booking_data = resp.json
        response = booking_service_pb2.CreateBookingResponse()
        response_obj = ParseDict(js_dict=booking_data, message=response)
        booking_id = external_api_to_id(response_obj.booking.booking_id).get('business_id')
        assert SubBooking.objects.filter(id=booking_id).count() == 1
        booking = SubBooking.objects.get(id=booking_id)
        assert GoogleBooking.objects.filter(booking_id=booking_id).count() == 1
        google_booking = GoogleBooking.objects.get(booking_id=booking_id)
        assert booking.booked_from == google_booking.booked_from
        assert booking.booked_till == google_booking.booked_till
        appointment = booking.appointment
        assert appointment.booked_for.first_appointment_id == (booking.appointment_id)
        assert appointment.booked_for.client_type == (
            appointment.booked_for.CLIENT_TYPE__GOOGLE_PARTNER
        )
        bci.refresh_from_db()
        assert bci.visible_in_business is True
        assert not ZoomMeeting.objects.filter(
            business=self.business,
            appointment_id=booking.appointment_id,
        ).exists()

    @pytest.mark.freeze_time(datetime(2018, 10, 15, 12, tzinfo=UTC))
    @patch(
        'webapps.zoom.models.ZoomBusinessCredentials.get_client',
        return_value=ZoomTestClient(),
    )
    @patch('webapps.notification.scenarios.scenarios_booking_mixin.send_email')
    def test_create_booking_online_service(self, send_email_mock, _):
        baker.make(ZoomBusinessCredentials, business=self.business)
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            visible_in_business=False,
            email='<EMAIL>',
            cell_phone='123123123',
        )
        online_service = baker.make(
            Service,
            business=self.business,
            is_online_service=True,
        )
        online_service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=online_service,
            time_slot_interval=relativedelta(minutes=15),
        )
        self.staffer.add_services([online_service.id])

        booked_from = (tznow() + relativedelta(hours=3)).replace(minute=0, second=0, microsecond=0)
        diff_in_seconds = 1800
        duration = str(diff_in_seconds)
        start = str(int(datetime_to_timestamp(booked_from)))
        body = {
            'slot': {
                'merchant_id': id_to_external_api(self.business.id),
                'service_id': id_to_external_api(online_service_variant.id),
                'start_sec': start,
                'duration_sec': duration,
                'resources': {'staff_id': id_to_external_api(self.staffer.id)},
            },
            'user_information': {
                'user_id': '0',
                'given_name': bci.first_name,
                'family_name': bci.last_name,
                'telephone': bci.cell_phone,
                'email': bci.email,
            },
            'payment_information': {'prepayment_status': 'PREPAYMENT_NOT_PROVIDED'},
            'idempotency_token': '1238',
        }

        with self.captureOnCommitCallbacks(execute=True):
            resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == 200
        booking_data = resp.json
        response = booking_service_pb2.CreateBookingResponse()
        response_obj = ParseDict(js_dict=booking_data, message=response)
        booking_id = external_api_to_id(response_obj.booking.booking_id).get('business_id')
        assert SubBooking.objects.filter(id=booking_id).count() == 1
        booking = SubBooking.objects.get(id=booking_id)
        assert GoogleBooking.objects.filter(booking_id=booking_id).count() == 1
        google_booking = GoogleBooking.objects.get(booking_id=booking_id)
        assert booking.booked_from == google_booking.booked_from
        assert booking.booked_till == google_booking.booked_till
        appointment = booking.appointment
        assert appointment.booked_for.first_appointment_id == (booking.appointment_id)
        assert appointment.booked_for.client_type == (
            appointment.booked_for.CLIENT_TYPE__GOOGLE_PARTNER
        )
        bci.refresh_from_db()
        assert bci.visible_in_business is True
        zoom_meeting = ZoomMeeting.objects.filter(
            business=self.business,
            appointment_id=booking.appointment_id,
        ).first()
        assert zoom_meeting
        assert send_email_mock.call_args.args[0] == bci.email
        assert zoom_meeting.join_url in send_email_mock.call_args.args[1]
