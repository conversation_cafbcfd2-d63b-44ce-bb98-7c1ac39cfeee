import urllib.request
import urllib.parse
import urllib.error

import logging
import datetime
import calendar
import dateutil
import nptime
from dateutil.relativedelta import relativedelta

from django.utils import timezone
from django.conf import settings

from lib.ranges import merge_ranges
from lib.tools import relativedelta_total_seconds

from webapps.business.enums import CustomData
from webapps.consts import GOOGLE
from webapps.feeds.enums import EventType, FeedsPartner
from webapps.feeds.cache import (
    get_categories_mapping,
    get_revert_categories_mapping,
)
from webapps.feeds.google import enums as google_enums
from webapps.feeds.google import tasks as google_tasks
from webapps.feeds.facebook import tasks as facebook_tasks
from webapps.kill_switch.models import KillSwitch

_bk_utils_log = logging.getLogger('booksy.utils')


def get_external_partners():
    partners = []
    _bk_utils_log.info('GOOGLE_RESERVE_LIVE_UPDATES %s', settings.GOOGLE_RESERVE_LIVE_UPDATES)
    _bk_utils_log.info(
        'KillSwitch.System.GOOGLE_RESERVE_LIVE_UPDATES %s',
        KillSwitch.alive(KillSwitch.System.GOOGLE_RESERVE_LIVE_UPDATES),
    )
    if (
        # TODO: bring back after testing is done
        # settings.GOOGLE_RESERVE_LIVE_UPDATES and
        KillSwitch.alive(KillSwitch.System.GOOGLE_RESERVE_LIVE_UPDATES)
    ):
        partners.append(FeedsPartner.RWG)
    if settings.GROUPON_RESERVE_LIVE_UPDATES and KillSwitch.alive(
        KillSwitch.System.GROUPON_RESERVE_LIVE_UPDATES
    ):
        partners.append(FeedsPartner.GROUPON)
    if settings.FACEBOOK_SERVICE_LIVE_UPDATES:
        partners.append(FeedsPartner.FACEBOOK)
    return partners


def utc_timestamp(date):
    """Converts a datetime object to UTC timestamp.

    Naive datetime will throw ValueError

    """
    if date.tzinfo is None:
        raise ValueError('You must use time zone aware datetimes')
    return calendar.timegm(date.utctimetuple())


def utc_timestamp_to_business_datetime(timestamp, business_tz):
    return (
        datetime.datetime.utcfromtimestamp(timestamp)
        .replace(tzinfo=dateutil.tz.gettz('UTC'))
        .astimezone(business_tz)
    )


def merge_date_with_datetime_from_str(date_str, time_str, tz):
    """Don't use dateutil.parser, it's super slow."""
    year, month, day = list(map(int, date_str.split('-')))
    hour, minute = list(map(int, time_str.split(':')[:2]))
    return datetime.datetime(
        year=year,
        month=month,
        day=day,
        hour=hour,
        minute=minute,
        tzinfo=tz,
    )


def get_time_slots_from_booking_ranges(booking_rages_time_slots):
    ret = []
    for time_slot in booking_rages_time_slots:
        ret.extend(get_time_slots_from_booking_ranges_for_date(time_slot))
    return ret


def get_time_slots_from_booking_ranges_for_date(time_slot):
    return list(
        time_slot_range_gen(
            start_time=time_slot['from'],
            end_time=time_slot['to'],
            duration=time_slot['interval'],
        )
    )


def time_slot_range_gen(start_time, end_time, duration):
    _start_time = timezone.datetime.strptime(start_time, '%H:%M').time()
    _end_time = timezone.datetime.strptime(end_time, '%H:%M').time()
    start_time = nptime.nptime(_start_time.hour, _start_time.minute)
    end_time = nptime.nptime(_end_time.hour, _end_time.minute)
    while start_time <= end_time:
        yield start_time
        new_start_time = start_time + timezone.timedelta(minutes=duration)
        if new_start_time < start_time:
            break
        start_time = new_start_time


class CalculateAvailability:
    def __init__(self, business, service_variant):
        self.business = business
        self.service_variant = service_variant

    @property
    def padding_type(self):
        return self.service_variant.service.padding_type

    @property
    def padding_time(self):
        return self.service_variant.service.padding_time

    @property
    def duration(self):
        return self.service_variant.duration

    @property
    def tz(self):
        return self.business.get_timezone()

    @property
    def interval(self):
        return self.service_variant.time_slot_interval or relativedelta(minutes=5)

    @property
    def interval_minutes(self):
        return (relativedelta_total_seconds(self.service_variant.time_slot_interval) // 60) or 5

    def transform_slots_booksy_format(self, time_slots_dict):
        date_format = settings.DATE_FORMAT
        interval = self.interval_minutes

        def serialize_date(date_):
            return date_.strftime(date_format)

        def serialize_slots(date_slots):
            return [
                {
                    'from': slot[0].strftime(settings.TIME_FORMAT),
                    'to': slot[1].strftime(settings.TIME_FORMAT),
                    'interval': interval,
                }
                for slot in date_slots
            ]

        return {
            sid: {serialize_date(date_): serialize_slots(date_slots) for date_, date_slots in slots}
            for sid, slots in list(time_slots_dict.items())
        }

    @staticmethod
    def split_slots_by_interval_continuity(t_s, interval):
        result = []
        tzz = []
        time_slots = t_s[:]
        for _ in range(len(time_slots)):
            e = time_slots.pop(0)
            if not tzz or tzz[-1] + interval == e:
                tzz.append(e)
                continue
            result.append(list(tzz))
            tzz = [e]
        if tzz:
            result.append(tzz)
        return result

    # pylint: disable=invalid-name
    def exceptions(self, date, working_slots, staff_slots):
        datetime_interval = self.interval

        start_w = working_slots[0]

        occupied_slots = sorted(list(set(working_slots) - set(staff_slots)))

        ex_slot = self.split_slots_by_interval_continuity(occupied_slots, datetime_interval)

        tmp_ex_slot = []
        for e in ex_slot:
            if not e:
                continue
            f_f = e[0] + self.duration

            if e[0] + datetime.timedelta(minutes=-self.interval_minutes) in staff_slots:
                f_f = e[0] + datetime.timedelta(minutes=-self.interval_minutes) + self.duration

            if self.padding_type in ['A', 'C']:
                f_f += self.padding_time

            for i, el in enumerate(e):
                if el == start_w:
                    break
                if f_f > el:
                    continue

                e = [f_f] + e[i:]
                break
            tmp_ex_slot.append(e)

        exceptions = []
        for o in tmp_ex_slot:
            f = merge_date_with_datetime_from_str(
                date_str=date,
                time_str=str(o[0]),
                tz=self.tz,
            )
            l = merge_date_with_datetime_from_str(
                date_str=date,
                time_str=str(o[-1]),
                tz=self.tz,
            ) + datetime.timedelta(minutes=self.interval_minutes)
            exceptions.extend(merge_ranges([(f, l)]))
        return exceptions

    # pylint: enable=invalid-name

    def calculate(self, date, start_work, end_work, staff_range):
        raise NotImplementedError


def can_business_be_exported_to_external_partner(business):
    from webapps.business.models import Business

    base_ex = bool(
        business.active
        and business.visible
        and (
            business.status
            in [
                Business.Status.PAID,
                Business.Status.OVERDUE,
            ]
            or (
                business.status
                in [
                    Business.Status.TRIAL,
                ]
                and (
                    business.verification == Business.Verification.VERIFIED
                    or not settings.FEEDS__CHECK_TRIAL_VERIFICATION
                )
            )
            or business.status == Business.Status.VENUE,
        )
        and business.booking_mode == Business.BookingMode.AUTO
        and bool(business.address)
    )
    return base_ex


def can_by_exporter_to_external_partners(business, partners):
    base_ex = can_business_be_exported_to_external_partner(business)
    if FeedsPartner.RWG in partners:
        return base_ex and not business.custom_data.get(CustomData.DISABLE_GOOGLE_RESERVE)
    if FeedsPartner.FACEBOOK in partners:
        return base_ex and business.is_fbe_connected()
    return False


def update_to_external_partners(
    action_type, business, partners=None, **kwargs
):  # pylint: disable=too-many-branches
    if partners is None:
        partners = get_external_partners()

    _bk_utils_log.info(partners)
    if not partners or not can_by_exporter_to_external_partners(business, partners):
        return

    if action_type == EventType.BOOKING:
        booking_list = kwargs.get('booking_list')
        omit_booking_list = kwargs.get('omit_booking_ids') or []
        bookings = [b for b in booking_list if b not in omit_booking_list]
        if FeedsPartner.RWG in partners:
            google_bookings = [b for b in bookings if b.appointment.source.name == GOOGLE]
            if google_bookings:
                google_tasks.update_booking_to_google_task.delay([b.id for b in google_bookings])
            if bookings:
                google_tasks.update_availability_to_google_task.delay([b.id for b in bookings])
    elif action_type == EventType.SERVICE:
        service = kwargs.get('service')
        ids_to_delete = kwargs.get('ids_to_delete')
        ids_to_create = kwargs.get('ids_to_create')
        ids_to_update = kwargs.get('ids_to_update')

        if FeedsPartner.RWG in partners:
            google_tasks.update_business_task.delay(
                business_id=business.id,
                service_id=service.id,
                ids_to_delete=ids_to_delete,
                ids_to_create=ids_to_create,
                ids_to_update=ids_to_update,
                type_=google_enums.FeedType.SERVICES.value,
            )
        if FeedsPartner.FACEBOOK in partners:
            facebook_tasks.update_fb_services_task.delay(business.id)
    elif action_type == EventType.MERCHANT:
        pass
    elif action_type == EventType.STAFFER:
        if FeedsPartner.RWG in partners:
            staffer = kwargs.get('staffer')
            google_tasks.update_business_task.delay(
                business_id=business.id,
                tz=business.time_zone_name,
                type_=google_enums.FeedType.STAFFER.value,
                staffer_id=staffer.id,
            )
    else:
        raise Exception('Wrong external type action')


def get_category(partner, category_name):
    category_mapping = get_categories_mapping()
    try:
        return category_mapping[partner.name][category_name]
    except KeyError:
        return None


def get_revert_categories(partner, category_name):
    category_mapping = get_revert_categories_mapping()
    try:
        return category_mapping[partner.name][category_name]
    except KeyError:
        return []


def get_all_revert_categories(partner):
    category_mapping = get_revert_categories_mapping()
    try:
        return category_mapping[partner.name]
    except KeyError:
        return []


def instagram_external_link(business_id):
    return urllib.parse.urljoin(
        settings.MARKETPLACE_URL,
        f'/{settings.MARKETPLACE_LANG_COUNTRY}/instant-experiences/widget/{business_id}'
        '?instant_experiences_enabled=true&ig_ix=true',
    )


def facebook_external_link(business_id, service_variant_id=None):
    facebook_external_url = instagram_external_link(business_id)
    facebook_external_url += '&is_fb=1'
    if service_variant_id:
        facebook_external_url += f'&service_variant_id={service_variant_id}'
    return facebook_external_url
