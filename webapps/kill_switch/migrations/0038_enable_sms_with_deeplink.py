# Generated by Django 2.2.13 on 2020-11-30 21:13

from django.db import migrations, models
from webapps.kill_switch.models import KillSwitch

CHOICES = [
    ('business_availability', 'Business Availability'),
    ('c2b_referral', 'C2B Referral'),
    ('double_subscriptions_report', 'Double subscriptions report'),
    ('google_reserve_live_updates', 'Google reserve live updates'),
    ('yelp_reserve_live_updates', 'Yelp reserve live updates'),
    ('pipedrive_celery', 'Pipedrive celery updates'),
    ('slots_from_slave', 'Use Slave DB for time slots handler'),
    ('slave_booking_listing', 'Use Slave DB for customer bookings list handler'),
    ('booksy_auth', 'Booksy Auth layer is enabled'),
    ('slave_booking_details', 'Use Slave DB for booking details'),
    ('slave_calendar', 'Use Slave DB for business calendar'),
    ('slave_pos_trans_details', 'Use Slave DB for POS Transaction details '),
    ('replica_pos_transactions', 'Use Replica DB for POS Transactions'),
    ('replica_booking_notifications', 'Use Replica DB for Booking Notifications'),
    ('patterns', 'Use patterns experiment in book again'),
    ('patterns_push', 'Send patterns push'),
    ('patterns_popup', 'Send patterns popup'),
    ('martech_analytics', 'Turn on/off martech analytics'),
    ('near_availability', 'Add inner hits to real availability (LA badge)'),
    ('available_today', "Generates 'Available Today' gallery (MyBooksy)"),
    ('booking_reactivation', 'Adds booking reactivation actions (gallery, sms)'),
    ('rate_limit', 'Disable Rate limit check on every request'),
    ('aws_synchronous_upload', 'S3 sync upload'),
    ('sms_whitelist_enabled', 'Blocking SMSes to non-whitelisted countries'),
    ('donations_promo', 'Donations Promo'),
    ('utt2_backend', 'Enable operation included in UTT2 upgrade.'),
    ('utt2_experiment', 'Enable search using UTT2 based fields.'),
    ('booking_user_search_data', 'Synchronous calculate search data for new booking'),
    ('discount_after_pob', '1 free month of subscription after Payment Overdue Blocked period.'),
    ('replace_banned_twilio_number', 'Replace banned Twilio number'),
    ('iterable_email_service', 'Enable sending messages via Iterable.'),
    ('replica_feed_generator', 'Enable using replica db for RwG feed generator.'),
    ('replica_live_update_rwg_task', 'Enable using replica db for RwG RTUs.'),
    ('replica_yelp_feed_generation', 'Enable using replica db for Yelp feed generator.'),
    ('use_alternative_us_sms_service', 'Enable sending sms in US via evox without extra headers'),
    ('use_sms_with_deeplinks_enabled', "Enables customer booking sms's with deeplinks"),
]


def forward(apps, schema_editor):
    from webapps.kill_switch.utils import ensure_all_exist

    db_alias = schema_editor.connection.alias
    KillSwitch = apps.get_model('kill_switch', 'KillSwitch')
    ensure_all_exist(
        ['use_sms_with_deeplinks_enabled'],
        model=KillSwitch,
        names=[x[0] for x in CHOICES],
        using=db_alias,
    )


class Migration(migrations.Migration):

    dependencies = [
        ('kill_switch', '0037_sms_us_noncontinental'),
    ]

    operations = [
        migrations.AlterField(
            model_name='killswitch',
            name='name',
            field=models.CharField(choices=CHOICES, max_length=32, unique=True),
        ),
        migrations.RunPython(code=forward, reverse_code=migrations.RunPython.noop),
    ]
