# Generated by Django 2.0.13 on 2019-12-23 13:26

from django.db import migrations


def forwards_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    KillSwitch = apps.get_model('kill_switch', 'KillSwitch')
    name = 'booksy_auth'
    if KillSwitch.objects.using(db_alias).filter(name=name).exists():
        return

    booksy_auth_kill_switch = KillSwitch(
        name=name,
        is_killed=True,
    )
    booksy_auth_kill_switch.save(using=db_alias)


def backwards_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    KillSwitch = apps.get_model('kill_switch', 'KillSwitch')
    qs = KillSwitch.objects.using(db_alias).filter(name='booksy_auth')
    if qs.exists():
        qs.delete()


class Migration(migrations.Migration):

    dependencies = [
        ('kill_switch', '0015_add_booksy_auth_switch'),
    ]

    operations = [migrations.RunPython(forwards_func, backwards_func)]
