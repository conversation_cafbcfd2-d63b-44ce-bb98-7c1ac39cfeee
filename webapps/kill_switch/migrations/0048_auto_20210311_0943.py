# Generated by Django 3.1.2 on 2021-03-11 09:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('kill_switch', '0047_auto_20210208_1232'),
    ]

    operations = [
        migrations.AlterField(
            model_name='killswitch',
            name='name',
            field=models.CharField(
                choices=[
                    ('business_availability', 'Business Availability'),
                    ('c2b_referral', 'C2B Referral'),
                    ('double_subscriptions_report', 'Double subscriptions report'),
                    ('google_reserve_live_updates', 'Google reserve live updates'),
                    ('yelp_reserve_live_updates', 'Yelp reserve live updates'),
                    ('pipedrive_celery', 'Pipedrive celery updates'),
                    ('slots_from_slave', 'Use Slave DB for time slots handler'),
                    ('slave_booking_listing', 'Use Slave DB for customer bookings list handler'),
                    ('booksy_auth', 'Booksy Auth layer is enabled'),
                    ('slave_booking_details', 'Use Slave DB for booking details'),
                    ('slave_calendar', 'Use Slave DB for business calendar'),
                    ('slave_pos_trans_details', 'Use Slave DB for POS Transaction details '),
                    ('replica_pos_transactions', 'Use Replica DB for POS Transactions'),
                    ('replica_booking_notifications', 'Use Replica DB for Booking Notifications'),
                    ('patterns', 'Use patterns experiment in book again'),
                    ('patterns_push', 'Send patterns push'),
                    ('patterns_popup', 'Send patterns popup'),
                    ('near_availability', 'Add inner hits to real availability (LA badge)'),
                    ('available_today', "Generates 'Available Today' gallery (MyBooksy)"),
                    ('booking_reactivation', 'Adds booking reactivation actions (gallery, sms)'),
                    ('rate_limit', 'Disable Rate limit check on every request'),
                    ('aws_synchronous_upload', 'S3 sync upload'),
                    ('sms_whitelist_enabled', 'Blocking SMSes to non-whitelisted countries'),
                    ('donations_promo', 'Donations Promo'),
                    ('utt2_backend', 'Enable operation included in UTT2 upgrade.'),
                    ('utt2_experiment', 'Enable search using UTT2 based fields.'),
                    (
                        'booking_user_search_data',
                        'Synchronous calculate search data for new booking',
                    ),
                    (
                        'discount_after_pob',
                        '1 free month of subscription after Payment Overdue Blocked period.',
                    ),
                    ('replace_banned_twilio_number', 'Replace banned Twilio number'),
                    ('iterable_email_service', 'Enable sending messages via Iterable.'),
                    ('replica_feed_generator', 'Enable using replica db for RwG feed generator.'),
                    ('replica_live_update_rwg_task', 'Enable using replica db for RwG RTUs.'),
                    (
                        'replica_yelp_feed_generation',
                        'Enable using replica db for Yelp feed generator.',
                    ),
                    (
                        'use_alternative_us_sms_service',
                        'Enable sending sms in US via evox without extra headers',
                    ),
                    (
                        'use_sms_with_deeplinks_enabled',
                        "Enables customer booking sms's with deeplinks",
                    ),
                    ('gtm_for_admin', 'Enable Google Tag Manager (GTM) for admin'),
                    (
                        'reports_logical_replication',
                        'Stats and reports will be calculated using logical replica db. When killed queries are routed to physical replica.',
                    ),
                    ('business_registration_started', 'business_registration_started'),
                    ('business_registration_completed', 'business_registration_completed'),
                    ('customer_registration_completed', 'customer_registration_completed'),
                    ('business_info_updated', 'business_info_updated'),
                    ('business_categories_updated', 'business_categories_updated'),
                    ('cb_created_for_business', 'cb_created_for_business'),
                    ('cb_finished_for_business', 'cb_finished_for_business'),
                    ('cb_finished_for_customer', 'cb_finished_for_customer'),
                    ('cb_customer_info_updated', 'cb_customer_info_updated'),
                    ('staffer_created', 'staffer_created'),
                    ('user_language_set', 'user_language_set'),
                    ('paid_status_achieved', 'paid_status_achieved'),
                    ('review_completed', 'review_completed'),
                    ('cb_created_for_customer', 'cb_created_for_customer'),
                    ('boost_updated', 'boost_updated'),
                    ('business_pos_updated', 'business_pos_updated'),
                    ('business_subscription_updated', 'business_subscription_updated'),
                    ('business_app_opened', 'business_app_opened'),
                    ('business_status_updated', 'business_status_updated'),
                    ('old_martech_analytics', 'Turn on/off old MarTech analytics'),
                    ('martech_analytics', 'Turn on/off martech analytics'),
                    ('3d secure enabled', '3D Secure enabled'),
                    ('navision_integration', 'Turn on/off Navision integration'),
                    ('match_users_by_phone_number', 'Match users by phone number'),
                    ('old_mail_setup_complete', 'Turn on/off sending setup complete email'),
                    ('contact_preferences_updated', 'Customer contact preferences updated'),
                    ('old_mail_account_added', 'Turn on/off sending account added email'),
                ],
                max_length=32,
                unique=True,
            ),
        ),
    ]
