import typing as t
from collections import defaultdict
from itertools import chain

from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import Q

from lib import safe_json
from lib.db import READ_ONLY_DB, retry_on_sync_error, using_db_for_reads
from lib.rivers import bump_document, River
from lib.unicode_utils import slugify
from webapps.business.business_categories.cache import (
    CategoryCache,
    TreatmentCache,
    TreatmentsForAutoAssignmentCache,
)
from webapps.business.models import Business, Service
from webapps.business.models.business_change import BusinessChange


class TreatmentAssignmentUTT1:
    @classmethod
    @using_db_for_reads(READ_ONLY_DB)
    def assign_treatments_for_services(
        cls,
        services_ids: t.Optional[t.Iterable[int]] = None,
        only_null: bool = True,
        clear_no_match: bool = False,
    ) -> None:
        business_ids = set()
        qs = Service.objects.filter(
            active=True,
        )
        if services_ids is not None:
            qs = qs.filter(id__in=services_ids)
        if only_null:
            qs = qs.filter(treatment=None)

        slugified_service_names = defaultdict(list)
        for service_id, name, business_id in qs.values_list(
            'id',
            'name',
            'business_id',
        ):
            slugified_service_names[slugify(name)].append(service_id)
            business_ids.add(business_id)
        cls._update_services_treatments(slugified_service_names, clear_no_match)
        cls.update_businesses_treatments(business_ids)

    @classmethod
    def _update_services_treatments(
        cls, slugified_service_names: t.Dict[str, t.List[int]], clear_no_match: bool
    ) -> None:
        treatments = cls._get_treatments()
        treatments = [
            (
                [
                    slugify(synonym)
                    for synonym in chain([treatment['name']], treatment.get('keywords') or [])
                ],
                treatment['id'],
            )
            for treatment in treatments
        ]
        treatments = sorted(treatments, key=lambda treatment: len(treatment[0][0]), reverse=True)
        to_update = defaultdict(list)

        # Method returns none if treatment was found or list of ids in no treatment has been found
        to_clear = [
            cls._find_treatment(to_update, treatments, service_name, ids)
            for service_name, ids in slugified_service_names.items()
        ]
        for key, ids in to_update.items():
            Service.objects.filter(id__in=ids).update(
                treatment_id=key,
                is_treatment_selected_by_user=False,
            )

        if clear_no_match:
            to_clear = list(chain.from_iterable(filter(None, to_clear)))
            Service.objects.filter(id__in=to_clear, treatment__isnull=False).update(
                treatment=None,
                is_treatment_selected_by_user=False,
            )

    @staticmethod
    def _get_treatments():
        return TreatmentsForAutoAssignmentCache.get_all()

    @staticmethod
    def _find_treatment(
        to_update: dict[int, list[int]],
        treatments: [([str], int)],
        service_name: str,
        service_ids: [int],
    ) -> [int] or None:
        for synonyms, treatment_id in treatments:
            for synonym in synonyms:
                if synonym in service_name:
                    to_update[treatment_id].extend(service_ids)
                    return
        return service_ids

    @classmethod
    def update_businesses_treatments(
        cls,
        business_ids: t.Iterable[int],
        update_es: bool = True,
    ) -> None:
        businesses = (
            Business.objects.filter(
                id__in=business_ids,
            )
            .annotate(
                business_categories=ArrayAgg(
                    'categories', filter=Q(categories__isnull=False), distinct=True
                ),
                business_treatments=ArrayAgg(
                    'treatments', filter=Q(treatments__isnull=False), distinct=True
                ),
                services_treatments=ArrayAgg(
                    'services__treatment',
                    filter=Q(services__active=True, services__treatment__isnull=False),
                    distinct=True,
                ),
            )
            .only('id')
            .distinct()
        )

        categories_map = {
            treatment['id']: treatment['parent'] for treatment in TreatmentCache.get_all()
        }
        updated_business_ids = list(
            filter(
                None,
                (
                    cls._update_business_treatments(business, categories_map)
                    for business in businesses
                ),
            )
        )
        if update_es and updated_business_ids:
            bump_document(River.BUSINESS, updated_business_ids)

    @classmethod
    @retry_on_sync_error
    def _update_business_treatments(
        cls, business: Business, categories_map: t.Dict[int, int]
    ) -> t.Optional[int]:
        updated = False
        services_treatments = cls._get_treatment_ids(business, 'services_treatments')
        business_treatments = cls._get_treatment_ids(business, 'business_treatments')
        if services_treatments != business_treatments:
            business.treatments.set(list(services_treatments))
            updated = True

        business_categories = cls._get_treatment_ids(business, 'business_categories')
        service_categories = (
            categories_map.get(treatment_id) for treatment_id in services_treatments
        )
        service_categories = set(filter(None, service_categories))
        if not service_categories.issubset(business_categories):
            updated_business_categories = list(service_categories | business_categories)
            business.categories.set(updated_business_categories)
            updated = True
            cls._add_business_change(
                business=business,
                categories_ids_before=list(business_categories),
                categories_ids_after=updated_business_categories,
            )

        return business.id if updated else None

    @staticmethod
    def _get_treatment_ids(business: Business, attr_name: str) -> t.Set[int]:
        return set(map(int, getattr(business, attr_name) or []))

    @staticmethod
    def _add_business_change(
        business: Business,
        categories_ids_before: t.List[int],
        categories_ids_after: t.List[int],
    ):
        categories_before = [
            category['internal_name']
            for category in CategoryCache().get_by_ids(categories_ids_before)
        ]
        categories_after = [
            category['internal_name']
            for category in CategoryCache().get_by_ids(categories_ids_after)
        ]
        data = {'business.categories': [categories_before, categories_after]}
        metadata = {
            'description': 'Category update triggered by treatment assignment',
            'source': 'TreatmentAssignmentUTT1.update_businesses_treatments',
        }
        BusinessChange.objects.create(
            business=business,
            data=safe_json.dumps(data, pretty=True),
            metadata=safe_json.dumps(metadata, pretty=True),
        )
