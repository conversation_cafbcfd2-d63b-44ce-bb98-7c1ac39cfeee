import pytest

from webapps.business.baker_recipes import business_recipe, category_recipe
from webapps.business.business_categories.merger import BusinessCategoryMerger


@pytest.mark.django_db
def test_business_category_merge__fields():
    category_1 = category_recipe.make(
        name='A',
        internal_name='a',
        slug='a',
        previous_slugs=['aa'],
    )
    category_2 = category_recipe.make(
        name='B',
        internal_name='b',
        slug='b',
        previous_slugs=['bb', 'bbb'],
    )

    BusinessCategoryMerger.merge(category_1, category_2)

    category_1.refresh_from_db()
    assert category_1.name == 'A'
    assert category_1.internal_name == 'a'
    assert category_1.slug == 'a'
    assert set(category_1.previous_slugs) == {'aa', 'b', 'bb', 'bbb'}

    category_2.refresh_from_db()
    assert category_2.deleted is not None


@pytest.mark.django_db
def test_business_category_merge__business_primary_categories():
    category_1 = category_recipe.make(name='A')
    category_2 = category_recipe.make(name='B')
    business = business_recipe.make(primary_category=category_2)

    BusinessCategoryMerger.merge(category_1, category_2)

    business.refresh_from_db()
    assert business.primary_category == category_1


@pytest.mark.django_db
def test_business_category_merge__business_secondary_categories():
    category_1 = category_recipe.make(name='A')
    category_2 = category_recipe.make(name='B')

    business_1 = business_recipe.make()
    business_1.categories.add(category_1, category_2)

    business_2 = business_recipe.make()
    business_2.categories.add(category_2)

    BusinessCategoryMerger.merge(category_1, category_2)

    assert list(business_1.categories.all()) == [category_1]
    assert list(business_2.categories.all()) == [category_1]
