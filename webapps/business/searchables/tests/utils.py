import random
import string
from collections import namedtuple
from enum import IntEnum

from django.conf import settings

from lib.elasticsearch.consts import ESIndex
from lib.elasticsearch.tests.geo_data_test import (
    BUSINESS_LOCATIONS,
    CITY_PARAMS,
    COUNTRY_CITY_MAPPING,
)
from webapps.business.elasticsearch import BusinessDocument
from webapps.business.enums import BusinessCategoryEnum
from webapps.business.models import Business
from webapps.structure.elasticsearch import RegionDocument

NAME_TYPE = (
    'Hair salon',
    'SPA salon',
    'Massage salon',
    'Barber Shop',
    'Personal trainers',
    'Home services',
    'Massage salon',
    'Tattoo salon',
    'Podiatry salon',
    'Brows & Lashes salon',
    'Pet services',
    'Classic massage salon',
    'Make-up salon',
    'Bathing procedures as',
    'The Barber',
    'A Class Salon',
    'The Hot-Dog shop',
    'An Apple Beauty complex',
)

NAME_PREFIX = (
    'Green',
    'Orange',
    'Blue',
    'Yellow',
    'Pink',
    'Melancholic',
    'Angry',
    'Pessimistic',
    'Aggressive',
    'Gorgeous',
    'Awesome',
    'Dirty',
    'Emphatic',
    'Dreaming',
    'Extendable',
    'Irresistible',
    'Anxious',
    'Unbeatable',
    'Unbelievable',
    'Sophisticated',
    'Painless',
    'Nervous',
    'Elegant',
    'Glamorous',
    'Bald',
    'Quaint',
    'Ugly',
    'Unsightly',
    'Short',
    'Sharp',
    'Muscular',
    'Long',
    'Magnificent',
    'Handsome',
    'Gorgeous',
    'Glamorous',
    'Fancy',
    'Dazzling',
    'Drab',
    'Clean',
    'Eager',
    'Inexpensive',
    'Mushy',
    'Skinny',
    'Shallow',
    'Wide',
)

NAME_STAFFER_SUFFIX = (
    'John',
    'Alex',
    'Alina',
    'Monika',
    'Ivvy',
    'Tomasz',
    'Vladislav',
    'Anatole',
    'Steve',
    'Dmitriy',
    'Rachel',
    'Aamir',
    'Shan',
    'Salaman',
    'Kamal',
    'Harold',
)

BUSINESS_CATEGORY = (
    BusinessCategoryEnum.SKIN_CARE,
    'Day SPA',
    BusinessCategoryEnum.BARBERS,
    'Hair salons',
    BusinessCategoryEnum.NAIL_SALONS,
    'Home services',
    BusinessCategoryEnum.PERSONAL_TRAINERS,
    'Tattoo artists',
    BusinessCategoryEnum.MAKE_UP,
    'Other',
)
NAME_SUFFIX = (
    'Monkey',
    'Giraffe',
    'Dog',
    'Tiger',
    'Donkey',
    'Armadillo',
    'Wombat',
    'Otter',
    'Pig',
    'Cat',
    'Sea lion',
    'Elephant',
)

SERVICE_SUFFIX = (
    'Tattooing',
    'Bathing',
    'Sun Bathing',
    'Scroling',
    'Browsing',
    'Watching',
    'Reading',
    'Working Out',
    'Falling',
    'Wondering',
)

MANUAL_SCORE = 100
NUMBER_RETRY = 3
BUSINESS_NAMES = set()
USER_NAMES = set()
MIDDLE_OF_DESERT = (23.806078, 11.288452)


class BCE(IntEnum):
    # BusinessCategoryEnum
    HAIR_SALONS = 1
    BARBERS = 2
    MASSAGE = 3
    TATTOO_ARTISTS = 4
    SPA = 5
    SKIN_CARE = 6
    NAIL_SALONS = 7
    TANNING = 8
    PET_SERVICE = 9


class TE(IntEnum):
    # TreatmentEnum
    COLOR = 101
    OMBRE = 102
    HAIRCUT = 103
    BEARD_SHAVE = 111
    BEARD_TRIM = 112
    SHAVE = 113
    HEAD_MASSAGE = 121
    THAI_MASSAGE = 121
    MEDICAL_MASSAGE = 131
    TATTOO = 141
    TATTOO_RESTORATION = 142
    PIERCING = 143
    SAUNA = 151
    BODY_SCRUB = 152
    MAKE_UP = 153
    WAXING = 161
    BOTOX = 162
    COSMELAN = 163
    MANICURE = 171
    PEDICURE = 172
    GEL_NAILS = 173

    @staticmethod
    def _format_name(name):
        return str.capitalize(''.join(str.lower(w) for w in name.split('_')))

    @classmethod
    def get_id_name(cls):
        return {
            member.value: cls._format_name(member.name) for member in list(cls.__members__.values())
        }


CATEGORY_TREATMENT_MAPPING = {
    BCE.HAIR_SALONS: [TE.COLOR, TE.OMBRE, TE.HAIRCUT],
    BCE.BARBERS: [TE.BEARD_SHAVE, TE.BEARD_TRIM, TE.SHAVE],
    BCE.MASSAGE: [TE.HEAD_MASSAGE, TE.THAI_MASSAGE, TE.MEDICAL_MASSAGE],
    BCE.TATTOO_ARTISTS: [TE.TATTOO, TE.TATTOO_RESTORATION, TE.PIERCING],
    BCE.SPA: [TE.SAUNA, TE.BODY_SCRUB, TE.MAKE_UP],
    BCE.SKIN_CARE: [TE.WAXING, TE.BOTOX, TE.COSMELAN],
    BCE.NAIL_SALONS: [TE.MANICURE, TE.PEDICURE, TE.GEL_NAILS],
    BCE.TANNING: [],
    BCE.PET_SERVICE: [],
}

TREATMENT_ID_CATEGORY_ID_MAPPING = {
    treatment.value: category.value
    for category, treatments in CATEGORY_TREATMENT_MAPPING.items()
    for treatment in treatments
}

BusinessCategory = namedtuple(
    'BusinessCategory',
    [
        'id',
        'name',
        'internal_name',
    ],
)
Region = namedtuple(
    'Region',
    [
        'id',
        'type',
        'name',
        'display_name',
        'time_zone_name',
        'longitude',
        'latitude',
    ],
)


def generate_business_name():
    """Will generate business name
    Do not use it anywhere  except tests
    :return: str. Generated name
    """
    name_type = random.choice(NAME_TYPE)
    prefix = random.choice(NAME_PREFIX)
    suffix = random.choice(NAME_SUFFIX)
    name = '{}: {} {}'.format(name_type, prefix, suffix)
    if name in BUSINESS_NAMES:
        for _ in range(NUMBER_RETRY):
            name_type = random.choice(NAME_TYPE)
            prefix = random.choice(NAME_PREFIX)
            suffix = random.choice(NAME_SUFFIX)
            name = '{}: {} {}'.format(name_type, prefix, suffix)
            if name not in BUSINESS_NAMES:
                BUSINESS_NAMES.add(name)
                return name
        # fail to generate name get random characters
        suffix = ''.join(random.choice(string.ascii_uppercase + string.digits) for _ in range(6))
        name = '{}: {} {}'.format(name_type, prefix, suffix)
        return name
    BUSINESS_NAMES.add(name)
    return name


def make_business_categories():
    categories = []
    for _id, name in enumerate(BUSINESS_CATEGORY):
        category = BusinessCategory(
            id=_id,
            name=name,
            internal_name=name,
        )
        categories.append(category)
    return categories


def get_region(region_id, timezone_name):
    params = CITY_PARAMS.get(timezone_name)
    return Region(
        id=region_id,
        type='city',
        name=params['name'],
        display_name=params['name'],
        time_zone_name=timezone_name,
        longitude=params['log'],
        latitude=params['lat'],
    )


def make_business_with_services(
    categories, b_id, manual_score=False, boost_status=Business.BoostStatus.DISABLED, name=None
):
    """
    Will create one business with one main category
    (randomly chosen from categories)
    Will create number_staffers of staffers
    Will try to assign suggested services to primary category of business
    :param categories: list of BusinessCategories Objects
    :param b_id: business id
    :param manual_score: bool if set to True will business with manual score
    :param boost_status: BoostStatus if set to BoostStatus.ENABLED will
        add one business with manual promoted
    :param name: str optional business name
    :return: BusinessDocument
    """
    # copy categories because it will be modified
    local_categories = categories[::]
    business_category = random.choice(local_categories)
    # create business
    local_categories.remove(business_category)
    second_category = random.choice(local_categories)
    possible_timezones = COUNTRY_CITY_MAPPING.get(settings.API_COUNTRY)
    if possible_timezones is None:
        raise KeyError('Non existing timezone')

    # choose region
    timezone_name = random.choice(possible_timezones)
    # get latitude and longitude
    business_location = random.choice(BUSINESS_LOCATIONS[timezone_name])

    manual_boost_score = 0
    if manual_score:
        manual_boost_score = MANUAL_SCORE
    if name is None:
        name = generate_business_name()
    BusinessDocument(
        id=b_id,
        _id='business:{}'.format(b_id),
        name=name,
        visible=True,
        active=True,
        is_b_listing=False,
        business_categories=(
            [
                dict(id=category.id, name=category.name)
                for category in [business_category, second_category]
            ]
        ),
        business_primary_category=dict(id=business_category.id, name=business_category.name),
        business_location=dict(
            coordinate=dict(lat=business_location[0], lon=business_location[1]),
        ),
        regions=[dict(id=1)],
        manual_boost_score=manual_boost_score,
        promoted=boost_status in Business.BoostStatus.active_statuses(),
        join='business',
    ).save(refresh=True)


def make_businesses(clean_index_fixture, num_business=10, manual_score=0):
    index = clean_index_fixture(ESIndex.BUSINESS)
    categories = make_business_categories()
    for i in range(num_business):
        if i < manual_score:
            manual_score = True
        make_business_with_services(
            categories=categories,
            b_id=i,
            manual_score=manual_score,
        )
    # do run refresh because we refresh after each saved document
    return index


def create_country(clean_index_fixture):
    index = clean_index_fixture(ESIndex.REGION)
    RegionDocument(
        _id='region:1',
        id=1,
        name='USA',
        latitude=MIDDLE_OF_DESERT[0],
        longitude=MIDDLE_OF_DESERT[1],
        type=settings.ES_COUNTRY_LVL[0],
        canonical_parent=[],
        canonical_children=[],
    ).save()
    index.refresh()


def mock_categories_mapping(categories, func):
    func.return_value = {category.value: index for index, category in enumerate(categories)}


def create_business_with_params(business_info):
    default = dict(
        active=True,
        visible=True,
        is_b_listing=False,
        popularity=0,
        mp_promotion=0.0,
        promotion_boost=0.0,
        join='business',
    )
    # business_info must be last to overwrite default
    # params for some tests
    result_kwargs = {**default, **business_info}
    return BusinessDocument(**result_kwargs).save()
