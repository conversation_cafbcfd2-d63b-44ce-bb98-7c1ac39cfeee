import pytest

from lib.elasticsearch.consts import (
    ESDocType,
    ESIndex,
)
from webapps.business.elasticsearch.account import BusinessAccountDocument
from webapps.business.models.models import Business
from webapps.business.searchables.business import (
    BusinessAccountSearchable,
)

BUSINESS_ACCOUNT_DATA = [
    {
        'id': 1,
        'name': '<PERSON> the Barber',
        'active': True,
        'status': 'P',
        'business_location': {
            'coordinate': {'lat': 28.33705, 'lon': -82.27309},
            'city': 'San Antonio',
            'address': '32819 Pennsylvania Ave, San Antonio, 33576',
        },
    },
    {
        'id': 2,
        'name': 'Tattoo Studio',
        'active': True,
        'status': 'P',
        'business_location': {
            'coordinate': {'lat': 28.4825733, 'lon': -82.5427106},
            'city': 'Spring Hill',
            'address': '30 Landover Blvd, Spring Hill, 33576',
        },
        'venue_location': {
            'coordinate': {'lat': 28.4825733, 'lon': -82.5427106},
            'city': 'Spring Hill',
            'address': '30 Landover Blvd, Spring Hill, 34609',
        },
    },
    {
        'id': 3,
        'name': 'Other',
        'active': True,
        'status': 'P',
        'business_location': {
            'coordinate': {'lat': 28.4825733, 'lon': -82.5427106},
            'city': 'Spring Hill',
            'address': '30 Landover Blvd, Spring Hill, 33576',
        },
        'venue_location': {
            'coordinate': {'lat': 28.4825733, 'lon': -82.5427106},
            'city': 'Spring Hill',
            'address': '30 Landover Blvd, Spring Hill, 34609',
        },
    },
]


def create_business_accounts(clean_index_module_fixture):
    index = clean_index_module_fixture(ESIndex.BUSINESS_ACCOUNT)

    for data in BUSINESS_ACCOUNT_DATA:
        BusinessAccountDocument(**data).save()

    index.refresh()


@pytest.fixture(scope='module', autouse=True)
def setup_module(clean_index_module_fixture):
    create_business_accounts(clean_index_module_fixture)


@pytest.mark.parametrize(
    'query, expected_ids',
    [
        ('joe', {1}),  # name
        ('barber', {1}),  # name
        ('tatt', {2}),  # name
        ('ave', {1}),  # street
        ('san', {1}),  # city
        ('zzz', set()),
        ('', {1, 2}),
    ],
)
def test_business_account_searchable__query(query, expected_ids):
    searchable = BusinessAccountSearchable(ESDocType.BUSINESS_ACCOUNT)
    response = (
        searchable.params(
            explain=True,
        )
        .search(
            {
                'query': query,
                'business_ids': [1, 2],
                'excluded_statuses': [
                    Business.Status.VENUE.value,
                    Business.Status.B_LISTING.value,
                ],
            }
        )
        .execute()
    )

    ids = {hit['id'] for hit in response.hits}
    if ids != expected_ids:
        pytest.fail(f'Failed to search query "{query}". Response:\n{response.to_dict()}')
