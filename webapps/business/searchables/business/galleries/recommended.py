import elasticsearch_dsl as dsl

from webapps.business.searchables.business.search_engine import BusinessSearchableWithoutLocation
from webapps.business.searchables.business.sub_searchables import (
    BusinessLocationSearchable,
    GenderBasedBusinessSearchable,
)


class MyBooksyLocationSearchable(BusinessLocationSearchable):
    regions = None
    rings = None


class RecommendedSearchBusinessSearchable(BusinessSearchableWithoutLocation):
    promoted = dsl.query.Term(promoted=True)
    gender = GenderBasedBusinessSearchable()

    location = MyBooksyLocationSearchable()

    available_implicit = None
    popularity = None
    reviews_stars = None
