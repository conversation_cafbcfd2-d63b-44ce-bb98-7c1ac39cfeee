from django.conf import settings

from lib.events import lazy_event_receiver, event_receiver
from lib.tools import tznow
from webapps.business.events import business_ended_promotion_event
from webapps.business.models import Business
from webapps.business.notifications.promotions import (
    businesses_with_late_cancellations,
    businesses_without_promotions,
    clients_delivered_from_active_boost,
    LastMinuteSuggestion,
    NoPromotionsSocialMediaSuggestion,
    NoPromotionsPromotionSuggestion,
    ReactivateBoostSuggestion,
)
from webapps.celery.events import every_tuesday_event, every_sunday_event

__all__ = []

if settings.POPUP_NOTIFICATIONS_ENABLED:
    if settings.POPUP_PHASE3:
        __all__ = [
            'no_promotions_social_media_suggest',
            'last_minute_suggestion',
            'schedule_reactivate_promotion_suggestion',
        ]

        @lazy_event_receiver(every_tuesday_event)
        def no_promotions_social_media_suggest(*_args, **_kwargs):
            today = tznow(settings.COUNTRY_CONFIG.default_time_zone).date()
            week = today.isocalendar()[1]
            if week % 2 == 0:
                notif_class = NoPromotionsSocialMediaSuggestion
            else:
                notif_class = NoPromotionsPromotionSuggestion

            for business_id in businesses_without_promotions():
                notif_class(None, business_id=business_id).async_send()

        @event_receiver(every_sunday_event)
        def last_minute_suggestion(**_kwargs):
            for business_id, count in businesses_with_late_cancellations():
                LastMinuteSuggestion(
                    None,
                    business_id=business_id,
                    count=count,
                ).send()

        @event_receiver(business_ended_promotion_event)
        def schedule_reactivate_promotion_suggestion(business: Business, **_kwargs):
            if clients_delivered_from_active_boost(business) >= 5:
                ReactivateBoostSuggestion(business).schedule(days_ahead=31)
