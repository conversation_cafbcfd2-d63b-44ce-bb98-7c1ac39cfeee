from django.utils.translation import gettext as _

from webapps.business.models import Business
from webapps.notification.base import (
    BaseNotification,
    PopupTemplate,
    PushTarget,
)
from webapps.notification.channels import PopupChannel
from webapps.notification.enums import (
    NotificationCategory,
    NotificationGroup,
    NotificationSize,
    NotificationIcon,
)
from webapps.notification.recipients import (
    Managers,
    Reception,
    SystemSender,
)


class BaseImportAndInviteConfirmation(BaseNotification):
    category = NotificationCategory.IMPORT_AND_INVITE
    sender = SystemSender
    recipients = (Managers, Reception)
    channels = (PopupChannel,)

    def __init__(self, instance, business_id=None):
        super().__init__(instance, business_id=business_id)
        self.business = Business.objects.get(pk=business_id)

    @property
    def identity(self):
        return f'{self.notif_type},{self.business.id}'

    def get_target(self):
        return PushTarget(type='customers')


class ImportAndInviteConfirmation(BaseImportAndInviteConfirmation):
    popup_template = PopupTemplate(
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        icon=NotificationIcon.IMPORT_INVITE,
        crucial=False,
        messages=[
            _("Congrats,"),
            _("your clients have been imported and invited to use Booksy."),
            _("Be on the lookout for bookings."),
        ],
    )


class ImportWithDelayedInviteConfirmation(BaseImportAndInviteConfirmation):
    popup_template = PopupTemplate(
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        icon=NotificationIcon.IMPORT_INVITE,
        crucial=False,
        messages=[
            _("Congrats, your clients have been imported."),
            _("When your Profile goes live we'll invite them"),
            _("to get on your books."),
        ],
    )
