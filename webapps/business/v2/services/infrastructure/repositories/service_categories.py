from collections import defaultdict

from lib.db import using_db_for_reads, READ_ONLY_DB
from webapps.business.models import (
    ServiceCategory,
    Service,
    ServiceVariantPayment,
)
from webapps.business.serializers import ServiceSerializer
from webapps.business.v2.services.domain.dtos import ServiceFiltersDTO
from webapps.business.v2.services.domain.repositories.service_categories import (
    ServiceCategoryAbstractRepository,
)
from webapps.public_partners.models import ServiceMetadata


# pylint: disable=duplicate-code
class ServiceCategoryRepository(ServiceCategoryAbstractRepository):
    @using_db_for_reads(READ_ONLY_DB)
    def get_categories(self, business_id: int, service_filter_dto: ServiceFiltersDTO) -> tuple:
        # TODO split into multiple methods in later iterations and return different type
        service_categories = ServiceCategory.objects.filter(
            business=business_id,
        ).order_by('order')

        services = (
            Service.objects.order_by('order')
            .filter(business=business_id, active=True, **service_filter_dto.to_filter_kwargs())
            .prefetch_related(*ServiceSerializer.get_prefetches())
            .with_partner_app_data(ServiceMetadata)
            .annotate_no_show_protection_payments()
            .annotate_is_traveling_service()
            .annotate_combo_parents_count()
            .select_related('business')
            .with_treatment()
        )
        services_by_id = {service.id: service for service in services}
        service_variants_by_id = {
            service_variant.id: service_variant
            for service in services
            for service_variant in service.active_variants
        }

        # to escape additional queries for already loaded objects in serializer...
        def set_related_objects(service, _services_by_id):
            service.business_id = business_id
            for variant in service.active_variants:
                variant.service = service
                for through in variant.combo_children_through.all():
                    if through.child_id not in service_variants_by_id:
                        continue
                    through.child = service_variants_by_id[through.child_id]

        services_by_category_id = defaultdict(list)
        for service in services:
            set_related_objects(service, services_by_id)
            services_by_category_id[service.service_category_id].append(service)

        for category in service_categories:
            category.business_id = business_id
            category.active_services = services_by_category_id[category.id]
            for service in category.active_services:
                set_related_objects(service, services_by_id)

        # NoShowProtectionServiceField._get_service_payment_obj() was fetching ServiceVariantPayment
        # instances for each Service in a loop. We prefetch SVP payments for Services before to
        # avoid that.
        services_ids = [service.id for service in services_by_category_id[None]] + [
            service.id for category in service_categories for service in category.active_services
        ]
        payments_qs = (
            ServiceVariantPayment.objects.filter(
                service_variant__service_id__in=services_ids,
                service_variant__deleted__isnull=True,
                service_variant__active=True,
                service_variant__price__isnull=False,
            )
            .select_related('service_variant')
            .distinct('service_variant__service_id')
        )
        service_payments_map = {
            payment.service_variant.service_id: payment for payment in payments_qs
        }
        return service_categories, services_by_category_id, service_payments_map
