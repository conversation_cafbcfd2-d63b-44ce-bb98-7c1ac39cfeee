import pytest
from django.test import TestCase
from django.utils import timezone

from webapps.business.baker_recipes import business_recipe, category_recipe
from webapps.business.models import Business as BusinessDjango
from webapps.business.v2.business.domain.models.business import (
    BusinessCategory,
    BusinessRWG,
)
from webapps.business.v2.business.infrastructure.repositories.business import (
    BusinessRepository,
    BusinessRWGRepository,
)
from webapps.user.baker_recipes import user_recipe


@pytest.mark.django_db
class BusinessRepositoryTest(TestCase):
    def setUp(self):
        self.repository = BusinessRepository()
        self.user = user_recipe.make()
        self.business = business_recipe.make(owner=self.user)
        self.business_2 = business_recipe.make(owner=self.user)
        self.user_2 = user_recipe.make()
        self.business_user_2 = business_recipe.make(owner=self.user_2)

    def test_get_all_user_businesses__returns_all_user_businesses(self):
        businesses = self.repository.get_all_user_businesses(self.user.id)
        self.assertEqual(2, len(businesses))
        self.assertListEqual(
            [self.business.id, self.business_2.id], [business.id for business in businesses]
        )

    def test_get_all_user_businesses__no_businesses(self):
        businesses = self.repository.get_all_user_businesses(2137)
        self.assertEqual(0, len(businesses))

    def test_get_businesses_by_ids__all_businesses_sorted(self):
        self.business_2.status = BusinessDjango.Status.BLOCKED_OVERDUE
        self.business_2.save(update_fields=['status'])
        businesses = self.repository.get_businesses_by_ids(
            {self.business.id, self.business_2.id, self.business_user_2.id}
        )
        self.assertEqual(3, len(businesses))
        self.assertListEqual(
            [self.business.id, self.business_user_2.id, self.business_2.id],
            [business.id for business in businesses],
        )

    def test_get_businesses_by_ids__empty_ids_list(self):
        businesses = self.repository.get_businesses_by_ids({})
        self.assertEqual(0, len(businesses))

    def test_get_business_activation_date__with_delayed_activation(self):
        activation_date = timezone.now() + timezone.timedelta(days=7)
        business = business_recipe.make(visible_delay_till=activation_date, visible=False)

        result = self.repository.get_business_activation_date(business_id=business.id)

        self.assertEqual(activation_date, result)

    def test_get_business_activation_date__without_delayed_activation(self):
        business = business_recipe.make(visible_delay_till=None)

        result = self.repository.get_business_activation_date(business_id=business.id)

        self.assertIsNone(result)

    def test_get_business_activation_date__business_not_found(self):
        result = self.repository.get_business_activation_date(business_id=99999)

        self.assertIsNone(result)


@pytest.mark.django_db
class BusinessRWGRepositoryTest(TestCase):
    def setUp(self):
        self.repository = BusinessRWGRepository()
        self.user = user_recipe.make()
        self.category = category_recipe.make(
            name="Test Category", slug="test-category", report_name="Test Report"
        )
        self.business = business_recipe.make(
            owner=self.user,
            primary_category=self.category,
            name="Test Business",
            official_name="Test Official Name",
            address="123 Test St",
            city="Test City",
            zipcode="12345",
        )

    def test_get_business_rwg_data__returns_business_rwg(self):
        business_rwg = self.repository.get_business_rwg_data(self.business.id)

        self.assertIsInstance(business_rwg, BusinessRWG)

        self.assertEqual(business_rwg.id, self.business.id)
        self.assertEqual(business_rwg.name, self.business.name)
        self.assertEqual(business_rwg.official_name, self.business.official_name)
        self.assertEqual(business_rwg.address, self.business.address)
        self.assertEqual(business_rwg.city, self.business.city)
        self.assertEqual(business_rwg.zipcode, self.business.zipcode)
        self.assertEqual(business_rwg.owner_id, self.business.owner_id)

        self.assertEqual(business_rwg.primary_category_id, self.category.id)
        self.assertEqual(business_rwg.primary_category_name, self.category.name)
        self.assertEqual(business_rwg.primary_category_slug, self.category.slug)
        self.assertEqual(business_rwg.primary_category_report_name, self.category.report_name)

    def test_get_business_rwg_data__business_not_found(self):
        with self.assertRaises(ValueError) as context:
            self.repository.get_business_rwg_data(999999)
        self.assertEqual(str(context.exception), "Business with id 999999 not found.")

    def test_get_business_category_data__returns_business_category(self):
        category = self.repository.get_business_category_data(self.business.id)
        self.assertIsInstance(category, BusinessCategory)

        self.assertEqual(category.id, self.category.id)
        self.assertEqual(category.name, self.category.name)
        self.assertEqual(category.slug, self.category.slug)
        self.assertEqual(category.report_name, self.category.report_name)

    def test_get_business_category_data__business_not_found(self):
        with self.assertRaises(ValueError) as context:
            self.repository.get_business_category_data(999999)

        self.assertEqual(str(context.exception), "Business with id 999999 not found.")

    def test_get_business_category_data__business_without_category(self):
        business_without_category = business_recipe.make(owner=self.user, primary_category=None)

        with self.assertRaises(ValueError) as context:
            self.repository.get_business_category_data(business_without_category.id)

        self.assertEqual(
            str(context.exception), f"Business with id {business_without_category.id} not found."
        )
