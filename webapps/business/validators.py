from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from lib.feature_flag.feature.business import ResourceVisibleValidationFlag
from webapps.business.models import Resource, Service


class ResourceUniqueTogetherValidator(serializers.UniqueTogetherValidator):
    def __init__(self):
        super().__init__(Resource.objects.filter(active=True), ('type', 'name', 'business'))
        self.serializer_field = None

    def __call__(self, attrs, serializer):
        type_ = attrs.get('type')
        type_display = Resource(type=type_).get_type_display() if type_ else _('Resource')
        self.message = _('%s with this name already exists for this business') % type_display
        return super().__call__(attrs, serializer)


class ResourceVisibilityWithServicesValidator:
    requires_context = True

    def __call__(self, attrs, serializer):
        if (
            serializer.instance
            and attrs.get('visible') in (False,)
            and ResourceVisibleValidationFlag()
        ):  # only for UPDATE
            service_ids = attrs.get('services')
            services = (
                Service.objects.annotate_is_combo().filter(
                    id__in=service_ids,
                    active=True,
                    is_combo=False,
                )
                if service_ids
                else serializer.instance.active_services
            )

            if orphaned_service := next(
                (service for service in services if service.one_resource_perform_service()),
                None,
            ):
                message = _(
                    'At least one staff or resource is required to perform service %(name)s'
                ) % {'name': orphaned_service.name}
                raise serializers.ValidationError(detail={'visible': message}, code='conflict')


class ResourceVisibilityWithServiceVariantsValidator:
    requires_context = True

    def __call__(self, attrs, serializer):
        if (
            serializer.instance
            and attrs.get('visible') in (False,)
            and ResourceVisibleValidationFlag()
        ):  # only for UPDATE
            variants = serializer.instance.service_variants.select_related('service').filter(
                service_id__in=attrs.get('services', serializer.instance.active_services_ids),
                active=True,
            )

            if orphaned_variant := next(
                (variant for variant in variants if variant.has_only_one_resource),
                None,
            ):
                message = _(
                    'At least one staff or resource is required '
                    'to perform service %(name)s variant'
                ) % {'name': orphaned_variant.name_with_details}
                raise serializers.ValidationError(detail={'visible': message}, code='conflict')


def email_localhost_localdomain_validator(value: str) -> None:
    if value.endswith('@localhost.localdomain'):
        raise serializers.ValidationError(
            detail=_("Email can't end with localhost.localdomain"), code='localhost.localdomain'
        )
