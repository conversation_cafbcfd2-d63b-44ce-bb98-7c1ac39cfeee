# Generated by Django 1.10.7 on 2017-10-03 13:18
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0098_service_wordcloud'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='serviceswordclouds',
            options={'get_latest_by': 'updated'},
        ),
        migrations.AddField(
            model_name='serviceswordclouds',
            name='created',
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now, verbose_name='Created (UTC)'
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='serviceswordclouds',
            name='deleted',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
        ),
        migrations.AddField(
            model_name='serviceswordclouds',
            name='updated',
            field=models.DateTimeField(auto_now=True, db_index=True, verbose_name='Updated (UTC)'),
        ),
    ]
