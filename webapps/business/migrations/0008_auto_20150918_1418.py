from django.db import models, migrations


def forwards_func(apps, schema_editor):
    BusinessTag = apps.get_model('business', 'BusinessTag')
    db_alias = schema_editor.connection.alias
    BusinessTag.objects.using(db_alias).bulk_create(
        [
            BusinessTag(name="quality:migrated_calendar"),
            BusinessTag(name="quality:many_photos"),
            BusinessTag(name="quality:invited_customers"),
        ]
    )


def no_op(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0007_business_sales_model'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessTag',
            fields=[
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='business_tag_id'
                    ),
                ),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(null=True, blank=True)),
            ],
            options={
                'ordering': ['name'],
                'verbose_name': 'Business Tag',
                'verbose_name_plural': 'Business Tags',
            },
        ),
        migrations.AddField(
            model_name='business',
            name='tags',
            field=models.ManyToManyField(
                related_name='businesses', to='business.BusinessTag', blank=True
            ),
        ),
        migrations.RunPython(forwards_func, no_op),
    ]
