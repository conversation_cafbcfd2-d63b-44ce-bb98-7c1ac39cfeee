from django.db import migrations, models


def migrate_searchtag_categories(apps, schema_editor):
    db_name = schema_editor.connection.alias
    SearchTag = apps.get_model('business', 'SearchTag')
    for st in SearchTag.objects.using(db_name):
        st.category = st.categories.first()
        st.save()


def rollback_searchtag_categories(apps, schema_editor):
    db_name = schema_editor.connection.alias
    SearchTag = apps.get_model('business', 'SearchTag')
    for st in SearchTag.objects.using(db_name):
        st.categories.add(st.category)


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0018_searchtag_related_name'),
    ]

    operations = [
        migrations.AddField(
            model_name='searchtag',
            name='category',
            field=models.ForeignKey(
                on_delete=models.deletion.CASCADE, to='business.BusinessCategory', null=True
            ),
        ),
        migrations.RunPython(migrate_searchtag_categories, rollback_searchtag_categories),
        migrations.RemoveField(
            model_name='searchtag',
            name='categories',
        ),
        migrations.AlterField(
            model_name='searchtag',
            name='category',
            field=models.ForeignKey(
                on_delete=models.deletion.CASCADE,
                related_name='search_tags',
                to='business.BusinessCategory',
            ),
        ),
    ]
