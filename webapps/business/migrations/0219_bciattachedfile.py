# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2019-03-22 13:55

from django.db import migrations, models
import django.db.models.deletion
import webapps.business.models
import webapps.business.models.bci
import webapps.images.storages


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0231_attendance_list_presence'),
    ]

    operations = [
        migrations.CreateModel(
            name='BCIAttachedFile',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('original_name', models.CharField(max_length=150)),
                (
                    'uploaded_file',
                    models.FileField(
                        max_length=150,
                        upload_to=webapps.business.models.bci.get_bci_attached_file_path,
                    ),
                ),
                (
                    'bci',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='attached_files',
                        to='business.BusinessCustomerInfo',
                    ),
                ),
            ],
        ),
    ]
