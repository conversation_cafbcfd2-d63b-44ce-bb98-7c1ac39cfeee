# Generated by Django 3.1.13 on 2021-08-04 10:57

from django.db import migrations, models
import webapps.segment.enums


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0338_remove_pipedrive_from_choice_in_api_name'),
    ]

    operations = [
        migrations.AddField(
            model_name='booksy30feedback',
            name='platform',
            field=models.CharField(
                choices=[
                    ('Android', 'ANDROID'),
                    ('iOS', 'IOS'),
                    ('Desktop', 'DESKTOP'),
                    ('Tablet', 'TABLET'),
                    ('UNKNOWN', 'UNKNOWN'),
                ],
                default=webapps.segment.enums.DeviceTypeName['DESKTOP'],
                max_length=7,
            ),
        ),
    ]
