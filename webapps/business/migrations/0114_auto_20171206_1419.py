# Generated by Django 1.11.7 on 2017-12-06 14:19
import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0113_merge_20171206_1342'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='businesscategory',
            name='f_order',
            field=models.IntegerField(blank=True, help_text='Female order', null=True),
        ),
        migrations.AlterField(
            model_name='businesscategory',
            name='keywords',
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=100), blank=True, null=True, size=None
            ),
        ),
        migrations.AlterField(
            model_name='businesscategory',
            name='m_order',
            field=models.IntegerField(blank=True, help_text='Male order', null=True),
        ),
        migrations.AlterField(
            model_name='businesscategory',
            name='order',
            field=models.IntegerField(blank=True, help_text='Gender neutral order', null=True),
        ),
    ]
