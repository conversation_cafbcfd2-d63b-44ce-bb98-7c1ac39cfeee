# Generated by Django 2.2.12 on 2020-07-07 10:24

from django.db import migrations
from lib.tools import tznow


def forwards_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    Business = apps.get_model('business', 'Business')
    Business.objects.using(db_alias).filter(
        status__in=['F', 'L'],
    ).update(active=False)


def no_op(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0288_business_statuses_new_and_rename'),
    ]

    operations = [migrations.RunPython(forwards_func, no_op)]
