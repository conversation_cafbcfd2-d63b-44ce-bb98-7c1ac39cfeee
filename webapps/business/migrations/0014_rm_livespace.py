from django.db import models, migrations

INTERNAL_NAMES = {
    'Annat': 'Other',
    'Barberare': 'Barbers',
    '<PERSON><PERSON>': 'Barbers',
    'Body Art': 'Body Art',
    'Bridal Makeup': 'Bridal Makeup',
    'Dietetycy': 'Dietician',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>uc<PERSON>': 'Therapy',
    'Fotostudios': 'Photo studios',
    'Friseursalons': 'Hair salons',
    'Frisörer': 'Hair salons',
    'Fryzjerzy': 'Hair salons',
    'Hair Salons': 'Hair salons',
    'Hair Stylists': 'Hair salons',
    'Haushaltsservice': 'Home services',
    'Hautpflege': 'Skin care',
    'Heimtierservice': 'Pet services',
    'Hemtjänster': 'Home services',
    'Henkilökohtaiset valmentajat': 'Personal trainers',
    'Herrensalons': 'Hair salons',
    'Hieronta': 'Massage',
    'Hudvård': 'Skin care',
    'Husdjurstj<PERSON>nster': 'Pet services',
    'Ihonhoito': 'Skin care',
    'Inni': 'Other',
    'Kampaamot': 'Hair salons',
    '<PERSON><PERSON> palvelut': 'Home services',
    'Kosmetycz<PERSON>': 'Skin care',
    '<PERSON>ynsistudiot': 'Nail salons',
    'Ladies Beauty Parlors': 'Ladies Beauty Parlors',
    'Lemmikkieläinpalvelut': 'Pet services',
    'Masażyści': 'Massage',
    'Medycyna Estetyczna': 'Aesthetic medicine',
    'Meikki': 'Make-up',
    'Nagelsalonger': 'Nail salons',
    'Nagelstudios': 'Nail salons',
    'Nail Spa': 'Nail salons',
    'Parturit': 'Barbers',
    'Paznokcie': 'Nail salons',
    'Personlig tränare': 'Personal trainers',
    'Persöliche Trainer': 'Personal trainers',
    'SPA hoitola': 'Day SPA',
    'Sonstige': 'Other',
    'Spa': 'Day SPA',
    'Spa & Massage': 'Spa & Massage',
    'Spa und Wellness': 'Day SPA',
    'Sporthallar': 'Sport',
    'Stomatolodzy': 'Dental',
    'Städtjänster': 'Home services',
    'Terapi': 'Therapy',
    'Trenerzy personalni': 'Personal trainers',
    'Wizażyści': 'Make-up',
    'Yoga': 'Yoga',
    'Zdrowie': 'Health',
    'Другое': 'Other',
    'Макияж': 'Make-up',
    'Маникюрные салоны': 'Nail salons',
    'Массаж': 'Massage',
    'Мужские парикмахерские': 'Barbers',
    'Парикмахерские': 'Hair salons',
    'Персональные тренеры': 'Personal trainers',
    'Спа': 'Day SPA',
    'Услуги для домашних питомцев': 'Pet services',
    'Услуги на дому': 'Home services',
    'Уход за кожей': 'Skin care',
}


def update_internal_names(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    BusinessCategory = apps.get_model("business", "BusinessCategory")
    for name, iname in list(INTERNAL_NAMES.items()):
        BusinessCategory.objects.using(db_alias).filter(name=name).update(internal_name=iname)


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0013_auto_20151021_1141'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='business',
            name='livespace_company_id',
        ),
        migrations.RemoveField(
            model_name='business',
            name='livespace_contact_id',
        ),
        migrations.RemoveField(
            model_name='businesscategory',
            name='description',
        ),
        migrations.RemoveField(
            model_name='businesscategory',
            name='footer',
        ),
        migrations.RemoveField(
            model_name='businesscategory',
            name='livespace_group_id',
        ),
        migrations.RemoveField(
            model_name='businesscategory',
            name='title',
        ),
        migrations.AddField(
            model_name='businesscategory',
            name='internal_name',
            field=models.CharField(default='', max_length=50),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='externalapisync',
            name='api_name',
            field=models.CharField(max_length=40, serialize=False, primary_key=True, choices=[]),
        ),
        migrations.RunPython(
            code=update_internal_names,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
