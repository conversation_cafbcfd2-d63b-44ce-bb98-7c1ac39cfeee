# Generated by Django 1.11.17 on 2019-02-26 13:08
from django.db import migrations
from elasticsearch import TransportError

from webapps.business.elasticsearch.business_category import (
    BusinessCategoryDocument,
)
from webapps.business.elasticsearch.business_category import (
    BusinessCategoryIndex,
)


def apply_new_female_weight(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    categories = {
        'Piercing': 70,
        'Day SPA': 80,
        'Skin care': 90,
        'Wedding Makeup Artist': 100,
        'Tattoo artists': 40,
        'Hair Removal': 70,
        'Home services': 70,
        'Holistic Medicine': 60,
        'Aesthetic medicine': 90,
    }
    apply_female_weight(apps, categories, db_alias)


def apply_old_female_weight(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    categories = {
        'Piercing': 50,
        'Day SPA': 50,
        'Skin care': 70,
        'Wedding Makeup Artist': 50,
        'Tattoo artists': 50,
        'Hair Removal': 50,
        'Home services': 50,
        'Holistic Medicine': 50,
        'Aesthetic medicine': 50,
    }
    apply_female_weight(apps, categories, db_alias)


def apply_female_weight(apps, business_category_info, db_alias):
    BusinessCategory = apps.get_model('business', 'BusinessCategory')
    for internal_name, weight in list(business_category_info.items()):
        BusinessCategory.objects.using(db_alias).filter(internal_name=internal_name).update(
            female_weight=weight,
        )
    ids = list(
        BusinessCategory.objects.using(db_alias)
        .filter(internal_name__in=list(business_category_info.keys()))
        .values_list(
            'id',
            flat=True,
        )
    )
    try:
        index = BusinessCategoryIndex()
        exists = index.exists()
    except TransportError:
        exists = False

    if exists:
        doc = BusinessCategoryDocument()
        doc.reindex(ids)


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0212_merge_20190226_1304'),
    ]

    operations = [
        migrations.RunPython(
            apply_new_female_weight,
            apply_old_female_weight,
        ),
    ]
