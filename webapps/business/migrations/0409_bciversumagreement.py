# Generated by Django 4.1.7 on 2023-04-16 15:50

from django.db import migrations, models
import django.db.models.deletion
import lib.models


class Migration(migrations.Migration):
    dependencies = [
        ("business", "0408_versumtobooksyagreements"),
    ]

    operations = [
        migrations.CreateModel(
            name="BCIVersumAgreement",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created (UTC)"),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="Updated (UTC)"
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(blank=True, null=True, verbose_name="Deleted (UTC)"),
                ),
                (
                    "agreement_type",
                    models.CharField(
                        choices=[
                            ("notifications", "Notifications"),
                            ("bulk", "Bulk Messages"),
                            ("birthday", "Birthday and Name Day wishes"),
                            ("loyalty", "Loyalty Program"),
                            ("reviews", "Appointments Reviews"),
                        ],
                        max_length=13,
                    ),
                ),
                ("sms", models.BooleanField(default=False)),
                ("email", models.BooleanField(default=False)),
                (
                    "bci",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="versum_agreements",
                        to="business.businesscustomerinfo",
                    ),
                ),
            ],
            options={
                "get_latest_by": "updated",
                "abstract": False,
            },
            managers=[
                ("objects", lib.models.AutoUpdateManager()),
            ],
        ),
    ]
