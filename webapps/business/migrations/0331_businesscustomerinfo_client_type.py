# Generated by Django 3.1.8 on 2021-05-18 14:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0330_service_variant_qs_manager'),
    ]

    operations = [
        migrations.AlterField(
            model_name='businesscustomerinfo',
            name='client_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('UN', 'Unknown'),
                    ('BD', 'Added by business directly'),
                    ('BP', 'Added by business via cell phone'),
                    ('BI', 'Added by business by import'),
                    ('BV', 'Added by business via invite'),
                    ('BS', 'Added by business via subdomain/deeplink'),
                    ('BF', 'Added by facebook'),
                    ('BW', 'Added by widget'),
                    ('CR', 'Recurring customer'),
                    ('CN', 'New customer'),
                    ('GP', 'Google Partner'),
                    ('RP', 'Groupon Partner'),
                    ('CP', 'ClassPass Partner'),
                    ('YP', 'Yelp Partner'),
                    ('LO', 'Legacy'),
                    ('IG', 'Added by Instagram'),
                ],
                default='UN',
                max_length=2,
                null=True,
            ),
        ),
    ]
