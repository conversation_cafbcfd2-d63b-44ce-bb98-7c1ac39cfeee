# Generated by Django 1.11.7 on 2017-12-14 14:46
import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0111_merge_20171205_1338'),
    ]

    operations = [
        migrations.AlterField(
            model_name='businesscategory',
            name='f_order',
            field=models.IntegerField(blank=True, help_text='Female order', null=True),
        ),
        migrations.AlterField(
            model_name='businesscategory',
            name='keywords',
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=100), blank=True, null=True, size=None
            ),
        ),
        migrations.AlterField(
            model_name='businesscategory',
            name='m_order',
            field=models.IntegerField(blank=True, help_text='Male order', null=True),
        ),
        migrations.AlterField(
            model_name='businesscategory',
            name='order',
            field=models.IntegerField(blank=True, help_text='Gender neutral order', null=True),
        ),
        migrations.AlterField(
            model_name='businesscustomerinfo',
            name='client_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('UN', 'Unknown'),
                    ('BD', 'Added by business directly'),
                    ('BI', 'Added by business by import'),
                    ('BV', 'Added by business via invite'),
                    ('BS', 'Added by business via subdomain/deeplink'),
                    ('BF', 'Added by facebook'),
                    ('BW', 'Added by widget'),
                    ('CR', 'Recurring customer'),
                    ('CN', 'New customer'),
                    ('GP', 'Google Partner'),
                    ('YP', 'Yelp Partner'),
                    ('LO', 'Legacy'),
                ],
                default='UN',
                max_length=2,
                null=True,
            ),
        ),
    ]
