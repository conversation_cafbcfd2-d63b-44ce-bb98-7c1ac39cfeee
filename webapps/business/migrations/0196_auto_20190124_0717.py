# Generated by Django 1.11.17 on 2019-01-24 07:17
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0195_merge_20190115_1442'),
    ]

    operations = [
        migrations.AlterField(
            model_name='business',
            name='owner',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name='businesses',
                to='user.User',
            ),
        ),
        migrations.AlterField(
            model_name='business',
            name='registration_source',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to='booking.BookingSources',
            ),
        ),
        migrations.AlterField(
            model_name='businesscategory',
            name='parent',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name='children',
                to='business.BusinessCategory',
            ),
        ),
        migrations.AlterField(
            model_name='businesscustomerinfo',
            name='user',
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='business_customer_infos',
                to='user.User',
            ),
        ),
    ]
