# Generated by Django 3.1.7 on 2021-04-06 09:24
import dateutil.relativedelta
from django.conf import settings
from django.db import migrations
import lib.interval.fields


class Migration(migrations.Migration):
    dependencies = [
        ('business', '0326_merge_20210409_1114'),
    ]

    operations = [
        migrations.AlterField(
            model_name='servicevariant',
            name='time_slot_interval',
            field=lib.interval.fields.IntervalField(
                default=dateutil.relativedelta.relativedelta(
                    minutes=settings.SERVICE_VARIANT_DEFAULT_INTERVAL,
                )
            ),
        ),
    ]
