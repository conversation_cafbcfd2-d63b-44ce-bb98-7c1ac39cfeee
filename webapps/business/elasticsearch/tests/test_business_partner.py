import pytest
from model_bakery import baker

from webapps.business.baker_recipes import business_recipe
from webapps.public_partners.models import PublicBooksyPartner


@pytest.mark.django_db
def test_business_document_with_partners():
    partner = baker.make(PublicBooksyPartner, name='dummy_partner')
    business = business_recipe.make()
    partner.add_business(business)
    assert business.partnerpermissionbusiness_set.exists()
    doc = business.get_document(refresh=True)
    assert doc.partners == ['dummy_partner']
