import unittest

import pytest
from django.test.utils import override_settings
from django.conf import settings
from mock import patch
from model_bakery import baker
from segment.analytics import Client

from country_config.enums import Country
from lib.tools import id_to_external_api
from service.tests import dict_assert
from webapps.business.models import (
    Business,
    BusinessPolicyAgreement,
)
from webapps.user.models import User
from webapps.kill_switch.models import KillSwitch


@pytest.mark.django_db
class BusinessPolicyAgreementSaveTest(unittest.TestCase):

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_save_analytics_killswitch(self, analytics_track_mock, analytics_identify_mock):
        baker.make(
            KillSwitch, name=KillSwitch.MarTech.BUSINESS_CONTACT_PREFERENCES_UPDATED, is_killed=True
        )
        owner = baker.make(User)
        business = baker.make(
            Business,
            owner=owner,
            active=True,
            status=Business.Status.PAID,
        )
        bpa = baker.make(
            BusinessPolicyAgreement,
            business=business,
        )

        bpa.marketing_agreement = True
        bpa.save()

        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_save_country_us(self, analytics_track_mock, analytics_identify_mock):
        owner = baker.make(User)
        business = baker.make(
            Business,
            owner=owner,
            active=True,
            status=Business.Status.PAID,
        )
        bpa = baker.make(
            BusinessPolicyAgreement,
            business=business,
        )

        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Business_Contact_Preferences_Updated',
                'user_id': id_to_external_api(owner.id),
                'properties': {
                    'email': owner.email,
                    'business_receiving_messages_consent': False,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(owner.id),
                'traits': {
                    'user_role': 'Owner',
                    'country': settings.API_COUNTRY,
                    'email': owner.email,
                    'business_receiving_messages_consent': False,
                    # due to law in US if marketing_agreement in None the field is not sent with event
                },
            },
        )

        analytics_track_mock.call_count = 1
        analytics_track_mock.call_count = 1

        bpa.marketing_agreement = True
        bpa.save()

        dict_assert(
            analytics_track_mock.call_args_list[1][1],
            {
                'event': 'Business_Contact_Preferences_Updated',
                'user_id': id_to_external_api(owner.id),
                'properties': {
                    'email': owner.email,
                    'business_marketing_agreement': True,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[1][1],
            {
                'user_id': id_to_external_api(owner.id),
                'traits': {
                    'user_role': 'Owner',
                    'country': settings.API_COUNTRY,
                    'email': owner.email,
                    'business_marketing_agreement': True,
                },
            },
        )

        assert analytics_track_mock.call_count == 2
        assert analytics_identify_mock.call_count == 2

    @override_settings(API_COUNTRY=Country.ES)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_save_country_es(self, analytics_track_mock, analytics_identify_mock):
        owner = baker.make(User)
        business = baker.make(
            Business,
            owner=owner,
            active=True,
            status=Business.Status.PAID,
        )
        bpa = baker.make(
            BusinessPolicyAgreement,
            business=business,
        )

        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Business_Contact_Preferences_Updated',
                'user_id': id_to_external_api(owner.id),
                'properties': {
                    'email': owner.email,
                    'business_marketing_agreement': False,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(owner.id),
                'traits': {
                    'user_role': 'Owner',
                    'email': owner.email,
                    'business_marketing_agreement': False,
                },
            },
        )

        analytics_track_mock.call_count = 1
        analytics_track_mock.call_count = 1

        bpa.marketing_agreement = True
        bpa.save()

        dict_assert(
            analytics_track_mock.call_args_list[1][1],
            {
                'event': 'Business_Contact_Preferences_Updated',
                'user_id': id_to_external_api(owner.id),
                'properties': {
                    'email': owner.email,
                    'business_marketing_agreement': True,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[1][1],
            {
                'user_id': id_to_external_api(owner.id),
                'traits': {
                    'user_role': 'Owner',
                    'country': settings.API_COUNTRY,
                    'email': owner.email,
                    'business_marketing_agreement': True,
                },
            },
        )

        assert analytics_track_mock.call_count == 2
        assert analytics_identify_mock.call_count == 2


@pytest.mark.django_db
class BusinessUpdateSaveTest(unittest.TestCase):

    def test_save(self):
        business = baker.make(Business)
        updated_on_creation = business.updated

        business.name = 'Barbers'
        business.save(update_fields=['name'])
        updated_after_save = business.updated
        self.assertNotEqual(updated_on_creation, updated_after_save)

        business.name = 'Hairdressers'
        business.save()
        updated_after_second_save = business.updated
        self.assertNotEqual(updated_after_save, updated_after_second_save)

    def test_update(self):
        business = baker.make(Business)
        updated_on_creation = business.updated

        Business.objects.filter(id=business.id).update(name='Barbers')
        business.refresh_from_db()
        updated_after_update = business.updated
        self.assertNotEqual(updated_on_creation, updated_after_update)

        Business.objects.filter(id=business.id).update()
        business.refresh_from_db()
        updated_after_second_update = business.updated
        self.assertNotEqual(updated_after_update, updated_after_second_update)

    def test_update_or_create(self):
        business = baker.make(Business)
        updated_on_creation = business.updated

        Business.objects.update_or_create(id=business.id, defaults={'name': 'Hair'})
        business.refresh_from_db()
        updated_after_update = business.updated
        self.assertNotEqual(updated_on_creation, updated_after_update)
