from copy import deepcopy

from webapps.business_customer_info.customer_data import CustomerData
from webapps.business.baker_recipes import bci_recipe, pet_bci_recipe, vehicle_bci_recipe
from webapps.family_and_friends.tests.utils import TestFamilyAndFriendsMixin


class TestCustomerData(TestFamilyAndFriendsMixin):
    def test_vehicle_full_name(self):
        vehicle = vehicle_bci_recipe.make()
        customer_data = CustomerData(vehicle)
        assert customer_data.full_name == 'BMT 216A, Aston Martin DB5'

    def test_pet_full_name(self):
        pet = pet_bci_recipe.make()
        pet.first_name = '<PERSON>'
        pet.last_name = 'Sweetest'
        pet.save()
        customer_data = CustomerData(pet)
        assert customer_data.full_name == '<PERSON> <PERSON>, <PERSON>, <PERSON>odle'

    def test_deepcopy(self):
        bci = bci_recipe.make()
        customer_data = CustomerData(bci)
        deepcopy(customer_data)
