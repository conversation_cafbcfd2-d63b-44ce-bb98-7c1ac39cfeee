import pytest
from mock import patch, call
from model_bakery import baker

from lib.feature_flag.feature.business import CacheBusinessInviteDeeplink
from lib.tests.utils import override_feature_flag
from webapps.business.cache import get_cached_invite_deeplink
from webapps.business.models import Business
from webapps.notification.base import Channel
from webapps.notification.enums import DeeplinkFeature


def create_test_business():
    return baker.make(
        Business,
        status=Business.Status.TRIAL,
        active=True,
    )


@pytest.mark.django_db
@patch.object(
    Business,
    'get_mp_deeplink',
    return_value='dl.booksy.com/WithoutCache',
)
def test_get_email_invite_mp_deeplink_without_cache(generate_mock):
    business = baker.make(Business)
    deeplink = business.get_email_invite_mp_deeplink()
    assert deeplink == 'dl.booksy.com/WithoutCache'
    assert generate_mock.call_count == 1
    assert generate_mock.call_args[1]['feature'] == DeeplinkFeature.MERCHANT_CUSTOMER_INVITE
    assert generate_mock.call_args[1]['channel'] == Channel.Type.EMAIL


@pytest.mark.django_db
@patch.object(
    Business,
    'get_mp_deeplink',
    return_value='dl.booksy.com/WithoutCache',
)
def test_get_staffer_email_invite_mp_deeplink_without_cache(generate_mock):
    business = baker.make(Business)
    deeplink = business.get_email_invite_mp_deeplink(staffer_invite=True)
    assert deeplink == 'dl.booksy.com/WithoutCache'
    assert generate_mock.call_count == 1
    assert generate_mock.call_args[1]['feature'] == DeeplinkFeature.STAFFER_CUSTOMER_INVITE
    assert generate_mock.call_args[1]['channel'] == Channel.Type.EMAIL


@pytest.mark.django_db
@override_feature_flag({CacheBusinessInviteDeeplink.flag_name: True})
@patch.object(
    Business,
    'get_mp_deeplink',
    return_value='dl.booksy.com/WithCache',
)
def test_get_email_invite_mp_deeplink_with_cache_ff_on(generate_mock):
    business = baker.make(Business)
    deeplink = business.get_email_invite_mp_deeplink()
    assert deeplink == 'dl.booksy.com/WithCache'
    assert generate_mock.call_count == 1
    assert generate_mock.call_args == call(
        feature=DeeplinkFeature.MERCHANT_CUSTOMER_INVITE,
        channel=Channel.Type.EMAIL,
    )


@pytest.mark.django_db
@override_feature_flag({CacheBusinessInviteDeeplink.flag_name: True})
@patch.object(Business, 'get_mp_deeplink', return_value='dl.booksy.com/WithCache')
def test_get_staffer_email_invite_mp_deeplink_with_cache_ff_on(generate_mock):
    business = baker.make(Business)
    deeplink = business.get_email_invite_mp_deeplink(staffer_invite=True)
    assert deeplink == 'dl.booksy.com/WithCache'
    assert generate_mock.call_count == 1
    assert generate_mock.call_args == call(
        feature=DeeplinkFeature.STAFFER_CUSTOMER_INVITE,
        channel=Channel.Type.EMAIL,
    )


@pytest.mark.django_db
@patch.object(
    Business,
    'get_mp_deeplink',
    return_value='dl.booksy.com/WithoutCache',
)
def test_get_push_invite_mp_deeplink_without_cache(generate_mock):
    business = baker.make(Business)
    deeplink = business.get_push_invite_mp_deeplink()
    assert deeplink == 'dl.booksy.com/WithoutCache'
    assert generate_mock.call_count == 1
    assert generate_mock.call_args[1]['feature'] == DeeplinkFeature.MERCHANT_CUSTOMER_INVITE
    assert generate_mock.call_args[1]['channel'] == Channel.Type.PUSH


@pytest.mark.django_db
@patch.object(
    Business,
    'get_mp_deeplink',
    return_value='dl.booksy.com/WithoutCache',
)
def test_get_staffer_push_invite_mp_deeplink_without_cache(generate_mock):
    business = baker.make(Business)
    deeplink = business.get_push_invite_mp_deeplink(staffer_invite=True)
    assert deeplink == 'dl.booksy.com/WithoutCache'
    assert generate_mock.call_count == 1
    assert generate_mock.call_args[1]['feature'] == DeeplinkFeature.STAFFER_CUSTOMER_INVITE
    assert generate_mock.call_args[1]['channel'] == Channel.Type.PUSH


@pytest.mark.django_db
@override_feature_flag({CacheBusinessInviteDeeplink.flag_name: True})
@patch.object(
    Business,
    'get_mp_deeplink',
    return_value='dl.booksy.com/WithCache',
)
def test_get_push_invite_mp_deeplink_with_cache_ff_on(generate_mock):
    business = baker.make(Business)
    deeplink = business.get_push_invite_mp_deeplink()
    assert deeplink == 'dl.booksy.com/WithCache'
    assert generate_mock.call_count == 1
    assert generate_mock.call_args == call(
        feature=DeeplinkFeature.MERCHANT_CUSTOMER_INVITE,
        channel=Channel.Type.PUSH,
    )


@pytest.mark.django_db
@override_feature_flag({CacheBusinessInviteDeeplink.flag_name: True})
@patch.object(Business, 'get_mp_deeplink', return_value='dl.booksy.com/WithCache')
def test_get_staffer_push_invite_mp_deeplink_with_cache_ff_on(generate_mock):
    business = baker.make(Business)
    deeplink = business.get_push_invite_mp_deeplink(staffer_invite=True)
    assert deeplink == 'dl.booksy.com/WithCache'
    assert generate_mock.call_count == 1
    assert generate_mock.call_args == call(
        feature=DeeplinkFeature.STAFFER_CUSTOMER_INVITE,
        channel=Channel.Type.PUSH,
    )


@pytest.mark.django_db
@patch.object(
    Business,
    'get_sms_invite_mp_deeplink',
    return_value='dl.booksy.com/WithCache',
)
def test_get_sms_invite_deeplink(generate_mock):
    business = baker.make(Business)
    deeplink = get_cached_invite_deeplink(business_id=business.id, channel=Channel.Type.SMS)
    assert deeplink == 'dl.booksy.com/WithCache'
    assert generate_mock.call_count == 1
    assert generate_mock.call_args == call(staffer_invite=False)


@pytest.mark.django_db
@patch.object(
    Business,
    'get_sms_invite_mp_deeplink',
    return_value='dl.booksy.com/WithCache',
)
def test_get_staffer_sms_invite_deeplink(generate_mock):
    business = baker.make(Business)
    deeplink = get_cached_invite_deeplink(
        business_id=business.id,
        channel=Channel.Type.SMS,
        staffer_invite=True,
    )
    assert deeplink == 'dl.booksy.com/WithCache'
    assert generate_mock.call_count == 1
    assert generate_mock.call_args == call(staffer_invite=True)
