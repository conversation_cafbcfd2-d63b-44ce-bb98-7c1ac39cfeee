import pytest
from model_bakery import baker

from lib.elasticsearch.consts import ESDocType, ESIndex
from webapps.business.models import Business
from webapps.elasticsearch.elastic import ELASTIC
from webapps.elasticsearch.tests.elasticsearch_test_helpers import (
    ElasticSearchClientDjangoTestCase,
)


@pytest.mark.django_db
class BusinessElasticsearchTestBase(ElasticSearchClientDjangoTestCase):
    def setUp(self):
        Business.objects.all().delete()
        self.prepare_es_index(ESIndex.BUSINESS)
        self.prepare_es_index(ESIndex.BUSINESS_CUSTOMER)
        super().setUp()

    @staticmethod
    def create_business(**kwargs):
        return baker.make('business.Business', **kwargs)

    @property
    def business_index(self):
        return ELASTIC.indices[ESIndex.BUSINESS]

    @property
    def business_customer_index(self):
        return ELASTIC.indices[ESIndex.BUSINESS_CUSTOMER]

    def create_customer_doc(self, **kwargs):
        index = self.business_customer_index
        doc = index.documents[ESDocType.BUSINESS_CUSTOMER](**kwargs)
        doc.save(refresh='true')
        return doc
