# pylint: disable=protected-access,too-many-lines,invalid-name
"""Services promotions tests."""
import typing as t
from collections import namedtuple
from datetime import (
    datetime,
    time,
    timedelta,
)
from decimal import Decimal
from unittest import TestCase

import pytest
import pytz
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.db.models import Q
from freezegun import freeze_time
from mock import (
    MagicMock,
    PropertyMock,
    patch,
)
from model_bakery.recipe import baker

from lib.datetime_utils import get_week_day
from lib.test_utils import create_subbooking
from lib.time_24_hour import time24hour
from lib.tools import (
    format_currency,
    tznow,
)
from service.booking.tools import AppointmentData
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    SubbookingServiceVariantMode as SVMode,
    WhoMakesChange,
)
from webapps.booking.models import (
    Appointment,
    BookingResource,
    BookingSources,
    SubBooking,
)
from webapps.booking.serializers.appointment import (
    AppointmentSerializer,
    CustomerAppointmentSerializer,
)
from webapps.booking.serializers.booking import (
    BusinessBookingSerializer,
)
from webapps.booking.tasks import bulk_finish_appointments
from webapps.booking.tests.test_appointment_base import (
    BaseTestAppointment,
    build_appointment_data,
    build_custappt_data,
)
from webapps.business.baker_recipes import (
    happy_hours_recipe,
    service_recipe,
    service_variant_recipe,
)
from webapps.business.enums import (
    ComboPricing,
    ComboType,
    DiscountType,
    PriceType,
)
from webapps.business.models import (
    SERVICE_VARIANT_CLIENT_DISCOUNT,
    SERVICE_VARIANT_FLASH_SALE,
    SERVICE_VARIANT_HAPPY_HOURS,
    SERVICE_VARIANT_LAST_MINUTE,
    Business,
    ComboMembership,
    Resource,
    Service,
    ServicePromotion,
    ServiceVariant,
    ServiceVariantPayment,
    ServiceAddOn,
)
from webapps.business.serializers import (
    FlashSalePromotionSerializer,
    HappyHourPromotionSerializer,
    HappyHoursServiceVariantSerializer,
    LastMinutePromotionSerializer,
    PromotionServiceVariantSerializer,
)
from webapps.business.service_price import ServicePrice
from webapps.business.service_promotions import (
    BookingsGapFinder,
    CustomerAppointmentServicePromotionMixin,
    IncentiveBusinessFinder,
    ServicePromotionResolver,
    ServicePromotions,
)
from webapps.business.service_promotions import HappyHoursPromotion
from webapps.elasticsearch.elastic import ELASTIC
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.notification.models import (
    NotificationHistory,
    UserNotification,
)
from webapps.payment_gateway.scripts import create_default_wallets
from webapps.pos.baker_recipes import pos_recipe
from webapps.pos.calculations import round_currency
from webapps.pos.enums import PaymentTypeEnum, POSPlanPaymentTypeEnum
from webapps.pos.models import (
    POS,
    PaymentType,
    POSPlan,
)
from webapps.pos.provider.fake import _CARDS
from webapps.pos.serializers import CustomerAppointmentTransactionSerializer
from webapps.schedule.enums import DayOfWeek


class PropertiesMixin:

    @property
    def initial_time(self):
        return datetime(
            year=2018,
            month=11,
            day=20,
            hour=11,
            minute=12,
            second=13,
            tzinfo=self.business.get_timezone(),
        )

    @property
    def initial_date(self):
        return self.initial_time.date()

    @property
    def context(self):
        return {
            'business': self.business,
            'booking_source': self.booking_source,
        }

    @property
    def promotion_context(self):
        return {
            'business': self.business,
            'booking_source': self.booking_source,
        }

    @property
    def customer_appointment_context(self):
        return {
            'business': self.business,
            'single_category': self.business.is_single_category,
            'source': self.booking_source,
            'type': Appointment.TYPE.CUSTOMER,
            'user': self.customer_bci.user,
            'compatibilities': {
                'prepayment': True,
            },
            'is_external': False,
        }

    @property
    def business_appointment_context(self):
        return {
            'business': self.business,
            'single_category': self.business.is_single_category,
            'source': self.booking_source,
            'type': Appointment.TYPE.BUSINESS,
            'user': self.user,
        }


class BasePromotionTest(BaseTestAppointment, PropertiesMixin):

    def setUp(self):
        super().setUp()

        self.pos = pos_recipe.make(
            business=self.business,
            pay_by_app_status=POS.PAY_BY_APP_ENABLED,
        )
        baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=self.pos)
        baker.make(PaymentType, code=PaymentTypeEnum.CREDIT_CARD, pos=self.pos)

        self.adyen_pos_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            # not random just to force non-negative value of a fee (same for param below)
            provision=0.0235,
            txn_fee=0.1,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )
        self.pos.pos_plans.add(self.adyen_pos_plan)

        create_default_wallets()

        self.variant.price = Decimal('100.00')
        self.variant.type = PriceType.FIXED
        self.variant.save()

        self.gap_hole_variant.price = Decimal('200.0')
        self.gap_hole_variant.type = PriceType.FIXED
        self.gap_hole_variant.save()

    @property
    def current_date(self):
        return self.date.strftime(settings.DATE_FORMAT)

    def customer_user(self, discount=0):
        user = baker.make('user.User', email='<EMAIL>', cell_phone='3213213214')
        self.customer_bci = baker.make(
            'business.BusinessCustomerInfo',
            user=user,
            discount=discount,
            business=self.business,
        )

        return self.customer_bci.user

    def make_subbooking_data(self, booked_from, variants, addons=None, **kwargs):
        dry_run = kwargs.pop('dry_run', True)

        if isinstance(booked_from, time):
            booked_from = self._dt_from_hour(booked_from)

        variant = variants[0]

        data = build_custappt_data(
            variant=variant,
            booked_from=booked_from,
            staffer=self.staffer,
            addons=addons,  # assign addons only for the first subbooking
            **kwargs,
        )

        for sbk_id, _variant in enumerate(variants[1:], 1):
            if dry_run:
                _booked_from = None
            else:
                offset = self.variant.duration * sbk_id
                _booked_from = booked_from + offset

            _d = build_custappt_data(
                variant=_variant,
                booked_from=_booked_from,
                staffer=self.staffer,
                **kwargs,
            )
            data['subbookings'].extend(_d['subbookings'])

        return data

    def make_payment_method(self, user=None):
        return baker.make(
            'pos.PaymentMethod',
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=user or self.user,
        )

    def customer_appointment_data(self, booked_from, dry_run=True, variants=None, recurring=False):
        variants = variants or [list(self.variants.values())[0]]

        data = self.make_subbooking_data(
            booked_from=booked_from,
            variants=variants,
            dry_run=dry_run,
            recurring=recurring,
        )

        data['dry_run'] = dry_run
        data['payment_method'] = self.make_payment_method().id

        return data

    def customer_appointment_serializer(
        self,  # pylint: disable=too-many-arguments
        booked_from,
        recurring=False,
        subbookings_hours=None,
        dry_run=True,
        variants=None,
        addons=None,
        member_id=None,
    ):
        subbookings_hours = subbookings_hours or []
        _variants = variants or [self.variant]
        variants = _variants + _variants * len(subbookings_hours)

        if isinstance(booked_from, time):
            booked_from = self._dt_from_hour(booked_from)

        data = self.make_subbooking_data(
            booked_from,
            variants,
            dry_run=dry_run,
            recurring=recurring,
            addons=addons,
        )

        card = self.make_payment_method()
        data['payment_method'] = card.id
        data['dry_run'] = dry_run
        if member_id:
            data['book_for_family_member'] = {'member': member_id}

        serializer = CustomerAppointmentSerializer(
            instance=None,
            data=data,
            context=self.customer_appointment_context,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        appointment = serializer.save()
        _ = serializer.data

        cat_serializer = self.customer_appointment_transaction_serializer(data, appointment)

        return serializer, cat_serializer

    def customer_appointment_transaction_serializer(self, data, appointment):
        serializer = CustomerAppointmentTransactionSerializer(
            data=data,
            context={
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'user': self.user,
                'business': self.business,
                'pos': self.business.pos,
                'appointment_checkout': appointment.checkout,
                'appointment_data': AppointmentData.build(appointment.appointment),
                'extra_data': {},
                'compatibilities': {
                    'prepayment': True,
                },
            },
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)

        return serializer

    def business_appointment_serializer(
        self,
        booked_from,
        customer_user=None,
        dry_run=True,
        # subbookings_hours=None,
        staffer=None,
    ):
        staffer = staffer or self.staffer
        # subbookings_hours = subbookings_hours or []

        if isinstance(booked_from, time):
            booked_from = self._dt_from_hour(booked_from)

        data = build_appointment_data(
            variant=self.variant,
            booked_from=booked_from,
            booked_till=None,
            staffer=staffer,
        )

        data['dry_run'] = dry_run

        if customer_user:
            data.update(
                {
                    'customer': {
                        'mode': ACMode.CUSTOMER_CARD,
                        'id': customer_user.id,
                    }
                }
            )
        else:
            data.update({'customer': None})

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self.business_appointment_context,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()
        _ = serializer.data
        return serializer

    def biz_push_recipient(self):
        # profile for notification
        booking_source, _ = BookingSources.objects.get_or_create(
            name='Android',
            app_type=BookingSources.BUSINESS_APP,
            api_key='xx312',
        )
        biz_user_profile = baker.make(
            'user.UserProfile',
            profile_type='B',
            region=self.region,
            source=booking_source,
            user=self.user,
        )
        biz_notification = baker.make(
            'notification.UserNotification',
            profile=biz_user_profile,
            type='P',
        )
        baker.make(
            'notification.Reciever',
            identifier='xxx',
            business=self.business,
            device='android',
            customer_notifications=biz_notification,
        )

    def flash_sale(self, **kwargs):
        promotion_duration = kwargs.pop('promotion_duration', 1)
        discount_rate = kwargs.pop('discount_rate', 10)
        booking_start = kwargs.pop('booking_start', self.date).strftime(settings.DATE_FORMAT)
        _be = kwargs.pop('booking_end', None)
        booking_end = _be.strftime(settings.DATE_FORMAT) if _be else None
        variant_ids = kwargs.pop('variant_ids', [])

        data = {
            'promotion_duration': promotion_duration,
            'discount_rate': discount_rate,
            'booking_start': booking_start,
            'booking_end': booking_end,
            'service_variant_ids': variant_ids,
        }

        serializer = FlashSalePromotionSerializer(
            data=data,
            context=self.promotion_context,
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()
        return serializer

    def last_minute(self, **kwargs):
        data = {
            'discount_rate': kwargs.get('discount_rate', 10),
            'last_minute_hours': kwargs.get('last_minute_hours', 1),
            'service_variant_ids': kwargs.get('variant_ids', []),
        }
        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.promotion_context,
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()
        return serializer

    def happy_hours(self, data):
        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.promotion_context,
            many=True,
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()

        return serializer

    def booking_time(self, hour, minute):
        return datetime.combine(
            self.date,
            time(hour=hour, minute=minute),
        ).replace(tzinfo=self.business.get_timezone())

    def prepayment_for_variant(self, amount, variant=None):
        baker.make(
            ServiceVariantPayment,
            service_variant=variant or self.variant,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=amount,
        )


@patch.object(FlashSalePromotionSerializer, 'validate_service_variant_ids')
class TestFlashSalePromotionSerializer(PropertiesMixin, BaseTestAppointment):
    """
    Flash Sale (FS) promotion tests.
    """

    def test_1_day_promotion_duration(self, validate_mock):
        validate_mock.return_value = [123, 124]

        data = {
            'promotion_duration': 1,
            'discount_rate': 10.0,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            is_valid = serializer.is_valid()
            self.assertTrue(is_valid, serializer.errors)

            p_start = serializer.validated_data['promotion_start']
            p_end = serializer.validated_data['promotion_end']
            self.assertEqual(0, (p_end - p_start).days)

    def test_2_days_promotion_duration(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'promotion_duration': 2,
            'discount_rate': 10.0,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            is_valid = serializer.is_valid()
            self.assertTrue(is_valid, serializer.errors)

            p_start = serializer.validated_data['promotion_start']
            p_end = serializer.validated_data['promotion_end']
            self.assertEqual(1, (p_end - p_start).days)

    def test_3_days_promotion_duration(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'promotion_duration': 3,
            'discount_rate': 10.0,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            is_valid = serializer.is_valid()
            self.assertTrue(is_valid, serializer.errors)

            p_start = serializer.validated_data['promotion_start']
            p_end = serializer.validated_data['promotion_end']
            self.assertEqual(2, (p_end - p_start).days)

    def test_7_days_promotion_promotion_duration(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'promotion_duration': 7,
            'discount_rate': 10.0,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            is_valid = serializer.is_valid()
            self.assertTrue(is_valid, serializer.errors)

            p_start = serializer.validated_data['promotion_start']
            p_end = serializer.validated_data['promotion_end']
            self.assertEqual(6, (p_end - p_start).days)

    def test_custom_end_date_duration(self, validate_mock):
        validate_mock.return_value = [123, 124]
        promotion_end = self.initial_time + timedelta(days=4)
        data = {
            'discount_rate': 10.0,
            'promotion_end': promotion_end.strftime(settings.DATE_FORMAT),
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_time):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)
            self.assertEqual(
                serializer.validated_data['promotion_duration'],
                5,
                serializer.validated_data,
            )
            instance = serializer.save()
            assert instance.id

    def test_promotion_end_today_only(self, validate_mock):
        validate_mock.return_value = [123, 124]
        promotion_end = self.initial_time
        data = {
            'discount_rate': 10.0,
            'promotion_end': promotion_end.strftime(settings.DATE_FORMAT),
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_time):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)
            self.assertEqual(
                serializer.validated_data['promotion_duration'],
                1,
                serializer.validated_data,
            )
            instance = serializer.save()
            assert instance.id

    def test_invalid_promotion_promotion_duration(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'promotion_duration': 5,
            'discount_rate': 10.0,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        serializer = FlashSalePromotionSerializer(data=data, context=self.context)

        is_valid = serializer.is_valid()
        self.assertFalse(is_valid, serializer.errors)

    def test_missing_promotion_duration(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            # 'promotion_duration': 5,
            'discount_rate': 10.0,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        serializer = FlashSalePromotionSerializer(data=data, context=self.context)

        self.assertFalse(serializer.is_valid())

    def test_missing_discount_rate(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'promotion_duration': 1,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        serializer = FlashSalePromotionSerializer(data=data, context=self.context)

        self.assertFalse(serializer.is_valid())

    def test_booking_dates_range_today_any_day(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'promotion_duration': 1,
            'discount_rate': 10.0,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)
            self.assertIn('booking_start', serializer.validated_data)
            self.assertIn('booking_end', serializer.validated_data)

            booking_start = serializer.validated_data['booking_start']
            self.assertEqual(self.initial_date, booking_start)

            self.assertIsNone(serializer.validated_data['booking_end'])

    def test_booking_start_in_future(self, validate_mock):
        validate_mock.return_value = [123, 124]
        start_time = self.initial_date + timedelta(days=2)
        data = {
            'promotion_duration': 2,
            'discount_rate': 10.0,
            'booking_start': start_time.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)
            self.assertEqual(
                start_time,
                serializer.validated_data['booking_start'],
            )

    def test_booking_end_must_be_after_booking_start(self, validate_mock):
        validate_mock.return_value = [123, 124]
        start_time = self.initial_date + timedelta(days=2)
        end_time = self.initial_date + timedelta(days=1)

        data = {
            'promotion_duration': 1,
            'discount_rate': 10.0,
            'booking_start': start_time.strftime(settings.DATE_FORMAT),
            'booking_end': end_time.strftime(settings.DATE_FORMAT),
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            self.assertFalse(serializer.is_valid())

    def test_booking_end_may_be_forever(self, validate_mock):
        validate_mock.return_value = [123, 124]
        start_time = self.initial_date + timedelta(days=2)

        data = {
            'promotion_duration': 2,
            'discount_rate': 10.0,
            'booking_start': start_time.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            self.assertTrue(serializer.is_valid())

    def test_min_discount_rate_not_lt_1(self, validate_mock):
        validate_mock.return_value = [123, 124]
        start_time = self.initial_date + timedelta(days=2)

        data = {
            'promotion_duration': 2,
            'discount_rate': 0,
            'booking_start': start_time.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        serializer = FlashSalePromotionSerializer(
            data=data,
            context=self.context,
        )

        self.assertFalse(serializer.is_valid(), serializer.errors)
        self.assertEqual(
            ['Ensure this value is greater than or equal to 1.'],
            serializer.errors['discount_rate'],
        )

    def test_discount_rate_not_gt_100(self, validate_mock):
        validate_mock.return_value = [123, 124]
        start_time = self.initial_date + timedelta(days=2)

        data = {
            'promotion_duration': 2,
            'discount_rate': 101,
            'booking_start': start_time.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        serializer = FlashSalePromotionSerializer(
            data=data,
            context=self.context,
        )

        self.assertFalse(serializer.is_valid(), serializer.errors)
        self.assertEqual(
            ['Ensure this value is less than or equal to 100.'],
            serializer.errors['discount_rate'],
        )

    def test_serializer_create_instance(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'promotion_duration': 2,
            'discount_rate': 10.0,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)

            instance = serializer.save()
            self.assertIsNotNone(instance)
            self.assertIsNotNone(instance.booking_source)
            self.assertEqual(self.booking_source, instance.booking_source)
            self.assertEqual(SERVICE_VARIANT_FLASH_SALE, instance.type)

    def test_serializer_create_instance_no_booking_source(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'promotion_duration': 2,
            'discount_rate': 10.0,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date):
            context = self.context
            context.pop('booking_source')
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=context,
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)

            instance = serializer.save()
            self.assertIsNotNone(instance)
            self.assertIsNone(instance.booking_source)
            self.assertEqual(SERVICE_VARIANT_FLASH_SALE, instance.type)


@patch.object(FlashSalePromotionSerializer, 'validate_service_variant_ids')
class TestFlashSalePromotionStatus(PropertiesMixin, BaseTestAppointment):

    def test_promotion_is_active(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'promotion_duration': 2,
            'discount_rate': 10.0,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)

            instance = serializer.save()
            self.assertTrue(instance.active)

            has_active_promotion = ServicePromotions.has_active_promotion(
                business=self.business,
                promotion_type=SERVICE_VARIANT_FLASH_SALE,
            )
            self.assertTrue(has_active_promotion)

    def test_promotion_is_inactive(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'promotion_duration': 2,
            'discount_rate': 10.0,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date):
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)

            instance = serializer.save()
            self.assertTrue(instance.active)

            instance.active = False
            instance.save()

            has_active_promotion = ServicePromotions.has_active_promotion(
                business=self.business,
                promotion_type=SERVICE_VARIANT_FLASH_SALE,
            )
            self.assertFalse(has_active_promotion)

    def test_no_active_promotion_found(self, __):
        self.assertIsNone(ServicePromotions.flash_sale_promotion(self.business))

    def test_promotion_inactive_after_promotion_end(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'promotion_duration': 2,
            'discount_rate': 10.0,
            'booking_start': self.initial_date.strftime(settings.DATE_FORMAT),
            'booking_end': None,
            'service_variant_ids': [123, 124],
        }

        with freeze_time(self.initial_date) as frozen_time:
            serializer = FlashSalePromotionSerializer(
                data=data,
                context=self.context,
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)
            serializer.save()

            delta = timedelta(days=data['promotion_duration'] + 1)
            frozen_time.tick(delta)

            self.assertIsNone(ServicePromotions.flash_sale_promotion(self.business))


@patch.object(LastMinutePromotionSerializer, 'validate_service_variant_ids')
class TestLastMinutePromotionSerializer(PropertiesMixin, BaseTestAppointment):
    """
    Last Minute (LM) promotion test.
    """

    @property
    def initial_date(self):
        return datetime(
            year=2018,
            month=11,
            day=20,
            hour=11,
            minute=12,
            second=13,
            tzinfo=self.business.get_timezone(),
        )

    def test_discount_rate_is_required(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'discount_rate': 10,
            'last_minute_hours': 1,
            'service_variant_ids': [123, 124],
        }

        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.context,
        )

        is_valid = serializer.is_valid()
        self.assertTrue(is_valid, serializer.errors)

    def test_hours_before_1_hour_is_valid(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'discount_rate': 10,
            'last_minute_hours': 1,
            'service_variant_ids': [123, 124],
        }

        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.context,
        )

        is_valid = serializer.is_valid()
        self.assertTrue(is_valid, serializer.errors)

    def test_hours_before_2_hours_is_valid(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'discount_rate': 10,
            'last_minute_hours': 2,
            'service_variant_ids': [123, 124],
        }

        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.context,
        )

        is_valid = serializer.is_valid()
        self.assertTrue(is_valid, serializer.errors)

    def test_hours_before_3_hours_is_valid(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'discount_rate': 10,
            'last_minute_hours': 3,
            'service_variant_ids': [123, 124],
        }

        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.context,
        )

        is_valid = serializer.is_valid()
        self.assertTrue(is_valid, serializer.errors)

    def test_hours_before_4_hours_is_valid(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'discount_rate': 10,
            'last_minute_hours': 4,
            'service_variant_ids': [123, 124],
        }

        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.context,
        )

        is_valid = serializer.is_valid()
        self.assertTrue(is_valid, serializer.errors)

    def test_hours_before_5_hours_is_invalid(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'discount_rate': 10,
            'last_minute_hours': 5,
            'service_variant_ids': [123, 124],
        }

        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.context,
        )

        is_valid = serializer.is_valid()
        self.assertFalse(is_valid, serializer.errors)

    def test_serializer_create_instance(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'discount_rate': 10,
            'last_minute_hours': 1,
            'service_variant_ids': [123, 124],
        }

        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.context,
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)

        instance = serializer.save()
        self.assertIsNotNone(instance)
        self.assertIsNotNone(instance.booking_source)
        self.assertEqual(self.booking_source, instance.booking_source)
        self.assertEqual(SERVICE_VARIANT_LAST_MINUTE, instance.type)

    def test_discount_rate_no_lt_1(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'discount_rate': 0,
            'last_minute_hours': 1,
            'service_variant_ids': [123, 124],
        }

        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.context,
        )

        self.assertFalse(serializer.is_valid(), serializer.errors)
        self.assertEqual(
            ['Ensure this value is greater than or equal to 1.'],
            serializer.errors['discount_rate'],
        )

    def test_discount_rate_not_gt_100(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'discount_rate': 101,
            'last_minute_hours': 1,
            'service_variant_ids': [123, 124],
        }

        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.context,
        )

        self.assertFalse(serializer.is_valid(), serializer.errors)
        self.assertEqual(
            ['Ensure this value is less than or equal to 100.'],
            serializer.errors['discount_rate'],
        )


@patch.object(LastMinutePromotionSerializer, 'validate_service_variant_ids')
class TestLastMinutePromotionStatus(PropertiesMixin, BaseTestAppointment):

    def test_promotion_is_active(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'discount_rate': 10,
            'last_minute_hours': 1,
            'service_variant_ids': [123, 124],
        }

        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.context,
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)

        instance = serializer.save()
        self.assertTrue(instance.active)

        has_active_promotion = ServicePromotions.has_active_promotion(
            business=self.business,
            promotion_type=SERVICE_VARIANT_LAST_MINUTE,
        )
        self.assertTrue(has_active_promotion)

    def test_promotion_is_not_active(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = {
            'discount_rate': 10,
            'last_minute_hours': 1,
            'service_variant_ids': [123, 124],
        }

        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.context,
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)

        instance = serializer.save()
        instance.active = False
        instance.save()

        self.assertFalse(instance.active)

        has_active_promotion = ServicePromotions.has_active_promotion(
            business=self.business,
            promotion_type=SERVICE_VARIANT_LAST_MINUTE,
        )
        self.assertFalse(has_active_promotion)

    def test_no_active_promotion_found(self, __):
        self.assertIsNone(ServicePromotions.last_minute_promotion(self.business))


@patch.object(HappyHourPromotionSerializer, '_validate_service_variant_ids')
class TestHappyHourPromotionSerializer(PropertiesMixin, BaseTestAppointment):

    def test_service_variant_serializer_discount_type_rate(self, validate_mock):
        validate_mock.return_value = True
        data = {
            'discount_type': 'R',
            'discount_amount': 10.0,
            'hour_from': '10:00',
            'hour_till': '12:00',
            'service_variant_id': 123,
        }

        serializer = HappyHoursServiceVariantSerializer(
            data=data,
            context=self.context,
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)

    def test_service_variant_discount_rate_not_lt_1(self, validate_mock):
        validate_mock.return_value = True
        data = {
            'discount_type': 'R',
            'discount_amount': 0.0,
            'hour_from': '10:00',
            'hour_till': '12:00',
            'service_variant_id': 123,
        }

        serializer = HappyHoursServiceVariantSerializer(
            data=data,
            context=self.context,
        )

        self.assertFalse(serializer.is_valid(), serializer.errors)
        self.assertEqual(
            ['Ensure discount_rate is greater than or equal to 1.'],
            serializer.errors['non_field_errors'],
        )

    def test_service_variant_discount_rate_not_gt_99(self, validate_mock):
        validate_mock.return_value = True
        data = {
            'discount_type': 'R',
            'discount_amount': 101.0,
            'hour_from': '10:00',
            'hour_till': '12:00',
            'service_variant_id': 123,
        }

        serializer = HappyHoursServiceVariantSerializer(
            data=data,
            context=self.context,
        )

        self.assertFalse(serializer.is_valid(), serializer.errors)
        self.assertEqual(
            ['Ensure discount_rate is less than or equal to 100.'],
            serializer.errors['non_field_errors'],
        )

    def test_service_variant_serializer_discount_type_amount(self, validate_mock):
        validate_mock.return_value = True
        data = {
            'discount_type': 'A',
            'discount_amount': 0.1,
            'hour_from': '10:00',
            'hour_till': '12:00',
            'service_variant_id': 123,
        }

        serializer = HappyHoursServiceVariantSerializer(
            data=data,
            context=self.context,
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)

    def test_service_variant_discount_amount_not_lt_00_1(self, validate_mock):
        validate_mock.return_value = True
        data = {
            'discount_type': 'A',
            'discount_amount': 0.0,
            'hour_from': '10:00',
            'hour_till': '12:00',
            'service_variant_id': 123,
        }

        serializer = HappyHoursServiceVariantSerializer(
            data=data,
            context=self.context,
        )

        self.assertFalse(serializer.is_valid(), serializer.errors)
        self.assertEqual(
            ['Ensure discount_amount is greater than or equal to 0.01.'],
            serializer.errors['non_field_errors'],
        )

    def test_required_parameters(self, __):
        data = {}

        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
        )

        self.assertFalse(serializer.is_valid())
        self.assertIn('day_of_week', serializer.errors)
        self.assertIn('service_variants', serializer.errors)

    def test_save_instance(self, validate_mock):
        validate_mock.return_value = [123, 124, 125, 126]
        data = {
            'day_of_week': 0,
            'service_variants': [
                {
                    'discount_type': 'R',
                    'discount_amount': 10.0,
                    'hour_from': '10:00',
                    'hour_till': '12:00',
                    'service_variant_id': 123,
                },
                {
                    'discount_type': 'A',
                    'discount_amount': 10.50,
                    'hour_from': '10:00',
                    'hour_till': '12:00',
                    'service_variant_id': 124,
                },
                {
                    'discount_type': 'R',
                    'discount_amount': 10.0,
                    'hour_from': '10:00',
                    'hour_till': '12:00',
                    'service_variant_id': 125,
                },
                {
                    'discount_type': 'R',
                    'discount_amount': 10.0,
                    'hour_from': '10:00',
                    'hour_till': '12:00',
                    'service_variant_id': 126,
                },
            ],
        }

        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)

        instance = serializer.save()
        self.assertIsNotNone(instance)
        self.assertIsNotNone(instance.booking_source)
        self.assertEqual(self.booking_source, instance.booking_source)
        self.assertEqual(SERVICE_VARIANT_HAPPY_HOURS, instance.type)

    def test_save_many_instances(self, validate_mock):
        validate_mock.return_value = [123, 124, 125, 126]
        data = [
            {
                'day_of_week': 0,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 2,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 5,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 6,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
        ]

        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)

        instances = serializer.save()
        self.assertEqual(4, len(instances))

    def test_save_4_instances_set_2_empty_days(self, validate_mock):
        validate_mock.return_value = [123, 124, 125, 126]
        data = [
            {
                'day_of_week': 0,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 2,
                'service_variants': [],
            },
            {
                'day_of_week': 5,
                'service_variants': [],
            },
            {
                'day_of_week': 6,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
        ]

        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)

        instances = serializer.save()
        self.assertEqual(2, len(instances))

    def test_one_active_day_set_empty_promotion_becomes_inactive(self, validate_mock):
        validate_mock.return_value = [123, 124, 125, 126]
        data = [
            {
                'day_of_week': 2,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                ],
            },
        ]

        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)

        instances = serializer.save()
        self.assertEqual(1, len(instances))

        self.assertTrue(ServicePromotions.happy_hours_promotion(self.business))

        data = [
            {
                'day_of_week': 2,
                'service_variants': [],
            },
        ]
        serializer = HappyHourPromotionSerializer(
            instance=None,
            data=data,
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)

        instances = serializer.save()
        self.assertEqual(0, len(instances))

        self.assertFalse(ServicePromotions.happy_hours_promotion(self.business))

    def test_disable_1_of_4_promotion_is_active(self, validate_mock):
        validate_mock.return_value = [123, 124, 125, 126]
        data = [
            {
                'day_of_week': 0,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 2,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 5,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 6,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
        ]

        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)

        instances = serializer.save()
        self.assertEqual(4, len(instances))

        empty_day_2 = [
            {
                'day_of_week': 2,
                'service_variants': [],
            },
        ]
        serializer = HappyHourPromotionSerializer(
            data=empty_day_2,
            context=self.context,
            many=True,
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)
        instances = serializer.save()
        self.assertEqual(0, len(instances))

        self.assertTrue(ServicePromotions.happy_hours_promotion(self.business))

    def test_serialize_deserialize_many_instances(self, validate_mock):
        validate_mock.return_value = [123, 124, 125, 126]
        data = [
            {
                'day_of_week': 0,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 2,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 5,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 6,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
        ]

        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)

        instances = serializer.save()
        self.assertEqual(4, len(instances))

        data = HappyHourPromotionSerializer(instances, many=True, context=self.context).data

        self.assertEqual(4, len(data))

    def test_add_new_day_to_existing(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = [
            {
                'day_of_week': 0,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                ],
            }
        ]

        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()
        self.assertEqual(1, len(serializer.data))

        data = [
            {
                'day_of_week': 1,
                'service_variants': [
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                ],
            }
        ]

        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()

        happy_hours = ServicePromotions.happy_hours_promotion(self.business)
        self.assertEqual(2, len(happy_hours))

    def test_add_new_service_to_existing_day(self, validate_mock):
        validate_mock.return_value = [123, 124]
        data = [
            {
                'day_of_week': 0,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                ],
            }
        ]
        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()

        data = [
            {
                'day_of_week': 0,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                ],
            }
        ]

        serializer = HappyHourPromotionSerializer(
            data=data,
            instance=ServicePromotions.happy_hours_promotion(self.business),
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()
        self.assertEqual(0, serializer.data[0]['day_of_week'])
        self.assertEqual(2, len(serializer.data[0]['service_variants']))

    def test_replace_service_for_existing_day(self, validate_mock):
        validate_mock.return_value = [123, 124, 125, 126]
        data = [
            {
                'day_of_week': 0,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                ],
            }
        ]
        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()

        data = [
            {
                'day_of_week': 0,
                'service_variants': [
                    {
                        'discount_type': 'A',
                        'discount_amount': 20.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                ],
            }
        ]

        serializer = HappyHourPromotionSerializer(
            data=data,
            instance=ServicePromotions.happy_hours_promotion(self.business),
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()
        self.assertEqual(0, serializer.data[0]['day_of_week'])

        service_variants = serializer.data[0]['service_variants']
        self.assertEqual(1, len(service_variants))
        self.assertEqual('A', service_variants[0]['discount_type'])

    def test_add_new_day_with_existing_one(self, validate_mock):
        validate_mock.return_value = [123]
        data = [
            {
                'day_of_week': 0,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                ],
            }
        ]
        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()

        data = [
            {
                'day_of_week': 1,
                'service_variants': [
                    {
                        'discount_type': 'A',
                        'discount_amount': 20.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                ],
            }
        ]

        serializer = HappyHourPromotionSerializer(
            data=data,
            instance=ServicePromotions.happy_hours_promotion(self.business),
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()
        self.assertEqual(0, serializer.data[0]['day_of_week'])

        self.assertEqual(2, len(serializer.data))
        self.assertEqual(
            123,
            serializer.data[0]['service_variants'][0]['service_variant_id'],
        )
        self.assertEqual(
            123,
            serializer.data[1]['service_variants'][0]['service_variant_id'],
        )


@patch.object(HappyHourPromotionSerializer, '_validate_service_variant_ids')
class TestHappyHoursPromotionStatus(PropertiesMixin, BaseTestAppointment):

    def test_promotion_is_active(self, validate_mock):
        validate_mock.return_value = [123, 124, 125, 126]
        data = {
            'day_of_week': 0,
            'service_variants': [
                {
                    'discount_type': 'R',
                    'discount_amount': 10.0,
                    'hour_from': '10:00',
                    'hour_till': '12:00',
                    'service_variant_id': 123,
                },
                {
                    'discount_type': 'A',
                    'discount_amount': '10.50',
                    'hour_from': '10:00',
                    'hour_till': '12:00',
                    'service_variant_id': 124,
                },
                {
                    'discount_type': 'R',
                    'discount_amount': 10.0,
                    'hour_from': '10:00',
                    'hour_till': '12:00',
                    'service_variant_id': 125,
                },
                {
                    'discount_type': 'R',
                    'discount_amount': 10.0,
                    'hour_from': '10:00',
                    'hour_till': '12:00',
                    'service_variant_id': 126,
                },
            ],
        }

        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)

        instance = serializer.save()
        self.assertIsNotNone(instance)
        self.assertTrue(instance.active)

        has_active_promotion = ServicePromotions.has_active_promotion(
            business=self.business,
            promotion_type=SERVICE_VARIANT_HAPPY_HOURS,
        )
        self.assertTrue(has_active_promotion)

    def test_promotion_is_inactive(self, validate_mock):
        validate_mock.return_value = [123, 124, 125, 126]
        data = {
            'day_of_week': 0,
            'service_variants': [
                {
                    'discount_type': 'R',
                    'discount_amount': 10.0,
                    'hour_from': '10:00',
                    'hour_till': '12:00',
                    'service_variant_id': 123,
                },
                {
                    'discount_type': 'A',
                    'discount_amount': '10.50',
                    'hour_from': '10:00',
                    'hour_till': '12:00',
                    'service_variant_id': 124,
                },
                {
                    'discount_type': 'R',
                    'discount_amount': 10.0,
                    'hour_from': '10:00',
                    'hour_till': '12:00',
                    'service_variant_id': 125,
                },
                {
                    'discount_type': 'R',
                    'discount_amount': 10.0,
                    'hour_from': '10:00',
                    'hour_till': '12:00',
                    'service_variant_id': 126,
                },
            ],
        }

        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)

        instance = serializer.save()
        self.assertIsNotNone(instance)

        instance.active = False
        instance.save()
        self.assertFalse(instance.active)

        has_active_promotion = ServicePromotions.has_active_promotion(
            business=self.business,
            promotion_type=SERVICE_VARIANT_HAPPY_HOURS,
        )
        self.assertFalse(has_active_promotion)

    def test_disable_promotions(self, validate_mock):
        validate_mock.return_value = [123, 124, 125, 126]
        data = [
            {
                'day_of_week': 0,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 2,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 5,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
            {
                'day_of_week': 6,
                'service_variants': [
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 123,
                    },
                    {
                        'discount_type': 'A',
                        'discount_amount': '10.50',
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 124,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 125,
                    },
                    {
                        'discount_type': 'R',
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': 126,
                    },
                ],
            },
        ]

        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.context,
            many=True,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)

        instances = serializer.save()
        self.assertEqual(4, len(instances))

        result = ServicePromotions.disable_happy_hours(self.business)
        self.assertEqual(4, result)


# pylint: disable=too-many-public-methods
@patch(
    (
        'webapps.booking.serializers.appointment.'
        'BusinessInCustomerAppointmentSerializer.to_representation'
    ),
    return_value=None,
)
@patch('lib.elasticsearch.tools.index_document', return_value=None)
@patch.object(Business, 'boost_status', PropertyMock(return_value=Business.BoostStatus.ENABLED))
class TestPromotionResolver(BasePromotionTest):

    def test_client_flash_sale_single_appointment(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])

    def test_client_flash_sale_single_prepayment_appointment(self, *__):
        discount_rate = 10
        prepayment_amount = 20.0
        expected_price = round_currency(
            self.variant.price - self.variant.price * discount_rate / 100
        )
        expected_prepayment = round_currency(
            prepayment_amount - prepayment_amount * discount_rate / 100
        )

        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.prepayment_for_variant(amount=prepayment_amount)
        self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=discount_rate,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            c_serializer, cat_serializer = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            subbooking = c_serializer.data['subbookings'][0]
            self.assertEqual(
                expected_price,
                subbooking['service_promotion']['_price'],
            )
            self.assertEqual(
                expected_prepayment,
                cat_serializer.validated_data['prepayment'],
            )

    def test_client_flash_sale_next_week(self, *__):
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=5)

        with freeze_time(booking_time):
            booking_start = tznow()
            booking_end = booking_start + timedelta(days=14)

            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
                booking_start=booking_start,
                booking_end=booking_end,
            )

            booked_from = booking_time + timedelta(days=7, hours=1)

            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])

    def test_client_flash_sale_book_for_last_day(self, *__):
        """Book today for today and tomorrow to get 10% off."""
        self.customer_user(discount=5)

        booking_time = self.booking_time(9, 30)

        with freeze_time(booking_time):
            booking_start = booking_time + timedelta(days=1, hours=1)
            booking_end = booking_start + timedelta(days=1)

            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
                booking_start=booking_start,
                booking_end=booking_end,
            )

            booked_from = booking_end

            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])

    def test_client_flash_sale_book_on_last_day(self, *__):
        """Book today for today and tomorrow to get 10% off."""
        self.customer_user(discount=5)

        booking_time = self.booking_time(9, 30)

        with freeze_time(booking_time) as frozen_time:
            booking_start = booking_time + timedelta(days=1, hours=1)
            booking_end = booking_start + timedelta(days=1)

            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
                booking_start=booking_start,
                booking_end=booking_end,
            )

            frozen_time.tick(timedelta(days=1))
            booked_from = booking_end - timedelta(minutes=30)

            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])

    def test_client_flash_sale_from_booking(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            _appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            _subbooking = _appointment.data['subbookings'][0]
            self.assertIn('service_promotion', _subbooking)
            self.assertIsNotNone(_subbooking['service_promotion'])

            booking = SubBooking.objects.get(id=_subbooking['id'])
            appointment_from_booking = AppointmentWrapper([booking])
            appointment = CustomerAppointmentSerializer(
                instance=appointment_from_booking,
                context={
                    'business': booking.appointment.business,
                    'user': booking.appointment.booked_for.user,
                    'dry_run': True,
                },
            )

            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])

    def test_client_flash_sale_single_appointment_no_bci(self, *__):
        booked_from = self.booking_time(10, 0)
        booking_time = self.booking_time(9, 30)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )

            customer_user_no_bci = baker.make(
                'user.User', email='<EMAIL>', cell_phone='3213213666'
            )
            data = {
                'compatibilities': {
                    'prepayment': True,
                },
                'customer_note': 'duh!',
                'dry_run': False,
                'recurring': False,
                'subbookings': [
                    {
                        'booked_from': booked_from.isoformat(),
                        'service_variant': {
                            'id': self.variant.id,
                            'mode': SVMode.VARIANT,
                        },
                        'staffer_id': self.staffer.id,
                    }
                ],
            }

            serializer = CustomerAppointmentSerializer(
                instance=None,
                data=data,
                context={
                    'business': self.business,
                    'single_category': self.business.is_single_category,
                    'source': self.booking_source,
                    'type': Appointment.TYPE.CUSTOMER,
                    'user': customer_user_no_bci,
                },
            )
            self.assertTrue(serializer.is_valid(), serializer.errors)
            serializer.save()

            subbooking = serializer.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])

    def test_client_resolve_for_combo_service(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=10)

        service_variant_child_1 = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                tax_rate=None,
            ),
            price=Decimal('100.00'),
            type=PriceType.FIXED,
        )
        service_variant_child_2 = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                tax_rate=None,
            ),
            price=Decimal('200.00'),
            type=PriceType.FIXED,
        )
        service_variant_child_1.add_staffers([self.staffer])
        service_variant_child_2.add_staffers([self.staffer])

        service_variant = service_variant_recipe.make(
            service=service_recipe.make(
                name='Combo Service',
                business=self.business,
                combo_type=ComboType.SEQUENCE,
            ),
            combo_pricing=ComboPricing.CUSTOM,
            type=None,
            price=None,
            label='Combo ServiceVariant',
        )
        ComboMembership.objects.bulk_create(
            [
                ComboMembership(
                    combo=service_variant,
                    child=service_variant_child_1,
                    order=1,
                    type=PriceType.FIXED,
                    price=Decimal('10.00'),
                ),
                ComboMembership(
                    combo=service_variant,
                    child=service_variant_child_1,
                    order=2,
                    type=PriceType.FREE,
                    price=None,
                ),
                ComboMembership(
                    combo=service_variant,
                    child=service_variant_child_2,
                    order=3,
                    type=PriceType.FIXED,
                    price=Decimal('20.00'),
                ),
            ]
        )

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=40,
                variant_ids=[
                    service_variant.id,
                ],
            )
            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                variants=[service_variant],
                dry_run=True,
            )

        self.assertEqual(
            serializer.data['subbookings'][0]['service_promotion']['promotion_type'],
            SERVICE_VARIANT_FLASH_SALE,
        )
        self.assertEqual(serializer.data['subbookings'][0]['service_promotion']['discount'], '40%')
        self.assertEqual(
            serializer.data['subbookings'][0]['service_promotion']['price_before_discount'],
            Decimal('30.00'),
        )
        self.assertEqual(serializer.data['total_value'], 18.00)
        self.assertEqual(serializer.data['total_discount_amount'], 12.00)

        with freeze_time(booking_time):
            data = self.make_subbooking_data(
                booked_from,
                [service_variant],
                dry_run=False,
                recurring=False,
            )
            serializer = CustomerAppointmentSerializer(
                instance=None,
                data=data,
                context=self.customer_appointment_context,
            )
            self.assertTrue(serializer.is_valid(), serializer.errors)
            serializer.save()

        appointment_wrapper = AppointmentWrapper.get_by_appointment_id(
            appointment_id=serializer.data['appointment_uid'],
            customer_user_id=self.customer_bci.user.id,
        )
        subbooking = appointment_wrapper.appointment.subbookings[0]
        self.assertEqual(subbooking.resolved_price, Decimal('18.00'))
        self.assertEqual(subbooking.combo_children[0].resolved_price, Decimal('6.00'))
        self.assertEqual(subbooking.combo_children[1].resolved_price, Decimal('0.00'))
        self.assertEqual(subbooking.combo_children[2].resolved_price, Decimal('12.00'))
        self.assertEqual(
            appointment_wrapper.checkout.total,
            ServicePrice(
                value=Decimal('18.00'),
                price_type=PriceType.FIXED,
                discount=Decimal('12.00'),
            ),
        )

    def test_client_total_for_multibooking_appointment(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10, variant_ids=[v.id for v in list(self.variants.values())]
            )
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
                subbookings_hours=[time(10, 30), time(11, 0)],
            )

            for subbooking in appointment.data['subbookings']:
                self.assertIn('service_promotion', subbooking)
                self.assertIsNotNone(subbooking['service_promotion'])

            subbooking_sum = sum(
                subbooking['service_promotion']['_price']
                for subbooking in appointment.data['subbookings']
            )

            self.assertEqual(
                format_currency(subbooking_sum),
                appointment.data['total'],
            )

    def test_client_multi_appointment_total_using_single(self, *__):
        booking_time = self.booking_time(9, 30)
        booked_from = self.booking_time(10, 0)
        customer_user = self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10, variant_ids=[v.id for v in list(self.variants.values())]
            )
            appt, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
                subbookings_hours=[time(10, 30), time(11, 0)],
            )

            appointment = AppointmentWrapper.get_by_appointment_id(
                appointment_id=appt.data['appointment_uid'],
                customer_user_id=customer_user.id,
            )

            serializer = CustomerAppointmentSerializer(
                instance=appointment,
                context={
                    'business': appt.business,
                    'user': customer_user,
                    'single_category': appt.business.is_single_category,
                },
            )

            data = serializer.data

            subbooking_sum = sum(s['service_promotion']['_price'] for s in data['subbookings'])
            self.assertEqual(270.0, subbooking_sum)
            self.assertIn('total', data)
            self.assertEqual(format_currency(subbooking_sum), data['total'])

    def test_client_multi_appointment_total_using_multi(self, *__):
        booking_time = self.booking_time(9, 30)
        booked_from = self.booking_time(10, 0)
        customer_user = self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10, variant_ids=[v.id for v in list(self.variants.values())]
            )
            appt, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
                subbookings_hours=[time(10, 30), time(11, 0)],
            )

            appointment = AppointmentWrapper.get_by_appointment_id(
                appointment_id=appt.data['appointment_id'],
                customer_user_id=customer_user.id,
            )

            serializer = CustomerAppointmentSerializer(
                instance=appointment,
                context={
                    'business': appt.business,
                    'user': customer_user,
                    'single_category': appt.business.is_single_category,
                },
            )

            data = serializer.data

            subbooking_sum = sum(s['service_promotion']['_price'] for s in data['subbookings'])

            self.assertIn('total', data)
            self.assertEqual(270.0, subbooking_sum)
            self.assertEqual(format_currency(subbooking_sum), data['total'])

    def test_client_last_minute_for_single_appointment(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.last_minute(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])

    def test_client_last_minute_for_single_prepayment_appointment(self, *__):
        discount_rate = 10
        prepayment_amount = 20.0
        expected_price = round_currency(
            self.variant.price - self.variant.price * discount_rate / 100
        )
        expected_prepayment = round_currency(
            prepayment_amount - prepayment_amount * discount_rate / 100
        )

        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()
        self.prepayment_for_variant(amount=prepayment_amount)

        with freeze_time(booking_time):
            self.last_minute(
                discount_rate=discount_rate,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            c_serializer, cat_serializer = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            subbooking = c_serializer.data['subbookings'][0]
            self.assertEqual(
                expected_price,
                subbooking['service_promotion']['_price'],
            )
            self.assertEqual(
                expected_prepayment,
                cat_serializer.validated_data['prepayment'],
            )

    def test_client_happy_hours_rate_discount(self, *__):
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.RATE,
                        'discount_amount': 50.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(50.0, subbooking['service_promotion']['_price'])

    def test_client_happy_hours_100_rate_discount(self, *__):
        discount_amount = 100.0
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.RATE,
                        'discount_amount': discount_amount,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(0.0, subbooking['service_promotion']['_price'])

    def test_client_happy_hours_book_before_end(self, *__):
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.RATE,
                        'discount_amount': 50.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(11, 45)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(50.0, subbooking['service_promotion']['_price'])

    def test_client_happy_hours_book_after_end(self, *__):
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.RATE,
                        'discount_amount': 50.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(12, 45)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            assert not subbooking['service_promotion']

    def test_client_happy_hours_amount_discount(self, *__):
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.AMOUNT,
                        'discount_amount': 25.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(75.0, subbooking['service_promotion']['_price'])

    def test_client_happy_hours_amount_discount_over_price(self, *__):
        discount_amount = 101.0  # 1 more than variant.price
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.AMOUNT,
                        'discount_amount': discount_amount,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(0.0, subbooking['service_promotion']['_price'])

    def test_client_happy_hours_combo_custom_price(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=0)

        service_variant_child_1 = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                name='Child 1',
                tax_rate=None,
            ),
            price=Decimal('100.00'),
            type=PriceType.FIXED,
        )
        service_variant_child_2 = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                name='Child 2',
                tax_rate=None,
            ),
            price=Decimal('200.00'),
            type=PriceType.FIXED,
        )
        service_variant_child_1.add_staffers([self.staffer])
        service_variant_child_2.add_staffers([self.staffer])

        service_variant = service_variant_recipe.make(
            service=service_recipe.make(
                name='Combo Service',
                business=self.business,
                combo_type=ComboType.SEQUENCE,
            ),
            combo_pricing=ComboPricing.CUSTOM,
            type=None,
            price=None,
            label='Combo ServiceVariant',
        )
        ComboMembership.objects.bulk_create(
            [
                ComboMembership(
                    combo=service_variant,
                    child=service_variant_child_1,
                    order=1,
                    type=PriceType.FIXED,
                    price=Decimal('10.00'),
                ),
                ComboMembership(
                    combo=service_variant,
                    child=service_variant_child_1,
                    order=2,
                    type=PriceType.FREE,
                    price=None,
                ),
                ComboMembership(
                    combo=service_variant,
                    child=service_variant_child_2,
                    order=3,
                    type=PriceType.FIXED,
                    price=Decimal('20.00'),
                ),
            ]
        )
        for day_of_week in DayOfWeek:
            happy_hours_recipe.make(
                business=self.business,
                happy_hours_week_day=day_of_week,
                promotion_options={
                    'service_variants': [
                        {
                            'hour_from': '10:00',
                            'hour_till': '12:00',
                            'discount_type': DiscountType.RATE.value,
                            'discount_amount': '40.00',
                            'service_variant_id': service_variant.id,
                        },
                        {
                            'hour_from': '10:00',
                            'hour_till': '12:00',
                            'discount_type': DiscountType.RATE.value,
                            'discount_amount': '80.00',
                            'service_variant_id': service_variant_child_1.id,
                        },  # expect this to be ignored
                    ],
                },
            )

        with freeze_time(booking_time):
            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                variants=[service_variant],
                dry_run=True,
            )

        # expect to use only promotion for whole combo, not its children
        expected_service_promotion = {
            'promotion_type': SERVICE_VARIANT_HAPPY_HOURS,
            'price_before_discount': Decimal('30.00'),
            'price_unformatted': Decimal('18.00'),
            'discount': '40%',
            'discount_amount': Decimal('12.00'),
            'discount_value': Decimal('40.00'),
        }
        self.assertIsNotNone(serializer.data['subbookings'][0]['service_promotion'])
        self.assertDictEqual(
            serializer.data['subbookings'][0]['service_promotion'],
            serializer.data['subbookings'][0]['service_promotion'] | expected_service_promotion,
        )
        self.assertEqual(serializer.data['total_value'], 18.00)
        self.assertEqual(serializer.data['total_discount_amount'], 12.00)

        with freeze_time(booking_time):
            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                variants=[service_variant],
                dry_run=False,
            )

        appointment_wrapper = AppointmentWrapper.get_by_appointment_id(
            appointment_id=serializer.data['appointment_uid'],
            customer_user_id=self.customer_bci.user.id,
        )
        subbooking = appointment_wrapper.appointment.subbookings[0]
        self.assertEqual(subbooking.resolved_price, Decimal('18.00'))
        self.assertEqual(subbooking.combo_children[0].resolved_price, Decimal('6.00'))
        self.assertEqual(subbooking.combo_children[1].resolved_price, Decimal('0.00'))
        self.assertEqual(subbooking.combo_children[2].resolved_price, Decimal('12.00'))
        self.assertEqual(
            appointment_wrapper.checkout.total,
            ServicePrice(
                value=Decimal('18.00'),
                price_type=PriceType.FIXED,
                discount=Decimal('12.00'),
            ),
        )

    def test_client_change_hh_appointment_to_day_without_promotion(self, *__):
        discount_amount = 50.0
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        day_of_week = (booking_time.weekday() + 1) % 7
        data = [
            {
                'day_of_week': day_of_week,
                'service_variants': [
                    {
                        'discount_type': DiscountType.AMOUNT,
                        'discount_amount': discount_amount,
                        'hour_from': '10:00',
                        'hour_till': '19:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            },
        ]
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            c_serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            subbooking = c_serializer.data['subbookings'][0]
            self.assertIsNotNone(subbooking['service_promotion'])

            appt = AppointmentWrapper.get_by_appointment_id(
                customer_user_id=self.customer_bci.user_id,
                appointment_id=c_serializer.data['appointment_uid'],
            )

            _booking = appt.subbookings[0]
            updated_data = c_serializer.data
            updated_data['subbookings'][0]['booked_from'] = (
                _booking.booked_from + timedelta(days=1)
            ).isoformat()
            updated_data['subbookings'][0]['booked_till'] = (
                _booking.booked_from + timedelta(days=1)
            ).isoformat()
            updated_data['dry_run'] = False

            serializer = CustomerAppointmentSerializer(
                instance=appt,
                data=updated_data,
                context={
                    'business': appt.business,
                    'single_category': appt.business.is_single_category,
                    'user': self.customer_bci.user,
                    'compatibilities': {},
                },
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)
            serializer.save()

            data = serializer.data
            subbooking = data['subbookings'][0]
            assert not subbooking['service_promotion']
            self.assertEqual(format_currency(self.variant.price), data['total'])

    def test_hh_variant_resolving(self, *__):
        services = baker.make(
            'business.Service',
            business=self.business,
            active=True,
            _quantity=3,
        )
        variants = [
            baker.make(
                ServiceVariant,
                service=service,
                price=100,
                type=PriceType.FIXED,
                active=True,
                duration=relativedelta(minutes=30),
                time_slot_interval=relativedelta(minutes=self.INTERVAL),
            )
            for service in services
        ]
        self.staffer.add_services(services)
        self.assertEqual(3, len(variants))

        data = [
            {
                'day_of_week': 1,
                'service_variants': [
                    {
                        'discount_type': DiscountType.RATE,
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': variant.id,
                    }
                    for variant in variants
                ],
            },
            {
                'day_of_week': 2,
                'service_variants': [
                    {
                        'discount_type': DiscountType.RATE,
                        'discount_amount': 50.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    }
                ],
            },
        ]
        self.assertEqual(3, len(data[0]['service_variants']))
        self.assertEqual(1, len(data[1]['service_variants']))

        self.customer_user()

        booking_time = self.booking_time(9, 30)
        # we want to book for monday and thuesday
        booked_from = self.booking_time(10, 0)
        booked_from = booked_from + timedelta(days=(7 - booked_from.isoweekday() + 1))

        with freeze_time(booking_time):
            self.happy_hours(data)

            # visit on Monday would be 10% off
            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                variants=variants[1:],
                dry_run=False,
            )

            subbooking = serializer.data['subbookings'][0]
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])

            # another visit on tuesday same variant no longer promoted
            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from + timedelta(days=1),
                variants=variants[1:],
                dry_run=False,
            )

            subbooking = serializer.data['subbookings'][0]
            assert not subbooking['service_promotion']

    def test_client_change_hh_appointment_after_promo_time_range(self, *__):
        discount_amount = 50.0  # 1 more than variant.price
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.AMOUNT,
                        'discount_amount': discount_amount,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)

            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            subbooking = serializer.data['subbookings'][0]
            self.assertIsNotNone(subbooking['service_promotion'])

            appt = AppointmentWrapper.get_by_appointment_id(
                customer_user_id=self.customer_bci.user_id,
                appointment_id=serializer.data['appointment_uid'],
            )

            updated_data = serializer.data
            updated_data['subbookings'][0].update(
                {
                    'booked_from': self.booking_time(12, 0),
                    'booked_till': self.booking_time(12, 30),
                }
            )
            updated_data['dry_run'] = False

            serializer = CustomerAppointmentSerializer(
                instance=appt,
                data=updated_data,
                context={
                    'business': appt.business,
                    'single_category': appt.business.is_single_category,
                    'user': self.customer_bci.user,
                    'compatibilities': {},
                },
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)
            serializer.save()

            data = serializer.data
            subbooking = data['subbookings'][0]
            assert not subbooking['service_promotion']
            self.assertEqual(format_currency(self.variant.price), data['total'])

    def test_client_client_discount_promotion(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=33)

        with freeze_time(booking_time):
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(67.0, subbooking['service_promotion']['_price'])

    def test_client_client_discount_prepayment_promotion(self, *__):
        discount_rate = 10
        prepayment_amount = 20.0
        expected_price = round_currency(
            self.variant.price - self.variant.price * discount_rate / 100
        )
        expected_prepayment = round_currency(
            prepayment_amount - prepayment_amount * discount_rate / 100
        )
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=discount_rate)
        self.prepayment_for_variant(amount=prepayment_amount)

        with freeze_time(booking_time):
            c_serializer, cat_serializer = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            subbooking = c_serializer.data['subbookings'][0]
            self.assertEqual(
                expected_price,
                subbooking['service_promotion']['_price'],
            )
            self.assertEqual(expected_prepayment, cat_serializer.validated_data['prepayment'])

    def test_client_client_discount_from_booking(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=50)

        with freeze_time(booking_time):
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(50.0, subbooking['service_promotion']['_price'])

            # get appointment from existing booking
            booking = SubBooking.objects.get(id=subbooking['id'])
            appointment_from_booking = AppointmentWrapper([booking])
            context = {
                'business': booking.appointment.business,
                'user': booking.appointment.booked_for.user,
                'dry_run': True,
            }
            appt_from_booking = CustomerAppointmentSerializer(
                instance=appointment_from_booking,
                context=context,
            )

            subbooking = appt_from_booking.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(50.0, subbooking['service_promotion']['_price'])

    def test_client_best_discount(self, *__):
        """LM Should always win.

        CD = 5, FS = 10, HH = 15, LM = 20 (%)

        """
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 45)

        client_discount = 5
        flash_sale_discount = 10
        happy_hours_discount = 15
        last_minute_discount = 20

        self.customer_user(discount=client_discount)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=flash_sale_discount,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            self.last_minute(
                discount_rate=last_minute_discount,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            happy_hours = [
                {
                    'day_of_week': dow,
                    'service_variants': [
                        {
                            'discount_type': DiscountType.RATE,
                            'discount_amount': happy_hours_discount,
                            'hour_from': '10:00',
                            'hour_till': '12:00',
                            'service_variant_id': self.variant.id,
                        },
                    ],
                }
                for dow in DayOfWeek
            ]
            self.happy_hours(happy_hours)

            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            _subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', _subbooking)
            self.assertIsNotNone(_subbooking['service_promotion'])
            self.assertEqual(80.0, _subbooking['service_promotion']['_price'])

            # get appointment from existing booking
            booking = SubBooking.objects.get(id=_subbooking['id'])
            appointment_from_booking = AppointmentWrapper([booking])
            context = {
                'business': booking.appointment.business,
                'user': booking.appointment.booked_for.user,
                'dry_run': True,
            }
            subbooking = CustomerAppointmentSerializer(
                instance=appointment_from_booking,
                context=context,
            ).data['subbookings'][0]

            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(80.0, subbooking['service_promotion']['_price'])

    def test_client_best_prepayment_discount(self, *__):
        """LM Should always win.

        CD = 5, FS = 10, HH = 15, LM = 20 (%)

        """
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 45)

        client_discount = 5
        flash_sale_discount = 10
        happy_hours_discount = 15
        expected_discount = last_minute_discount = 20
        prepayment_amount = 20.0

        expected_price = round_currency(
            self.variant.price - self.variant.price * expected_discount / 100
        )

        expected_prepayment = round_currency(
            prepayment_amount - prepayment_amount * expected_discount / 100
        )

        self.customer_user(discount=client_discount)
        self.prepayment_for_variant(amount=prepayment_amount)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=flash_sale_discount,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            self.last_minute(
                discount_rate=last_minute_discount,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            happy_hours = [
                {
                    'day_of_week': dow,
                    'service_variants': [
                        {
                            'discount_type': DiscountType.RATE,
                            'discount_amount': happy_hours_discount,
                            'hour_from': '10:00',
                            'hour_till': '12:00',
                            'service_variant_id': self.variant.id,
                        },
                    ],
                }
                for dow in DayOfWeek
            ]
            self.happy_hours(happy_hours)

            c_serializer, cat_serializer = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            _subbooking = c_serializer.data['subbookings'][0]
            self.assertEqual(
                expected_price,
                _subbooking['service_promotion']['_price'],
            )
            self.assertEqual(expected_prepayment, cat_serializer.validated_data['prepayment'])

            # get appointment from existing booking
            booking = SubBooking.objects.get(id=_subbooking['id'])
            c_serializer_2 = CustomerAppointmentSerializer(
                instance=AppointmentWrapper([booking]),
                context={
                    'business': booking.appointment.business,
                    'user': booking.appointment.booked_for.user,
                    'dry_run': True,
                },
            )
            subbooking = c_serializer_2.data['subbookings'][0]

            self.assertEqual(
                expected_price,
                subbooking['service_promotion']['_price'],
            )

    def test_client_no_promotion_from_request(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            assert not subbooking['service_promotion']

    def test_client_promotion_as_default(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=0)

        with freeze_time(booking_time):
            appt_serializer, _ = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            booking = appt_serializer.instance.subbookings[0]
            self.assertEqual(
                SERVICE_VARIANT_CLIENT_DISCOUNT,
                booking.resolved_promotion_type,
            )

    def test_always_resolve_client_discount(self, *__):
        booked_from = self.booking_time(10, 0)
        booking_time = self.booking_time(9, 23)
        self.customer_user(discount=20)

        with freeze_time(booking_time):
            appointment_data = self.make_subbooking_data(
                booked_from=booked_from,
                variants=[self.variant],
                recurring=True,
            )
            appointment_serializer = CustomerAppointmentSerializer(
                data=appointment_data,
                context=self.customer_appointment_context,
            )

            self.assertTrue(
                appointment_serializer.is_valid(),
                appointment_serializer.errors,
            )
            appointment_serializer.save()

            booking = appointment_serializer.data['subbookings'][0]
            self.assertIn('service_promotion', booking, booking)
            self.assertEqual(
                booking['service_promotion']['discount_amount'],
                20,
            )

    def test_client_no_promotion_from_booking(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            booking_id = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )[0].data['subbookings'][0]['id']
            booking = SubBooking.objects.get(id=booking_id)

            appointment = CustomerAppointmentSerializer(
                instance=AppointmentWrapper([booking]),
                context={
                    'business': booking.appointment.business,
                    'user': booking.appointment.booked_for.user,
                },
            )

            subbooking = appointment.data['subbookings'][0]
            self.assertIsNone(subbooking['service_promotion'])

    def test_client_multi_no_promotions_from_request(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=True,
                subbookings_hours=[time(10, 30), time(11, 0)],
            )

            self.assertIsNotNone(appointment)
            for sbk in appointment.data['subbookings']:
                assert not sbk['service_promotion']

    def test_business_no_promotion_for_customer(self, *__):
        booked_from = self.booking_time(10, 0)
        booking_time = self.booking_time(10, 0)
        self.customer_user()

        with freeze_time(booking_time):
            ba_serializer = self.business_appointment_serializer(
                booked_from=booked_from,
                customer_user=self.customer_bci,
            )

            subbooking = ba_serializer.data['subbookings'][0]
            assert not subbooking['service_promotion']
            # self.assertEqual(
            #     SERVICE_VARIANT_CLIENT_DISCOUNT,
            #     subbooking['service_promotion']['promotion_type'],
            # )
            # self.assertEqual(
            #     self.customer_bci.discount,
            #     subbooking['service_promotion']['discount_value'],
            # )

    def test_business_only_client_discount_for_customer(self, *__):
        booked_from = self.booking_time(10, 0)
        booking_time = self.booking_time(10, 0)
        self.customer_user(discount=50)

        with freeze_time(booking_time):
            serializer = self.business_appointment_serializer(
                booked_from=booked_from,
                customer_user=self.customer_bci,
            )

            subbooking = serializer.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(50.0, subbooking['service_promotion']['_price'])

    def test_customer_booking_info_total(self, *__):
        booked_from = self.booking_time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=5)

        addon = baker.make(
            ServiceAddOn,
            services=[self.service],
            business=self.business,
            price=3,
            price_type=PriceType.FIXED,
            duration=relativedelta(minutes=15),
            max_allowed_quantity=10,
        )

        with freeze_time(booking_time):
            serializer_1, _cat_1 = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
                subbookings_hours=[time(10, 30), time(11, 0)],
            )
            self.assertIsNotNone(serializer_1)
            self.assertEqual(
                serializer_1.data['subbookings'][0]['service_promotion']['discount_amount'],
                Decimal('5.00'),
            )
            self.assertEqual(
                serializer_1.instance.total,
                ServicePrice(
                    value=Decimal('285.00'),  # 3 * $100 * 0.95
                    price_type=PriceType.FIXED,
                    discount=Decimal('15.00'),
                ),
            )
            self.assertEqual(serializer_1.data['total_value'], 285.0)
            self.assertEqual(serializer_1.data['total_discount_amount'], 15.0)

            serializer_2, _cat_2 = self.customer_appointment_serializer(
                booked_from=booked_from + timedelta(hours=2),
                dry_run=False,
                subbookings_hours=[time(12, 30), time(13, 0)],
                addons=[addon.clone(quantity=2)],
            )
            self.assertIsNotNone(serializer_2)
            self.assertEqual(
                serializer_2.instance.total,
                ServicePrice(
                    value=Decimal('290.70'),  # (3 * $100 + 2 * $3) * 0.95
                    price_type=PriceType.FIXED,
                    discount=Decimal('15.30'),
                ),
            )
            self.assertEqual(serializer_2.data['total_value'], 290.7)
            self.assertEqual(serializer_2.data['total_discount_amount'], 15.3)

            bci = self.customer_bci
            filters = [Q(booked_for=bci)]

            bookings, _ = SubBooking.get_dashboard_items(
                filters,
                0,
                10,
                order='-booked_from',
            )

            for booking in bookings:
                booking.appointment.business_note = 'business note'
                booking.appointment.business_secret_note = 'business secret note'
                booking.appointment.save(update_fields=['business_note', 'business_secret_note'])
                booking.refresh_from_db()

            bookings_serializer = BusinessBookingSerializer(
                bookings,
                many=True,
                context={
                    'business': bci.business,
                    'access_level': 'owner',
                    'single_category': bci.business.is_single_category,
                },
            )

            self.assertIsNotNone(bookings_serializer)
            for booking in bookings_serializer.data:
                self.assertIn('total', booking)
                self.assertEqual(booking['business_note'], 'business note')
                self.assertEqual(booking['business_secret_note'], 'business secret note')

    def test_client_client_discount_resolve_max_100_discount(self, *__):
        """What happens when client has 100% discount."""
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=100)

        with freeze_time(booking_time):
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(0.0, subbooking['service_promotion']['_price'])

    def test_business_do_not_clean_client_single(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=5)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            ca_serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            ca_subbooking = ca_serializer.data['subbookings'][0]
            self.assertIn('service_promotion', ca_subbooking)
            self.assertIsNotNone(ca_subbooking['service_promotion'])
            self.assertEqual(90.0, ca_subbooking['service_promotion']['_price'])

            b_appointment = AppointmentWrapper.get_by_appointment_id(
                ca_serializer.data['appointment_uid'],
                self.business.id,
            )
            self.assertIsNotNone(b_appointment)

            # move appointment as business
            data = {
                '_notify_about_reschedule': False,
                '_preserve_order': False,
                '_update_future_bookings': False,
                '_version': b_appointment._version,
                'business_note': '',
                'business_secret_note': '',
                'customer': {
                    'id': self.customer_bci.id,
                    'mode': ACMode.CUSTOMER_CARD,
                },
                'customer_note': None,
                'dry_run': False,
                'new_repeating': None,
                'overbooking': False,
                'subbookings': [
                    {
                        'appliance_id': -1,
                        'booked_from': self.booking_time(10, 15),
                        'booked_till': (self.booking_time(10, 15) + self.variant.duration),
                        'duration': None,
                        'id': None,
                        'service_variant': {
                            'id': self.variant.id,
                            'mode': SVMode.VARIANT,
                            'service_name': None,
                        },
                        'staffer_id': -1,
                    }
                ],
            }

            ba_serializer = AppointmentSerializer(
                instance=b_appointment,
                data=data,
                context=self.business_appointment_context,
            )

            self.assertTrue(ba_serializer.is_valid(), ba_serializer.errors)
            ba_serializer.save()

            b_subbooking = ba_serializer.data['subbookings'][0]
            self.assertIsNotNone(b_subbooking['service_promotion'])
            self.assertEqual(90.0, b_subbooking['service_promotion']['_price'])

    def test_business_do_not_clean_client_multi(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=5)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            ca_serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                subbookings_hours=[time(10, 30), time(11, 0)],
                dry_run=False,
            )
            ca_subbookings = ca_serializer.data['subbookings']

            for subbooking in ca_subbookings:
                self.assertIn('service_promotion', subbooking)
                self.assertIsNotNone(subbooking['service_promotion'])
                self.assertEqual(90.0, subbooking['service_promotion']['_price'])

            b_appointment = AppointmentWrapper.get_by_appointment_id(
                ca_serializer.data['appointment_id'],
                self.business.id,
            )

            self.assertIsNotNone(b_appointment)

            data = {
                '_notify_about_reschedule': False,
                '_preserve_order': False,
                '_update_future_bookings': False,
                '_version': b_appointment._version,
                'business_note': '',
                'business_secret_note': '',
                'customer': {
                    'id': self.customer_bci.id,
                    'mode': ACMode.CUSTOMER_CARD,
                },
                'customer_note': None,
                'dry_run': False,
                'new_repeating': None,
                'overbooking': False,
                'subbookings': [],
            }
            delta = timedelta(minutes=15)
            for idx, ca_subbooking in enumerate(ca_subbookings, 1):
                booked_from = booking_time + delta * idx
                booked_till = booked_from + self.variant.duration
                data['subbookings'].append(
                    {
                        'appliance_id': -1,
                        'booked_from': booked_from,
                        'booked_till': booked_till,
                        'duration': None,
                        'id': None,
                        'service_variant': {
                            'id': ca_subbooking['service_variant']['id'],
                            'mode': SVMode.VARIANT,
                        },
                        'staffer_id': ca_subbooking['staffer']['id'],
                    }
                )

            ba_serializer = AppointmentSerializer(
                instance=b_appointment,
                data=data,
                context=self.business_appointment_context,
            )

            self.assertTrue(ba_serializer.is_valid(), ba_serializer.errors)
            ba_serializer.save()

            for b_subbooking in ba_serializer.data['subbookings']:
                for key in ['_price', 'discount', 'type']:
                    self.assertEqual(
                        ca_subbookings[-1]['service_promotion'][key],
                        b_subbooking['service_promotion'][key],
                    )

    def test_business_change_bci_for_appointment(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=5)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            ca_serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            ca_subbooking = ca_serializer.data['subbookings'][0]
            self.assertIn('service_promotion', ca_subbooking)
            self.assertIsNotNone(ca_subbooking['service_promotion'])
            self.assertEqual(90.0, ca_subbooking['service_promotion']['_price'])

            b_appointment = AppointmentWrapper.get_by_appointment_id(
                ca_serializer.data['appointment_uid'],
                self.business.id,
            )
            self.assertIsNotNone(b_appointment)

            bci_2 = baker.make(
                'business.BusinessCustomerInfo',
                user=baker.make(
                    'user.User', email='<EMAIL>', cell_phone='3213213215'
                ),
                discount=50,
                business=self.business,
            )

            # change bci for client appointment as business
            data = {
                '_notify_about_reschedule': False,
                '_preserve_order': False,
                '_update_future_bookings': False,
                '_version': b_appointment._version,
                'business_note': '',
                'business_secret_note': '',
                'customer': {
                    'id': bci_2.id,
                    'mode': ACMode.CUSTOMER_CARD,
                },
                'customer_note': None,
                'dry_run': False,
                'new_repeating': None,
                'overbooking': False,
                'subbookings': [
                    {
                        'appliance_id': -1,
                        'booked_from': self.booking_time(10, 0),
                        'booked_till': (self.booking_time(10, 0) + self.variant.duration),
                        'duration': None,
                        'id': None,
                        'service_variant': {
                            'id': self.variant.id,
                            'mode': SVMode.VARIANT,
                            'service_name': None,
                        },
                        'staffer_id': -1,
                    }
                ],
            }

            ba_serializer = AppointmentSerializer(
                instance=b_appointment,
                data=data,
                context=self.business_appointment_context,
            )

            self.assertTrue(ba_serializer.is_valid(), ba_serializer.errors)
            ba_serializer.save()
            b_subbooking = ba_serializer.data['subbookings'][0]
            self.assertIsNotNone(b_subbooking['service_promotion'])
            self.assertEqual(50.0, b_subbooking['service_promotion']['_price'])

    def test_business_change_service_for_bci_appointment(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=5)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            ca_serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            ca_subbooking = ca_serializer.data['subbookings'][0]
            self.assertIn('service_promotion', ca_subbooking)
            self.assertIsNotNone(ca_subbooking['service_promotion'])
            self.assertEqual(90.0, ca_subbooking['service_promotion']['_price'])

            b_appointment = AppointmentWrapper.get_by_appointment_id(
                ca_serializer.data['appointment_uid'],
                self.business.id,
            )
            self.assertIsNotNone(b_appointment)

            # move appointment as business
            data = {
                '_notify_about_reschedule': False,
                '_preserve_order': False,
                '_update_future_bookings': False,
                '_version': b_appointment._version,
                'business_note': '',
                'business_secret_note': '',
                'customer': {
                    'id': self.customer_bci.id,
                    'mode': ACMode.CUSTOMER_CARD,
                },
                'customer_note': None,
                'dry_run': True,
                'new_repeating': None,
                'overbooking': False,
                'subbookings': [
                    {
                        'appliance_id': -1,
                        'booked_from': self.booking_time(10, 0),
                        'booked_till': None,
                        'duration': None,
                        'id': ca_subbooking['id'],
                        'service_variant': {
                            # 'id': self.variant.id,
                            'id': self.gap_hole_variant.id,
                            'mode': SVMode.VARIANT,
                            'service_name': None,
                        },
                        'staffer_id': -1,
                    }
                ],
            }

            ba_serializer = AppointmentSerializer(
                instance=b_appointment,
                data=data,
                context=self.business_appointment_context,
            )

            self.assertTrue(ba_serializer.is_valid(), ba_serializer.errors)
            ba_serializer.save()

            b_subbooking = ba_serializer.data['subbookings'][0]
            self.assertIsNotNone(b_subbooking['service_promotion'])
            self.assertEqual(190.0, b_subbooking['service_promotion']['_price'])

    @patch.object(
        Business, 'boost_status', PropertyMock(return_value=Business.BoostStatus.DISABLED)
    )
    def test_promotions_no_business_promotion_client_discount_50(self, *__):
        booked_from = self.booking_time(10, 30)
        booking_time = self.booking_time(10, 0)
        self.customer_user(discount=50)

        with freeze_time(booking_time):
            self.last_minute(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            ca_serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = ca_serializer.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertEqual(50.0, subbooking['service_promotion']['_price'])

    def test_business_empty_appointment(self, *__):
        booked_from = self.booking_time(10, 0)
        booked_till = booked_from + timedelta(minutes=30)
        booking_time = self.booking_time(9, 45)
        self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )

            data = {
                '_notify_about_reschedule': True,
                '_preserve_order': False,
                '_update_future_bookings': False,
                '_version': 0,
                'business_note': '',
                'business_secret_note': '',
                'customer': None,
                'customer_note': None,
                'dry_run': True,
                'new_repeating': None,
                'overbooking': False,
                'subbookings': [
                    {
                        'appliance_id': -1,
                        'booked_from': booked_from.isoformat(),
                        'booked_till': booked_till.isoformat(),
                        'duration': None,
                        'id': None,
                        'service_variant': None,
                        'staffer_id': -1,
                    }
                ],
            }
            ba_serializer = AppointmentSerializer(
                instance=None,
                data=data,
                context=self.business_appointment_context,
            )
            self.assertTrue(ba_serializer.is_valid(), ba_serializer.errors)

    @patch('webapps.notification.tasks.push.fcm_android_push_task')
    def test_send_push_on_cancel_6_hours_before(self, *__):
        booked_from = self.booking_time(10, 0) + timedelta(days=1)
        booking_time = self.booking_time(9, 30)
        self.customer_user()
        self.biz_push_recipient()

        with freeze_time(booking_time) as frozen_time:
            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = serializer.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)

            appointment = serializer.instance
            self.assertIsNotNone(appointment)

            frozen_time.tick(timedelta(hours=18))

            appointment.appointment.update_appointment(
                updated_by=self.customer_bci.user,
                status=Appointment.STATUS.CANCELED,
                customer_note='',
                who_makes_change=WhoMakesChange.CUSTOMER,
            )

            caspm = CustomerAppointmentServicePromotionMixin
            result = caspm.push_last_minute_incentive(
                appointment=appointment.appointment,
            )

            self.assertTrue(result)

            task_id = f'last_minute_incentive:now:business_id={appointment.business_id}'
            self.assertEqual(1, NotificationHistoryDocument.task_count(task_id=task_id))

    def test_do_not_send_push_on_cancel_13_hours_before(self, *__):
        booked_from = self.booking_time(10, 0) + timedelta(days=1)
        booking_time = self.booking_time(9, 30)
        self.customer_user()
        self.biz_push_recipient()

        with freeze_time(booking_time) as frozen_time:
            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = serializer.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)

            appointment = serializer.instance
            self.assertIsNotNone(appointment)

            frozen_time.tick(timedelta(hours=11))

            appointment.appointment.update_appointment(
                updated_by=self.customer_bci.user,
                status=Appointment.STATUS.CANCELED,
                customer_note='',
                who_makes_change=WhoMakesChange.CUSTOMER,
            )

            caspm = CustomerAppointmentServicePromotionMixin
            scenario_result = caspm.push_last_minute_incentive(
                appointment=appointment.appointment,
            )

            self.assertFalse(scenario_result)

            task_id = f'last_minute_incentive:now:business_id={appointment.business_id}'
            self.assertEqual(0, NotificationHistoryDocument.task_count(task_id=task_id))

    def test_do_not_send_push_on_cancel_1_hours_before(self, *__):
        booked_from = self.booking_time(10, 0) + timedelta(days=1)
        booking_time = self.booking_time(9, 30)
        self.customer_user()
        self.biz_push_recipient()

        with freeze_time(booking_time) as frozen_time:
            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = serializer.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)

            appointment = serializer.instance
            self.assertIsNotNone(appointment)

            frozen_time.tick(timedelta(hours=23))

            appointment.appointment.update_appointment(
                updated_by=self.customer_bci.user,
                status=Appointment.STATUS.CANCELED,
                customer_note='',
                who_makes_change=WhoMakesChange.CUSTOMER,
            )

            caspm = CustomerAppointmentServicePromotionMixin
            scenario_result = caspm.push_last_minute_incentive(
                appointment=appointment.appointment,
            )

            self.assertFalse(scenario_result)

            task_id = f'last_minute_incentive:now:business_id={appointment.business_id}'
            self.assertEqual(0, NotificationHistoryDocument.task_count(task_id=task_id))

    def test_do_not_send_push_if_sent_lte_7_days(self, *__):
        booked_from = self.booking_time(10, 0) + timedelta(days=1)
        booking_time = self.booking_time(9, 30)
        push_sent_days_ago = 4

        NotificationHistoryDocument.tasks_clear()
        self.customer_user()
        self.biz_push_recipient()

        with freeze_time(booking_time) as frozen_time:
            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = serializer.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)

            appointment = serializer.instance
            self.assertIsNotNone(appointment)

            # some fake notification in the past
            frozen_time.tick(timedelta(days=-push_sent_days_ago))
            task_id = f'last_minute_incentive:now:business_id={appointment.business_id}'
            NotificationHistory.add(
                sender=NotificationHistory.SENDER_SYSTEM,
                type=UserNotification.PUSH_NOTIFICATION,
                task_id=task_id,
                created=booking_time - timedelta(days=push_sent_days_ago),
            )
            ELASTIC.indices['notification_history'].refresh()
            frozen_time.tick(timedelta(days=push_sent_days_ago))

            frozen_time.tick(timedelta(hours=18))
            appointment.appointment.update_appointment(
                updated_by=self.customer_bci.user,
                status=Appointment.STATUS.CANCELED,
                customer_note='',
                who_makes_change=WhoMakesChange.CUSTOMER,
            )
            appointment.appointment.refresh_from_db()

            _caspm = CustomerAppointmentServicePromotionMixin
            result = _caspm.push_last_minute_incentive(
                appointment=appointment.appointment,
            )

            self.assertIsNot(False, result)
            self.assertEqual(1, NotificationHistoryDocument.task_count(task_id=task_id))


@pytest.mark.parametrize(
    'discount_rate, last_minute_hours, expected, subdomain',
    (
        (
            100,
            1,
            (
                'Book an appointment no more than 1 hour in advance'
                ' to score a 100% discount! What are you waiting for?'
                ' http://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
                '.booksy.com'
            ),
            'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.booksy.com',
        ),
        (
            100,
            2,
            (
                'Book an appointment no more than 2 hours in advance'
                ' to score a 100% discount! What are you waiting for?'
                ' http://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
                '.booksy.com'
            ),
            'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.booksy.com',
        ),
        (
            100,
            3,
            (
                'Book an appointment no more than 3 hours in advance'
                ' to score a 100% discount! What are you waiting for?'
                ' http://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
                '.booksy.com'
            ),
            'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.booksy.com',
        ),
        (
            100,
            7,
            (
                'Book an appointment no more than 7 hours in advance'
                ' to score a 100% discount! What are you waiting for?'
                ' http://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
                '.booksy.com'
            ),
            'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.booksy.com',
        ),
    ),
)
def test_last_minute_blast(discount_rate, last_minute_hours, expected, subdomain):
    instance = MagicMock(
        promotion_options={'discount_rate': discount_rate},
        last_minute_hours=last_minute_hours,
    )

    business = MagicMock()
    business.name = subdomain.split('.')[0]
    business.get_seo_url.return_value = 'http://' + subdomain

    serializer = LastMinutePromotionSerializer(instance=instance, context={'business': business})
    assert expected == serializer.blast_message(instance)


@pytest.mark.parametrize(
    ','.join(
        [
            'discount_rate',
            'booking_start',
            'booking_end',
            'promotion_start',
            'promotion_duration',
            'expected',
            'subdomain',
        ]
    ),
    (
        (
            100,
            datetime(2019, 2, 4),
            datetime(2019, 2, 17),
            datetime(2019, 2, 4),
            1,
            (
                "Flash sale! Get a 100% discount when you"
                " come in for appointment between 2019-02-04 and 2019-02-17."
                " Small catch: You need to book TODAY!"
                " http://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                ".booksy.com"
            ),
            "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.booksy.com",
        ),
        (
            100,
            datetime(2019, 2, 10),
            None,
            datetime(2019, 2, 4),
            1,
            (
                "Flash sale! Get a 100% discount when you"
                " come in for appointment from 2019-02-10."
                " Small catch: You need to book TODAY!"
                " http://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                ".booksy.com"
            ),
            "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.booksy.com",
        ),
        (
            100,
            datetime(2019, 2, 4),
            datetime(2019, 2, 4),
            datetime(2019, 2, 4),
            1,
            (
                "Flash sale! Get a 100% discount when you book Today."
                " http://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                ".booksy.com"
            ),
            "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.booksy.com",
        ),
        (
            100,
            datetime(2019, 2, 10),
            datetime(2019, 2, 17),
            datetime(2019, 2, 4),
            3,
            (
                "Flash sale! Get a 100% discount when you"
                " come in for appointment between 2019-02-10 and 2019-02-17."
                " Small catch: You need to book within the next 3 DAYS!"
                " http://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                ".booksy.com"
            ),
            "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.booksy.com",
        ),
        (
            100,
            datetime(2019, 2, 10),
            None,
            datetime(2019, 2, 4),
            7,
            (
                "Flash sale! Get a 100% discount when you"
                " come in for appointment from 2019-02-10."
                " Small catch: You need to book within the next 7 DAYS!"
                " http://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                ".booksy.com"
            ),
            "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.booksy.com",
        ),
        (
            100,
            datetime(2019, 2, 4),
            datetime(2019, 2, 4),
            datetime(2019, 2, 4),
            2,
            (
                "Flash sale!"
                " Get a 100% discount when you book in next 2 days."
                " http://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                ".booksy.com"
            ),
            "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.booksy.com",
        ),  # pylint: disable=too-many-arguments
    ),
)
def test_flash_sale_blast(
    discount_rate,
    booking_start,
    booking_end,
    promotion_start,
    promotion_duration,
    expected,
    subdomain,
):
    instance = MagicMock(
        booking_start=booking_start,
        booking_end=booking_end,
        promotion_start=promotion_start,
        promotion_duration=promotion_duration,
        promotion_options={'discount_rate': discount_rate},
    )
    business = MagicMock(subdomain=subdomain)
    business.name = subdomain.split('.')[0]
    business.get_seo_url.return_value = 'http://' + subdomain

    serializer = FlashSalePromotionSerializer(instance=instance, context={'business': business})
    assert expected == serializer.blast_message(instance)


@pytest.mark.parametrize(
    'discount_amount, discount_type, expected, subdomain',
    (
        (
            100,
            DiscountType.RATE,
            (
                'Dear Customer, Happy Hours are back! Book now with great'
                ' discounts up to 100%!'
                ' http://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
                '.booksy.com'
            ),
            'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.booksy.com',
        ),
        (
            150,
            DiscountType.AMOUNT,
            (
                'Dear Customer, Happy Hours are back! Book now with great'
                ' discounts up to $150.00!'
                ' http://xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
                '.booksy.com'
            ),
            'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.booksy.com',
        ),
    ),
)
def test_happy_hours_blast(discount_amount, discount_type, expected, subdomain):
    instance = MagicMock(
        promotion_options={
            'happy_hours_week_day': 1,
            'service_variants': [
                {
                    'discount_amount': discount_amount,
                    'discount_type': discount_type,
                },
            ],
        }
    )
    business = MagicMock()
    business.name = subdomain.split('.')[0]
    business.get_seo_url.return_value = 'http://' + subdomain

    booking_source_mock = MagicMock(id=1)

    serializer = HappyHourPromotionSerializer(
        instance=[instance],
        context={
            'business': business,
            'booking_source': booking_source_mock,
        },
        many=True,
    )

    assert expected == serializer.blast_message(serializer.data)


@pytest.mark.django_db
def test_happy_hours_promotion__from_request(minimal_business):
    """
    Check if HappyHoursPromotion.from_request gives no result if there is no matching
    service variant
    """
    timezone = minimal_business.get_timezone()
    service_variant, other_service_variant = service_variant_recipe.make(
        service__business=minimal_business,
        _quantity=2,
    )
    service_promotion = baker.make(
        ServicePromotion,
        type=SERVICE_VARIANT_HAPPY_HOURS,
        booking_start=datetime(2018, 10, 1, tzinfo=timezone),
        booking_end=datetime(2018, 11, 1, tzinfo=timezone),
        promotion_start=datetime(2018, 10, 1, tzinfo=timezone),
        promotion_end=datetime(2018, 11, 1, tzinfo=timezone),
        promotion_options={
            'service_variants': [
                {
                    "hour_from": "0:00",
                    "hour_till": "23:59",
                    "discount_type": DiscountType.RATE,
                    "discount_amount": "20.00",
                    "service_variant_id": service_variant.id,
                },
            ],
        },
        happy_hours_week_day=0,
    )

    promotion = HappyHoursPromotion(
        business=minimal_business,
        promotion=service_promotion,
    )
    assert (
        promotion.from_request(
            {
                'service_variant': service_variant,
                'booked_from': datetime(2018, 10, 21, 12, 30, tzinfo=timezone),
            }
        )
        is not None
    )

    assert (
        promotion.from_request(
            {
                'service_variant': other_service_variant,
                'booked_from': datetime(2018, 10, 21, 12, 30, tzinfo=timezone),
            }
        )
        is None
    )


@pytest.mark.django_db
class TestHappyHoursIncentive(TestCase):

    def setUp(self):
        self.biz_booking_src, _ = BookingSources.objects.get_or_create(
            app_type=BookingSources.BUSINESS_APP, api_key=f'biz-abc-{datetime.now().microsecond}'
        )

        self.ny_tz = self.timezone_from_unicode('America/New_York')
        # self.ny_biz = self.biz_setup(self.ny_tz)

        self.ch_tz = self.timezone_from_unicode('America/Chicago')
        # self.ch_biz = self.biz_setup(self.ch_tz)

        self.la_tz = self.timezone_from_unicode('America/Los_Angeles')
        # self.la_biz = self.biz_setup(self.la_tz)

    @staticmethod
    def timezone_from_unicode(tz_name):
        return pytz.timezone(tz_name)

    @staticmethod
    def biz_setup(tz):
        region = baker.make('structure.Region', name='A', time_zone_name=tz)
        owner = baker.make('user.User')

        business = baker.make(
            'business.Business',
            owner=owner,
            active=True,
            status=Business.Status.PAID,
            region=region,
            time_zone_name=tz,
            visible=True,
        )
        staffers = [
            baker.make(
                Resource,
                business=business,
                type=Resource.STAFF,
                active=True,
                visible=True,
                staff_user=owner,
                staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
            )
        ]
        BaseTestAppointment._make_resource_calendar(staffers[0])
        staffers.extend(
            baker.make(
                Resource,
                business=business,
                type=Resource.STAFF,
                visible=True,
                active=True,
                staff_user=baker.make('user.User'),
                staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
                _quantity=2,
            )
        )
        BaseTestAppointment._make_resource_calendar(staffers[1])
        BaseTestAppointment._make_resource_calendar_with_break(staffers[2])

        service_variants = baker.make(
            'business.ServiceVariant',
            service=baker.make('business.Service', business=business, active=True),
            active=True,
            duration=relativedelta(minutes=30),
            time_slot_interval=relativedelta(minutes=15),
            _quantity=3,
        )
        for sv in service_variants:
            sv.add_staffers(staffers)

        assert len(staffers) == 3
        assert len(service_variants) == 3

        BizSetup = namedtuple(
            'BizSetup',
            [
                'region',
                'owner',
                'business',
                'staffers',
                'service_variants',
            ],
        )

        return BizSetup(
            region,
            owner,
            business,
            staffers,
            service_variants,
        )

    def test_find_timezones_local_time_before_search(self):
        biz = self.biz_setup(self.ch_tz.zone)
        tz = biz.business.get_timezone()

        datetime_to_freeze = datetime(2019, 2, 27, 3, 13, tzinfo=tz)
        time_to_find = time(7, 26)

        with freeze_time(datetime_to_freeze):
            result = IncentiveBusinessFinder.timezones_for_localtime(
                time_to_find,
                skip_minutes=True,
            )
            self.assertEqual(0, len(result), result)

            result = IncentiveBusinessFinder.timezones_for_localtime(
                time_to_find,
                skip_minutes=False,
            )
            self.assertEqual(0, len(result), result)

    def test_find_timezones_local_time_on_search(self):
        biz = self.biz_setup(self.ch_tz.zone)
        tz = biz.business.get_timezone()

        datetime_to_freeze = datetime(2019, 2, 27, 3, 13, tzinfo=tz)
        time_to_find = time(7, 26)

        with freeze_time(datetime_to_freeze) as frozen_time:
            frozen_time.tick(timedelta(hours=4, minutes=13))
            result = IncentiveBusinessFinder.timezones_for_localtime(
                time_to_find,
                skip_minutes=True,
            )
            self.assertEqual(1, len(result))
            self.assertIn('America/Chicago', result)

            result = IncentiveBusinessFinder.timezones_for_localtime(
                time_to_find,
                skip_minutes=False,
            )
            self.assertEqual(1, len(result))
            self.assertIn('America/Chicago', result)

    def test_find_timezones_local_time_after_search(self):
        biz = self.biz_setup(self.ch_tz.zone)
        tz = biz.business.get_timezone()

        datetime_to_freeze = datetime(2019, 2, 27, 3, 13, tzinfo=tz)
        time_to_find = time(7, 26)

        with freeze_time(datetime_to_freeze) as frozen_time:
            frozen_time.tick(timedelta(hours=4, minutes=14))
            result = IncentiveBusinessFinder.timezones_for_localtime(
                time_to_find,
                skip_minutes=True,
            )
            self.assertEqual(1, len(result))
            self.assertIn('America/Chicago', result)

            result = IncentiveBusinessFinder.timezones_for_localtime(
                time_to_find,
                skip_minutes=False,
            )
            self.assertEqual(0, len(result))

    def test_business_ids_for_incentive(self):
        notified_business = self.biz_setup(self.ch_tz.zone)
        promotion_business = self.biz_setup(self.ch_tz.zone)
        business_to_notify = self.biz_setup(self.ch_tz.zone)

        Business.objects.all().update(boost_status=Business.BoostStatus.ENABLED)
        tz = notified_business.business.get_timezone()
        datetime_to_freeze = datetime(2019, 2, 27, 6, 59, tzinfo=tz)

        with freeze_time(datetime_to_freeze) as frozen_time:
            # NY biz already notified 7 days ago
            frozen_time.tick(timedelta(days=-7))
            n_history = baker.make(
                NotificationHistory,
                sender=NotificationHistory.SENDER_SYSTEM,
                type=UserNotification.PUSH_NOTIFICATION,
                task_type=NotificationHistory.TASK_TYPE__HH_PROMO_INCENTIVE,
                business_id=notified_business.business.id,
            )
            self.assertIsNotNone(n_history)

            # business with active HH promotion
            frozen_time.tick(timedelta(days=7))
            happy_hours = baker.make(
                ServicePromotion,
                type=SERVICE_VARIANT_HAPPY_HOURS,
                active=True,
                business=promotion_business.business,
            )
            self.assertIsNotNone(happy_hours)

            # clean business without promotions and notifications
            result = IncentiveBusinessFinder.business_ids_for_happy_hours_incentive(
                [self.ch_tz.zone]
            )
            self.assertEqual(1, len(result))
            self.assertIn(business_to_notify.business.id, result, result)

    def test_booking_ranges_from_time_of_day(self):
        biz = self.biz_setup(self.ch_tz.zone)
        tz = biz.business.get_timezone()

        datetime_to_freeze = datetime(2019, 2, 27, 6, 13, tzinfo=tz)

        expected_am_from = datetime(2019, 2, 27, tzinfo=tz)
        expected_am_till = datetime(2019, 2, 27, 11, 59, 59, 999999, tzinfo=tz)
        expected_pm_from = datetime(2019, 2, 27, 12, tzinfo=tz)
        expected_pm_till = datetime(2019, 2, 27, 23, 59, 59, 999999, tzinfo=tz)

        with freeze_time(datetime_to_freeze) as frozentime:
            bookings_from, bookings_till = IncentiveBusinessFinder.booking_ranges_from_localtime(
                tznow(self.ch_tz),
            )
            self.assertEqual(expected_am_from, bookings_from)
            self.assertEqual(expected_am_till, bookings_till)

            frozentime.tick(timedelta(hours=12))
            bookings_from, bookings_till = IncentiveBusinessFinder.booking_ranges_from_localtime(
                tznow(self.ch_tz),
            )
            self.assertEqual(expected_pm_from, bookings_from)
            self.assertEqual(expected_pm_till, bookings_till)

    def test_all_staffers_has_gaps_gap_rate_100(self):
        time_to_freeze = datetime(2019, 2, 27, 6, 13, tzinfo=self.ch_tz)
        booking_base_dt = time_to_freeze

        with freeze_time(time_to_freeze):
            biz = self.biz_setup(self.ch_tz.zone)
            tz = biz.business.get_timezone()
            search_from = datetime.combine(
                booking_base_dt,
                time.min,
            ).replace(tzinfo=tz)
            search_till = datetime.combine(
                booking_base_dt,
                time.max,
            ).replace(tzinfo=tz)

            # bookings with gap between time(10, 30), time(12, 0), time(16, 0)
            # for _time in [time(10, 30), time(16, 0), time(12, 0)]:
            _booking_times = [
                time24hour(10, 30),
                time24hour(16, 0),
                time24hour(12, 0),
            ]
            for _time in _booking_times:
                booked_from = datetime.combine(
                    booking_base_dt,
                    _time,
                ).replace(tzinfo=tz)
                booked_till = booked_from + biz.service_variants[0].duration

                booking, *_ = create_subbooking(
                    business=biz.business,
                    booking_kws=dict(
                        updated_by=biz.owner,
                        source=self.biz_booking_src,
                        type=Appointment.TYPE.BUSINESS,
                        status=Appointment.STATUS.ACCEPTED,
                        booked_from=booked_from,
                        booked_till=booked_till,
                    ),
                )
                baker.make(
                    BookingResource,
                    subbooking=booking,
                    resource=biz.staffers[0],
                )
                self.assertIsNotNone(booking)

            gap_finder = BookingsGapFinder(
                business=biz.business,
                date_from=search_from,
                date_till=search_till,
            )
            self.assertTrue(gap_finder.has_gap)

            staffers_gaps = gap_finder.staffers_gaps
            self.assertIsNotNone(staffers_gaps)
            self.assertIn(biz.staffers[0].id, gap_finder.staffers_gaps)
            self.assertEqual(100, gap_finder.gap_rate)

    def test_2_out_of_3_staffers_have_gaps_gap_rate_77(self):
        time_to_freeze = datetime(2019, 2, 27, 6, 13, tzinfo=self.ch_tz)
        booking_base_dt = time_to_freeze

        with freeze_time(time_to_freeze):
            biz = self.biz_setup(self.ch_tz.zone)
            tz = biz.business.get_timezone()
            search_from = datetime.combine(
                booking_base_dt,
                time.min,
            ).replace(tzinfo=tz)
            search_till = datetime.combine(
                booking_base_dt,
                time.max,
            ).replace(tzinfo=tz)

            # bookings with gap between time(10, 30), time(12, 0), time(16, 0)
            _booking_times = [
                time24hour(10, 30),
                time24hour(14, 0),
                time24hour(16, 0),
                time24hour(12, 0),
            ]
            for _time in _booking_times:
                booked_from = datetime.combine(
                    booking_base_dt,
                    _time,
                ).replace(tzinfo=tz)
                booked_till = booked_from + biz.service_variants[0].duration
                booking, *_ = create_subbooking(
                    business=biz.business,
                    booking_kws=dict(
                        updated_by=biz.owner,
                        source=self.biz_booking_src,
                        type=Appointment.TYPE.BUSINESS,
                        status=Appointment.STATUS.ACCEPTED,
                        booked_from=booked_from,
                        booked_till=booked_till,
                    ),
                )
                baker.make(
                    BookingResource,
                    subbooking=booking,
                    resource=biz.staffers[0],
                )
                self.assertIsNotNone(booking)

            gap_finder = BookingsGapFinder(
                business=biz.business,
                date_from=search_from,
                date_till=search_till,
            )
            self.assertTrue(gap_finder.has_gap)

            staffers_gaps = gap_finder.staffers_gaps
            self.assertIsNotNone(staffers_gaps)
            self.assertIn(biz.staffers[0].id, gap_finder.staffers_gaps)
            self.assertEqual(67, gap_finder.gap_rate)

    def test_1_out_of_3_staffers_have_gaps_gap_rate_33(self):
        time_to_freeze = datetime(2019, 2, 27, 6, 13, tzinfo=self.ch_tz)
        booking_base_dt = time_to_freeze

        with freeze_time(time_to_freeze):
            biz = self.biz_setup(self.ch_tz.zone)
            tz = biz.business.get_timezone()

            search_from = datetime.combine(
                booking_base_dt,
                time.min,
            ).replace(tzinfo=tz)
            search_till = datetime.combine(
                booking_base_dt,
                time.max,
            ).replace(tzinfo=tz)

            # bookings with gap between time(10, 30), time(12, 0), time(16, 0)
            _booking_times = [
                time24hour(10, 30),
                time24hour(14, 0),
                time24hour(16, 0),
                time24hour(12, 0),
            ]
            for _time in _booking_times:
                for staffer in biz.staffers[:2]:
                    booked_from = datetime.combine(
                        booking_base_dt,
                        _time,
                    ).replace(tzinfo=tz)
                    booked_till = booked_from + biz.service_variants[0].duration

                    booking, *_ = create_subbooking(
                        business=biz.business,
                        booking_kws=dict(
                            updated_by=biz.owner,
                            source=self.biz_booking_src,
                            type=Appointment.TYPE.BUSINESS,
                            status=Appointment.STATUS.ACCEPTED,
                            booked_from=booked_from,
                            booked_till=booked_till,
                        ),
                    )
                    baker.make(
                        BookingResource,
                        subbooking=booking,
                        resource=staffer,
                    )
                    self.assertIsNotNone(booking)

            gap_finder = BookingsGapFinder(
                business=biz.business,
                date_from=search_from,
                date_till=search_till,
            )
            self.assertTrue(gap_finder.has_gap)

            staffers_gaps = gap_finder.staffers_gaps
            self.assertIsNotNone(staffers_gaps)
            self.assertIn(biz.staffers[0].id, gap_finder.staffers_gaps)
            self.assertEqual(33, gap_finder.gap_rate)


@pytest.mark.django_db
def test_promotion_service_variant_serializer(business_with_variants):
    _, service_variants = business_with_variants()

    serializer = PromotionServiceVariantSerializer(instance=service_variants, many=True)

    data = serializer.data
    assert 'service_variant_id' in data[0]
    assert 'price' in data[0]
    assert 'name' in data[0]
    assert 'type' in data[0]
    assert 'duration' in data[0]


@pytest.mark.django_db
def test_resolver_from_instance_no_promo_after_converting_to_one_time_service():
    """No service_variant - no promotion."""
    time_to_freeze = datetime(2020, 10, 24, 19, 10, tzinfo=pytz.UTC)

    with freeze_time(time_to_freeze) as frozen_time:
        booking_source = baker.make_recipe(
            'webapps.booking.booking_source_recipe',
        )
        pos: POS = baker.make_recipe('webapps.pos.pos_recipe')
        baker.make_recipe('webapps.pos.tax_rate_recipe', pos=pos)
        baker.make_recipe('webapps.pos.payment_type_cash_recipe', pos=pos)

        service: Service = baker.make_recipe(
            'webapps.business.service_recipe',
            business=pos.business,
        )
        service_variant: ServiceVariant = baker.make_recipe(
            'webapps.business.service_variant_recipe',
            service=service,
        )
        bci = baker.make_recipe(
            'webapps.business.bci_recipe',
            business=pos.business,
            discount=Decimal('10'),
            user=baker.make_recipe('webapps.user.user_recipe'),
        )

        biz_tz = pos.business.get_timezone()
        booked_from = datetime(2020, 10, 26, 9, tzinfo=biz_tz)
        booked_till = booked_from + service_variant.duration

        hh_promotion: ServicePromotion = baker.make(
            ServicePromotion,
            business=pos.business,
            type=SERVICE_VARIANT_HAPPY_HOURS,
            active=False,
            happy_hours_week_day=get_week_day(booked_from),
            promotion_options={
                'service_variants': [
                    {
                        'hour_from': '09:00',
                        'hour_till': '11:00',
                        'discount_type': 'R',
                        'discount_amount': '10.00',
                        'service_variant_id': service_variant.id,
                    }
                ]
            },
        )
        _resolved_price = Decimal(
            service_variant.price - service_variant.price * Decimal('0.1'),
        )

        appointment: Appointment = create_subbooking(
            dict(
                updated_by=bci.user,
                booked_for=bci,
                booked_from=booked_from,
                booked_till=booked_till,
                service_variant=service_variant,
                status=Appointment.STATUS.ACCEPTED,
                resolved_promotion=hh_promotion,
                resolved_promotion_type=SERVICE_VARIANT_HAPPY_HOURS,
                resolved_discount=10,
                resolved_price=_resolved_price,
            ),
            Appointment.TYPE.CUSTOMER,
            business=pos.business,
            source=booking_source,
        )[0].appointment

        # TODO: this is wrong: no service change in this method
        appointment.update_appointment(
            subbooking=appointment.first_booking,
            updated_by=pos.business.owner,
            service_variant=None,
            service_name='No Variant',
        )

        assert not appointment.first_booking.service_variant

        frozen_time.tick(booked_till + timedelta(minutes=15) - tznow(biz_tz))

        bulk_finish_appointments(
            Appointment.objects.filter(id=appointment.id),
            Appointment.STATUS.FINISHED,
        )

        resolver = ServicePromotionResolver(pos.business)
        result = resolver.from_instance(
            appointment.first_booking,
            appointment.booked_for,
        )
        assert not result.result


@pytest.mark.parametrize(
    'booking_gap, gap_size, expected',
    [
        pytest.param(
            (time24hour(10, 0), time24hour(11, 1)),
            timedelta(minutes=60),
            True,
        ),
        pytest.param(
            (time24hour(10, 1), time24hour(11, 0)),
            timedelta(minutes=60),
            False,
        ),
    ],
)
def test_gap_check_with_time_and_time24hour(
    booking_gap: t.Tuple[t.Union[time, time24hour]],
    gap_size: timedelta,
    expected: bool,
):
    assert (
        BookingsGapFinder._is_gap_over_gap_size(
            booking_gap,
            gap_size,
        )
        is expected
    )
