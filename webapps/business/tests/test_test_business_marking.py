import pytest
from django.test import override_settings
from model_bakery import baker

from webapps.business.enums import CustomData
from webapps.business.models import Business
from webapps.business.models.business_change import BusinessChange
from webapps.user.models import User


@override_settings(LIVE_DEPLOYMENT=True)
@pytest.mark.parametrize(
    'email, in_analysis, expected',
    (
        pytest.param(
            f'2f29b0c226ff3033e02fcbe2f9f61d54@{User.DELETED_USER_DOMAIN}',
            True,
            False,
            id='non-test email',
        ),
        pytest.param(
            f'2f29b0c226ff3033e02fcbe2f9f61d54@{User.DELETED_USER_DOMAIN}',
            None,
            False,
            id='non-test email - no flag set',
        ),
        pytest.param('<EMAIL>', False, True, id='already excluded account'),
        pytest.param('<EMAIL>', True, True, id='test account'),
    ),
)
def test_is_test_business(email, in_analysis, expected):
    business = Business(
        owner=User(email=email),
        include_in_analysis=in_analysis,
    )
    assert business.is_test_business() == expected


@override_settings(LIVE_DEPLOYMENT=True)
@pytest.mark.parametrize(
    'email, in_analysis, importer, expected',
    (
        pytest.param('<EMAIL>', True, 'versum regular', False, id='included importer'),
        pytest.param('<EMAIL>', False, 'versum regular', False, id='included importer'),
        pytest.param('<EMAIL>', True, 'some other importer', False, id='excluded importer'),
        pytest.param('<EMAIL>', False, 'some other importer', True, id='excluded importer'),
        pytest.param('<EMAIL>', False, 'wosp_importer', True, id='some historical importer'),
        pytest.param(
            '<EMAIL>',
            False,
            'versum regular',
            False,
            id='versum imported business',
        ),
        pytest.param(
            '<EMAIL>',
            True,
            '',
            True,
            id='test user with no importer',
        ),
    ),
)
def test_is_test_business_importers(email, in_analysis, importer, expected):
    business = Business(
        owner=User(email=email),
        include_in_analysis=in_analysis,
        integrations={'importer': importer},
    )
    assert business.is_test_business() == expected


@override_settings(LIVE_DEPLOYMENT=True)
@pytest.mark.django_db
def test_hiding_business_business():
    business = baker.make(
        Business,
        owner=baker.make(
            'user.User', email=f'2f29b0c226ff3033e02fcbe2f9f61d54@{User.DELETED_USER_DOMAIN}'
        ),
    )
    assert BusinessChange.objects.count() == 0

    business.hide()
    business.refresh_from_db()

    assert business.visible is False
    assert business.custom_data.get(CustomData.NOINDEX) is True
    assert business.custom_data.get(CustomData.HIDDEN_IN_SEARCH) is True
    assert business.include_in_analysis is True
    assert BusinessChange.objects.count() == 1


@pytest.mark.django_db
@override_settings(LIVE_DEPLOYMENT=True)
def test_marking_test_business():
    business = baker.make(
        Business,
        owner=baker.make('user.User', email='<EMAIL>'),
    )

    business.owner.email = '<EMAIL>'
    # this will trigger mark_test_business
    business.owner.save()
    business.refresh_from_db()

    assert business.visible is False
    assert business.custom_data.get(CustomData.NOINDEX) is True
    assert business.custom_data.get(CustomData.HIDDEN_IN_SEARCH) is True
    assert business.include_in_analysis is False
    assert BusinessChange.objects.count() == 1


@override_settings(LIVE_DEPLOYMENT=True)
@pytest.mark.parametrize(
    'email, is_visible, noindex, hidden_in_search',
    (
        pytest.param(
            f'2f29b0c226ff3033e02fcbe2f9f61d54@{User.DELETED_USER_DOMAIN}',
            True,
            None,
            None,
            id='non-test email',
        ),
        pytest.param('<EMAIL>', False, True, True, id='test account'),
    ),
)
@pytest.mark.django_db
def test_test_business_visibility(email, is_visible, noindex, hidden_in_search):
    business = baker.make(Business, owner=baker.make('user.User', email=email))
    assert business.visible == is_visible
    assert business.custom_data.get(CustomData.NOINDEX) == noindex
    assert business.custom_data.get(CustomData.HIDDEN_IN_SEARCH) == hidden_in_search


@pytest.mark.django_db
@override_settings(LIVE_DEPLOYMENT=True)
def test_owner_email_updated_to_test():
    business = baker.make(
        Business,
        owner=baker.make('user.User', email='<EMAIL>'),
        include_in_analysis=True,
        visible=True,
    )
    assert business.include_in_analysis is True

    business.owner.email = '<EMAIL>'
    business.owner.save()

    business.refresh_from_db()
    assert business.include_in_analysis is False


@pytest.mark.django_db
@override_settings(LIVE_DEPLOYMENT=True)
def test_owner_changed_to_test():
    business = baker.make(
        Business,
        owner=baker.make('user.User', email='<EMAIL>'),
        include_in_analysis=True,
        visible=True,
    )
    assert business.include_in_analysis is True

    business.owner = baker.make(
        'user.User',
        email='<EMAIL>',
    )
    business.save()

    business.refresh_from_db()
    assert business.include_in_analysis is False
