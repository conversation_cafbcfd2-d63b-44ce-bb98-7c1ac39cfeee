import pytest
from mock import patch
from model_bakery import baker

from webapps.business.models import Business
from webapps.notification.base import Channel


def _create_test_business():
    return baker.make(
        Business,
        status=Business.Status.TRIAL,
        active=True,
    )


@pytest.mark.django_db
@patch(
    'lib.deeplink.generate_deeplink',
    return_value='dl.booksy.com/F7UMKGW1aN',
)
def test_get_mp_deeplink_with_cache(generate_mock):
    business = _create_test_business()
    business.integrations = {
        'branchio_business': 'dl.booksy.com/3c3cj4W1aN',
    }
    business.save()
    deeplink = business.get_mp_deeplink()
    assert deeplink == 'dl.booksy.com/3c3cj4W1aN'
    assert generate_mock.call_count == 0


@pytest.mark.django_db
@patch(
    'lib.deeplink.generate_deeplink',
    return_value='dl.booksy.com/F7UMKGW1aN',
)
def test_get_mp_deeplink_without_cache(generate_mock):
    business = _create_test_business()
    deeplink = business.get_mp_deeplink()
    assert deeplink == 'dl.booksy.com/F7UMKGW1aN'
    assert generate_mock.call_count == 1


@pytest.mark.django_db
@patch(
    'lib.deeplink.generate_deeplink',
    return_value='dl.booksy.com/F7UMKGW1aN',
)
def test_get_mp_deeplink_with_cache_and_with_analytics_data(generate_mock):
    business = _create_test_business()
    business.integrations = {
        'branchio_business': 'dl.booksy.com/3c3cj4W1aN',
    }
    business.save()
    deeplink = business.get_mp_deeplink(channel=Channel.Type.SMS)
    assert deeplink == 'dl.booksy.com/F7UMKGW1aN'
    assert generate_mock.call_count == 1


@pytest.mark.django_db
@patch(
    'lib.deeplink.generate_deeplink',
    return_value='dl.booksy.com/F7UMKGW1aN',
)
def test_get_mp_deeplink_without_cache_and_with_analytics_data(generate_mock):
    business = _create_test_business()
    deeplink = business.get_mp_deeplink(campaign='SUPER_KAMPANIA')
    assert deeplink == 'dl.booksy.com/F7UMKGW1aN'
    assert generate_mock.call_count == 1
