from datetime import datetime

from django.utils import timezone

import pytz
from dateutil.relativedelta import relativedelta
from freezegun import freeze_time
from parameterized import parameterized
from rest_framework.reverse import reverse

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from lib.feature_flag.enums import ChurnSupportEnum
from lib.feature_flag.experiment.business import CancelChurnCustomerBookingExperiment
from lib.tests.utils import override_eppo_feature_flag
from webapps.booking.baker_recipes import appointment_recipe
from webapps.booking.models import Appointment
from webapps.business.baker_recipes import business_recipe


@freeze_time(datetime(2024, 10, 11, tzinfo=pytz.UTC))
class ChurnSupportTest(BaseBusinessApiTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = business_recipe.make(
            active_from=datetime.now(pytz.UTC),
        )
        cls.user = cls.business.owner
        cls.url = reverse("churn_support", kwargs={"business_pk": cls.business.id})

    def test_flag_switched_off_more_than_10_cb(self):
        [  # pylint: disable=W0106
            appointment_recipe.make(
                business=self.business,
                type=Appointment.TYPE.CUSTOMER,
                created=timezone.now() - relativedelta(months=1),
            )
            for _ in range(12)
        ]
        with override_eppo_feature_flag({CancelChurnCustomerBookingExperiment.flag_name: ""}):
            response = self.client.get(self.url)
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json(), {"path_variant": None})

    def test_flag_switched_on_equal_10_cb(self):
        [  # pylint: disable=W0106
            appointment_recipe.make(
                business=self.business,
                type=Appointment.TYPE.CUSTOMER,
                created=timezone.now() - relativedelta(months=1),
            )
            for _ in range(11)
        ]
        with override_eppo_feature_flag({CancelChurnCustomerBookingExperiment.flag_name: ""}):
            response = self.client.get(self.url)
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json(), {"path_variant": None})

    def test_flag_switched_on_less_than_10_cb(self):
        [  # pylint: disable=W0106
            appointment_recipe.make(
                business=self.business,
                type=Appointment.TYPE.CUSTOMER,
                created=timezone.now() - relativedelta(months=1),
            )
            for _ in range(10)
        ]
        with override_eppo_feature_flag(
            {CancelChurnCustomerBookingExperiment.flag_name: ChurnSupportEnum.SCHEDULE.value}
        ):
            response = self.client.get(self.url)
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json(), {"path_variant": None})

    @parameterized.expand(
        [
            (Appointment.TYPE.CUSTOMER, ChurnSupportEnum.SCHEDULE.value),
            (Appointment.TYPE.BUSINESS, None),
        ]
    )
    def test_flag_switched_on_more_than_10_cb(self, appointment_type, mocked_path_variant):
        [  # pylint: disable=W0106
            appointment_recipe.make(
                business=self.business,
                type=appointment_type,
                created=timezone.now() - relativedelta(months=1),
            )
            for _ in range(12)
        ]
        with override_eppo_feature_flag(
            {CancelChurnCustomerBookingExperiment.flag_name: ChurnSupportEnum.SCHEDULE.value}
        ):
            response = self.client.get(self.url)
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json(), {"path_variant": mocked_path_variant})
