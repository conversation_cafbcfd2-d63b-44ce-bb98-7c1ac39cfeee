# Generated by Django 3.1.9 on 2021-05-17 11:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('printer_api', '0004_printerconfig_version_app'),
    ]

    operations = [
        migrations.AlterField(
            model_name='printerconfig',
            name='data_bits',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (6, 'DATA_BITS_6'),
                    (7, 'DATA_BITS_7'),
                    (8, 'DATA_BITS_8'),
                ],
                default=8,
                verbose_name='Data Bits',
            ),
        ),
        migrations.AlterField(
            model_name='printerconfig',
            name='flow_control',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, 'FLOW_CONTROL_NONE'),
                    (1, 'FLOW_CONTROL_RTSCTS_IN'),
                    (2, 'FLOW_CONTROL_RTSCTS_OUT'),
                    (4, 'FLOW_CONTROL_XONXOFF_IN'),
                    (8, 'FLOW_CONTROL_XONXOFF_OUT'),
                ],
                default=0,
                verbose_name='Flow control',
            ),
        ),
        migrations.AlterField(
            model_name='printerconfig',
            name='protocol',
            field=models.CharField(
                choices=[
                    ('File', 'FILE'),
                    ('Console', 'CONSOLE'),
                    ('DFEmul', 'DFE_MUL'),
                    ('ElzabMera', 'ELZAB_MERA'),
                    ('ElzabOmega2', 'ELZAB_OMEGA_2'),
                    ('InnovaProfit451', 'INNOVA_PROFIT_451'),
                    ('Novitus', 'NOVITUS'),
                    ('OptimusVivo', 'OPTIMUS_VIVO'),
                    ('Posnet101', 'POSTNET_101'),
                    ('Thermal101', 'THERMAL_101'),
                    ('Thermal203', 'THERMAL_203'),
                    ('Thermal301', 'THERMAL_301'),
                    ('ThermalOld', 'THERMAL_OLD'),
                ],
                max_length=100,
                verbose_name='Protocol Name',
            ),
        ),
        migrations.AlterField(
            model_name='printerconfig',
            name='stop_bits',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, 'STOP_BITS_1'),
                    (3, 'STOP_BITS_1_5'),
                    (2, 'STOP_BITS_2'),
                ],
                default=1,
                verbose_name='Stop Bits',
            ),
        ),
    ]
