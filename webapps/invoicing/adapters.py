# pylint: disable=cyclic-import
import typing
from decimal import Decimal

from lib.tools import major_unit


def _get_lang(business) -> str:
    from webapps.marketing.ports import _get_lang_port

    return _get_lang_port(business)


def get_operation_type(document_type: str) -> str:
    from webapps.invoicing.models import CashRegisterDocumentType
    from webapps.register.models import RegisterOperation

    document_types_mapping: typing.Dict[str, str] = {
        CashRegisterDocumentType.KW: RegisterOperation.REGISTER_OPERATION_TYPES__CASH_OUT,
        CashRegisterDocumentType.KP: RegisterOperation.REGISTER_OPERATION_TYPES__CASH_IN,
    }
    if document_type not in document_types_mapping:
        raise RuntimeError(  # pylint: disable=broad-exception-raised
            (
                f'Cash register document type {document_type} has no mapping to register '
                'operation type defined'
            )
        )
    return document_types_mapping.get(document_type)


def create_register_operation(
    register_id: int,
    amount: int,  # minor units
    document_type: str,
    operator_user_id: int,
    note: str = None,
) -> int:
    from webapps.register.ports import create_register_operation_port

    operation_type = get_operation_type(document_type)

    return create_register_operation_port(
        register_id,
        major_unit(amount),
        operation_type,
        operator_user_id,
        note,
    )


def update_register_operation(
    register_operation_id: int,
    amount: Decimal,  # major units
    note: str = None,
) -> None:
    from webapps.register.ports import update_register_operation_port

    return update_register_operation_port(
        register_operation_id,
        amount,
        note,
    )


def taxable_company_change_event_adapter(
    business_id: int,
    changed_field: str,
    operator_id: int,
) -> None:
    from webapps.french_certification.ports import JETPort

    JETPort.taxable_company_change_event(
        business_id=business_id,
        changed_field=changed_field,
        operator_id=operator_id,
    )


def business_has_fiscal_receipts(business_id: int) -> bool:
    from webapps.french_certification.ports import FiscalReceiptPort

    return FiscalReceiptPort.business_has_fiscal_receipts(business_id=business_id)
