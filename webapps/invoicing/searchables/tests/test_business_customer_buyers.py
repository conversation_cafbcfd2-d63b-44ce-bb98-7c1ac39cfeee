# pylint: disable=redefined-outer-name, unused-import
import pytest

from lib.elasticsearch.consts import ESDocType
from webapps.business.elasticsearch.business_customer import (
    BusinessCustomerDocument,
)
from webapps.business.searchables.serializers import CustomerBuyerHitSerializer
from webapps.invoicing.searchables.buyers import (
    BCIBuyersSearchable,
)
from webapps.invoicing.serializers.buyer import CustomerBuyerFlattenSerializer

# pylint: disable=unused-import
# used by  prepare_invoicing_buyers_data
from webapps.business.conftest import create_business_customer_index_function

# pylint: disable=enable-import

BUSINESS_ID = 1
merged_data_keys = (
    'first_name',
    'last_name',
    'full_name',
    'email',
    'cell_phone',
    'zipcode',
)
invoicing_buyers_keys = (
    'id',
    'name',
    'tax_id_number',
    'address',
    'zip_code',
    'city',
    'state',
    'country',
    'email',
)

data = (
    (
        ('fn1', 'ln1', 'fn1 ln1', '<EMAIL>', '123', '111'),
        (
            (1, 'Comp_1', 'T1', 'A1', '1', 'C1', 's1', 'us', '<EMAIL>'),
            (2, 'Comp_2', 'T2', 'A2', '2', 'C1', 's1', 'us', '<EMAIL>'),
        ),
    ),
    (
        ('fn2', 'ln2', 'fn2 ln2', '<EMAIL>', '234', '111'),
        (
            (3, 'Comp_1', 'T1', 'A1', '1', 'C1', 's1', 'us', '<EMAIL>'),
            (4, 'Comp_3', 'T3', 'A3', '3', 'C3', 's3', 'us', '<EMAIL>'),
        ),
    ),
    (
        ('fn3', 'ln3', 'fn3 ln3', '<EMAIL>', '345', '222'),
        (
            (5, 'Comp_4', 'T4', 'A4', '4', 'C4', 's4', 'us', '<EMAIL>'),
            (6, 'Comp_5', 'T5', 'A5', '5', 'C5', 's5', 'us', '<EMAIL>'),
        ),
    ),
)


@pytest.fixture(name='buyers_data')
def prepare_invoicing_buyers_data(create_business_customer_index_function):  # NOQA
    index = create_business_customer_index_function

    for idx, (merged_data, buyers_data) in enumerate(data, 1):
        BusinessCustomerDocument(
            id=idx,
            _routing=BUSINESS_ID,
            merged_data=dict(
                business=BUSINESS_ID,
                visible_in_business=True,
                **dict(zip(merged_data_keys, merged_data)),
            ),
            invoicing_buyers=[
                dict(zip(invoicing_buyers_keys, buyer_data)) for buyer_data in buyers_data
            ],
        ).save()

    index.refresh()
    yield


@pytest.fixture(name='buyers_searchable')
def make_customers_buyers_searchable(buyers_data):
    yield BCIBuyersSearchable(
        ESDocType.BUSINESS_CUSTOMER,
        serializer=CustomerBuyerHitSerializer(),
    ).search(
        dict(
            buyers_query='Comp_1',
        )
    )


def test_buyers_searchable(buyers_searchable):
    result = buyers_searchable.execute()
    assert result.hits.total.value == 2


def test_buyers_flatten_serializer(buyers_searchable):
    result = buyers_searchable.execute()
    serializer = CustomerBuyerFlattenSerializer(result.hits, many=True)
    assert len(serializer.data) == 4
