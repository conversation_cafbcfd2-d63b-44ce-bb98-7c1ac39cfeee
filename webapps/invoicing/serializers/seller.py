from abc import ABC, abstractmethod

from django.db import transaction
from django_countries.serializer_fields import Country<PERSON>ield
from rest_framework import serializers
from lib.french_certification.utils import (
    extended_tax_info_enabled,
    french_certification_enabled,
)
from webapps.invoicing.adapters import (
    business_has_fiscal_receipts,
    taxable_company_change_event_adapter,
)
from webapps.invoicing.consts import (
    SELLER_SERIALIZER_FIELDS,
    SELLER_SERIALIZER_OPTIONAL_FIELDS,
    FC_REQUIRED_SELLER_FIELDS,
    FC_SELLER_SERIALIZER_FIELDS,
    FC_CHANGE_MONITORED_FIELDS,
    FC_CONSTANT_SELLER_FIELDS_MAPPING,
)
from webapps.invoicing.models import Seller


class SellerSerializer(serializers.ModelSerializer):
    country = CountryField(required=True)

    class Meta:
        model = Seller
        fields = SELLER_SERIALIZER_FIELDS
        optional_fields = SELLER_SERIALIZER_OPTIONAL_FIELDS


class FCSellerSerializer(SellerSerializer):

    class Meta:
        model = SellerSerializer.Meta.model
        fields = FC_SELLER_SERIALIZER_FIELDS
        extra_kwargs = dict.fromkeys(
            FC_REQUIRED_SELLER_FIELDS,
            {
                "required": True,
                "allow_null": False,
                "allow_blank": False,
            },
        )

    def create(self, validated_data):
        validated_data.update(FC_CONSTANT_SELLER_FIELDS_MAPPING)
        seller = super().create(validated_data=validated_data)
        return seller

    def validate(self, attrs):
        data = super().validate(attrs)
        if not self.edit_permitted(data['business'].id):
            raise serializers.ValidationError(
                {
                    'non_field_errors': ['Edit seller data not permitted'],
                }
            )
        return data

    @transaction.atomic
    def update(self, instance, validated_data):
        self._detect_changed_fields(instance, validated_data)
        return super().update(instance, validated_data)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['edit_permitted'] = self.edit_permitted(instance.business_id)
        return data

    def _detect_changed_fields(self, instance, validated_data):
        changed_fields = [
            field
            for field in FC_CHANGE_MONITORED_FIELDS
            if validated_data[field] != getattr(instance, field)
        ]
        for changed_field in changed_fields:
            taxable_company_change_event_adapter(
                business_id=instance.business_id,
                changed_field=changed_field,
                operator_id=self.context['operator_id'],
            )

    @staticmethod
    def edit_permitted(business_id):
        return not business_has_fiscal_receipts(business_id)


class AbstractSellerSerializerFactory(ABC):

    @abstractmethod
    def get_seller_serializer(self, *args, **kwargs): ...


class SellerSerializerFactory(AbstractSellerSerializerFactory):
    def get_seller_serializer(self, *args, **kwargs):
        return SellerSerializer(*args, **kwargs)


class FCSellerSerializerFactory(AbstractSellerSerializerFactory):
    def get_seller_serializer(self, *args, **kwargs):
        return FCSellerSerializer(*args, **kwargs)


def get_seller_serializer_factory(business_id):
    if french_certification_enabled(business_id) or extended_tax_info_enabled(business_id):
        return FCSellerSerializerFactory()
    return SellerSerializerFactory()
