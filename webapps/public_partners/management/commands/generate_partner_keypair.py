import json
import os

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from django.conf import settings
from django.core.management.base import BaseCommand
from jwt.algorithms import RSAAlgorithm
from webapps.public_partners.models import PublicBooksyPartner


def _write_to_file(path, body):
    with open(path, 'w') as _f:
        _f.write(body)


class Command(BaseCommand):
    help = 'Creates key-pair for Booksy '

    def __init__(self, *args, **kwargs):
        self._path_to_dir = None
        super().__init__(*args, **kwargs)

    def add_arguments(self, parser):
        path_to_dir = os.path.join(
            settings.PROJECT_PATH,
            'webapps/public_partners/data',
        )
        parser.add_argument('--partner-name', type=str, required=True, help='Partner Name ')
        parser.add_argument(
            '--path',
            type=str,
            help='Path to dir ',
            default=path_to_dir,
        )
        parser.add_argument('--key_size', type=int, default=4096, help='Size of key')

    def handle(self, *args, **options):
        public_exponent = 65537
        name = options['partner_name']
        self._path_to_dir = options.get('path')
        key_size = options.get('key_size')
        key = rsa.generate_private_key(
            backend=default_backend(),
            public_exponent=public_exponent,
            key_size=key_size,
        )
        self._write_public_key(key, name)
        self._write_private_key(key, name)

    def _write_public_key(self, key, name):
        # get public key in PEM format
        public_key = key.public_key().public_bytes(
            serialization.Encoding.PEM,
            serialization.PublicFormat.SubjectPublicKeyInfo,
        )
        public_key_path = os.path.join(self._path_to_dir, f'{name}_rsa.pub')
        # algorithm RS256 == SHA256
        rsa_algorithm = RSAAlgorithm(RSAAlgorithm.SHA256)
        public_key_jwk = json.loads(rsa_algorithm.to_jwk(key.public_key()))
        partner = PublicBooksyPartner(name=name, public_jwk=public_key_jwk)
        partner.save()
        _write_to_file(
            public_key_path,
            public_key.decode('utf-8'),
        )

    def _write_private_key(self, key, name):
        # get private key in PEM container format
        private_key = key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.TraditionalOpenSSL,
            encryption_algorithm=serialization.NoEncryption(),
        )
        private_key_path = os.path.join(self._path_to_dir, f'{name}_rsa.private')
        _write_to_file(private_key_path, private_key.decode('utf-8'))
