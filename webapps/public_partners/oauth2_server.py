import json

from oauthlib.oauth2 import (
    UnsupportedGrantTypeError,
    InvalidRequestError,
    InvalidClientError,
    OAuth2Error,
)
from oauthlib.oauth2.rfc6749.endpoints import (
    AuthorizationEndpoint,
    IntrospectEndpoint,
    ResourceEndpoint,
    RevocationEndpoint,
    TokenEndpoint,
)
from oauthlib.oauth2.rfc6749.grant_types import (
    AuthorizationCodeGrant as OAuth2AuthorizationCodeGrant,
    ClientCredentialsGrant,
    ImplicitGrant as OAuth2ImplicitGrant,
    RefreshTokenGrant,
    ResourceOwnerPasswordCredentialsGrant,
)
from oauthlib.oauth2.rfc6749.grant_types.base import GrantTypeBase
from oauthlib.oauth2.rfc6749.tokens import BearerToken

from oauthlib.openid import UserInfoEndpoint
from oauthlib.openid.connect.core.grant_types import (
    AuthorizationCodeGrant,
    ImplicitGrant,
    HybridGrant,
    AuthorizationCodeGrantDispatcher,
    ImplicitTokenGrantDispatcher,
    AuthorizationTokenGrantDispatcher,
)
from oauthlib.openid.connect.core.tokens import JWTToken

from webapps.public_partners.models import OAuth2Application


class JWTAssertionGrant(GrantTypeBase):
    refresh_token = False

    def create_authorization_response(self, request, token_handler):
        pass

    def create_token_response(self, request, token_handler):
        headers = self._get_default_headers()
        try:
            self.validate_token_request(request)
        except OAuth2Error as e:
            headers.update(e.headers)
            return headers, e.json, e.status_code

        token = token_handler.create_token(request, self.refresh_token)

        for modifier in self._token_modifiers:
            token = modifier(token)

        self.request_validator.save_token(token, request)

        return headers, json.dumps(token), 200

    def validate_token_request(self, request):
        for validator in self.custom_validators.pre_token:
            validator(request)

        for param in (
            'grant_type',
            'assertion',
        ):
            if not getattr(request, param, None):
                raise InvalidRequestError(
                    f'Request is missing {param} parameter.',
                    request=request,
                )

        for param in ('grant_type', 'assertion', 'scope'):
            if param in request.duplicate_params:
                raise InvalidRequestError(
                    description=f'Duplicate {param} parameter.',
                    request=request,
                )

        if not request.grant_type == OAuth2Application.GRANT_JWT_BEARER:
            raise UnsupportedGrantTypeError(request=request)

        if not self.request_validator.validate_jwt_assertion_bearer_token(request):
            raise InvalidClientError(request=request)

        self.validate_grant_type(request)
        self.validate_scopes(request)

        for validator in self.custom_validators.post_token:
            validator(request)


class OAuth2Server(
    AuthorizationEndpoint,
    IntrospectEndpoint,
    TokenEndpoint,
    ResourceEndpoint,
    RevocationEndpoint,
    UserInfoEndpoint,
):  # pylint: disable=too-many-instance-attributes
    def __init__(
        self,
        request_validator,
        token_expires_in=None,
        token_generator=None,
        refresh_token_generator=None,
        *args,
        **kwargs,
    ):  # pylint: disable=keyword-arg-before-vararg
        """Construct a new all-grants-in-one server.

        :param request_validator: An implementation of
                                  oauthlib.oauth2.RequestValidator.
        :param token_expires_in: An int or a function to generate a token
                                 expiration offset (in seconds) given a
                                 oauthlib.common.Request object.
        :param token_generator: A function to generate a token from a request.
        :param refresh_token_generator: A function to generate a token from a
                                        request for the refresh token.
        :param kwargs: Extra parameters to pass to authorization-,
                       token-, resource-, and revocation-endpoint constructors.
        """
        self.auth_grant = OAuth2AuthorizationCodeGrant(request_validator)
        self.implicit_grant = OAuth2ImplicitGrant(request_validator)
        self.password_grant = ResourceOwnerPasswordCredentialsGrant(request_validator)
        self.credentials_grant = ClientCredentialsGrant(request_validator)
        self.refresh_grant = RefreshTokenGrant(request_validator)
        self.openid_connect_auth = AuthorizationCodeGrant(request_validator)
        self.openid_connect_implicit = ImplicitGrant(request_validator)
        self.openid_connect_hybrid = HybridGrant(request_validator)
        self.jwt_assertion_grant = JWTAssertionGrant(request_validator)

        self.bearer = BearerToken(
            request_validator,
            token_generator,
            token_expires_in,
            refresh_token_generator,
        )

        self.jwt = JWTToken(
            request_validator,
            token_generator,
            token_expires_in,
            refresh_token_generator,
        )

        self.auth_grant_choice = AuthorizationCodeGrantDispatcher(
            default_grant=self.auth_grant,
            oidc_grant=self.openid_connect_auth,
        )
        self.implicit_grant_choice = ImplicitTokenGrantDispatcher(
            default_grant=self.implicit_grant,
            oidc_grant=self.openid_connect_implicit,
        )

        AuthorizationEndpoint.__init__(
            self,
            default_response_type='code',
            response_types={
                'code': self.auth_grant_choice,
                'token': self.implicit_grant_choice,
                'id_token': self.openid_connect_implicit,
                'id_token token': self.openid_connect_implicit,
                'code token': self.openid_connect_hybrid,
                'code id_token': self.openid_connect_hybrid,
                'code id_token token': self.openid_connect_hybrid,
                'none': self.auth_grant,
            },
            default_token_type=self.bearer,
        )

        self.token_grant_choice = AuthorizationTokenGrantDispatcher(
            request_validator,
            default_grant=self.auth_grant,
            oidc_grant=self.openid_connect_auth,
        )

        TokenEndpoint.__init__(
            self,
            default_grant_type='authorization_code',
            grant_types={
                'authorization_code': self.token_grant_choice,
                'password': self.password_grant,
                'client_credentials': self.credentials_grant,
                'refresh_token': self.refresh_grant,
                'urn:ietf:params:oauth:grant-type:jwt-bearer': self.jwt_assertion_grant,
            },
            default_token_type=self.bearer,
        )
        ResourceEndpoint.__init__(
            self,
            default_token='Bearer',
            token_types={'Bearer': self.bearer, 'JWT': self.jwt},
        )
        RevocationEndpoint.__init__(self, request_validator)
        IntrospectEndpoint.__init__(self, request_validator)
        UserInfoEndpoint.__init__(self, request_validator)
