from rest_framework.pagination import (
    LimitOffsetPagination,
    PageNumberPagination,
)
from rest_framework.response import Response


class PAPageNumberPagination(PageNumberPagination):
    page_query_param = 'page'
    page_size = 100
    page_size_query_param = 'page_size'
    max_page_size = 1000


class PaginationV01(LimitOffsetPagination):
    default_limit = 200
    max_limit = 200

    def get_paginated_response(self, data):
        return Response(data)


class PaginationV02(LimitOffsetPagination):
    default_limit = 200
    max_limit = 200
