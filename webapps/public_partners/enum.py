from django.utils.translation import gettext_lazy as _

from lib.enums import StrEnum, StrChoicesEnum
from webapps.booking.enums import BookingAction
from webapps.notification.enums import NotificationTarget


class BasePublicAPIVersionEnum(StrChoicesEnum):
    @classmethod
    def allowed_versions(cls):
        """Return set of allowed versions"""
        return {value for value, _label in cls.choices()}

    @classmethod
    def choices(cls):
        visited = set()
        for label, value in super().choices():
            if value not in visited:
                # do not return DEFAULT VERSION
                visited.add(value)
                yield label, value


class PublicAPIVersionEnum(BasePublicAPIVersionEnum):
    V01 = '0.1', 'Version 0.1'
    V02 = '0.2', 'Version 0.2'
    V03 = '0.3', 'Version 0.3'
    V04 = '0.4', 'Version 0.4'
    V05 = '0.5', 'Version 0.5'
    V06 = '0.6', 'Version 0.6'
    DEFAULT = V05
    # TODO possible equal operation

    @classmethod
    def partner_versions(cls):
        for value, label in super().choices():
            if value in (
                PublicAPIVersionEnum.V01,
                PublicAPIVersionEnum.V02,
                PublicAPIVersionEnum.V03,
            ):
                yield value, label

    @classmethod
    def oauth2_versions(cls):
        for value, label in super().choices():
            if value in (
                PublicAPIVersionEnum.V04,
                PublicAPIVersionEnum.V05,
                PublicAPIVersionEnum.V06,
            ):
                yield value, label


class SubdomainStatus(StrEnum):
    TAKEN = 'taken'
    AVAILABLE = 'available'
    INCORRECT = 'incorrect'


class OAuth2InstallationStatusEnum(StrChoicesEnum):
    ACTIVE = 'A', 'Active'
    PAID = 'P', 'Paid'
    PENDING_CANCELLATION = 'E', 'Pending Cancellation'
    OVERDUE_BLOCKED = 'O', 'Overdue Blocked'
    CHURNED = 'C', 'Churned'
    SUSPENDED = 'S', 'Suspended'
    DISABLED = 'D', 'Disabled'
    BLOCKED = 'B', 'Blocked'


class UserNotificationEnum(StrChoicesEnum):
    EMAIL = 'E', 'email'
    PUSH = 'P', 'push'
    SMS = 'S', 'sms'
    POPUP = 'O', 'popup'


class NotificationRecipientEnum(StrEnum):
    STAFFER = 'staffer'
    CUSTOMER = 'customer'


class NotificationTargetEnum(StrEnum):
    CALENDAR = NotificationTarget.CALENDAR.value
    APPOINTMENT_DETAILS = NotificationTarget.BOOKING.value
    CUSTOMER_DETAILS = NotificationTarget.CUSTOMER_CARD.value
    DEEPLINK = NotificationTarget.DEEPLINK.value
    PARTNER_APP_SETUP = NotificationTarget.PARTNER_APP_SETUP.value
    PARTNER_APP_DASHBOARD = NotificationTarget.PARTNER_APP_DASHBOARD.value
    PARTNER_APP_ACTION = NotificationTarget.PARTNER_APP_ACTION.value


class AppointmentStatusAction(StrEnum):
    ACCEPT = BookingAction.ACCEPT  # customer
    CONFIRM = BookingAction.CONFIRM  # business
    DECLINE = BookingAction.DECLINE  # business
    FINISH = 'finish'  # business
    NO_SHOW = BookingAction.NO_SHOW  # business
    CANCEL = BookingAction.CANCEL  # customer & business


class ServiceKindEnum(StrEnum):
    BASIC = 'basic'
    MEDICAL = 'medical'


class ResourceKindEnum(StrEnum):
    BASIC = 'basic'
    MEDICAL = 'medical'


class BusinessCustomerInfoKindEnum(StrEnum):
    BASIC = 'basic'
    MEDICAL = 'medical'


class SubbookingKindEnum(StrEnum):
    BASIC = 'basic'
    MEDICAL = 'medical'


class SyncStatusEnum(StrEnum):
    INITIAL = 'initial'
    PENDING = 'pending'
    FINISHED = 'finished'
    OVERDUE = 'overdue'


class CallToActionEnum(StrEnum):
    SIDEBAR = 'sidebar'
    APPOINTMENT_DETAILS = 'appointment_details'
    RESOURCE_DETAILS = 'resource_details'
    SERVICE_DETAILS = 'service_details'
    CUSTOMER_DETAILS = 'customer_details'


class CallToActionTargetEnum(StrEnum):
    BLANK = 'blank'
    SELF = 'self'
    IFRAME = 'iframe'


class ApplicationOwnership(StrEnum):
    BUSINESS = 'business'
    USER = 'user'


class WebhookEnum(StrChoicesEnum):
    APPLICATION_INSTALLATION_CREATED = (
        'application_installation:created',
        'Application installation created',
    )
    APPLICATION_INSTALLATION_UPDATED = (
        'application_installation:updated',
        'Application installation updated',
    )
    APPLICATION_INSTALLATION_DELETED = (
        'application_installation:deleted',
        'Application installation deleted',
    )

    BUSINESS_CREATED = 'business:created', 'Business created'
    BUSINESS_UPDATED = 'business:updated', 'Business updated'
    BUSINESS_DELETED = 'business:deleted', 'Business deleted'

    RESOURCE_CREATED = 'resource:created', 'Resource created'
    RESOURCE_UPDATED = 'resource:updated', 'Resource updated'
    RESOURCE_DELETED = 'resource:deleted', 'Resource deleted'

    SERVICE_CREATED = 'service:created', 'Service created'
    SERVICE_UPDATED = 'service:updated', 'Service updated'
    SERVICE_DELETED = 'service:deleted', 'Service deleted'

    CONSENT_FORM_CREATED = 'consent_form:created', 'Consent form created'
    CONSENT_FORM_UPDATED = 'consent_form:updated', 'Consent form updated'
    CONSENT_FORM_DELETED = 'consent_form:deleted', 'Consent form deleted'

    CONSENT_CREATED = 'consent:created', 'Consent created'
    CONSENT_UPDATED = 'consent:updated', 'Consent updated'
    CONSENT_DELETED = 'consent:deleted', 'Consent deleted'

    IMAGE_CREATED = 'image:created', 'Image created'
    IMAGE_UPDATED = 'image:updated', 'Image updated'
    IMAGE_DELETED = 'image:deleted', 'Image deleted'

    CUSTOMER_CREATED = 'customer:created', 'Customer created'
    CUSTOMER_UPDATED = 'customer:updated', 'Customer updated'
    CUSTOMER_DELETED = 'customer:deleted', 'Customer deleted'

    APPOINTMENT_CREATED = 'appointment:created', 'Appointment created'
    APPOINTMENT_UPDATED = 'appointment:updated', 'Appointment updated'
    APPOINTMENT_DELETED = 'appointment:deleted', 'Appointment deleted'

    CUSTOMER_CLAIM_LOG_CREATED = 'customer_claim_log:created', 'Customer claim log created'
    CUSTOMER_CLAIM_LOG_UPDATED = 'customer_claim_log:updated', 'Customer claim log updated'
    CUSTOMER_CLAIM_LOG_DELETED = 'customer_claim_log:deleted', 'Customer claim log deleted'


class WebhookHMACDigestEnum(StrChoicesEnum):
    SHA512 = 'sha512', 'SHA512'
    SHA256 = 'sha256', 'SHA256'
    SHA1 = 'sha1', 'SHA1'


class BillingSubscriptionStatus(StrChoicesEnum):
    PENDING = 'P', _('Pending')
    ACTIVE = 'A', _('Active')
    CLOSED = 'C', _('Closed')
    BLOCKED = 'B', _('Blocked')
    SUSPENDED = 'S', _('Suspended')
    SUSPENSION_PENDING = 'U', _('Suspension pending')


class OAuth2ApplicationCategoryEnum(StrEnum):
    MEDICAL = 'Medical'
