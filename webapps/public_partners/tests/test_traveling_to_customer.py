from django.test import override_settings
from rest_framework import status

from lib.test_utils import compare_expected_fields
from webapps.business.baker_recipes import traveling_to_client_recipe
from webapps.business.enums import PriceType
from webapps.business.models import TravelingToClients
from webapps.public_partners.tests import FirewallTestCaseMixin, PublicApiBaseTestCase


class TravelingToCustomerTestCase(FirewallTestCaseMixin, PublicApiBaseTestCase):
    _url = '/public-api/us/business/{}/traveling_to_customer/'
    _details_url = '/public-api/us/business/{}/traveling_to_customer/{}/'
    _VERSION = '0.2'
    TRAVELING_TO_CUSTOMER_FIELDS = {
        'business_id',
        'distance',
        'distance_unit',
        'hide_address',
        'id',
        'policy',
        'price',
        'price_type',
        'traveling_only',
    }

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        with override_settings(COUNTRY_DISTANCE_UNIT='mi'):
            cls.traveling_to_customer = traveling_to_client_recipe.make(business=cls.business)

    def get_url(self, business_id=None):
        return self._url.format(business_id or self.business.id)

    def get_sample_data(self, **kwargs):
        sample_data = {
            'distance': 20,
            'distance_unit': 'km',
            'hide_address': True,
            'price': 3.33,
            'price_type': PriceType.FIXED,
            'policy': 'lorem ipsum',
            'traveling_only': True,
        }
        return {**sample_data, **kwargs}

    def basic_response_for_firewall_testing(self):
        return self.get(self.get_url())

    def test_check_not_allowed_methods(self):
        self.check_not_allowed_methods(self.get_url(), {'get', 'put', 'patch', 'delete'})

    def test_retrieve_success(self):
        resp = self.get(self.get_url())

        assert resp.status_code == status.HTTP_200_OK
        body = resp.json()
        assert body['id'] == self.traveling_to_customer.id

    def test_retrieve_failure_with_deleted_traveling_to_customer(self):
        self.business.traveling.soft_delete()
        resp = self.get(self.get_url())

        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_retrieve_failure_without_traveling_to_customer(self):
        self.business.traveling.delete()
        resp = self.get(self.get_url())

        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_create_success(self):
        self.business.traveling.delete()
        expected_data = self.get_sample_data()

        resp = self.put(self.get_url(business_id=self.business.id), data=expected_data)

        assert resp.status_code == status.HTTP_200_OK
        body = resp.json()
        compare_expected_fields(body.keys(), self.TRAVELING_TO_CUSTOMER_FIELDS)
        assert body['distance'] == expected_data['distance']
        assert body['distance_unit'] == expected_data['distance_unit']
        assert body['hide_address'] == expected_data['hide_address']
        assert body['policy'] == expected_data['policy']
        assert body['price'] == str(expected_data['price'])
        assert body['price_type'] == expected_data['price_type']
        assert body['traveling_only'] == expected_data['traveling_only']
        assert body['business_id'] == self.business.id

    def test_create_success_with_deleted(self):
        self.business.traveling.soft_delete()
        expected_data = self.get_sample_data()

        resp = self.put(self.get_url(business_id=self.business.id), data=expected_data)

        assert resp.status_code == status.HTTP_200_OK
        body = resp.json()
        compare_expected_fields(body.keys(), self.TRAVELING_TO_CUSTOMER_FIELDS)
        assert body['id'] == self.traveling_to_customer.id
        assert body['distance'] == expected_data['distance']
        assert body['distance_unit'] == expected_data['distance_unit']
        assert body['hide_address'] == expected_data['hide_address']
        assert body['policy'] == expected_data['policy']
        assert body['price'] == str(expected_data['price'])
        assert body['price_type'] == expected_data['price_type']
        assert body['traveling_only'] == expected_data['traveling_only']
        assert body['business_id'] == self.business.id
        assert TravelingToClients.all_objects.count() == 1

    def test_update_success(self):
        expected_data = self.get_sample_data()
        assert self.traveling_to_customer.distance != expected_data['distance']
        assert self.traveling_to_customer.hide_address != expected_data['hide_address']
        assert self.traveling_to_customer.policy != expected_data['policy']
        assert self.traveling_to_customer.price != str(expected_data['price'])
        assert self.traveling_to_customer.price_type != expected_data['price_type']
        assert self.traveling_to_customer.traveling_only != expected_data['traveling_only']
        assert self.traveling_to_customer.distance_unit != expected_data['distance_unit']

        resp = self.put(self.get_url(), data=self.get_sample_data())

        assert resp.status_code == status.HTTP_200_OK
        body = resp.json()
        compare_expected_fields(body.keys(), self.TRAVELING_TO_CUSTOMER_FIELDS)
        assert body['distance'] == expected_data['distance']
        assert body['distance_unit'] == expected_data['distance_unit']
        assert body['hide_address'] == expected_data['hide_address']
        assert body['policy'] == expected_data['policy']
        assert body['price'] == str(expected_data['price'])
        assert body['price_type'] == expected_data['price_type']
        assert body['traveling_only'] == expected_data['traveling_only']

    def test_update_success_with_default_data(self):
        resp = self.put(self.get_url(), data={})

        assert resp.status_code == status.HTTP_200_OK
        result_data = resp.json()
        assert result_data['business_id'] == self.business.id
        assert result_data['id']
        assert result_data['distance'] == 15
        assert result_data['hide_address'] is False
        assert result_data['price'] is None
        assert result_data['distance_unit'] == 'mi'
        assert result_data['price_type'] == PriceType.FREE
        assert result_data['policy'] == ''
        assert result_data['traveling_only'] is False

    def test_update_failure_with_wrong_distance_values(self):
        sample_data = self.get_sample_data()

        with self.subTest('wrong data type'):
            sample_data['distance'] = 'er'

            resp = self.put(self.get_url(), data=sample_data)

            assert resp.status_code == status.HTTP_400_BAD_REQUEST
            result_data = resp.json()
            assert result_data == dict(distance=['A valid integer is required.'])

            sample_data = self.get_sample_data()
            sample_data['distance'] = 0.5

            resp = self.put(self.get_url(), data=sample_data)

            assert resp.status_code == status.HTTP_400_BAD_REQUEST
            result_data = resp.json()
            assert result_data == dict(distance=['A valid integer is required.'])

        with self.subTest('missing data'):
            sample_data = self.get_sample_data()
            sample_data['distance'] = None

            resp = self.put(self.get_url(), data=sample_data)

            assert resp.status_code == status.HTTP_400_BAD_REQUEST
            result_data = resp.json()
            assert result_data == dict(distance=['This field may not be null.'])

        with self.subTest('value out of limit'):
            sample_data = self.get_sample_data()
            sample_data['distance'] = 33333333

            resp = self.put(self.get_url(), data=sample_data)

            assert resp.status_code == status.HTTP_400_BAD_REQUEST
            result_data = resp.json()
            assert result_data == dict(
                distance=['Ensure this value is less than or equal to 32767.']
            )

    def test_update_failure_with_wrong_distance_type_value(self):
        sample_data = self.get_sample_data()

        with self.subTest('value out of limit'):
            sample_data['distance_unit'] = 'kilometer'

            resp = self.put(self.get_url(), data=sample_data)

            assert resp.status_code == status.HTTP_400_BAD_REQUEST
            result_data = resp.json()
            assert result_data == dict(
                distance_unit=['Ensure this field has no more than 2 characters.']
            )

        with self.subTest('missing data'):
            sample_data = self.get_sample_data()
            sample_data['distance_unit'] = None

            resp = self.put(self.get_url(), data=sample_data)

            assert resp.status_code == status.HTTP_400_BAD_REQUEST
            result_data = resp.json()
            assert result_data == dict(distance_unit=['This field may not be null.'])

    def test_update_failure_with_wrong_price_value(self):
        sample_data = self.get_sample_data()

        with self.subTest('wrong data type'):
            sample_data['price'] = 'err'

            resp = self.put(self.get_url(), data=sample_data)

            assert resp.status_code == status.HTTP_400_BAD_REQUEST
            result_data = resp.json()
            assert result_data == dict(price=['A valid number is required.'])

        with self.subTest('value outside the limits'):
            sample_data = self.get_sample_data()
            sample_data['price'] = 100000000000.0

            resp = self.put(self.get_url(), data=sample_data)

            assert resp.status_code == status.HTTP_400_BAD_REQUEST
            result_data = resp.json()
            assert result_data == dict(
                price=['Ensure that there are no more than 10 digits in total.']
            )

    def test_update_failure_with_wrong_price_data_type(self):
        sample_data = self.get_sample_data()
        sample_data['price_type'] = 'err'

        resp = self.put(self.get_url(), data=sample_data)

        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        result_data = resp.json()
        assert result_data == dict(price_type=['"err" is not a valid choice.'])

    def test_delete_success(self):
        assert self.business.is_traveling

        resp = self.delete(self.get_url())

        assert resp.status_code == status.HTTP_204_NO_CONTENT
        assert TravelingToClients.all_objects.filter(
            id=self.traveling_to_customer.pk,
            deleted__isnull=False,
        ).exists()

    def test_delete_failure_already_deleted(self):
        self.business.traveling.soft_delete()

        resp = self.delete(self.get_url())

        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_delete_failure_non_exists(self):
        self.business.traveling.delete()

        resp = self.delete(self.get_url())

        assert resp.status_code == status.HTTP_404_NOT_FOUND
