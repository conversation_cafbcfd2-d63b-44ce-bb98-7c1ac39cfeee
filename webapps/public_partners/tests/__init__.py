import json
from copy import deepcopy

import pytest
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from django.conf import settings
from django.http import HttpResponse
from django.test import TestCase
from django.test.utils import override_settings
from jwcrypto.tests import RSAPublicPEM, RSAPrivatePEM
from jwt.algorithms import RSAAlgorithm
from mock import PropertyMock, patch
from model_bakery import baker
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

from lib.baker_utils import get_or_create_booking_source
from lib.tools import tznow
from webapps.booking.models import BookingSources
from webapps.business.enums import StaffAccessLevels, CustomData
from webapps.pos.baker_recipes import default_pos_recipe
from webapps.public_partners.enum import PublicAPIVersionEnum
from webapps.public_partners.models import PartnerPermissionBusiness

rest_settings = deepcopy(settings.REST_FRAMEWORK)
rest_settings['DEFAULT_THROTTLE_RATES'] = {'anon': '1000/sec', 'user': '1000/sec'}


BLACK_PIXEL = (
    b"\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x04\x00\x00\x00\xb5"
    + b"\x1c\x0c\x02\x00\x00\x00\x0bIDATx\xdacd\xf8\x0f\x00\x01\x05\x01\x01'\x18\xe3f\x00\x00\x00"
    + b"\x00IEND\xaeB`\x82"
)


class BooksyClientAPIClient(APIClient):
    # pylint: disable=useless-super-delegation,too-many-arguments,too-many-positional-arguments
    def generic(
        self, method, path, data='', content_type='application/octet-stream', secure=False, **extra
    ):
        return super().generic(method, path, data, content_type, secure, **extra)


def generate_keys():
    key = rsa.generate_private_key(
        backend=default_backend(),
        public_exponent=65537,
        key_size=4096,
    )
    public_key_jwk = json.loads(RSAAlgorithm(RSAAlgorithm.SHA256).to_jwk(key.public_key()))

    private_key = key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.TraditionalOpenSSL,
        encryption_algorithm=serialization.NoEncryption(),
    )
    return public_key_jwk, private_key


# pylint probably interprets @patch as reference to .patch method from the class below
# pylint: disable=used-before-assignment
@pytest.mark.public_api
@override_settings(
    REST_FRAMEWORK=rest_settings,
)
@patch(
    'webapps.public_partners.throttling.FivePerMinuteAnon.allow_request',
    lambda *args, **kwargs: True,
)
class PublicApiWithoutCredentialsTestCase(TestCase):
    _VERSION = None
    POSSIBLE_REST_METHODS = {
        'get',
        'post',
        'put',
        'patch',
        'delete',
        'options',
    }

    @property
    def version(self):
        if self._VERSION is None:
            raise NotImplementedError
        return self._VERSION

    def setUp(self):
        super().setUp()
        self.client = BooksyClientAPIClient()

    def get_accept_header(self, version):
        return f'application/json; version={version or self.version}'

    def get_headers(self, version):
        return {'HTTP_ACCEPT': self.get_accept_header(version=version)}

    def get(self, *args, **kwargs):
        return self.fetch('get', *args, **kwargs)

    def post(self, *args, **kwargs):
        return self.fetch('post', *args, **kwargs)

    def put(self, *args, **kwargs):
        return self.fetch('put', *args, **kwargs)

    def patch(self, *args, **kwargs):
        return self.fetch('patch', *args, **kwargs)

    def delete(self, *args, **kwargs):
        return self.fetch('delete', *args, **kwargs)

    def options(self, *args, **kwargs):
        return self.fetch('options', *args, **kwargs)

    def fetch(
        self,
        method,
        path,
        data=None,
        format=None,
        content_type=None,
        follow=False,
        version=None,
        **extra,
    ):  # pylint: disable=redefined-builtin,too-many-arguments,too-many-positional-arguments
        handler = getattr(self.client, method)
        extra = {**extra, **self.get_headers(version=version)}

        if method == 'get':
            return handler(
                path,
                data=data,
                format=format,
                **extra,
            )

        if format is None and content_type is None:
            content_type = 'application/json'
        if data is not None and content_type == 'application/json':
            data = json.dumps(data)

        return handler(
            path,
            data=data,
            format=format,
            content_type=content_type,
            follow=follow,
            **extra,
        )

    def check_not_allowed_methods(self, url, allowed_methods, version=None):
        allowed_methods.add('options')
        errors = []
        for method in self.POSSIBLE_REST_METHODS - allowed_methods:
            resp = getattr(self, method)(url, version=version)
            if resp.status_code != 405:
                errors.append(method)
        current_errors = '", '.join(errors)
        assert not any(errors), f'"{current_errors}" should be not allowed.'

    def _authenticate_user(self, user):
        self.client.force_authenticate(user=user)

    def _authenticate_token(self, token):
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')


class PublicApiBaseTestCase(PublicApiWithoutCredentialsTestCase):
    @classmethod
    def is_oauth2_version(cls):
        return cls._VERSION in set(v for v, _ in PublicAPIVersionEnum.oauth2_versions())

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        default_pos_recipe.make()
        cls.booking_source = get_or_create_booking_source(
            app_type=BookingSources.PUBLIC_API_APP,
        )
        cls.business = baker.make_recipe(
            'webapps.business.business_recipe',
            created=tznow(),
            updated=tznow(),
            custom_data={CustomData.VERSUM_OFFICIAL_NAME: 'Billing name'},
        )
        cls.user = cls.business.owner
        cls.staffer_owner = baker.make_recipe(
            'webapps.business.staffer_recipe',
            business=cls.business,
            staff_access_level=StaffAccessLevels.OWNER,
            staff_user=cls.user,
        )
        if cls.is_oauth2_version():
            cls.set_up_test_oauth2_data()
        else:
            cls.set_up_test_partner_data()

    @classmethod
    def set_up_test_oauth2_data(cls):
        cls.oauth2_app_owner = baker.make_recipe(
            'webapps.public_partners.oauth2_application_owner_recipe'
        )
        cls.oauth2_app = baker.make_recipe(
            'webapps.public_partners.oauth2_application_recipe',
            client_id='pytest',
            user=cls.oauth2_app_owner,
        )
        cls.oauth2_app_config = baker.make_recipe(
            'webapps.public_partners.oauth2_application_config_recipe',
            application=cls.oauth2_app,
        )
        cls.oauth2_grant = baker.make_recipe(
            'webapps.public_partners.oauth2_grant_recipe',
            application=cls.oauth2_app,
            user=cls.user,
            scope='business:read business:write profile openid',
        )
        cls.oauth2_access_token_scope = 'business:read business:write profile openid'
        cls.oauth2_access_token = baker.make_recipe(
            'webapps.public_partners.oauth2_access_token_recipe',
            application=cls.oauth2_app,
            user=cls.user,
            scope=cls.oauth2_access_token_scope,
        )
        cls.oauth2_owner_access_token_scope = 'application:read application:write profile openid'
        cls.oauth2_owner_access_token = baker.make_recipe(
            'webapps.public_partners.oauth2_access_token_recipe',
            application=cls.oauth2_app,
            user=cls.oauth2_app_owner,
            scope=cls.oauth2_owner_access_token_scope,
        )
        cls.oauth2_installation = baker.make_recipe(
            'webapps.public_partners.oauth2_installation_recipe',
            application=cls.oauth2_app,
            business=cls.business,
            user=cls.user,
        )

    @classmethod
    def set_up_test_partner_data(cls):
        cls.public_key_jwk, cls.private_key_jwk = generate_keys()
        cls.partner = cls._create_partner(public_jwk=cls.public_key_jwk)
        cls.partner.add_business(cls.business)
        cls.jwt_access_token = RefreshToken.for_user(cls.partner)

    def setUp(self):
        super().setUp()
        if self.__class__.is_oauth2_version():
            self._authenticate_token(self.oauth2_access_token.token)
        else:
            self._authenticate_token(self.jwt_access_token.access_token)

    def add_permission(self, business):
        permission = baker.make(
            PartnerPermissionBusiness,
            partner=self.partner,
            business=business,
        )
        return permission

    def authenticate_other_partner(self, **attrs):
        other_public_key_jwk, __ = generate_keys()
        other_partner = self._create_partner(public_jwk=other_public_key_jwk, **attrs)
        self._authenticate_user(other_partner)
        return other_partner

    @staticmethod
    def _create_partner(**attrs):
        return baker.make_recipe('webapps.public_partners.booksy_partner', **attrs)

    def authenticate_other_user(self, **_kwargs):
        other_business = baker.make_recipe('webapps.business.business_recipe')
        other_oauth2_app = baker.make_recipe('webapps.public_partners.oauth2_application_recipe')
        other_oauth2_access_token = baker.make_recipe(
            'webapps.public_partners.oauth2_access_token_recipe',
            application=other_oauth2_app,
            user=other_business.owner,
            scope='read write profile openid',
        )
        self._authenticate_token(other_oauth2_access_token.token)


class FirewallTestCaseMixin:
    def basic_response_for_firewall_testing(self) -> HttpResponse:
        raise NotImplementedError(
            '\'basic_response_for_firewall_testing\' has to be implemented '
            'in order to test firewall permissions. '
            'This method should return response object'
        )

    def _init_partner_for_firewall(self, uid='some_uid'):
        firewall_ip = '**********/16'
        partner_configuration = baker.make_recipe(
            'webapps.public_partners.partner_configuration',
            partner=self.partner,
            firewall_ip=firewall_ip,
        )
        self.patched_request_ip = (
            'webapps.public_partners.views.base.BooksyPublicRequest.request_ip'
        )

        business = baker.make_recipe(
            'webapps.business.business_recipe',
        )
        self.partner.add_business(business, import_uid=uid)
        return partner_configuration

    def test_partner_firewall_success(self, *_):
        uid = 'some_uid'
        origin = '**********'
        self._init_partner_for_firewall(uid=uid)
        with patch(
            self.patched_request_ip,
            new_callable=PropertyMock(return_value=origin),
        ) as _mocked_request_ip:
            resp = self.basic_response_for_firewall_testing()
            assert status.is_success(resp.status_code)

    def test_partner_firewall_failure(self, *_):
        uid = 'failed_id'
        origin = '**********'
        self._init_partner_for_firewall(uid=uid)
        with patch(
            self.patched_request_ip,
            new_callable=PropertyMock(return_value=origin),
        ) as _mocked_request_ip:
            resp = self.basic_response_for_firewall_testing()
            assert resp.status_code == status.HTTP_403_FORBIDDEN

    def test_partner_not_configured_success(self, *_):
        uid = 'partner_uid'
        origin = '0.0.0.0'
        partner_configuration = self._init_partner_for_firewall(uid=uid)
        partner_configuration.firewall_ip = None
        partner_configuration.save()
        with patch(
            self.patched_request_ip,
            new_callable=PropertyMock(return_value=origin),
        ) as _mocked_request_ip:
            resp = self.basic_response_for_firewall_testing()
            assert status.is_success(resp.status_code)

    def test_partner_no_forwarded_ip(self, *_):
        uid = 'partner_uid'
        self._init_partner_for_firewall(uid=uid)
        with patch(
            self.patched_request_ip,
            new_callable=PropertyMock(return_value=None),
        ) as _mocked_request_ip:
            resp = self.basic_response_for_firewall_testing()
            assert resp.status_code == status.HTTP_403_FORBIDDEN


class ResourcePermissionTestCaseMixin:
    _FORBIDDEN_STAFF_ACCESS_LEVELS = []

    def basic_response_for_resource_permission_testing(self) -> HttpResponse:
        raise NotImplementedError(
            '\'basic_response_for_resource_permission_testing\' has to be implemented '
            'in order to test resource permissions. '
            'This method should return response object'
        )

    def test_failure_with_forbidden_staff_access_levels(self):
        for level in self._FORBIDDEN_STAFF_ACCESS_LEVELS:
            forbidden_user = baker.make_recipe('webapps.user.user_recipe')
            _forbidden_staffer = baker.make_recipe(
                'webapps.business.staffer_recipe',
                business=self.business,
                staff_access_level=level,
                staff_user=forbidden_user,
            )
            forbidden_oauth2_access_token = baker.make_recipe(
                'webapps.public_partners.oauth2_access_token_recipe',
                application=self.oauth2_app,
                user=forbidden_user,
                scope=self.oauth2_access_token_scope,
            )
            _forbidden_oauth2_installation = baker.make_recipe(
                'webapps.public_partners.oauth2_installation_recipe',
                application=self.oauth2_app,
                business=self.business,
                user=forbidden_user,
                parent=self.oauth2_installation,
            )
            self._authenticate_token(forbidden_oauth2_access_token.token)
            resp = self.basic_response_for_resource_permission_testing()
            body = resp.json()
            self.assertEqual(resp.status_code, status.HTTP_403_FORBIDDEN)
            self.assertEqual(
                body['detail'],
                'Your access level does not allow you to perform this action.',
            )


class TokenScopePermissionTestCaseMixin:
    _REQUIRED_TOKEN_SCOPES = []

    def get_token_with_invalid_scopes(self, scope):
        return baker.make_recipe(
            'webapps.public_partners.oauth2_access_token_recipe',
            application=self.oauth2_app,
            user=self.user,
            scope=' '.join(set(self.oauth2_access_token_scope.split()) - set([scope])),
        )

    def basic_response_for_token_scope_permission_testing(self) -> HttpResponse:
        raise NotImplementedError(
            '\'basic_response_for_token_scope_permission_testing\' has to be implemented '
            'in order to test token scope permissions. '
            'This method should return response object'
        )

    def test_failure_with_invalid_token_scopes(self):
        for scope in self._REQUIRED_TOKEN_SCOPES:
            weak_oauth2_access_token = self.get_token_with_invalid_scopes(scope)
            self._authenticate_token(weak_oauth2_access_token.token)
            resp = self.basic_response_for_token_scope_permission_testing()
            body = resp.json()
            self.assertEqual(resp.status_code, status.HTTP_403_FORBIDDEN)
            self.assertEqual(
                body['detail'],
                'Access token does not have permission scope to perform this action.',
            )
