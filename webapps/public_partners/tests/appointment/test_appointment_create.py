from unittest.mock import patch
from dateutil.relativedelta import relativedelta
from model_bakery import baker
from rest_framework import status

from lib.test_utils import increase_appointment_next_id
from webapps.booking.models import (
    Appointment,
    SubBooking,
)
from webapps.business.models import (
    Service,
    ServiceVariant,
    Resource,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.public_partners.tests import (
    FirewallTestCaseMixin,
    PublicApiBaseTestCase,
)
from webapps.public_partners.tests.conftest import assert_booking_changes


class AppointmentCreateViewV01TestCase(
    FirewallTestCaseMixin,
    PublicApiBaseTestCase,
):
    url_fmt = '/public-api/us/business/{}/appointment/'.format
    _VERSION = '0.1'

    def setUp(self):
        super().setUp()
        increase_appointment_next_id()
        self.staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
        )
        target = 'webapps.public_partners.notification_dispatchers.notify_client_task'
        self.notification_patcher = patch(target=target)
        self.patched_notification_task = self.notification_patcher.start()

    def tearDown(self):
        self.notification_patcher.stop()
        super().tearDown()

    def test_check_not_allowed_methods(self):
        url = self.url_fmt(self.business.id)
        self.check_not_allowed_methods(url, {'get', 'post'})

    def get_url_to_check_allowed_methods(self):
        return self.url_fmt(997)

    def test_invalid_business_id(self):
        url = self.url_fmt(111)
        resp = self.post(url, data={})

        assert resp.status_code == status.HTTP_404_NOT_FOUND

        url = self.url_fmt('xxx')
        resp = self.post(url, data={})

        assert resp.status_code == status.HTTP_404_NOT_FOUND

        assert_booking_changes(
            expected_size=0,
            reason='public_api:appointment:business_create',
        )

    def test_create_invalid(self):
        url = self.url_fmt(self.business.id)

        resp = self.post(url, data={})

        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        assert resp.json() == dict(
            subbookings=['This field is required.'],
        )

        data = dict(subbookings=[])
        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        assert resp.json() == dict(
            subbookings=['Exactly one subbooking is required'],
        )

        data = dict(
            status=Appointment.STATUS.ACCEPTED,
            subbookings=[{}],
        )

        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        assert resp.json() == dict(
            subbookings=[
                dict(
                    booked_from=['This field is required.'],
                    staffer_id=['This field is required.'],
                ),
            ]
        )

        data = dict(
            status=Appointment.STATUS.ACCEPTED,
            subbookings=[
                dict(
                    booked_from=None,
                    staffer_id='',
                )
            ],
        )

        resp = self.post(url, data=data)
        assert resp.json() == dict(
            subbookings=[
                dict(
                    booked_from=['This field may not be null.'],
                    staffer_id=['This field may not be null.'],
                ),
            ]
        )

        data = dict(
            status=Appointment.STATUS.ACCEPTED,
            subbookings=[
                dict(
                    booked_from='2020-07-13T13:00',
                    staffer_id=self.staffer.id,
                )
            ],
        )

        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        assert resp.json() == dict(
            subbookings=[
                dict(
                    non_field_errors=[
                        'One of these fields is required: '
                        'service_variant_id or booked_till with service_name.'
                    ],
                )
            ],
        )

        variant = baker.make(ServiceVariant, duration='0100')
        data['subbookings'][0]['service_variant_id'] = variant.id  # not related
        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        assert resp.json() == dict(
            subbookings=[
                dict(
                    service_variant_id=[f'Invalid pk "{variant.id}" - object does not exist.'],
                )
            ],
        )

        assert_booking_changes(
            expected_size=0,
            reason='public_api:appointment:business_create',
        )

    def test_create_with_service_variant(self):
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            service=baker.make(Service, business=self.business, name='service_name'),
        )
        url = self.url_fmt(self.business.id)
        data = dict(
            customer_name='Customer Name',
            customer_email='<EMAIL>',
            customer_phone=None,
            booked_for_id=None,
            subbookings=[
                dict(
                    booked_from='2037-07-13T13:00',
                    booked_till='2037-07-13T18:30',  # this will be not used
                    staffer_id=self.staffer.id,
                    appliance_id=None,
                    service_variant_id=variant.id,
                    service_name='service_name',
                )
            ],
        )

        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 1
        bci_ids = list(BusinessCustomerInfo.objects.values_list('id', flat=True))
        assert len(bci_ids) == 1
        subbookings = list(SubBooking.objects.all())
        assert len(subbookings) == 1
        subbooking = subbookings[0]
        assert resp.json() == dict(
            id=subbooking.appointment_id,
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:45',
            status=Appointment.STATUS.ACCEPTED,
            customer_name='Customer Name',
            customer_email='<EMAIL>',
            customer_phone='',
            booked_for_id=bci_ids[0],
            subbookings=[
                dict(
                    id=subbooking.id,
                    booked_from='2037-07-13T13:00',
                    booked_till='2037-07-13T13:45',
                    staffer_id=self.staffer.id,
                    appliance_id=None,
                    service_variant_id=variant.id,
                    service_name='service_name',
                ),
            ],
        )

        resp = self.post(url, data=data)
        # should be a conflict - force=False
        assert resp.status_code == status.HTTP_409_CONFLICT
        assert resp.json() == {'detail': 'This time slot is no longer available.'}

        data['force'] = True
        resp = self.post(url, data=data)
        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 2

        assert_booking_changes(
            expected_size=2,
            booking_function='first',
            expected_booking=subbooking,
            reason='public_api:appointment:business_create',
        )
        self.patched_notification_task.delay.assert_not_called()

    def test_create_with_appliance(self):
        appliance = baker.make(
            Resource,
            type=Resource.APPLIANCE,
            business=self.business,
        )
        service = baker.make(Service, business=self.business, name='service_name')
        self.staffer.add_services([service.id])
        appliance.add_services([service.id])
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            service=service,
        )
        url = self.url_fmt(self.business.id)
        data = dict(
            customer_name='Customer Name',
            customer_email='<EMAIL>',
            customer_phone=None,
            booked_for_id=None,
            subbookings=[
                dict(
                    booked_from='2037-07-13T13:00',
                    booked_till='2037-07-13T18:30',  # this will be not used
                    staffer_id=self.staffer.id,
                    appliance_id=appliance.id,
                    service_variant_id=variant.id,
                    service_name='service_name',
                )
            ],
        )

        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 1
        bci_ids = list(BusinessCustomerInfo.objects.values_list('id', flat=True))
        assert len(bci_ids) == 1
        subbookings = list(SubBooking.objects.all())
        assert len(subbookings) == 1
        subbooking = subbookings[0]
        assert resp.json() == dict(
            id=subbooking.appointment_id,
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:45',
            status=Appointment.STATUS.ACCEPTED,
            customer_name='Customer Name',
            customer_email='<EMAIL>',
            customer_phone='',
            booked_for_id=bci_ids[0],
            subbookings=[
                dict(
                    id=subbooking.id,
                    booked_from='2037-07-13T13:00',
                    booked_till='2037-07-13T13:45',
                    staffer_id=self.staffer.id,
                    appliance_id=appliance.id,
                    service_variant_id=variant.id,
                    service_name='service_name',
                ),
            ],
        )

        resp = self.post(url, data=data)
        # should be a conflict - force=False
        assert resp.status_code == status.HTTP_409_CONFLICT
        assert resp.json() == {'detail': 'This time slot is no longer available.'}

        data['force'] = True
        resp = self.post(url, data=data)
        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 2

        assert_booking_changes(
            expected_size=2,
            booking_function='first',
            expected_booking=subbooking,
            reason='public_api:appointment:business_create',
        )
        self.patched_notification_task.delay.assert_not_called()

    def test_create_with_service_variant_use_exists_bci(self):
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            service=baker.make(Service, business=self.business, name='some_name'),
        )
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            full_name='Old customer',
            email='<EMAIL>',
        )
        url = self.url_fmt(self.business.id)
        data = dict(
            customer_name='Customer Name',  # this will be not used
            customer_email='<EMAIL>',
            booked_for_id=bci.id,
            subbookings=[
                dict(
                    booked_from='2020-07-13T13:00',
                    booked_till='2020-07-13T18:30',  # this will be not used
                    staffer_id=self.staffer.id,
                    service_variant_id=variant.id,
                )
            ],
        )

        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 1
        assert BusinessCustomerInfo.objects.count() == 1
        subbookings = list(SubBooking.objects.all())
        assert len(subbookings) == 1
        subbooking = subbookings[0]
        assert resp.json() == dict(
            id=subbooking.appointment_id,
            booked_from='2020-07-13T13:00',
            booked_till='2020-07-13T13:45',
            status=Appointment.STATUS.FINISHED,
            customer_name=bci.full_name,
            customer_email=bci.email,
            customer_phone='',
            booked_for_id=bci.id,
            subbookings=[
                dict(
                    id=subbooking.id,
                    booked_from='2020-07-13T13:00',
                    booked_till='2020-07-13T13:45',
                    staffer_id=self.staffer.id,
                    appliance_id=None,
                    service_variant_id=variant.id,
                    service_name='some_name',
                ),
            ],
        )

        assert_booking_changes(
            expected_size=1,
            booking_function='first',
            expected_booking=subbooking,
            reason='public_api:appointment:business_create',
        )
        self.patched_notification_task.delay.assert_not_called()

    def test_create_without_service_variant_no_bci(self):
        url = self.url_fmt(self.business.id)
        data = dict(
            subbookings=[
                dict(
                    booked_from='2030-07-13T13:00',
                    booked_till='2030-07-13T18:30',
                    staffer_id=self.staffer.id,
                    service_name='lock_slot',
                    service_variant_id=None,
                )
            ],
        )

        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 1
        subbookings = list(SubBooking.objects.all())
        assert len(subbookings) == 1
        subbooking = subbookings[0]
        assert resp.json() == dict(
            id=subbooking.appointment_id,
            booked_from='2030-07-13T13:00',
            booked_till='2030-07-13T18:30',
            status=Appointment.STATUS.ACCEPTED,
            customer_name='',
            customer_phone='',
            customer_email='',
            booked_for_id=None,
            subbookings=[
                dict(
                    id=subbooking.id,
                    booked_from='2030-07-13T13:00',
                    booked_till='2030-07-13T18:30',
                    staffer_id=self.staffer.id,
                    appliance_id=None,
                    service_variant_id=None,
                    service_name='lock_slot',
                ),
            ],
        )

        assert_booking_changes(
            expected_size=1,
            booking_function='first',
            expected_booking=subbooking,
            reason='public_api:appointment:business_create',
        )
        self.patched_notification_task.delay.assert_not_called()

    def basic_response_for_firewall_testing(self):
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            service=baker.make(Service, business=self.business, name='service_name'),
        )
        url = self.url_fmt(self.business.id)
        data = dict(
            customer_name='Customer Name',
            customer_email='<EMAIL>',
            customer_phone=None,
            booked_for_id=None,
            subbookings=[
                dict(
                    booked_from='2040-07-13T13:00',
                    booked_till='2040-07-13T18:30',  # this will be not used
                    staffer_id=self.staffer.id,
                    appliance_id=None,
                    service_variant_id=variant.id,
                    service_name='service_name',
                )
            ],
        )

        return self.post(url, data=data)


class AppointmentCreateViewV02TestCase(AppointmentCreateViewV01TestCase):
    url_fmt = '/public-api/us/business/{}/appointment/'.format
    _VERSION = '0.2'

    def test_create_with_service_variant(self):
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            service=baker.make(Service, business=self.business, name='service_name'),
        )
        url = self.url_fmt(self.business.id)
        data = dict(
            customer_name='Customer Name',
            customer_email='<EMAIL>',
            customer_phone=None,
            booked_for_id=None,
            subbookings=[
                dict(
                    booked_from='2037-07-13T13:00',
                    booked_till='2037-07-13T18:30',  # this will be not used
                    staffer_id=self.staffer.id,
                    appliance_id=None,
                    service_variant_id=variant.id,
                    service_name='service_name',
                )
            ],
        )

        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 1
        bci_ids = list(BusinessCustomerInfo.objects.values_list('id', flat=True))
        assert len(bci_ids) == 1
        subbookings = list(SubBooking.objects.all())
        assert len(subbookings) == 1
        subbooking = subbookings[0]
        assert resp.json() == dict(
            id=subbooking.appointment_id,
            import_uid=None,
            business_id=self.business.id,
            business_timezone=self.business.get_timezone()._long_name,  # pylint: disable=protected-access
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:45',
            status=Appointment.STATUS.ACCEPTED,
            business_note=None,
            customer_note=None,
            customer_name='Customer Name',
            customer_email='<EMAIL>',
            customer_phone='',
            booked_for_id=bci_ids[0],
            subbookings=[
                dict(
                    id=subbooking.id,
                    booked_from='2037-07-13T13:00',
                    booked_till='2037-07-13T13:45',
                    staffer_id=self.staffer.id,
                    appliance_id=None,
                    service_variant_id=variant.id,
                    service_name='service_name',
                ),
            ],
        )

        resp = self.post(url, data=data)
        # should be a conflict - force=False
        assert resp.status_code == status.HTTP_409_CONFLICT
        assert resp.json() == {'detail': 'This time slot is no longer available.'}

        data['force'] = True
        resp = self.post(url, data=data)
        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 2

        assert_booking_changes(
            expected_size=2,
            booking_function='first',
            expected_booking=subbooking,
            reason='public_api:appointment:business_create',
        )
        self.patched_notification_task.delay.assert_not_called()

    def test_create_with_appliance(self):
        appliance = baker.make(
            Resource,
            type=Resource.APPLIANCE,
            business=self.business,
        )
        service = baker.make(Service, business=self.business, name='service_name')
        self.staffer.add_services([service.id])
        appliance.add_services([service.id])
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            service=service,
        )
        url = self.url_fmt(self.business.id)
        data = dict(
            customer_name='Customer Name',
            customer_email='<EMAIL>',
            customer_phone=None,
            booked_for_id=None,
            subbookings=[
                dict(
                    booked_from='2037-07-13T13:00',
                    booked_till='2037-07-13T18:30',  # this will be not used
                    staffer_id=self.staffer.id,
                    appliance_id=appliance.id,
                    service_variant_id=variant.id,
                    service_name='service_name',
                )
            ],
        )

        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 1
        bci_ids = list(BusinessCustomerInfo.objects.values_list('id', flat=True))
        assert len(bci_ids) == 1
        subbookings = list(SubBooking.objects.all())
        assert len(subbookings) == 1
        subbooking = subbookings[0]
        assert resp.json() == dict(
            id=subbooking.appointment_id,
            import_uid=None,
            business_id=self.business.id,
            business_timezone=self.business.get_timezone()._long_name,  # pylint: disable=protected-access
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:45',
            status=Appointment.STATUS.ACCEPTED,
            business_note=None,
            customer_note=None,
            customer_name='Customer Name',
            customer_email='<EMAIL>',
            customer_phone='',
            booked_for_id=bci_ids[0],
            subbookings=[
                dict(
                    id=subbooking.id,
                    booked_from='2037-07-13T13:00',
                    booked_till='2037-07-13T13:45',
                    staffer_id=self.staffer.id,
                    appliance_id=appliance.id,
                    service_variant_id=variant.id,
                    service_name='service_name',
                ),
            ],
        )

        resp = self.post(url, data=data)
        # should be a conflict - force=False
        assert resp.status_code == status.HTTP_409_CONFLICT
        assert resp.json() == {'detail': 'This time slot is no longer available.'}

        data['force'] = True
        resp = self.post(url, data=data)
        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 2

        assert_booking_changes(
            expected_size=2,
            booking_function='first',
            expected_booking=subbooking,
            reason='public_api:appointment:business_create',
        )
        self.patched_notification_task.delay.assert_not_called()

    def test_create_with_service_variant_use_exists_bci(self):
        variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=45),
            service=baker.make(Service, business=self.business, name='some_name'),
        )
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            full_name='Old customer',
            email='<EMAIL>',
        )
        url = self.url_fmt(self.business.id)
        data = dict(
            customer_name='Customer Name',  # this will be not used
            customer_email='<EMAIL>',
            booked_for_id=bci.id,
            subbookings=[
                dict(
                    booked_from='2020-07-13T13:00',
                    booked_till='2020-07-13T18:30',  # this will be not used
                    staffer_id=self.staffer.id,
                    service_variant_id=variant.id,
                )
            ],
        )

        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 1
        assert BusinessCustomerInfo.objects.count() == 1
        subbookings = list(SubBooking.objects.all())
        assert len(subbookings) == 1
        subbooking = subbookings[0]
        assert resp.json() == dict(
            id=subbooking.appointment_id,
            import_uid=None,
            business_id=self.business.id,
            business_timezone=self.business.get_timezone()._long_name,  # pylint: disable=protected-access
            booked_from='2020-07-13T13:00',
            booked_till='2020-07-13T13:45',
            status=Appointment.STATUS.FINISHED,
            business_note=None,
            customer_note=None,
            customer_name=bci.full_name,
            customer_email=bci.email,
            customer_phone='',
            booked_for_id=bci.id,
            subbookings=[
                dict(
                    id=subbooking.id,
                    booked_from='2020-07-13T13:00',
                    booked_till='2020-07-13T13:45',
                    staffer_id=self.staffer.id,
                    appliance_id=None,
                    service_variant_id=variant.id,
                    service_name='some_name',
                ),
            ],
        )

        assert_booking_changes(
            expected_size=1,
            booking_function='first',
            expected_booking=subbooking,
            reason='public_api:appointment:business_create',
        )
        self.patched_notification_task.delay.assert_not_called()

    def test_create_without_service_variant_no_bci(self):
        url = self.url_fmt(self.business.id)
        data = dict(
            subbookings=[
                dict(
                    booked_from='2030-07-13T13:00',
                    booked_till='2030-07-13T18:30',
                    staffer_id=self.staffer.id,
                    service_name='lock_slot',
                    service_variant_id=None,
                )
            ],
        )

        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 1
        subbookings = list(SubBooking.objects.all())
        assert len(subbookings) == 1
        subbooking = subbookings[0]
        assert resp.json() == dict(
            id=subbooking.appointment_id,
            import_uid=None,
            business_id=self.business.id,
            business_timezone=self.business.get_timezone()._long_name,  # pylint: disable=protected-access
            booked_from='2030-07-13T13:00',
            booked_till='2030-07-13T18:30',
            status=Appointment.STATUS.ACCEPTED,
            business_note=None,
            customer_note=None,
            customer_name='',
            customer_phone='',
            customer_email='',
            booked_for_id=None,
            subbookings=[
                dict(
                    id=subbooking.id,
                    booked_from='2030-07-13T13:00',
                    booked_till='2030-07-13T18:30',
                    staffer_id=self.staffer.id,
                    appliance_id=None,
                    service_variant_id=None,
                    service_name='lock_slot',
                ),
            ],
        )

        assert_booking_changes(
            expected_size=1,
            booking_function='first',
            expected_booking=subbooking,
            reason='public_api:appointment:business_create',
        )
        self.patched_notification_task.delay.assert_not_called()

    def test_create_with_business_note(self):
        url = self.url_fmt(self.business.id)
        data = dict(
            business_note='Note',
            subbookings=[
                dict(
                    booked_from='2030-07-13T13:00',
                    booked_till='2030-07-13T18:30',
                    staffer_id=self.staffer.id,
                    service_name='lock_slot',
                    service_variant_id=None,
                )
            ],
        )

        resp = self.post(url, data=data)

        assert resp.status_code == status.HTTP_201_CREATED
        assert Appointment.objects.count() == 1
        subbookings = list(SubBooking.objects.all())
        assert len(subbookings) == 1
        subbooking = subbookings[0]
        assert resp.json() == dict(
            id=subbooking.appointment_id,
            import_uid=None,
            business_id=self.business.id,
            business_timezone=self.business.get_timezone()._long_name,  # pylint: disable=protected-access
            booked_from='2030-07-13T13:00',
            booked_till='2030-07-13T18:30',
            status=Appointment.STATUS.ACCEPTED,
            business_note='Note',
            customer_note=None,
            customer_name='',
            customer_phone='',
            customer_email='',
            booked_for_id=None,
            subbookings=[
                dict(
                    id=subbooking.id,
                    booked_from='2030-07-13T13:00',
                    booked_till='2030-07-13T18:30',
                    staffer_id=self.staffer.id,
                    appliance_id=None,
                    service_variant_id=None,
                    service_name='lock_slot',
                ),
            ],
        )

        assert_booking_changes(
            expected_size=1,
            booking_function='first',
            expected_booking=subbooking,
            reason='public_api:appointment:business_create',
        )
        self.patched_notification_task.delay.assert_not_called()
