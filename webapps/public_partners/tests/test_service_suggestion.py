from decimal import Decimal
from urllib import parse
from unittest.mock import patch

from dateutil.relativedelta import relativedelta
from django.conf import settings
from rest_framework import status

from lib.test_utils import compare_expected_fields
from webapps.business.baker_recipes import category_recipe, treatment_recipe
from webapps.business.models import ServiceSuggestion
from webapps.public_partners.tests import (
    FirewallTestCaseMixin,
    PublicApiBaseTestCase,
)


def mock_get_suggestions(treatment):
    return {
        'empty': [
            ServiceSuggestion(
                treatment=treatment,
                name=treatment.full_name,
                duration=None,
                price=None,
                tax_rate=None,
                popularity=0.0,
            )
        ],
        'base': [
            ServiceSuggestion(
                treatment=treatment,
                name=treatment.full_name,
                duration=relativedelta(hours=1),
                price=Decimal(100),
                tax_rate=Decimal(23),
                popularity=2.0,
            )
        ],
    }


class ServiceSuggestionViewSetTestCase(FirewallTestCaseMixin, PublicApiBaseTestCase):
    _url = '/public-api/us/service_suggestion/'
    _VERSION = '0.2'

    def setUp(self):
        super().setUp()

        self.hairsalon_category = category_recipe.make(
            full_name='Barber',
        )
        self.haircut_treatment = treatment_recipe.make(
            full_name='Haircut',
            parent=self.hairsalon_category,
        )

    def get_url(self, category_id, country_code):
        kwargs = {
            k: v
            for k, v in {
                'category_id': category_id,
                'country_code': country_code,
            }.items()
            if v is not None
        }
        url_parts = parse.urlparse(self._url)._asdict()
        url_parts['query'] = parse.urlencode(kwargs)
        return parse.urlunparse(parse.ParseResult(**url_parts))

    def test_check_not_allowed_methods(self):
        self.check_not_allowed_methods(self.get_url(None, None), {'get', 'options'})

    def test_list_service_suggestions_no_params(self):
        resp = self.get(self.get_url(None, None))
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        assert resp.json() == ['\'country_code\' and \'category_id\' has to be specified']

    @patch('webapps.public_partners.views.service_suggestion.ServiceSuggestionUtil')
    def test_list_service_suggestions(self, mock_service_suggestion):
        mock_service_suggestion.get_suggestions_by_category.return_value = mock_get_suggestions(
            self.haircut_treatment
        )['base']
        resp = self.get(self.get_url(self.hairsalon_category.id, settings.API_COUNTRY))
        assert resp.status_code == status.HTTP_200_OK
        services = resp.json()
        assert len(services) == 1
        service = services[0]
        compare_expected_fields(
            service.keys(),
            (
                'treatment',
                'name',
                'duration',
                'price',
                'tax_rate',
                'popularity',
            ),
        )
        self.assertDictEqual(
            service,
            {
                'treatment': self.haircut_treatment.pk,
                'name': "Haircut",
                'duration': 60,
                'price': "100.00",
                'tax_rate': "23.00",
                'popularity': 2.0,
            },
        )

    @patch('webapps.public_partners.views.service_suggestion.ServiceSuggestionUtil')
    def test_suggestion_with_empty_fields(self, mock_service_suggestion):
        category = category_recipe.make(
            full_name='Hairdresser',
        )
        treatment = treatment_recipe.make(
            full_name='Haircut',
            parent=category,
        )
        mock_service_suggestion.get_suggestions_by_category.return_value = mock_get_suggestions(
            treatment
        )['empty']
        resp = self.get(self.get_url(category.id, settings.API_COUNTRY))
        assert resp.status_code == status.HTTP_200_OK
        services = resp.json()
        assert len(services) == 1
        service = services[0]
        self.assertDictEqual(
            service,
            {
                'treatment': treatment.pk,
                'name': "Haircut",
                'duration': None,
                'price': None,
                'tax_rate': None,
                'popularity': 0.0,
            },
        )

    def basic_response_for_firewall_testing(self):
        return self.get(self.get_url(self.hairsalon_category.id, settings.API_COUNTRY))
