from freezegun import freeze_time
from model_bakery import baker
from rest_framework import status

from lib.test_utils import compare_expected_fields
from lib.tools import tznow
from webapps.business.baker_recipes import (
    service_recipe,
    service_variant_recipe,
    service_with_variant_recipe,
    traveling_to_client_recipe,
    treatment_recipe,
)
from webapps.business.enums import ComboType
from webapps.business.models import ServiceCategory, ServiceVariant
from webapps.public_partners.models import ServiceMetadata
from webapps.public_partners.tests import PublicApiBaseTestCase


class BaseServiceViewSetV04TestCase(PublicApiBaseTestCase):
    _url = '/public-api/us/business/{}/service/'
    _details_url = '/public-api/us/business/{}/service/{}/'
    _duplicate_url = '/public-api/us/business/{}/service/{}/duplicate/'
    _mapping_url = '/public-api/us/business/{}/service/mapping/'
    _VERSION = '0.4'

    SERVICE_FIELDS = {
        'id',
        'business_id',
        'name',
        'description',
        'note',
        'order',
        'is_online_service',
        'is_available_for_customer_booking',
        'padding_time',
        'padding_type',
        'tax_rate',
        'parallel_clients',
        'service_category_id',
        'treatment_id',
        'variants',
        'active',
        'questions',
        'import_uid',
        'is_traveling_service',
        'color',
        'combo_type',
        'metadata',
        'created',
        'updated',
    }
    SERVICE_VARIANT_FIELDS = {
        'id',
        'active',
        'type',
        'price',
        'duration',
        'label',
        'time_slot_interval',
        'gap_hole_duration',
        'gap_hole_start_after',
        'combo_pricing',
        'combo_children',
        'created',
        'updated',
    }
    MAPPING_SERVICE_FIELDS = SERVICE_FIELDS

    def get_url(self, business_id=None):
        return self._url.format(business_id or self.business.id)

    def get_details_url(self, service_id, business_id=None):
        return self._details_url.format(business_id or self.business.id, service_id)

    def get_duplicate_url(self, service_id, business_id=None):
        return self._duplicate_url.format(business_id or self.business.id, service_id)

    def get_mapping_url(self, business_id=None):
        return self._mapping_url.format(business_id or self.business.id)

    @staticmethod
    def get_sample_data(**kwargs):
        result = {
            'order': 1,
            'name': 'sample service',
            'description': 'sample description of service',
            'note': 'internal note',
            'import_uid': 'external_id-1234',
            'is_online_service': True,
            'is_available_for_customer_booking': True,
            'questions': ['Question 1?', 'Question 2?'],
            'color': 'f6d9ff',
        }
        return {**result, **kwargs}


class ServiceViewSetV04TestCase(BaseServiceViewSetV04TestCase):
    def test_check_not_allowed_methods(self):
        self.check_not_allowed_methods(
            self.get_url(), {'post', 'get', 'patch', 'delete', 'options'}
        )

    def test_update_service(self):
        treatment = treatment_recipe.make()
        category = baker.make(ServiceCategory, business=self.business)
        expected_service = service_recipe.make(business=self.business)
        service_variant_recipe.make(service=expected_service)
        baker.make(
            ServiceMetadata,
            service=expected_service,
            application=self.oauth2_app,
            metadata=dict(kind='basic'),
        )
        self.assertEqual(ServiceMetadata.objects.filter(deleted__isnull=True).count(), 1)
        data = self.get_sample_data(
            service_category_id=category.id,
            treatment_id=treatment.id,
            metadata=dict(
                kind=None,
                bar=1,
                extra=dict(baz=True),
            ),
        )
        assert self.business.services.all().count() == 1
        resp = self.patch(self.get_details_url(expected_service.pk), data=data)
        assert resp.status_code == status.HTTP_200_OK
        assert self.business.services.all().count() == 1

        service = resp.json()
        compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
        compare_expected_fields(service['variants'][0], self.SERVICE_VARIANT_FIELDS)
        self.assertEqual(service['id'], expected_service.id)
        self.assertEqual(service['service_category_id'], data['service_category_id'])
        self.assertEqual(service['treatment_id'], data['treatment_id'])
        self.assertTrue(service['active'])
        self.assertIsNone(service['metadata']['kind'])
        self.assertNotIn('bar', service['metadata'])
        self.assertTrue(service['metadata']['extra']['baz'])
        self.assertEqual(len(service['questions']), 2)
        self.assertEqual(ServiceMetadata.objects.filter(deleted__isnull=True).count(), 1)

    def test_update_service_with_forcing_import_uid(self):
        expected_service = service_recipe.make(business=self.business, import_uid='some_uid')
        assert self.business.services.all().count() == 1
        resp = self.patch(self.get_details_url(expected_service.pk), dict(import_uid='new_uid'))
        assert resp.status_code == status.HTTP_200_OK
        assert self.business.services.all().count() == 1
        service = resp.json()
        compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
        assert service['id'] == expected_service.id
        assert service['import_uid'] == 'some_uid'

    def test_update_service_with_combo(self):
        expected_service = service_recipe.make(
            business=self.business,
            combo_type=ComboType.SEQUENCE,
        )
        assert self.business.services.all().count() == 1
        resp = self.patch(self.get_details_url(expected_service.pk), dict(name='Combo name'))
        assert resp.status_code == status.HTTP_200_OK
        assert self.business.services.all().count() == 1
        service = resp.json()
        assert service['id'] == expected_service.id
        assert service['name'] == 'Combo name'

    def test_deactivate_active_service(self):
        service = service_recipe.make(business=self.business)
        service_variant = service_variant_recipe.make(service=service)
        request_time = tznow()

        assert service.active
        assert service_variant.active

        data = {'active': False}
        with freeze_time(request_time):
            resp = self.patch(self.get_details_url(service.pk), data=data)

        assert resp.status_code == status.HTTP_200_OK
        service.refresh_from_db()
        service_variant.refresh_from_db()

        assert self.business.services.all().count() == 1
        assert not service.active
        assert not service_variant.active
        assert service_variant.valid_till == request_time

    def test_activate_inactive_service(self):
        service = service_recipe.make(business=self.business, active=False)
        service_variant = service_variant_recipe.make(service=service, active=False)

        data = {'active': True}
        resp = self.patch(self.get_details_url(service.pk), data=data)

        assert resp.status_code == status.HTTP_200_OK
        service.refresh_from_db()
        service_variant.refresh_from_db()

        assert self.business.services.all().count() == 1
        assert service.active
        assert not service_variant.active

    def test_active_flag_doesnt_default_to_true(self):
        service = service_recipe.make(business=self.business, active=False)

        data = {'name': 'New service name'}  # active isn't passed
        resp = self.patch(self.get_details_url(service.pk), data=data)

        assert resp.status_code == status.HTTP_200_OK
        service.refresh_from_db()

        assert not service.active

    def test_get_service(self):
        expected_service = service_recipe.make(business=self.business)
        service_variant_recipe.make(service=expected_service)
        metadata = baker.make(
            ServiceMetadata,
            service=expected_service,
            application=self.oauth2_app,
            metadata=dict(kind='basic'),
        )
        baker.make(
            ServiceMetadata,
            service=expected_service,
            application=baker.make_recipe('webapps.public_partners.oauth2_application_recipe'),
            metadata=dict(kind='medical'),
        )

        assert self.business.services.all().count() == 1
        resp = self.get(self.get_details_url(expected_service.pk))
        assert resp.status_code == status.HTTP_200_OK
        assert self.business.services.all().count() == 1

        service = resp.json()
        compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
        compare_expected_fields(service['variants'][0], self.SERVICE_VARIANT_FIELDS)
        service_metadata = service['metadata']
        self.assertEqual(service_metadata['kind'], metadata.metadata['kind'])
        self.assertNotIn('resource_url', service_metadata)

    def test_list_services(self):
        expected_services = service_recipe.make(business=self.business, _quantity=19)

        assert self.business.services.all().count() == len(expected_services)
        resp = self.get(self.get_url())
        assert resp.status_code == status.HTTP_200_OK

        services = resp.json()
        assert len(services) == len(expected_services)
        service = resp.json()[0]
        compare_expected_fields(service.keys(), self.SERVICE_FIELDS)

    def test_list_services_with_filters(self):
        service_recipe.make(business=self.business, name='First', active=True)
        service_recipe.make(business=self.business, name='Second', active=False)

        def make_request(url):
            resp = self.get(url)
            assert resp.status_code == status.HTTP_200_OK
            return resp.json()

        with self.subTest('found one with name optional filter'):
            filter_url = self.get_url() + '?name=First'
            body = make_request(filter_url)
            assert len(body) == 1
            assert body[0]['name'] == 'First'

        with self.subTest('found two with name optional filter'):
            filter_url = self.get_url() + '?name=First, Second'
            body = make_request(filter_url)
            assert len(body) == 2

        with self.subTest('found none with name optional filter'):
            filter_url = self.get_url() + '?name=Incorrect'
            body = make_request(filter_url)
            assert len(body) == 0

        with self.subTest('found one with active optional filter'):
            filter_url = self.get_url() + '?active=True'
            body = make_request(filter_url)
            assert len(body) == 1

    def test_delete_service(self):
        expected_service = service_with_variant_recipe.make(business=self.business)

        assert self.business.services.all().count() == 1
        resp = self.delete(self.get_details_url(expected_service.pk))
        assert resp.status_code == status.HTTP_204_NO_CONTENT
        assert self.business.services.all().count() == 0
        assert ServiceVariant.objects.filter(deleted__isnull=True, active=False).count() == 0

    def test_get_incorrect_business(self):
        other_business = baker.make_recipe('webapps.business.business_recipe')
        service = service_recipe.make(business=other_business)
        resp = self.get(self.get_details_url(service.id, business_id=other_business.id))
        assert resp.status_code == status.HTTP_403_FORBIDDEN

    def test_get_deleted_service(self):
        expected_service = service_with_variant_recipe.make(business=self.business)
        assert self.business.services.all().count() == 1
        self.delete(self.get_details_url(expected_service.pk))
        assert self.business.services.all().count() == 0

        resp = self.get(self.get_details_url(expected_service.pk))
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_list_services_by_wrong_partner_error(self):
        self.authenticate_other_user()
        resp = self.get(self.get_url())
        assert resp.status_code == status.HTTP_403_FORBIDDEN

    def test_mapping_service(self):
        import_uid = 'some-uid'
        service_recipe.make(business=self.business, import_uid=import_uid)

        resp = self.post(self.get_mapping_url(), data=dict(import_uid=import_uid))
        assert resp.status_code == status.HTTP_200_OK
        service = resp.json()
        compare_expected_fields(service.keys(), self.MAPPING_SERVICE_FIELDS)

    def test_mapping_service_with_blank_value(self):
        import_uid = ''
        resp = self.post(self.get_mapping_url(), data=dict(import_uid=import_uid))
        assert resp.status_code == status.HTTP_400_BAD_REQUEST

    def test_mapping_no_service(self):
        import_uid = 'some-uid'
        service_recipe.make(business=self.business, import_uid=import_uid)
        resp = self.post(self.get_mapping_url(), data=dict(import_uid='other_id'))
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_mapping_deleted_service(self):
        import_uid = 'some-uid'
        service_recipe.make(business=self.business, deleted=tznow(), import_uid=import_uid)

        resp = self.post(self.get_mapping_url(), data=dict(import_uid=import_uid))
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_mapping_service_by_wrong_partner_error(self):
        self.authenticate_other_user()
        import_uid = 'some-uid'
        service_recipe.make(business=self.business, import_uid=import_uid)

        resp = self.post(self.get_mapping_url(), data=dict(import_uid=import_uid))
        assert resp.status_code == status.HTTP_403_FORBIDDEN

    def test_mapping_failure_with_multiple_results(self):
        import_uid = 'some-uid'
        service_recipe.make(business=self.business, import_uid=import_uid, _quantity=2)
        resp = self.post(self.get_mapping_url(), dict(import_uid=import_uid))
        assert resp.status_code == status.HTTP_409_CONFLICT

    def test_duplicate_success(self):
        service = service_recipe.make(business=self.business, import_uid='some_uid')
        resp = self.post(
            self.get_duplicate_url(service.id),
            dict(name='New name', import_uid='new-uid'),
        )
        assert resp.status_code == status.HTTP_201_CREATED
        body = resp.json()
        compare_expected_fields(body.keys(), self.SERVICE_FIELDS)
        assert body['id'] != service.id
        assert body['name'] == 'New name'
        assert body['import_uid'] == 'new-uid'

    def test_duplicate_failure_with_invalid_payload(self):
        service = service_recipe.make(business=self.business, import_uid='some_uid')
        resp = self.post(
            self.get_duplicate_url(service.id),
            dict(name=''),
        )
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        compare_expected_fields(resp.json().keys(), ['name'])

    def test_duplicate_failure_with_invalid_service(self):
        resp = self.post(
            self.get_duplicate_url(123),
            dict(name='New name', import_uid='new-uid'),
        )
        assert resp.status_code == status.HTTP_404_NOT_FOUND


class ServiceCreateViewV04TestCase(BaseServiceViewSetV04TestCase):
    @staticmethod
    def get_sample_nested_data(**kwargs):
        result = {
            'order': 1,
            'name': 'sample service',
            'import_uid': 'external_id-1234',
            'variants': [
                {
                    'type': 'X',
                    'price': '79.99',
                    'duration': 30,
                },
                {
                    'type': 'X',
                    'price': '99.99',
                    'duration': 60,
                    'label': 'Some variant name',
                },
            ],
        }
        return {**result, **kwargs}

    def test_create_simple_service(self):
        self.assertEqual(ServiceMetadata.objects.filter(deleted__isnull=True).count(), 0)
        assert self.business.services.all().count() == 0
        resp = self.post(self.get_url(), dict(name='Simple service'))
        assert resp.status_code == status.HTTP_201_CREATED
        assert self.business.services.all().count() == 1

        service = resp.json()
        compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
        self.assertIsNotNone(service['id'])
        self.assertEqual(service['name'], 'Simple service')
        self.assertEqual(service['variants'], [])
        self.assertTrue(service['active'])
        self.assertIsNotNone(service['color'])
        self.assertEqual(len(service['color']), 6)
        self.assertEqual(ServiceMetadata.objects.filter(deleted__isnull=True).count(), 0)

    def test_create_complex_service(self):
        self.assertEqual(ServiceMetadata.objects.filter(deleted__isnull=True).count(), 0)
        treatment = treatment_recipe.make()
        category = baker.make(ServiceCategory, business=self.business)
        data = self.get_sample_data(
            service_category_id=category.id,
            treatment_id=treatment.id,
            metadata=dict(
                kind='basic',
                bar=1,
                extra=dict(baz=True),
            ),
        )
        assert self.business.services.all().count() == 0
        resp = self.post(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_201_CREATED
        assert self.business.services.all().count() == 1

        service = resp.json()
        compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
        self.assertIsNotNone(service['id'])
        self.assertEqual(service['import_uid'], data['import_uid'])
        self.assertEqual(service['service_category_id'], data['service_category_id'])
        self.assertEqual(service['treatment_id'], data['treatment_id'])
        self.assertEqual(service['variants'], [])
        self.assertTrue(service['active'])
        self.assertEqual(service['metadata']['kind'], 'basic')
        self.assertNotIn('bar', service['metadata'])
        self.assertTrue(service['metadata']['extra']['baz'])
        self.assertEqual(len(service['questions']), 2)
        self.assertEqual(service['color'], 'f6d9ff')
        self.assertEqual(ServiceMetadata.objects.filter(deleted__isnull=True).count(), 1)

    def test_create_service_with_variants(self):
        self.assertEqual(ServiceVariant.objects.filter(deleted__isnull=True).count(), 0)
        self.assertEqual(ServiceMetadata.objects.filter(deleted__isnull=True).count(), 0)
        assert self.business.services.all().count() == 0
        data = self.get_sample_nested_data()
        resp = self.post(self.get_url(), data=data)
        service = resp.json()
        assert resp.status_code == status.HTTP_201_CREATED
        assert self.business.services.all().count() == 1

        compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
        compare_expected_fields(service['variants'][0].keys(), self.SERVICE_VARIANT_FIELDS)
        self.assertIsNotNone(service['id'])
        self.assertEqual(service['name'], 'sample service')
        self.assertTrue(service['active'])
        self.assertEqual(len(service['variants']), 2)
        self.assertEqual(ServiceVariant.objects.filter(deleted__isnull=True).count(), 2)
        self.assertEqual(ServiceMetadata.objects.filter(deleted__isnull=True).count(), 0)

    def test_create_service_with_variants_doesnt_touch_other_data(self):
        service_with_variant_recipe.make()
        self.assertEqual(ServiceVariant.objects.filter(deleted__isnull=True).count(), 1)
        self.assertEqual(ServiceMetadata.objects.filter(deleted__isnull=True).count(), 0)
        assert self.business.services.all().count() == 0
        data = self.get_sample_nested_data()
        resp = self.post(self.get_url(), data=data)
        service = resp.json()
        assert resp.status_code == status.HTTP_201_CREATED
        assert self.business.services.all().count() == 1

        compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
        compare_expected_fields(service['variants'][0].keys(), self.SERVICE_VARIANT_FIELDS)
        self.assertIsNotNone(service['id'])
        self.assertEqual(service['name'], 'sample service')
        self.assertTrue(service['active'])
        self.assertEqual(len(service['variants']), 2)
        self.assertEqual(ServiceVariant.objects.filter(deleted__isnull=True).count(), 3)
        self.assertEqual(ServiceMetadata.objects.filter(deleted__isnull=True).count(), 0)

    def test_create_service_with_invalid_payload(self):
        data = self.get_sample_data(name=None)
        assert self.business.services.all().count() == 0
        resp = self.post(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        compare_expected_fields(resp.json().keys(), ['name'])

        data = self.get_sample_data(padding_type='X', padding_time='15')
        resp = self.post(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        compare_expected_fields(resp.json().keys(), ['padding_type'])

        data = self.get_sample_data(padding_type='A', padding_time=None)
        resp = self.post(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        compare_expected_fields(resp.json().keys(), ['padding_time'])

        data = self.get_sample_data(metadata=dict(kind='foo', bar=1))
        resp = self.post(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        body = resp.json()
        compare_expected_fields(body.keys(), ['metadata'])
        self.assertEqual(body['metadata'], {'kind': ['"foo" is not a valid choice.']})

    def test_create_service_by_wrong_partner_error(self):
        self.authenticate_other_user()
        treatment = treatment_recipe.make()
        category = baker.make(ServiceCategory, business=self.business)
        data = self.get_sample_data(service_category=category.id, treatment=treatment.id)
        resp = self.post(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_403_FORBIDDEN

    def test_create_success_with_traveling_to_clients(self):
        traveling_to_client_recipe.make(business=self.business)
        resp = self.post(
            self.get_url(business_id=self.business.id),
            dict(name='Simple service', is_traveling_service=True),
        )
        assert resp.status_code == status.HTTP_201_CREATED
        service = resp.json()
        compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
        assert service['id']
        assert service['name'] == 'Simple service'
        assert service['is_traveling_service']

        service = service_recipe.make(business=self.business, is_traveling_service=False)

        resp = self.patch(
            self.get_details_url(service.pk, business_id=self.business.id),
            data=dict(is_traveling_service=True),
        )
        assert resp.status_code == status.HTTP_200_OK
        service = resp.json()
        compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
        assert service['id']
        assert service['is_traveling_service']

    def test_create_failure_with_traveling_to_clients_and_invalid_business(self):
        resp = self.post(
            self.get_url(business_id=self.business.id),
            dict(name='Simple service', is_traveling_service=True),
        )
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        body = resp.json()
        assert body == dict(is_traveling_service=['Travel settings not defined for business'])

        service = service_recipe.make(business=self.business, is_traveling_service=False)

        resp = self.patch(
            self.get_details_url(service.pk, business_id=self.business.id),
            data=dict(is_traveling_service=True),
        )
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        body = resp.json()
        assert body == dict(is_traveling_service=['Travel settings not defined for business'])
