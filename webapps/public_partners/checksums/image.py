from rest_framework import serializers

from webapps.business.models import BusinessCategory
from webapps.images.models import Image
from webapps.public_partners.checksums.base import BaseChecksumSerializer
from webapps.public_partners.models import ImageChecksum


class ImageChecksumSerializer(BaseChecksumSerializer):
    class Meta(BaseChecksumSerializer.Meta):
        model = Image
        checksum_model = ImageChecksum
        fields = read_only_fields = (
            'business_id',
            'cms_content_id',
            'inspiration_category_ids',
            'category',
            'is_cover_photo',
            'description',
            'tags',
            'staffers',
            'image_url',
            'order',
            'active',
            'visible',
            'height',
            'width',
            'deleted',
        )

    inspiration_category_ids = serializers.PrimaryKeyRelatedField(
        many=True,
        required=False,
        queryset=BusinessCategory.objects.order_by('pk'),
        source='inspiration_categories',
    )
