# pylint: skip-file
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.utils.functional import cached_property
from rest_framework.generics import get_object_or_404
from rest_framework.mixins import (
    ListModelMixin,
)
from rest_framework.permissions import IsAuthenticated

from lib.db import using_db_for_reads, READ_ONLY_DB

from webapps.booking.models import Appointment
from webapps.business.resources import AnyResource
from webapps.consents.manager import ConsentManager
from webapps.public_partners.consts import STAFF_ACCESS_LEVELS_ALL
from webapps.public_partners.enum import PublicAPIVersionEnum
from webapps.public_partners.permissions import (
    BusinessAccessPermission,
    OAuth2TokenMayHaveScopePermission,
    PartnerFirewallPermission,
    ResourceAccessPermission,
)
from webapps.public_partners.serializers import (
    PAAppointmentConsentFormSerializerV02,
    PAAppointmentConsentFormSerializerV03,
    PAAppointmentConsentFormSerializerV04,
)
from webapps.public_partners.versioning import VersionSpec
from webapps.public_partners.views.base import (
    BusinessRelatedViewMixin,
    GenericVersionPublicAPIViewSet,
)


class ConsentViewSet(
    ListModelMixin,
    BusinessRelatedViewMixin,
    GenericVersionPublicAPIViewSet,
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    permission_classes = [
        IsAuthenticated,
        PartnerFirewallPermission,
        OAuth2TokenMayHaveScopePermission,
        BusinessAccessPermission,
        ResourceAccessPermission,
    ]
    versioned_oauth2_token_scopes = {
        VersionSpec(min_version=PublicAPIVersionEnum.V04): dict(
            default=[[settings.OAUTH2_BUSINESS_READ_SCOPE]],
        ),
    }
    versioned_resource_access_levels = {
        VersionSpec(min_version=PublicAPIVersionEnum.V04): dict(
            default=STAFF_ACCESS_LEVELS_ALL,
        ),
    }
    action_versions_map = {
        'list': VersionSpec(PublicAPIVersionEnum.V02),
    }
    versioned_serializer_classes = {
        PublicAPIVersionEnum.V02: dict(default=PAAppointmentConsentFormSerializerV02),
        PublicAPIVersionEnum.V03: dict(default=PAAppointmentConsentFormSerializerV03),
        PublicAPIVersionEnum.V04: dict(default=PAAppointmentConsentFormSerializerV04),
        # currently use v0.4 serializers, change if needed
        PublicAPIVersionEnum.V05: dict(default=PAAppointmentConsentFormSerializerV04),
    }

    def get_queryset(self):
        return ConsentManager.from_appointment(self.appointment).get_consent_forms(
            _booked_for=self.booked_for
        )

    @using_db_for_reads(READ_ONLY_DB)
    def list(self, request, *args, **kwargs):
        return super().list(request, args, kwargs)

    @cached_property
    @using_db_for_reads(READ_ONLY_DB)
    def appointment(self):
        return get_object_or_404(
            Appointment.objects.filter(deleted__isnull=True),
            business_id=self.kwargs['business_pk'],
            id=self.kwargs['appointment_pk'],
        )

    @cached_property
    @using_db_for_reads(READ_ONLY_DB)
    def booked_for(self):
        booked_for = self.appointment.booked_for
        if booked_for and booked_for.id is AnyResource:
            booked_for = None
        return booked_for

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['appointment'] = self.appointment
        return context
