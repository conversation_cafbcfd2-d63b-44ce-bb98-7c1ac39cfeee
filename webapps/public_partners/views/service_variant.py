# pylint: skip-file
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.db import transaction
from django.db.models import Exists, OuterRef, Prefetch
from django.utils.translation import gettext_lazy as _
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated

from lib.tools import tznow
from webapps.business.models import ComboMembership, ServiceVariant, ServiceVariantPayment
from webapps.public_partners.consts import STAFF_ACCESS_LEVELS_ALL, STAFF_ACCESS_LEVELS_MIN_MANAGER
from webapps.public_partners.enum import PublicAPIVersionEnum
from webapps.public_partners.filters import (
    ServiceVariantFilterBackend,
    ServiceVariantListingFilterSet,
)
from webapps.public_partners.permissions import (
    BusinessAccessPermission,
    OAuth2TokenMayHaveScopePermission,
    PartnerFirewallPermission,
    ResourceAccessPermission,
)
from webapps.public_partners.serializers import (
    PABasicServiceVariantSerializerV02,
    PABasicServiceVariantSerializerV03,
    PABasicServiceVariantSerializerV04,
    PAComboServiceVariantSerializerV04,
    PAServiceVariantSerializerV02,
    PAServiceVariantSerializerV03,
    PAServiceVariantSerializerV04,
)
from webapps.public_partners.versioning import VersionSpec
from webapps.public_partners.views.base import (
    BulkImportModelMixin,
    BusinessRelatedViewMixin,
    FullModelMixin,
    GenericVersionPublicAPIViewSet,
    ServiceRelatedViewMixin,
)


class ServiceVariantViewSet(
    FullModelMixin,
    BulkImportModelMixin,
    ServiceRelatedViewMixin,
    BusinessRelatedViewMixin,
    GenericVersionPublicAPIViewSet,
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    permission_classes = [
        IsAuthenticated,
        PartnerFirewallPermission,
        OAuth2TokenMayHaveScopePermission,
        BusinessAccessPermission,
        ResourceAccessPermission,
    ]
    versioned_oauth2_token_scopes = {
        VersionSpec(min_version=PublicAPIVersionEnum.V04): dict(
            list=[[settings.OAUTH2_BUSINESS_READ_SCOPE]],
            retrieve=[[settings.OAUTH2_BUSINESS_READ_SCOPE]],
            default=[[settings.OAUTH2_BUSINESS_WRITE_SCOPE]],
        ),
    }
    versioned_resource_access_levels = {
        VersionSpec(min_version=PublicAPIVersionEnum.V04): dict(
            list=STAFF_ACCESS_LEVELS_ALL,
            retrieve=STAFF_ACCESS_LEVELS_ALL,
            default=STAFF_ACCESS_LEVELS_MIN_MANAGER,
        ),
    }
    action_versions_map = {
        'retrieve': VersionSpec(PublicAPIVersionEnum.V02),
        'list': VersionSpec(PublicAPIVersionEnum.V02),
        'create': VersionSpec(PublicAPIVersionEnum.V02),
        'update': VersionSpec(PublicAPIVersionEnum.V02),
        'partial_update': VersionSpec(PublicAPIVersionEnum.V02),
        'destroy': VersionSpec(PublicAPIVersionEnum.V02),
        'bulk_import': VersionSpec(min_version=PublicAPIVersionEnum.V04),
    }
    filterset_class = ServiceVariantListingFilterSet
    filter_backends = (ServiceVariantFilterBackend,)
    versioned_serializer_classes = {
        PublicAPIVersionEnum.V02: dict(
            list=PAServiceVariantSerializerV02,
            retrieve=PAServiceVariantSerializerV02,
            default=PABasicServiceVariantSerializerV02,
        ),
        PublicAPIVersionEnum.V03: dict(
            list=PAServiceVariantSerializerV03,
            retrieve=PAServiceVariantSerializerV03,
            default=PABasicServiceVariantSerializerV03,
        ),
        PublicAPIVersionEnum.V04: dict(
            list=PAServiceVariantSerializerV04,
            retrieve=PAServiceVariantSerializerV04,
            default=PABasicServiceVariantSerializerV04,
        ),
        # currently use v0.4 serializers, change if needed
        PublicAPIVersionEnum.V05: dict(
            list=PAServiceVariantSerializerV04,
            retrieve=PAServiceVariantSerializerV04,
            default=PABasicServiceVariantSerializerV04,
        ),
    }

    def get_serializer_class(self):
        # currently use v0.4 serializers, change if needed
        if (
            self._version
            in (
                PublicAPIVersionEnum.V04,
                PublicAPIVersionEnum.V05,
            )
            and self.service.is_combo
        ):
            return PAComboServiceVariantSerializerV04
        return super().get_serializer_class()

    def get_queryset(self):
        return (
            ServiceVariant.objects.annotate(
                # PublicAPI doesn't support cancellation fee or prepayment
                fee_exist=Exists(
                    ServiceVariantPayment.objects.filter(
                        service_variant_id=OuterRef('id'),
                        deleted__isnull=True,
                    ),
                )
            )
            .filter(
                service_id=self.kwargs['service_pk'],
                fee_exist=False,
            )
            .prefetch_related(
                Prefetch(
                    'combo_children_through',
                    queryset=ComboMembership.objects.select_related('child'),
                )
            )
            .order_by('duration')
        )

    @transaction.atomic
    def perform_destroy(self, instance):
        if not self.validation_skipped and instance.get_related_combo_services().exists():
            error_message = _('This variant has assigned other combo services')
            raise ValidationError(error_message, code='has_related_combo_services')
        for combo_service in instance.get_related_combo_services():
            combo_service.safe_delete()
        instance.valid_till = tznow()
        instance.safe_delete()
