# pylint: skip-file

from bo_obs.datadog.enums import BooksyTeams
from django.utils.functional import cached_property
from rest_framework.exceptions import ValidationError
from rest_framework.mixins import (
    ListModelMixin,
)
from rest_framework.permissions import IsAuthenticated

from lib.db import using_db_for_reads, READ_ONLY_DB

from webapps.business.business_categories.service_suggestion import ServiceSuggestionUtil
from webapps.business.models import BusinessCategory
from webapps.public_partners.permissions import (
    PartnerFirewallPermission,
)
from webapps.public_partners.serializers.service_suggestion import (
    PAServiceSuggestionSerializer,
)
from webapps.public_partners.views.base import (
    GenericVersionPublicAPIViewSet,
)


class ServiceSuggestionViewSet(ListModelMixin, GenericVersionPublicAPIViewSet):
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    permission_classes = [IsAuthenticated, PartnerFirewallPermission]
    serializer_class = PAServiceSuggestionSerializer

    def get_queryset(self):
        if not {'country_code', 'category_id'} <= self.request.query_params.keys():
            raise ValidationError('\'country_code\' and \'category_id\' has to be specified')

        return ServiceSuggestionUtil.get_suggestions_by_category(category_ids=[self.category.id])

    @using_db_for_reads(READ_ONLY_DB)
    def list(self, request, *args, **kwargs):
        return super().list(request, args, kwargs)

    @cached_property
    @using_db_for_reads(READ_ONLY_DB)
    def category(self):
        return BusinessCategory.objects.get(id=self.request.query_params['category_id'])
