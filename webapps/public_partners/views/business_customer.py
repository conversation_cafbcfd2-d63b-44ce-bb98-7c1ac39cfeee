# pylint: skip-file
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.db.models import Prefetch
from rest_framework.permissions import IsAuthenticated

from webapps.business.models.bci import BusinessCustomerInfo
from webapps.public_partners.consts import STAFF_ACCESS_LEVELS_ALL, STAFF_ACCESS_LEVELS_MIN_ADVANCED
from webapps.public_partners.enum import PublicAPIVersionEnum
from webapps.public_partners.filters import (
    BusinessCustomerFilterBackend,
    BusinessCustomerListingFilterSet,
)
from webapps.public_partners.models import BusinessCustomerInfoMetadata
from webapps.public_partners.paginators import PaginationV02
from webapps.public_partners.permissions import (
    BusinessAccessPermission,
    OAuth2TokenMayHaveScopePermission,
    PartnerFirewallPermission,
    ResourceAccessPermission,
)
from webapps.public_partners.serializers import (
    PABusinessCustomerSerializerV01,
    PABusinessCustomerSerializerV02,
    PABusinessCustomerSerializerV03,
    PABusinessCustomerSerializerV04,
    PABusinessCustomerSerializerV06,
)
from webapps.public_partners.versioning import VersionSpec
from webapps.public_partners.views.base import (
    BulkImportModelMixin,
    BusinessRelatedViewMixin,
    FullModelMixin,
    GenericVersionPublicAPIViewSet,
    MappingModelMixin,
)


class BusinessCustomerViewSet(
    FullModelMixin,
    MappingModelMixin,
    BulkImportModelMixin,
    BusinessRelatedViewMixin,
    GenericVersionPublicAPIViewSet,
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    permission_classes = [
        IsAuthenticated,
        PartnerFirewallPermission,
        OAuth2TokenMayHaveScopePermission,
        BusinessAccessPermission,
        ResourceAccessPermission,
    ]
    versioned_oauth2_token_scopes = {
        VersionSpec(min_version=PublicAPIVersionEnum.V04): dict(
            list=[[settings.OAUTH2_BUSINESS_READ_SCOPE]],
            retrieve=[[settings.OAUTH2_BUSINESS_READ_SCOPE]],
            mapping=[[settings.OAUTH2_BUSINESS_READ_SCOPE]],
            default=[[settings.OAUTH2_BUSINESS_WRITE_SCOPE]],
        ),
    }
    versioned_resource_access_levels = {
        VersionSpec(min_version=PublicAPIVersionEnum.V04): dict(
            list=STAFF_ACCESS_LEVELS_ALL,
            retrieve=STAFF_ACCESS_LEVELS_ALL,
            mapping=STAFF_ACCESS_LEVELS_ALL,
            default=STAFF_ACCESS_LEVELS_MIN_ADVANCED,
        ),
    }
    action_versions_map = {
        'retrieve': VersionSpec(PublicAPIVersionEnum.V02),
        'list': VersionSpec(min_version=PublicAPIVersionEnum.V01),
        'create': VersionSpec(PublicAPIVersionEnum.V02),
        'update': VersionSpec(PublicAPIVersionEnum.V02),
        'partial_update': VersionSpec(PublicAPIVersionEnum.V02),
        'destroy': VersionSpec(PublicAPIVersionEnum.V02),
        'mapping': VersionSpec(PublicAPIVersionEnum.V02),
        'bulk_import': VersionSpec(min_version=PublicAPIVersionEnum.V04),
    }
    versioned_serializer_classes = {
        PublicAPIVersionEnum.V01: dict(default=PABusinessCustomerSerializerV01),
        PublicAPIVersionEnum.V02: dict(default=PABusinessCustomerSerializerV02),
        PublicAPIVersionEnum.V03: dict(default=PABusinessCustomerSerializerV03),
        PublicAPIVersionEnum.V04: dict(default=PABusinessCustomerSerializerV04),
        # currently use v0.4 serializers, change if needed
        PublicAPIVersionEnum.V05: dict(default=PABusinessCustomerSerializerV04),
        PublicAPIVersionEnum.V06: dict(default=PABusinessCustomerSerializerV06),
    }

    @property
    def pagination_class(self):
        if self._version == PublicAPIVersionEnum.V01:
            return None
        return PaginationV02

    @property
    def filterset_class(self):
        if self._version == PublicAPIVersionEnum.V01:
            return ()
        return BusinessCustomerListingFilterSet

    @property
    def filter_backends(self):
        if self._version == PublicAPIVersionEnum.V01:
            return ()
        return (BusinessCustomerFilterBackend,)  # noqa

    def get_queryset(self):
        return (
            BusinessCustomerInfo.objects.filter(
                visible_in_business=True,
                deleted__isnull=True,
                business__deleted__isnull=True,
                business__id=self.kwargs['business_pk'],
            )
            .prefetch_related(
                Prefetch(
                    'partner_app_data',
                    queryset=BusinessCustomerInfoMetadata.objects.filter(deleted__isnull=True),
                ),
                'first_appointment',
            )
            .order_by('created')
        )

    def perform_destroy(self, instance):
        instance.visible_in_business = False
        instance.soft_delete()
