# pylint: skip-file
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from rest_framework.permissions import IsAuthenticated
from rest_framework.mixins import (
    ListModelMixin,
    RetrieveModelMixin,
)

from lib.db import using_db_for_reads, READ_ONLY_DB

from webapps.business_related.models import ClaimLog
from webapps.public_partners.consts import (
    STAFF_ACCESS_LEVELS_ALL,
    STAFF_ACCESS_LEVELS_MIN_RECEPTION,
)
from webapps.public_partners.enum import PublicAPIVersionEnum
from webapps.public_partners.filters import PublicAPIBaseFilterBackend
from webapps.public_partners.filters.base import TimestampFilterSet
from webapps.public_partners.paginators import PaginationV02
from webapps.public_partners.permissions import (
    BusinessAccessPermission,
    OAuth2TokenMayHaveScopePermission,
    PartnerFirewallPermission,
    ResourceAccessPermission,
)
from webapps.public_partners.serializers import PAClaimLogSerializerV06
from webapps.public_partners.versioning import VersionSpec

from webapps.public_partners.views.base import (
    BusinessRelatedViewMixin,
    GenericVersionPublicAPIViewSet,
)


class CustomerClaimLogViewSet(
    ListModelMixin,
    RetrieveModelMixin,
    BusinessRelatedViewMixin,
    GenericVersionPublicAPIViewSet,
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    permission_classes = [
        IsAuthenticated,
        PartnerFirewallPermission,
        OAuth2TokenMayHaveScopePermission,
        BusinessAccessPermission,
        ResourceAccessPermission,
    ]
    versioned_oauth2_token_scopes = {
        VersionSpec(min_version=PublicAPIVersionEnum.V06): dict(
            default=[[settings.OAUTH2_BUSINESS_READ_SCOPE]]
        ),
    }
    versioned_resource_access_levels = {
        VersionSpec(min_version=PublicAPIVersionEnum.V06): dict(
            list=STAFF_ACCESS_LEVELS_ALL,
            default=STAFF_ACCESS_LEVELS_MIN_RECEPTION,
        ),
    }
    action_versions_map = {
        'list': VersionSpec(PublicAPIVersionEnum.V06),
        'retrieve': VersionSpec(PublicAPIVersionEnum.V06),
    }
    versioned_serializer_classes = {
        PublicAPIVersionEnum.V06: dict(default=PAClaimLogSerializerV06),
    }
    filterset_class = TimestampFilterSet
    filter_backends = (PublicAPIBaseFilterBackend,)
    pagination_class = PaginationV02

    def get_queryset(self):
        return ClaimLog.objects.select_related("target_bci__business").filter(
            target_bci__business__deleted__isnull=True,
            target_bci__business__pk=self.kwargs['business_pk'],
        )

    @using_db_for_reads(READ_ONLY_DB)
    def list(self, request, *args, **kwargs):
        return super().list(request, args, kwargs)

    @using_db_for_reads(READ_ONLY_DB)
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, args, kwargs)
