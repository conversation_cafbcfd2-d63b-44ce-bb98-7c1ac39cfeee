from collections import OrderedDict
from datetime import date, datetime, timedelta

from dateutil.relativedelta import relativedelta
from django.shortcuts import reverse
from freezegun import freeze_time
from model_bakery import baker
from rest_framework import status
from rest_framework.test import APITestCase

from lib.baker_utils import get_or_create_booking_source
from lib.test_utils import user_recipe
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import (
    business_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
)
from webapps.business.models import Resource
from webapps.consts import WEB
from webapps.user.enums import AuthOriginEnum
from webapps.wait_list.models import WaitList


class TestWaitlist(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.booking_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=WEB,
        )
        cls.headers = {'HTTP_X_API_KEY': cls.booking_source.api_key}
        cls.user = user_recipe.make()

    def setUp(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.client.credentials(HTTP_X_ACCESS_TOKEN=self.session.session_key)
        self.business = business_recipe.make()

    def test_waitlist_get(self):
        with freeze_time(datetime(2022, 9, 30, 15, 15, tzinfo=self.business.get_timezone())):
            service = service_recipe.make(business=self.business)
            service_variant = service_variant_recipe.make(
                service=service,
                duration=relativedelta(minutes=20),
            )

            staffer = staffer_recipe.make(
                business=self.business,
                staff_user=self.user,
                staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
            )
            staffer.add_services([service])

            wait_list_1 = baker.make(
                WaitList,
                business=self.business,
                service_variant=service_variant,
                date=date.today(),
                staffers=[staffer.id],
                date_exact=False,
                user=self.user,
            )

            wait_list_2 = baker.make(
                WaitList,
                business=self.business,
                service_variant=service_variant,
                date=date.today() + timedelta(days=1),
                staffers=[staffer.id],
                date_exact=False,
                user=self.user,
            )

            # This wait list is in the past, so it should not be returned
            baker.make(
                WaitList,
                business=self.business,
                service_variant=service_variant,
                date=date.today() - timedelta(days=1),
                staffers=[staffer.id],
                date_exact=False,
                user=self.user,
            )

            wait_list_1_dict = OrderedDict(
                [
                    ('id', wait_list_1.id),
                    (
                        'user',
                        OrderedDict(
                            [
                                ('id', self.user.id),
                                ('first_name', ''),
                                ('last_name', ''),
                                ('cell_phone', ''),
                                ('email', self.user.email),
                                ('photo_url', None),
                                ('cell_phone_local', ''),
                            ]
                        ),
                    ),
                    ('date', str(wait_list_1.date)),
                    ('date_exact', False),
                    (
                        'service_variant',
                        OrderedDict(
                            [
                                ('id', service_variant.id),
                                ('type', 'X'),
                                ('price', '15.00'),
                                ('duration', 20),
                                ('time_slot_interval', 15),
                                ('gap_hole_start_after', 0),
                                ('gap_hole_duration', 0),
                                ('no_show_protection', None),
                                ('formula', []),
                                ('label', service_variant.label),
                                ('combo_children', []),
                                ('combo_pricing', None),
                                ('service_price', '$15.00'),
                                ('staffers', [staffer.id]),
                            ]
                        ),
                    ),
                    (
                        'staffers',
                        [
                            OrderedDict(
                                [
                                    ('id', staffer.id),
                                    ('type', 'S'),
                                    ('name', staffer.name),
                                    ('active', True),
                                    ('visible', True),
                                    ('description', None),
                                    ('position', ''),
                                    ('staff_user_exists', True),
                                    ('is_invited', False),
                                ]
                            ),
                        ],
                    ),
                ]
            )
            wait_list_2_dict = OrderedDict(
                [
                    ('id', wait_list_2.id),
                    (
                        'user',
                        OrderedDict(
                            [
                                ('id', self.user.id),
                                ('first_name', ''),
                                ('last_name', ''),
                                ('cell_phone', ''),
                                ('email', self.user.email),
                                ('photo_url', None),
                                ('cell_phone_local', ''),
                            ]
                        ),
                    ),
                    ('date', str(wait_list_2.date)),
                    ('date_exact', False),
                    (
                        'service_variant',
                        OrderedDict(
                            [
                                ('id', service_variant.id),
                                ('type', 'X'),
                                ('price', '15.00'),
                                ('duration', 20),
                                ('time_slot_interval', 15),
                                ('gap_hole_start_after', 0),
                                ('gap_hole_duration', 0),
                                ('no_show_protection', None),
                                ('formula', []),
                                ('label', service_variant.label),
                                ('combo_children', []),
                                ('combo_pricing', None),
                                ('service_price', '$15.00'),
                                ('staffers', [staffer.id]),
                            ]
                        ),
                    ),
                    (
                        'staffers',
                        [
                            OrderedDict(
                                [
                                    ('id', staffer.id),
                                    ('type', 'S'),
                                    ('name', staffer.name),
                                    ('active', True),
                                    ('visible', True),
                                    ('description', None),
                                    ('position', ''),
                                    ('staff_user_exists', True),
                                    ('is_invited', False),
                                ]
                            )
                        ],
                    ),
                ]
            )
            url = reverse('wait_list', args=(self.business.id,))
            response = self.client.get(url, **self.headers)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(len(response.data), 2)
            self.assertDictEqual(response.data[0], wait_list_1_dict)
            self.assertDictEqual(response.data[1], wait_list_2_dict)

    def test_waitlist_get_multiple_businesses(self):
        with freeze_time(datetime(2022, 9, 30, 15, 15, tzinfo=self.business.get_timezone())):
            # first business
            service = service_recipe.make(business=self.business)
            service_variant = service_variant_recipe.make(
                service=service,
                duration=relativedelta(minutes=20),
            )

            staffer = staffer_recipe.make(
                business=self.business,
                staff_user=self.user,
                staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
            )
            staffer.add_services([service])

            wait_list_1 = baker.make(
                WaitList,
                business=self.business,
                service_variant=service_variant,
                date=date.today(),
                staffers=[staffer.id],
                date_exact=False,
                user=self.user,
            )

            # second business
            business_2 = business_recipe.make()
            service_2 = service_recipe.make(business=business_2)
            service_variant_2 = service_variant_recipe.make(
                service=service_2,
                duration=relativedelta(minutes=20),
            )

            staffer_2 = staffer_recipe.make(
                business=business_2,
                staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
            )
            staffer_2.add_services([service_2])

            wait_list_2 = baker.make(
                WaitList,
                business=business_2,
                service_variant=service_variant_2,
                date=date.today() + timedelta(days=1),
                staffers=[staffer_2.id],
                date_exact=False,
                user=self.user,
            )

            url = reverse('wait_list', args=(self.business.id,))
            response = self.client.get(url, **self.headers)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(len(response.data), 1)
            self.assertEqual(response.data[0]['id'], wait_list_1.id)

            url = reverse('wait_list', args=(business_2.id,))
            response = self.client.get(url, **self.headers)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(len(response.data), 1)
            self.assertEqual(response.data[0]['id'], wait_list_2.id)

    def test_waitlist_get_last_day(self):
        with freeze_time(datetime(2022, 9, 30, 15, 15, tzinfo=self.business.get_timezone())):
            service = service_recipe.make(business=self.business)
            service_variant = service_variant_recipe.make(
                service=service,
                duration=relativedelta(minutes=20),
            )

            staffer = staffer_recipe.make(
                business=self.business,
                staff_user=self.user,
                staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
            )
            staffer.add_services([service])
            end_date = date.today() + timedelta(days=3)
            wait_list = baker.make(
                WaitList,
                business=self.business,
                service_variant=service_variant,
                date=end_date,
                staffers=[staffer.id],
                date_exact=False,
                user=self.user,
            )

            wait_list_dict = OrderedDict(
                [
                    ('id', wait_list.id),
                    (
                        'user',
                        OrderedDict(
                            [
                                ('id', self.user.id),
                                ('first_name', ''),
                                ('last_name', ''),
                                ('cell_phone', ''),
                                ('email', self.user.email),
                                ('photo_url', None),
                                ('cell_phone_local', ''),
                            ]
                        ),
                    ),
                    ('date', str(wait_list.date)),
                    ('date_exact', False),
                    (
                        'service_variant',
                        OrderedDict(
                            [
                                ('id', service_variant.id),
                                ('type', 'X'),
                                ('price', '15.00'),
                                ('duration', 20),
                                ('time_slot_interval', 15),
                                ('gap_hole_start_after', 0),
                                ('gap_hole_duration', 0),
                                ('no_show_protection', None),
                                ('formula', []),
                                ('label', service_variant.label),
                                ('combo_children', []),
                                ('combo_pricing', None),
                                ('service_price', '$15.00'),
                                ('staffers', [staffer.id]),
                            ]
                        ),
                    ),
                    (
                        'staffers',
                        [
                            OrderedDict(
                                [
                                    ('id', staffer.id),
                                    ('type', 'S'),
                                    ('name', staffer.name),
                                    ('active', True),
                                    ('visible', True),
                                    ('description', None),
                                    ('position', ''),
                                    ('staff_user_exists', True),
                                    ('is_invited', False),
                                ]
                            ),
                        ],
                    ),
                ]
            )
            data_dict = {
                'start_date': datetime.today().strftime("%Y-%m-%d"),
                'end_date': end_date.strftime("%Y-%m-%d"),
            }
            url = reverse('wait_list', args=(self.business.id,))
            response = self.client.get(url, **self.headers, data=data_dict)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(len(response.data), 1)
            self.assertDictEqual(response.data[0], wait_list_dict)
