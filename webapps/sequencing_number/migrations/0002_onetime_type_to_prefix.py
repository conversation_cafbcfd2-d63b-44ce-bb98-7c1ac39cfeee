# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2019-04-26 07:15
from django.db import migrations


SEQUENCE_RECORD_SQL = """
    UPDATE sequencing_number_sequencerecord record
        SET prefix = CASE
            WHEN record.prefix IS NULL or record.prefix = '' THEN
                'FV'
            ELSE
                CONCAT('FV/', prefix)
            END
    ;
"""


SEQUENCE_RECORD_SETTINGS_SQL = """
    UPDATE sequencing_number_sequencerecordssettings settings
        SET prefix = CASE
            WHEN settings.prefix IS NULL or settings.prefix = '' THEN
                'FV'
            ELSE
                CONCAT('FV/', prefix)
            END
    ;
"""


class Migration(migrations.Migration):

    dependencies = [
        ('sequencing_number', '0001_initial'),
    ]

    operations = [
        migrations.RunSQL(
            sql=SEQUENCE_RECORD_SQL,
            reverse_sql=migrations.RunSQL.noop,
        ),
        migrations.RunSQL(
            sql=SEQUENCE_RECORD_SETTINGS_SQL,
            reverse_sql=migrations.RunSQL.noop,
        ),
    ]
