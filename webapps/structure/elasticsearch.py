import math
from django.conf import settings
from django.db.models import Q, Count
import elasticsearch_dsl as dsl
from rest_framework import serializers

from settings.elasticsearch import ES_SUFFIX

from lib.elasticsearch import analyzers as a
from lib.elasticsearch import fields as f
from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.document import Document
from lib.elasticsearch.index import Index

from webapps.structure.models import (
    Region,
    MAGIC_SCORE,
)
from webapps.structure.constants import REGION_TYPES_WITH_BUSINESS_SCORE


class RegionDocumentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Region
        fields = [
            'id',
            'name',
            'full_name',
            'slug',
            'boost_phrase',
            'latitude',
            'longitude',
            'show_county',
            'type',
        ] + [
            'search_name',
            'location',
            'city',
            'city_full_name',
            'county',
            'neighborhood',
            'parents',
            'canonical_parent',
            'canonical_children',
            'suggest_regions',
            'businesses_count',
        ]

    search_name = serializers.SerializerMethodField()
    location = serializers.SerializerMethodField()
    city = serializers.SerializerMethodField()
    city_full_name = serializers.SerializerMethodField()
    county = serializers.SerializerMethodField()
    neighborhood = serializers.SerializerMethodField()
    parents = serializers.SerializerMethodField()
    canonical_parent = serializers.IntegerField(source='get_canonical_parent_id')
    canonical_children = serializers.ListField(
        child=serializers.IntegerField(), source='get_canonical_children_ids'
    )
    suggest_regions = serializers.SerializerMethodField()
    businesses_count = serializers.IntegerField()

    @staticmethod
    def get_search_tokens(instance):
        """For non english name region will created search tokens that will
        be used in street_hints/resolve.

        :param instance: Region
        :return: set. Of token for region.name
        """

        index = RegionIndex()
        tokens = set()
        for analyzer in list(settings.ES_TEXT_MULTI_FIELDS.keys()):
            analyzed_results = index.analyze_text(instance.name, analyzer)
            for result in analyzed_results.get('tokens', []):
                if 'token' in result:
                    tokens.add(result['token'])
        return tokens

    def get_search_name(self, instance):
        search_names = {
            f
            for f in [
                instance.name,
                instance.get_full_name(),
                instance.get_full_name(with_show_county=False),
                instance.get_full_name(state_abbrev=False),
                instance.get_full_name(force_state_name=True),
                instance.get_full_name(force_state_name=True, state_abbrev=False),
                instance.boost_phrase,
                instance.abbrev,
                (instance.name.replace('-', '') if instance.type == Region.Type.ZIP else None),
            ]
            if f
        }
        # experimental
        search_tokens = set()
        if instance.type != Region.Type.ZIP:
            search_tokens = self.get_search_tokens(instance)
        # get union of sets
        return list(search_names | search_tokens)

    @staticmethod
    def get_location(instance):
        if instance.latitude is not None and instance.longitude is not None:
            return {'lat': instance.latitude, 'lon': instance.longitude}
        return None

    def get_suggest_regions(self, instance):
        if instance.type not in settings.ES_SUGGESTION_REGION_TYPES:
            return {}
        weight = {'city': 20, 'neighborhood': 10, 'metropolis': 30, 'zip': 20}.get(instance.type, 0)
        search_names = self.get_search_name(instance)
        return {
            'input': search_names,
            # 'output': instance.full_name,
            'weight': weight,
            # 'payload': payload,
        }

    @staticmethod
    def get_parents(instance):
        return [r.id for r in instance.get_parents()]

    @staticmethod
    def _get_parent_attr(instance, type_, attr):
        parent = instance.get_parent_by_type(type_)
        return getattr(parent, attr) if parent else None

    def get_city(self, instance):
        return self._get_parent_attr(instance, settings.ES_CITY_LVL, 'name')

    def get_city_full_name(self, instance):
        return self._get_parent_attr(
            instance,
            settings.ES_CITY_LVL,
            'full_name',
        )

    def get_county(self, instance):
        return self._get_parent_attr(instance, settings.ES_ADM_2_LVL, 'name')

    def get_neighborhood(self, instance):
        return self._get_parent_attr(
            instance,
            settings.ES_NEIGHBORHOOD_LVL,
            'name',
        )


class RegionDocument(Document):
    class Meta:
        serializer = RegionDocumentSerializer
        queryset = Region.objects.annotate(businesses_count=Count('businesses'))
        es_bulk_size = 50

    name = f.region_multi
    full_name = f.region_multi
    slug = dsl.Text(index=False)
    search_name = f.region_multi
    boost_phrase = f.region_multi
    abbrev = dsl.Text(index=False)
    location = dsl.GeoPoint(index=True)
    timezone_short = dsl.Text(index=False)
    timezone_long = dsl.Text(index=False)
    parents = dsl.Integer(index=True)
    type = dsl.Keyword()
    show_county = dsl.Boolean(index=True)
    city = dsl.Text(index=False)
    city_full_name = dsl.Text(index=False)
    county = dsl.Text(index=False)
    district = dsl.Text(index=False)
    canonical_parent = dsl.Integer(index=True)
    canonical_children = dsl.Integer(index=True)
    # suggests:
    suggest_regions = dsl.Completion(
        # simple analyzer strips out numbers
        # (but we'd like completion for zipcodes)
        analyzer=a.icu_folded_lowercase,  # a.standard,
        # 'payloads': True,
        # this will find New York for suggest "newy"
        preserve_separators=False,
    )
    businesses_count = dsl.Integer(index=True)
    count_business_score = dsl.Float(index=True)

    @classmethod
    def filter_updated(cls, qs, last_updated):  # pylint: disable=unused-argument
        # not supported
        return qs.none()


class BusinessScorePartialDocument(Document):
    '''
    Partial Document

    Update count_business_score of RegionDocument

    Warning:
    if last_updated param is used, it is applied to business.created
    not region.updated
    '''

    class Meta:
        es_bulk_size = 100
        model = Region
        queryset = Region.objects.filter(
            type__in=REGION_TYPES_WITH_BUSINESS_SCORE,
        )

    @classmethod
    def filter_updated(cls, qs, last_updated):
        return qs.filter(
            Q(businesses__created__gte=last_updated)
            | Q(immediate_children__businesses__created__gte=last_updated)
            | Q(immediate_children__immediate_children__businesses__created__gte=last_updated)
        )

    @classmethod
    def get_queryset(cls, ids=None):
        index = RegionDocument._doc_type.index  # pylint: disable=protected-access
        search = dsl.Search(index=index, doc_type=index).params(size=0)

        for id_ in ids:
            search.aggs.bucket(
                str(id_),
                dsl.aggs.Filter(dsl.query.Term(parents=id_)),
            ).bucket(
                'businesses',
                dsl.aggs.Sum(field='businesses_count'),
            )
        res = search.execute()
        return res.aggs.to_dict()

    @classmethod
    def generate_actions(cls, queryset, progress, **kwargs):
        for rid, aggs in list(queryset.items()):
            businesses_count = aggs['businesses']['value']
            score = MAGIC_SCORE * math.log1p(businesses_count)

            doc = {'count_business_score': score}
            if progress is not None:
                progress.inc()

            yield {
                '_op_type': 'update',
                '_id': f'region:{rid}',
                '_index': cls._doc_type.index,
                # '_type': cls._doc_type.index,
                'doc': doc,
            }

    @classmethod
    def delete_extra(cls, ids):
        pass


class RegionIndex(Index):
    no_suffix_name = 'region_index'
    name = f'{no_suffix_name}_{ES_SUFFIX}'
    documents = {
        ESDocType.REGION: RegionDocument,
        ESDocType.REGION_BUSINESS_SCORE_PARTIAL: BusinessScorePartialDocument,
    }
    index_settings = {
        'max_ngram_diff': 20,
    }
