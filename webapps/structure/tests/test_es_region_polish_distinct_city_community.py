import pytest

from lib.elasticsearch.consts import ESDocType
from lib.geocoding.here_maps import _ADDRESS_FIELD_TO_REGION_TYPE_MAPPING
from settings.es_countries.languages import (
    A_POLISH_ICU,
)
from webapps.structure.elasticsearch import RegionDocument
from webapps.structure.models import Region
from webapps.structure.searchables import RegionCoordinateNameTypeSearchable

region_types_mapping = _ADDRESS_FIELD_TO_REGION_TYPE_MAPPING


@pytest.fixture(scope='module', autouse=True)
def _create_regions(new_polish_region_index_module_fixture):
    index = new_polish_region_index_module_fixture
    regions = (
        dict(
            full_name='Bielawa, powiat wrocławski, dolnośląski',
            latitude=51.1611111119624,
            longitude=17.2519444449736,
            name='Bielawa',
            show_county=True,
            boost_phrase='',
            _type=Region.Type.VILLAGE,
        ),
        dict(
            full_name='Bielawa',
            latitude=50.6753897775235,
            longitude=16.6071926434626,
            name='Bielawa',
            show_county=False,
            boost_phrase='',
            _type=Region.Type.COMMUNITY,
        ),
    )
    for _id, region_info in enumerate(regions, 1):
        full_name = region_info['full_name']
        analyze_result = index.analyze_text(full_name, A_POLISH_ICU)
        search_name = [t['token'] for t in analyze_result.get('tokens', []) if 'token' in t]
        RegionDocument(
            _id=f'region:{_id}',
            id=_id,
            full_name=full_name,
            search_name=search_name,
            location=dict(lat=region_info['latitude'], lon=region_info['longitude']),
            type=region_info['_type'],
            boost_phrase=region_info['boost_phrase'],
            # not important for this test case
            canonical_parent=[],
            canonical_children=[],
        ).save(refresh=True)
    return index


@pytest.mark.parametrize(
    'data, expected_id',
    [
        (
            {
                'main_query': 'Bielawa',
                'location_geo': '50.69158,16.62356',
                'main_type': 'city',
                'regions_query_type': [
                    {
                        'query': 'Bielawa',
                        'types': region_types_mapping.get('city'),
                        'weight': 100.0,
                    },
                    {
                        'query': 'Powiat Dzierżoniowski',
                        'types': region_types_mapping.get('county'),
                        'weight': 52.5,
                    },
                    {
                        'query': 'Woj. Dolnośląskie',
                        'types': region_types_mapping.get('state'),
                        'weight': 35.0,
                    },
                ],
            },
            2,
        ),
        (
            {
                'main_query': 'Bielawa, Długołęka',
                'location_geo': '51.16073,17.25235',
                'main_type': 'neighborhood',
                'regions_query_type': [
                    {
                        'query': 'Bielawa',
                        'types': region_types_mapping.get('district'),
                        'weight': 100.0,
                    },
                    {
                        'query': 'Długołęka',
                        'types': region_types_mapping.get('city'),
                        'weight': 56.0,
                    },
                    {
                        'query': 'Powiat Wrocławski',
                        'types': region_types_mapping.get('county'),
                        'weight': 42.0,
                    },
                    {
                        'query': 'Woj. Dolnośląskie',
                        'types': region_types_mapping.get('state'),
                        'weight': 28.0,
                    },
                ],
            },
            1,
        ),
    ],
)
def test_es_region_polish_community_and_village(data, expected_id):
    searchable = RegionCoordinateNameTypeSearchable(ESDocType.REGION)
    resp = searchable.params(
        size=1,
    ).execute(data)
    assert resp.hits, 'Hits can\'t be empty'
    assert resp.hits[0].id == expected_id, f'Data: {data}'
