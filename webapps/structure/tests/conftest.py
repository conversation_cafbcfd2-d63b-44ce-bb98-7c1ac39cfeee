import pytest

from conftest_helpers import (
    new_es_index_factory,
    update_es_index_factory,
)
from country_config import Country
from lib.elasticsearch.consts import ESIndex
from settings.es_countries.languages import ENGLISH, POLISH, SPANISH
from webapps.structure.tests.us_regions import create_us_regions


@pytest.fixture(scope='module')
def create_us_region_module_fixture(clean_index_module_fixture):
    index = clean_index_module_fixture(ESIndex.REGION)
    create_us_regions(index)
    return index


updated_us_region_index_module_fixture = update_es_index_factory(
    ESIndex.REGION,
    country=Country.US.value,
    lang_code=ENGLISH,
    name='updated_us_region_index_module_fixture',
    scope='module',
)

new_polish_region_index_module_fixture = new_es_index_factory(
    ESIndex.REGION,
    country=Country.PL.value,
    lang_code=POLISH,
    name='new_polish_region_index_module_fixture',
    scope='module',
)

new_british_region_index_module_fixture = new_es_index_factory(
    ESIndex.REGION,
    country=Country.GB.value,
    lang_code=ENGLISH,
    name='new_british_region_index_module_fixture',
    scope='module',
)

new_spanish_region_index_module_fixture = new_es_index_factory(
    ESIndex.REGION,
    country=Country.ES.value,
    lang_code=SPANISH,
    name='new_spanish_region_index_module_fixture',
    scope='module',
)
