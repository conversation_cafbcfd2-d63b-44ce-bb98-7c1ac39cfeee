import pytest

from lib.elasticsearch.consts import ESDocType
from settings.es_countries.languages import (
    A_POLISH_ICU,
)
from webapps.structure.elasticsearch import RegionDocument
from webapps.structure.models import Region
from webapps.structure.searchables import RegionCoordinateNameTypeSearchable


@pytest.fixture(scope='module', autouse=True)
def _create_regions(new_polish_region_index_module_fixture):
    index = new_polish_region_index_module_fixture
    regions = (
        # 1
        ('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', (51.107883, 17.038538)),
        ('<PERSON>jawsko-pomorskie ', (53.0561240505, 18.4877489147)),
        ('<PERSON><PERSON>ski<PERSON> ', (51.2697939699, 22.7786393001)),
        # 3
        ('<PERSON><PERSON><PERSON> ', (52.2434789565, 15.2815110466)),
        ('<PERSON><PERSON><PERSON><PERSON><PERSON>', (51.6187605185, 19.393601536)),
        ('<PERSON><PERSON><PERSON><PERSON><PERSON>', (49.8495067365, 20.2654486642)),
        # 7
        ('<PERSON><PERSON><PERSON><PERSON><PERSON>', (52.2475868262, 21.5626632801)),
        ('<PERSON><PERSON><PERSON>', (50.5836703952, 17.8509962716)),
        ('<PERSON>d<PERSON>pack<PERSON>', (49.9111714495, 22.1785985666)),
        ('<PERSON>dła<PERSON>e', (53.3455984743, 22.7090434116)),
        # 11
        ('<PERSON>mor<PERSON>e', (54.1634348233, 18.0009322453)),
        ('<PERSON>l<PERSON><PERSON>e', (50.2467279861, 18.8463820655)),
        ('Świętokrzyskie', (50.7638863859, 20.7985798653)),
        ('Warmińsko-mazurskie', (53.7961156565, 21.1038513442)),
        # 15
        ('Wielkopolskie', (52.37976994, 17.3140482862)),
        ('Zachodniopomorskie', (53.5977882429, 15.5280951726)),
    )
    for _id, region in enumerate(regions, 1):
        full_name, location = region
        analyze_result = index.analyze_text(full_name, A_POLISH_ICU)
        search_name = [t['token'] for t in analyze_result.get('tokens', []) if 'token' in t]
        RegionDocument(
            _id='region:{}'.format(_id),
            id=_id,
            full_name=full_name,
            search_name=search_name,
            location=dict(lat=location[0], lon=location[1]),
            type=Region.Type.STATE,
            # not important for this test case
            canonical_parent=[],
            canonical_children=[],
        ).save()

    index.refresh()
    index.save()


@pytest.mark.parametrize(
    'data, expected',
    [
        (
            dict(
                # simple test for existing region with unicode in name
                # polish analyzer to test
                regions_query_type=[
                    dict(
                        query='Śląskie',  # 'slaskie', 'slaski'
                        types=[Region.Type.STATE],
                        weight=10,
                    )
                ]
            ),
            [12],
        ),
        (
            dict(
                # test with non existing token 'woj'
                main_query='Woj. Dolnośląskie',
                regions_query_type=[
                    dict(
                        query='Woj. Dolnośląskie',
                        types=[Region.Type.STATE],
                        weight=10,
                    )
                ],
            ),
            [1],
        ),
        (
            dict(
                # 30 km from center of Woj. Mazowieckie
                main_query='Woj. Mazowieckie',
                location_geo='52.2356,21.01038',
                regions_query_type=[
                    dict(
                        query='Mazowieckie',
                        types=[Region.Type.STATE],
                        weight=10,
                    )
                ],
            ),
            [7],
        ),
        (
            # empty test for non existing region
            dict(
                regions_query_type=[
                    dict(
                        query='Lub',
                        types=[Region.Type.STATE],
                    )
                ]
            ),
            [],
        ),
        (
            dict(
                # mismatch test. location_geo
                # from Jesionki not from Gorzów Wielkopolski
                location_geo='52.246896, 15.217891',
                regions_query_type=[
                    dict(
                        query='Lubuskie',
                        types=[Region.Type.STATE],
                    )
                ],
            ),
            [4],
        ),
        (
            dict(
                # empty test again
                main_query='polskie',
                regions_query_type=[
                    dict(
                        query='polskie',
                        types=[Region.Type.STATE],
                    )
                ],
            ),
            [],
        ),
        (
            dict(
                # mismatch query
                main_query='polskie',
                regions_query_type=[
                    dict(
                        query='Opolskie',
                        types=[Region.Type.STATE],
                    )
                ],
            ),
            [8],
        ),
        (
            dict(
                main_query='Podłaskie',
                location_geo='53.3455984743,22.7090434116',
                regions_query_type=[
                    dict(
                        query='Podłaskie',
                        types=[Region.Type.STATE],
                    )
                ],
            ),
            [10],
        ),
    ],
)
def test_es_region_polish_state(data, expected):
    searchable = RegionCoordinateNameTypeSearchable(ESDocType.REGION)
    resp = searchable.params(explain=True).execute(data)
    assert [h.id for h in resp.hits] == expected, 'Data: {}'.format(data)
