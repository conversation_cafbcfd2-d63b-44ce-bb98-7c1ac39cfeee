from django.urls import reverse

from webapps.admin_extra.tests import DjangoTestCase
from webapps.structure.models import Region


class TestEditZipcode(DjangoTestCase):
    def setUp(self):
        self.parent = Region.objects.create(
            name='Ohio',
            type=Region.Type.STATE,
            latitude=71,
            longitude=69,
        )
        self.region = Region.objects.create(
            name='60700',
            type=Region.Type.ZIP,
            latitude=70,
            longitude=70,
        )
        self.region.immediate_parents.set([self.parent])

    @staticmethod
    def _get_url(region_id: int):
        return reverse('admin:structure_region_change', args=(region_id,))

    def test_edit_old_region_as_superuser(self):
        self.login_admin()

        response = self.client.post(
            self._get_url(self.region.id),
            data={
                'type': Region.Type.ZIP,
                'latitude': 60,
                'longitude': 50,
                'time_zone_name': 'America/Los_Angeles',
                'immediate_parents': [parent.id for parent in self.region.immediate_parents.all()],
            },
            follow=True,
        )

        # no validation errors
        self.assertRedirects(response, reverse('admin:structure_region_changelist'))

        self.region.refresh_from_db()

        self.assertEqual(self.region.latitude, 60)
        self.assertEqual(self.region.longitude, 50)
        self.assertEqual(self.region.time_zone_name, 'America/Los_Angeles')
