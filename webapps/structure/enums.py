from django.utils.translation import gettext_lazy as _

from lib.enums import StrChoicesEnum


class RegionType(StrChoicesEnum):
    COUNTRY = 'country', _('Country')
    STATE = 'state', _('State')
    REGION = 'region', _('Region')
    VOIVODESHIP = 'voivodeship', _('Voivodeship')
    METROPOLIS = 'metropolis', _('Metropolis')
    COUNTY = 'county', _('County')
    COMMUNITY = 'community', _('Community')
    CITY = 'city', _('City')
    BOROUGH = 'borough', _('Borough')
    NEIGHBORHOOD = 'neighborhood', _('Neighborhood')
    VILLAGE = 'village', _('Village')
    ZIP = 'zip', _('ZIP')


REGION_TYPES_WITH_BUSINESS_SCORE = {
    RegionType.CITY,
    RegionType.NEIGHBORHOOD,
}
REGION_TYPES = [code for code, name in RegionType.choices()]
REGION_ORDER = {code: i for i, code in enumerate(REGION_TYPES)}
