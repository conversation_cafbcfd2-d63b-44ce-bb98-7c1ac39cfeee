import datetime
from typing import Optional

from django.utils.translation import gettext as _

from lib.feature_flag.feature import CancellationFeeFullAmountAuthorizationFlag
from lib.payment_providers.consts import APPLE_PAY_NATIVE_TOKEN
from lib.payment_providers.entities import (
    Adyen3dsAuthAdditionalDataEntity,
    AdyenAuthAdditionalDataEntity,
    AdyenBasketItemEntity,
    AuthAdditionalDataEntity,
    AuthorizePaymentMethodDataEntity,
    BooksyGiftCardsAuthAdditionalDataEntity,
    DeviceDataEntity,
    FraudPreventionAuthAdditionalDataEntity,
)
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import BasketPaymentAnalyticsTrigger
from lib.pos.utils import txn_refactor_stage2_enabled
from lib.smartlock import SmartLock
from lib.tools import tznow, sget_v2
from webapps.adyen.typing import DeviceDataDict
from webapps.payment_providers.ports.payment_ports import PaymentProvidersPaymentPort
from webapps.point_of_sale.adapters import (
    get_anonymous_wallet_id_adapter,
    get_business_wallet_id_adapter,
    get_customer_wallet_id_adapter,
)
from webapps.point_of_sale.exceptions import BasketPaymentNotFound
from webapps.point_of_sale.models import (
    BasketPayment,
    CancellationFeeAuth,
)
from webapps.point_of_sale.services.basket_payment import BasketPaymentService
from webapps.point_of_sale.services.basket_payment_analytics import BasketPaymentAnalyticsService
from webapps.point_of_sale.services.cancellation_fee_auth import CancellationFeeAuthService
from webapps.point_of_sale.services.cancellation_fee_auth_analytics import (
    CancellationFeeAuthAnalyticsService,
)
from webapps.pos.enums import PaymentTypeEnum, receipt_status, CARD_TYPE__KEYED_IN_PAYMENT
from webapps.pos.exceptions import InvalidPaymentMethod
from webapps.pos.models import (
    PaymentMethod,
    PaymentRow,
    PaymentRowChange,
    Transaction,
)
from webapps.pos.provider.base import PaymentProviderError
from webapps.pos.refund import is_refund_possible
from webapps.pos.services import (
    PaymentRowService,
    TransactionService,
)
from webapps.register.models import Register
from webapps.stripe_integration.services import SynchronizeService
from webapps.user.models import User

# for these payment methods initialize_payment, authorize it as well
_PAYMENT_METHODS_DO_NOT_NEED_AUTHORIZATION = {CARD_TYPE__KEYED_IN_PAYMENT}


def get_auth_additional_data(
    transaction: Transaction,
    device_data: DeviceDataDict | None = None,
    extra_data: dict | None = None,
    fraud_prevention: Optional[FraudPreventionAuthAdditionalDataEntity] = None,
) -> AuthAdditionalDataEntity:
    extra_data = extra_data if extra_data else {}
    device_data = device_data if device_data else {}

    def get_amount(value):
        from webapps.adyen.helpers import float_amount_to_cents

        return float_amount_to_cents(
            {
                'currency': transaction.currency_symbol,
                'value': value,
            }
        )['value']

    basket_items = []
    for row in transaction.rows.all():
        if row.type in [row.TRANSACTION_ROW_TYPE__SERVICE, row.TRANSACTION_ROW_TYPE__DEPOSIT]:
            service_variant = (
                row.service_variant or row.subbooking and row.subbooking.service_variant
            )
            item_id = service_variant.id if service_variant else row.subbooking_id
            item_title = (
                service_variant
                and service_variant.service.name
                or row.subbooking
                and row.subbooking.service_name
                or row.name_line_1
            )
        elif row.type == row.TRANSACTION_ROW_TYPE__PRODUCT:
            item_id = row.product_id
            item_title = row.product.name
        elif row.type == row.TRANSACTION_ROW_TYPE__VOUCHER:
            item_id = row.voucher_id
            item_title = row.voucher.voucher_template.name
        else:
            item_id = None
            item_title = row.name_line_1

        basket_items.append(
            AdyenBasketItemEntity(
                item_id=item_id,
                product_title=item_title,
                amount_per_item=get_amount(row.item_price),
                currency=row.transaction.currency_symbol,
                quantity=row.quantity,
                manufacturer=row.transaction.pos.business_id,
                product_type=row.type,
                total_amount=get_amount(row.total),
                unit_of_measure='BX',
            )
        )

    return AuthAdditionalDataEntity(
        adyen=AdyenAuthAdditionalDataEntity(
            device_data=DeviceDataEntity(
                device_fingerprint=device_data.get('fingerprint', ''),
                phone_number=device_data.get('phone_number', ''),
                user_agent=device_data.get('user_agent', ''),
                ip=extra_data.get('cardholder_ip', ''),
            ),
            basket_items=basket_items,
            recurring_model=TransactionService.get_adyen_recurring_model(transaction),
            total_tax_amount=get_amount(sum(s.tax_amount for s in transaction.tax_subtotals.all())),
            order_date=transaction.charge_date.strftime('%d%m%y'),
            user_v1_id=None,
        ),
        booksy_gift_cards=BooksyGiftCardsAuthAdditionalDataEntity(
            user_id=transaction.customer_id,
        ),
        fraud_prevention=fraud_prevention,
    )


def synchronize_adyen_payment(basket_payment: 'BasketPayment'):
    if basket_payment.payment_provider_code != PaymentProviderCode.ADYEN:
        return
    payment_row = PaymentRow.objects.filter(basket_payment_id=basket_payment.id).last()
    payment_row.marketpay_splits = payment_row.get_marketpay_splits()
    payment_row.save(update_fields=['marketpay_splits'])


class ProxyProvider:
    @staticmethod
    def make_capture(  # pylint: disable=too-many-arguments
        payment_row: 'PaymentRow',
        *args,
        trigger: BasketPaymentAnalyticsTrigger | None = None,
        device_data: DeviceDataDict = None,
        extra_data: dict = None,
        **kwargs,
    ):
        """Method caputres payment. Used in finalizing 3ds scenario or during accepting
        Prepayment in semi-auto businesses (Authorization is done during booking process)
        """
        with SmartLock(key=f'transaction_id:{payment_row.receipt.transaction_id}'):
            basket_payment = BasketPayment.objects.get(id=payment_row.basket_payment_id)
            wallet_id = get_customer_wallet_id_adapter(basket_payment.user_id)

            BasketPaymentService.capture_payment(basket_payment=basket_payment, wallet_id=wallet_id)

            if device_data and extra_data and trigger:
                BasketPaymentAnalyticsService.create_analytics_data(
                    basket_payment=basket_payment,
                    device_data=DeviceDataEntity(
                        device_fingerprint=device_data.get('fingerprint', ''),
                        phone_number=device_data.get('phone_number', ''),
                        user_agent=device_data.get('user_agent', ''),
                        ip=extra_data.get('cardholder_ip', ''),
                    ),
                    trigger=trigger,
                    payment_link=payment_row.payment_link,
                )

    @staticmethod
    def create_analytics_data(basket_payment, payment_row, trigger, device_data=None):
        BasketPaymentAnalyticsService.create_analytics_data(
            basket_payment=basket_payment,
            device_data=device_data,
            trigger=trigger,
            payment_link=payment_row.payment_link,
        )

    @staticmethod
    def make_payment(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        transaction: Transaction,
        payment_method: PaymentMethod,
        payment_row: PaymentRow,
        device_data: DeviceDataDict = None,
        extra_data: dict = None,
        trigger: BasketPaymentAnalyticsTrigger | None = None,
        fraud_prevention: Optional[FraudPreventionAuthAdditionalDataEntity] = None,
    ):
        """
        Methods initializes payment in PaymentGateway and PaymentProviders and start
        process of charging it. Initialization is done when we are sure transaction won't change
        anymore. So for example during regular payment for appointment:
        1. Business initializes Transaction - it creates Payment Row and Basket Payment.
        2. Customer open his app, and can change tip - these changes needs to be propagated only to
            Transaction, PaymentRows and BasketPayment.
        3. Customer clicks PAY. Payment is initialized in PaymentGateway and PaymentProviders.

        :param transaction: Transaction object we want to charge. Used to prepare additional_info
            about transaction for adyen (Security)
        :param payment_method: PaymentMethod which is used by customer
        :param payment_row: PaymentRow object we want to charge.
        :param device_data: Adyen related object
        :param extra_data: Adyen related object
        :param trigger: Scenario which triggered make_payment
        :param fraud_prevention: Info for the fraud system, might be used later to block a transaction
        :return:
        """
        with SmartLock(key=f'transaction_id:{transaction.id}'):
            TransactionService.get_fees_and_initialize_payment(
                pr=payment_row,
                payment_method=payment_method,
            )

            basket_payment_id = payment_row.basket_payment_id
            basket_payment = BasketPayment.objects.get(id=basket_payment_id)

            SynchronizeService.synchronize_stripe_payment_intent(basket_payment)

            payment_token = payment_method.token if hasattr(payment_method, 'token') else None
            is_apple_pay_native = payment_token == APPLE_PAY_NATIVE_TOKEN
            if (
                not trigger == BasketPaymentAnalyticsTrigger.KIP_RETRY
                and payment_method.card_type in _PAYMENT_METHODS_DO_NOT_NEED_AUTHORIZATION
            ) or is_apple_pay_native:
                ProxyProvider.create_analytics_data(
                    basket_payment=basket_payment, payment_row=payment_row, trigger=trigger
                )
                return

            if basket_payment.user_id:
                wallet_id = get_customer_wallet_id_adapter(basket_payment.user_id)
            else:
                wallet_id = get_anonymous_wallet_id_adapter()

            synchronize_adyen_payment(basket_payment)

            additional_data = get_auth_additional_data(
                transaction=transaction,
                device_data=device_data,
                extra_data=extra_data,
                fraud_prevention=fraud_prevention,
            )

            BasketPaymentService.authorize_payment(
                basket_payment=basket_payment,
                wallet_id=wallet_id,
                additional_data=additional_data,
                off_session=TransactionService.is_off_session_payment(
                    transaction,
                    basket_payment.payment_method,
                ),
                payment_method_data=AuthorizePaymentMethodDataEntity(
                    tokenized_pm_id=payment_method.tokenized_pm_id,
                    payment_token=(
                        payment_method.token if hasattr(payment_method, 'token') else None
                    ),
                    gift_cards_ids=sget_v2(payment_method, ['gift_cards_ids']),
                ),
            )

            ProxyProvider.create_analytics_data(
                device_data=additional_data.device_data,
                basket_payment=basket_payment,
                payment_row=payment_row,
                trigger=trigger,
            )

    @staticmethod
    def cancel_payment(payment_row: 'PaymentRow', log_note: str = None) -> bool:
        """
        Cancels payment. In old structure Failed -> Canceled transition is required.
        In new structure we leave Failed status.

        :param payment_row: PaymentRow object we want to cancel
        :param log_note: short information what triggered change
        """
        with SmartLock(key=f'transaction_id:{payment_row.receipt.transaction_id}'):
            if payment_row.status == receipt_status.PAYMENT_FAILED:
                # It's hack for old structure. Don't mess up with new statuses.
                # We won't allow transition Failed -> Cancelled in new structure
                # So just update old structure here
                payment_row.receipt.transaction.update_payment_rows(
                    receipt_status.PAYMENT_CANCELED,
                    log_action=PaymentRowChange.MULTI_ROW_UPDATE,
                    log_note=log_note,
                )

                return True

            basket_payment = BasketPayment.objects.get(id=payment_row.basket_payment_id)

            return BasketPaymentService.cancel_payment(
                basket_payment=basket_payment,
            )

    @staticmethod
    def authorize_deposit(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        transaction: Transaction,
        payment_method: PaymentMethod,
        payment_row: PaymentRow = None,
        device_data: DeviceDataDict = None,
        extra_data: dict = None,
        expiration_date_validation: datetime.date = None,
    ):
        """
        Authorization of CancellationFee. For Prepayment should be used `make_payment`.
        It can be done in Online or Offline way.

        In offline way there is instant success - payment row is updated to status
        DEPOSIT_AUTHORISATION_SUCCESS. Transaction is updated.

        In online way there is request via PaymentProvider app. Payment Row is waiting for update
        from new structure. Transaction is not updated in this scenario.

        :param transaction: Authorization of Transaction
        :param payment_method: PaymentMethod used
        :param payment_row: PaymentRow, currently not used.
            Keep for same signature as in AdyenEEProvider
        :param extra_data: Adyen related objects
        :param device_data: Adyen related objects
        :param expiration_date_validation: date due to which to check card expiration date
        :return: For online auth transaction is in NOT UPDATED state
        """
        with SmartLock(key=f'transaction_id:{transaction.id}'):
            send_request = CancellationFeeFullAmountAuthorizationFlag()

            if extra_data is None:
                extra_data = {}

            if not payment_row:
                payment_row = transaction.latest_receipt.payment_rows.get()

            is_deposit = payment_row.payment_type.code != PaymentTypeEnum.PREPAYMENT

            if not is_deposit:
                raise RuntimeError('Wrong method')  # pylint: disable=broad-exception-raised
            validation = PaymentProvidersPaymentPort.validate_tokenized_pm(
                tokenized_pm_id=payment_method.tokenized_pm_id,
                expiration_date_validation=expiration_date_validation,
            )
            if validation.errors[0]:
                raise InvalidPaymentMethod(
                    errors=[
                        {
                            'code': 'invalid',
                            'type': 'validation',
                            'field': 'payment_method',
                            'description': str(validation.errors[0].label),
                        }
                    ]
                )

            if send_request:
                cf_auth = CancellationFeeAuth.objects.get(id=transaction.cancellation_fee_auth_id)
                wallet_id = get_business_wallet_id_adapter(business_id=transaction.pos.business_id)

                additional_data = get_auth_additional_data(
                    transaction=transaction,
                    device_data=device_data,
                    extra_data=extra_data,
                )

                CancellationFeeAuthService.authorize_cf_auth(
                    cf_auth=cf_auth,
                    wallet_id=wallet_id,
                    additional_data=additional_data,
                    off_session=TransactionService.is_off_session_payment(transaction),
                    payment_method_data=AuthorizePaymentMethodDataEntity(
                        tokenized_pm_id=payment_method.tokenized_pm_id,
                        payment_token=None,
                    ),
                )

                CancellationFeeAuthAnalyticsService.create_analytics_data(
                    cf_auth=cf_auth,
                    device_data=additional_data.device_data,
                )

            else:
                payment_row.update_status(
                    provider=(
                        PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
                            TransactionService.get_payment_provider_code(
                                pos=transaction.pos,
                                payment_type_code=PaymentTypeEnum.PAY_BY_APP,
                                txn=transaction,
                            )
                        )
                    ),
                    status=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
                    log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
                    log_note='FakeProvider authorize deposit',
                )
            return transaction

    @staticmethod
    def charge_deposit(
        transaction: 'Transaction',
        operator: User,
        device_data: DeviceDataDict = None,
        register: Register = None,
    ):
        """From perspective of PaymentGateway its another Mobile Card payment.
        Basket, BasketPayment and ther rest of objects in PaymentGateway and PaymentProviders
        are created when business is decided to charge CF.
        Until that moment only CancellationFeeAuth exists.

        Operator and register filed are filled in transaction.
        """

        with SmartLock(key=f'transaction_id:{transaction.id}'):
            transaction.operator = operator
            transaction.register = register
            transaction.save(update_fields=['operator', 'register'])

            basket_payment = TransactionService.create_basket_from_cf_auth(txn=transaction)
            if not basket_payment:
                raise BasketPaymentNotFound()

            wallet_id = get_customer_wallet_id_adapter(basket_payment.user_id)

            transaction.refresh_from_db()
            additional_data = get_auth_additional_data(
                transaction=transaction,
                device_data=device_data,
            )

            BasketPaymentService.authorize_payment(
                basket_payment=basket_payment,
                wallet_id=wallet_id,
                additional_data=additional_data,
                off_session=TransactionService.is_off_session_payment(transaction),
                payment_method_data=AuthorizePaymentMethodDataEntity(
                    tokenized_pm_id=None,
                    payment_token=None,
                ),
            )

            BasketPaymentAnalyticsService.create_analytics_data(
                basket_payment=basket_payment,
                device_data=additional_data.device_data,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__CANCELLATION_FEE_CHARGE,
            )

    @staticmethod
    def cancel_deposit(transaction, *args, **kwargs):
        with SmartLock(key=f'transaction_id:{transaction.id}'):
            cf_auth = CancellationFeeAuth.objects.get(id=transaction.cancellation_fee_auth_id)
            wallet_id = get_business_wallet_id_adapter(business_id=transaction.pos.business_id)

            CancellationFeeAuthService.cancel_authorization(
                cf_auth=cf_auth,
                wallet_id=wallet_id,
            )

    @staticmethod
    def send_for_refund(
        payment_row: 'PaymentRow',
        operator: User,
        from_admin: bool = True,
        payment_splits: dict = None,
    ):
        """
        Metods handles sending payment row for refund.

        :param payment_row: PaymentRow which will be sent for refund
        :param operator: Operator of refund
        :param from_admin: True if action is triggered from admin panel,
            some validators are bypassed
        :param payment_splits: dict with new payment_splits
        :return: PaymentRow in senf for refund state
        """
        with SmartLock(key=f'transaction_id:{payment_row.receipt.transaction_id}'):
            possible, _error = is_refund_possible(
                payment_row,
                check_requested=False,
                from_admin=from_admin,
            )

            if not possible:
                raise AssertionError(  # pylint: disable=broad-exception-raised
                    'That row cannot be refunded'
                )

            if not payment_row.refund_requested:
                payment_row.refund_requested = tznow()
                payment_row.refund_operator = operator
                payment_row.save(update_fields=['refund_requested', 'refund_operator'])

            refund_row = payment_row.update_status(
                status=receipt_status.SENT_FOR_REFUND,
                settled=True,
                operator=operator,
                payment_splits=payment_splits,
                log_action=PaymentRowChange.SENT_FOR_REFUND,
                log_note=(
                    f'{"Admin" if from_admin else "User"} sent for refund action - ProxyProvider'
                ),
            )

            basket_payment = BasketPayment.objects.get(id=payment_row.basket_payment_id)
            assert txn_refactor_stage2_enabled(payment_row.receipt.transaction) is True
            refund_basket_payment = BasketPaymentService.initialize_refund_basket_payment(
                original_basket_payment=basket_payment,
            )

            refund_row.basket_payment_id = refund_basket_payment.id
            refund_row.save()

            return refund_row

    # pylint: disable=invalid-name
    @staticmethod
    def finish_adding_payment_method_or_payment_3ds(auth_psp_reference, md, pa_response):
        """After initiating 3ds process, customer will get some additional_date to authorize payment
        with his bank.

        Result of this authorization should be passed via this method to PaymentProvider app.
        If something went wrong `PaymentProviderError` should be raised
        """
        response = PaymentProvidersPaymentPort.adyen_authorize_3ds_payment(
            auth_psp_reference=auth_psp_reference,
            additional_data=Adyen3dsAuthAdditionalDataEntity(md=md, pa_response=pa_response),
        )

        if not response.entity.success:
            raise PaymentProviderError('3DSecure1 flow was unsuccessful.')

    @staticmethod
    def add_payment_method_err_msg(payment_row: PaymentRow):
        """Provider specific. Returns reject reason.

        :param payment_row: PaymentRow instance
        :return dict(
            'type': PaymentRow.PAYMENT_ROW__REJECT_REASON,
            'msg': text,
            'error': text,
        )
        """
        msg = _('Something went wrong!')

        if not payment_row.basket_payment_id:
            return

        basket_payment = BasketPayment.objects.get(id=payment_row.basket_payment_id)

        return {
            'type': PaymentRow.PAYMENT_ROW__REJECT_REASON,
            'msg': basket_payment.get_error_code_display() or msg,
            'error': '',  # for coherence with AdyenEEPaymentProvider
        }
