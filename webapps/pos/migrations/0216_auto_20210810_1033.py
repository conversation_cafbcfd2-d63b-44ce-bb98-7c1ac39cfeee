# Generated by Django 3.1.13 on 2021-08-10 10:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pos', '0215_rename_gitftcard_payment_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='paymenttype',
            name='code',
            field=models.CharField(
                choices=[
                    ('pay_by_app', 'Mobile Payment'),
                    ('pba_donations', 'Donations'),
                    ('cash', 'Cash'),
                    ('check', 'Check'),
                    ('cheque', 'Cheque'),
                    ('credit_card', 'Credit Card'),
                    ('subscription', 'Subscription Card'),
                    ('store_credit', 'Store Credit'),
                    ('bank_transfer', 'Bank Transfer'),
                    ('american_express', 'American Express'),
                    ('paypal', 'PayPal'),
                    ('square', 'Square'),
                    ('split', 'Split payment'),
                    ('prepayment', 'Prepayment'),
                    ('egift_card', 'Gift Card'),
                    ('membership', 'Membership'),
                    ('package', 'Package'),
                    ('direct_payment', 'Direct Payment'),
                    ('giftcard', 'Own GiftCard'),
                    ('voucher', 'Voucher'),
                ],
                max_length=16,
            ),
        ),
    ]
