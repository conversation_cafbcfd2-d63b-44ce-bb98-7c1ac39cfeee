# Generated by Django 4.0.2 on 2022-03-14 09:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0364_service_variant_version'),
        ('pos', '0226_alter_pos_pay_by_app_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='transactionrow',
            name='service_variant_combo_parent',
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to='business.servicevariant',
            ),
        ),
    ]
