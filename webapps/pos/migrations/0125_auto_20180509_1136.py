# Generated by Django 1.11.11 on 2018-05-09 11:36
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pos', '0124_merge_20180314_2140'),
    ]

    operations = [
        migrations.AlterField(
            model_name='receipt',
            name='status_code',
            field=models.CharField(
                choices=[
                    ('O', 'Call for Payment'),
                    ('G', 'Pending'),
                    ('A', 'Payment Awaiting'),
                    ('P', 'Payment Completed'),
                    ('F', 'Payment Failed'),
                    ('X', 'Payment Canceled'),
                    ('W', 'Deposit Verification Awaiting'),
                    ('D', 'Deposit Verification Completed'),
                    ('E', 'Deposit Verification Failed'),
                    ('T', 'Deposit Charge Awaiting'),
                    ('C', 'Deposit Charge Completed'),
                    ('L', 'Deposit Charge Failed'),
                    ('N', 'Deposit Charge Canceled'),
                    ('V', 'Deposit Cancel Awaiting'),
                    ('Y', 'Deposit Cancel Failed'),
                    ('R', 'Payment archived'),
                ],
                max_length=1,
            ),
        ),
    ]
