from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0005_added_zh_language'),
        ('pos', '0024_auto_20160330_1102'),
    ]

    operations = [
        migrations.CreateModel(
            name='POSChangeLog',
            fields=[
                (
                    'id',
                    models.AutoField(
                        verbose_name='ID', serialize=False, auto_created=True, primary_key=True
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('data', models.TextField()),
                (
                    'operator',
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        blank=True,
                        to='user.User',
                        null=True,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name='pos',
            name='operator',
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.DO_NOTHING,
                blank=True,
                to='user.User',
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='status_code',
            field=models.CharField(
                max_length=1,
                choices=[
                    ('A', 'Awaiting Customer Approval'),
                    ('P', 'Payment Completed'),
                    ('F', 'Payment Failed'),
                    ('W', 'Deposit awaiting'),
                    ('D', 'Deposit Authorisation Completed'),
                    ('E', 'Deposit Authorisation Failed'),
                    ('T', 'Deposit Charge Awaiting'),
                    ('C', 'Deposit Charge Completed'),
                    ('L', 'Deposit Charge Failed'),
                ],
            ),
        ),
        migrations.AddField(
            model_name='poschangelog',
            name='pos',
            field=models.ForeignKey(
                on_delete=models.deletion.CASCADE, related_name='change_logs', to='pos.POS'
            ),
        ),
    ]
