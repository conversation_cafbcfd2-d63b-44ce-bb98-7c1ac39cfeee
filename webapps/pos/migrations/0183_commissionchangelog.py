# Generated by Django 2.0.13 on 2020-01-09 14:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0038_email__iexact_lookups_indices'),
        ('pos', '0182_add_unique_on_business_pos'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommissionChangeLog',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('data', models.TextField()),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.User',
                    ),
                ),
                (
                    'transaction_row',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='commission_change_logs',
                        to='pos.TransactionRow',
                    ),
                ),
            ],
        ),
    ]
