# Generated by Django 1.11.11 on 2018-06-26 11:02
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pos', '0138_merge_20180620_1119'),
    ]

    operations = [
        migrations.AddField(
            model_name='paymentrow',
            name='oper_result',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='paymentmethod',
            name='card_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('amex', 'American Express'),
                    ('unionpay', 'China UnionPay'),
                    ('diners', 'Diners Club'),
                    ('discover', 'Discover Card'),
                    ('interpayment', 'InterPayment'),
                    ('jcb', 'JCB'),
                    ('maestro', 'Maestro'),
                    ('dankort', 'Dankort'),
                    ('nspk_mir', 'NSPK MIR'),
                    ('mastercard', 'MasterCard'),
                    ('visa', 'Visa'),
                    ('uatp', 'UATP'),
                    ('verve', 'Verve'),
                    ('google_pay', 'GooglePay'),
                    ('apple_pay', 'ApplePay'),
                    ('other', ''),
                ],
                max_length=12,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='paymentrow',
            name='card_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('amex', 'American Express'),
                    ('unionpay', 'China UnionPay'),
                    ('diners', 'Diners Club'),
                    ('discover', 'Discover Card'),
                    ('interpayment', 'InterPayment'),
                    ('jcb', 'JCB'),
                    ('maestro', 'Maestro'),
                    ('dankort', 'Dankort'),
                    ('nspk_mir', 'NSPK MIR'),
                    ('mastercard', 'MasterCard'),
                    ('visa', 'Visa'),
                    ('uatp', 'UATP'),
                    ('verve', 'Verve'),
                    ('google_pay', 'GooglePay'),
                    ('apple_pay', 'ApplePay'),
                    ('other', ''),
                ],
                max_length=64,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='receipt',
            name='card_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('amex', 'American Express'),
                    ('unionpay', 'China UnionPay'),
                    ('diners', 'Diners Club'),
                    ('discover', 'Discover Card'),
                    ('interpayment', 'InterPayment'),
                    ('jcb', 'JCB'),
                    ('maestro', 'Maestro'),
                    ('dankort', 'Dankort'),
                    ('nspk_mir', 'NSPK MIR'),
                    ('mastercard', 'MasterCard'),
                    ('visa', 'Visa'),
                    ('uatp', 'UATP'),
                    ('verve', 'Verve'),
                    ('google_pay', 'GooglePay'),
                    ('apple_pay', 'ApplePay'),
                    ('other', ''),
                ],
                max_length=64,
                null=True,
            ),
        ),
    ]
