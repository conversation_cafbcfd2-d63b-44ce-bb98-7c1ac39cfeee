# Generated by Django 3.2.7 on 2021-11-30 10:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0353_servise_addons'),
        ('pos', '0219_stripe_init'),
    ]

    operations = [
        migrations.AddField(
            model_name='transactionrow',
            name='addon_use',
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='transaction_rows',
                to='business.serviceaddonuse',
            ),
        ),
        migrations.AlterField(
            model_name='transactionrow',
            name='type',
            field=models.CharField(
                choices=[
                    ('S', 'Service'),
                    ('P', 'Product'),
                    ('A', 'Add-On'),
                    ('D', 'Deposit'),
                    ('V', 'Voucher'),
                    ('T', 'Travel Fee'),
                ],
                default='S',
                max_length=1,
            ),
        ),
    ]
