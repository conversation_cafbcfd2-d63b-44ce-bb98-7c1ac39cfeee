from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pos', '0010_transaction_payment_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='transactionrow',
            name='discount_rate',
            field=models.DecimalField(default=0, max_digits=10, decimal_places=2),
        ),
        migrations.AlterField(
            model_name='transactionrow',
            name='discounted_item_price',
            field=models.DecimalField(null=True, max_digits=10, decimal_places=2, blank=True),
        ),
        migrations.AlterField(
            model_name='transactionrow',
            name='item_price',
            field=models.DecimalField(null=True, max_digits=10, decimal_places=2, blank=True),
        ),
        migrations.AlterField(
            model_name='transactionrow',
            name='tax_amount',
            field=models.DecimalField(null=True, max_digits=10, decimal_places=2, blank=True),
        ),
        migrations.AlterField(
            model_name='transactionrow',
            name='tax_rate',
            field=models.PositiveSmallIntegerField(null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='transactionrow',
            name='total',
            field=models.DecimalField(null=True, max_digits=10, decimal_places=2, blank=True),
        ),
    ]
