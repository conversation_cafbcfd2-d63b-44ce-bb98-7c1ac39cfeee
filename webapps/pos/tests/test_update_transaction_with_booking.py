# pylint: disable=protected-access
from datetime import datetime

import pytest
from dateutil import tz
from django.conf import settings
from mock import patch, MagicMock
from model_bakery import baker
from segment.analytics import Client

from lib.test_utils import create_subbooking
from lib.tools import id_to_external_api
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    SubbookingServiceVariantMode as SVMode,
)
from webapps.booking.models import (
    Appointment,
    BookingSources,
    BookingResource,
)
from webapps.business.enums import PriceType
from webapps.business.models import (
    Business,
    Service,
    ServiceVariant,
    ServiceVariantPayment,
    Resource,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_gateway.scripts import create_default_wallets
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import (
    PaymentType,
    PaymentRow,
    POS,
    Receipt,
    Transaction,
    TransactionRow,
    TransactionTip,
)
from webapps.pos.serializers import TransactionSerializer
from webapps.pos.tests import TestCaseWithSetUp
from webapps.segment.consts import UserRoleEnum
from webapps.user.models import User


@pytest.mark.django_db
class TestUpdateTransactionWithBooking(TestCaseWithSetUp):

    def setUp(self):
        super().setUp()
        service = baker.make(Service, business=self.business)
        self.service_variant = baker.make(
            ServiceVariant,
            duration='3600',
            service=service,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=10,
        )

        self.booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                updated_by=self.user,
                source=self.booking_source,
                service_variant=self.service_variant,
                booked_for=self.bci,
                type=Appointment.TYPE.CUSTOMER,
                status=Appointment.STATUS.ACCEPTED,
            ),
        )

    @patch.object(Client, 'track')
    def test_prepayment_before_checkout(
        self,
        analytics_track_mock,
    ):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'
        self.pos.business.get_timezone = MagicMock()
        self.pos.business.get_timezone.return_value = biz_tz

        txn = baker.make(
            Transaction,
            appointment_id=self.booking.appointment_id,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            total=200,
        )
        baker.make(
            TransactionTip,
            transaction=txn,
            amount=0,
            rate=0,
        )
        receipt = baker.make(
            Receipt,
            payment_type=self.split,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
            already_paid=0,
        )

        txn.latest_receipt = receipt
        txn.save()

        payment_row = baker.make(
            PaymentRow,
            amount=10,
            receipt=receipt,
            payment_type=self.prepayment,
            status=receipt_status.PREPAYMENT_SUCCESS,
        )

        TransactionSerializer.update_transaction_with_booking(
            self.booking.appointment,
        )
        txns = Transaction.objects.by_appointment_id(self.booking.appointment_id).last()

        assert len(txn.series()) == 2
        receipts = [r for txn in txns.series() for r in txn.receipts.all().order_by('id')]
        assert len(receipts) == 3
        assert receipts[0].status_code == receipt_status.PREPAYMENT_SUCCESS
        assert receipts[1].status_code == receipt_status.ARCHIVED
        assert receipts[2].status_code == receipt_status.PREPAYMENT_SUCCESS

        assert receipts[2].payment_rows.get().parent_payment_row == payment_row

        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Payment_Transaction_Completed',
                'properties': {
                    'user_id': id_to_external_api(self.business.owner.id),
                    'user_role': UserRoleEnum.OWNER,
                    'phone': '',
                    'offer_type': Business.Package(
                        self.business.package
                    ).label,  # pylint: disable=no-value-for-parameter
                    'country': settings.API_COUNTRY,
                    'email': self.business.owner.email,
                    'business_id': id_to_external_api(self.business.id),
                    'transaction_type': PaymentTypeEnum.PAY_BY_APP,
                    'mobile_payment_type': 'prepayment',
                },
            },
        )

    @patch.object(Client, 'track')
    def test_analytics_update_transactions(
        self,
        analytics_track_mock,
    ):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'
        self.pos.business.get_timezone = MagicMock()
        self.pos.business.get_timezone.return_value = biz_tz

        txn = baker.make(
            Transaction,
            appointment_id=self.booking.appointment_id,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            total=200,
        )
        baker.make(
            TransactionTip,
            transaction=txn,
            amount=0,
            rate=0,
        )
        receipt = baker.make(
            Receipt,
            payment_type=self.split,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
            already_paid=0,
        )

        txn.latest_receipt = receipt
        txn.save()

        payment_row = baker.make(
            PaymentRow,
            amount=10,
            receipt=receipt,
            payment_type=self.prepayment,
            status=receipt_status.PREPAYMENT_SUCCESS,
        )

        TransactionSerializer.update_transaction_with_booking(
            self.booking.appointment, ignore_analytics=True
        )
        txns = Transaction.objects.by_appointment_id(self.booking.appointment_id).last()

        assert len(txn.series()) == 2
        receipts = [r for txn in txns.series() for r in txn.receipts.all().order_by('id')]
        assert len(receipts) == 3
        assert receipts[0].status_code == receipt_status.PREPAYMENT_SUCCESS
        assert receipts[1].status_code == receipt_status.ARCHIVED
        assert receipts[2].status_code == receipt_status.PREPAYMENT_SUCCESS

        assert receipts[2].payment_rows.get().parent_payment_row == payment_row

        assert analytics_track_mock.call_count == 0, (
            f'{analytics_track_mock.call_count} !=0 '
            f' events triggerd:\n {analytics_track_mock.call_args_list}'
        )

    def test_prepayment_after_checkout(self):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'
        self.pos.business.get_timezone = MagicMock()
        self.pos.business.get_timezone.return_value = biz_tz

        txn = baker.make(
            Transaction,
            appointment_id=self.booking.appointment_id,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = baker.make(
            Receipt,
            payment_type=self.split,
            status_code=receipt_status.PAYMENT_SUCCESS,
            transaction=txn,
        )

        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=10,
            receipt=receipt,
            payment_type=self.prepayment,
            status=receipt_status.PAYMENT_SUCCESS,
        )
        baker.make(
            PaymentRow,
            amount=10,
            receipt=receipt,
            payment_type=self.cash,
            status=receipt_status.PAYMENT_SUCCESS,
        )

        TransactionSerializer.update_transaction_with_booking(self.booking.appointment)

        txns = Transaction.objects.by_appointment_id(self.booking.appointment_id).last()

        assert len(txn.series()) == 1

        receipts = [r for txn in txns.series() for r in txn.receipts.all().order_by('id')]
        assert len(receipts) == 1
        assert receipts[0].status_code == receipt_status.PAYMENT_SUCCESS

    def test_prepayment_before_checout_without_sv(self):
        """This example from error log. Customer made appointement with
        prepayment, then business remove serviceVariant"""

        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'
        self.pos.business.get_timezone = MagicMock()
        self.pos.business.get_timezone.return_value = biz_tz

        self.booking.service_variant = None
        self.booking.save(override=True)

        txn = baker.make(
            Transaction,
            appointment_id=self.booking.appointment_id,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = baker.make(
            Receipt,
            payment_type=self.split,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )

        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=10,
            receipt=receipt,
            payment_type=self.prepayment,
            status=receipt_status.PREPAYMENT_SUCCESS,
        )

        TransactionSerializer.update_transaction_with_booking(self.booking.appointment)

        txns = Transaction.objects.by_appointment_id(self.booking.appointment_id).last()

        assert len(txn.series()) == 2
        receipts = [r for txn in txns.series() for r in txn.receipts.all().order_by('id')]
        assert len(receipts) == 3
        assert receipts[0].status_code == receipt_status.PREPAYMENT_SUCCESS
        assert receipts[1].status_code == receipt_status.ARCHIVED
        assert receipts[2].status_code == receipt_status.PREPAYMENT_SUCCESS

    def test_booksy_pay_before_checkout(self):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'
        self.pos.business.get_timezone = MagicMock()
        self.pos.business.get_timezone.return_value = biz_tz

        txn = baker.make(
            Transaction,
            appointment_id=self.booking.appointment_id,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            total=200,
        )
        baker.make(
            TransactionTip,
            transaction=txn,
            amount=0,
            rate=0,
        )
        receipt = baker.make(
            Receipt,
            payment_type=self.split,
            status_code=receipt_status.BOOKSY_PAY_SUCCESS,
            transaction=txn,
            already_paid=0,
        )

        txn.latest_receipt = receipt
        txn.save()

        payment_row = baker.make(
            PaymentRow,
            amount=10,
            receipt=receipt,
            payment_type=self.booksy_pay,
            status=receipt_status.BOOKSY_PAY_SUCCESS,
        )

        TransactionSerializer.update_transaction_with_booking(
            self.booking.appointment,
        )
        txns = Transaction.objects.by_appointment_id(self.booking.appointment_id).last()

        assert len(txn.series()) == 2
        receipts = [r for txn in txns.series() for r in txn.receipts.all().order_by('id')]
        assert len(receipts) == 3
        assert receipts[0].status_code == receipt_status.BOOKSY_PAY_SUCCESS
        assert receipts[1].status_code == receipt_status.ARCHIVED
        assert receipts[2].status_code == receipt_status.BOOKSY_PAY_SUCCESS

        assert receipts[2].payment_rows.get().parent_payment_row == payment_row

    def test_booksy_pay_after_checkout(self):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'
        self.pos.business.get_timezone = MagicMock()
        self.pos.business.get_timezone.return_value = biz_tz

        txn = baker.make(
            Transaction,
            appointment_id=self.booking.appointment_id,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = baker.make(
            Receipt,
            payment_type=self.split,
            status_code=receipt_status.PAYMENT_SUCCESS,
            transaction=txn,
        )

        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=10,
            receipt=receipt,
            payment_type=self.booksy_pay,
            status=receipt_status.PAYMENT_SUCCESS,
        )
        baker.make(
            PaymentRow,
            amount=10,
            receipt=receipt,
            payment_type=self.cash,
            status=receipt_status.PAYMENT_SUCCESS,
        )

        TransactionSerializer.update_transaction_with_booking(self.booking.appointment)

        txns = Transaction.objects.by_appointment_id(self.booking.appointment_id).last()

        assert len(txn.series()) == 1

        receipts = [r for txn in txns.series() for r in txn.receipts.all().order_by('id')]
        assert len(receipts) == 1
        assert receipts[0].status_code == receipt_status.PAYMENT_SUCCESS


@pytest.mark.django_db
class TestUpdateTransactionWithBookingService(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        self.pos = baker.make(POS, business=self.business, active=True)
        self.booking_source = baker.make(BookingSources)
        self.user = baker.make(User)

        self.bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='1234567890',
            user=baker.make(User),
        )

        service = baker.make(Service, business=self.business)
        self.service_variant = baker.make(
            ServiceVariant,
            duration='3600',
            service=service,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=10,
        )

        self.booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                updated_by=self.user,
                source=self.booking_source,
                service_variant=self.service_variant,
                booked_for=self.bci,
                type=Appointment.TYPE.CUSTOMER,
                status=Appointment.STATUS.ACCEPTED,
            ),
        )

        self.prepayment = baker.make(
            PaymentType,
            pos=self.pos,
            code=PaymentTypeEnum.PREPAYMENT,
        )
        create_default_wallets()
        PaymentGatewayPort.get_or_create_business_wallet(
            self.business.id,
            statement_name=self.business.name,
        )

    @pytest.mark.django_db
    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    def test_make_multi_from_charged_booking(
        self,
        get_timezone_mock,
        get_location_name_mock,
    ):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'
        get_timezone_mock.return_value = biz_tz
        get_location_name_mock.return_value = 'adasdas, 00000'

        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            price=100,
        )

        baker.make(
            ServiceVariantPayment,
            service_variant=service_variant,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=100,
        )

        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
        )
        staffer.add_services([service.id])

        bci = baker.make(BusinessCustomerInfo, business=self.business)

        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_for=bci,
                updated_by=self.user,
                source=self.customer_booking_src,
                booked_from=datetime(2017, 1, 1, 12, tzinfo=biz_tz),
                booked_till=datetime(2017, 1, 1, 13, tzinfo=biz_tz),
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.FINISHED,
                autoassign=True,
                service_variant=service_variant,
            ),
        )

        BookingResource(subbooking=booking, resource=staffer).save()

        txn = baker.make(
            Transaction,
            appointment_id=booking.appointment_id,
            pos=self.pos,
            total=200,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        receipt = baker.make(
            Receipt,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            payment_type=self.prepayment,
            transaction=txn,
            already_paid=100,
        )

        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=self.prepayment,
            receipt=receipt,
            status=receipt_status.PREPAYMENT_SUCCESS,
        )

        # get booking
        url = (
            f'/business_api/me/businesses/{self.business.id}/'
            f'appointments/multi/{booking.appointment_id}/'
        )

        resp = self.fetch(url)
        assert resp.code == 200
        pmnt_info = resp.json['appointment']['payment_info']
        txn_info = pmnt_info['transaction_info']
        assert txn_info['status_code'] == receipt_status.PREPAYMENT_SUCCESS
        assert pmnt_info['payable'] is True

        # add service to booking (create multibooking from single
        body = {
            "subbookings": [
                {
                    "booked_from": "2017-01-01T12:00",
                    "booked_till": "2017-01-01T13:00",
                    "staffer_id": staffer.id,
                    "appliance_id": None,
                    "service_variant": {"id": service_variant.id, "mode": SVMode.VARIANT},
                    "id": booking.id,
                    "service": {
                        "id": service.id,
                        SVMode.VARIANT: {
                            "id": service_variant.id,
                            "duration": 60,
                            "type": "X",
                            "deposit": None,
                            "price": 100,
                        },
                    },
                    "staffer": {"id": staffer.id},
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                    '_version': booking.appointment._version,
                },
                {
                    "booked_from": "2017-01-01T13:00",
                    "booked_till": "2017-01-01T14:00",
                    "staffer_id": staffer.id,
                    "appliance_id": None,
                    "service_variant": {"id": service_variant.id, "mode": SVMode.VARIANT},
                    "id": None,
                    "service": {
                        "id": service.id,
                        "variant": {
                            "id": service_variant.id,
                            "duration": 60,
                            "type": "X",
                            "deposit": None,
                            "price": 100,
                        },
                    },
                    "staffer": {"id": staffer.id},
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                },
            ],
            "customer": {
                "mode": ACMode.WALK_IN,
            },
            '_version': booking.appointment._version,
            'dry_run': False,
            'overbooking': False,
            '_notify_about_reschedule': False,
        }

        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200
        appointment_id = resp.json['appointment']['appointment_id']

        txns = Transaction.objects.filter(appointment_id=appointment_id).last()

        assert len(txn.series()) == 2
        receipts = [r for txn in txns.series() for r in txn.receipts.all().order_by('id')]
        assert len(receipts) == 3
        assert receipts[0].status_code == receipt_status.PREPAYMENT_SUCCESS
        assert receipts[1].status_code == receipt_status.ARCHIVED
        assert receipts[2].status_code == receipt_status.PREPAYMENT_SUCCESS

    @pytest.mark.django_db
    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    def test_make_multi_from_cf_booking(
        self,
        get_timezone_mock,
        get_location_name_mock,
    ):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'
        get_timezone_mock.return_value = biz_tz
        get_location_name_mock.return_value = 'adasdas, 00000'

        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            price=100,
        )

        baker.make(
            ServiceVariantPayment,
            service_variant=service_variant,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=100,
        )

        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
        )
        staffer.add_services([service.id])

        bci = baker.make(BusinessCustomerInfo, business=self.business)

        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_for=bci,
                updated_by=self.user,
                source=self.customer_booking_src,
                booked_from=datetime(2017, 1, 1, 12, tzinfo=biz_tz),
                booked_till=datetime(2017, 1, 1, 13, tzinfo=biz_tz),
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.ACCEPTED,
                autoassign=True,
                service_variant=service_variant,
            ),
        )

        BookingResource(subbooking=booking, resource=staffer).save()

        txn = baker.make(
            Transaction,
            appointment_id=booking.appointment_id,
            pos=self.pos,
            total=200,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )

        baker.make(
            TransactionRow,
            transaction=txn,
            type=TransactionRow.TRANSACTION_ROW_TYPE__DEPOSIT,
            subbooking=booking,
        )

        receipt = baker.make(
            Receipt,
            status_code=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            payment_type=self.prepayment,
            transaction=txn,
            already_paid=100,
        )

        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=self.prepayment,
            receipt=receipt,
            status=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
        )

        # get booking
        url = f'/business_api/me/businesses/{self.business.id}/appointments/single/{booking.id}/'

        resp = self.fetch(url)
        assert resp.code == 200
        pmnt_info = resp.json['appointment']['payment_info']
        deposit_info = pmnt_info['deposit_info']
        assert deposit_info['status_code'] == receipt_status.DEPOSIT_AUTHORISATION_SUCCESS

        # add service to booking (create multibooking from single
        body = {
            "subbookings": [
                {
                    "booked_from": "2017-01-01T12:00",
                    "booked_till": "2017-01-01T13:00",
                    "staffer_id": staffer.id,
                    "appliance_id": None,
                    "service_variant": {"id": service_variant.id, "mode": SVMode.VARIANT},
                    "id": booking.id,
                    "service": {
                        "id": service.id,
                        "variant": {
                            "id": service_variant.id,
                            "duration": 60,
                            "type": "X",
                            "deposit": None,
                            "price": 100,
                        },
                    },
                    "staffer": {"id": staffer.id},
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                    '_version': booking.appointment._version,
                },
                {
                    "booked_from": "2017-01-01T13:00",
                    "booked_till": "2017-01-01T14:00",
                    "staffer_id": staffer.id,
                    "appliance_id": None,
                    "service_variant": {"id": service_variant.id, "mode": SVMode.VARIANT},
                    "id": None,
                    "service": {
                        "id": service.id,
                        "variant": {
                            "id": service_variant.id,
                            "duration": 60,
                            "type": "X",
                            "deposit": None,
                            "price": 100,
                        },
                    },
                    "staffer": {"id": staffer.id},
                    "appliance": None,
                    "autoassign": False,
                    "gap_hole_start": None,
                    "gap_hole_end": None,
                    "wait_time": {},
                },
            ],
            "customer": {
                "mode": ACMode.WALK_IN,
            },
            '_version': booking.appointment._version,
            'dry_run': False,
            'overbooking': False,
            '_notify_about_reschedule': False,
        }

        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == 200
        appointment_id = resp.json['appointment']['appointment_id']

        txns = Transaction.objects.filter(appointment_id=appointment_id).last()

        assert len(txn.series()) == 2
        receipts = [r for txn in txns.series() for r in txn.receipts.all().order_by('id')]
        assert len(receipts) == 3
        assert receipts[0].status_code == receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
        assert receipts[1].status_code == receipt_status.ARCHIVED
        assert receipts[2].status_code == receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
