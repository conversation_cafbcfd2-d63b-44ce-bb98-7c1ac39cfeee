from copy import deepcopy

import pytest
from model_bakery import baker

from service.tests import BaseAsyncHTTPTest
from webapps.business.enums import PriceType
from webapps.business.models import (
    Business,
    Service,
    ServiceVariant,
)
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import (
    POS,
    PaymentType,
    TaxRate,
    Tip,
)


@pytest.mark.django_db
class TestTaxRate(BaseAsyncHTTPTest):

    def setUp(self):
        super().setUp()
        self.pos = baker.make(POS, business=self.business)

        baker.make(
            TaxRate, rate=10, default_for_service=True, default_for_product=True, pos=self.pos
        )

        baker.make(TaxRate, rate=50, pos=self.pos)

        baker.make(
            Tip,
            rate=20,
            default=True,
            pos=self.pos,
        )

        baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos,
            default=True,
        )

        self.service = baker.make(
            Service,
            business=self.business,
            tax_rate=50,  # Set to as not default on start
        )

        self.service_variant = baker.make(
            ServiceVariant,
            duration='3600',
            service=self.service,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=10,
        )

        self.pos_url = f"/business_api/me/businesses/{self.business.id}/pos"

        self.base_pos_body = self.fetch(self.pos_url)
        self.pos_body = self.base_pos_body.json["pos"]

    def setUpSecond(self):  # pylint: disable=invalid-name
        """
        SetUp second business for test.
        """
        self.business2 = baker.make(
            Business,
        )
        self.pos2 = baker.make(POS, business=self.business2)

        baker.make(
            TaxRate, rate=10, default_for_service=True, default_for_product=True, pos=self.pos2
        )

        baker.make(TaxRate, rate=50, pos=self.pos2)

        baker.make(
            Tip,
            rate=20,
            default=True,
            pos=self.pos2,
        )

        baker.make(
            PaymentType,
            code=PaymentTypeEnum.CASH,
            pos=self.pos2,
            default=True,
        )

        self.service2 = baker.make(
            Service,
            business=self.business2,
            tax_rate=50,  # Set to as not default on start
        )

        self.service_variant2 = baker.make(
            ServiceVariant,
            duration='3600',
            service=self.service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=10,
        )

    def test_create_tax_rate(self):
        """
        Test creating new tax rate through POSSerializer
        """

        post_body = {
            'tax_rates': deepcopy(self.pos_body['tax_rates'])
            + [
                {'rate': 20},
                {'rate': None},
            ],
        }

        resp = self.fetch(self.pos_url, method="PUT", body=post_body)

        print(resp)
        assert resp.code == 200
        body = resp.json["pos"]

        assert len(body['tax_rates']) == 4
        assert body['tax_rates'][0]['rate'] is None
        assert body['tax_rates'][1]['rate'] == '50.00'
        assert body['tax_rates'][2]['rate'] == '20.00'
        assert body['tax_rates'][3]['rate'] == '10.00'

        pos_tax_rates = self.pos.tax_rates.all()
        assert len(pos_tax_rates) == 4
        assert pos_tax_rates[0].rate is None
        assert pos_tax_rates[1].rate == 50
        assert pos_tax_rates[2].rate == 20
        assert pos_tax_rates[3].rate == 10

    def test_delete_tax_rate(self):
        """
        Test deleting tax rate through POSSerializer
        """

        post_body = {'tax_rates': deepcopy(self.pos_body['tax_rates'])[:-1]}

        resp = self.fetch(self.pos_url, method="PUT", body=post_body)

        print(resp)
        assert resp.code == 200
        body = resp.json["pos"]

        assert len(body['tax_rates']) == 1
        assert body['tax_rates'][0]['rate'] == '10.00'

        pos_tax_rates = self.pos.tax_rates.all()
        assert len(pos_tax_rates) == 1
        assert pos_tax_rates[0].rate == 10

    def test_edit_tax_rate(self):
        """
        Test editing tax rate through POSSerializer
        """

        tax_rates = deepcopy(self.pos_body['tax_rates'])
        tax_rates[0]['rate'] = 60  # Change 10 to 60

        post_body = {
            'tax_rates': tax_rates,
        }

        resp = self.fetch(self.pos_url, method="PUT", body=post_body)

        print(resp)
        assert resp.code == 200
        body = resp.json["pos"]

        assert len(body['tax_rates']) == 2
        assert body['tax_rates'][0]['rate'] == '60.00'
        assert body['tax_rates'][1]['rate'] == '50.00'

        pos_tax_rates = self.pos.tax_rates.all()
        assert len(pos_tax_rates) == 2
        assert pos_tax_rates[0].rate == 60
        assert pos_tax_rates[1].rate == 50

    def test_edit_tax_rate_change_places(self):
        """
        Test editing tax rate through POSSerializer
        """

        tax_rates = deepcopy(self.pos_body['tax_rates'])
        tax_rates[0]['rate'] = 50  # Change 10 to 50
        tax_rates[1]['rate'] = 10  # Change 50 to 10

        post_body = {
            'tax_rates': tax_rates,
        }

        resp = self.fetch(self.pos_url, method="PUT", body=post_body)

        assert resp.code == 200
        body = resp.json["pos"]

        assert len(body['tax_rates']) == 2
        assert body['tax_rates'][0]['rate'] == '50.00'
        assert body['tax_rates'][1]['rate'] == '10.00'

        pos_tax_rates = self.pos.tax_rates.all()
        assert len(pos_tax_rates) == 2
        assert pos_tax_rates[0].rate == 50
        assert pos_tax_rates[1].rate == 10
