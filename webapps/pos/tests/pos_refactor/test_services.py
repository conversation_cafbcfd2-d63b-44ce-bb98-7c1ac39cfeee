import uuid
from decimal import Decimal
from unittest.mock import (
    MagicMock,
    patch,
)

import mock
from django.test import TestCase
from model_bakery import baker
from parameterized import parameterized

from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    PaymentSplitsEntity,
    RefundSplitsEntity,
)
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import BasketTipSource
from webapps.business.models import (
    Business,
    Resource,
)
from webapps.point_of_sale.models import (
    Basket,
    BasketTip,
)
from webapps.pos.enums import (
    PaymentProviderEnum,
    PaymentTypeEnum,
    POSPlanPaymentTypeEnum,
    receipt_status,
)
from webapps.pos.models import (
    POS,
    POSPlan,
    PaymentRow,
    Receipt,
    Transaction,
    TransactionTip,
    TransactionTipRow,
)
from webapps.pos.services import (
    TransactionService,
    TransactionTipService,
)
from webapps.pos.tip_calculations import SimpleTip
from webapps.stripe_integration.models import StripeAccount
from webapps.user.models import User


class TransactionTipServiceTestCase(TestCase):
    @patch('webapps.pos.services.BasketTipService.delete_tips')
    def test_create_basket_tips_no_tip(
        self,
        _delete_tips_mock,
    ):
        tip = {
            'rate': 0,
            'amount': 0,
            'type': SimpleTip.TIP_TYPE__HAND,
        }
        TransactionTipService.create_basket_tips(
            basket=baker.make(Basket),
            txn=baker.make(Transaction),
            tip=tip,
            tip_rows=[],
            source=BasketTipSource.CHECKOUT,
            tip_already_paid=0,
        )

        self.assertEqual(BasketTip.all_objects.count(), 0)

    @patch('webapps.pos.services.BasketService.save_history')
    @patch('webapps.pos.services.BasketTipService.create_basket_tip')
    @patch('webapps.pos.services.BasketTipService.delete_tips')
    def test_create_basket_tips_single_tip_dict(
        self,
        _delete_tips_mock,
        create_basket_tip_mock,
        save_history_mock,
    ):
        basket_tip_id = uuid.uuid4()
        create_basket_tip_mock.return_value = MagicMock(id=basket_tip_id)

        tip = {
            'rate': 10,
            'amount': 10,
            'type': SimpleTip.TIP_TYPE__HAND,
        }
        TransactionTipService.create_basket_tips(
            basket=baker.make(Basket),
            txn=baker.make(Transaction),
            tip=tip,
            tip_rows=[],
            source=BasketTipSource.CHECKOUT,
            tip_already_paid=0,
        )

        self.assertEqual(create_basket_tip_mock.call_count, 1)
        self.assertEqual(create_basket_tip_mock.call_args[1]['staffer_id'], None)
        self.assertEqual(tip['basket_tip_id'], basket_tip_id)
        self.assertEqual(save_history_mock.call_count, 1)

    @patch('webapps.pos.services.BasketService.save_history')
    @patch('webapps.pos.services.BasketTipService.create_basket_tip')
    @patch('webapps.pos.services.BasketTipService.delete_tips')
    def test_create_basket_tips_single_tip_obj_with_staffer(
        self,
        _delete_tips_mock,
        create_basket_tip_mock,
        save_history_mock,
    ):
        business = baker.make(Business)
        pos = baker.make(POS, business=business)
        basket_tip_id = uuid.uuid4()
        create_basket_tip_mock.return_value = MagicMock(id=basket_tip_id)

        operator = baker.make(User)
        staffer = baker.make(
            Resource,
            staff_user=operator,
            type=Resource.STAFF,
            active=True,
            business_id=business.id,
        )
        tip = baker.make(
            TransactionTip,
            rate=10,
            amount=10,
            type=SimpleTip.TIP_TYPE__PERCENT,
        )
        TransactionTipService.create_basket_tips(
            basket=baker.make(Basket, business_id=business.id),
            txn=baker.make(Transaction, operator=operator, pos=pos),
            tip=tip,
            tip_rows=[],
            source=BasketTipSource.CHECKOUT,
            tip_already_paid=0,
        )

        self.assertEqual(create_basket_tip_mock.call_count, 1)
        self.assertEqual(create_basket_tip_mock.call_args[1]['staffer_id'], staffer.id)
        self.assertEqual(tip.basket_tip_id, basket_tip_id)
        self.assertEqual(save_history_mock.call_count, 1)

    @patch('webapps.pos.services.BasketService.save_history')
    @patch('webapps.pos.services.BasketTipService.create_basket_tip')
    @patch('webapps.pos.services.BasketTipService.delete_tips')
    def test_create_basket_tips_tip_rows_dict(
        self,
        _delete_tips_mock,
        create_basket_tip_mock,
        save_history_mock,
    ):
        business = baker.make(Business)
        basket_tip_id = uuid.uuid4()
        create_basket_tip_mock.return_value = MagicMock(id=basket_tip_id)

        operator = baker.make(User)
        staffer = baker.make(
            Resource,
            staff_user=operator,
            type=Resource.STAFF,
            active=True,
            business_id=business.id,
        )
        staffer2 = baker.make(
            Resource,
            staff_user=operator,
            type=Resource.STAFF,
            active=True,
            business_id=business.id,
        )

        tip_rows = [
            {
                'rate': 50,
                'amount': 10,
                'type': SimpleTip.TIP_TYPE__HAND,
                'staffer_id': staffer.id,
            },
            {
                'rate': 50,
                'amount': 10,
                'type': SimpleTip.TIP_TYPE__HAND,
                'staffer_id': staffer2.id,
            },
        ]

        TransactionTipService.create_basket_tips(
            basket=baker.make(Basket, business_id=business.id),
            txn=baker.make(Transaction),
            tip={
                'rate': 20,
                'amount': 20,
                'type': SimpleTip.TIP_TYPE__HAND,
            },
            tip_rows=tip_rows,
            source=BasketTipSource.CHECKOUT,
            tip_already_paid=0,
        )

        self.assertEqual(create_basket_tip_mock.call_count, 2)
        self.assertEqual(create_basket_tip_mock.call_args_list[0][1]['staffer_id'], staffer.id)
        self.assertEqual(create_basket_tip_mock.call_args_list[1][1]['staffer_id'], staffer2.id)
        self.assertEqual(tip_rows[0]['basket_tip_id'], basket_tip_id)
        self.assertEqual(tip_rows[1]['basket_tip_id'], basket_tip_id)
        self.assertEqual(save_history_mock.call_count, 1)

    @patch('webapps.pos.services.BasketService.save_history')
    @patch('webapps.pos.services.BasketTipService.create_basket_tip')
    @patch('webapps.pos.services.BasketTipService.delete_tips')
    def test_create_basket_tips_tip_rows_obj(
        self,
        _delete_tips_mock,
        create_basket_tip_mock,
        save_history_mock,
    ):
        business = baker.make(Business)

        basket_tip_id = uuid.uuid4()
        basket_tip_id2 = uuid.uuid4()
        create_basket_tip_mock.side_effect = [
            MagicMock(id=basket_tip_id),
            MagicMock(id=basket_tip_id2),
        ]

        operator = baker.make(User)
        staffer = baker.make(
            Resource,
            staff_user=operator,
            type=Resource.STAFF,
            active=True,
            business_id=business.id,
        )
        staffer2 = baker.make(
            Resource,
            staff_user=operator,
            type=Resource.STAFF,
            active=True,
            business_id=business.id,
        )

        tip_rows = [
            baker.make(
                TransactionTipRow,
                rate=50,
                amount=10,
                staffer_id=staffer.id,
            ),
            baker.make(
                TransactionTipRow,
                rate=50,
                amount=10,
                staffer_id=staffer2.id,
            ),
        ]

        TransactionTipService.create_basket_tips(
            basket=baker.make(Basket, business_id=business.id),
            txn=baker.make(Transaction),
            tip=baker.make(
                TransactionTip,
                rate=20,
                amount=20,
                type=SimpleTip.TIP_TYPE__HAND,
            ),
            tip_rows=tip_rows,
            source=BasketTipSource.CHECKOUT,
            tip_already_paid=0,
        )

        self.assertEqual(create_basket_tip_mock.call_count, 2)
        self.assertEqual(create_basket_tip_mock.call_args_list[0][1]['staffer_id'], staffer.id)
        self.assertEqual(create_basket_tip_mock.call_args_list[1][1]['staffer_id'], staffer2.id)
        self.assertEqual(tip_rows[0].basket_tip_id, basket_tip_id)
        self.assertEqual(tip_rows[1].basket_tip_id, basket_tip_id2)
        self.assertEqual(save_history_mock.call_count, 1)


class TransactionServiceTestCase(TestCase):
    @parameterized.expand(
        [
            (PaymentTypeEnum.PAY_BY_APP, PaymentProviderCode.ADYEN),
            (PaymentTypeEnum.PREPAYMENT, PaymentProviderCode.ADYEN),
            (PaymentTypeEnum.PAY_BY_APP_DONATIONS, PaymentProviderCode.ADYEN),
            (PaymentTypeEnum.STRIPE_TERMINAL, PaymentProviderCode.STRIPE),
            (PaymentTypeEnum.TAP_TO_PAY, PaymentProviderCode.STRIPE),
        ]
    )
    def test_get_payment_provider_wo_override(self, payment_type, provider_code):
        pos = baker.make(POS)

        provider = TransactionService.get_payment_provider_code(
            pos=pos,
            payment_type_code=payment_type,
        )
        self.assertEqual(provider, provider_code)

    @parameterized.expand(
        [
            (PaymentTypeEnum.PAY_BY_APP, PaymentProviderCode.ADYEN),
            (PaymentTypeEnum.PREPAYMENT, PaymentProviderCode.ADYEN),
            (PaymentTypeEnum.PAY_BY_APP_DONATIONS, PaymentProviderCode.ADYEN),
            (PaymentTypeEnum.STRIPE_TERMINAL, PaymentProviderCode.STRIPE),
            (PaymentTypeEnum.TAP_TO_PAY, PaymentProviderCode.STRIPE),
        ]
    )
    def test_get_payment_provider_force_stripe(self, payment_type, provider_code):
        pos = baker.make(
            POS,
            pos_refactor_stage2_enabled=True,
            _force_stripe_pba=True,
        )

        provider = TransactionService.get_payment_provider_code(
            pos=pos,
            payment_type_code=payment_type,
        )
        self.assertEqual(provider, provider_code)

    @parameterized.expand(
        [
            (PaymentTypeEnum.PAY_BY_APP, PaymentProviderCode.STRIPE),
            (PaymentTypeEnum.PREPAYMENT, PaymentProviderCode.STRIPE),
            (PaymentTypeEnum.PAY_BY_APP_DONATIONS, PaymentProviderCode.STRIPE),
            (PaymentTypeEnum.STRIPE_TERMINAL, PaymentProviderCode.STRIPE),
            (PaymentTypeEnum.TAP_TO_PAY, PaymentProviderCode.STRIPE),
        ]
    )
    def test_get_payment_provider_force_stripe_kyc_verified(self, payment_type, provider_code):
        pos = baker.make(
            POS,
            pos_refactor_stage2_enabled=True,
            _force_stripe_pba=True,
        )

        baker.make(StripeAccount, pos=pos, kyc_verified_at_least_once=True)

        provider = TransactionService.get_payment_provider_code(
            pos=pos,
            payment_type_code=payment_type,
        )
        self.assertEqual(provider, provider_code)

    @parameterized.expand(
        [
            (
                PaymentTypeEnum.PREPAYMENT,
                PaymentProviderEnum.ADYEN_PROVIDER,
                POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
            ),
            (
                PaymentTypeEnum.PREPAYMENT,
                PaymentProviderEnum.STRIPE_PROVIDER,
                POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
            ),
            (
                PaymentTypeEnum.PAY_BY_APP,
                PaymentProviderEnum.ADYEN_PROVIDER,
                POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
            ),
            (
                PaymentTypeEnum.PAY_BY_APP,
                PaymentProviderEnum.STRIPE_PROVIDER,
                POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
            ),
            (
                PaymentTypeEnum.PAY_BY_APP_DONATIONS,
                PaymentProviderEnum.ADYEN_PROVIDER,
                POSPlanPaymentTypeEnum.ADYEN_DONATIONS,
            ),
            (
                PaymentTypeEnum.PAY_BY_APP_DONATIONS,
                PaymentProviderEnum.STRIPE_PROVIDER,
                POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
            ),
            (
                PaymentTypeEnum.STRIPE_TERMINAL,
                PaymentProviderEnum.STRIPE_PROVIDER,
                POSPlanPaymentTypeEnum.STRIPE_TERMINAL,
            ),
            (
                PaymentTypeEnum.TAP_TO_PAY,
                PaymentProviderEnum.STRIPE_PROVIDER,
                POSPlanPaymentTypeEnum.TAP_TO_PAY,
            ),
            (
                PaymentTypeEnum.BOOKSY_PAY,
                PaymentProviderEnum.STRIPE_PROVIDER,
                POSPlanPaymentTypeEnum.BOOKSY_PAY,
            ),
        ]
    )
    def test_calculate_fees(self, payment_type_code, payment_provider_code, plan_type):
        pos = baker.make(POS)

        baker.make(
            POSPlan,
            plan_type=plan_type,
            provision=0.01,  # 1%
            txn_fee=2,  # $2
            refund_provision=0.02,
            refund_txn_fee=3,
            chargeback_provision=0.03,
            chargeback_txn_fee=7,
        )
        pos.recalculate_pos_plans()

        result = TransactionService.calculate_fees(
            pos=pos,
            payment_provider_code=payment_provider_code,
            payment_type_code=payment_type_code,
        )

        self.assertEqual(
            result,
            (
                PaymentSplitsEntity(
                    percentage_fee=Decimal('1'),
                    fixed_fee=200,
                ),
                RefundSplitsEntity(
                    percentage_fee=Decimal('2'),
                    fixed_fee=300,
                ),
                DisputeSplitsEntity(percentage_fee=Decimal('3'), fixed_fee=700),
            ),
        )

    def test_get_staffer_if_from_txn_operator_cached(self):
        business = baker.make(Business)
        pos = baker.make(POS, business=business)

        operator = baker.make(User)
        staffer = baker.make(
            Resource,
            staff_user=operator,
            type=Resource.STAFF,
            active=True,
            business_id=business.id,
        )

        txn = baker.make(Transaction, operator=operator, pos=pos)
        self.assertEqual(TransactionService.get_staffer_id_from_txn_operator(txn), staffer.id)

        txn2 = baker.make(Transaction)
        self.assertEqual(TransactionService.get_staffer_id_from_txn_operator(txn2), None)

        txn3 = baker.make(Transaction)
        txn3._staffer_id = 123123123  # pylint: disable=protected-access
        self.assertEqual(TransactionService.get_staffer_id_from_txn_operator(txn3), 123123123)

    def test_action__cancel_payment_invalid_pr(self):
        txn = baker.make(Transaction)
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()

        pr = baker.make(PaymentRow, receipt=receipt, payment_type__code=PaymentTypeEnum.PAY_BY_APP)

        result = TransactionService.action__cancel_payment(txn, 'test msg', pr.id + 100)
        self.assertEqual(result, False)

    @patch('webapps.pos.provider.adyen_ee.AdyenEEPaymentProvider.cancel_payment')
    def test_action__cancel_payment_no_pr_id(self, cancel_payment_mock):
        cancel_payment_mock.return_value = True

        txn = baker.make(Transaction)
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()

        pr = baker.make(
            PaymentRow,
            receipt=receipt,
            status=receipt_status.CALL_FOR_PAYMENT,
            payment_type__code=PaymentTypeEnum.PAY_BY_APP,
        )

        result = TransactionService.action__cancel_payment(txn, 'test_msg')
        self.assertEqual(result, True)
        self.assertEqual(cancel_payment_mock.call_args[1]['payment_row'], pr)
        self.assertEqual(cancel_payment_mock.call_args[1]['log_note'], 'test_msg')

    @patch('webapps.pos.provider.adyen_ee.AdyenEEPaymentProvider.cancel_payment')
    def test_action__cancel_payment_pr_id(self, cancel_payment_mock):
        cancel_payment_mock.return_value = True

        txn = baker.make(Transaction)
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            receipt=receipt,
            status=receipt_status.CALL_FOR_PAYMENT,
            payment_type__code=PaymentTypeEnum.PAY_BY_APP,
        )

        pr2 = baker.make(
            PaymentRow,
            receipt=receipt,
            status=receipt_status.CALL_FOR_PAYMENT,
            payment_type__code=PaymentTypeEnum.PREPAYMENT,
        )

        result = TransactionService.action__cancel_payment(txn, 'test_msg', pr2.id)
        self.assertEqual(result, True)
        self.assertEqual(cancel_payment_mock.call_args[1]['payment_row'], pr2)
        self.assertEqual(cancel_payment_mock.call_args[1]['log_note'], 'test_msg')

    @parameterized.expand(
        [
            (PaymentTypeEnum.PAY_BY_APP, receipt_status.CALL_FOR_PAYMENT, False, True),
            (PaymentTypeEnum.PREPAYMENT, receipt_status.CALL_FOR_PREPAYMENT, False, True),
            (PaymentTypeEnum.BOOKSY_PAY, receipt_status.CALL_FOR_BOOKSY_PAY, True, True),
            (PaymentTypeEnum.BOOKSY_PAY, receipt_status.BOOKSY_PAY_FAILED, True, True),
            (PaymentTypeEnum.BOOKSY_PAY, receipt_status.BOOKSY_PAY_SUCCESS, True, False),
        ]
    )
    @patch('webapps.pos.provider.adyen_ee.AdyenEEPaymentProvider.cancel_payment')
    @patch('webapps.pos.provider.proxy.ProxyProvider.cancel_payment')
    def test_action__cancel_payment_different_payment_types(
        self,
        payment_type: PaymentTypeEnum,
        status: str,
        pos_refactor_stage2_enabled: bool,
        expected: bool,
        proxy_cancel_payment_mock: mock.MagicMock,
        adyen_cancel_payment_mock: mock.MagicMock,
    ) -> None:
        adyen_cancel_payment_mock.return_value = True
        proxy_cancel_payment_mock.return_value = True
        business = baker.make(Business)
        pos = baker.make(
            POS, business=business, pos_refactor_stage2_enabled=pos_refactor_stage2_enabled
        )
        txn = baker.make(Transaction, pos=pos)
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()
        pr = baker.make(
            PaymentRow,
            receipt=receipt,
            status=status,
            payment_type__code=payment_type,
        )

        result = TransactionService.action__cancel_payment(txn, 'test msg', pr.id)
        self.assertEqual(result, expected)

        if expected is False:
            proxy_cancel_payment_mock.assert_not_called()
            adyen_cancel_payment_mock.assert_not_called()
        elif payment_type in PaymentTypeEnum.stripe_only_methods():
            proxy_cancel_payment_mock.assert_called_once()
            adyen_cancel_payment_mock.assert_not_called()
        else:
            proxy_cancel_payment_mock.assert_not_called()
            adyen_cancel_payment_mock.assert_called_once()

    @parameterized.expand(
        [
            (PaymentTypeEnum.PAY_BY_APP, receipt_status.CALL_FOR_PAYMENT, False),
            (PaymentTypeEnum.PREPAYMENT, receipt_status.CALL_FOR_PREPAYMENT, False),
            (PaymentTypeEnum.BOOKSY_PAY, receipt_status.CALL_FOR_BOOKSY_PAY, True),
        ]
    )
    @patch('webapps.pos.provider.adyen_ee.AdyenEEPaymentProvider.cancel_payment')
    @patch('webapps.pos.provider.proxy.ProxyProvider.cancel_payment')
    def test_action__cancel_payment_stripe_only_method_protection(
        self,
        payment_type: PaymentTypeEnum,
        status: str,
        should_raise: bool,
        proxy_cancel_payment_mock: mock.MagicMock,
        adyen_cancel_payment_mock: mock.MagicMock,
    ) -> None:
        adyen_cancel_payment_mock.return_value = True
        proxy_cancel_payment_mock.return_value = True
        business = baker.make(Business)
        pos = baker.make(POS, business=business, pos_refactor_stage2_enabled=False)
        txn = baker.make(Transaction, pos=pos)
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()
        pr = baker.make(
            PaymentRow,
            receipt=receipt,
            status=status,
            payment_type__code=payment_type,
        )

        if should_raise:
            with self.assertRaises(RuntimeError) as context:
                TransactionService.action__cancel_payment(txn, 'test msg', pr.id)

            self.assertEqual(
                f'Unexpected call for {payment_type.label}',
                str(context.exception),
            )
        else:
            try:
                TransactionService.action__cancel_payment(txn, 'test msg', pr.id)
            except RuntimeError:  # pylint: disable=broad-exception-caught
                self.fail("Exception raised unexpectedly!")

    def test_get_transaction(self):
        transaction = baker.make(Transaction)
        receipt = baker.make(Receipt, transaction=transaction)
        payment_row = baker.make(PaymentRow, basket_payment_id=uuid.uuid4(), receipt=receipt)
        self.assertEqual(
            TransactionService.get_transaction(basket_payment_id=payment_row.basket_payment_id),
            transaction,
        )
