from unittest.mock import patch
import uuid
import pytest
from django.test import TestCase
from model_bakery import baker

from webapps.pos.models import PaymentRow, Receipt, Transaction
from webapps.pos.ports import PaymentRowPort, TransactionPort
from webapps.pos.tests.pos_refactor.helpers import get_basket_payment_entity


@pytest.mark.django_db
class TestTransactionPort(TestCase):
    @patch('webapps.pos.services.TransactionService.is_off_session_payment')
    def test_is_off_session_transaction(self, is_off_session_payment_mock):
        is_off_session_payment_mock.return_value = True

        transaction = baker.make(Transaction)
        receipt = baker.make(
            Receipt,
            transaction=transaction,
        )

        self.assertTrue(
            TransactionPort.is_off_session_transaction(
                receipt_id=receipt.id,
            )
        )
        is_off_session_payment_mock.assert_called_once_with(
            txn=transaction,
        )

    def test_get_transaction(self):
        basket_payment_id = uuid.uuid4()
        transaction = baker.make(Transaction)
        receipt = baker.make(Receipt, transaction=transaction)
        baker.make(PaymentRow, basket_payment_id=basket_payment_id, receipt=receipt)

        transaction_entity = TransactionPort.get_transaction(basket_payment_id=basket_payment_id)

        self.assertEqual(transaction_entity.id, transaction_entity.id)


@pytest.mark.django_db
class TestPaymentRowPort(TestCase):
    def test_get_last_payment_row_entity(self):
        basket_payment_entity = get_basket_payment_entity()
        baker.make(
            PaymentRow,
            basket_payment_id=basket_payment_entity.id,
        )
        payment_row2 = baker.make(
            PaymentRow,
            basket_payment_id=basket_payment_entity.id,
        )

        payment_row_entity = PaymentRowPort.get_last_payment_row_entity(
            basket_payment_id=basket_payment_entity.id
        )
        self.assertEqual(
            payment_row_entity.id,
            payment_row2.id,
        )
