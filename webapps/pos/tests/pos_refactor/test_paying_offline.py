import pytest
from model_bakery import baker

from lib.point_of_sale.enums import BasketPayment<PERSON>tatus, BasketPaymentType, BasketType
from lib.point_of_sale.enums import PaymentMethodType as PointOfSalePaymentMethodType
from webapps.booking.tests.utils import create_appointment
from webapps.business.models import Service, ServiceVariant
from webapps.payment_gateway.models import BalanceTransaction
from webapps.point_of_sale.models import Basket, BasketPayment
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import PaymentType, Transaction
from webapps.pos.serializers import TransactionSerializer
from webapps.pos.tests.pos_refactor import TestTransactionSerializerBase
from webapps.voucher.models import Voucher, VoucherServiceVariant, VoucherTemplate


@pytest.mark.usefixtures('default_voucher_background')
class TestTransactionSerializerPayingOffline(TestTransactionSerializerBase):
    def setUp(self):
        super().setUp()

        self.sv = baker.make(
            ServiceVariant,
            service=baker.make(Service, business=self.business),
            price=987.56,
            duration='0100',
            time_slot_interval='0000',
            gap_hole_duration='0000',
            gap_hole_start_after='0000',
        )

        self.appointment = create_appointment(
            [
                {
                    'service_variant_id': self.sv.id,
                }
            ],
            business=self.business,
            booked_for=self.bci,
        )

    def create_and_check_paying_offline(
        self, payment_type: PaymentTypeEnum, payment_method_type: PointOfSalePaymentMethodType
    ):
        txn = self.create_transaction(payment_type)
        self._check_paying_offline(txn, payment_method_type)

    def _check_paying_offline(
        self, txn: Transaction, payment_method_type: PointOfSalePaymentMethodType
    ):
        self.assertEqual(txn.receipts.all().count(), 1)
        basket = Basket.objects.get(id=txn.basket_id)
        self.assertEqual(basket.type, BasketType.PAYMENT)
        self.assertEqual(basket.archived, None)
        self.assertEqual(basket.business_id, self.business.id)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method_type,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BalanceTransaction.objects.count(), 0)

    def _get_paying_data(self):
        return {
            'dry_run': False,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'bookings': [
                {
                    'booking_id': booking.id,
                    'item_price': 123.45,
                }
                for booking in self.appointment.subbookings
            ],
            'customer_card_id': self.bci.id,
        }

    def test_paying_with_cash(self):
        self.create_and_check_paying_offline(
            PaymentTypeEnum.CASH, PointOfSalePaymentMethodType.CASH
        )

    def test_paying_with_check(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.CHECK)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.CHEQUE)
        self.create_and_check_paying_offline(
            PaymentTypeEnum.CHECK, PointOfSalePaymentMethodType.CHECK
        )
        self.create_and_check_paying_offline(
            PaymentTypeEnum.CHEQUE, PointOfSalePaymentMethodType.CHECK
        )

    def test_paying_with_credit_card(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.CREDIT_CARD)
        self.create_and_check_paying_offline(
            PaymentTypeEnum.CREDIT_CARD, PointOfSalePaymentMethodType.CREDIT_CARD
        )

    def test_paying_with_subscription(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.SUBSCRIPTION)
        self.create_and_check_paying_offline(
            PaymentTypeEnum.SUBSCRIPTION, PointOfSalePaymentMethodType.SUBSCRIPTION
        )

    def test_paying_with_store_credit(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.STORE_CREDIT)
        self.create_and_check_paying_offline(
            PaymentTypeEnum.STORE_CREDIT, PointOfSalePaymentMethodType.STORE_CREDIT
        )

    def test_paying_with_bank_transfer(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.BANK_TRANSFER)
        self.create_and_check_paying_offline(
            PaymentTypeEnum.BANK_TRANSFER, PointOfSalePaymentMethodType.BANK_TRANSFER
        )

    def test_paying_with_american_express(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.AMERICAN_EXPRESS)
        self.create_and_check_paying_offline(
            PaymentTypeEnum.AMERICAN_EXPRESS, PointOfSalePaymentMethodType.AMERICAN_EXPRESS
        )

    def test_paying_with_paypal(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAYPAL)
        self.create_and_check_paying_offline(
            PaymentTypeEnum.PAYPAL, PointOfSalePaymentMethodType.PAYPAL
        )

    def test_paying_with_direct_payment(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.DIRECT_PAYMENT)
        self.create_and_check_paying_offline(
            PaymentTypeEnum.DIRECT_PAYMENT, PointOfSalePaymentMethodType.DIRECT_PAYMENT
        )

    def test_paying_with_giftcard(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.GIFT_CARD)
        self.create_and_check_paying_offline(
            PaymentTypeEnum.GIFT_CARD, PointOfSalePaymentMethodType.GIFT_CARD
        )

    def test_paying_with_voucher(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.VOUCHER)
        self.create_and_check_paying_offline(
            PaymentTypeEnum.VOUCHER, PointOfSalePaymentMethodType.VOUCHER
        )

    def test_paying_with_egiftcard(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.EGIFT_CARD)

        voucher = baker.make(
            Voucher,
            pos=self.pos,
            current_balance=500,
            status=Voucher.ACTIVE,
            voucher_template=baker.make(
                VoucherTemplate,
                pos=self.pos,
                type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            ),
        )

        data = self._get_paying_data()
        data['payment_rows'] = [
            {
                'payment_type_code': PaymentTypeEnum.EGIFT_CARD,
                'amount': 123.45,
                'voucher_id': voucher.id,
            }
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
            },
        )

        assert serializer.is_valid(), serializer.errors
        txn = serializer.save()

        self._check_paying_offline(txn, PointOfSalePaymentMethodType.EGIFT_CARD)

    def test_paying_with_package(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PACKAGE)
        voucher = baker.make(
            Voucher,
            pos=self.pos,
            current_balance=500,
            status=Voucher.ACTIVE,
            voucher_template=baker.make(
                VoucherTemplate,
                pos=self.pos,
                type=Voucher.VOUCHER_TYPE__PACKAGE,
            ),
        )

        baker.make(
            VoucherServiceVariant,
            voucher=voucher,
            service_variant=self.sv,
            amount=2,
            item_price=20,
        )

        data = self._get_paying_data()
        data['payment_rows'] = [
            {
                'payment_type_code': PaymentTypeEnum.PACKAGE,
                'voucher_service_id': self.sv.id,
                'amount': 0,
                'voucher_id': voucher.id,
            }
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
            },
        )

        assert serializer.is_valid(), serializer.errors
        txn = serializer.save()

        self._check_paying_offline(txn, PointOfSalePaymentMethodType.PACKAGE)

    def test_paying_with_membership(self):
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.MEMBERSHIP)
        voucher = baker.make(
            Voucher,
            pos=self.pos,
            current_balance=500,
            status=Voucher.ACTIVE,
            voucher_template=baker.make(
                VoucherTemplate,
                pos=self.pos,
                type=Voucher.VOUCHER_TYPE__MEMBERSHIP,
            ),
        )

        baker.make(
            VoucherServiceVariant,
            voucher=voucher,
            service_variant=self.sv,
            amount=-1,
            item_price=20,
        )

        data = self._get_paying_data()
        data['payment_rows'] = [
            {
                'payment_type_code': PaymentTypeEnum.MEMBERSHIP,
                'voucher_service_id': self.sv.id,
                'amount': 0,
                'voucher_id': voucher.id,
            }
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
            },
        )

        assert serializer.is_valid(), serializer.errors
        txn = serializer.save()

        self._check_paying_offline(txn, PointOfSalePaymentMethodType.MEMBERSHIP)
