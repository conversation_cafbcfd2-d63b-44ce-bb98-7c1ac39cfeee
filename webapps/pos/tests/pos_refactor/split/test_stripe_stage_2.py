from unittest.mock import patch

import pytest
from mock import mock
from model_bakery import baker

from lib.feature_flag.feature.payment import UpdateBGCPaymentRowIfPBASucceededFlag
from lib.payments.enums import PaymentProviderCode
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import minor_unit
from service.pos.business_transactions import BaseAutoPayHandler
from webapps.payment_providers.consts.stripe import RefundStripeStatus
from webapps.pos.enums import (
    PaymentProviderEnum,
    PaymentTypeEnum,
)
from webapps.pos.models import (
    PaymentMethod,
    Transaction,
)
from webapps.pos.provider import get_payment_provider
from webapps.pos.tests.pos_refactor.helpers_stripe import StripeMixin
from webapps.pos.tests.pos_refactor.split.base import TestTransactionSerializerSplitBase


class TestTransactionSerializerSplit(StripeMixin, TestTransactionSerializerSplitBase):
    def setUp(self):
        super().setUp()

        self.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.pos.save()

        self.payment_method = baker.make(
            PaymentMethod, provider=PaymentProviderEnum.STRIPE_PROVIDER
        )
        self.provider = get_payment_provider(
            codename=self.payment_method.provider,
            txn=Transaction(pos=self.pos),
        )

        self.create_stripe_related_obj()
        self.provider_code = PaymentProviderCode.STRIPE
        self.random_id = self.generate_id()

    def test_split_cash_check(self):
        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CHECK,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.CASH,
                    'amount': 23.45,
                },
            ]
        )

        self._check_split_cash_check(txn)

    def test_split_cash_pba_cfp(self):
        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CHECK,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 23.45,
                },
            ]
        )
        self._check_split_cash_pba_cfp(txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_split_cash_pba_success(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CHECK,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 23.45,
                },
            ]
        )

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=baker.make(PaymentMethod),
            payment_row=txn.payment_rows[1],
        )
        self._success_payment_notifications(external_id=self.random_id)

        self._check_split_cash_pba_success(txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_split_cash_pba_send_for_refund(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)

        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CHECK,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 23.45,
                },
            ]
        )

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=baker.make(PaymentMethod),
            payment_row=txn.payment_rows[1],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[1]

        # Send for refund
        success_row.send_for_refund(
            operator=self.user,
        )

        txn.refresh_from_db()
        self._check_split_cash_pba_send_for_refund(txn, success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_split_cash_pba_refund(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)

        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CHECK,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 23.45,
                },
            ]
        )

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=baker.make(PaymentMethod),
            payment_row=txn.payment_rows[1],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[1]

        # Send for refund
        send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )
        send_for_refund_row.refresh_from_db()

        # Refund
        self._handle_refund_notification(
            status=RefundStripeStatus.SUCCEEDED,
            external_id=self.random_id,
            intent_external_id=self.random_id,
        )

        self._check_split_cash_pba_refund(txn, success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_split_cash_pba_refund_fail(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)

        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CHECK,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 23.45,
                },
            ]
        )

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[1],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[1]

        # Send for refund
        send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )
        send_for_refund_row.refresh_from_db()

        # Refund
        self._handle_refund_notification(
            status=RefundStripeStatus.FAILED,
            external_id=self.random_id,
            intent_external_id=self.random_id,
        )

        self._check_split_cash_pba_refund_fail(txn, success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_transfer')
    def test_split_cash_pba_chargeback(
        self,
        create_transfer_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_transfer_mock.side_effect = lambda **_: mock.MagicMock(id=self.generate_id())
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CHECK,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 23.45,
                },
            ]
        )

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[1],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[1]

        # Chargeback
        self._handle_chargeback_notification(amount=minor_unit(23.45))

        self._check_split_cash_pba_chargeback(txn, success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_transfer')
    def test_split_cash_pba_chargeback_reversed(
        self,
        create_transfer_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_transfer_mock.side_effect = lambda **_: mock.MagicMock(id=self.generate_id())
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CHECK,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 23.45,
                },
            ]
        )

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[1],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[1]

        # Chargeback
        self._handle_chargeback_notification(amount=minor_unit(23.45))

        txn.refresh_from_db()
        chargeback_row = txn.payment_rows[1]

        # Chargeback reverse
        self._handle_reversed_chargeback_notification(amount=minor_unit(23.45))

        self._check_split_cash_pba_chargeback_reversed(txn, success_row, chargeback_row)

    @override_eppo_feature_flag({UpdateBGCPaymentRowIfPBASucceededFlag.flag_name: True})
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_split_bgc_pba_success(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.BOOKSY_GIFT_CARD,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 23.45,
                },
            ]
        )

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=baker.make(PaymentMethod),
            payment_row=txn.payment_rows[1],
        )
        self._success_payment_notifications(external_id=self.random_id)
        self._check_split_bgc_pba_success(txn, PaymentTypeEnum.PAY_BY_APP)

    @override_eppo_feature_flag({UpdateBGCPaymentRowIfPBASucceededFlag.flag_name: False})
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_split_bgc_pba_fail_because_flag_off(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.BOOKSY_GIFT_CARD,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 23.45,
                },
            ]
        )

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=baker.make(PaymentMethod),
            payment_row=txn.payment_rows[1],
        )
        with pytest.raises(AssertionError):
            self._success_payment_notifications(external_id=self.random_id)

    @override_eppo_feature_flag({UpdateBGCPaymentRowIfPBASucceededFlag.flag_name: True})
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_split_bgc_blik_success(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.BOOKSY_GIFT_CARD,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.BLIK,
                    'amount': 23.45,
                },
            ]
        )
        BaseAutoPayHandler().auto_accept_blik(
            blik_code='123456',
            pos=self.pos,
            transaction=txn,
            device_data=self.device_data_dict['device_data'],
        )

        self._success_payment_notifications(external_id=self.random_id)
        self._check_split_bgc_pba_success(txn, PaymentTypeEnum.BLIK)

    @override_eppo_feature_flag({UpdateBGCPaymentRowIfPBASucceededFlag.flag_name: True})
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.cancel_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_split_bgc_pba_payment_cancel(
        self,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
        cancel_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        cancel_payment_intent_mock.return_value = True

        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.BOOKSY_GIFT_CARD,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.PAY_BY_APP,
                    'amount': 23.45,
                },
            ]
        )

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=baker.make(PaymentMethod),
            payment_row=txn.payment_rows[1],
        )

        self.provider.cancel_payment(txn.payment_rows[1])
        self._check_split_bgc_pba_cancel(txn, PaymentTypeEnum.PAY_BY_APP)
