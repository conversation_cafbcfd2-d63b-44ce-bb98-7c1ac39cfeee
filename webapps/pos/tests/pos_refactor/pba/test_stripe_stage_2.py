from unittest.mock import Mock, patch

import pytest
import responses
from mock import mock
from model_bakery import baker

from lib.tests.utils import override_feature_flag
from lib.feature_flag.feature.payment import StripeCheckRefundOnlineBalance
from lib.payments.enums import PaymentProviderCode, PaymentError
from lib.point_of_sale.enums import BasketItemType
from webapps.payment_providers.consts.stripe import RefundStripeStatus
from webapps.payment_providers.consts.stripe_error_code import Decline<PERSON>ode
from webapps.payment_providers.models import (
    Payment,
)
from webapps.payment_providers.services.stripe.stripe import StripeProvider
from webapps.pos.enums import (
    CARD_TYPE__APPLE_PAY,
    CARD_TYPE__GOOGLE_PAY,
    PaymentProviderEnum,
    PaymentTypeEnum,
)
from webapps.pos.models import (
    PaymentMethod,
    PaymentType,
    Transaction,
)
from webapps.pos.provider import get_payment_provider
from webapps.pos.refund import (
    RefundNonPossible,
    is_refund_possible,
)
from webapps.pos.tests.pos_refactor.helpers_stripe import StripeMixin
from webapps.pos.tests.pos_refactor.pba.base import TestTransactionSerializerPBABase
from webapps.stripe_integration.enums import StripeExternalAccountType
from webapps.french_certification.models import FiscalReceipt
from webapps.invoicing.tests.common import (
    french_certification_enabled,
)
from webapps.invoicing.baker_recipes import fc_seller_recipe
from webapps.stripe_integration.tests.mocks import mock_stripe_balance_retrieve


@pytest.mark.usefixtures('default_voucher_background')
class TestTransactionSerializerStripePBA(StripeMixin, TestTransactionSerializerPBABase):
    def setUp(self):
        super().setUp()

        self.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.pos.save()

        self.pba = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        self.payment_method = baker.make(
            PaymentMethod, provider=PaymentProviderEnum.STRIPE_PROVIDER
        )
        self.provider = get_payment_provider(
            codename=self.payment_method.provider,
            txn=Transaction(pos=self.pos),
        )

        self.create_stripe_related_obj()

        self.provider_code = PaymentProviderCode.STRIPE
        self.random_id = self.generate_id()

    @responses.activate
    def test_pba_call_for_payment(self):
        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)
        self._check_pba_call_for_payment(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_call_for_payment_3ds(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_3ds_required_response(confirm_payment_intent_mock, self.random_id)
        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction, but additional verification is needed
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        txn.refresh_from_db()
        tds_data = Payment.objects.order_by('-created').first().action_required_details
        self._check_pba_call_for_payment_3ds(txn=txn, tds_data=tds_data)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_success(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        self._check_pba_success(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.cancel_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_success_cancel_race_condition_cancel_success(
        self,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
        cancel_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        cancel_payment_intent_mock.return_value = True

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        # Success notification and cancel are related to same receipt - race condition
        self.provider.cancel_payment(txn.payment_rows[0])  # BusinessAction - Success

        self._check_pba_success_cancel_race_condition_cancel_success(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.cancel_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_success_cancel_race_condition_cancel_fail(
        self,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
        cancel_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        cancel_payment_intent_mock.return_value = False

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        # Success notification and cancel are related to same receipt - race condition
        self.provider.cancel_payment(txn.payment_rows[0])  # BusinessAction - Fail
        self._success_payment_notifications(external_id=self.random_id)

        self._check_pba_success(txn=txn)

    @french_certification_enabled(certification_enabled=True)
    @patch(
        'webapps.french_certification.actions.LoyaltyCardService.get_loyalty_card_transaction_data',
        Mock(return_value={'total_stamps': 5}),
    )
    @patch.object(StripeProvider, 'create_payment_intent')
    @patch.object(StripeProvider, 'confirm_payment_intent')
    @patch.object(StripeProvider, 'capture_payment_intent', mock.Mock())
    def test_pba_success_creates_fiscal_receipt(
        self, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        fc_seller_recipe.make(business=self.business)
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        self._check_pba_success(txn=txn)

        self.assertEqual(FiscalReceipt.objects.count(), 1)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_success_selling_product(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self._prepare_transaction_selling_things()

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        self._check_pba_success_selling_things(txn=txn)

    @french_certification_enabled(certification_enabled=True)
    @patch(
        'webapps.french_certification.actions.LoyaltyCardService.get_loyalty_card_transaction_data',
        Mock(return_value={'total_stamps': 5}),
    )
    @patch.object(StripeProvider, 'create_payment_intent')
    @patch.object(StripeProvider, 'confirm_payment_intent')
    @patch.object(StripeProvider, 'capture_payment_intent', mock.Mock())
    def test_pba_success_selling_product_creates_fiscal_receipt(
        self, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        fc_seller_recipe.make(business=self.business)
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self._prepare_transaction_selling_things()

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        self._check_pba_success_selling_things(txn=txn)
        self.assertEqual(FiscalReceipt.objects.count(), 1)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_success_selling_voucher(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self._prepare_transaction_selling_vouchers()

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        self._check_pba_success_selling_things(
            txn=txn,
            basket_item_type=BasketItemType.VOUCHER,
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_success_google_pay(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        # Important - its not saved
        self.payment_method.card_type = CARD_TYPE__GOOGLE_PAY
        self.payment_method.token = '123'

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        self._check_pba_success_google_pay(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_success_apple_pay(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        # Important - its not saved
        self.payment_method.card_type = CARD_TYPE__APPLE_PAY
        self.payment_method.token = '123'

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        self._check_pba_success_apple_pay(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_success_with_fees(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        self.stripe_plan.provision = 0.15
        self.stripe_plan.txn_fee = 21
        self.stripe_plan.save()

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        self._check_pba_success_with_fees(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_3ds_success(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_3ds_required_response(confirm_payment_intent_mock, self.random_id)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction, but additional verification is needed
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._success_payment_notifications()

        tds_data = Payment.objects.order_by('-created').first().action_required_details
        txn.refresh_from_db()
        self._check_pba_success_3ds(txn=txn, tds_data=tds_data)

    def test_pba_canceled(self):
        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        self.provider.cancel_payment(txn.payment_rows[0])

        txn.refresh_from_db()
        self._check_pba_canceled(txn=txn)

    def test_pba_canceled_retry_cash(self):
        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Cancel transaction
        self.provider.cancel_payment(txn.payment_rows[0])
        txn.refresh_from_db()

        txn2 = self.edit_transaction(
            old_txn=txn,
            edit=False,
            payment_typ_code=PaymentTypeEnum.CASH,
        )

        txn.refresh_from_db()
        txn2.refresh_from_db()
        self._check_pba_canceled_retry_cash(txn=txn, txn2=txn2)

    def test_pba_canceled_retry_pba_cfp(self):
        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Cancel transaction
        self.provider.cancel_payment(txn.payment_rows[0])

        # EDIT - PBA - cfp
        txn2 = self.edit_transaction(
            old_txn=txn,
            edit=False,
            payment_typ_code=PaymentTypeEnum.PAY_BY_APP,
        )

        txn.refresh_from_db()
        txn2.refresh_from_db()
        self._check_pba_canceled_retry_pba_cfp(txn=txn, txn2=txn2)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_canceled_retry_pba_success(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Cancel transaction
        self.provider.cancel_payment(txn.payment_rows[0])

        # EDIT - success with PBA
        txn2 = self.edit_transaction(
            old_txn=txn,
            edit=False,
            payment_typ_code=PaymentTypeEnum.PAY_BY_APP,
        )

        # Pay for transaction
        self.make_payment(
            transaction=txn2,
            payment_method=self.payment_method,
            payment_row=txn2.payment_rows[0],
        )

        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        txn2.refresh_from_db()
        self._check_pba_canceled_retry_pba_success(txn=txn, txn2=txn2)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_pba_auth_fail(self, confirm_payment_intent_mock, create_payment_intent_mock):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(
            confirm_payment_intent_mock,
            False,
            self.random_id,
            DeclineCode.EXPIRED_CARD,
            raise_exception=True,
        )

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._handle_auth_notification(
            success=False, decline_code=DeclineCode.EXPIRED_CARD, external_id=self.random_id
        )

        txn.refresh_from_db()
        self._check_pba_auth_fail(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch(
        'webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent',
    )
    def test_pba_auth_fail_invalid_request(
        self, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response_for_invalid_request_error(
            confirm_payment_intent_mock,
        )
        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        txn.refresh_from_db()
        self._check_pba_auth_fail(
            txn=txn,
            error_code=PaymentError.GENERIC_ERROR,
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_pba_auth_fail_google_pay(
        self, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(
            confirm_payment_intent_mock,
            False,
            self.random_id,
            DeclineCode.LOST_CARD,
        )

        # Important - its not saved
        self.payment_method.card_type = CARD_TYPE__GOOGLE_PAY
        self.payment_method.token = '123'

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._handle_auth_notification(
            success=False, decline_code=DeclineCode.LOST_CARD, external_id=self.random_id
        )

        txn.refresh_from_db()
        self._check_pba_auth_fail_google_pay(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_pba_auth_fail_apple_pay(self, confirm_payment_intent_mock, create_payment_intent_mock):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(
            confirm_payment_intent_mock,
            False,
            self.random_id,
            DeclineCode.INVALID_CVC,
        )

        # Important - its not saved
        self.payment_method.card_type = CARD_TYPE__APPLE_PAY
        self.payment_method.token = '123'

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._handle_auth_notification(
            success=False, decline_code=DeclineCode.INVALID_CVC, external_id=self.random_id
        )

        txn.refresh_from_db()
        self._check_pba_auth_fail_apple_pay(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_capture_fail(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._handle_auth_notification(success=True, external_id=self.random_id)
        self._handle_capture_notification(
            success=False, decline_code=DeclineCode.INVALID_CVC, external_id=self.random_id
        )

        txn.refresh_from_db()
        self._check_pba_capture_fail(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_capture_fail_google_pay(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        # Important - its not saved
        self.payment_method.card_type = CARD_TYPE__GOOGLE_PAY
        self.payment_method.token = '123'

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._handle_auth_notification(success=True, external_id=self.random_id)
        self._handle_capture_notification(
            success=False, decline_code=DeclineCode.EXPIRED_CARD, external_id=self.random_id
        )

        txn.refresh_from_db()
        self._check_pba_capture_fail_google_pay(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_capture_fail_apple_pay(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        # Important - its not saved
        self.payment_method.card_type = CARD_TYPE__APPLE_PAY
        self.payment_method.token = '123'

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._handle_auth_notification(success=True, external_id=self.random_id)
        self._handle_capture_notification(
            success=False, decline_code=DeclineCode.EXPIRED_CARD, external_id=self.random_id
        )

        txn.refresh_from_db()
        self._check_pba_capture_fail_apple_pay(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_3ds_fail(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_3ds_required_response(confirm_payment_intent_mock, self.random_id)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction, but additional verification is needed
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )
        self._handle_payment_intent_failed_3ds_notification(external_id=self.random_id)

        tds_data = Payment.objects.order_by('-created').first().action_required_details
        self._check_pba_3ds_fail(
            txn=txn, tds_data=tds_data, error_code=PaymentError.AUTHENTICATION_REQUIRED
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_failed_cancel(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(
            confirm_payment_intent_mock,
            False,
            self.random_id,
            DeclineCode.EXPIRED_CARD,
        )

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._handle_auth_notification(
            success=False, decline_code=DeclineCode.EXPIRED_CARD, external_id=self.random_id
        )

        txn.refresh_from_db()

        # Cancel transaction
        self.provider.cancel_payment(txn.payment_rows[0])

        txn.refresh_from_db()
        self._check_pba_failed_cancel(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_failed_cancel_retry_cfp(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )

        self._handle_auth_notification(success=True, external_id=self.random_id)
        self._handle_capture_notification(
            success=False, decline_code=DeclineCode.INVALID_CVC, external_id=self.random_id
        )
        txn.refresh_from_db()

        # Cancel transaction
        self.provider.cancel_payment(txn.payment_rows[0])

        # retry
        txn2 = self.edit_transaction(
            old_txn=txn,
            edit=False,
            payment_typ_code=PaymentTypeEnum.PAY_BY_APP,
        )

        txn.refresh_from_db()
        txn2.refresh_from_db()
        self._check_pba_failed_cancel_retry_cfp(txn=txn, txn2=txn2)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_pba_send_for_refund(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )

        txn.refresh_from_db()
        self._check_pba_send_for_refund(
            txn=txn, success_row=success_row, send_for_refund_row=send_for_refund_row
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_pba_refund(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )
        send_for_refund_row.refresh_from_db()

        # Refund
        self._handle_refund_notification(
            status=RefundStripeStatus.SUCCEEDED,
            external_id=self.random_id,
            intent_external_id=self.random_id,
        )

        txn.refresh_from_db()
        self._check_pba_refund(txn=txn, success_row=success_row)

        all_payment_rows_ids = []
        for receipt in txn.receipts.all():
            all_payment_rows_ids.extend(receipt.payment_rows.values_list('id', flat=True))

        self.assertEqual(
            set(txn.payment_rows.last().intents.last().payment_rows.values_list('id', flat=True)),
            set(all_payment_rows_ids),
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_pba_refund_with_ff(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )
        send_for_refund_row.refresh_from_db()

        # Refund
        self._handle_refund_notification(
            status=RefundStripeStatus.SUCCEEDED,
            external_id=self.random_id,
            intent_external_id=self.random_id,
        )

        txn.refresh_from_db()
        self._check_pba_refund(txn=txn, success_row=success_row)

        all_payment_rows_ids = []
        for receipt in txn.receipts.all():
            all_payment_rows_ids.extend(receipt.payment_rows.values_list('id', flat=True))

        self.assertEqual(
            set(txn.payment_rows.last().intents.last().payment_rows.values_list('id', flat=True)),
            set(all_payment_rows_ids),
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_transfer')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_pba_refund_with_fees(
        self,
        refund_possible_mock,
        create_transfer_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        self.stripe_plan.provision = 0.15
        self.stripe_plan.txn_fee = 21
        self.stripe_plan.refund_provision = 0.1
        self.stripe_plan.refund_txn_fee = 22
        self.stripe_plan.save()

        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        create_transfer_mock.side_effect = lambda **_: mock.MagicMock(id=self.generate_id())
        refund_possible_mock.return_value = (True, None)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )
        send_for_refund_row.refresh_from_db()
        self.assertDictEqual(
            success_row.payment_splits,
            {
                'amount': 8393,
                'txn_fee': 2100,
                'provision': 1852,
                'total_amount': 12345,
                'currency_code': 'USD',
            },
        )
        self.assertDictEqual(
            send_for_refund_row.payment_splits,
            {
                'currency_code': 'USD',
                'refund_txn_fee': 2200,
                'refund_provision': 1235,
                'total_fee_amount': 3435,
            },
        )
        # Refund
        self._handle_refund_notification(
            status=RefundStripeStatus.SUCCEEDED,
            external_id=self.random_id,
            intent_external_id=self.random_id,
        )

        txn.refresh_from_db()
        self._check_pba_refund_with_fees(txn=txn, success_row=success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    @patch('webapps.pos.provider.proxy.is_refund_possible')
    def test_pba_refund_fail(
        self,
        refund_possible_mock,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        refund_possible_mock.return_value = (True, None)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )
        send_for_refund_row.refresh_from_db()

        # Refund
        self._handle_refund_notification(
            status=RefundStripeStatus.FAILED,
            external_id=self.random_id,
            intent_external_id=self.random_id,
        )

        txn.refresh_from_db()
        self._check_pba_refund_fail(txn=txn, success_row=success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_transfer')
    def test_pba_chargeback(
        self,
        create_transfer_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_transfer_mock.side_effect = lambda **_: mock.MagicMock(id=self.generate_id())
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Chargeback
        self._handle_chargeback_notification()

        txn.refresh_from_db()
        self._check_pba_chargeback(txn=txn, success_row=success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_transfer')
    def test_pba_chargeback_with_fees(
        self,
        create_transfer_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        self.stripe_plan.provision = 0.15
        self.stripe_plan.txn_fee = 21
        self.stripe_plan.chargeback_provision = 0.1
        self.stripe_plan.chargeback_txn_fee = 22
        self.stripe_plan.save()

        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        create_transfer_mock.side_effect = lambda **_: mock.MagicMock(id=self.generate_id())

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Chargeback
        self._handle_chargeback_notification()
        self.assertDictEqual(
            success_row.payment_splits,
            {
                'amount': 8393,
                'txn_fee': 2100,
                'provision': 1852,
                'total_amount': 12345,
                'currency_code': 'USD',
            },
        )
        self.assertDictEqual(
            success_row.children.last().payment_splits,
            {
                'total_amount': 12345,
                'currency_code': 'USD',
                'total_fee_amount': 3435,
                'chargeback_txn_fee': 2200,
                'chargeback_provision': 1235,
            },
        )
        txn.refresh_from_db()
        self._check_pba_chargeback_with_fees(txn=txn, success_row=success_row)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_transfer')
    def test_pba_chargeback_reversed(
        self,
        create_transfer_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_transfer_mock.side_effect = lambda **_: mock.MagicMock(id=self.generate_id())
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Chargeback
        self._handle_chargeback_notification()

        txn.refresh_from_db()
        chargeback_row = txn.payment_rows[0]

        # Chargeback reverse
        self._handle_reversed_chargeback_notification()

        txn.refresh_from_db()
        self._check_pba_chargeback_reversed(
            txn=txn, success_row=success_row, chargeback_row=chargeback_row
        )

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    def test_pba_is_refund_possible(
        self,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        self._test_pba_is_refund_possible_success(
            confirm_payment_intent_mock,
            create_payment_intent_mock,
            create_refund_mock,
        )

    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 0, 100000, 0)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    def test_pba_is_refund_possible_with_checking_online_balance(
        self,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        self._test_pba_is_refund_possible_success(
            confirm_payment_intent_mock,
            create_payment_intent_mock,
            create_refund_mock,
            check_balance=True,
        )

    @override_feature_flag({StripeCheckRefundOnlineBalance.flag_name: True})
    @mock_stripe_balance_retrieve(StripeExternalAccountType.CARD, 0, 1, 0)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_refund')
    def test_pba_is_refund_possible_with_checking_online_balance_no_funds(
        self,
        create_refund_mock,
        _capture_payment_intent_mock,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )
        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        is_possible, error = is_refund_possible(
            payment_row=success_row,
            check_balance=True,
        )

        self.assertFalse(is_possible)
        self.assertEqual(
            error.code,
            RefundNonPossible.MISSING_FUNDS,
        )

        # Send for refund
        with self.assertRaises(AssertionError):
            _send_for_refund_row = success_row.send_for_refund(
                operator=self.user,
            )

        txn.refresh_from_db()

        is_possible, error = is_refund_possible(
            payment_row=success_row,
        )

        self.assertFalse(is_possible)
        self.assertEqual(error[0], RefundNonPossible.MISSING_FUNDS)

    def _test_pba_is_refund_possible_success(
        self,
        confirm_payment_intent_mock,
        create_payment_intent_mock,
        create_refund_mock,
        check_balance: bool = False,
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        create_refund_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)
        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)
        # Pay for transaction
        self.make_payment(
            transaction=txn,
            payment_method=self.payment_method,
            payment_row=txn.payment_rows[0],
        )
        self._success_payment_notifications(external_id=self.random_id)
        txn.refresh_from_db()
        success_row = txn.payment_rows[0]
        is_possible, error = is_refund_possible(
            payment_row=success_row,
            check_balance=check_balance,
        )
        self.assertTrue(is_possible)
        # Send for refund
        _send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )
        txn.refresh_from_db()
        is_possible, error = is_refund_possible(
            payment_row=success_row,
        )
        self.assertFalse(is_possible)
        self.assertEqual(error[0], RefundNonPossible.ROW_NOT_REFUNDABLE)
