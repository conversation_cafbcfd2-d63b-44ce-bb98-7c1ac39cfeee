import datetime
import decimal
from collections import OrderedDict

from dateutil.relativedelta import relativedelta
import pytest
from model_bakery import baker
from rest_framework.reverse import reverse

from drf_api.lib.base_drf_test_case import CustomerAPITestCase
from webapps.booking.models import (
    Appointment,
    Resource,
)
from webapps.booking.factory.subbookings import customer_subbookings_make
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import (
    bci_recipe,
    business_recipe,
    service_variant_recipe,
    service_recipe,
)
from webapps.business.enums import PriceType
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.user.baker_recipes import customer_user
from webapps.pos.models import (
    TippingAfterAppointmentAnswer,
)
from webapps.user.models import User


@pytest.mark.django_db
class TestTapToPayAppetiteExperimentSplash(CustomerAPITestCase):
    def setUp(self):
        self.user: User = customer_user.make(email='<EMAIL>')
        self.bci = bci_recipe.make(user=self.user)
        self.business = business_recipe.make()
        self.appointment = create_appointment(
            type=Appointment.TYPE.CUSTOMER,
            booked_for=self.bci,
            total_value=decimal.Decimal('100'),
        )
        self.url = reverse(
            'tipping_experiment_splash', kwargs={'appointment_id': self.appointment.id}
        )

        super().setUp()

    def test_get_ok(self):
        # Arrange
        staffer_1 = baker.make(
            Resource,
            type=Resource.STAFF,
            name='Staff X',
            position='Senior Barber',
        )

        staffer_2 = baker.make(
            Resource,
            type=Resource.STAFF,
            name='Staff X 2',
            position='Senior Barber 2',
        )

        service_variant = service_variant_recipe.make(
            service=service_recipe.make(business=self.business),
            duration=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=20.00,
        )
        service_variant.staffer_ids = staffer_1.id

        subbookings = customer_subbookings_make(
            [  # noqa
                {
                    "service_variant": service_variant,
                    "booked_from": datetime.datetime(2021, 8, 1, 12, tzinfo=datetime.UTC),
                },
                {
                    "service_variant": service_variant,
                    "booked_from": datetime.datetime(2021, 8, 1, 12, tzinfo=datetime.UTC),
                    "staffer": staffer_1,
                },
                {
                    "service_variant": service_variant,
                    "booked_from": datetime.datetime(2021, 8, 1, 12, tzinfo=datetime.UTC),
                    "staffer": staffer_2,
                },
            ],
            business=self.business,
        )
        bci = baker.make(
            BusinessCustomerInfo,
            user=self.user,
            business=self.business,
            client_type=BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_RECURRING,
        )
        appointment = create_appointment(
            subbookings,
            business=self.business,
            booked_for=bci,
            status=Appointment.STATUS.FINISHED,
            total_value=decimal.Decimal('100'),
        )

        # Act
        response = self.client.get(
            reverse(
                'tipping_experiment_splash',
                kwargs={'appointment_id': appointment.id},  # subbookings.appointment.id}
            )
        )
        expected_response = {
            'staffer': OrderedDict([('photo_url', ''), ('name', 'Staff X')]),
            'service_name': None,
            'tip_rate': [
                OrderedDict([('percent', '5'), ('value', '5.00')]),
                OrderedDict([('percent', '10'), ('value', '10.00')]),
                OrderedDict([('percent', '15'), ('value', '15.00')]),
                OrderedDict([('percent', '20'), ('value', '20.00')]),
            ],
            'appointment_value': '100.00',
        }
        # Assert
        assert response.data == expected_response

    def test_get_request_for_not_existing_appointment(self):
        # Act
        response = self.client.get(
            reverse('tipping_experiment_splash', kwargs={'appointment_id': self.appointment.id + 1})
        )

        # Assert
        assert response.status_code == 404

    def test_get_request_for_appointment_without_staffer(self):
        # Arrange
        service_variant = baker.prepare(
            'business.ServiceVariant', duration=relativedelta(minutes=30)
        )
        service_variant.staffer_ids = (1, 2)
        subbookings = customer_subbookings_make(
            [  # noqa
                {
                    "booked_from": datetime.datetime(2021, 8, 1, 12),
                    "service_variant": service_variant,
                    "staffer": None,
                }
            ],
            business=self.business,
        )
        self.appointment.subbookings = subbookings
        self.appointment.save()

        # Act
        response = self.client.get(self.url)

        # Assert
        assert response.status_code == 404


@pytest.mark.django_db
class TestTapToPayAppetiteAcceptView(CustomerAPITestCase):
    def setUp(self):
        self.url = reverse('tipping_experiment_decision')
        self.user: User = customer_user.make(email='<EMAIL>')
        super().setUp()

    def test_post_ok(self):
        # Act
        self.client.post(self.url, data={"answer": "NOT_NOW"})

        # Assert
        assert TippingAfterAppointmentAnswer.objects.filter(
            client=self.user, answer=TippingAfterAppointmentAnswer.Answer.NOT_NOW
        ).exists()

    def test_post_error_on_tipping_experiment_answer_already_exists(self):
        # Arrange
        TippingAfterAppointmentAnswer.objects.create(
            client=self.user, answer=TippingAfterAppointmentAnswer.Answer.ACCEPT
        )

        # Act
        response = self.client.post(self.url, data={"answer": "NOT_NOW"})

        # Assert
        assert response.status_code == 400
