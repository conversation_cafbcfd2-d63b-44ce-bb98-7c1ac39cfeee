import unittest

import pytest
from model_bakery import baker
from parameterized import parameterized

from lib.point_of_sale.enums import BasketPaymentAnalyticsTrigger
from lib.point_of_sale.enums import BasketPaymentSource
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import (
    PaymentRow,
    Receipt,
    Transaction,
)
from webapps.pos.services import TransactionService


@pytest.mark.django_db
class TestTransactionService(unittest.TestCase):

    @parameterized.expand(
        [
            (
                PaymentTypeEnum.PREPAYMENT,
                Transaction.TRANSACTION_TYPE__PAYMENT,
                BasketPaymentSource.PREPAYMENT,
            ),
            (
                PaymentTypeEnum.BOOKSY_PAY,
                Transaction.TRANSACTION_TYPE__PAYMENT,
                BasketPaymentSource.BOOKSY_PAY,
            ),
        ]
    )
    def test_get_basket_payment_source(
        self,
        payment_type: PaymentTypeEnum,
        transaction_type: str,
        expected_basket_payment_source: BasketPaymentSource,
    ):
        txn = baker.make(Transaction, transaction_type=transaction_type)
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()
        pr = baker.make(PaymentRow, receipt=receipt, payment_type__code=payment_type)

        self.assertEqual(
            TransactionService.get_basket_payment_source(txn, pr), expected_basket_payment_source
        )

    @parameterized.expand(
        [
            (
                PaymentTypeEnum.PREPAYMENT,
                BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            ),
            (
                PaymentTypeEnum.STRIPE_TERMINAL,
                BasketPaymentAnalyticsTrigger.BUSINESS__BOOKSY_CARD_READER_CFP,
            ),
            (
                PaymentTypeEnum.PAY_BY_APP,
                BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ),
            (
                PaymentTypeEnum.TAP_TO_PAY,
                BasketPaymentAnalyticsTrigger.BUSINESS__TAP_TO_PAY,
            ),
            (
                PaymentTypeEnum.BOOKSY_PAY,
                BasketPaymentAnalyticsTrigger.CUSTOMER__BOOKSY_PAY,
            ),
            (
                PaymentTypeEnum.CASH,
                BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ),
            (
                PaymentTypeEnum.BLIK,
                BasketPaymentAnalyticsTrigger.BUSINESS__BLIK,
            ),
        ]
    )
    def test_get_device_data_trigger(
        self, payment_type: PaymentTypeEnum, expected_trigger: BasketPaymentSource
    ):
        txn = baker.make(Transaction)
        receipt = baker.make(Receipt, transaction=txn)
        txn.latest_receipt = receipt
        txn.save()
        pr = baker.make(PaymentRow, receipt=receipt, payment_type__code=payment_type)

        self.assertEqual(TransactionService.get_device_data_trigger(pr), expected_trigger)
