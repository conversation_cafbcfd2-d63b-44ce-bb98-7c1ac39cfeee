import unittest
from decimal import Decimal
from pprint import pprint

import pytest
from django.test.utils import override_settings
from model_bakery import baker

from lib.tools import format_currency
from webapps.booking.models import (
    BookingSources,
)
from webapps.business.models import (
    Business,
    Resource,
)
from webapps.pos.enums import (
    CASH,
    PaymentTypeEnum,
    receipt_status,
    compatibilities,
)
from webapps.pos.models import (
    POS,
    PaymentType,
    Transaction,
    TaxRate,
    Tip,
)
from webapps.pos.serializers import TransactionSerializer
from webapps.pos.tests import TestCaseWithSetUp
from webapps.register.models import Register
from webapps.user.models import User


@pytest.mark.django_db
class TestTransactionSerializerLegacy(unittest.TestCase):
    def setUp(self):
        """Set up an business ready for appointments."""

        # @session stuff
        self.booking_source = baker.make(BookingSources)
        self.user = baker.make(User)
        self.access_level = Resource.STAFF_ACCESS_LEVEL_OWNER

        # business
        self.business = baker.make(
            Business,
            owner=self.user,
        )

        # pos
        self.pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
            registers_enabled=False,
        )

        # cash payment type must exist
        self.cash = baker.make(
            PaymentType,
            pos=self.pos,
            code=CASH,
        )
        baker.make(
            PaymentType,
            pos=self.pos,
            code=PaymentTypeEnum.SPLIT,
        )

        # setup tax rates etc. needed for transactions
        baker.make(TaxRate, pos=self.pos, default_for_service=True, rate=0)
        baker.make(Tip, pos=self.pos, rate=0, default=True)

    @override_settings(
        POS=True,
    )
    def test_transaction_simple(self):
        """Create transaction without any dry_run involved."""
        serializer = TransactionSerializer(
            data=self._get_data(dry_run=False),
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        serializer.is_valid()
        print(("ERRORS:", serializer.errors))
        assert serializer.is_valid()
        print("VALIDATED DATA:")
        pprint(dict(serializer.validated_data), indent=4)

        # save the serializer
        transaction = serializer.save()
        assert transaction.total == Decimal('123.45')
        assert transaction.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS

        # serialize transaction back to the user
        ret = serializer.data
        assert ret['total'] == format_currency(Decimal('123.45'))

    @override_settings(
        POS=True,
    )
    def test_transaction_with_dry_run(self):
        """Create transaction with dry_run."""
        # first use data with dry run
        serializer = TransactionSerializer(
            data=self._get_data(dry_run=True),
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        serializer.is_valid()
        print(("ERRORS:", serializer.errors))
        assert serializer.is_valid()
        print("VALIDATED DATA:")
        pprint(dict(serializer.validated_data), indent=4)

        # use data form dry run to make a save call
        data = serializer.data
        data['dry_run'] = False
        print("DATA AFTER DRY RUN:")
        pprint(dict(serializer.validated_data), indent=4)

        serializer = TransactionSerializer(
            data=self._get_data(dry_run=False),
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        serializer.is_valid()
        print(("ERRORS:", serializer.errors))
        assert serializer.is_valid()
        print("VALIDATED DATA:")
        pprint(dict(serializer.validated_data), indent=4)

        # save the serializer
        transaction = serializer.save()
        assert transaction.total == Decimal('123.45')
        assert transaction.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS

        # serialize transaction back to the user
        ret = serializer.data
        assert ret['total'] == format_currency(Decimal('123.45'))

    def _get_data(self, dry_run):
        return {
            'dry_run': dry_run,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'payment_type_code': CASH,
            'bookings': [
                {
                    'service_name': 'test service',
                    'item_price': 123.45,
                }
            ],
        }


@pytest.mark.django_db
class TestTransactionPaymentTypeSerializerLegacy(TestCaseWithSetUp):

    def _get_data(self, dry_run, code, customer_card_id=None):
        ret = {
            'dry_run': dry_run,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'payment_type_code': code,
            'bookings': [
                {
                    'service_name': 'test service',
                    'item_price': 123.45,
                }
            ],
        }

        if customer_card_id:
            ret['customer_card_id'] = customer_card_id

        return ret

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_without_payment_type(self):
        """Create transaction without any dry_run involved."""
        data = self._get_data(dry_run=False, code=PaymentTypeEnum.CASH)
        del data['payment_type_code']

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        serializer.is_valid()
        assert serializer.is_valid() is False
        assert len(serializer.errors['non_field_errors']) == 1
        assert serializer.errors['non_field_errors'][0].code == 'missing_payment_type'

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_cash(self):
        """Create transaction without any dry_run involved."""
        serializer = TransactionSerializer(
            data=self._get_data(dry_run=False, code=PaymentTypeEnum.CASH),
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        serializer.is_valid()
        print(("ERRORS:", serializer.errors))
        assert serializer.is_valid()
        print("VALIDATED DATA:")
        pprint(dict(serializer.validated_data), indent=4)

        # save the serializer
        transaction = serializer.save()
        assert transaction.total == Decimal('123.45')
        assert transaction.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS

        # serialize transaction back to the user
        ret = serializer.data
        assert ret['total'] == format_currency(Decimal('123.45'))

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_check(self):
        """Create transaction without any dry_run involved."""
        serializer = TransactionSerializer(
            data=self._get_data(dry_run=False, code=PaymentTypeEnum.CHECK),
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        serializer.is_valid()
        print(("ERRORS:", serializer.errors))
        assert serializer.is_valid()
        print("VALIDATED DATA:")
        pprint(dict(serializer.validated_data), indent=4)

        # save the serializer
        transaction = serializer.save()
        assert transaction.total == Decimal('123.45')
        assert transaction.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS

        # serialize transaction back to the user
        ret = serializer.data
        assert ret['total'] == format_currency(Decimal('123.45'))

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_pba_without_user(self):
        """Create transaction without any dry_run involved."""
        serializer = TransactionSerializer(
            data=self._get_data(dry_run=False, code=PaymentTypeEnum.PAY_BY_APP),
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        serializer.is_valid()
        print(("ERRORS:", serializer.errors))
        assert serializer.is_valid() is False
        assert len(serializer.errors['payment_type']) == 1
        assert serializer.errors['payment_type'][0].code == 'no_booksy_user'

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_pba(self):
        """Create transaction without any dry_run involved."""
        serializer = TransactionSerializer(
            data=self._get_data(
                dry_run=False,
                code=PaymentTypeEnum.PAY_BY_APP,
                customer_card_id=self.bci.id,
            ),
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        serializer.is_valid()
        print(("ERRORS:", serializer.errors))
        assert serializer.is_valid()

        # save the serializer
        transaction = serializer.save()

        assert transaction.total == Decimal('123.45')
        assert transaction.latest_receipt.total == Decimal('123.45')
        assert transaction.latest_receipt.remaining == Decimal('123.45')
        assert transaction.latest_receipt.status_code == receipt_status.CALL_FOR_PAYMENT

        # serialize transaction back to the user
        ret = serializer.data

        assert ret['total'] == format_currency(Decimal('0.00'))

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_pba_stardust(self):
        """Create transaction without any dry_run involved."""
        serializer = TransactionSerializer(
            data=self._get_data(
                dry_run=False,
                code=PaymentTypeEnum.PAY_BY_APP,
                customer_card_id=self.bci.id,
            ),
            context={
                'pos': self.pos,
                'operator': self.user,
                compatibilities.COMPATIBILITIES: {
                    compatibilities.STARDUST: True,
                },
            },
        )
        serializer.is_valid()
        print(("ERRORS:", serializer.errors))
        assert serializer.is_valid()

        # save the serializer
        transaction = serializer.save()

        assert transaction.total == Decimal('123.45')
        assert transaction.latest_receipt.total == Decimal('123.45')
        assert transaction.latest_receipt.remaining == Decimal('123.45')
        assert transaction.latest_receipt.status_code == receipt_status.CALL_FOR_PAYMENT

        # serialize transaction back to the user
        ret = serializer.data

        assert ret['total'] == format_currency(Decimal('123.45'))

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_square(self):
        """Create transaction without any dry_run involved."""
        serializer = TransactionSerializer(
            data=self._get_data(
                dry_run=False,
                code=PaymentTypeEnum.SQUARE,
                customer_card_id=self.bci.id,
            ),
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        serializer.is_valid()
        print(("ERRORS:", serializer.errors))
        assert serializer.is_valid()

        # save the serializer
        transaction = serializer.save()
        assert transaction.total == Decimal('123.45')
        assert transaction.latest_receipt.total == Decimal('123.45')
        assert transaction.latest_receipt.remaining == Decimal('123.45')
        assert transaction.latest_receipt.status_code == receipt_status.PENDING

        # serialize transaction back to the user
        ret = serializer.data
        assert ret['total'] == format_currency(Decimal('0.00'))

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_pba_free(self):

        not_free_list = [
            PaymentTypeEnum.PAY_BY_APP,
            PaymentTypeEnum.SQUARE,
        ]

        for i in not_free_list:
            data = self._get_data(
                dry_run=False,
                code=i,
                customer_card_id=self.bci.id,
            )

            data['bookings'][0]['item_price'] = 0

            serializer = TransactionSerializer(
                data=data,
                context={
                    'pos': self.pos,
                    'operator': self.user,
                },
            )
            assert serializer.is_valid() is False, serializer.errors
            assert len(serializer.errors['non_field_errors']) == 1
            assert serializer.errors['non_field_errors'][0].code == 'free_transaction'

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_edit(self):
        """
        To edit transaction - old transaction should be successfully paid.
        """

        code_list = [
            PaymentTypeEnum.PAY_BY_APP,
            PaymentTypeEnum.SQUARE,
        ]

        for i in code_list:
            serializer = TransactionSerializer(
                data=self._get_data(
                    dry_run=False,
                    code=i,
                    customer_card_id=self.bci.id,
                ),
                context={
                    'pos': self.pos,
                    'operator': self.user,
                    'compatibilities': {
                        'square': True,
                    },
                },
            )
            serializer.is_valid()
            assert serializer.is_valid(), serializer.errors

            # save the serializer
            transaction = serializer.save()
            assert transaction.total == Decimal('123.45')

            # serialize transaction back to the user
            ret = serializer.data
            assert ret['total'] == format_currency(Decimal('0.00'))

            txn = Transaction.objects.get(id=ret['id'])

            serializer = TransactionSerializer(
                data=self._get_data(dry_run=False, code=i, customer_card_id=self.bci.id),
                context={
                    'pos': self.pos,
                    'operator': self.user,
                    'old_txn': txn,
                    'edit': True,
                    'compatibilities': {
                        'square': True,
                    },
                },
            )

            assert serializer.is_valid() is False, serializer.errors
            assert len(serializer.errors['payment_type_code']) == 1
            assert serializer.errors['payment_type_code'][0].code == 'locked'

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_edit_with_stardust(self):
        """
        To edit transaction - old transaction should be successfully paid.
        """

        code_list = [
            PaymentTypeEnum.PAY_BY_APP,
            PaymentTypeEnum.SQUARE,
        ]

        for i in code_list:
            serializer = TransactionSerializer(
                data=self._get_data(
                    dry_run=False,
                    code=i,
                    customer_card_id=self.bci.id,
                ),
                context={
                    'pos': self.pos,
                    'operator': self.user,
                    compatibilities.COMPATIBILITIES: {
                        'square': True,
                        compatibilities.STARDUST: True,
                    },
                },
            )
            serializer.is_valid()
            assert serializer.is_valid(), serializer.errors

            # save the serializer
            transaction = serializer.save()
            assert transaction.total == Decimal('123.45')

            # serialize transaction back to the user
            ret = serializer.data
            assert ret['total'] == format_currency(Decimal('123.45'))

            txn = Transaction.objects.get(id=ret['id'])

            serializer = TransactionSerializer(
                data=self._get_data(dry_run=False, code=i, customer_card_id=self.bci.id),
                context={
                    'pos': self.pos,
                    'operator': self.user,
                    'old_txn': txn,
                    'edit': True,
                    'compatibilities': {
                        'square': True,
                    },
                },
            )

            assert serializer.is_valid() is False, serializer.errors
            assert len(serializer.errors['payment_type_code']) == 1
            assert serializer.errors['payment_type_code'][0].code == 'locked'

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_square_edit_paid(self):
        """
        Create transaction without any dry_run involved.

        Creating transaction with payment type form locking payment types list.
        This kind of transaction shouldn't be allowed to edit.
        """

        serializer = TransactionSerializer(
            data=self._get_data(
                dry_run=False,
                code=PaymentTypeEnum.SQUARE,
                customer_card_id=self.bci.id,
            ),
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': {
                    'square': True,
                },
            },
        )
        assert serializer.is_valid(), serializer.errors

        # save the serializer
        transaction = serializer.save()
        assert transaction.total == Decimal('123.45')
        assert transaction.latest_receipt.total == Decimal('123.45')
        assert transaction.latest_receipt.remaining == Decimal('123.45')
        assert transaction.latest_receipt.status_code == receipt_status.PENDING

        # serialize transaction back to the user
        ret = serializer.data
        assert ret['total'] == format_currency(Decimal('0.00'))

        txn = Transaction.objects.get(id=ret['id'])

        txn.latest_receipt.status_code = receipt_status.PAYMENT_SUCCESS
        txn.save()

        serializer = TransactionSerializer(
            data=self._get_data(
                dry_run=False, code=PaymentTypeEnum.SQUARE, customer_card_id=self.bci.id
            ),
            context={
                'pos': self.pos,
                'operator': self.user,
                'old_txn': txn,
                'edit': True,
                'compatibilities': {'square': True, 'split': True},
            },
        )

        assert serializer.is_valid() is False, serializer.errors
        assert len(serializer.errors['payment_type_code']) == 1
        assert serializer.errors['payment_type_code'][0].code == 'locked'

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_pba_edit_paid(self):
        """
        Create transaction without any dry_run involved.

        Creating transaction with payment type form locking payment types list.
        This kind of transaction shouldn't be allowed to edit.
        """

        serializer = TransactionSerializer(
            data=self._get_data(
                dry_run=False,
                code=PaymentTypeEnum.PAY_BY_APP,
                customer_card_id=self.bci.id,
            ),
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        assert serializer.is_valid(), serializer.errors

        # save the serializer
        transaction = serializer.save()

        assert transaction.total == Decimal('123.45')
        assert transaction.latest_receipt.total == Decimal('123.45')
        assert transaction.latest_receipt.remaining == Decimal('123.45')
        assert transaction.latest_receipt.status_code == receipt_status.CALL_FOR_PAYMENT

        # serialize transaction back to the user
        ret = serializer.data
        assert ret['total'] == format_currency(Decimal('0.00'))

        txn = Transaction.objects.get(id=ret['id'])

        txn.latest_receipt.status_code = receipt_status.PAYMENT_SUCCESS
        txn.save()

        serializer = TransactionSerializer(
            data=self._get_data(
                dry_run=False, code=PaymentTypeEnum.PAY_BY_APP, customer_card_id=self.bci.id
            ),
            context={
                'pos': self.pos,
                'operator': self.user,
                'old_txn': txn,
                'edit': True,
            },
        )

        assert serializer.is_valid() is False, serializer.errors
        assert len(serializer.errors['payment_type_code']) == 1
        assert serializer.errors['payment_type_code'][0].code == 'locked'

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_pba_edit_paid_stardust(self):
        """
        Create transaction without any dry_run involved.

        Creating transaction with payment type form locking payment types list.
        This kind of transaction shouldn't be allowed to edit.
        """

        serializer = TransactionSerializer(
            data=self._get_data(
                dry_run=False,
                code=PaymentTypeEnum.PAY_BY_APP,
                customer_card_id=self.bci.id,
            ),
            context={
                'pos': self.pos,
                'operator': self.user,
                compatibilities.COMPATIBILITIES: {
                    compatibilities.STARDUST: True,
                },
            },
        )
        assert serializer.is_valid(), serializer.errors

        # save the serializer
        transaction = serializer.save()

        assert transaction.total == Decimal('123.45')
        assert transaction.latest_receipt.total == Decimal('123.45')
        assert transaction.latest_receipt.remaining == Decimal('123.45')
        assert transaction.latest_receipt.status_code == receipt_status.CALL_FOR_PAYMENT

        # serialize transaction back to the user
        ret = serializer.data
        assert ret['total'] == format_currency(Decimal('123.45'))

        txn = Transaction.objects.get(id=ret['id'])

        txn.latest_receipt.status_code = receipt_status.PAYMENT_SUCCESS
        txn.save()

        serializer = TransactionSerializer(
            data=self._get_data(
                dry_run=False, code=PaymentTypeEnum.PAY_BY_APP, customer_card_id=self.bci.id
            ),
            context={
                'pos': self.pos,
                'operator': self.user,
                'old_txn': txn,
                'edit': True,
            },
        )

        assert serializer.is_valid() is False, serializer.errors
        assert len(serializer.errors['payment_type_code']) == 1
        assert serializer.errors['payment_type_code'][0].code == 'locked'

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_split_wrong_sum(self):
        """Create transaction without any dry_run involved."""

        data = self._get_data(dry_run=False, code=PaymentTypeEnum.SPLIT)

        data['payment_rows'] = [
            {"amount": 50.0, "payment_type_code": "cash", "label": "Cash", "amount_text": "$50.00"},
            {"amount": 3.0, "payment_type_code": "check", "label": "Check", "amount_text": "$3.00"},
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        assert serializer.is_valid() is False, serializer.errors
        assert len(serializer.errors['non_field_errors']) == 1
        assert serializer.errors['non_field_errors'][0].code == 'wrong_sum_payment_rows'

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_split_nested_split(self):
        """Create transaction without any dry_run involved."""

        data = self._get_data(dry_run=False, code=PaymentTypeEnum.SPLIT)

        data['payment_rows'] = [
            {"amount": 50.0, "payment_type_code": "cash", "label": "Cash", "amount_text": "$50.00"},
            {
                "amount": 73.45,
                "payment_type_code": "split",
                "label": "Split",
                "amount_text": "$73.45",
            },
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        assert serializer.is_valid() is False, serializer.errors
        assert len(serializer.errors['non_field_errors']) == 1
        assert serializer.errors['non_field_errors'][0].code == 'nested_split'

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_split_doubled_method(self):
        """Create transaction without any dry_run involved."""

        data = self._get_data(dry_run=False, code=PaymentTypeEnum.SPLIT)

        data['payment_rows'] = [
            {
                "amount": 50.0,
                "payment_type_code": "square",
                "label": "Square",
                "amount_text": "$50.00",
            },
            {
                "amount": 73.45,
                "payment_type_code": "square",
                "label": "Square",
                "amount_text": "73.45",
            },
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        assert serializer.is_valid() is False, serializer.errors
        assert len(serializer.errors['non_field_errors']) == 1
        assert serializer.errors['non_field_errors'][0].code == 'doubled_method'

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_split_doubled_method_but_1_locked(self):
        """Create transaction without any dry_run involved."""

        data = self._get_data(dry_run=False, code=PaymentTypeEnum.SPLIT)

        data['payment_rows'] = [
            {
                "amount": 50.0,
                "payment_type_code": "square",
                "label": "Square",
                "amount_text": "$50.00",
            },
            {
                "amount": 73.45,
                "payment_type_code": "square",
                "label": "Square",
                "amount_text": "73.45",
                "locked": True,
            },
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        assert serializer.is_valid(), serializer.errors

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_with_nested_split(self):
        """Create transaction without any dry_run involved."""

        data = self._get_data(dry_run=False, code=PaymentTypeEnum.SPLIT)

        data['payment_rows'] = [
            {
                "amount": 50.0,
                "payment_type_code": "split",
                "label": "Split",
                "amount_text": "$50.00",
            },
            {
                "amount": 73.45,
                "payment_type_code": "check",
                "label": "Check",
                "amount_text": "$73.45",
            },
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        assert serializer.is_valid() is False, serializer.errors
        assert serializer.errors['non_field_errors'][0].code == 'nested_split'

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_without_split_with_dry(self):
        """
        Check if split_payment_choices are not attached any other paymentType
        """
        data = self._get_data(dry_run=False, code=PaymentTypeEnum.CASH)

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        assert serializer.is_valid(), serializer.errors

        ret = serializer.data
        assert ret.get('split_payment_choices', None) is None

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_with_split_with_dry(self):
        """
        Check if split_payment_choices are attached to split payment_method
        """

        data = self._get_data(dry_run=True, code=PaymentTypeEnum.SPLIT)

        print(self.pba.__dict__)

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': {'square': True, 'split': True},
            },
        )

        assert serializer.is_valid(), serializer.errors
        ret = serializer.data

        for item in ret.get('payment_type_choices'):
            print(item)
        for item in ret.get('split_payment_choices'):
            print(item)
        assert len(ret.get('payment_type_choices')) == 6
        assert len(ret.get('split_payment_choices')) == 4  # Without split

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_with_split_with_dry_with_payment_row(self):
        """
        Check if selected items in payment_rows are
        removed for split_payment_chocies.
        """

        data = self._get_data(dry_run=True, code=PaymentTypeEnum.SPLIT)

        data['payment_rows'] = [
            {"amount": 50.0, "payment_type_code": "cash", "label": "Cash", "amount_text": "$50.00"},
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': {'square': True, 'split': True},
            },
        )

        assert serializer.is_valid(), serializer.errors
        ret = serializer.data

        assert len(ret.get('payment_type_choices')) == 6
        # Without split and cash
        assert len(ret.get('split_payment_choices')) == 3

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_with_split_with_dry_with_locked_pr(self):
        """
        Check if selected items in payment_rows are
        removed for split_payment_choices.
        Locked payment rows should be excluded from this filter.
        """

        data = self._get_data(dry_run=True, code=PaymentTypeEnum.SPLIT)

        data['payment_rows'] = [
            {
                "amount": 50.0,
                "payment_type_code": "pay_by_app",
                "label": "Cash",
                "amount_text": "$50.00",
                "locked": True,
            },
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': {'square': True, 'split': True},
            },
        )

        assert serializer.is_valid(), serializer.errors
        ret = serializer.data

        assert len(ret.get('payment_type_choices')) == 6
        assert len(ret.get('split_payment_choices')) == 4  # Only without split

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_split_with_payment_rows(self):
        """
        Create successful transaction. Registers turned off.
        """

        data = self._get_data(dry_run=False, code=PaymentTypeEnum.SPLIT)

        data['payment_rows'] = [
            {"amount": 50.0, "payment_type_code": "cash", "label": "Cash", "amount_text": "$50.00"},
            {
                "amount": 73.45,
                "payment_type_code": "check",
                "label": "Check",
                "amount_text": "$73.45",
            },
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': {
                    'square': True,
                },
            },
        )
        assert serializer.is_valid(), serializer.errors

        serializer.save()
        ret = serializer.data
        txn = Transaction.objects.get(id=ret['id'])
        rows = txn.payment_rows.all().order_by('created')
        operations = txn.latest_receipt.register_operations.all()

        # Check if split_payment_choices are attached only for dry_run
        assert ret.get('split_payment_choices', None) is None

        # Check rows
        assert len(rows) == 2
        assert rows[0].payment_type.code == 'cash'
        assert rows[0].amount == Decimal('50')
        assert rows[1].payment_type.code == 'check'
        assert rows[1].amount == Decimal('73.45')

        # Check operations
        assert len(operations) == 0

        # Check receipt
        assert txn.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        assert txn.latest_receipt.payment_type.code == PaymentTypeEnum.SPLIT

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_split_with_payment_rows_registers(self):
        """
        Create successful transaction. Registers turned on.
        """

        self.pos.registers_enabled = True
        self.pos.save()

        self.register = baker.make(
            Register,
            pos=self.pos,
            is_open=True,
            opened_by=self.user,
        )

        data = self._get_data(dry_run=False, code=PaymentTypeEnum.SPLIT)

        data['payment_rows'] = [
            {"amount": 50.0, "payment_type_code": "cash", "label": "Cash", "amount_text": "$50.00"},
            {
                "amount": 73.45,
                "payment_type_code": "check",
                "label": "Check",
                "amount_text": "$73.45",
            },
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': {
                    'square': True,
                },
            },
        )
        assert serializer.is_valid(), serializer.errors

        serializer.save()
        ret = serializer.data
        txn = Transaction.objects.get(id=ret['id'])
        rows = txn.latest_receipt.payment_rows.all().order_by('payment_type__code')
        operations = txn.latest_receipt.register_operations.all()

        # Check if split_payment_choices are attached only for dry_run
        assert ret.get('split_payment_choices', None) is None

        # Check rows
        assert len(rows) == 2
        assert rows[0].payment_type.code == 'cash'
        assert rows[0].amount == Decimal('50')
        assert rows[1].payment_type.code == 'check'
        assert rows[1].amount == Decimal('73.45')

        # Check operations
        assert len(operations) == 2
        assert operations[0].amount == Decimal('50')
        assert operations[0].operator == self.user
        assert operations[0].payment_type.code == 'cash'

        assert operations[1].amount == Decimal('73.45')
        assert operations[1].operator == self.user
        assert operations[1].payment_type.code == 'check'
        # Check receipt
        assert txn.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        assert txn.latest_receipt.payment_type.code == PaymentTypeEnum.SPLIT

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_split_with_payment_rows_pba(self):
        """
        Create successful transaction including PBA.
        """

        data = self._get_data(
            dry_run=False, code=PaymentTypeEnum.SPLIT, customer_card_id=self.bci.id
        )

        data['payment_rows'] = [
            {"amount": 50.0, "payment_type_code": "cash", "label": "Cash", "amount_text": "$50.00"},
            {
                "amount": 73.45,
                "payment_type_code": "pay_by_app",
                "label": "Pay By App",
                "amount_text": "$73.45",
            },
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        assert serializer.is_valid(), serializer.errors

        serializer.save()
        ret = serializer.data
        txn = Transaction.objects.get(id=ret['id'])
        rows = txn.payment_rows.all().order_by('payment_type__code')
        operations = txn.latest_receipt.register_operations.all()

        # Check if split_payment_choices are attached only for dry_run
        assert ret.get('split_payment_choices', None) is None

        # Check rows
        assert len(rows) == 2
        assert rows[0].payment_type.code == 'cash'
        assert rows[0].amount == Decimal('50')
        assert rows[1].payment_type.code == 'pay_by_app'
        assert rows[1].amount == Decimal('73.45')

        # Check operations
        assert len(operations) == 0

        # Check receipt
        assert txn.latest_receipt.status_code == receipt_status.CALL_FOR_PAYMENT
        assert txn.latest_receipt.payment_type.code == PaymentTypeEnum.SPLIT

    @override_settings(
        POS=True,
    )
    def test_transaction_payment_split_with_payment_rows_pba_registers(self):
        """
        Create successful transaction including PBA. Register turned on.
        """
        self.pos.registers_enabled = True
        self.pos.save()

        temp_register = Register(
            pos=self.pos,
            is_open=True,
            opened_by=self.pos.business.owner,
            opening_cash=0,
        )
        temp_register.save()

        data = self._get_data(
            dry_run=False, code=PaymentTypeEnum.SPLIT, customer_card_id=self.bci.id
        )

        data['payment_rows'] = [
            {"amount": 50.0, "payment_type_code": "cash", "label": "Cash", "amount_text": "$50.00"},
            {
                "amount": 73.45,
                "payment_type_code": "pay_by_app",
                "label": "Pay By App",
                "amount_text": "$73.45",
            },
        ]

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': {
                    'square': True,
                },
            },
        )
        assert serializer.is_valid(), serializer.errors

        serializer.save()
        ret = serializer.data
        txn = Transaction.objects.get(id=ret['id'])
        rows = txn.payment_rows.all().order_by('payment_type__code')
        operations = txn.latest_receipt.register_operations.all()

        # Check if split_payment_choices are attached only for dry_run
        assert ret.get('split_payment_choices', None) is None

        # Check rows
        assert len(rows) == 2
        assert rows[0].payment_type.code == 'cash'
        assert rows[0].amount == Decimal('50')
        assert rows[1].payment_type.code == 'pay_by_app'
        assert rows[1].amount == Decimal('73.45')

        # Check operations
        # Still 0, coz call for payment doesn't add operations
        assert len(operations) == 0

        # Check receipt
        assert txn.latest_receipt.status_code == receipt_status.CALL_FOR_PAYMENT
        assert txn.latest_receipt.payment_type.code == PaymentTypeEnum.SPLIT


@pytest.mark.django_db
class TestCompatibilityTestCaseLegacy(TestCaseWithSetUp):

    @override_settings(
        POS=True,
    )
    def test_without_compatibilities(self):
        """
        Create transaction with dry_run.

        Square paymentType should not be visible.
        """

        data = {
            'dry_run': True,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'payment_type_code': self.cash.code,
            'bookings': [
                {
                    'service_name': 'test service',
                    'item_price': 123.45,
                }
            ],
        }

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        assert serializer.is_valid(), serializer.errors

        ret = serializer.data

        assert not any(
            item['code'] == 'square' or item['code'] == 'split'
            for item in ret['payment_type_choices']
        )

    @override_settings(
        POS=True,
    )
    def test_square_off(self):
        """
        Create transaction with dry_run.

        Square paymentType should not be visible.
        """

        data = {
            'dry_run': True,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'payment_type_code': self.cash.code,
            'bookings': [
                {
                    'service_name': 'test service',
                    'item_price': 123.45,
                }
            ],
            'compatibilities': {
                'square': False,
            },
        }

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        assert serializer.is_valid(), serializer.errors

        ret = serializer.data

        assert not any(item['code'] == 'square' for item in ret['payment_type_choices'])

    @override_settings(
        POS=True,
    )
    def test_square_on(self):
        """
        Create transaction with dry_run.

        Square paymentType should be visible.
        """

        data = {
            'dry_run': True,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'payment_type_code': self.cash.code,
            'bookings': [
                {
                    'service_name': 'test service',
                    'item_price': 123.45,
                }
            ],
        }

        serializer = TransactionSerializer(
            data=data,
            context={'pos': self.pos, 'operator': self.user, 'compatibilities': {'square': True}},
        )
        assert serializer.is_valid(), serializer.errors

        ret = serializer.data

        assert any(item['code'] == 'square' for item in ret['payment_type_choices'])

    @override_settings(
        POS=True,
    )
    def test_split_off(self):
        """
        Create transaction with dry_run.

        Square paymentType should not be visible.
        """

        data = {
            'dry_run': True,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'payment_type_code': self.cash.code,
            'bookings': [
                {
                    'service_name': 'test service',
                    'item_price': 123.45,
                }
            ],
            'compatibilities': {
                'split': False,
            },
        }

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
            },
        )
        assert serializer.is_valid(), serializer.errors

        ret = serializer.data

        assert not any(item['code'] == 'square' for item in ret['payment_type_choices'])

    @override_settings(
        POS=True,
    )
    def test_split_on(self):
        """
        Create transaction with dry_run.

        Split paymentType should be visible.
        """

        data = {
            'dry_run': True,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'payment_type_code': self.cash.code,
            'bookings': [
                {
                    'service_name': 'test service',
                    'item_price': 123.45,
                }
            ],
        }

        serializer = TransactionSerializer(
            data=data,
            context={'pos': self.pos, 'operator': self.user, 'compatibilities': {'split': True}},
        )
        assert serializer.is_valid(), serializer.errors

        ret = serializer.data

        for item in ret.get('payment_type_choices'):
            print(item)

        assert any(item['code'] == 'split' for item in ret['payment_type_choices'])

    @override_settings(
        POS=True,
    )
    def test_split_on_but_off(self):
        """
        Create transaction with dry_run.

        Split paymentType should be visible.
        """

        data = {
            'dry_run': True,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'payment_type_code': self.cash.code,
            'bookings': [
                {
                    'service_name': 'test service',
                    'item_price': 123.45,
                }
            ],
        }

        serializer = TransactionSerializer(
            data=data,
            context={'pos': self.pos, 'operator': self.user, 'compatibilities': {'split': False}},
        )
        assert serializer.is_valid(), serializer.errors

        ret = serializer.data

        for item in ret.get('payment_type_choices'):
            print(item)

        assert any(item['code'] != 'split' for item in ret['payment_type_choices'])
