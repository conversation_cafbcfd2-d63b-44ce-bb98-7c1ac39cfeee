import unittest
from unittest.mock import patch

import mock
import pytest
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC
from segment.analytics import Client

from lib.timezone_hours import get_timezones_with_hour
from lib.test_utils import create_subbooking
from lib.tools import tznow
from webapps.booking.models import Appointment
from webapps.booking.models import BookingSources
from webapps.booking.tests.utils import create_appointment
from webapps.business.models import Business
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import Transaction, PaymentType, Receipt, PaymentRow, POS
from webapps.pos.tools import unfinished_booksy_pay_query
from webapps.segment.utils import assert_events_triggered
from webapps.structure.models import Region
from webapps.user.models import User

test_date_time = tznow(tz=UTC)

setup_time = test_date_time.replace(year=2018, month=3, day=1, hour=10, minute=0)
test_date_time = test_date_time.replace(year=2018, month=4, day=1, hour=10, minute=0)


@pytest.mark.django_db
class TestAutoClosingBooksyPayTransactions(unittest.TestCase):

    def setUp(self):
        super().setUp()
        self.test_zone = 'Europe/Warsaw'

        list_regions = [
            'Europe/Warsaw',
            'Europe/Helsinki',
            'America/New_York',
            'Indian/Comoro',
            'America/Sao_Paulo',
        ]

        self.source = baker.make(BookingSources)
        for key, region_name in enumerate(list_regions):
            # business
            business = baker.make(
                Business,
                time_zone_name=region_name,
            )
            baker.make(
                Region,
                time_zone_name=region_name,
            )

            tz = business.get_timezone()
            assert tz._long_name == region_name  # pylint: disable=protected-access

            booking, *_ = create_subbooking(
                business=business,
                booking_kws=dict(
                    updated_by=baker.make(User),
                    source=self.source,
                    type=Appointment.TYPE.CUSTOMER,
                    status=Appointment.STATUS.FINISHED,
                ),
            )

            pos = baker.make(POS, business=business)
            txn = baker.make(
                Transaction,
                pos=pos,
                total=100,
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
                appointment_id=booking.appointment_id,
            )
            booksy_pay = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.BOOKSY_PAY)
            receipt = baker.make(
                Receipt,
                payment_type=booksy_pay,
                status_code=receipt_status.BOOKSY_PAY_SUCCESS,
                transaction=txn,
            )
            txn.latest_receipt = receipt
            txn.save()

            baker.make(
                PaymentRow,
                amount=100 if key != 4 else 50,
                payment_type=booksy_pay,
                status=receipt_status.BOOKSY_PAY_SUCCESS,
                receipt=receipt,
            )

        self.business = baker.make(
            Business,
            time_zone_name=self.test_zone,
        )

        tz = self.business.get_timezone()
        assert tz._long_name == self.test_zone  # pylint: disable=protected-access

    def _create_appointment_helper(
        self,
        appointment_status: Appointment.STATUS,
        is_fully_paid: bool = True,
        refund: bool = False,
        is_multibooking: bool = False,
        has_parent_txn: bool = False,
    ):
        appointment = create_appointment(
            [{}, {}] if is_multibooking else [{}],
            business=self.business,
            source=self.source,
            type=Appointment.TYPE.CUSTOMER,
            status=appointment_status,
        )
        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=appointment.id,
        )
        booksy_pay = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.BOOKSY_PAY)
        receipt = baker.make(
            Receipt,
            payment_type=booksy_pay,
            status_code=receipt_status.BOOKSY_PAY_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=100 if is_fully_paid else 70,
            payment_type=booksy_pay,
            status=receipt_status.BOOKSY_PAY_SUCCESS,
            receipt=receipt,
        )

        if refund is True:
            baker.make(
                PaymentRow,
                amount=100,
                payment_type=booksy_pay,
                status=receipt_status.BOOKSY_PAY_SUCCESS,
                receipt=baker.make(
                    Receipt,
                    payment_type=booksy_pay,
                    status_code=receipt_status.SENT_FOR_REFUND,
                    transaction=txn,
                ),
            )

            receipt = baker.make(
                Receipt,
                payment_type=booksy_pay,
                status_code=receipt_status.BOOKSY_PAY_SUCCESS,
                transaction=txn,
            )
            baker.make(
                PaymentRow,
                amount=100,
                payment_type=booksy_pay,
                status=receipt_status.REFUNDED,
                receipt=receipt,
            )
            txn.latest_receipt = receipt
            txn.save()

        if has_parent_txn is True:
            txn2 = baker.make(
                Transaction,
                pos=pos,
                total=100,
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
                parent_txn=txn,
                appointment_id=appointment.id,
            )
            receipt2 = baker.make(
                Receipt,
                payment_type=booksy_pay,
                status_code=receipt_status.BOOKSY_PAY_SUCCESS,
                transaction=txn2,
            )
            txn2.latest_receipt = receipt2
            txn2.save()

            baker.make(
                PaymentRow,
                amount=100,
                payment_type=booksy_pay,
                status=receipt_status.BOOKSY_PAY_SUCCESS,
                receipt=receipt2,
            )

    @parameterized.expand(
        [
            (
                'Close existing transactions',
                None,
                {
                    6: 1,
                    12: 1,
                    13: 2,
                    7: 0,  # Transaction is not fully paid
                },
                4,
            ),
            (
                'Finished booking',
                {'appointment_status': Appointment.STATUS.FINISHED},
                {
                    6: 1,
                    12: 2,
                    13: 2,
                    7: 0,  # Transaction is not fully paid
                },
                5,
            ),
            (
                'No-show booking',
                {'appointment_status': Appointment.STATUS.NOSHOW},
                {
                    6: 1,
                    12: 2,
                    13: 2,
                    7: 0,  # Transaction is not fully paid
                },
                5,
            ),
            (
                'No-show not fully paid booking',
                {
                    'appointment_status': Appointment.STATUS.NOSHOW,
                    'is_fully_paid': False,
                },
                {
                    6: 1,
                    12: 2,
                    13: 2,
                    7: 0,  # Transaction is not fully paid
                },
                5,
            ),
            (
                'Canceled booking',
                {
                    'appointment_status': Appointment.STATUS.CANCELED,
                    'is_fully_paid': False,
                    'refund': True,
                },
                {
                    6: 1,
                    12: 1,  # 1 as the latest receipt's transaction is in the refunded state
                    13: 2,
                    7: 0,  # Transaction is not fully paid
                },
                4,
            ),
            (
                'Future booking',
                {'appointment_status': Appointment.STATUS.ACCEPTED},
                {
                    6: 1,
                    12: 1,  # 1 instead of 2 as the appointment is not finished/noshow/canceled
                    13: 2,
                    7: 0,  # Transaction is not fully paid
                },
                4,
            ),
            (
                'Finished multi-booking',
                {
                    'appointment_status': Appointment.STATUS.FINISHED,
                    'is_multibooking': True,
                },
                {
                    6: 1,
                    12: 2,  # finished here
                    13: 2,
                    7: 0,  # Transaction is not fully paid
                },
                5,
            ),
            (
                'No-show multi-booking',
                {
                    'appointment_status': Appointment.STATUS.NOSHOW,
                    'is_multibooking': True,
                },
                {
                    6: 1,
                    12: 2,  # No show here
                    13: 2,
                    7: 0,  # Transaction is not fully paid
                },
                5,
            ),
            (
                'Canceled multi-booking',
                {
                    'appointment_status': Appointment.STATUS.CANCELED,
                    'is_multibooking': True,
                },
                {
                    6: 1,
                    12: 2,  # Canceled here
                    13: 2,
                    7: 0,  # Transaction is not fully paid
                },
                5,
            ),
            (
                'Future multi-booking',
                {
                    'appointment_status': Appointment.STATUS.ACCEPTED,
                    'is_multibooking': True,
                },
                {
                    6: 1,
                    12: 1,  # 1 instead of 2 as it's a future appointment
                    13: 2,
                    7: 0,  # Transaction is not fully paid
                },
                4,
            ),
            (
                'Parent transaction',
                {
                    'appointment_status': Appointment.STATUS.FINISHED,
                    'is_multibooking': True,
                    'has_parent_txn': True,
                },
                {
                    6: 1,
                    12: 2,
                    13: 2,
                    7: 0,  # Transaction is not fully paid
                },
                5,
            ),
        ]
    )
    @pytest.mark.freeze_time(test_date_time)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_auto_closing_booking(
        self,
        __: str,
        create_appointment_kwargs: list[dict] | None,
        expected_result: dict,
        expected_tracks_checkout_transaction_completed: int,
        analytics_track_mock: mock.MagicMock,
        analytics_identify_mock: mock.MagicMock,
    ) -> None:
        if create_appointment_kwargs:
            # if a status provided, create a new appointment
            self._create_appointment_helper(**create_appointment_kwargs)

        #  MAKE TEST
        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_booksy_pay_query(closing_timezones)
            assert len(txs) == expected_result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_booksy_pay_query(closing_timezones)

            assert len(txs) == 0

        assert analytics_track_mock.call_count == expected_tracks_checkout_transaction_completed
        assert analytics_identify_mock.call_count == 0
        self.assertEqual(
            {call[1]['event'] for call in analytics_track_mock.call_args_list},
            {'Checkout_Transaction_Completed'},
        )
        assert_events_triggered(
            {
                'Checkout_Transaction_Completed': {
                    'expected_tracks': expected_tracks_checkout_transaction_completed,
                }
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )
