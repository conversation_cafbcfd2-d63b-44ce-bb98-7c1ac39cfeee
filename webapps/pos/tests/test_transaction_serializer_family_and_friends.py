import pytest

from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import business_recipe
from webapps.family_and_friends.tests.utils import TestFamilyAndFriendsMixin
from webapps.pos.baker_recipes import (
    payment_type_recipe,
    pos_recipe,
    receipt_recipe,
    tax_rate_recipe,
    transaction_recipe,
)
from webapps.pos.models import PaymentMethod, Transaction
from webapps.pos.enums import receipt_status
from webapps.pos.enums.compatibilities import NEW_CHECKOUT
from webapps.pos.enums.payment_types import PaymentTypeEnum
from webapps.pos.serializers import TransactionSerializer
from webapps.user.baker_recipes import user_recipe


@pytest.mark.django_db
class TestTransactionSerializerForFamilyAndFriends(TestFamilyAndFriendsMixin):
    def setUp(self):
        super().setUp()
        self.user = user_recipe.make()
        self.business = business_recipe.make(owner=self.user)
        self.pos = pos_recipe.make(business=self.business, active=True)
        payment_type_recipe.make(pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        payment_type_recipe.make(pos=self.pos, code=PaymentTypeEnum.CASH)
        payment_type_recipe.make(pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        tax_rate_recipe.make(pos=self.pos)
        compatibilities = {NEW_CHECKOUT: True}
        self.context = {
            'pos': self.pos,
            'business': self.business,
            'operator': self.user,
            'compatibilities': compatibilities,
            'user': self.user,
        }
        self.data = {
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'dry_run': True,
            'bookings': [
                {
                    'service_name': 'test service',
                    'item_price': 123.45,
                }
            ],
            'payment_rows': [
                {
                    'amount': 123.45,
                    'payment_type_code': PaymentTypeEnum.CASH,
                }
            ],
        }
        parent_bci, member_bci = self.create_inactive_member_bcis(business=self.business)
        self.family_and_friends_appointment = self.create_appointment(parent_bci, member_bci)
        self.appointment = create_appointment()

    def test_is_family_and_friends_for_not_family_and_friends(self):
        self.data['booking'] = self.appointment.subbookings[0].id
        serializer = TransactionSerializer(data=self.data, context=self.context)
        assert serializer.is_valid()
        assert not serializer.data['is_family_and_friends']

    def test_is_family_and_friends(self):
        self.data['booking'] = self.family_and_friends_appointment.subbookings[0].id
        serializer = TransactionSerializer(data=self.data, context=self.context)
        assert serializer.is_valid()
        assert serializer.data['is_family_and_friends']

    def test_is_family_and_friends_customer_data(self):
        self.data['booking'] = self.family_and_friends_appointment.subbookings[0].id
        self.data['customer_card_id'] = self.family_and_friends_appointment.booked_for.id
        serializer = TransactionSerializer(data=self.data, context=self.context)
        assert serializer.is_valid()
        name = self.family_and_friends_appointment.booked_for.full_name
        phone = self.family_and_friends_appointment.booked_by.user.cell_phone
        email = self.family_and_friends_appointment.booked_by.user.email
        assert serializer.data['customer_data'] == f'{name}, {phone}, {email}'
        assert serializer.data['customer_info']['phone'] == phone
        assert serializer.data['customer_info']['email'] == email
        assert serializer.data['customer_info']['full_name'] == name

    def test_pay_by_app_for_normal_appointment(self):
        self.data['booking'] = self.appointment.subbookings[0].id
        self.data['payment_rows'][0]['payment_type_code'] = PaymentTypeEnum.PAY_BY_APP
        serializer = TransactionSerializer(data=self.data, context=self.context)
        assert serializer.is_valid()

    def test_pay_by_app_not_supported_in_payment_rows(self):
        self.data['booking'] = self.family_and_friends_appointment.subbookings[0].id
        self.data['payment_rows'][0]['payment_type_code'] = PaymentTypeEnum.PAY_BY_APP
        serializer = TransactionSerializer(data=self.data, context=self.context)
        assert not serializer.is_valid()
        assert serializer.errors.get('payment_type')

    def test_pay_by_app_not_supported(self):
        self.data['booking'] = self.family_and_friends_appointment.subbookings[0].id
        self.data['payment_type_code'] = PaymentTypeEnum.PAY_BY_APP
        serializer = TransactionSerializer(data=self.data, context=self.context)
        assert not serializer.is_valid()
        assert serializer.errors.get('payment_type')

    def test_get_payment_types_for_transaction_force_pay_by_app_not_family_and_friends(self):
        self.data['appointment'] = self.appointment
        self.data['deposit'] = transaction_recipe.make(
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
        )
        self.data['customer_id'] = self.user.id
        PaymentMethod.objects.create(user_id=self.user.id, active=True, default=True)
        self.pos.force_pba_for_cf = True
        self.pos.save()
        serializer = TransactionSerializer(data=self.data, context=self.context)
        payment_types = serializer.get_payment_types_for_transaction(
            pos=self.pos,
            compatibilities=self.context['compatibilities'],
            is_edit=False,
            data=self.data,
        )
        assert len(payment_types) == 1
        assert payment_types[0].code == PaymentTypeEnum.PAY_BY_APP

    def test_get_payment_types_for_transaction_force_pay_by_app_family_and_friends(self):
        self.data['appointment'] = self.family_and_friends_appointment
        self.data['deposit'] = transaction_recipe.make(
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
        )
        self.data['customer_id'] = self.user.id
        PaymentMethod.objects.create(user_id=self.user.id, active=True, default=True)
        self.pos.force_pba_for_cf = True
        self.pos.save()
        serializer = TransactionSerializer(data=self.data, context=self.context)
        payment_types = serializer.get_payment_types_for_transaction(
            pos=self.pos,
            compatibilities=self.context['compatibilities'],
            is_edit=False,
            data=self.data,
        )
        assert len(payment_types) == 2
        payment_type_codes = {payment_type.code for payment_type in payment_types}
        assert {PaymentTypeEnum.CASH, PaymentTypeEnum.PAY_BY_APP} == payment_type_codes

    def test_pay_by_app_family_and_friends_deposit(self):
        self.data['booking'] = self.family_and_friends_appointment.subbookings[0].id
        self.data['transaction_type'] = Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
        self.data['payment_rows'][0]['payment_type_code'] = PaymentTypeEnum.PAY_BY_APP
        self.data['dry_run'] = False
        self.data['customer_id'] = None
        serializer = TransactionSerializer(data=self.data, context=self.context)
        assert serializer.is_valid()

    def test_pay_by_app_deposit_without_customer_without_family_and_friend(self):
        self.data['booking'] = self.appointment.subbookings[0].id
        self.data['transaction_type'] = Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
        self.data['payment_rows'][0]['payment_type_code'] = PaymentTypeEnum.PAY_BY_APP
        self.data['dry_run'] = False
        self.data['customer_id'] = None
        serializer = TransactionSerializer(data=self.data, context=self.context)
        assert not serializer.is_valid()

    def test_family_and_friends_prepayment(self):
        self.data['booking'] = self.family_and_friends_appointment.subbookings[0].id
        self.data['payment_rows'][0]['payment_type_code'] = PaymentTypeEnum.PREPAYMENT
        self.data['dry_run'] = False
        serializer = TransactionSerializer(data=self.data, context=self.context)
        assert serializer.is_valid()

    def test_family_and_friends_edit_no_customer_card(self):
        self.data['booking'] = self.family_and_friends_appointment.subbookings[0].id
        self.data['saved_bookings'] = [self.family_and_friends_appointment.subbookings[0].id]
        self.data['transaction_type'] = Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
        self.data['dry_run'] = False
        self.data['customer_id'] = None
        self.context['edit'] = True
        transaction = transaction_recipe.make()
        receipt = receipt_recipe.make(
            transaction=transaction, status_code=receipt_status.PAYMENT_CANCELED
        )
        transaction.latest_receipt = receipt
        transaction.save()
        self.context['old_txn'] = transaction
        serializer = TransactionSerializer(data=self.data, context=self.context)
        assert serializer.is_valid()
        assert (
            serializer.data['customer_card_id'] == self.family_and_friends_appointment.booked_by.id
        )
