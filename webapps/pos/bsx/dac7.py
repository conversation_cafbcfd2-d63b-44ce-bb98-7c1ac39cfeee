from django.conf import settings
from django.db.models import (
    Count,
    F,
    OuterRef,
    QuerySet,
    Subquery,
    Sum,
)

from webapps.booking.models import (
    Appointment,
)
from webapps.pos.models import (
    Transaction,
)
from webapps.pos.bsx.models import (
    BsxLog,
)

_RECEIPT_LOG__SUCCESS = "receipt printed successfully"


def report() -> QuerySet:
    # Since
    #  1. there is no relation between BsxLog and Transaction,
    #  2. we need data from both tables
    # Thus, in spite of a simple JOIN, we get complicated subqueries.
    # Also, due to this situation, we will be working on the WHOLE BsxLog table so...
    # NOTE executed queries may be quite heavy unless limited by a short time!
    # That being said, here's a precaution mechanism against running this in production.
    if settings.LIVE_DEPLOYMENT:
        raise RuntimeError(
            "This function invokes a HEAVY query! It should NOT be used on real databases.",
        )

    query_bsx = BsxLog.objects.filter(log=_RECEIPT_LOG__SUCCESS)
    query_trx_parents = (
        Transaction.objects.filter(parent_txn_id__isnull=False)
        .values("parent_txn_id")
        .distinct("parent_txn_id")
    )
    query_trx = Transaction.objects.filter(
        id__in=(
            query_bsx.values("transaction_id")
            .distinct("transaction_id")
            .difference(query_trx_parents)
        ),
        appointment__type=Appointment.TYPE.CUSTOMER,
    )
    query = (
        query_bsx.filter(transaction_id__in=query_trx)
        .values(
            "business_id",
            day=F("created__date"),
            total=Subquery(query_trx.filter(id=OuterRef("transaction_id")).values("total")[:1]),
        )
        .order_by(
            "day",
            "business_id",
        )
    )
    query_grouped_by_day_business = (
        query.values(
            "day",
            "business_id",
        )
        .annotate(
            amount=Sum("total"),
            volume=Count("*"),
        )
        .values(
            "day",
            "business_id",
            "amount",
            "volume",
        )
    )
    return query_grouped_by_day_business
