from django.utils.translation import gettext_lazy as _

from country_config import Country

# Change in https://booksy.atlassian.net/browse/POS-1997
BANK_ACCOUNT_NUMBER_FORM = {
    Country.US: {
        'routing_number': {
            'type': 'aba',
            'label': _('Routing number'),
            'title': _("Where's my routing number?"),
            'description': _(
                'Your routing number is the first series of numbers '
                'on your check or bank statement.'
            ),
        },
        'account_number': {
            'type': 'aba',
            'label': _('Account number'),
            'title': _("Where's my account number?"),
            'description': _(
                'Your account number is the second series of numbers '
                'on your check or bank statement.'
            ),
        },
    },
    Country.FR: {
        'account_number': {
            'type': 'iban',
            'label': _('IBAN'),
            'title': _("Where's my IBAN number?"),
            'description': _(
                'Your IBAN number is the series of numbers on your bank statement. '
                'Eg. *************************** (27 characters)'
            ),
        },
    },
    Country.PL: {
        'account_number': {
            'type': 'iban',
            'label': _('IBAN'),
            'title': _("Where's my IBAN number?"),
            'description': _(
                'Your IBAN number is the series of numbers on your bank statement. '
                'Eg. **************************** (28 characters)'
            ),
        },
    },
    Country.ES: {
        'account_number': {
            'type': 'iban',
            'label': _('IBAN'),
            'title': _("Where's my IBAN number?"),
            'description': _(
                'Your IBAN number is the series of numbers on your bank statement. '
                'Eg. ************************ (24 characters)'
            ),
        },
    },
    Country.IE: {
        'account_number': {
            'type': 'iban',
            'label': _('IBAN'),
            'title': _("Where's my IBAN number?"),
            'description': _(
                'Your IBAN number is the series of numbers on your bank statement. '
                'Eg. ********************** (22 characters)'
            ),
        },
    },
    Country.GB: {
        'sort_code': {
            'type': 'sort',
            'label': _('Sort code'),
            'title': _("Where's my sort code?"),
            'description': _(
                'Your sort code is the first series of numbers on your check or bank statement.'
            ),
        },
        'account_number': {
            'type': 'sort',
            'label': _('Account number'),
            'title': _("Where's my account number?"),
            'description': _(
                'Your account number is the second series of numbers '
                'on your check or bank statement.'
            ),
        },
    },
}
