import mock
import pytest
from django.test import TestCase

from lib.payments.enums import PaymentProviderCode
from webapps.payment_providers.ports.payment_ports import PaymentProvidersPaymentPort


@pytest.mark.django_db
class TestStripeTerminal(TestCase):
    @mock.patch('webapps.payment_providers.providers.stripe.stripe.terminal.ConnectionToken.create')
    def test_get_terminal_connection_token(self, stripe_provider_mock):
        stripe_provider_mock.return_value = mock.MagicMock(
            secret='connection_token',
        )
        connection_token = PaymentProvidersPaymentPort.get_terminal_connection_token(
            payment_provider_code=PaymentProviderCode.STRIPE,
        ).entity.token
        self.assertEqual(connection_token, 'connection_token')
        stripe_provider_mock.assert_called_once()
