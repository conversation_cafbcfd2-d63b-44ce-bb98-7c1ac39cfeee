from datetime import datetime

import pytest
import pytz
from django.test import TestCase
from model_bakery import baker

from lib.payment_providers.entities import (
    AccountHolderSettingsData,
    StripeAccountHolderSettingsEntity,
)
from lib.payment_providers.enums import ProviderAccountHolderStatus
from lib.payments.enums import PaymentProviderCode
from lib.tools import tznow
from webapps.business.models import Business
from webapps.payment_gateway.models import Wallet
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import (
    AccountHolder,
    Customer,
    StripeAccountHolder,
    StripeAccountHolderSettings,
    StripeCustomer,
    StripeLocation,
    TokenizedPaymentMethod,
)
from webapps.payment_providers.ports.account_holder_ports import PaymentProvidersAccountHolderPort
from webapps.pos.enums import PaymentProviderEnum
from webapps.pos.models import POS, PaymentMethod
from webapps.stripe_integration.enums import StripeAccountOnboardingInfo
from webapps.stripe_integration.models import StripeAccount as StripeAccountV1
from webapps.stripe_integration.models import StripeCustomer as StripeCustomerV1
from webapps.stripe_integration.models import StripeLocation as StripeLocationV1
from webapps.user.models import User, UserProfile


@pytest.mark.django_db
class TestSynchronizeStripeObjects(TestCase):
    def setUp(self) -> None:
        super().setUp()
        self.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+***********',
        )
        self.user_profile_customer = baker.make(
            UserProfile,
            user=self.user,
            profile_type=UserProfile.Type.CUSTOMER,
        )
        self.business = baker.make(
            Business,
            owner=self.user,
        )
        self.pos = baker.make(
            POS,
            pos_refactor_stage2_enabled=True,
            business=self.business,
        )
        self.common_account_holder = AccountHolder.objects.get(
            id=Wallet.objects.get(business_id=self.business.id).account_holder_id
        )
        self.common_customer = Customer.objects.get(
            id=Wallet.objects.get(user_id=self.user.id).customer_id
        )

    def test_synchronize_stripe_account_holder(self):
        def _assert_accounts(old_acc: StripeAccountV1, new_acc: StripeAccountHolder):
            self.assertEqual(old_acc.payouts_enabled, new_acc.payouts_enabled)
            self.assertEqual(old_acc.charges_enabled, new_acc.charges_enabled)
            self.assertEqual(old_acc.blocked, new_acc.blocked)
            self.assertEqual(old_acc.charge_for_tips, new_acc.charge_fee_for_tips)
            self.assertEqual(old_acc.status, new_acc.status)
            self.assertEqual(old_acc.kyc_verified_at_least_once, new_acc.kyc_verified_at_least_once)
            self.assertEqual(old_acc.tos_acceptance_date, new_acc.tos_acceptance_date)
            self.assertEqual(old_acc.onboarding_info, new_acc.onboarding_info)
            self.assertEqual(
                old_acc.fast_payout_merchant_max_limit,
                new_acc.fast_payout_merchant_max_limit,
            )
            self.assertEqual(old_acc.fast_payout_max_limit, new_acc.fast_payout_max_limit)
            self.assertEqual(old_acc.fast_payout_min_limit, new_acc.fast_payout_min_limit)
            self.assertEqual(old_acc.account_type, new_acc.account_type)
            self.assertEqual(
                old_acc.default_payout_method_for_fast_payout,
                new_acc.default_payout_method_for_fast_payout,
            )
            self.assertEqual(old_acc.created, new_acc.created)
            self.assertEqual(old_acc.updated, new_acc.updated)
            self.assertEqual(
                old_acc.pos.business.id,
                Wallet.objects.get(
                    account_holder_id=new_acc.account_holder_id,
                ).business_id,
            )
            self.assertEqual(
                old_acc.account_link_first_time_created,
                new_acc.account_link_first_time_created,
            )

            account_link_first_time_created = tznow()
            old_account = StripeAccountV1.objects.create(
                pos=self.pos,
                external_id='external_id',
                payouts_enabled=True,
                charges_enabled=True,
                blocked=False,
                charge_for_tips=True,
                status=ProviderAccountHolderStatus.VERIFIED,
                kyc_verified_at_least_once=True,
                tos_acceptance_date=datetime(2022, 1, 1, tzinfo=pytz.UTC),
                onboarding_info=StripeAccountOnboardingInfo.DEFAULT,
                fast_payout_merchant_max_limit='400.00',
                fast_payout_max_limit='5000.00',
                fast_payout_min_limit='100.00',
                account_link_first_time_created=account_link_first_time_created,
            )
            old_account.refresh_from_db()
            new_account = StripeAccountHolder.objects.get(external_id=old_account.external_id)

            _assert_accounts(old_account, new_account)

            old_account.payouts_enabled = False
            old_account.charges_enabled = False
            old_account.blocked = True
            old_account.charge_for_tips = False
            old_account.status = ProviderAccountHolderStatus.NOT_VERIFIED
            old_account.kyc_verified_at_least_once = False
            old_account.tos_acceptance_date = datetime(2022, 1, 2, tzinfo=pytz.UTC)
            old_account.onboarding_info = StripeAccountOnboardingInfo.SKIP
            old_account.fast_payout_merchant_max_limit = '401.00'
            old_account.fast_payout_max_limit = '5001.00'
            old_account.fast_payout_min_limit = '101.00'
            old_account.save()
            old_account.refresh_from_db()
            new_account.refresh_from_db()

            _assert_accounts(old_account, new_account)

    def test_synchronize_stripe_customer(self):
        def _assert_customers(old_cust, new_cust):
            self.assertEqual(old_cust.external_id, new_cust.external_id)
            self.assertEqual(
                old_cust.user.id,
                Wallet.objects.get(customer_id=new_cust.customer_id).user_id,
            )

        PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=self.user.id,
            email=self.user.email,
            phone=self.user.cell_phone,
            statement_name=self.user.email,
        )

        old_customer = StripeCustomerV1.objects.create(
            external_id='external_id',
            user=self.user,
        )
        new_customer = StripeCustomer.objects.get(external_id=old_customer.external_id)
        _assert_customers(old_customer, new_customer)

    def test_synchronize_stripe_location(self):
        def _assert_locations(old_loc, new_loc):
            self.assertEqual(old_loc.external_id, new_loc.external_id)
            self.assertEqual(
                old_loc.reader_configuration_id,
                new_loc.reader_configuration_external_id,
            )
            self.assertEqual(old_loc.name, new_loc.name)
            self.assertEqual(old_loc.created, new_loc.created)
            self.assertEqual(old_loc.updated, new_loc.updated)

            old_account = baker.make(
                StripeAccountV1,
                pos=self.pos,
                external_id='test_external_unique_id',
            )
            old_location = StripeLocationV1.objects.create(
                external_id='external_id',
                reader_configuration_id='config_external_id',
                name='name',
                account=old_account,
            )
            new_location = StripeLocation.objects.get(external_id=old_location.external_id)
            _assert_locations(old_location, new_location)

            old_location.reader_configuration_id = 'config_external_id_2'
            old_location.name = 'name_2'
            old_location.save()
            new_location.refresh_from_db()
            _assert_locations(old_location, new_location)

    def test_synchronize_stripe_tokenized_pm(self):
        def _assert_tokenized_pms(old_structure_tokenized_pm, new_structure_tokenized_pm):
            self.assertEqual(old_structure_tokenized_pm.default, new_structure_tokenized_pm.default)
            self.assertEqual(
                old_structure_tokenized_pm.card_type,
                new_structure_tokenized_pm.details['brand'],
            )
            self.assertEqual(
                old_structure_tokenized_pm.card_last_digits,
                new_structure_tokenized_pm.details['last_digits'],
            )
            self.assertNotEqual(
                old_structure_tokenized_pm.active,
                bool(new_structure_tokenized_pm.deleted),
            )
            self.assertEqual(
                old_structure_tokenized_pm.expiry_month,
                int(new_structure_tokenized_pm.details['expiry_month']),
            )
            self.assertEqual(
                old_structure_tokenized_pm.expiry_year,
                int(new_structure_tokenized_pm.details['expiry_year']),
            )
            self.assertEqual(
                old_structure_tokenized_pm.cardholder_name,
                new_structure_tokenized_pm.details['cardholder_name'],
            )
            self.assertEqual(old_structure_tokenized_pm.user_id, self.user.id)
            self.assertEqual(
                old_structure_tokenized_pm.provider,
                PaymentProviderEnum.STRIPE_PROVIDER,
            )

        new_structure_tokenized_pm = baker.make(
            TokenizedPaymentMethod,
            customer=self.common_customer,
            provider_code=PaymentProviderCode.STRIPE,
            details={
                'brand': 'visa',
                'last_digits': '1234',
                'expiry_month': 1,
                'expiry_year': 2032,
                'cardholder_name': 'ala makota',
            },
        )
        old_structure_tokenized_pm = PaymentMethod.objects.get(
            tokenized_pm_id=new_structure_tokenized_pm.id,
        )
        _assert_tokenized_pms(old_structure_tokenized_pm, new_structure_tokenized_pm)

        new_structure_tokenized_pm.default = False
        new_structure_tokenized_pm.details = {
            'last_digits': '2345',
            'brand': 'maestro',
            'expiry_month': '04',
            'expiry_year': '2032',
            'cardholder_name': 'new carholder_name',
        }
        new_structure_tokenized_pm.save()
        old_structure_tokenized_pm.refresh_from_db()
        _assert_tokenized_pms(old_structure_tokenized_pm, new_structure_tokenized_pm)

        new_structure_tokenized_pm.soft_delete()
        old_structure_tokenized_pm.refresh_from_db()
        _assert_tokenized_pms(old_structure_tokenized_pm, new_structure_tokenized_pm)

    def test_set_account_holder_settings(self):
        stripe_account_holder_settings = baker.make(
            StripeAccountHolderSettings,
            account_holder=self.common_account_holder,
            pba_fees_accepted=False,
            bcr_fees_accepted=False,
            tap_to_pay_fees_accepted=False,
            tap_to_pay_celebration_screen_shown=False,
        )
        PaymentProvidersAccountHolderPort.set_account_holder_settings(
            account_holder_id=self.common_account_holder.id,
            payment_provider_code=PaymentProviderCode.STRIPE,
            account_holder_settings_data=AccountHolderSettingsData(
                stripe=StripeAccountHolderSettingsEntity(
                    pba_fees_accepted=True,
                    bcr_fees_accepted=True,
                    tap_to_pay_fees_accepted=True,
                    tap_to_pay_celebration_screen_shown=True,
                )
            ),
        )
        stripe_account_holder_settings.refresh_from_db()
        self.assertEqual(
            stripe_account_holder_settings.pba_fees_accepted,
            True,
        )
        self.assertEqual(
            stripe_account_holder_settings.bcr_fees_accepted,
            True,
        )
        self.assertEqual(
            stripe_account_holder_settings.tap_to_pay_fees_accepted,
            True,
        )
        self.assertEqual(
            stripe_account_holder_settings.tap_to_pay_celebration_screen_shown,
            True,
        )
