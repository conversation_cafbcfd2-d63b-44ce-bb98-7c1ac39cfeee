import datetime
from dataclasses import asdict

import mock
import pytest
from mock import MagicMock
from model_bakery import baker

from django.test import TestCase
from django.test.utils import override_settings
from stripe import util
from stripe.api_resources.account import Account

from country_config import Country

from lib.business_consents.events import user_input_kyc_consent_accepted_event
from lib.payment_providers.entities import USUserInputKYCEntity
from lib.tools import tznow
from service.tests import dict_assert

from webapps.business_consents.enums import ConsentCode, ConsentAction
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import (
    AccountHolder,
    StripeAccountHolder,
)
from webapps.payment_providers.management.commands.tests.mocked_responses import (
    get_stripe_account_create_response,
)
from webapps.stripe_integration.models import StripeAccount
from webapps.stripe_integration.enums import StripeAccountStatus

STRIPE_ACCOUNT_ID = 'acct_1MEbikIKKlixRLbl'


def prepare_stripe_account_response(stripe_account_id: str) -> Account:
    stripe_account_create_response = get_stripe_account_create_response(
        stripe_account_id,
    )
    account = util.convert_to_stripe_object(stripe_account_create_response, None, None, None)
    return account


@pytest.mark.django_db
@override_settings(API_COUNTRY=Country.US)
@mock.patch('webapps.payment_gateway.ports.PaymentGatewayPort.get_account_holder_wallet')
@mock.patch('webapps.payment_providers.providers.stripe.stripe.Account.create')
@mock.patch('webapps.payment_providers.providers.stripe.stripe.Account.modify')
class TestStripeCreateAccountHolderWithUserInputKYC(TestCase):
    def setUp(self):
        self.business = baker.make("business.Business", name='test name')
        self.pos = baker.make("pos.POS", business=self.business)
        self.account_holder = baker.make(
            AccountHolder,
            statement_name='account_holder',
            metadata={'business_id': self.business.id},
        )

    def test_create_account_holder_with_user_input_kyc_individual(
        self,
        modify_stripe_account,
        create_stripe_account,
        get_account_holder_wallet_mock,
    ):
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=self.business.id)
        create_stripe_account.side_effect = [prepare_stripe_account_response(STRIPE_ACCOUNT_ID)]

        self.assertFalse(StripeAccount.objects.filter(pos=self.pos).exists())

        consent = baker.make(
            'BusinessConsent',
            business_id=self.business.id,
            consent_code=ConsentCode.US_USER_INPUT_KYC,
            decision=ConsentAction.AGREE,
            filling_ip='127.0.0.1',
            filling_date=tznow(),
            payload={
                'type': 'individual',
                'first_name': 'bob',
                'last_name': 'kowalski',
            },
        )
        consent_data = consent.payload
        user_input_kyc_consent_accepted_event.send(
            asdict(
                USUserInputKYCEntity(
                    business_id=consent.business_id,
                    type=consent_data['type'],
                    filling_ip=consent.filling_ip,
                    filling_date=consent.filling_date,
                    first_name=consent_data['first_name'],
                    last_name=consent_data['last_name'],
                    company_name=None,
                )
            ),
        )

        create_stripe_account.assert_called_once()
        dict_assert(
            create_stripe_account.call_args_list[0][1],
            {
                'type': 'custom',
                'capabilities': {'transfers': {'requested': True}},
                'business_profile': {'url': 'www.booksy.com'},
                'settings': {
                    'payments': {
                        'statement_descriptor_prefix': 'Booksy',
                        'statement_descriptor': 'test name',
                    }
                },
                'metadata': {
                    'business_id': self.business.id,
                    'country_code': 'us',
                    'test_environment_code': 'dev',
                },
                'tos_acceptance': {
                    'date': int(datetime.datetime.timestamp(consent.filling_date)),
                    'ip': '127.0.0.1',
                },
                'individual': {
                    'first_name': 'bob',
                    'last_name': 'kowalski',
                },
                'email': self.business.owner.email,
                'business_type': 'individual',
                'account_token': None,
            },
            strict=True,
        )
        self.assertTrue(StripeAccount.objects.filter(pos=self.pos).exists())
        self.assertTrue(StripeAccountHolder.objects.filter(external_id=STRIPE_ACCOUNT_ID).exists())

        self.assertEqual(modify_stripe_account.call_count, 2)

    def test_create_account_holder_with_user_input_kyc_company(
        self,
        modify_stripe_account,
        create_stripe_account,
        get_account_holder_wallet_mock,
    ):
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=self.business.id)
        create_stripe_account.side_effect = [prepare_stripe_account_response(STRIPE_ACCOUNT_ID)]

        self.assertFalse(StripeAccount.objects.filter(pos=self.pos).exists())

        consent = baker.make(
            'BusinessConsent',
            business_id=self.business.id,
            consent_code=ConsentCode.US_USER_INPUT_KYC,
            decision=ConsentAction.AGREE,
            filling_ip='127.0.0.1',
            filling_date=tznow(),
            payload={
                'type': 'company',
                'company_name': 'Test corp',
            },
        )
        consent_data = consent.payload
        user_input_kyc_consent_accepted_event.send(
            asdict(
                USUserInputKYCEntity(
                    business_id=consent.business_id,
                    type=consent_data['type'],
                    filling_ip=consent.filling_ip,
                    filling_date=consent.filling_date,
                    first_name=None,
                    last_name=None,
                    company_name=consent_data['company_name'],
                )
            ),
        )

        create_stripe_account.assert_called_once()
        dict_assert(
            create_stripe_account.call_args_list[0][1],
            {
                'type': 'custom',
                'capabilities': {'transfers': {'requested': True}},
                'business_profile': {'url': 'www.booksy.com'},
                'settings': {
                    'payments': {
                        'statement_descriptor_prefix': 'Booksy',
                        'statement_descriptor': 'test name',
                    }
                },
                'metadata': {
                    'business_id': self.business.id,
                    'country_code': 'us',
                    'test_environment_code': 'dev',
                },
                'tos_acceptance': {
                    'date': int(datetime.datetime.timestamp(consent.filling_date)),
                    'ip': '127.0.0.1',
                },
                'company': {
                    'name': 'Test corp',
                },
                'email': self.business.owner.email,
                'business_type': 'company',
                'account_token': None,
            },
            strict=True,
        )
        self.assertTrue(StripeAccount.objects.filter(pos=self.pos).exists())
        self.assertTrue(StripeAccountHolder.objects.filter(external_id=STRIPE_ACCOUNT_ID).exists())

        self.assertEqual(modify_stripe_account.call_count, 2)


@pytest.mark.django_db
@override_settings(API_COUNTRY=Country.US)
@mock.patch('webapps.payment_gateway.ports.PaymentGatewayPort.get_account_holder_wallet')
@mock.patch('webapps.payment_providers.providers.stripe.stripe.Account.modify')
class TestStripeUpdateAccountHolderWithUserInputKYC(TestCase):
    def setUp(self):
        self.business = baker.make("business.Business", name='test name')
        self.pos = baker.make("pos.POS", business=self.business)
        self.account_holder = baker.make(
            AccountHolder,
            statement_name='account_holder',
            metadata={'business_id': self.business.id},
        )
        self.stripe_account = baker.make(
            StripeAccount,
            external_id="acct_666",
            pos=self.pos,
            status=StripeAccountStatus.TURNED_OFF,
            charges_enabled=False,
            payouts_enabled=False,
        )
        business_wallet, _ = PaymentGatewayPort.get_or_create_business_wallet(
            business_id=self.business.id,
            statement_name='statement name',
        )
        self.stripe_account_holder = baker.make(
            StripeAccountHolder,
            account_holder_id=business_wallet.account_holder_id,
            external_id=self.stripe_account.external_id,
        )

    def test_update_account_holder_with_user_input_kyc_individual(
        self,
        modify_stripe_account,
        get_account_holder_wallet_mock,
    ):
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=self.business.id)

        consent = baker.make(
            'BusinessConsent',
            business_id=self.business.id,
            consent_code=ConsentCode.US_USER_INPUT_KYC,
            decision=ConsentAction.AGREE,
            filling_ip='127.0.0.1',
            filling_date=tznow(),
            payload={
                'type': 'individual',
                'first_name': 'bob',
                'last_name': 'kowalski',
            },
        )
        consent_data = consent.payload
        user_input_kyc_consent_accepted_event.send(
            asdict(
                USUserInputKYCEntity(
                    business_id=consent.business_id,
                    type=consent_data['type'],
                    filling_ip=consent.filling_ip,
                    filling_date=consent.filling_date,
                    first_name=consent_data['first_name'],
                    last_name=consent_data['last_name'],
                    company_name=None,
                )
            ),
        )

        modify_stripe_account.assert_called_once()
        dict_assert(
            modify_stripe_account.call_args_list[0][1],
            {
                'capabilities': {'transfers': {'requested': True}},
                'business_profile': {'url': 'www.booksy.com'},
                'settings': {
                    'payments': {
                        'statement_descriptor_prefix': 'Booksy',
                        'statement_descriptor': 'test name',
                    }
                },
                'metadata': {
                    'business_id': self.business.id,
                    'country_code': 'us',
                    'test_environment_code': 'dev',
                },
                'tos_acceptance': {
                    'date': int(datetime.datetime.timestamp(consent.filling_date)),
                    'ip': '127.0.0.1',
                },
                'individual': {
                    'first_name': 'bob',
                    'last_name': 'kowalski',
                },
                'business_type': 'individual',
            },
            strict=True,
        )
