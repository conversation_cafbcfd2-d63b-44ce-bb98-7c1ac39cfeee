import pytest
from django.core.management import call_command
from mock import patch
from model_bakery import baker

from webapps.business.models import Business
from webapps.payment_providers.models import (
    StripeAccountHolder,
    StripeAccountHolderSettings,
    StripeLocation as StripeLocationV2,
)
from webapps.pos.models import POS
from webapps.stripe_integration.models import StripeAccount as StripeAccountV1
from webapps.stripe_integration.models import StripeLocation as StripeLocationV1
from webapps.user.models import User


def mock_submit(self, fn, *args, **kwargs):  # pylint: disable=invalid-name
    # without separate threads
    result = fn(*args, **kwargs)
    return result


@patch('concurrent.futures.ThreadPoolExecutor.submit', mock_submit)
@patch('stripe.Account.delete')
@patch('stripe.terminal.Location.delete')
@pytest.mark.django_db
def test_clean_up_stripe_accounts_success(mock_stripe_location_delete, mock_stripe_account_delete):
    user_1 = baker.make(
        User,
        email='<EMAIL>',
        cell_phone='+***********',
    )
    user_2 = baker.make(
        User,
        email='<EMAIL>',
        cell_phone='+***********',
    )
    business_1 = baker.make(Business, owner=user_1, name='B1')
    business_2 = baker.make(Business, owner=user_2, name='B2')
    pos_1 = baker.make(
        POS,
        pos_refactor_stage2_enabled=True,
        business=business_1,
    )
    pos_2 = baker.make(
        POS,
        pos_refactor_stage2_enabled=True,
        business=business_2,
    )
    account_1 = baker.make(
        StripeAccountV1,
        pos=pos_1,
        external_id='test_external_unique_id_1',
    )
    account_2 = baker.make(
        StripeAccountV1,
        pos=pos_2,
        external_id='test_external_unique_id_2',
    )
    baker.make(
        StripeLocationV1,
        external_id='external_id_1',
        reader_configuration_id='config_external_id_1',
        account=account_1,
        default=True,
    )
    baker.make(
        StripeLocationV1,
        external_id='external_id_2',
        reader_configuration_id='config_external_id_2',
        account=account_2,
        default=True,
    )

    assert StripeLocationV1.all_objects.count() == 2
    assert StripeLocationV2.all_objects.count() == 2

    assert StripeAccountHolder.all_objects.count() == 2
    assert StripeAccountHolderSettings.all_objects.count() == 2

    call_command('clean_up_stripe_accounts', f'--businesses={business_1.id}')

    assert mock_stripe_account_delete.call_count == 1
    assert mock_stripe_location_delete.call_count == 1

    assert pos_1.stripe_account is None
    assert pos_2.stripe_account == account_2

    assert StripeLocationV1.all_objects.count() == 1
    assert StripeLocationV2.all_objects.count() == 1
    assert StripeLocationV1.all_objects.filter(external_id='external_id_2').exists()
    assert StripeLocationV2.all_objects.filter(external_id='external_id_2').exists()

    assert StripeAccountHolder.all_objects.count() == 1
    assert StripeAccountHolder.all_objects.filter(external_id='test_external_unique_id_2').exists()

    assert StripeAccountHolderSettings.all_objects.count() == 1
