from concurrent.futures import ThreadPoolExecutor
import stripe

from django.conf import settings
from django.core.management import CommandError
from django.core.management.base import BaseCommand
from django.db import transaction

from lib.db import PAYMENTS_DB
from country_config import Country
from webapps.stripe_integration.models import StripeAccount
from webapps.stripe_integration.provider import StripeProvider
from webapps.payment_providers.models import StripeAccountHolder
from webapps.pos.models import POS


class Command(BaseCommand):
    help = 'Script to delete old Stripe accounts (both in Stripe and database) and related objects'

    def add_arguments(self, parser):
        parser.add_argument(
            '--max_workers',
            type=int,
            default=10,
            help='ThreadPoolExecutor.max_workers argument',
            dest='max_workers',
        )
        parser.add_argument(
            '--businesses',
            type=str,
            help='Comma-separated list of businesses: 123,345,567',
            dest='businesses',
            required=False,
        )
        parser.add_argument(
            '--filename',
            type=str,
            help='Name of file with businesses to be loaded (requires one business ID per line!)',
            dest='filename',
            required=False,
        )

    def turn_off_pba(self, pos: POS):
        result = pos.disable_pay_by_app()
        if not result:
            # just for safety: if payment type didn't exist - disable POS status anyway
            pos.update_disable_pba()

    def can_be_deleted(self, stripe_account: StripeAccount):
        if stripe_account.intents.exists() or stripe_account.payouts.exists():
            return False
        return True

    @staticmethod
    def _check_country():
        if settings.API_COUNTRY not in (Country.US, Country.PL):
            raise CommandError(f'Invalid country - {settings.API_COUNTRY.upper()}')

    def load_businesses(self, **opts) -> list:
        businesses_str = opts.get('businesses')
        filename = opts.get('filename')
        if businesses_str:
            businesses = businesses_str.split(',')
        elif filename:
            with open(filename, 'r', encoding='UTF-8') as f:
                businesses = f.read().splitlines()
        else:
            raise CommandError('No businesses provided')

        self.stdout.write(self.style.SUCCESS(f"Got {len(businesses)} businesses"))

        return businesses

    def handle(self, *args, **opts):  # pylint: disable=unused-argument
        def thread_wrapper(stripe_account: StripeAccount):
            external_id = stripe_account.external_id
            pos = stripe_account.pos
            location = StripeProvider.get_location(stripe_account)

            self.stdout.write(self.style.SUCCESS(f"Starting for {external_id}"))

            if not self.can_be_deleted(stripe_account):
                self.stdout.write(
                    self.style.ERROR(
                        f'Cannot delete account {external_id} (business={pos.business_id}) '
                        f'because it has intents or payouts!'
                    )
                )
                return

            try:
                with transaction.atomic(), transaction.atomic(using=PAYMENTS_DB):
                    # hard delete with cascade-deleting related objects (eg. StripeLocation)
                    stripe_account.delete()
                    self.turn_off_pba(pos)

                    sah = (
                        StripeAccountHolder.objects.filter(external_id=external_id)
                        .select_related('account_holder')
                        .first()
                    )
                    if not sah:
                        # shouldn't happen but...
                        self.stdout.write(
                            self.style.ERROR(
                                f'StripeAccountHolder for account {external_id}, '
                                f'business={pos.business_id} not found!'
                            )
                        )
                        raise Exception  # pylint: disable=broad-exception-raised
                    account_holder = sah.account_holder
                    sahs = getattr(account_holder, "stripe_account_holder_settings", None)

                    sah.delete()
                    if sahs:
                        # we don't want to store old fees acceptance
                        sahs.delete()

                    try:
                        stripe.Account.delete(external_id)
                    except Exception as e:  # pylint: disable=broad-exception-caught
                        self.stdout.write(
                            self.style.ERROR(
                                f'Failed to delete account {external_id} in Stripe! {e}'
                            )
                        )
                        raise
                    if location:
                        try:
                            stripe.terminal.Location.delete(location.external_id)
                        except Exception as e:  # pylint: disable=broad-exception-caught
                            self.stdout.write(
                                self.style.ERROR(
                                    f'Failed to delete location {location.external_id} '
                                    f'in Stripe! {e}'
                                )
                            )
            except Exception as ex:  # pylint: disable=broad-exception-caught
                self.stdout.write(
                    self.style.ERROR(
                        f'Process failed for account {external_id} (business={pos.business_id})! '
                        f'{ex}'
                    )
                )
                return

            self.stdout.write(
                self.style.SUCCESS(
                    f"Stripe account {external_id} deleted for business {pos.business_id}"
                )
            )

        self._check_country()
        businesses = self.load_businesses(**opts)

        qs = StripeAccount.all_objects.filter(pos__business_id__in=businesses).select_related('pos')
        self.stdout.write(self.style.SUCCESS(f"Found {qs.count()} stripe accounts"))

        with ThreadPoolExecutor(max_workers=opts.get('max_workers')) as executor:
            executor.map(thread_wrapper, qs.iterator(chunk_size=100))

        self.stdout.write(self.style.SUCCESS("Done"))
