class AlreadySentForAuthorizationException(Exception):
    pass


class AlreadyAuthorizedException(Exception):
    pass


class AlreadySentForCaptureException(Exception):
    pass


class AlreadyCapturedException(Exception):
    pass


class WrongPaymentActionException(Exception):
    pass


class NotificationHandlerNotFoundException(Exception):
    pass


class TokenizedPaymentMethodException(Exception):
    pass


class ProviderNotSupportException(Exception):
    pass


class MissingRequiredData(Exception):
    pass


class MissingCustomer(Exception):
    pass


class ProviderServiceClassNotImplementedException(Exception):
    pass


class WrongPaymentStatusTransitionException(Exception):
    pass


class PayoutMethodAddingException(Exception):
    pass


class DefaultPayoutMethodDeleteAttempt(Exception):
    pass


class PayoutMethodNotFound(Exception):
    pass


class BusinessKYCDataNotFoundException(Exception):
    pass


class BusinessKYCCompanyClosedException(Exception):
    pass
