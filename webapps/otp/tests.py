import mock
import pytest
from django.urls import reverse
from model_bakery import baker
from rest_framework import status

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from lib.feature_flag.feature.payment import OTPCodeRequiredFlag
from lib.tests.utils import override_eppo_feature_flag
from webapps.business.baker_recipes import business_recipe
from webapps.user.models import User

# pylint: disable=no-name-in-module
from webapps.session.booksy_auth.pb2.auth_pb2 import GenerateOTPResponse

# pylint: enable=no-name-in-module


@override_eppo_feature_flag({OTPCodeRequiredFlag.flag_name: True})
class OTPResendTestCase(BaseBusinessApiTestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+***********',
        )
        cls.business = business_recipe.make()

    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.get_otp_code',
        return_value=GenerateOTPResponse(otp_code='123456'),
    )
    @pytest.mark.usefixtures('switch_on_new_login_fixture')
    def test_resend_otp(
        self,
        _mock_get_otp_code,
    ):
        url = reverse(
            'otp_resend',
        )
        response = self.client.post(url, data={'otp_code': 'resend'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient.get_otp_code',
        return_value=GenerateOTPResponse(otp_code='123456'),
    )
    @pytest.mark.usefixtures('switch_on_new_login_fixture')
    @mock.patch('service.mixins.throttling.is_throttle_whitelist_ip', return_value=False)
    def test_otp_throttling(
        self,
        _mock_is_throttle_whitelist_ip,
        _mock_get_otp_code,
    ):
        url = reverse(
            'otp_resend',
        )
        for _ in range(20):
            response = self.client.post(url, data={'otp_code': 'resend'})
            self.assertEqual(response.status_code, status.HTTP_200_OK)

        response = self.client.post(url, data={'otp_code': 'resend'})
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
