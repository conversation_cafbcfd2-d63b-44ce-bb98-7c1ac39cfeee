from copy import deepcopy

from django.test.utils import override_settings
from django.urls import reverse
from model_bakery import baker
from rest_framework import status

from drf_api.lib.base_drf_test_case import BusinessOwnerAPITestCase
from webapps.business.baker_recipes import business_recipe
from webapps.notification.enums import NotificationTarget
from webapps.profile_setup.models import ProfileSetupProgress
from webapps.profile_setup.text import ProfileSetupSteps, ProfileSetupStepData
from webapps.user.baker_recipes import user_recipe


class TestProfileSetupStepsView(BusinessOwnerAPITestCase):

    def setUp(self):
        self.user = user_recipe.make()
        self.business = business_recipe.make(owner=self.user)
        super().setUp()
        self.url = reverse('profile_setup_steps', args=(self.business.id,))

    def _get_expected_steps(self, **kwargs) -> list[ProfileSetupStepData]:
        expected_steps = deepcopy(ProfileSetupSteps)
        for step in expected_steps:
            if step['target_type'] is None:
                step['is_completed'] = True
            else:
                step['is_completed'] = kwargs.get(step['target_type'], False)
        return expected_steps

    def test_get_progress_exists(self):
        baker.make(ProfileSetupProgress, business=self.business, cover_photo=True)
        expected_steps = self._get_expected_steps(cover_photo=True)

        response = self.client.get(self.url)

        assert response.status_code == status.HTTP_200_OK
        assert response.json()
        assert response.json() == {'steps': expected_steps}

    def test_get_progress_not_found(self):
        response = self.client.get(self.url)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_business_not_found(self):
        url = reverse('profile_setup_steps', args=(123456,))

        response = self.client.get(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    @override_settings(LANGUAGE_CODE='es')
    def test_get_progress_translated(self):
        baker.make(ProfileSetupProgress, business=self.business, cover_photo=True)

        response = self.client.get(self.url)

        assert response.status_code == status.HTTP_200_OK
        assert response.json()
        step_names = [step['name'] for step in response.json()['steps']]
        assert 'foto' in '\n'.join(step_names)

    def test_create_progress_business_exists(self):
        expected_steps = self._get_expected_steps(profile_registered=True)

        response = self.client.post(self.url)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.json()
        assert response.json() == {'steps': expected_steps}
        assert ProfileSetupProgress.objects.filter(business=self.business).exists() is True
        progress = ProfileSetupProgress.objects.get(business=self.business)
        assert getattr(progress, NotificationTarget.COVER_PHOTO) is False
        assert getattr(progress, NotificationTarget.PORTFOLIO) is False
        assert getattr(progress, NotificationTarget.BUSINESS_HOURS) is False
        assert getattr(progress, NotificationTarget.SERVICES) is False

    def test_create_progress_business_not_found(self):
        url = reverse('profile_setup_steps', args=(123456,))

        response = self.client.post(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert ProfileSetupProgress.objects.filter(business=self.business).exists() is False
