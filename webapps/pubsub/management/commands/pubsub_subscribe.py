import logging
import signal

from django.core.management import BaseCommand

from webapps.pubsub.subscriber import SubscriberCategory, Subscriber
from webapps.pubsub.worker import Worker

logger = logging.getLogger('booksy.pubsub')


class Command(BaseCommand):
    help = "Start subscriber threads to consume messages from Pub/Sub topics."

    def add_arguments(self, parser):
        parser.add_argument(
            '--subscriber-category',
            type=str,
            choices=SubscriberCategory.values(),
            help='Category of subscribers that the worker should handle.',
        )

    def handle(self, *args, **options):
        subscriber_category = (
            SubscriberCategory(options['subscriber_category'])
            if options['subscriber_category']
            else None
        )

        if subscriber_category:
            subscribers = Subscriber.subscribers_by_category(subscriber_category)
        else:
            subscribers = list(Subscriber.registry.values())

        logger.info("Configuring worker with %s subscription(s)...", len(subscribers))
        for subscriber in subscribers:
            logger.info("  %s", subscriber.subscription_name)

        worker = Worker(subscribers=subscribers)

        # to allow killing worker via ctrl+c
        signal.signal(signal.SIGINT, worker.stop)
        signal.signal(signal.SIGTERM, worker.stop)
        signal.signal(signal.SIGTSTP, worker.stop)

        worker.start()
