import pytest
from django.test import override_settings
from webapps.market_pay.helpers import (
    _parse_external_id_to_account_holder_code,
    generate_account_holder_code,
)


@pytest.mark.parametrize(
    'external_id, expected',
    [
        ('pl-188641', 'pl-188641'),
        ('us-1232', 'us-1232'),
        ('test1.t1.booksy.pm-us-421839', 'test1-t1-booksy-pm-us-421839'),
        ('12s!@#$', '12s----'),
    ],
)
def test_parse_external_id_to_account_holder_code(external_id, expected):
    assert _parse_external_id_to_account_holder_code(external_id) == expected


@override_settings(DEPLOYMENT_LEVEL='test.test.', API_COUNTRY='us')
def test_generate_account_holder_code():
    assert generate_account_holder_code(1) == 'test-test--us-1'
