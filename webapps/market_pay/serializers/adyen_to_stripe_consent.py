import datetime
import typing as t
from django.db.models import Q
from django.utils.functional import cached_property
from rest_framework import serializers

from drf_api_utils.serializers import SchemaTypeSerializerMethodField
from lib.feature_flag.feature import (
    AdyenToStripeMaxNumberOfConsentsStageI,
    AdyenToStripeMaxNumberOfConsentsStageII,
    AdyenToStripeMigrationConsentDeadlineFlag,
)
from lib.feature_flag.killswitch import DisableAdyenToStripeMigrationConsentStageII
from lib.tools import sget
from webapps.business.models import Business, Resource
from webapps.experiment_v3.exp import ConsentAdyenStripeCalendarTextExperiment
from webapps.market_pay.consent_adyen_to_stripe_utils import (
    should_business_user_see_consent_request_stage_i,
)
from webapps.market_pay.enums import AdyenToStripeMigrationConsentStage
from webapps.market_pay.models import AdyenToStripeMigrationConsent
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import Transaction
from webapps.user.models import User


class BaseAdyenToStripeBusinessConsentSerializer(serializers.ModelSerializer):
    agreed = serializers.BooleanField(required=True, write_only=True)
    show_adyen_to_stripe_consent = SchemaTypeSerializerMethodField(
        schema_field=serializers.BooleanField,
    )
    deadline = serializers.SerializerMethodField()

    def get_show_adyen_to_stripe_consent(self, instance):  # pylint: disable=unused-argument
        raise NotImplementedError

    def validate(self, attrs: dict) -> dict:
        attrs = super().validate(attrs)
        attrs['business_id'] = self.business_id
        attrs['ip_address'] = self.get_ip_address()
        attrs['approver'] = self.get_approver()
        attrs['stage'] = self.stage
        return attrs

    def get_ip_address(self) -> str:
        ip_address = self._get_x_forwarded_for_address() or self._get_remote_address()
        if not ip_address:
            raise serializers.ValidationError(
                {
                    'ip_address': 'information about ip address is required',
                }
            )
        return ip_address

    def _get_x_forwarded_for_address(self):
        x_forwarded_for = sget(
            self.context,
            [
                'request',
                '_request',
                'META',
                'HTTP_X_FORWARDED_FOR',
            ],
        )
        if not x_forwarded_for:
            return None
        return x_forwarded_for.split(',')[0].strip()

    def _get_remote_address(self):
        return sget(self.context, ['request', '_request', 'META', 'REMOTE_ADDR'])

    def get_approver(self) -> User:
        request_user = sget(self, ['context', 'request', 'user'])
        request_user_id = sget(request_user, ['id'])
        if (
            not Resource.objects.filter(
                staff_user_id=request_user_id,
                business_id=self.business_id,
            ).exists()
            and self.business.owner_id != request_user_id
        ):
            raise serializers.ValidationError(
                {
                    'approver': '',
                }
            )
        return request_user

    @staticmethod
    def get_deadline(instance):  # pylint: disable=unused-argument
        flag_value = AdyenToStripeMigrationConsentDeadlineFlag()
        try:
            datetime.date.fromisoformat(flag_value)
        except ValueError:
            return None
        return flag_value

    @cached_property
    def business_id(self) -> int:
        if self.instance and isinstance(self.instance, dict):
            return self.instance.get('business_pk')
        return self.context['business_pk']

    @cached_property
    def business(self) -> Business:
        return Business.objects.filter(id=self.business_id).first()

    @cached_property
    def user_id(self) -> int:
        if isinstance(self.instance, dict):
            return self.instance['user_id']
        return self.instance.approver_id

    @property
    def stage(self):
        raise NotImplementedError


class AdyenToStripeBusinessConsentSerializer(BaseAdyenToStripeBusinessConsentSerializer):
    variant = serializers.SerializerMethodField()
    consent_status = serializers.SerializerMethodField()

    class Meta:
        model = AdyenToStripeMigrationConsent
        fields = (
            'agreed',
            'consent_status',
            'show_adyen_to_stripe_consent',
            'variant',
            'deadline',
        )

    def get_show_adyen_to_stripe_consent(self, instance):  # pylint: disable=unused-argument
        return should_business_user_see_consent_request_stage_i(
            self.business_id,
            self.user_id,
        )

    def get_variant(self, instance) -> t.Optional[str]:  # pylint: disable=unused-argument
        if not AdyenToStripeMigrationConsent.is_consent_feature_available(self.user_id):
            return None
        return self.experiment_obj.get_variant(return_usable_name=False)

    def get_consent_status(self, instance) -> str:  # pylint: disable=unused-argument
        return AdyenToStripeMigrationConsent.get_consent_status(
            business_id=self.business_id,
            user_id=self.user_id,
        )

    @cached_property
    def experiment_obj(self) -> ConsentAdyenStripeCalendarTextExperiment:
        return ConsentAdyenStripeCalendarTextExperiment(self.business_id)

    def get_timestamp(self):
        return self.business.tznow

    @property
    def stage(self):
        return AdyenToStripeMigrationConsentStage.STAGE_I


class AdyenToStripeBusinessPostCheckoutConsentSerializer(
    BaseAdyenToStripeBusinessConsentSerializer,
):
    class Meta:
        model = AdyenToStripeMigrationConsent
        fields = (
            'agreed',
            'show_adyen_to_stripe_consent',
            'deadline',
        )

    # pylint: disable=unused-argument, too-many-return-statements
    def get_show_adyen_to_stripe_consent(self, instance):
        if DisableAdyenToStripeMigrationConsentStageII():
            return False
        if not AdyenToStripeMigrationConsent.is_consent_needed(
            business_id=self.business_id,
            user_id=self.user_id,
        ):
            return False
        base_query = AdyenToStripeMigrationConsent.get_base_query(
            business_id=self.business_id,
            user_id=self.user_id,
        )
        count = base_query.count()
        if (count < AdyenToStripeMaxNumberOfConsentsStageI()) or (
            count >= AdyenToStripeMaxNumberOfConsentsStageII()
        ):
            return False
        if not self.is_pay_by_app_transaction:
            return False
        return not AdyenToStripeMigrationConsent.does_recent_consent_exists(
            business_id=self.business_id,
            user_id=self.user_id,
        )

    @cached_property
    def business_id(self) -> int:
        business_id = super().business_id
        if business_id:
            return business_id
        return (
            Transaction.objects.filter(
                id=self.transaction_id,
            )
            .values_list('pos__business_id', flat=True)
            .first()
        )

    @cached_property
    def transaction_id(self) -> int:
        if self.instance and isinstance(self.instance, dict):
            return self.instance.get('transaction_pk')
        return self.context['transaction_pk']

    @cached_property
    def is_pay_by_app_transaction(self):
        return (
            Transaction.objects.filter(
                id=self.transaction_id,
            )
            .filter(
                Q(
                    latest_receipt__payment_type__code=PaymentTypeEnum.PAY_BY_APP,
                )
                | Q(
                    latest_receipt__payment_rows__payment_type__code=PaymentTypeEnum.PAY_BY_APP,
                )
            )
            .exists()
        )

    @property
    def stage(self):
        return AdyenToStripeMigrationConsentStage.STAGE_II
