# Generated by Django 2.2.13 on 2020-11-18 13:28
from django.conf import settings
from django.db import DEFAULT_DB_ALIAS, connections, migrations

from lib.db import REPORTS_DB
from webapps.stats_and_reports.db_operations import (
    ConnectionDetails,
    CreateExtensionDevDeploymentOnly,
    CreatePgLogicalNodeDevDeploymentOnly,
    CreateSubscription,
)


def get_subscriber_node_connection_details() -> ConnectionDetails:
    reports_db_conf = settings.DATABASES[REPORTS_DB]
    return ConnectionDetails(
        host=reports_db_conf['HOST'],
        port=reports_db_conf['PORT'],
        user=reports_db_conf['USER'],
        password=reports_db_conf['PASSWORD'],
        dbname=reports_db_conf['NAME'],
    )


def get_subscription_connection_details() -> ConnectionDetails:
    default_db = settings.DATABASES[DEFAULT_DB_ALIAS]
    reports_db = settings.DATABASES[REPORTS_DB]

    # On prod and test deployments we connect to primary db with special
    # replication credentials.
    user = reports_db.get('REPLICATION_USER', '')
    password = reports_db.get('REPLICATION_PASSWORD', '')

    if settings.LOCAL_DEPLOYMENT:
        # On local developer environment we connect to primary db with primary
        # db credentials
        user = default_db['USER']
        password = default_db['PASSWORD']

    return ConnectionDetails(
        host=default_db['HOST'],
        port=default_db['PORT'],
        user=user,
        password=password,
        dbname=default_db['NAME'],
    )


def clean_tables(apps, schema_editor):
    """Some tables have data populated by data migrations.
    Replication expects that these tables to be empty.
    This operation will clean up such tables.
    """
    db_alias = schema_editor.connection.alias
    if db_alias != REPORTS_DB:
        return

    Donation = apps.get_model('donation', 'Donation')
    User = apps.get_model('user', 'User')
    POS = apps.get_model('pos', 'POS')
    PaymentType = apps.get_model('pos', 'PaymentType')

    # Don't know why by deletion via PaymentType.objects.using().delete()
    # didn't work as intended.
    with connections[db_alias].cursor() as cursor:
        cursor.execute(f'DELETE FROM {PaymentType._meta.db_table};')
        cursor.execute(
            'ALTER TABLE ONLY public.auth_user ' 'DROP CONSTRAINT auth_user_username_key;'
        )
        cursor.execute('ALTER TABLE ONLY public.auth_user ' 'DROP CONSTRAINT auth_user_email_key;')

    Donation.objects.using(db_alias).delete()
    User.objects.using(db_alias).delete()
    POS.objects.using(db_alias).delete()


class Migration(migrations.Migration):
    dependencies = [
        ('stats_and_reports', '0001_initial'),
    ]

    operations = [
        CreateExtensionDevDeploymentOnly('pglogical'),
        CreatePgLogicalNodeDevDeploymentOnly(
            node_name='subscriber',
            conn_details=get_subscriber_node_connection_details(),
        ),
        migrations.RunPython(clean_tables, migrations.RunPython.noop),
        CreateSubscription(
            subscription_name='reports_subscription',
            conn_details=get_subscription_connection_details(),
            # Replication set is being created in primary db in:
            #   webapps/database_publications/migrations/0001_initial.py
            replication_sets=['reports_replication_set'],
            # Data can't be synchronized during migrations as we can't be sure
            # that all tables have already been created. Data synchronization
            # must begin after all migrations are applied. We use post_migrate
            # signal for that.
            synchronize_data=False,
        ),
    ]
