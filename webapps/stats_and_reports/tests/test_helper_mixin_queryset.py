from model_bakery import baker
import pytest

from lib.test_utils import create_subbooking
from webapps.booking.models import SubBooking
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.booking.querysets import QuerySetHelpersMixin
from webapps.user.models import User


@pytest.mark.django_db
def test_clause_to_obtain_customer_data_through_appointment():
    user = baker.make(
        User,
        cell_phone='+***********',
        first_name='<PERSON><PERSON>',
        last_name='<PERSON>ie',
    )
    bci = baker.make(
        BusinessCustomerInfo,
        user=user,
        cell_phone=None,
    )
    subbooking, _, _ = create_subbooking(
        booking_kws=dict(booked_for=bci),
    )
    user.refresh_from_db()
    assert user.cell_phone, user.cell_phone

    querying_obj = QuerySetHelpersMixin()
    results = SubBooking.objects.filter(id=subbooking.id).annotate(
        phone=querying_obj.clause_to_obtain_phone_through_appointment(
            appointment_prefix='appointment'
        ),
        customer_name=querying_obj.clause_to_obtain_customer_name_through_appointment(
            appointment_prefix='appointment'
        ),
    )
    result = results[0]
    assert result.phone == user.cell_phone
    assert result.customer_name == f'{user.first_name} {user.last_name}'
