import datetime

import pytest
import pytz
from freezegun import freeze_time

from webapps.stats_and_reports.reports.revenue.packages_redemptions import (
    PackagesRedemptionsSection,
    PackagesTable,
)
from webapps.stats_and_reports.reports.tests.base import PrepareVouchers


@pytest.mark.django_db
@freeze_time(datetime.datetime(2020, 12, 27, 10, 15, tzinfo=pytz.UTC))
class TestPackagesRedemptions(PrepareVouchers):
    def setUp(self):
        super().setUp()
        self.section = PackagesRedemptionsSection(self.time_scope)

    def test_coerce_data_to_table_sales_log(self):
        result = self.section.get_data(False)

        expected_rows_list = [
            PackagesTable.Row(
                checkout_date=datetime.datetime(2020, 12, 7, 10, 15, tzinfo=pytz.UTC),
                buyer='- -',
                client='name_customer_1 surname_customer_1',
                package_name='PACKAGE 1',
                package_number='123',
                redeem_number=0,
                _business=self.business,
                _language='en',
            ),
            PackagesTable.Row(
                checkout_date=datetime.datetime(2020, 12, 7, 10, 16, tzinfo=pytz.UTC),
                buyer='- -',
                client='name_customer_2 surname_customer_2',
                package_name='PACKAGE 1',
                package_number='12',
                redeem_number=0,
                _business=self.business,
                _language='en',
            ),
        ]
        total = PackagesTable.TotalRow(redeem_number=0)

        self.assertCountEqual(result.rows, expected_rows_list)
        self.assertEqual(result.total_row, total)
