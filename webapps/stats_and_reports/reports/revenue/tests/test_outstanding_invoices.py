import datetime
from decimal import Decimal
from unittest.mock import PropertyMock, patch

import pytest
import pytz
from freezegun import freeze_time
from model_bakery import baker

from webapps.invoicing.models import CustomerInvoice
from webapps.stats_and_reports.reports.revenue.outstanding_invoices import (
    OutstandingInvoicesSection,
    OutstandingInvoicesTable,
)
from webapps.stats_and_reports.reports.tests.base import PrepareData


@pytest.mark.django_db
@freeze_time(datetime.datetime(2020, 12, 31, 10, 15, tzinfo=pytz.UTC))
@patch.object(CustomerInvoice, 'total_amount_to_pay', PropertyMock(return_value=Decimal(500)))
class TestsOutstandingInvoicesSection(PrepareData):
    def setUp(self):
        super().setUp()
        self.invoice_1 = baker.make(
            CustomerInvoice,
            number='FV/123/df',
            buyer_name='Hgz',
            payment_term=datetime.date(2020, 12, 16),
            payment_method='Cash',
            is_paid=False,
            business=self.business,
        )

        self.invoice_2 = baker.make(
            CustomerInvoice,
            number='FV/456/df',
            buyer_name='Hg',
            payment_term=datetime.date(2020, 12, 17),
            payment_method='Cash',
            is_paid=False,
            business=self.business,
        )

        self.invoice_3 = baker.make(
            CustomerInvoice,
            number='FV/678/df',
            buyer_name='Hgz',
            payment_term=datetime.date(2020, 12, 16),
            payment_method='Cash',
            is_paid=False,
            business=self.business,
            created=datetime.datetime(2020, 12, 25, 10, 15, tzinfo=pytz.UTC),
        )
        self.invoice_4 = baker.make(
            CustomerInvoice,
            corrected_invoice=self.invoice_3,
            number='FV/890/df',
            buyer_name='Hg',
            payment_term=datetime.date(2020, 12, 17),
            payment_method='Cash',
            is_paid=False,
            business=self.business,
            created=datetime.datetime(2020, 12, 26, 10, 15, tzinfo=pytz.UTC),
        )

        self.section = OutstandingInvoicesSection(self.time_scope)

    def test_coerce_data_to_table_sales_log(self):
        result = self.section.get_data(False)

        expected_rows_list = [
            OutstandingInvoicesTable.Row(
                invoice_number='FV/890/df',
                client='Hg',
                issue_date=datetime.date(2020, 12, 31),
                due_date=datetime.date(2020, 12, 17),
                overdue=14,
                total_revenue=Decimal('500'),
                payment_method='Cash',
                status='Unpaid',
                _business=self.business,
                _language='en',
            ),
            OutstandingInvoicesTable.Row(
                invoice_number='FV/456/df',
                client='Hg',
                issue_date=datetime.date(2020, 12, 31),
                due_date=datetime.date(2020, 12, 17),
                overdue=14,
                total_revenue=Decimal('500'),
                payment_method='Cash',
                status='Unpaid',
                _business=self.business,
                _language='en',
            ),
            OutstandingInvoicesTable.Row(
                invoice_number='FV/123/df',
                client='Hgz',
                issue_date=datetime.date(2020, 12, 31),
                due_date=datetime.date(2020, 12, 16),
                overdue=15,
                total_revenue=Decimal('500'),
                payment_method='Cash',
                status='Unpaid',
                _business=self.business,
                _language='en',
            ),
        ]

        expected_total_row = OutstandingInvoicesTable.TotalRow(
            total_revenue=Decimal('1500'),
        )

        self.assertCountEqual(result.rows, expected_rows_list)
        self.assertEqual(result.total_row, expected_total_row)
