import datetime
from decimal import Decimal

import pytest
import pytz
from freezegun import freeze_time
from model_bakery import baker
from webapps.pos.enums import receipt_status

from webapps.business.enums import PriceType
from webapps.business.models import ServiceAddOn, ServiceAddOnUse
from webapps.pos.models import Transaction, Receipt, TransactionRow
from webapps.stats_and_reports.models import TransactionReplica, TransactionRowReplica
from webapps.stats_and_reports.reports import TotalRevenueSection
from webapps.stats_and_reports.reports.revenue.total_revenue import (
    TotalRevenueTable,
)
from webapps.stats_and_reports.reports.tests.base import PrepareData


@pytest.mark.django_db
@freeze_time(datetime.datetime(2020, 12, 31, 10, 15, tzinfo=pytz.UTC))
class TestTotalRevenue(PrepareData):
    def setUp(self):
        super().setUp()
        self.section = TotalRevenueSection(self.time_scope)

        addon_use_1 = baker.make(
            ServiceAddOnUse,
            business=self.business,
            subbooking=self.bookings[0],
            service_addon=baker.make(
                ServiceAddOn,
                name='some addon 1',
                business=self.business,
                max_allowed_quantity=5,
                price_type=PriceType.FIXED,
                services=[self.bookings[0].service_variant.service],
            ),
            name='some addon 1',
            price=12.50,
            quantity=1,
            services_ids=[self.bookings[0].service_variant.service.id],
        )
        txn = baker.make(
            Transaction,
            appointment_id=self.bookings[0].appointment.id,
            customer_card_id=self.bookings[0].appointment.booked_for_id,
            pos=self.pos,
            settled=False,
            ready_for_settle=True,
            total=25,
            discount_amount=0,
            transaction_type=TransactionReplica.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = baker.make(
            Receipt,
            payment_type=self.prepayment,
            status_code=receipt_status.PAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.booking = self.bookings[0]
        txn.latest_receipt = receipt
        txn.save()
        baker.make(
            TransactionRow,
            type=TransactionRowReplica.TRANSACTION_ROW_TYPE__ADDON,
            transaction=txn,
            subbooking=self.bookings[0],
            service_variant=self.service_variant_1,
            addon_use=addon_use_1,
            net_total=12.50,
            item_price=12.50,
            real_discount_amount=0,
            discounted_item_price=12.50,
            tax_amount=0,
            tax_rate=0,
            gross_total=12.50,
        )
        baker.make(
            TransactionRow,
            type=TransactionRowReplica.TRANSACTION_ROW_TYPE__ADDON,
            transaction=txn,
            subbooking=None,
            service_variant=None,
            addon_use=addon_use_1,
            net_total=12.50,
            item_price=12.50,
            real_discount_amount=0,
            discounted_item_price=12.50,
            tax_amount=0,
            tax_rate=0,
            gross_total=12.50,
        )

    def test_coerce_data_to_table_sales_log(self):
        result = self.section.get_data(False)
        _key_choices = ['S', 'P', 'VP', 'VM', 'VG', 'D', 'T']

        expected_rows_list = [
            TotalRevenueTable.Row(
                item_type='Services',
                percentage=Decimal('41.66'),
                revenue=Decimal('832.33'),
                _meta={
                    'key': 'S',
                    '_key_choices': _key_choices,
                },
            ),
            TotalRevenueTable.Row(
                item_type='Products',
                percentage=Decimal('33.55'),
                revenue=Decimal('670.33'),
                _meta={
                    'key': 'P',
                    '_key_choices': _key_choices,
                },
            ),
            TotalRevenueTable.Row(
                item_type='Gift Cards',
                percentage=Decimal('21.89'),
                revenue=Decimal('437.33'),
                _meta={
                    'key': 'VG',
                    '_key_choices': _key_choices,
                },
            ),
            TotalRevenueTable.Row(
                item_type='Cancellation Fees',
                percentage=Decimal('2.90'),
                revenue=Decimal('58.00'),
                _meta={
                    'key': 'D',
                    '_key_choices': _key_choices,
                },
            ),
            TotalRevenueTable.Row(
                item_type='Not typed',
                percentage=Decimal('0.00'),
                revenue=Decimal('0.00'),
                _meta={
                    'key': 'Not typed',
                    '_key_choices': _key_choices,
                },
            ),
            TotalRevenueTable.Row(
                item_type='Packages',
                percentage=Decimal('0'),
                revenue=Decimal('0'),
                _meta={
                    'key': 'VP',
                    '_key_choices': _key_choices,
                },
            ),
            TotalRevenueTable.Row(
                item_type='Memberships',
                percentage=Decimal('0'),
                revenue=Decimal('0'),
                _meta={
                    'key': 'VM',
                    '_key_choices': _key_choices,
                },
            ),
            TotalRevenueTable.Row(
                item_type='Travel Fees',
                percentage=Decimal('0'),
                revenue=Decimal('0'),
                _meta={
                    'key': 'T',
                    '_key_choices': _key_choices,
                },
            ),
        ]
        self.assertCountEqual(result.rows, expected_rows_list)
