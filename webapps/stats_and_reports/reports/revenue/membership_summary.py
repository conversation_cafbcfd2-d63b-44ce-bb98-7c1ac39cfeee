# pylint: disable=duplicate-code

import datetime
import typing
from abc import ABC, abstractmethod
from dataclasses import dataclass, InitVar
from decimal import Decimal

from django.db.models import DurationField, F, QuerySet
from django.db.models.functions import Cast
from django.utils.translation import gettext, gettext_lazy as _
from openpyxl.styles import Alignment
from openpyxl.worksheet.worksheet import Worksheet
from rest_framework import serializers

from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature import FrenchCertificationVoucherSummaryReportFlag
from lib.french_certification.utils import french_certification_enabled
from lib.tools import tznow
from webapps.pos.enums import receipt_status
from webapps.stats_and_reports import models
from webapps.stats_and_reports.models import (
    BusinessCustomerInfoReplica,
    TransactionRowReplica,
)
from webapps.stats_and_reports.report_keys import ReportKeys
from webapps.stats_and_reports.reports import fields as report_fields
from webapps.stats_and_reports.reports.adapters import has_active_memberships_adapter
from webapps.stats_and_reports.reports.base import (
    BaseReport,
    ReportSection,
    ReportTable,
    ReportTableRow,
    SortableField,
)
from webapps.stats_and_reports.reports.mixins import PeriodDatesXlsxMixin, RedeemCountMixin
from webapps.stats_and_reports.reports.spreadsheet_utils import (
    format_alignment,
    format_number,
    NumberFormat,
    ws_range,
)
from webapps.stats_and_reports.reports.time_data import TimeDataScope
from webapps.voucher.enums import VoucherStatus, VoucherType

CLOSE_TO_EXPIRATION_DAYS = 10


class MembershipsTableRowSerializer(serializers.Serializer):
    buyer = serializers.CharField()
    client = serializers.CharField()
    membership_name = serializers.CharField()
    membership_number = serializers.CharField()
    price = report_fields.ReportsCurrencyField()
    valid_from = report_fields.ReportsDateOnlyField()
    valid_to = report_fields.ReportsDateOnlyField()
    redeem_number = serializers.IntegerField()
    days_to_expire = serializers.IntegerField()


class MembershipsTable(ReportTable):
    header = (
        _('Buyer'),
        _('Client name'),
        _('Membership name'),
        _('Membership number'),
        _('Price'),
        _('Valid from'),
        _('Valid to'),
        _('No of redeem'),
        _('Days to expire'),
    )

    @dataclass
    class Row(ReportTableRow):  # pylint: disable=too-many-instance-attributes
        buyer: str
        client: str
        membership_name: str
        membership_number: str
        price: Decimal
        valid_from: datetime.date
        valid_to: datetime.date
        redeem_number: int
        days_to_expire: typing.Optional[int]

        # Business and user language is required for formatting datetime
        _business: InitVar[models.Business]
        _language: InitVar[str]

        def __post_init__(self, _business, _language):
            self.business = _business
            self.language = _language

        def get_serializer_class(self):
            return MembershipsTableRowSerializer

        def get_serializer(self):
            serializer = super().get_serializer()
            serializer.context['business'] = self.business
            serializer.context['language'] = self.language
            return serializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        format_alignment(
            ws_range(ws, 'B:E'),
            Alignment(horizontal='left', wrap_text=True, vertical='center'),
        )
        format_alignment(
            ws_range(ws, 'F:J'),
            Alignment(horizontal='right', wrap_text=True, vertical='center'),
        )

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_alignment(ws_range(ws, 'B:D'), Alignment(horizontal='left'))
        format_number(ws_range(ws, 'E:E'), NumberFormat.TEXT)  # voucher number
        format_number(ws_range(ws, 'F:F'), NumberFormat.CURRENCY)
        format_alignment(ws_range(ws, 'G:H'), Alignment(horizontal='right'))
        format_number(ws_range(ws, 'I:J'), NumberFormat.GENERAL)


class BaseMembershipsSummarySection(
    RedeemCountMixin,
    ReportSection,
    ABC,
):
    is_paginated = True
    sortable_fields = (SortableField('membership_name', db_column='voucher_name'),)

    def get_base_queryset(self) -> QuerySet:
        success_statuses = receipt_status.SUCCESS_STATUSES_WITH_PREPAYMENT
        qs = TransactionRowReplica.objects.filter(
            transaction__pos__business_id=self.scope.business.id,
            transaction__children__isnull=True,
            transaction__latest_receipt__status_code__in=success_statuses,
            type=models.TransactionRowReplica.TRANSACTION_ROW_TYPE__VOUCHER,
            voucher__pos__business_id=self.scope.business.id,
            voucher__voucher_template__type=VoucherType.MEMBERSHIP,
        )
        return self.filter_voucher_status(qs)

    @abstractmethod
    def filter_voucher_status(self, queryset: QuerySet) -> QuerySet: ...

    def get_queryset(self) -> QuerySet:
        tz = self.scope.business.get_timezone()
        return (
            self.get_base_queryset()
            .annotate(
                buyer_bci=F('transaction__customer_card'),
                voucher_bci=F('voucher__customer'),
                voucher_name=F('voucher__voucher_template__name'),
                voucher_number=F('voucher__code'),
                voucher_price=F('voucher__voucher_template__item_price'),
                days_to_expire=Cast(
                    F('voucher__valid_till') - tznow(tz=tz),
                    output_field=DurationField(),
                ),
            )
            .values(
                'voucher_id',
                'voucher__status',
                'voucher__valid_from',
                'voucher__valid_till',
                'buyer_bci',
                'voucher_bci',
                'voucher_name',
                'voucher_number',
                'voucher_price',
                'days_to_expire',
            )
        )

    def get_bci_names(self, bci_ids: typing.List[int]) -> typing.Dict[int, str]:
        qs = (
            BusinessCustomerInfoReplica.objects.filter(
                business_id=self.scope.business.id,
                id__in=bci_ids,
            )
            .annotate_full_name()
            .values('id', 'full_name')
        )
        return {row['id']: row['full_name'] for row in qs}

    def coerce_data_to_table(self, results) -> MembershipsTable:
        voucher_ids = [transaction['voucher_id'] for transaction in results]
        redeem_count = self.redeem_count(voucher_ids)

        bci_ids = []
        for transaction in results:
            if transaction['buyer_bci']:
                bci_ids.append(transaction['buyer_bci'])
            if transaction['voucher_bci']:
                bci_ids.append(transaction['voucher_bci'])
        bci_names = self.get_bci_names(list(set(bci_ids)))

        rows = []
        for row in results:
            days_to_expire = row.get('days_to_expire').days
            if row['voucher__status'] == VoucherStatus.EXPIRED:
                days_to_expire = None

            rows.append(
                MembershipsTable.Row(
                    buyer=bci_names.get(row['buyer_bci'], '-'),
                    client=bci_names.get(row['voucher_bci'], '-'),
                    membership_name=row['voucher_name'],
                    membership_number=row['voucher_number'],
                    price=row['voucher_price'],
                    valid_from=row['voucher__valid_from'],
                    valid_to=row['voucher__valid_till'],
                    redeem_number=redeem_count.get(row['voucher_id'], 0),
                    days_to_expire=days_to_expire,
                    _business=self.scope.business,
                    _language=self.scope.language,
                )
            )

        return MembershipsTable(
            rows=rows,
            sortable_fields=self.sortable_fields,
            active_sorting=self.sorting_to_apply,
        )


class MembershipsCloseToExpirationSection(BaseMembershipsSummarySection):
    key = 'memberships_expired_close_section'
    title = _('Due to expire')
    description = _('Members whose membership is close to expiring')

    def filter_voucher_status(self, queryset: QuerySet) -> QuerySet:
        return queryset.filter(
            voucher__status=VoucherStatus.ACTIVE,
            voucher__valid_till__lt=(tznow() + datetime.timedelta(days=CLOSE_TO_EXPIRATION_DAYS)),
            voucher__valid_till__gt=tznow(),
        )


class MembershipsActiveSection(BaseMembershipsSummarySection):
    key = 'memberships_active_section'
    title = _('Active')
    description = _('Detailed list of active Memberships')

    def filter_voucher_status(self, queryset: QuerySet) -> QuerySet:
        return queryset.filter(
            voucher__status=VoucherStatus.ACTIVE,
            voucher__valid_till__gte=(tznow() + datetime.timedelta(days=CLOSE_TO_EXPIRATION_DAYS)),
        )


class MembershipsExpiredSection(BaseMembershipsSummarySection):
    key = 'memberships_expired_section'
    title = _('Expired')
    description = _('Detailed list of expired Memberships')

    def filter_voucher_status(self, queryset: QuerySet) -> QuerySet:
        return queryset.filter(
            voucher__status=VoucherStatus.EXPIRED,
        )


class MembershipsSummary(PeriodDatesXlsxMixin, BaseReport):
    key = ReportKeys.MembershipsSummary
    is_date_filtered = False  # current moment

    @classmethod
    def is_available(cls, scope: TimeDataScope) -> bool:
        if not super().is_available(scope):
            return False

        if french_certification_enabled(scope.business.id):
            userdata = UserData(subject_key=scope.business.id)
            ff = FrenchCertificationVoucherSummaryReportFlag(user=userdata)
            return bool(ff) and has_active_memberships_adapter(scope.business)

        return True

    @staticmethod
    def get_title() -> str:
        return gettext('Memberships summary')

    @property
    def subtitle(self) -> str:
        return gettext('List of Membership details based on status')

    def __init__(self, *args) -> None:
        super().__init__(*args)
        self._add_section(MembershipsCloseToExpirationSection)
        self._add_section(MembershipsActiveSection)
        self._add_section(MembershipsExpiredSection)

    def get_column_widths(self) -> typing.List[int]:
        # fmt: off
        return [
            5,
            18, 18, 18, 18,
            12, 12, 12, 12, 12,
        ]
