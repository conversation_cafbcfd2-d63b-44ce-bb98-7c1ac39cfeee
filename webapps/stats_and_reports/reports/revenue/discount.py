import datetime
from collections import OrderedDict
from dataclasses import InitVar, dataclass
from decimal import Decimal

import typing
from django.db.models import (
    F,
    QuerySet,
    Sum,
)
from django.db.models.functions import Coalesce
from django.utils.translation import gettext, gettext_lazy as _
from openpyxl.styles import Alignment
from openpyxl.worksheet.worksheet import Worksheet
from rest_framework import serializers

from webapps.pos.enums import receipt_status
from webapps.stats_and_reports import models
from webapps.stats_and_reports.report_keys import ReportKeys
from webapps.stats_and_reports.reports import fields as report_fields
from webapps.stats_and_reports.reports.base import (
    BaseReport,
    ReportSection,
    ReportTable,
    ReportTableRow,
    SortableField,
    Sorting,
)
from webapps.stats_and_reports.reports.mixins import (
    SequenceRecordSectionMixin,
)
from webapps.stats_and_reports.reports.spreadsheet_utils import (
    NumberFormat,
    format_alignment,
    format_number,
    ws_range,
)


class DiscountTableTotalRowSerializer(serializers.Serializer):
    before_discount = report_fields.ReportsCurrencyField()
    discount_on_items = report_fields.ReportsCurrencyField()
    discount_on_total = report_fields.ReportsCurrencyField()
    after_discount = report_fields.ReportsCurrencyField()

    def to_representation(self, instance):
        row_dict = super().to_representation(instance)
        result = OrderedDict.fromkeys('abc')  # 3 empty cols
        result.update(row_dict)
        return result


class DiscountTableRowSerializer(serializers.Serializer):
    checkout_date = report_fields.ReportsDatetimeField()
    client = serializers.CharField()
    transaction_id = serializers.CharField()
    before_discount = report_fields.ReportsCurrencyField()
    discount_on_items = report_fields.ReportsCurrencyField()
    discount_on_total = report_fields.ReportsCurrencyField()
    after_discount = report_fields.ReportsCurrencyField()
    cashier = serializers.CharField()


class DiscountTable(ReportTable):
    header = (
        _('Checkout date'),
        _('Client'),
        _('Transaction ID'),
        _('Before discount'),
        _('Discount on items'),
        _('Discount on total'),
        _('After discount'),
        _('Cashier'),
    )

    @dataclass
    class Row(ReportTableRow):  # pylint: disable=too-many-instance-attributes
        checkout_date: datetime.datetime
        client: str
        transaction_id: str
        before_discount: Decimal
        discount_on_items: Decimal
        discount_on_total: Decimal
        after_discount: Decimal
        cashier: str

        # Business and user language is required for formatting datetime
        _business: InitVar[models.Business]
        _language: InitVar[str]

        def __post_init__(self, _business, _language):
            self.business = _business
            self.language = _language

        def get_serializer_class(self):
            return DiscountTableRowSerializer

        def get_serializer(self):
            serializer = super().get_serializer()
            serializer.context['business'] = self.business
            serializer.context['language'] = self.language
            return serializer

    @dataclass
    class TotalRow(ReportTableRow):
        before_discount: Decimal
        discount_on_items: Decimal
        discount_on_total: Decimal
        after_discount: Decimal

        def get_serializer_class(self):
            return DiscountTableTotalRowSerializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        format_alignment(
            ws_range(ws, 'B:D'),
            Alignment(horizontal='left', wrap_text=True, vertical='center'),
        )
        format_alignment(
            ws_range(ws, 'E:H'),
            Alignment(horizontal='right', wrap_text=True, vertical='center'),
        )
        format_alignment(
            ws_range(ws, 'I:I'),
            Alignment(horizontal='left', wrap_text=True, vertical='center'),
        )

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_alignment(ws_range(ws, 'B:D'), Alignment(horizontal='left'))
        format_number(ws_range(ws, 'E:H'), NumberFormat.CURRENCY)
        format_alignment(ws_range(ws, 'I:I'), Alignment(horizontal='right'))

    def format_spreadsheet_table_total_row(self, ws: Worksheet):
        super().format_spreadsheet_table_total_row(ws)
        format_number(ws_range(ws, 'E:H'), NumberFormat.CURRENCY)


class DiscountSection(
    SequenceRecordSectionMixin,
    ReportSection,
):
    key = 'discount_section'
    is_paginated = True
    append_total_row = True
    sortable_fields = (SortableField('checkout_date'),)
    default_sorting = Sorting('checkout_date', Sorting.DESC)

    def get_queryset(self) -> QuerySet:
        filter_receipt_status = receipt_status.SUCCESS_STATUSES_WITH_PREPAYMENT
        return (
            models.TransactionRowReplica.objects.filter(
                # There can be transactions, that are not bound to Appointments:
                transaction__latest_receipt__status_code__in=filter_receipt_status,
                transaction__pos__business_id=self.scope.business.id,
                transaction__latest_receipt__created__gte=self.scope.date_from_datetime_utc,
                transaction__latest_receipt__created__lte=self.scope.date_till_by_span,
                real_discount_amount__gt=0,
            )
            .values('transaction__id')
            .annotate(
                checkout_date=F('transaction__latest_receipt__created'),
                before_discount=Sum(Coalesce(F('item_price'), Decimal(0))),
                discount_on_items=Sum(
                    Coalesce(F('item_price'), Decimal(0))
                    - Coalesce(F('discounted_item_price'), Decimal(0))
                ),
                cashier_name=F('transaction__operator__first_name'),
                cashier_surname=F('transaction__operator__last_name'),
                client_bci_first_name=F('transaction__customer_card__first_name'),
                client_bci_last_name=F('transaction__customer_card__last_name'),
                client_user_first_name=F('transaction__customer_card__user__first_name'),
                client_user_last_name=F('transaction__customer_card__user__last_name'),
            )
            .order_by('checkout_date')
        )

    @staticmethod
    def get_transaction_query(ids):
        return models.TransactionReplica.objects.filter(id__in=ids)

    def get_transaction_data(self, ids):
        transactions = self.get_transaction_query(ids).values(
            'id',
            'discount_amount',  # DISCOUNT ON TOTAL; discount on transaction
            'total',  # AFTER DISCOUNT;
        )
        return {transaction['id']: transaction for transaction in transactions}

    def get_transaction_total_row_data(self, ids):
        return self.get_transaction_query(ids).aggregate(
            total_discount_on_total=Sum('discount_amount'),
            total_after_discount=Sum('total'),
        )

    def coerce_data_to_table(self, results) -> DiscountTable:
        transaction_ids = [row['transaction__id'] for row in results]
        transaction_data = self.get_transaction_data(transaction_ids)
        assigned_numbers = self.get_sequence_record(transaction_ids)

        rows = []
        for row in results:
            if row.get('client_bci_first_name') or row.get('client_bci_last_name'):
                client = (
                    f"{row.get('client_bci_first_name', '')}"
                    f" {row.get('client_bci_last_name', '')}"
                )
            elif row.get('client_user_first_name') or row.get('client_user_last_name'):
                client = (
                    f"{row.get('client_user_first_name', '')}"
                    f" {row.get('client_user_last_name', '')}"
                )
            else:
                client = _('Walk-in')

            assigned_number = assigned_numbers.get(
                row['transaction__id'],
                str(row['transaction__id']),
            )
            cashier = (row.get('cashier_name') or '') + ' ' + (row.get('cashier_surname') or '')

            before_discount = row.get('before_discount')
            discount_on_items = row.get('discount_on_items')
            after_discount = transaction_data.get(row['transaction__id'])['total']
            discount_on_total = transaction_data.get(row['transaction__id'])['discount_amount']

            rows.append(
                DiscountTable.Row(
                    checkout_date=row.get('checkout_date'),
                    client=client,
                    transaction_id=assigned_number,
                    before_discount=before_discount,
                    discount_on_items=discount_on_items,
                    discount_on_total=discount_on_total,
                    after_discount=after_discount,
                    cashier=cashier,
                    _business=self.scope.business,
                    _language=self.scope.language,
                )
            )

        return DiscountTable(
            rows=rows,
            sortable_fields=self.sortable_fields,
            active_sorting=self.sorting_to_apply,
        )

    def calculate_total_row(self, queryset: QuerySet) -> ReportTableRow:
        aggregated_qs = queryset.aggregate(
            total_before_discount=Sum('before_discount'),
            total_discount_on_items=Sum('discount_on_items'),
        )

        transaction_ids = [row['transaction__id'] for row in queryset]
        transaction_data = self.get_transaction_total_row_data(transaction_ids)

        return DiscountTable.TotalRow(
            before_discount=aggregated_qs['total_before_discount'],
            discount_on_items=aggregated_qs['total_discount_on_items'],
            discount_on_total=transaction_data['total_discount_on_total'],
            after_discount=transaction_data['total_after_discount'],
        )


class DiscountsReport(BaseReport):
    key = ReportKeys.DiscountsReport

    @staticmethod
    def get_title() -> str:
        return gettext('Discounts')

    @property
    def subtitle(self) -> str:
        return gettext('List of applied discounts with associated details')

    def __init__(self, *args) -> None:
        super().__init__(*args)
        self._add_section(DiscountSection)

    def get_column_widths(self) -> typing.List[int]:
        return [
            5,
            18,
            18,
            18,
            12,
            12,
            12,
            12,
            12,
        ]
