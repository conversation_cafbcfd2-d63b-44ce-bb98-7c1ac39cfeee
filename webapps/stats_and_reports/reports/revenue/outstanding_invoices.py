import datetime
from collections import OrderedDict
from dataclasses import InitVar, dataclass
from decimal import Decimal

import typing

from django.db.models import F, QuerySet
from django.utils.translation import gettext, gettext_lazy as _
from openpyxl.styles import Alignment
from openpyxl.worksheet.worksheet import Worksheet
from rest_framework import serializers

from lib.tools import tznow

from webapps.stats_and_reports import models
from webapps.stats_and_reports.models import CustomerInvoiceReplica
from webapps.stats_and_reports.report_keys import ReportKeys
from webapps.stats_and_reports.reports import fields as report_fields
from webapps.stats_and_reports.reports.base import (
    BaseReport,
    ReportSection,
    ReportTable,
    ReportTableRow,
    SortableField,
    Sorting,
)
from webapps.stats_and_reports.reports.spreadsheet_utils import (
    NumberFormat,
    format_alignment,
    format_number,
    ws_range,
)


class OutstandingInvoicesTableTotalRowSerializer(serializers.Serializer):
    total_revenue = report_fields.ReportsCurrencyField()

    def to_representation(self, instance):
        row_dict = super().to_representation(instance)
        result = OrderedDict.fromkeys('abcde')  # 5 empty cols
        result.update(row_dict)
        return result


class OutstandingInvoicesTableRowSerializer(serializers.Serializer):
    invoice_number = serializers.CharField()
    client = serializers.CharField()
    issue_date = report_fields.ReportsDateOnlyField()
    due_date = report_fields.ReportsDateOnlyField()
    overdue = report_fields.ReportsDaysDurationCharField()
    total_revenue = report_fields.ReportsCurrencyField()
    payment_method = serializers.CharField()
    status = serializers.CharField()


class OutstandingInvoicesTable(ReportTable):
    header = (
        _('Invoice number'),
        _('Client'),
        _('Issue date'),
        _('Due date'),
        _('Overdue'),
        _('Total revenue'),
        _('Payment method'),
        _('Status'),
    )

    @dataclass
    class Row(ReportTableRow):  # pylint: disable=too-many-instance-attributes
        invoice_number: str
        client: str
        issue_date: datetime.date
        due_date: datetime.date
        overdue: int  # number of days overdue
        total_revenue: Decimal
        payment_method: str
        status: str

        # Business and user language is required for formatting datetime
        _business: InitVar[models.Business]
        _language: InitVar[str]

        def __post_init__(self, _business, _language):
            self.business = _business
            self.language = _language

        def get_serializer_class(self):
            return OutstandingInvoicesTableRowSerializer

        def get_serializer(self):
            serializer = super().get_serializer()
            serializer.context['business'] = self.business
            serializer.context['language'] = self.language
            return serializer

    @dataclass
    class TotalRow(ReportTableRow):
        total_revenue: Decimal

        def get_serializer_class(self):
            return OutstandingInvoicesTableTotalRowSerializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        format_alignment(
            ws_range(ws, 'B:D'), Alignment(horizontal='left', wrap_text=True, vertical='center')
        )
        format_alignment(
            ws_range(ws, 'E:I'), Alignment(horizontal='right', wrap_text=True, vertical='center')
        )

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_alignment(ws_range(ws, 'B:C'), Alignment(horizontal='left'))
        format_alignment(ws_range(ws, 'D:F'), Alignment(horizontal='right'))
        format_number(ws_range(ws, 'G:G'), NumberFormat.CURRENCY)
        format_alignment(ws_range(ws, 'H:I'), Alignment(horizontal='right'))

    def format_spreadsheet_table_total_row(self, ws: Worksheet):
        super().format_spreadsheet_table_total_row(ws)
        format_number(ws_range(ws, 'G:G'), NumberFormat.CURRENCY)


class OutstandingInvoicesSection(ReportSection):
    key = 'outstanding_invoices_section'
    is_paginated = True
    append_total_row = True
    sortable_fields = (SortableField('number'),)
    default_sorting = Sorting('number', Sorting.DESC)

    def get_queryset(self) -> QuerySet:
        # get all invoices without correction, and which are not as
        # corrected docs themselves
        return (
            CustomerInvoiceReplica.objects.filter(
                # There can be transactions, that are not bound to Appointments:
                business_id=self.scope.business.id,
                is_paid=False,
                payment_term__lt=tznow(),
                correcting_invoices_set__isnull=True,
            )
            .annotate(
                overdue=(tznow() - F('payment_term')),
            )
            .order_by('number')
        )

    def coerce_data_to_table(self, results) -> OutstandingInvoicesTable:
        rows = []

        for row in results:
            rows.append(
                OutstandingInvoicesTable.Row(
                    invoice_number=row.number,
                    client=row.buyer_name,
                    issue_date=row.issue_date,
                    due_date=row.payment_term,
                    overdue=row.overdue.days,
                    total_revenue=row.total_amount_to_pay,
                    payment_method=models.CustomerInvoiceReplica.CODES.get(
                        row.payment_method, row.payment_method
                    ),
                    status=gettext('Unpaid'),
                    _business=self.scope.business,
                    _language=self.scope.language,
                )
            )

        return OutstandingInvoicesTable(
            rows=rows,
            sortable_fields=self.sortable_fields,
            active_sorting=self.sorting_to_apply,
        )

    def calculate_total_row(self, queryset: QuerySet) -> ReportTableRow:
        return OutstandingInvoicesTable.TotalRow(
            total_revenue=sum(row.total_amount_to_pay for row in queryset),
        )


class OutstandingInvoices(BaseReport):
    key = ReportKeys.OutstandingInvoices
    is_date_filtered = False  # current moment

    def _set_period_dates_xlsx(self):
        self.business_xlsx_info.frozen_date_from = self.scope.date_today_datetime_utc
        self.business_xlsx_info.frozen_date_till = self.scope.date_today_datetime_utc

    @staticmethod
    def get_title() -> str:
        return gettext('Outstanding invoices')

    @property
    def subtitle(self) -> str:
        return gettext('List of invoices with unpaid status')

    def __init__(self, *args) -> None:
        super().__init__(*args)
        self._add_section(OutstandingInvoicesSection)

    def get_column_widths(self) -> typing.List[int]:
        return [
            5,
            18,
            18,
            18,
            18,
            12,
            12,
            12,
            12,
        ]
