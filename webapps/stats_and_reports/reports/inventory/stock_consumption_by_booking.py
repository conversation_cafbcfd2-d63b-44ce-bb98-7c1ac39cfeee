import datetime
import typing
from dataclasses import dataclass, InitVar
from decimal import Decimal

import pandas
from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import (
    Case,
    DecimalField,
    F,
    Q,
    QuerySet,
    Sum,
    Value,
    When,
)
from django.db.models.fields.json import KeyTextTransform
from django.db.models.functions import Cast, Coalesce
from django.utils.translation import gettext, gettext_lazy as _
from openpyxl.styles import Alignment
from openpyxl.worksheet.worksheet import Worksheet
from rest_framework import serializers

from webapps.business.models import Business
from webapps.pos.enums import receipt_status
from webapps.stats_and_reports.models import (
    CommissionReplica,
    TransactionRowReplica,
    WarehouseDocumentRowReplica,
)
from webapps.stats_and_reports.report_keys import ReportKeys
from webapps.stats_and_reports.reports import fields
from webapps.stats_and_reports.reports.base import (
    BaseReport,
    ReportSection,
    ReportTable,
    ReportTableRow,
)
from webapps.stats_and_reports.reports.spreadsheet_utils import (
    format_alignment,
    format_number,
    NumberFormat,
    ws_range,
)
from webapps.stats_and_reports.reports.utils import Round2
from webapps.warehouse.models import WarehouseDocumentType


# noinspection PyAbstractClass
class StockConsumptionByBookingTableRowSerializer(serializers.Serializer):
    booking_date = fields.ReportsDatetimeField()
    transaction_id = serializers.CharField()
    service_name = serializers.CharField()
    customer_name = serializers.CharField()
    staffer_name = serializers.CharField()
    service_value = fields.ReportsCurrencyField()

    revenue_net = fields.ReportsCurrencyField()
    tax_amount = fields.ReportsCurrencyField()
    total_revenue = fields.ReportsCurrencyField()

    product_category = serializers.CharField()
    product_name = serializers.CharField()
    quantity_pcs = serializers.IntegerField()
    quantity_volume_units = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
    )
    unit_symbol = serializers.CharField()
    value_of_products_used = fields.ReportsCurrencyField()

    staff_commission = fields.ReportsCurrencyField()
    total = fields.ReportsCurrencyField()


class StockConsumptionByBookingTable(ReportTable):
    header = (
        _('Booking date'),
        _('Transaction ID'),
        _('Service'),
        _('Client'),
        _('Staffer'),
        _('Service value'),
        _('Revenue net'),
        _('Tax'),
        _('Total revenue'),
        _('Product category'),
        _('Product'),
        _('Qty (pcs)'),
        _('Qty (units)'),
        '',  # unit symbol
        _('Value of products used'),
        _('Staff commission'),
        _('Total'),
    )

    @dataclass
    class Row(ReportTableRow):  # pylint: disable=too-many-instance-attributes
        booking_date: datetime.datetime
        transaction_id: str
        service_name: str
        customer_name: str
        staffer_name: str
        service_value: Decimal

        revenue_net: Decimal
        tax_amount: Decimal
        total_revenue: Decimal

        product_category: str
        product_name: str
        quantity_pcs: int
        quantity_volume_units: Decimal
        unit_symbol: str
        value_of_products_used: Decimal

        staff_commission: Decimal
        total: Decimal

        # Business and user language is required for formatting datetime
        _business: InitVar[Business]
        _language: InitVar[str]

        def __post_init__(self, _business, _language):
            self.business = _business
            self.language = _language

        def get_serializer(self):
            serializer = super().get_serializer()
            serializer.context['business'] = self.business
            serializer.context['language'] = self.language
            return serializer

        def get_serializer_class(self):
            return StockConsumptionByBookingTableRowSerializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_alignment(
            ws_range(ws, 'G:J'), Alignment(horizontal='right', wrap_text=True, vertical='center')
        )
        format_alignment(
            ws_range(ws, 'M:R'), Alignment(horizontal='right', wrap_text=True, vertical='center')
        )

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        format_number(ws_range(ws, 'G:J'), NumberFormat.CURRENCY)
        format_number(ws_range(ws, 'M:M'), NumberFormat.GENERAL)
        format_alignment(
            ws_range(ws, 'N:N'), Alignment(horizontal='right', wrap_text=True, vertical='center')
        )
        format_number(ws_range(ws, 'P:R'), NumberFormat.CURRENCY)


class InternalStockConsumptionByBookingSection(ReportSection):
    key = 'internal_stock_consumption_by_booking_section'
    is_paginated = True

    # This report can't apply any user provided sorting as it is bound to
    # predefined sorting by subbooking id

    def get_queryset(self) -> QuerySet:
        scope_datetime_from = self.scope.date_from_datetime_utc
        return (
            WarehouseDocumentRowReplica.objects.filter(
                document__warehouse__business_id=self.scope.business.id,
                document__type=WarehouseDocumentType.RW,
                document__subbooking__isnull=False,
                document__subbooking__booked_from__gte=scope_datetime_from,
                document__subbooking__booked_from__lte=self.scope.date_till_by_span,
            )
            .annotate_appointment_bci_full_name(
                target_field='customer_name',
                appointment_lookup_prefix='document__subbooking__appointment',
            )
            .annotate_subbooking_staffer_name(
                target_field='staffer_name',
                subbooking_lookup_prefix='document__subbooking',
            )
            .annotate(
                service_name=Coalesce(
                    F('document__subbooking__service_variant__service__name'),
                    F('document__subbooking__service_name'),
                ),
                total_packages=Case(
                    When(
                        is_full_package_expenditure=True,
                        then=F('quantity'),
                    ),
                    When(
                        Q(is_full_package_expenditure=False)
                        & Q(commodity__total_pack_capacity__gt=0),
                        then=F('quantity') / F('commodity__total_pack_capacity'),
                    ),
                    default=Value(Decimal(0)),
                    output_field=DecimalField(),
                ),
                value_of_used_commodity=Round2(
                    F('total_packages') * F('commodity__current_net_purchase_price')
                ),
                document__subbooking__booking_value=Cast(
                    KeyTextTransform(
                        'service_variant_price',
                        'document__subbooking__service_data_internal',
                    ),
                    DecimalField(max_digits=10, decimal_places=2),
                ),
            )
            .values(
                'document__subbooking_id',
                'document__subbooking__booked_from',
                'document__subbooking__booking_value',
                'service_name',
                'customer_name',
                'staffer_name',
                'commodity__name',
                'commodity__category__name',
                'commodity__volume_unit__symbol',
                'quantity',
                'is_full_package_expenditure',
                'value_of_used_commodity',
            )
            .order_by(
                # We MUST order by subbooking id because there may be multiple
                # commodity expenditures related to one subbooking and we want to
                # display them all next to each other.
                'document__subbooking_id',
                'id',
            )
        )

    @staticmethod
    def get_subbooking_revenue_stats(subbooking_ids: typing.List[int]) -> dict:
        qs = (
            TransactionRowReplica.objects.filter(
                subbooking_id__in=subbooking_ids,
                transaction__latest_receipt__status_code__in=[
                    receipt_status.PREPAYMENT_SUCCESS,
                    receipt_status.PAYMENT_SUCCESS,
                    receipt_status.DEPOSIT_CHARGE_SUCCESS,
                ],
            )
            .values(
                'subbooking_id',
            )
            .annotate(
                revenue_net=Coalesce(Sum('net_total'), Decimal(0)),
                tax_amount=Coalesce(Sum('tax_amount'), Decimal(0)),
                transaction_ids=ArrayAgg('transaction_id', distinct=True),
            )
            .order_by()
        )
        return {row['subbooking_id']: row for row in qs}

    @staticmethod
    def get_subbooking_commission_stats(
        subbooking_ids: typing.List[int],
    ) -> dict:
        qs = (
            CommissionReplica.objects.filter(
                row__subbooking_id__in=subbooking_ids,
            )
            .values(
                'row__subbooking_id',
            )
            .annotate(
                commission_amount=Coalesce(Sum('amount'), Decimal(0)),
            )
            .order_by()
        )
        return {row['row__subbooking_id']: row for row in qs}

    def coerce_data_to_table(self, results) -> StockConsumptionByBookingTable:
        subbooking_ids = list(set(x['document__subbooking_id'] for x in results))
        revenue_stats = self.get_subbooking_revenue_stats(subbooking_ids)
        commission_stats = self.get_subbooking_commission_stats(subbooking_ids)

        total_value_of_products_used = {}
        if results:
            results_df = pandas.DataFrame(results)
            total_value_of_products_used = (
                results_df.groupby(
                    [
                        'document__subbooking_id',
                    ]
                )
                .agg(
                    {
                        'value_of_used_commodity': 'sum',
                    }
                )
                .get(
                    'value_of_used_commodity',
                )
                .to_dict()
            )

        rows = []
        prev_subbooking_id = None
        for res_row in results:
            is_first_subbooking_in_series = res_row['document__subbooking_id'] != prev_subbooking_id
            service_value = res_row['document__subbooking__booking_value']
            value_of_used_commodity = res_row['value_of_used_commodity']
            quantity_pcs = 0
            quantity_volume_units = Decimal(0)
            if res_row['is_full_package_expenditure']:
                quantity_pcs = int(res_row['quantity'])
            else:
                quantity_volume_units = res_row['quantity']

            subbooking_revenue = revenue_stats.get(res_row['document__subbooking_id'], {})
            revenue_net = subbooking_revenue.get('revenue_net') or Decimal(0)
            tax_amount = subbooking_revenue.get('tax_amount') or Decimal(0)
            # Data structures allow that Appointment has multiple transactions
            # yet in this report we may display only one in column.
            transaction_ids = subbooking_revenue.get('transaction_ids')
            transaction_id = transaction_ids[0] if transaction_ids else '-'

            total_revenue = revenue_net + tax_amount
            staff_commission = commission_stats.get(res_row['document__subbooking_id'], {}).get(
                'commission_amount'
            ) or Decimal(0)
            total_value_of_all_products_used = total_value_of_products_used.get(
                res_row['document__subbooking_id']
            ) or Decimal(0)
            total = total_revenue - staff_commission - total_value_of_all_products_used

            # This report has strange structure. If few rows in a row are
            # related to the same SubBooking in first row we display all columns
            # but in latter we hide data from few cols. That's very specific
            # the only way to solve this was to make this ugly condition below.
            if is_first_subbooking_in_series:
                row = StockConsumptionByBookingTable.Row(
                    booking_date=res_row['document__subbooking__booked_from'],
                    transaction_id=transaction_id,
                    service_name=res_row['service_name'],
                    customer_name=res_row['customer_name'],
                    staffer_name=res_row['staffer_name'] or '-',
                    service_value=service_value,
                    revenue_net=revenue_net,
                    tax_amount=tax_amount,
                    total_revenue=total_revenue,
                    product_category=res_row['commodity__category__name'],
                    product_name=res_row['commodity__name'],
                    quantity_pcs=quantity_pcs,
                    quantity_volume_units=quantity_volume_units,
                    unit_symbol=res_row['commodity__volume_unit__symbol'],
                    value_of_products_used=value_of_used_commodity,
                    staff_commission=staff_commission,
                    total=total,
                    _business=self.scope.business,
                    _language=self.scope.language,
                )
            else:
                # noinspection PyTypeChecker
                row = StockConsumptionByBookingTable.Row(
                    booking_date=None,
                    transaction_id='',
                    service_name='',
                    customer_name='',
                    staffer_name='',
                    service_value=None,
                    revenue_net=None,
                    tax_amount=None,
                    total_revenue=None,
                    product_category=res_row['commodity__category__name'],
                    product_name=res_row['commodity__name'],
                    quantity_pcs=quantity_pcs,
                    quantity_volume_units=quantity_volume_units,
                    unit_symbol=res_row['commodity__volume_unit__symbol'],
                    value_of_products_used=value_of_used_commodity,
                    staff_commission=None,
                    total=None,
                    _business=self.scope.business,
                    _language=self.scope.language,
                )

            rows.append(row)
            prev_subbooking_id = res_row['document__subbooking_id']

        return StockConsumptionByBookingTable(rows=rows)


class InternalStockConsumptionByBooking(BaseReport):
    key = ReportKeys.InternalStockConsumptionByBooking

    @staticmethod
    def get_title() -> str:
        return gettext('Internal stock consumption by booking')

    @property
    def subtitle(self) -> str:
        return gettext('List of internal stock consumption with details per each booking')

    def __init__(self, *args) -> None:
        super().__init__(*args)
        self._add_section(InternalStockConsumptionByBookingSection)

    def get_column_widths(self) -> typing.List[int]:
        return [
            5,
            16,
            16,
            16,
            16,
            16,  # date, trans, service, client, staffer
            12,
            12,
            12,
            12,  # value, rev net, tax, total rev
            16,
            16,  # product cat, product name
            12,
            12,
            8,  # used amount
            12,
            12,
            12,  # used value, commission, total
        ]
