import datetime
from typing import Tuple

Duration = float
TimeRange = Tuple[datetime.time]
StampRange = Tuple[float, float]
TimeRanges = list[TimeRange]
StampRanges = list[StampRange]

EMPLOYEE = 'employee'
POSITION = 'position'
ACCESS_LEVEL = 'access_level'
SCHEDULED = 'scheduled'
WORK = 'work'
WORK_PERCENTAGE = 'work_percentage'
OFFS_TOTAL = 'offs_total'
OFFS_APPROVED = 'offs_approved'
OFFS_NOT_APPROVED = 'offs_not_approved'
BREAKS = 'breaks'
BLOCKS = 'blocks'
ON_SERVICE = 'on_service'

PREFIX = 'time_off_'
TOTAL = 'total'

DELTA1 = datetime.timedelta(days=1)
