import datetime
from decimal import Decimal

import pytest
import pytz
from freezegun import freeze_time

from webapps.stats_and_reports import models
from webapps.stats_and_reports.reports.sales_reports.sales_by_memberships import (
    MembershipsSalesLogSection,
    MembershipsSalesLogTable,
    SalesByMembershipsSection,
    SalesByMembershipsTable,
)
from webapps.stats_and_reports.reports.tests.base import PrepareVouchers


@pytest.mark.django_db
@freeze_time(datetime.datetime(2020, 12, 31, 10, 15, tzinfo=pytz.UTC))
class TestSalesMemberships(PrepareVouchers):
    def setUp(self):
        super().setUp()
        self.section = SalesByMembershipsSection(self.time_scope)

    def test_coerce_data_to_table(self):
        result = self.section.get_data(False)

        expected_rows_list = [
            SalesByMembershipsTable.Row(
                'MEMBERSHIP 1',
                Decimal('25.00'),
                models.VoucherTemplateReplica.VALID_TILL_TO_STRING['days_30'],
                2,
                <PERSON><PERSON><PERSON>('20.00'),
                <PERSON><PERSON><PERSON>('6.00'),
                <PERSON><PERSON><PERSON>('2.00'),
                <PERSON><PERSON><PERSON>('26.00'),
                _business=self.business,
                _language='en',
            ),
        ]

        expected_row_total = SalesByMembershipsTable.TotalRow(
            Decimal('20.00'),
            Decimal('6.00'),
            Decimal('2.00'),
            Decimal('26.00'),
        )

        self.assertCountEqual(result.rows, expected_rows_list)
        self.assertEqual(result.total_row, expected_row_total)


@pytest.mark.django_db
class TestSalesMembershipsLog(PrepareVouchers):
    def setUp(self):
        super().setUp()
        self.section = MembershipsSalesLogSection(self.time_scope)

    def test_coerce_data_to_table(self):
        result = self.section.get_data(False)

        expected_rows_list = [
            MembershipsSalesLogTable.Row(
                datetime.datetime(2020, 12, 7, 10, 15, tzinfo=pytz.UTC),
                'txn_membership_1/1',
                'Walk-in',
                'name_customer_1 surname_customer_1',
                '12345',
                datetime.date(2020, 12, 1),
                datetime.date(2020, 12, 31),
                Decimal('10.00'),
                Decimal('3.00'),
                Decimal('1.00'),
                Decimal('13.00'),
                'Deposit',
                _business=self.business,
                _language='en',
            ),
            MembershipsSalesLogTable.Row(
                datetime.datetime(2020, 12, 7, 10, 16, tzinfo=pytz.UTC),
                'txn_membership_2/2',
                'Walk-in',
                'name_customer_2 surname_customer_2',
                '1234',
                datetime.date(2020, 12, 1),
                datetime.date(2020, 12, 31),
                Decimal('10.00'),
                Decimal('3.00'),
                Decimal('1.00'),
                Decimal('13.00'),
                'Deposit',
                _business=self.business,
                _language='en',
            ),
        ]

        expected_row_total = MembershipsSalesLogTable.TotalRow(
            Decimal('20.00'),
            Decimal('6.00'),
            Decimal('2.00'),
            Decimal('26.00'),
        )

        self.assertCountEqual(result.rows, expected_rows_list)
        self.assertEqual(result.total_row, expected_row_total)
