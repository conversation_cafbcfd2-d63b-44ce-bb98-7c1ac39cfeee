import typing
from dataclasses import dataclass

from django.utils.translation import gettext, gettext_lazy as _
from rest_framework import serializers

from webapps.stats_and_reports.reports import fields as report_fields
from webapps.stats_and_reports.reports.base import (
    ReportSection,
    ReportTable,
    ReportTableRow,
)
from webapps.stats_and_reports.reports.common_stats import (
    ReturningAndNewClients,
)


class ClientsByTypesTableRowSerializer(serializers.Serializer):
    item_type = serializers.CharField()
    percentage = report_fields.ReportsPercentageField()
    count = serializers.IntegerField()
    _meta = serializers.DictField()


class ClientsByTypesTable(ReportTable):
    header = (_('Item Type'), '%', _('Clients'))

    @dataclass
    class Row(ReportTableRow):
        item_type: str
        percentage: int
        count: int
        _meta: typing.Dict

        def get_serializer_class(self):
            return ClientsByTypesTableRowSerializer


class ClientsByTypesSection(ReportSection):
    # TODO:
    """This "chart/table" is different from mockup. As we didn't get answers
    for the next questions:
    1. If it's really `Total Revenue by payment type` please clarify how it
       should be.
    2. If it's about payment - how can `slipping away` clients
       make payments for time-period ? As example: in current month we have got
       800$ from New and 300$ from Returning, but `slipping away` are clients
       who don't have bookings and payments at all (for chosen period)
    3. what are time-periods should be there ? - it's not possible to count
       `slipping away` clients for periods less one month

    So, this chart, now, shows us only number/percentage of clients
    for chosen period
    """
    key = 'clients_by_type'
    title = _('Total revenue by type')
    description = ''

    def get_queryset(self):
        return ReturningAndNewClients.get_queryset(scope=self.scope)

    def coerce_data_to_table(self, results) -> ClientsByTypesTable:
        stats = {row['client_status']: row['count'] for row in results}

        new_count = stats.get(ReturningAndNewClients.NEW, 0)
        returning_count = stats.get(ReturningAndNewClients.RETURNING, 0)
        total_count = new_count + returning_count

        if new_count != 0:
            new_percentage = round(new_count * 100 / total_count)
            returning_percentage = 100 - new_percentage
        elif returning_count != 0:
            returning_percentage = round(returning_count * 100 / total_count)
            new_percentage = 100 - returning_percentage
        else:
            new_percentage = 0
            returning_percentage = 0

        key_choices = [
            ReturningAndNewClients.NEW,
            ReturningAndNewClients.RETURNING,
        ]
        rows = [
            ClientsByTypesTable.Row(
                item_type=gettext('New'),
                percentage=new_percentage,
                count=new_count,
                _meta={
                    'key': ReturningAndNewClients.NEW,
                    '_key_choices': key_choices,
                },
            ),
            ClientsByTypesTable.Row(
                item_type=gettext('Returning'),
                percentage=returning_percentage,
                count=returning_count,
                _meta={
                    'key': ReturningAndNewClients.RETURNING,
                    '_key_choices': key_choices,
                },
            ),
        ]

        return ClientsByTypesTable(rows=rows)
