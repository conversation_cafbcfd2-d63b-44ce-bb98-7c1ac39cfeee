# pylint: disable=duplicate-code

import typing
from datetime import datetime
from dataclasses import InitVar, dataclass
from decimal import Decimal

from django.db.models import (
    Count,
    QuerySet,
    Q,
    Max,
    Min,
)
from django.contrib.postgres.aggregates import ArrayAgg
from django.utils.translation import gettext, gettext_lazy as _
from openpyxl.styles import Alignment
from openpyxl.worksheet.worksheet import Worksheet
from rest_framework import serializers

from webapps.business.models import Business
from webapps.stats_and_reports.reports.client_reports.clients_base_querysets import (
    ClientsQuerysetsMixin,
)
from webapps.stats_and_reports.consts import BCIGroupParam, BCIGroups
from webapps.stats_and_reports.reports import fields as report_fields
from webapps.stats_and_reports import models as repl_models
from webapps.stats_and_reports.reports.base import (
    BaseReport,
    ReportSection,
    ReportTable,
    ReportTableRow,
    SortableField,
    Sorting,
)
from webapps.stats_and_reports.report_keys import ReportKeys
from webapps.stats_and_reports.reports.mixins import PeriodDatesXlsxMixin
from webapps.stats_and_reports.reports.spreadsheet_utils import (
    NumberFormat,
    format_alignment,
    format_number,
    ws_range,
)
from webapps.stats_and_reports.reports.utils import check_and_correct_hashtag


class ClentTableRowSerializer(serializers.Serializer):
    full_name = serializers.CharField()
    groups = report_fields.ReportsListField()
    bookings_count = serializers.IntegerField()
    no_shows_count = serializers.IntegerField()
    first_visit = report_fields.ReportsDateField()
    last_visit = report_fields.ReportsDateField()
    bookings_value = report_fields.ReportsCurrencyField()
    revenue_net = report_fields.ReportsCurrencyField()
    discount = report_fields.ReportsCurrencyField()
    tax = report_fields.ReportsCurrencyField()
    gratuity = report_fields.ReportsCurrencyField()
    total_revenue = report_fields.ReportsCurrencyField()


class ClientsListTable(ReportTable):
    header = (
        _('First and last name'),
        _('Groups'),
        _('No. of bookings'),
        _('No. of no-shows'),
        _('First visit'),
        _('Last visit'),
        _('Bookings value'),
        _('Revenue net'),
        _('Discount'),
        _('Tax'),
        _('Tip'),
        _('Total Revenue'),
    )

    @dataclass
    class Row(ReportTableRow):  # pylint: disable=too-many-instance-attributes
        full_name: str
        groups: list
        bookings_count: int
        no_shows_count: int
        first_visit: datetime
        last_visit: datetime
        bookings_value: Decimal
        revenue_net: Decimal
        discount: Decimal
        tax: Decimal
        gratuity: Decimal
        total_revenue: Decimal

        # Business and user language is required for formatting datetime
        _business: InitVar[Business]
        _language: InitVar[str]

        def __post_init__(self, _business, _language):
            self.business = _business
            self.language = _language

        def get_serializer_class(self):
            return ClentTableRowSerializer

        def get_serializer(self):
            serializer = super().get_serializer()
            serializer.context['business'] = self.business
            serializer.context['language'] = self.language
            return serializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        format_alignment(
            ws_range(ws, 'B:B'), Alignment(horizontal='left', wrap_text=True, vertical='center')
        )
        format_alignment(
            ws_range(ws, 'C:M'), Alignment(horizontal='right', wrap_text=True, vertical='center')
        )

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_alignment(ws_range(ws, 'B:B'), Alignment(horizontal='left'))
        format_alignment(ws_range(ws, 'C:C'), Alignment(horizontal='left'))
        format_number(ws_range(ws, 'D:E'), NumberFormat.GENERAL)
        format_alignment(ws_range(ws, 'F:G'), Alignment(horizontal='right'))
        format_number(ws_range(ws, 'H:M'), NumberFormat.CURRENCY)

    def format_spreadsheet_table_total_row(self, ws: Worksheet):
        super().format_spreadsheet_table_total_row(ws)
        format_number(ws_range(ws, 'D:E'), NumberFormat.GENERAL)
        format_number(ws_range(ws, 'H:M'), NumberFormat.CURRENCY)


class ClientsListSection(ReportSection, ClientsQuerysetsMixin):
    key = 'clients_list_section'
    is_paginated = True
    append_total_row = True
    sortable_fields = (
        SortableField('full_name', db_column='customer_name_merged'),
        SortableField('bookings_count', db_column='appointments_count'),
        SortableField('no_shows_count', db_column='no_show_count'),
        SortableField('first_visit'),
        SortableField('last_visit'),
    )
    default_sorting = Sorting('full_name', Sorting.ASC)

    def get_queryset(self) -> QuerySet:
        return (
            self.all_appointments_queryset()
            .annotate_customer_name_merged()
            .exclude(
                booked_for__isnull=True,
            )
            .values(
                'booked_for_id',
                'customer_name_merged',
            )
            .annotate(
                all_tags=ArrayAgg(
                    'booked_for__tags__name',
                    filter=Q(booked_for__tags__isnull=False),
                    distinct=True,
                ),
                first_visit=Min('booked_for__first_appointment__booked_from'),
                last_visit=Max('bookings__booked_from'),
                no_show_count=Count(
                    'id',
                    filter=Q(status=repl_models.AppointmentReplica.STATUS.NOSHOW),
                    distinct=True,
                ),
                appointments_count=Count(
                    'id',
                    filter=Q(status=repl_models.AppointmentReplica.STATUS.FINISHED),
                    distinct=True,
                ),
            )
        )

    @staticmethod
    def get_groups(row):
        groups = []
        if row['groups_new_client']:
            groups.append(BCIGroups.NEW_CLIENT.label)
        elif row['groups_most_loyal'] >= BCIGroupParam.MOST_LOYAL_BOOKINGS_COUNT:
            groups.append(BCIGroups.RETURNING.label)
        elif row['groups_slipping_away'] == 0 and row['appointments_count']:
            groups.append(BCIGroups.SLIPPING_AWAY.label)

        if row['groups_first_visit'] and row['appointments_count'] == 1:
            groups.append(BCIGroups.FIRST_VISIT.label)

        if row['groups_active_gift_card'] != 0:
            groups.append(BCIGroups.ACTIVE_GIFT_CARD.label)

        if row['groups_non_app_user']:
            groups.append(BCIGroups.NON_APP_USER.label)

        if row['groups_upcoming_birthdays']:
            groups.append(BCIGroups.UPCOMING_BIRTHDAY.label)

        return groups

    def coerce_data_to_table(self, results) -> ClientsListTable:
        rows = []

        bci_ids = [row['booked_for_id'] for row in results if row['booked_for_id']]
        revenue_stats = self.get_revenue_stats_per_bci(bci_ids)
        appointments_stats = self.get_appointments_stats_by_queryset(
            self.all_appointments_queryset()
        )
        groups_data = self.get_data_for_groups(self.all_appointments_queryset())

        for row in results:
            row_data = {
                'customer_name_merged': '',
                'all_tags': [],
                'first_visit': None,
                'last_visit': None,
                'no_show_count': 0,
                'groups_new_client': '',
                'groups_slipping_away': 0,
                'groups_most_loyal': 0,
                'groups_first_visit': '',
                'groups_active_gift_card': 0,
                'groups_non_app_user': '',
                'groups_upcoming_birthdays': '',
                'appointments_count': 0,
                'bookings_value': Decimal('0'),
                'addons_value': Decimal('0'),
                'revenue_net': Decimal('0'),
                'real_discount': Decimal('0'),
                'tax': Decimal('0'),
                'gratuity': Decimal('0'),
            }

            row_data.update(row)
            row_data.update(appointments_stats.get(row['booked_for_id'], {}))
            row_data.update(revenue_stats.get(row['booked_for_id'], {}))
            row_data.update(groups_data.get(row['booked_for_id'], {}))

            groups = self.get_groups(row_data)
            groups.extend(check_and_correct_hashtag(tag) for tag in row_data['all_tags'])

            row_data['total_revenue'] = (
                row_data['revenue_net'] + row_data['tax'] + row_data['gratuity']
            )

            rows.append(
                ClientsListTable.Row(
                    full_name=row_data['customer_name_merged'],
                    groups=groups,
                    bookings_count=row_data['appointments_count'],
                    no_shows_count=row_data['no_show_count'],
                    first_visit=row_data['first_visit'],
                    last_visit=row_data['last_visit'],
                    bookings_value=row_data['bookings_value'] + row_data['addons_value'],
                    revenue_net=row_data['revenue_net'],
                    discount=row_data['real_discount'],
                    tax=row_data['tax'],
                    gratuity=row_data['gratuity'],
                    total_revenue=row_data['total_revenue'],
                    _business=self.scope.business,
                    _language=self.scope.language,
                )
            )

        return ClientsListTable(
            rows=rows,
            sortable_fields=self.sortable_fields,
            active_sorting=self.sorting_to_apply,
        )

    def calculate_total_row(self, queryset: QuerySet) -> ReportTableRow:
        total_counts = {
            'bookings_count': 0,
            'bookings_value': Decimal(0),
            'addons_value': Decimal(0),
            'revenue_net': Decimal(0),
            'real_discount': Decimal(0),
            'tax': Decimal(0),
            'gratuity': Decimal(0),
        }

        bci_ids = [row['booked_for_id'] for row in queryset if row['booked_for_id']]
        revenue_stats = self.get_revenue_stats_per_bci(bci_ids)
        appointments_stats = self.get_appointments_stats_by_queryset(
            self.all_appointments_queryset()
        )

        total_counts['no_show_count'] = sum(row['no_show_count'] for row in queryset)

        for bci_id, app_stats in appointments_stats.items():
            if bci_id:
                total_counts['bookings_value'] += app_stats['bookings_value']
                total_counts['addons_value'] += app_stats['addons_value']
                total_counts['bookings_count'] += app_stats['appointments_count']

        for revenue in revenue_stats.values():
            total_counts['revenue_net'] += revenue['revenue_net']
            total_counts['tax'] += revenue['tax']
            total_counts['real_discount'] += revenue['real_discount']
            total_counts['gratuity'] += revenue['gratuity']

        total_counts['total_revenue'] = (
            total_counts['revenue_net'] + total_counts['tax'] + total_counts['gratuity']
        )

        return ClientsListTable.Row(
            full_name=gettext('Total'),
            groups=[],
            bookings_count=total_counts['bookings_count'],
            no_shows_count=total_counts['no_show_count'],
            first_visit='',
            last_visit='',
            bookings_value=total_counts['bookings_value'] + total_counts['addons_value'],
            revenue_net=total_counts['revenue_net'],
            discount=total_counts['real_discount'],
            tax=total_counts['tax'],
            gratuity=total_counts['gratuity'],
            total_revenue=total_counts['total_revenue'],
            _business=self.scope.business,
            _language=self.scope.language,
        )


class ClientsList(PeriodDatesXlsxMixin, BaseReport):
    key = ReportKeys.ClientsList
    is_date_filtered = False  # shows "current" data

    @staticmethod
    def get_title() -> str:
        return gettext('Client list')

    @property
    def subtitle(self) -> str:
        return gettext('List of clients, count of bookings, bookings value, and revenue')

    def __init__(self, *args) -> None:
        super().__init__(*args)
        self._add_section(ClientsListSection)

    def get_column_widths(self) -> typing.List[int]:
        return [5, 25, 20, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15]
