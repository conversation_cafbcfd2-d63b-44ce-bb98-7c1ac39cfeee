import unittest
from datetime import datetime

import pytest
from django.test import override_settings
from model_bakery import baker
from parameterized import parameterized

from country_config import Country
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.baker_recipes import booking_source_recipe
from webapps.booking.serializers.appointment import FCAppointmentSerializer
from webapps.booking.tests.utils import create_appointment
from webapps.business.enums import PriceType
from webapps.business.models import Service, ServiceVariant, Resource, Business
from webapps.french_certification.adapters import get_payments_for_basket_adapter
from webapps.french_certification.tests.common import (
    get_fiscal_receipt_with_basket_data,
    basket_payments_patched,
)
from webapps.french_certification.utils import get_basket_items
from webapps.invoicing.baker_recipes import fc_seller_recipe
from webapps.invoicing.tests.common import french_certification_enabled
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.enums.compatibilities import NEW_CHECKOUT
from webapps.pos.models import (
    POS,
    PaymentType,
    Transaction,
    PaymentRow,
)
from webapps.pos.serializers import TransactionSerializer
from webapps.user.models import User


@pytest.mark.django_db
class TestBlockingAppointmentWithEditableFlag(
    unittest.TestCase
):  # pylint: disable=too-many-instance-attributes
    def setUp(self):
        self.booking_source = booking_source_recipe.make()
        self.user = baker.make(User)
        self.access_level = Resource.STAFF_ACCESS_LEVEL_OWNER
        self.business = baker.make(Business, owner=self.user)
        self.pos = baker.make(
            POS, business=self.business, active=True, pos_refactor_stage2_enabled=True
        )
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)
        self.compatibilities = {NEW_CHECKOUT: True}
        self.service = baker.make(Service, business=self.business, tax_rate=10)
        self.service_variant_1 = baker.make(
            ServiceVariant,
            service=self.service,
            type=PriceType.FIXED,
            price=10,
        )
        self.service_variant_2 = baker.make(
            ServiceVariant,
            service=self.service,
            type=PriceType.FIXED,
            price=20,
        )
        self.service_variant_3 = baker.make(
            ServiceVariant,
            service=self.service,
            type=PriceType.FIXED,
            price=30,
        )
        self.tz = self.business.get_timezone()
        self.appt = create_appointment(
            [
                {
                    'service_variant': self.service_variant_1,
                    'booked_from': datetime(2023, 11, 17, 12, tzinfo=self.tz),
                    'booked_till': datetime(2023, 11, 17, 13, tzinfo=self.tz),
                },
                {
                    'service_variant': self.service_variant_2,
                    'booked_from': datetime(2023, 11, 17, 13, tzinfo=self.tz),
                    'booked_till': datetime(2023, 11, 17, 14, tzinfo=self.tz),
                },
            ],
            business=self.business,
        )
        fc_seller_recipe.make(business=self.business)
        self.paid_booking = self.appt.bookings.all().order_by('created')[0]
        self.data = {
            'dry_run': False,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'payment_rows': [
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'mode': PaymentRow.PAYMENT_ROW_MODE__COMPLETE,
                    'amount': 60,
                }
            ],
            'bookings': [
                {
                    'booking_id': self.paid_booking.id,
                    'item_price': 60,
                },
            ],
        }
        self.serializer = TransactionSerializer(
            data=self.data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
                'business': self.business,
            },
        )
        self.assertTrue(self.serializer.is_valid(), self.serializer.errors)
        self.txn = self.serializer.save()
        get_fiscal_receipt_with_basket_data(
            business_id=self.business.id,
            is_prepayment=True,
            basket_id=self.txn.basket_id,
            basket_items=get_basket_items(self.txn.basket_id),
        )
        self.basket_payments = get_payments_for_basket_adapter(self.txn.basket_id)
        self.patch_function = self._get_patch_function(self.basket_payments)

    def _get_patch_function(self, basket_payments):
        return basket_payments_patched(basket_payments)

    @parameterized.expand([c for c in Country if c not in (Country.FR)])
    def test_editable_flag_for_country_other_than_france(self, country: Country):
        with override_settings(API_COUNTRY=country):
            with self._get_patch_function(basket_payments=self.basket_payments):
                data = FCAppointmentSerializer(
                    instance=AppointmentWrapper.get_by_appointment_id(
                        self.appt.id, business_id=self.business.id
                    ),
                    context={
                        'business': self.business,
                    },
                ).data

            self.assertTrue(data['subbookings'][0]['editable'])
            self.assertTrue(data['subbookings'][1]['editable'])

    @french_certification_enabled()
    def test_editable_flag_for_country_france(self):
        with self.patch_function:
            data = FCAppointmentSerializer(
                instance=AppointmentWrapper.get_by_appointment_id(
                    self.appt.id, business_id=self.business.id
                ),
                context={
                    'business': self.business,
                },
            ).data

        self.assertFalse(data['subbookings'][0]['editable'])
        self.assertTrue(data['subbookings'][1]['editable'])

    @french_certification_enabled()
    def test_block_edit(self):
        with self.patch_function:
            data = FCAppointmentSerializer(
                instance=AppointmentWrapper.get_by_appointment_id(
                    self.appt.id, business_id=self.business.id
                ),
                context={
                    'business': self.business,
                },
            ).data
        data['subbookings'][0]['service_variant']['id'] = self.service_variant_3.id
        data['dry_run'] = False
        data['overbooking'] = True
        data['_notify_about_reschedule'] = False

        serializer = FCAppointmentSerializer(
            instance=AppointmentWrapper.get_by_appointment_id(
                self.appt.id, business_id=self.business.id
            ),
            data=data,
            context={
                'business': self.business,
            },
        )

        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            str(serializer.errors['non_field_errors'][0]),
            'You cannot delete or edit service paid by prepayment',
        )

    @french_certification_enabled()
    def test_block_delete(self):
        with self.patch_function:
            data = FCAppointmentSerializer(
                instance=AppointmentWrapper.get_by_appointment_id(
                    self.appt.id, business_id=self.business.id
                ),
                context={
                    'business': self.business,
                },
            ).data
        del data['subbookings'][0]
        data['dry_run'] = False
        data['overbooking'] = True
        data['_notify_about_reschedule'] = False

        serializer = FCAppointmentSerializer(
            instance=AppointmentWrapper.get_by_appointment_id(
                self.appt.id, business_id=self.business.id
            ),
            data=data,
            context={
                'business': self.business,
            },
        )

        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            str(serializer.errors['non_field_errors'][0]),
            'You cannot delete or edit service paid by prepayment',
        )

    @french_certification_enabled()
    def test_allow_edit_not_paid_subbooking(self):
        with self.patch_function:
            data = FCAppointmentSerializer(
                instance=AppointmentWrapper.get_by_appointment_id(
                    self.appt.id, business_id=self.business.id
                ),
                context={
                    'business': self.business,
                },
            ).data
        data['subbookings'][1]['service_variant']['id'] = self.service_variant_3.id
        data['dry_run'] = False
        data['overbooking'] = True
        data['_notify_about_reschedule'] = False

        serializer = FCAppointmentSerializer(
            instance=AppointmentWrapper.get_by_appointment_id(
                self.appt.id, business_id=self.business.id
            ),
            data=data,
            context={
                'business': self.business,
            },
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)

    @french_certification_enabled()
    def test_two_paid_subbookings_with_same_service_variant(self):
        appt = create_appointment(
            [
                {
                    'service_variant': self.service_variant_1,
                    'booked_from': datetime(2023, 11, 17, 12, tzinfo=self.tz),
                    'booked_till': datetime(2023, 11, 17, 13, tzinfo=self.tz),
                },
                {
                    'service_variant': self.service_variant_1,
                    'booked_from': datetime(2023, 11, 17, 13, tzinfo=self.tz),
                    'booked_till': datetime(2023, 11, 17, 14, tzinfo=self.tz),
                },
            ],
            business=self.business,
        )
        data = {
            'dry_run': False,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'payment_rows': [
                {
                    'payment_type_code': PaymentTypeEnum.PREPAYMENT,
                    'mode': PaymentRow.PAYMENT_ROW_MODE__COMPLETE,
                    'amount': 120,
                }
            ],
            'bookings': [
                {
                    'booking_id': appt.bookings.all()[0].id,
                    'item_price': 60,
                },
                {
                    'booking_id': appt.bookings.all()[1].id,
                    'item_price': 60,
                },
            ],
        }
        txn_serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
                'business': self.business,
            },
        )
        self.assertTrue(txn_serializer.is_valid(), self.serializer.errors)
        txn = txn_serializer.save()
        get_fiscal_receipt_with_basket_data(
            business_id=self.business.id,
            is_prepayment=True,
            basket_id=txn.basket_id,
            basket_items=get_basket_items(txn.basket_id),
        )
        basket_payments = get_payments_for_basket_adapter(txn.basket_id)
        with self._get_patch_function(basket_payments):
            data = FCAppointmentSerializer(
                instance=AppointmentWrapper.get_by_appointment_id(
                    appt.id, business_id=self.business.id
                ),
                context={
                    'business': self.business,
                },
            ).data
        del data['subbookings'][0]
        data['dry_run'] = False
        data['overbooking'] = True
        data['_notify_about_reschedule'] = False

        serializer = FCAppointmentSerializer(
            instance=AppointmentWrapper.get_by_appointment_id(
                appt.id, business_id=self.business.id
            ),
            data=data,
            context={
                'business': self.business,
            },
        )

        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            str(serializer.errors['non_field_errors'][0]),
            'You cannot delete or edit service paid by prepayment',
        )


class TestBlockingAppointmentWithEditableFlagWithFlagOn(TestBlockingAppointmentWithEditableFlag):
    pass
