import io
import json
from typing import <PERSON><PERSON><PERSON><PERSON>
from zipfile import ZipFile

from django.conf import settings
from django.db import transaction

from webapps.french_certification.enums import ClosePeriodType
from webapps.french_certification.exceptions import FiscalArchiveGCSException
from webapps.french_certification.models import FiscalArchive
from webapps.french_certification.services.jet import JETService
from webapps.french_certification.storage import FiscalArchiveFileStorage


class File(NamedTuple):
    name: str
    content: bytes


class FiscalArchiveService:
    @staticmethod
    def create_zip_archive(files: list[File]):
        archive = io.BytesIO()
        with ZipFile(archive, 'w') as zip_archive:
            for file in files:
                zip_archive.writestr(file.name, file.content)
        return archive

    @staticmethod
    def create_name_for_fiscal_archive(fiscal_archive):
        date = fiscal_archive.grand_total.closure_date
        return f"{fiscal_archive.business_id}-{fiscal_archive.type}-{date}"

    @classmethod
    def get_fiscal_archive_storage_path(cls, fiscal_archive):
        if settings.LIVE_DEPLOYMENT:
            prefix = 'production'
        elif settings.LOCAL_DEPLOYMENT:
            prefix = 'test'
        else:
            prefix = settings.BOOKSY_DOMAIN

        date = fiscal_archive.grand_total.closure_date
        return f"{prefix}/{fiscal_archive.business_id}/{fiscal_archive.type}-{date}.zip"

    @classmethod
    def get_signature_file(cls, fiscal_archive):
        return json.dumps(
            {
                'signature': fiscal_archive.signature,
                'signature_timestamp': str(fiscal_archive.signature_timestamp),
            },
        ).encode()

    @classmethod
    def send_fiscal_archive_data_to_storage(cls, fiscal_archive):
        fiscal_archive_name = cls.create_name_for_fiscal_archive(fiscal_archive=fiscal_archive)
        path = cls.get_fiscal_archive_storage_path(fiscal_archive=fiscal_archive)

        saved_file_name = FiscalArchiveFileStorage().save(
            name=path,
            content=cls.create_zip_archive(
                [
                    File(
                        name=fiscal_archive_name + '-content.json',
                        content=fiscal_archive.signature_chaining_message.encode(),
                    ),
                    File(
                        name=fiscal_archive_name + '-signature.json',
                        content=cls.get_signature_file(fiscal_archive),
                    ),
                ]
            ),
        )
        if path != saved_file_name:
            raise FiscalArchiveGCSException(
                f"There was an error while uploading Fiscal Archive data. "
                f"Business ID: {fiscal_archive.business_id}."
            )

    @staticmethod
    @transaction.atomic
    def create_fiscal_archive(grand_total):
        from webapps.french_certification.serializers import FiscalArchiveSerializer

        fiscal_archive = FiscalArchive.objects.create(
            grand_total=grand_total,
            business_id=grand_total.business_id,
            type=grand_total.type,
            json_data=FiscalArchiveSerializer(grand_total).data,
            software_version=grand_total.software_version,
        )

        if grand_total.type == ClosePeriodType.YEAR:
            JETService.fiscal_year_archiving_event(grand_total=grand_total)
        else:
            JETService.intermediate_fiscal_archiving_event(grand_total=grand_total)

        return fiscal_archive
