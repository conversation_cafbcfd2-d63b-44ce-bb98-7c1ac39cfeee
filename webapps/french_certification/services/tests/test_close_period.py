import calendar
import datetime
from typing import Iterable
from unittest import mock

from dateutil.relativedelta import relativedelta
from django.db import IntegrityError
from django.test import TestCase
from freezegun import freeze_time
from mock.mock import patch, Mock
from model_bakery import baker
from parameterized import parameterized

from lib.feature_flag.bug import FrenchCertificationYearPeriodsRefactor
from lib.feature_flag.feature import FrenchCertificationMajorSoftwareVersionFlag
from lib.point_of_sale.enums import (
    BasketItemTaxType,
    BasketPaymentStatus,
    PaymentMethodType,
)
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from webapps.business.models import Business
from webapps.french_certification.enums import (
    ClosePeriodType,
    JETEventCode,
)
from webapps.french_certification.exceptions import CannotClosePeriod
from webapps.french_certification.models import (
    FiscalArchive,
    FiscalReceipt,
    FiscalReceiptDuplicate,
    GrandTotal,
    JET,
    TotalPerTaxRate,
)
from webapps.french_certification.services.close_period import (
    ClosePeriodFactory,
    ClosePeriodService,
    DailyClosePeriodFactory,
    MonthlyClosePeriodFactory,
    YearlyClosePeriodFactory,
)
from webapps.french_certification.services.fiscal_receipt import FiscalReceiptService
from webapps.french_certification.tests.common import (
    create_grand_total,
    bulk_close_day,
    create_data_initialization_event,
    disable_grand_total_checks,
    get_fiscal_receipt_with_basket_data,
)
from webapps.french_certification.tests.common_close_periods import ClosedPeriodsSetup
from webapps.french_certification.tests.entities import TestBasketPaymentEntity
from webapps.french_certification.utils import (
    get_data_from_basket_item,
    get_data_from_basket_payment,
)
from webapps.invoicing.baker_recipes import fc_seller_recipe
from webapps.point_of_sale.models import (
    Basket,
    BasketItem,
    BasketPayment,
)


def _create_receipt(business, day) -> FiscalReceipt:
    basket = baker.make(Basket, business_id=business.id)
    with freeze_time(day):
        basket_item_1 = baker.make(
            BasketItem,
            basket=basket,
            name_line_1='Shampoo',
            quantity=1,
            item_price=1800,
            discount_rate=0,
            discounted_item_price=1800,
            tax_amount=300,
            tax_rate=2000,
            tax_type=BasketItemTaxType.INCLUDED.value,
            total=1800,
            net_total=1500,
            gross_total=1800,
            discounted_total=1800,
            net_total_wo_discount=1500,
        )
        basket_item_2 = baker.make(
            BasketItem,
            basket=basket,
            name_line_1='Cut (30m)',
            quantity=1,
            item_price=5900,
            discount_rate=0,
            discounted_item_price=5900,
            tax_amount=536,
            tax_rate=1000,
            tax_type=BasketItemTaxType.INCLUDED.value,
            total=5900,
            net_total=5364,
            gross_total=5900,
            discounted_total=5900,
            net_total_wo_discount=5364,
        )
        if FrenchCertificationMajorSoftwareVersionFlag():
            basket_payment = TestBasketPaymentEntity(
                basket_id=basket.id, payment_method=PaymentMethodType.CASH, amount=7700
            )

            return get_fiscal_receipt_with_basket_data(
                business_id=basket.business_id,
                basket_id=basket.id,
                operator_id=None,
                basket_items=[basket_item_1, basket_item_2],
                basket_payments=[basket_payment],
            )
        basket_payment_1 = baker.make(
            BasketPayment,
            basket=basket,
            payment_method='cash',
            amount=7700,
            status=BasketPaymentStatus.SUCCESS,
        )
        return get_fiscal_receipt_with_basket_data(
            business_id=basket.business_id,
            basket_id=basket.id,
            operator_id=None,
            basket_items=[basket_item_1, basket_item_2],
            basket_payments=[basket_payment_1],
        )


def _bulk_close_month(business, months: list[datetime.date]):
    grand_totals = []
    utc = datetime.timezone.utc
    for start_at in months:
        period_start = datetime.datetime(
            year=start_at.year,
            month=start_at.month,
            day=1,
            tzinfo=utc,
        )
        end_day = calendar.monthrange(start_at.year, start_at.month)[1]
        period_end = period_start + relativedelta(days=end_day - 1)
        grand_total = GrandTotal(
            net_total=0,
            gross_total=0,
            cumulative_gross_total=0,
            business_id=business.id,
            type=ClosePeriodType.MONTH,
            period_start=period_start,
            period_end=period_end,
            signature_timestamp=tznow(),
        )
        grand_total.total_gross_by_tax_rate = []
        grand_totals.append(grand_total)

    GrandTotal.objects.bulk_create(grand_totals)


class TestClosePeriodService(TestCase):
    def setUp(self):
        self.business = baker.make(Business, time_zone_name='Europe/Paris')
        self.operator_id = 1
        self.business_timezone = self.business.get_timezone()
        fc_seller_recipe.make(business=self.business)

    def test_raises_if_previous_day_is_not_closed(self):
        # 15.10.2022 -> first grand total is created
        # 16.10.2022 -> not closed
        # 17.10.2022 -> closing day here should result in error because yesterday is not closed
        create_data_initialization_event(
            self.business.id, datetime.datetime(2022, 10, 14, tzinfo=self.business_timezone)
        )
        first_grand_total_created_at = datetime.datetime(
            2022, 10, 15, tzinfo=self.business_timezone
        )
        create_grand_total(
            business_id=self.business.id,
            net_total=10000,
            gross_total=12300,
            cumulative_gross_total=12300,
            type=ClosePeriodType.DAY,
            period_start=first_grand_total_created_at,
            period_end=first_grand_total_created_at + datetime.timedelta(days=1),
            software_version='3',
        )
        second_grand_total_created_at = first_grand_total_created_at + datetime.timedelta(days=2)

        with self.assertRaises(CannotClosePeriod):
            ClosePeriodService.close_period(
                self.business,
                second_grand_total_created_at,
                ClosePeriodType.DAY,
            )

    @parameterized.expand(
        [
            (ClosePeriodType.DAY,),
            (ClosePeriodType.MONTH,),
            (ClosePeriodType.YEAR,),
        ]
    )
    def test_closing_period_detects_fiscal_receipt_integrity_failure(self, period_type):
        create_data_initialization_event(
            self.business.id,
            datetime.datetime(2022, 12, 29, 14, 30, 29, tzinfo=self.business_timezone),
        )
        fiscal_receipt_created_at = datetime.datetime(
            2022, 12, 29, 15, 30, 29, tzinfo=self.business_timezone
        )
        with freeze_time(fiscal_receipt_created_at):
            basket = baker.make(Basket, business_id=self.business.id)
            basket_payment = TestBasketPaymentEntity(basket_id=basket.id)
            receipt = get_fiscal_receipt_with_basket_data(
                business_id=basket.business_id,
                basket_id=basket.id,
                operator_id=None,
                basket_items=[],
                basket_payments=[basket_payment],
            )
        FiscalReceipt.objects.filter(pk=receipt.id).update(number=-1)

        with (
            disable_grand_total_checks(),
            patch(
                'webapps.french_certification.serializers.get_basket_payments_adapter',
                Mock(return_value=[basket_payment]),
            ),
        ):
            ClosePeriodService.close_period(self.business, fiscal_receipt_created_at, period_type)

        integrity_failure_info = JET.objects.get(
            business_id=self.business.id,
            event_code=90,
        ).additional_info
        self.assertEqual(f'Detected in FiscalReceipt {receipt.id}', integrity_failure_info)

    @parameterized.expand(
        [
            (ClosePeriodType.DAY,),
            (ClosePeriodType.MONTH,),
            (ClosePeriodType.YEAR,),
        ]
    )
    def test_closing_period_detects_fiscal_receipt_duplicate_integrity_failure(self, period_type):
        create_data_initialization_event(
            self.business.id,
            datetime.datetime(2022, 12, 29, 14, 30, 29, tzinfo=self.business_timezone),
        )
        fiscal_receipt_duplicate_created_at = datetime.datetime(
            2022, 12, 29, 15, 30, 29, tzinfo=self.business_timezone
        )
        basket = baker.make(Basket, business_id=self.business.id)
        receipt = get_fiscal_receipt_with_basket_data(
            business_id=basket.business_id,
            basket_id=basket.id,
            operator_id=None,
            basket_items=[],
            basket_payments=[],
        )
        with freeze_time(fiscal_receipt_duplicate_created_at):
            duplicate = FiscalReceiptDuplicate.objects.create(
                original_document=receipt,
                reprint_number=1,
                business_id=self.business.id,
                software_version='3',
                operator_id=900,
                reason='wrong data',
            )
        FiscalReceiptDuplicate.objects.filter(pk=duplicate.id).update(reprint_number=80)

        with disable_grand_total_checks():
            ClosePeriodService.close_period(
                self.business,
                fiscal_receipt_duplicate_created_at,
                period_type,
            )

        integrity_failure_info = JET.objects.get(
            business_id=self.business.id,
            event_code=90,
        ).additional_info
        self.assertEqual(
            f'Detected in FiscalReceiptDuplicate {duplicate.id}',
            integrity_failure_info,
        )

    @parameterized.expand(
        [
            (ClosePeriodType.DAY,),
            (ClosePeriodType.MONTH,),
            (ClosePeriodType.YEAR,),
        ]
    )
    def test_closing_period_detects_jet_integrity_failure(self, period_type):
        create_data_initialization_event(
            self.business.id,
            datetime.datetime(2022, 12, 29, 14, 30, 29, tzinfo=self.business_timezone),
        )
        jet_created_at = datetime.datetime(2022, 12, 29, 15, 30, 29, tzinfo=self.business_timezone)
        with freeze_time(jet_created_at):
            jet = JET.objects.create(
                event_code=410,
                event_description='taxable_company_change',
                business_id=self.business.id,
                operator_id=4,
                additional_info='changed address',
            )
        JET.objects.filter(pk=jet.id).update(operator_id=5)

        with disable_grand_total_checks():
            ClosePeriodService.close_period(
                self.business,
                jet_created_at,
                period_type,
            )

        integrity_failure_info = JET.objects.get(
            business_id=self.business.id,
            event_code=90,
        ).additional_info
        self.assertIn(f'Detected in JET {jet.id}', integrity_failure_info)

    @parameterized.expand(
        [
            (ClosePeriodType.DAY,),
            (ClosePeriodType.MONTH,),
            (ClosePeriodType.YEAR,),
        ]
    )
    def test_closing_period_detects_previous_gt_and_fa_integrity_failures(self, period_type):
        create_data_initialization_event(
            self.business.id,
            datetime.datetime(2022, 12, 31, 10, 59, 59, tzinfo=self.business_timezone),
        )
        with disable_grand_total_checks():
            ClosePeriodService.close_period(
                self.business,
                datetime.datetime(2022, 12, 31, tzinfo=self.business_timezone),
                period_type,
            )
        GrandTotal.objects.filter(business_id=self.business.id).update(gross_total=-1)
        altered_grand_total = GrandTotal.objects.get(business_id=self.business.id)
        altered_fiscal_archive = FiscalArchive.objects.get(business_id=self.business.id)

        with disable_grand_total_checks():
            ClosePeriodService.close_period(
                self.business,
                datetime.datetime(2023, 1, 1, tzinfo=self.business_timezone),
                period_type,
            )

        integrity_failure_infos = JET.objects.filter(
            business_id=self.business.id,
            event_code=90,
        ).values_list('additional_info', flat=True)
        self.assertIn(f'Detected in GrandTotal {altered_grand_total.id}', integrity_failure_infos)
        self.assertIn(
            f'Detected in FiscalArchive {altered_fiscal_archive.id}',
            integrity_failure_infos,
        )

    def test_closing_monthly_period_detects_daily_grand_total_integrity_failure(self):
        utc = datetime.timezone.utc
        create_data_initialization_event(
            self.business.id,
            datetime.datetime(2022, 12, 31, 10, 59, 59, tzinfo=self.business_timezone),
        )
        grand_total = create_grand_total(
            business_id=self.business.id,
            net_total=10000,
            gross_total=12300,
            cumulative_gross_total=12300,
            type=ClosePeriodType.DAY,
            period_start=datetime.datetime(2022, 12, 31, tzinfo=utc),
            period_end=datetime.datetime(2023, 1, 1, tzinfo=utc),
            software_version='3',
        )
        GrandTotal.objects.filter(pk=grand_total.id).update(gross_total=-1)

        ClosePeriodService.close_period(
            self.business,
            datetime.datetime(2022, 12, 1, tzinfo=utc),
            ClosePeriodType.MONTH,
        )

        integrity_failure_info = JET.objects.get(
            business_id=self.business.id,
            event_code=90,
        ).additional_info
        self.assertIn(f'Detected in GrandTotal {grand_total.id}', integrity_failure_info)

    def test_closing_yearly_period_detects_monthly_grand_total_integrity_failure(self):
        create_data_initialization_event(
            self.business.id,
            datetime.datetime(2022, 12, 12, 10, 59, 59, tzinfo=self.business_timezone),
        )
        grand_total = create_grand_total(
            business_id=self.business.id,
            net_total=10000,
            gross_total=12300,
            cumulative_gross_total=12300,
            type=ClosePeriodType.MONTH,
            period_start=datetime.datetime(2022, 12, 1, tzinfo=self.business_timezone),
            period_end=datetime.datetime(2023, 1, 1, tzinfo=self.business_timezone),
            software_version='3',
        )
        GrandTotal.objects.filter(pk=grand_total.id).update(gross_total=-1)

        ClosePeriodService.close_period(
            self.business,
            datetime.datetime(2022, 1, 1, tzinfo=self.business_timezone),
            ClosePeriodType.YEAR,
        )

        integrity_failure_info = JET.objects.get(
            business_id=self.business.id,
            event_code=90,
        ).additional_info
        self.assertIn(f'Detected in GrandTotal {grand_total.id}', integrity_failure_info)

    def test_closing_period_adds_jet_event(self):
        create_data_initialization_event(
            self.business.id, datetime.datetime(2022, 5, 15, tzinfo=self.business_timezone)
        )

        ClosePeriodService.close_period(
            business=self.business,
            closing_time=datetime.datetime(2022, 5, 15, tzinfo=self.business_timezone),
            period_type=ClosePeriodType.DAY,
        )

        jet = JET.objects.get(
            business_id=self.business.id, event_code=JETEventCode.DAY_OR_MONTH_CLOSED
        )
        self.assertEqual(jet.event_code, 50)
        self.assertEqual(jet.business_id, self.business.id)
        self.assertEqual(jet.additional_info, 'Daily closure: 2022-05-15')

    @parameterized.expand(
        [
            (ClosePeriodType.DAY, datetime.datetime(2022, 5, 16)),
            (ClosePeriodType.MONTH, datetime.datetime(2022, 6, 1)),
            (ClosePeriodType.YEAR, datetime.datetime(2023, 1, 1)),
        ]
    )
    def test_cannot_close_if_start_closing_from_is_later_than_closing_date(
        self, period_type, mocked_start_closing_from
    ):
        create_data_initialization_event(
            self.business.id, mocked_start_closing_from.replace(tzinfo=self.business_timezone)
        )
        closing_time = datetime.datetime(2022, 5, 14, tzinfo=datetime.timezone.utc)

        with self.assertRaises(CannotClosePeriod):
            ClosePeriodService.close_period(
                business=self.business,
                closing_time=closing_time,
                period_type=period_type,
            )

    def test_cannot_close_month_if_last_day_not_closed(self):
        create_data_initialization_event(
            self.business.id, datetime.datetime(2022, 1, 1, tzinfo=self.business_timezone)
        )
        mocked_closed_days = [
            datetime.date(2022, 1, day) for day in range(1, 31)
        ]  # last day of the month not closed
        bulk_close_day(self.business, mocked_closed_days)
        with self.assertRaises(CannotClosePeriod):
            ClosePeriodService.close_period(
                self.business,
                closing_time=datetime.datetime(2022, 1, 1, tzinfo=self.business_timezone),
                period_type=ClosePeriodType.MONTH,
            )

    def test_cannot_close_year_if_last_month_not_closed(self):
        create_data_initialization_event(
            self.business.id, datetime.datetime(2022, 1, 1, tzinfo=self.business_timezone)
        )
        mocked_closed_days = [
            datetime.date(2022, month, 1) for month in range(1, 12)
        ]  # last day of the month not closed
        _bulk_close_month(self.business, mocked_closed_days)

        with self.assertRaises(CannotClosePeriod):
            ClosePeriodService.close_period(
                self.business,
                closing_time=datetime.datetime(2022, 1, 1, tzinfo=self.business_timezone),
                period_type=ClosePeriodType.YEAR,
            )

    @parameterized.expand(
        [
            (ClosePeriodType.DAY,),
            (ClosePeriodType.MONTH,),
            (ClosePeriodType.YEAR,),
        ]
    )
    def test_cannot_close_period_if_it_has_not_ended(self, close_period_type):
        create_data_initialization_event(
            self.business.id, datetime.datetime(2022, 10, 14, tzinfo=self.business_timezone)
        )
        already_closed_days = [
            datetime.date(2022, 10, 14),
            datetime.date(2022, 10, 15),
            datetime.date(2022, 10, 16),
            datetime.date(2022, 10, 17),
            datetime.date(2022, 10, 18),
            datetime.date(2022, 10, 19),
        ]
        bulk_close_day(self.business, already_closed_days)
        mocked_now = datetime.datetime(2022, 10, 20, 15, 31, 29, tzinfo=self.business_timezone)

        with (
            freeze_time(mocked_now),
            self.assertRaises(CannotClosePeriod),
        ):
            ClosePeriodService.close_period(
                self.business,
                datetime.datetime(2022, 10, 20, tzinfo=self.business_timezone),
                close_period_type,
            )

    @parameterized.expand(ClosePeriodType.values())
    def test_closing_period_with_fiscal_archive(self, period_type):
        data_initialized_at = datetime.datetime(2022, 3, 22, tzinfo=self.business_timezone)
        create_data_initialization_event(self.business.id, data_initialized_at)

        with disable_grand_total_checks():
            with mock.patch.object(
                ClosePeriodFactory, 'send_fiscal_archive_data_to_storage'
            ) as send_to_gcs:
                ClosePeriodService.close_period(
                    business=self.business,
                    closing_time=data_initialized_at,
                    period_type=period_type,
                )

        fiscal_archives = FiscalArchive.objects.filter(
            business_id=self.business.id, type=period_type
        ).all()
        self.assertEqual(len(fiscal_archives), 1)
        send_to_gcs.assert_called_once_with(fiscal_archive=fiscal_archives[0])

    @parameterized.expand(ClosePeriodType.values())
    def test_closing_period_with_software_version(self, period_type):
        data_initialized_at = datetime.datetime(2020, 8, 14, tzinfo=self.business_timezone)
        create_data_initialization_event(self.business.id, data_initialized_at)

        with disable_grand_total_checks():
            ClosePeriodService.close_period(
                business=self.business,
                closing_time=data_initialized_at,
                period_type=period_type,
            )

        grand_total = GrandTotal.objects.filter(
            business_id=self.business.id, type=period_type
        ).first()
        fiscal_archive = FiscalArchive.objects.filter(
            business_id=self.business.id, type=period_type
        ).first()
        self.assertEqual(grand_total.software_version, '3.0')
        self.assertEqual(grand_total.software_name, 'Booksy Biz')
        self.assertEqual(fiscal_archive.software_version, '3.0')
        self.assertEqual(fiscal_archive.software_name, 'Booksy Biz')

    def test_first_day_to_close_is_end_of_month_then_day_month_closed(self):
        utc = datetime.timezone.utc
        data_initialized_at = datetime.datetime(2022, 5, 30, tzinfo=utc)
        self._prepare_closed_periods(data_initialized_at=data_initialized_at)
        server_time = datetime.datetime(2022, 6, 1, 0, 3, tzinfo=utc)

        with freeze_time(server_time, tick=True):
            ClosePeriodService.close_period(
                self.business,
                closing_time=data_initialized_at,
                period_type=ClosePeriodType.DAY,
            )
            ClosePeriodService.close_period(
                self.business,
                closing_time=data_initialized_at + relativedelta(days=1),
                period_type=ClosePeriodType.DAY,
            )
            ClosePeriodService.close_period(
                self.business,
                closing_time=data_initialized_at,
                period_type=ClosePeriodType.MONTH,
            )

        gt_closed_day = self.get_grand_total(
            ClosePeriodType.DAY,
            datetime.datetime(2022, 5, 31, 0, 0, tzinfo=utc),
            datetime.datetime(2022, 6, 1, 0, 0, tzinfo=utc),
        )
        self.assert_day_month_closed(gt_closed_day)
        self.assert_no_data_integrity_errors(gt_closed_day)

        gt_closed_month = self.get_grand_total(
            ClosePeriodType.MONTH,
            datetime.datetime(2022, 5, 1, tzinfo=utc),
            datetime.datetime(2022, 6, 1, tzinfo=utc),
        )
        self.assert_day_month_closed(gt_closed_month)
        self.assert_no_data_integrity_errors(gt_closed_month)

    def test_first_day_to_close_is_end_of_year_then_day_year_closed(self):
        utc = datetime.timezone.utc
        data_initialized_at = datetime.datetime(2022, 1, 1, 2, tzinfo=utc)
        self._prepare_closed_periods(data_initialized_at=data_initialized_at)
        server_time = datetime.datetime(2023, 1, 1, 0, 3, tzinfo=utc)
        already_closed_months = [datetime.date(2022, i, 14) for i in range(1, 13)]
        _bulk_close_month(self.business, already_closed_months)

        with freeze_time(server_time, tick=True):
            ClosePeriodService.close_period(
                self.business,
                closing_time=data_initialized_at,
                period_type=ClosePeriodType.YEAR,
            )

        gt_closed_year = self.get_grand_total(
            ClosePeriodType.YEAR,
            datetime.datetime(2022, 1, 1, tzinfo=utc),
            datetime.datetime(2023, 1, 1, tzinfo=utc),
        )
        self.assertTrue(
            JET.objects.filter(
                business_id=self.business.id,
                event_code=JETEventCode.YEAR_CLOSED,
                additional_info__contains='Year closed: 2022',
            ).exists(),
            f"year closed not reported for period"
            f" '{gt_closed_year.period_start}'-'{gt_closed_year.period_end}'"
            f" {gt_closed_year.type}",
        )
        self.assert_no_data_integrity_errors(gt_closed_year)

    def _prepare_closed_periods(
        self,
        *,
        data_initialized_at: datetime.datetime,
        periods_closed: (
            Iterable[datetime.datetime | tuple[datetime.datetime, datetime.datetime | None]] | None
        ) = None,
        periods_closed_months: Iterable[datetime.datetime] | None = None,
    ):

        ClosedPeriodsSetup(self.business).setup(
            initialized_at=data_initialized_at,
            periods_closed={
                ClosePeriodType.DAY: periods_closed,
                ClosePeriodType.MONTH: periods_closed_months,
            },
        )

    def get_grand_total(
        self,
        period_type: ClosePeriodType,
        start: datetime.datetime,
        end: datetime.datetime,
    ) -> GrandTotal:
        return GrandTotal.objects.get(
            business_id=self.business.id,
            period_start=start,
            period_end=end,
            type=period_type,
        )

    def assert_day_month_closed(self, gt: GrandTotal):
        dateiso = gt.period_start.date().isoformat()
        additional_info_matchers = {
            ClosePeriodType.DAY: dateiso,
            ClosePeriodType.MONTH: dateiso.rsplit("-", maxsplit=1)[0],
        }
        self.assertTrue(
            JET.objects.filter(
                business_id=self.business.id,
                event_code=JETEventCode.DAY_OR_MONTH_CLOSED,
                additional_info__contains=additional_info_matchers[gt.type],
            ).exists(),
            f"day/month closed not reported for period"
            f" '{gt.period_start}'-'{gt.period_end}' {gt.type}",
        )

    def assert_no_data_integrity_errors(self, gt: GrandTotal):
        fiscal_archive = FiscalArchive.objects.get(grand_total_id=gt.id)
        self.assertFalse(
            JET.objects.filter(
                business_id=self.business.id,
                event_code=JETEventCode.INTEGRITY_FAILURE,
                additional_info__contains=fiscal_archive.id,
            ).exists(),
            f"integrity failure reported for period '{gt.period_start}'-'{gt.period_end}'",
        )

    def change_business_timezone(self, tzname: str) -> datetime.tzinfo | None:
        self.business.time_zone_name = tzname
        self.business.save()
        self.business_timezone = self.business.get_timezone()
        return self.business_timezone

    @parameterized.expand(
        [
            ("UTC to Europe/Paris", "Europe/Paris", 0),
            ("America/Guyana to Europe/Paris", "Europe/Paris", 4),
            ("Indian/Reunion to Europe/Paris", "Europe/Paris", 20),
            ("Europe/Paris to America/Martinique", "America/Martinique", 22),
        ]
    )
    def test_business_changed_timezone_reflected_in_closed_day_period(
        self,
        _: str,
        tz_to,
        period_hour_in_utc: int,
    ):
        utc = datetime.timezone.utc
        self._prepare_closed_periods(
            data_initialized_at=datetime.datetime(2024, 9, 6, 7, 18, tzinfo=utc),
            periods_closed=(
                datetime.datetime(2024, 9, 8, period_hour_in_utc, tzinfo=utc),
                datetime.datetime(2024, 9, 7, period_hour_in_utc, tzinfo=utc),
                datetime.datetime(2024, 9, 6, period_hour_in_utc, tzinfo=utc),
            ),
        )
        self.change_business_timezone(tz_to)
        server_time = datetime.datetime(2024, 9, 11, 0, 3, tzinfo=utc)

        with freeze_time(server_time, tick=True):
            ClosePeriodService.close_period(
                self.business,
                closing_time=datetime.datetime(2024, 9, 9, period_hour_in_utc, tzinfo=utc),
                period_type=ClosePeriodType.DAY,
            )

        gt_latest = self.get_grand_total(
            ClosePeriodType.DAY,
            datetime.datetime(2024, 9, 9, period_hour_in_utc, tzinfo=utc),
            datetime.datetime(2024, 9, 10, period_hour_in_utc, tzinfo=utc),
        )
        self.assert_day_month_closed(gt_latest)
        self.assert_no_data_integrity_errors(gt_latest)

    @parameterized.expand(
        [
            ("UTC to Europe/Paris", "Europe/Paris", 0),
            ("America/Guyana to Europe/Paris", "Europe/Paris", 4),
            ("Indian/Reunion to Europe/Paris", "Europe/Paris", 20),
            ("Europe/Paris to America/Martinique", "America/Martinique", 22),
        ]
    )
    def test_business_changed_timezone_reflected_in_closed_month_period(
        self,
        _: str,
        tz_to,
        period_hour_in_utc: int,
    ):
        utc = datetime.timezone.utc
        self._prepare_closed_periods(
            data_initialized_at=datetime.datetime(2024, 7, 15, 7, 18, tzinfo=utc),
            periods_closed=[
                *[
                    datetime.datetime(2024, 7, day, period_hour_in_utc, tzinfo=utc)
                    for day in range(15, 31 + 1)
                ],
                *[
                    datetime.datetime(2024, 8, day, period_hour_in_utc, tzinfo=utc)
                    for day in range(1, 31 + 1)
                ],
            ],
            periods_closed_months=(datetime.datetime(2024, 7, 1, period_hour_in_utc, tzinfo=utc),),
        )
        self.change_business_timezone(tz_to)
        server_time = datetime.datetime(2024, 9, 2, 0, 3, tzinfo=utc)

        with freeze_time(server_time, tick=True):
            ClosePeriodService.close_period(
                self.business,
                closing_time=datetime.datetime(2024, 8, 1, period_hour_in_utc, tzinfo=utc),
                period_type=ClosePeriodType.MONTH,
            )

        gt_latest = self.get_grand_total(
            ClosePeriodType.MONTH,
            datetime.datetime(2024, 8, 1, period_hour_in_utc, tzinfo=utc),
            datetime.datetime(2024, 9, 1, period_hour_in_utc, tzinfo=utc),
        )
        self.assert_day_month_closed(gt_latest)
        self.assert_no_data_integrity_errors(gt_latest)

    def test_sanitizes_initialization_time(self):
        """
        Initialization time has a high chance of having non-zeroed seconds and/or microseconds,
        which may affect calculations for closed DAY periods when closing a MONTH. Such times must
        be sanitized before being used as a period's start time similar to how period's end times
        are sanitized when closing subsequent periods.

        This test is based on fr-46781 from prod.
        """
        utc = datetime.timezone.utc
        initialized_at = datetime.datetime(2024, 10, 1, 7, 44, 20, 567858, tzinfo=utc)
        self._prepare_closed_periods(
            data_initialized_at=initialized_at,
            periods_closed=[
                datetime.datetime(2024, 9, 30, 22, tzinfo=utc),
                *[
                    datetime.datetime(2024, 10, day_in_month, 22, tzinfo=utc)
                    for day_in_month in range(1, 26)
                ],
                # DST gets off
                (
                    datetime.datetime(2024, 10, 26, 22, tzinfo=utc),
                    datetime.datetime(2024, 10, 27, 23, tzinfo=utc),
                ),
                *[
                    datetime.datetime(2024, 10, day_in_month, 23, tzinfo=utc)
                    for day_in_month in range(27, 32)
                ],
            ],
        )

        with freeze_time(datetime.datetime(2024, 11, 2, 2, 23, tzinfo=utc), tick=True):
            gt = ClosePeriodService.close_period(
                self.business,
                closing_time=initialized_at,
                period_type=ClosePeriodType.MONTH,
            )

        assert (gt.period_start, gt.period_end) == (
            datetime.datetime(2024, 10, 1, 0, tzinfo=utc),
            datetime.datetime(2024, 11, 1, 0, tzinfo=utc),
        )

    @override_eppo_feature_flag({FrenchCertificationYearPeriodsRefactor.flag_name: True})
    def test_close_year_with_month_with_moved_period_start(self):
        ClosedPeriodsSetup(self.business).setup(
            initialized_at=datetime.datetime(
                2024, 9, 19, 9, 40, 25, 824521, tzinfo=datetime.timezone.utc
            ),
            periods_closed={
                ClosePeriodType.MONTH: (
                    datetime.datetime(2024, 9, 1, 4, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 10, 1, 4, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 11, 1, 4, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 12, 1, 4, 0, tzinfo=datetime.timezone.utc),
                ),
            },
        )
        ClosePeriodService.close_period(
            self.business,
            closing_time=datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc),
            period_type=ClosePeriodType.YEAR,
        )
        assert (
            GrandTotal.objects.filter(
                type=ClosePeriodType.YEAR, business_id=self.business.id, period_start__year=2024
            ).exists()
            is True
        )


class TestClosePeriodFactories(TestCase):
    def setUp(self):
        self.business = baker.make(Business)

    def create_prepayment_receipt(self, _datetime):
        basket_id = '66d4fbef-ce86-4515-90ea-e51fd39bc6ca'
        items_data = [
            {
                'id': '2ea07a80-47cc-49b8-baa4-a52186e38bec',
                'deleted': None,
                'basket_id': basket_id,
                'type': 'service',
                'order': 0,
                'name_line_1': 'pp 51% (30m)',
                'name_line_2': 'Czw, 12 października 2023 16:00, Kasey Schwartz',
                'quantity': 1,
                'item_price': 2999,
                'discount_rate': 10,
                'discounted_item_price': 2699,
                'tax_amount': 450,
                'tax_rate': 2000,
                'tax_type': 'included',
                'total': 2699,
                'net_total': 2249,
                'gross_total': 2699,
                'discounted_total': 2699,
                'net_total_wo_discount': 2499,
            },
            {
                'id': '9b7c00c4-3202-4c3c-9163-f3b01fffeb64',
                'deleted': None,
                'basket_id': basket_id,
                'type': 'service',
                'order': 1,
                'name_line_1': '0% podatku (30m)',
                'name_line_2': 'Czw, 12 października 2023 16:30, Kasey Schwartz',
                'quantity': 1,
                'item_price': 10000,
                'discount_rate': 10,
                'discounted_item_price': 9000,
                'tax_amount': 0,
                'tax_rate': 0,
                'tax_type': 'included',
                'total': 9000,
                'net_total': 9000,
                'gross_total': 9000,
                'discounted_total': 9000,
                'net_total_wo_discount': 10000,
            },
            {
                'id': '7a4c6d5e-b250-4f7a-ba99-171dc308c4b0',
                'deleted': None,
                'basket_id': basket_id,
                'type': 'service',
                'order': 2,
                'name_line_1': 'PP 30% (30m)',
                'name_line_2': 'Czw, 12 października 2023 17:00, Kasey Schwartz',
                'quantity': 1,
                'item_price': 10000,
                'discount_rate': 10,
                'discounted_item_price': 9000,
                'tax_amount': 1500,
                'tax_rate': 2000,
                'tax_type': 'included',
                'total': 9000,
                'net_total': 7500,
                'gross_total': 9000,
                'discounted_total': 9000,
                'net_total_wo_discount': 8333,
            },
            {
                'id': '0bf960ad-eb4b-49bc-862c-a7d1ddb665ec',
                'deleted': None,
                'basket_id': basket_id,
                'type': 'service',
                'order': 3,
                'name_line_1': 'null tax rate (30m)',
                'name_line_2': 'Czw, 12 października 2023 17:30, Kasey Schwartz',
                'quantity': 1,
                'item_price': 10000,
                'discount_rate': 10,
                'discounted_item_price': 9000,
                'tax_amount': 1500,
                'tax_rate': 2000,
                'tax_type': 'included',
                'total': 9000,
                'net_total': 7500,
                'gross_total': 9000,
                'discounted_total': 9000,
                'net_total_wo_discount': 8333,
            },
            {
                'id': '06b74f0e-b97b-4865-9160-fa7236f6066f',
                'deleted': None,
                'basket_id': basket_id,
                'type': 'service',
                'order': 4,
                'name_line_1': '8% podatku 50%pp (30m)',
                'name_line_2': 'Pt, 13 października 2023 12:00, Kasey Schwartz',
                'quantity': 1,
                'item_price': 11000,
                'discount_rate': 10,
                'discounted_item_price': 9900,
                'tax_amount': 733,
                'tax_rate': 800,
                'tax_type': 'included',
                'total': 9900,
                'net_total': 9167,
                'gross_total': 9900,
                'discounted_total': 9900,
                'net_total_wo_discount': 10185,
            },
        ]
        basket_payment_data = {
            'id': '3ff2b632-6209-4a40-83eb-20931de86cca',
            'basket_id': basket_id,
            'amount': 9027,
            'payment_method': 'card',
            'payment_provider_code': 'stripe',
            'balance_transaction_id': '78326b32-3049-4937-a69b-c3de191ca76b',
            'status': 'success',
            'user_id': 7642,
            'type': 'payment',
            'error_code': None,
            'metadata': {},
            'source': 'prepayment',
        }

        with freeze_time(_datetime):
            baker.make(Basket, id=basket_id, business_id=self.business.id)
            basket_payment = TestBasketPaymentEntity(**basket_payment_data)
            return FiscalReceiptService.create_fiscal_receipt(
                basket_id=basket_id,
                operator_id=None,
                basket_data={
                    'number_of_lines': 4,
                    'basket_items_data': {
                        '2ea07a80-47cc-49b8-baa4-a52186e38bec': get_data_from_basket_item(
                            basket_item=BasketItem.objects.create(**items_data[0]),
                            prepayment_amount=1377,
                        ),
                        '9b7c00c4-3202-4c3c-9163-f3b01fffeb64': get_data_from_basket_item(
                            basket_item=BasketItem.objects.create(**items_data[1]),
                            prepayment_amount=0,
                        ),
                        '7a4c6d5e-b250-4f7a-ba99-171dc308c4b0': get_data_from_basket_item(
                            basket_item=BasketItem.objects.create(**items_data[2]),
                            prepayment_amount=2700,
                        ),
                        '0bf960ad-eb4b-49bc-862c-a7d1ddb665ec': get_data_from_basket_item(
                            basket_item=BasketItem.objects.create(**items_data[3]),
                            prepayment_amount=0,
                        ),
                        '06b74f0e-b97b-4865-9160-fa7236f6066f': get_data_from_basket_item(
                            basket_item=BasketItem.objects.create(**items_data[4]),
                            prepayment_amount=4950,
                        ),
                    },
                    'basket_payments_data': {
                        '3ff2b632-6209-4a40-83eb-20931de86cca': get_data_from_basket_payment(
                            basket_payment=basket_payment
                        )
                    },
                },
                is_prepayment=True,
                loyalty_card_data={},
            )

    def test_correct_totals_for_period_with_prepayment(self):
        _datetime = datetime.datetime(2023, 10, 12, tzinfo=self.business.get_timezone())
        fc_seller_recipe.make(business=self.business)
        self.create_prepayment_receipt(_datetime)
        _create_receipt(self.business, _datetime)

        expected_totals_per_tax_rate_dicts_prepayment = [
            {'tax_rate': 0, 'net_total': 0, 'gross_total': 0},
            {'tax_rate': 800, 'net_total': 4584, 'gross_total': 4950},
            {'tax_rate': 2000, 'net_total': 3397, 'gross_total': 4077},
        ]

        expected_prepayment_net_total = sum(
            x['net_total'] for x in expected_totals_per_tax_rate_dicts_prepayment
        )
        expected_prepayment_gross_total = sum(
            x['gross_total'] for x in expected_totals_per_tax_rate_dicts_prepayment
        )

        expected_totals_per_tax_rate_dicts_receipt = [
            {'tax_rate': 1000, 'net_total': 5364, 'gross_total': 5900},
            {'tax_rate': 2000, 'net_total': 1500, 'gross_total': 1800},
        ]

        expected_receipt_net_total = sum(
            x['net_total'] for x in expected_totals_per_tax_rate_dicts_receipt
        )
        expected_receipt_gross_total = sum(
            x['gross_total'] for x in expected_totals_per_tax_rate_dicts_receipt
        )

        expected_net_total = expected_prepayment_net_total + expected_receipt_net_total
        expected_gross_total = expected_prepayment_gross_total + expected_receipt_gross_total
        expected_cumulative = expected_gross_total

        expected_totals_per_tax_rate_dicts = [
            {
                'tax_rate': 800,
                'net_total': 4584,
                'gross_total': 4950,
            },
            {
                'tax_rate': 1000,
                'net_total': 5364,
                'gross_total': 5900,
            },
            {
                'tax_rate': 2000,
                'net_total': 3397 + 1500,
                'gross_total': 4077 + 1800,
            },
        ]

        grand_total_of_the_day = DailyClosePeriodFactory(
            self.business,
            _datetime,
        ).create_grand_total()

        actual_totals_per_tax_rate_dicts = grand_total_of_the_day.totals_per_tax_rate.values(
            'tax_rate', 'net_total', 'gross_total'
        )
        self.assertEqual(expected_net_total, grand_total_of_the_day.net_total)
        self.assertEqual(expected_gross_total, grand_total_of_the_day.gross_total)
        self.assertEqual(expected_cumulative, grand_total_of_the_day.cumulative_gross_total)
        self.assertCountEqual(expected_totals_per_tax_rate_dicts, actual_totals_per_tax_rate_dicts)

    def test_cancelled_prepayment_sums_to_zero(self):
        creation_datetime = datetime.datetime(2023, 10, 12, tzinfo=self.business.get_timezone())
        creation_datetime2 = datetime.datetime(2023, 10, 12, 1, tzinfo=self.business.get_timezone())
        cancellation_datetime = datetime.datetime(
            2023, 10, 12, 15, tzinfo=self.business.get_timezone()
        )
        cancellation_datetime2 = datetime.datetime(
            2023, 10, 12, 16, tzinfo=self.business.get_timezone()
        )
        fc_seller_recipe.make(business=self.business)
        fiscal_prepayment = self.create_prepayment_receipt(creation_datetime)
        fiscal_receipt = _create_receipt(self.business, creation_datetime2)

        self._create_cancellation_receipt(fiscal_prepayment, cancellation_datetime)
        self._create_cancellation_receipt(fiscal_receipt, cancellation_datetime2)
        grand_total_of_the_day = DailyClosePeriodFactory(
            self.business,
            creation_datetime,
        ).create_grand_total()

        actual_totals_per_tax_rate_dicts = grand_total_of_the_day.totals_per_tax_rate.values(
            'tax_rate', 'net_total', 'gross_total'
        )
        expected_totals_per_tax_rate_dicts = [
            {
                'tax_rate': 800,
                'net_total': 0,
                'gross_total': 0,
            },
            {
                'tax_rate': 1000,
                'net_total': 0,
                'gross_total': 0,
            },
            {
                'tax_rate': 2000,
                'net_total': 0,
                'gross_total': 0,
            },
        ]
        self.assertEqual(0, grand_total_of_the_day.net_total)
        self.assertEqual(0, grand_total_of_the_day.gross_total)
        self.assertEqual(0, grand_total_of_the_day.cumulative_gross_total)
        self.assertCountEqual(expected_totals_per_tax_rate_dicts, actual_totals_per_tax_rate_dicts)

    def test_close_first_day_ever_no_receipts_creates_grand_total_with_zeros(self):
        day_start = datetime.datetime(2022, 12, 29, tzinfo=self.business.get_timezone())

        grand_total_of_the_day = DailyClosePeriodFactory(
            self.business,
            day_start,
        ).create_grand_total()

        self.assertEqual(0, grand_total_of_the_day.net_total)
        self.assertEqual(0, grand_total_of_the_day.gross_total)
        self.assertEqual(self.business.id, grand_total_of_the_day.business_id)
        self.assertEqual(0, grand_total_of_the_day.cumulative_gross_total)

    def test_close_first_day_ever_with_receipts_creates_grand_total_with_proper_fields(self):
        fc_seller_recipe.make(business=self.business)
        _datetime = datetime.datetime(2022, 12, 29, tzinfo=self.business.get_timezone())
        _create_receipt(self.business, _datetime)
        _create_receipt(self.business, _datetime)

        grand_total_of_the_day = DailyClosePeriodFactory(
            self.business,
            _datetime,
        ).create_grand_total()

        self.assertEqual(13728, grand_total_of_the_day.net_total)
        self.assertEqual(15400, grand_total_of_the_day.gross_total)
        self.assertEqual(self.business.id, grand_total_of_the_day.business_id)
        self.assertEqual(15400, grand_total_of_the_day.cumulative_gross_total)

    def test_close_second_day_with_receipts_increases_cumulative(self):
        fc_seller_recipe.make(business=self.business)
        first_day = datetime.datetime(2022, 12, 29, tzinfo=self.business.get_timezone())
        second_day = datetime.datetime(2022, 12, 30, tzinfo=self.business.get_timezone())
        _create_receipt(self.business, first_day.replace(hour=15, minute=31))
        _create_receipt(self.business, first_day.replace(hour=15, minute=45))
        _create_receipt(self.business, second_day.replace(hour=15, minute=31))
        _create_receipt(self.business, second_day.replace(hour=16, minute=52))
        DailyClosePeriodFactory(self.business, first_day).create_grand_total()

        second_day_grand_total = DailyClosePeriodFactory(
            self.business,
            second_day,
        ).create_grand_total()

        self.assertEqual(13728, second_day_grand_total.net_total)
        self.assertEqual(15400, second_day_grand_total.gross_total)
        self.assertEqual(self.business.id, second_day_grand_total.business_id)
        self.assertEqual(30800, second_day_grand_total.cumulative_gross_total)

    def test_closing_same_day_throws_integrity_error(self):
        fc_seller_recipe.make(business=self.business)
        closing_time = datetime.datetime(2022, 12, 29, tzinfo=datetime.UTC)
        _create_receipt(self.business, closing_time.date())

        DailyClosePeriodFactory(self.business, closing_time).create_grand_total()

        with self.assertRaises(IntegrityError):
            DailyClosePeriodFactory(self.business, closing_time).create_grand_total()

    def test_grand_total_has_proper_start_and_end(self):
        # TODO add test with different timezones
        # TODO add test with different hours
        fc_seller_recipe.make(business=self.business)
        business_timezone = self.business.get_timezone()
        day = datetime.datetime(2022, 12, 29, 15, 30, 29, tzinfo=business_timezone)
        expected_start = datetime.datetime(2022, 12, 29, tzinfo=business_timezone)
        expected_end = datetime.datetime(2022, 12, 30, tzinfo=business_timezone)
        _create_receipt(self.business, day)

        grand_total: GrandTotal = DailyClosePeriodFactory(
            self.business,
            expected_start,
        ).create_grand_total()

        self.assertEqual(grand_total.period_start, expected_start)
        self.assertEqual(grand_total.period_end, expected_end)

    def test_grand_total_with_cancellation_fiscal_receipt_decreases_cumulative_gross_total(self):
        fc_seller_recipe.make(business=self.business)
        business_timezone = self.business.get_timezone()
        _datetime = datetime.datetime(2022, 12, 29, 15, 30, 29, tzinfo=business_timezone)
        cancellation_time = datetime.datetime(2022, 12, 29, 15, 30, 58, tzinfo=business_timezone)
        receipt = _create_receipt(self.business, _datetime)
        self._create_cancellation_receipt(receipt, cancellation_time)

        grand_total: GrandTotal = DailyClosePeriodFactory(
            self.business,
            _datetime.replace(hour=0, minute=0, second=0),
        ).create_grand_total()

        self.assertEqual(0, grand_total.cumulative_gross_total)

    def test_cancellation_causes_negative_cumulative_gross_total(self):
        fc_seller_recipe.make(business=self.business)
        business_timezone = self.business.get_timezone()
        _datetime = datetime.datetime(2022, 12, 29, 15, 30, 29, tzinfo=business_timezone)
        cancellation_time = _datetime + datetime.timedelta(days=1)
        receipt = _create_receipt(self.business, _datetime)
        self._create_cancellation_receipt(receipt, cancellation_time)

        grand_total: GrandTotal = DailyClosePeriodFactory(
            self.business,
            cancellation_time.replace(hour=0, minute=0, second=0),
        ).create_grand_total()

        self.assertEqual(grand_total.cumulative_gross_total, -receipt.total_paid_value)

    def test_daily_closure_generate_proper_tax_rates(self):
        utc = datetime.timezone.utc
        fc_seller_recipe.make(business=self.business)
        day = datetime.date(2022, 12, 15)
        _create_receipt(self.business, day)
        _create_receipt(self.business, day)
        expected_totals_per_tax_rate_dicts = [
            {'tax_rate': 1000, 'net_total': 10728, 'gross_total': 11800},
            {'tax_rate': 2000, 'net_total': 3000, 'gross_total': 3600},
        ]

        daily_grand_total = DailyClosePeriodFactory(
            self.business, datetime.datetime(2022, 12, 15, tzinfo=utc)
        ).create_grand_total()

        actual_totals_per_tax_rate_dicts = daily_grand_total.totals_per_tax_rate.values(
            'tax_rate', 'net_total', 'gross_total'
        )
        self.assertCountEqual(expected_totals_per_tax_rate_dicts, actual_totals_per_tax_rate_dicts)

    def test_daily_closure_generate_proper_tax_rates_for_cancelled_receipts(self):
        utc = datetime.timezone.utc
        fc_seller_recipe.make(business=self.business)
        day = datetime.date(2022, 12, 15)
        receipt = _create_receipt(self.business, day)
        self._create_cancellation_receipt(receipt, day)
        expected_totals_per_tax_rate_dicts = [
            {'tax_rate': 1000, 'net_total': 0, 'gross_total': 0},
            {'tax_rate': 2000, 'net_total': 0, 'gross_total': 0},
        ]

        daily_grand_total = DailyClosePeriodFactory(
            self.business, datetime.datetime(2022, 12, 15, tzinfo=utc)
        ).create_grand_total()

        actual_totals_per_tax_rate_dicts = daily_grand_total.totals_per_tax_rate.values(
            'tax_rate', 'net_total', 'gross_total'
        )
        self.assertCountEqual(expected_totals_per_tax_rate_dicts, actual_totals_per_tax_rate_dicts)

    def test_close_month_no_receipts_creates_grand_total_with_zeros(self):
        business_tz = self.business.get_timezone()
        month = datetime.datetime(2022, 12, 1, tzinfo=business_tz)
        create_data_initialization_event(self.business.id, month)

        monthly_grand_total = MonthlyClosePeriodFactory(self.business, month).create_grand_total()

        self.assertEqual(0, monthly_grand_total.net_total)
        self.assertEqual(0, monthly_grand_total.gross_total)
        self.assertEqual(self.business.id, monthly_grand_total.business_id)
        self.assertEqual(0, monthly_grand_total.cumulative_gross_total)

    def test_month_grand_total_with_many_daily_grand_totals_has_proper_values(self):
        business_tz = self.business.get_timezone()
        create_grand_total(
            business_id=self.business.id,
            net_total=10000,
            gross_total=12300,
            cumulative_gross_total=12300,
            type=ClosePeriodType.DAY,
            period_start=datetime.datetime(2022, 12, 12, tzinfo=business_tz),
            period_end=datetime.datetime(2022, 12, 13, tzinfo=business_tz),
            software_version='3',
        )
        create_grand_total(
            business_id=self.business.id,
            net_total=20000,
            gross_total=22000,
            cumulative_gross_total=34300,
            type=ClosePeriodType.DAY,
            period_start=datetime.datetime(2022, 12, 13, tzinfo=business_tz),
            period_end=datetime.datetime(2022, 12, 14, tzinfo=business_tz),
            software_version='3',
        )
        create_grand_total(
            business_id=self.business.id,
            net_total=-10000,
            gross_total=-11000,
            cumulative_gross_total=23300,
            type=ClosePeriodType.DAY,
            period_start=datetime.datetime(2022, 12, 14, tzinfo=business_tz),
            period_end=datetime.datetime(2022, 12, 15, tzinfo=business_tz),
            software_version='3',
        )
        data_initialized_at = datetime.datetime(2022, 12, 1, tzinfo=business_tz)
        create_data_initialization_event(self.business.id, data_initialized_at)

        monthly_grand_total = MonthlyClosePeriodFactory(
            self.business,
            data_initialized_at,
        ).create_grand_total()

        self.assertEqual(ClosePeriodType.MONTH, monthly_grand_total.type)
        self.assertEqual(20000, monthly_grand_total.net_total)
        self.assertEqual(23300, monthly_grand_total.gross_total)
        self.assertEqual(23300, monthly_grand_total.cumulative_gross_total)

    def test_year_grand_total_with_many_monthly_grand_totals_has_proper_values(self):
        business_tz = self.business.get_timezone()
        create_grand_total(
            business_id=self.business.id,
            net_total=100000,
            gross_total=123000,
            cumulative_gross_total=123000,
            type=ClosePeriodType.MONTH,
            period_start=datetime.datetime(2022, 1, 1, tzinfo=business_tz),
            period_end=datetime.datetime(2022, 2, 1, tzinfo=business_tz),
            software_version='3',
        )
        create_grand_total(
            business_id=self.business.id,
            net_total=200000,
            gross_total=220000,
            cumulative_gross_total=343000,
            type=ClosePeriodType.MONTH,
            period_start=datetime.datetime(2022, 2, 1, tzinfo=business_tz),
            period_end=datetime.datetime(2022, 3, 1, tzinfo=business_tz),
            software_version='3',
        )
        create_grand_total(
            business_id=self.business.id,
            net_total=-100000,
            gross_total=-110000,
            cumulative_gross_total=233000,
            type=ClosePeriodType.MONTH,
            period_start=datetime.datetime(2022, 3, 1, tzinfo=business_tz),
            period_end=datetime.datetime(2022, 4, 1, tzinfo=business_tz),
            software_version='3',
        )

        yearly_grand_total = YearlyClosePeriodFactory(
            self.business,
            datetime.datetime(2022, 1, 1, tzinfo=business_tz),
        ).create_grand_total()

        self.assertEqual(ClosePeriodType.YEAR, yearly_grand_total.type)
        self.assertEqual(200000, yearly_grand_total.net_total)
        self.assertEqual(233000, yearly_grand_total.gross_total)
        self.assertEqual(233000, yearly_grand_total.cumulative_gross_total)

    def test_yearly_closure_generate_proper_tax_rates(self):
        business_tz = self.business.get_timezone()
        totals_per_tax_rate_dicts = [
            {'tax_rate': 1000, 'net_total': 40000, 'gross_total': 44000},
            {'tax_rate': 2300, 'net_total': 50000, 'gross_total': 61500},
        ]
        GrandTotal.objects.create(
            business_id=self.business.id,
            net_total=90000,
            gross_total=105500,
            cumulative_gross_total=105500,
            type=ClosePeriodType.MONTH,
            period_start=datetime.datetime(2022, 12, 1, tzinfo=business_tz),
            period_end=datetime.datetime(2023, 1, 1, tzinfo=business_tz),
            total_gross_by_tax_rate=[TotalPerTaxRate(**d) for d in totals_per_tax_rate_dicts],
            software_version='3',
        )

        yearly_grand_total = YearlyClosePeriodFactory(
            self.business, datetime.datetime(2022, 1, 1, tzinfo=business_tz)
        ).create_grand_total()

        actual_totals_per_tax_rate_dicts = yearly_grand_total.totals_per_tax_rate.values(
            'tax_rate', 'net_total', 'gross_total'
        )
        self.assertCountEqual(totals_per_tax_rate_dicts, actual_totals_per_tax_rate_dicts)

    def test_daily_closure_does_not_skip_first_and_last_receipt(self):
        business_tz = self.business.get_timezone()
        fc_seller_recipe.make(business=self.business)
        beginning_of_day = datetime.datetime(2022, 10, 15, tzinfo=business_tz)
        last_moment_of_day = datetime.datetime(
            2022, 10, 16, tzinfo=business_tz
        ) - datetime.timedelta(microseconds=1)
        _create_receipt(self.business, beginning_of_day)
        _create_receipt(self.business, last_moment_of_day)

        grand_total = DailyClosePeriodFactory(
            self.business,
            beginning_of_day,
        ).create_grand_total()

        self.assertEqual(13728, grand_total.net_total)
        self.assertEqual(15400, grand_total.gross_total)

    def test_monthly_closure_does_not_skip_first_and_last_day(self):
        business_timezone = self.business.get_timezone()
        create_grand_total(
            business_id=self.business.id,
            net_total=10000,
            gross_total=12300,
            cumulative_gross_total=12300,
            type=ClosePeriodType.DAY,
            period_start=datetime.datetime(2022, 5, 1, tzinfo=business_timezone),
            period_end=datetime.datetime(2022, 5, 2, tzinfo=business_timezone),
            software_version='3',
        )
        create_grand_total(
            business_id=self.business.id,
            net_total=10000,
            gross_total=12300,
            cumulative_gross_total=12300,
            type=ClosePeriodType.DAY,
            period_start=datetime.datetime(2022, 5, 31, tzinfo=business_timezone),
            period_end=datetime.datetime(2022, 6, 1, tzinfo=business_timezone),
            software_version='3',
        )
        data_initialized_at = datetime.datetime(2022, 5, 1, tzinfo=business_timezone)
        create_data_initialization_event(self.business.id, data_initialized_at)

        grand_total = MonthlyClosePeriodFactory(
            self.business,
            data_initialized_at,
        ).create_grand_total()

        self.assertEqual(20000, grand_total.net_total)
        self.assertEqual(24600, grand_total.gross_total)

    def test_yearly_closure_does_not_skip_first_and_last_month(self):
        business_timezone = self.business.get_timezone()
        create_grand_total(
            business_id=self.business.id,
            net_total=10000,
            gross_total=12300,
            cumulative_gross_total=12300,
            type=ClosePeriodType.MONTH,
            period_start=datetime.datetime(2022, 1, 1, tzinfo=business_timezone),
            period_end=datetime.datetime(2022, 2, 1, tzinfo=business_timezone),
            software_version='3',
        )
        create_grand_total(
            business_id=self.business.id,
            net_total=10000,
            gross_total=12300,
            cumulative_gross_total=12300,
            type=ClosePeriodType.MONTH,
            period_start=datetime.datetime(2022, 12, 1, tzinfo=business_timezone),
            period_end=datetime.datetime(2023, 1, 1, tzinfo=business_timezone),
            software_version='3',
        )

        grand_total = YearlyClosePeriodFactory(
            self.business,
            datetime.datetime(2022, 1, 1, tzinfo=business_timezone),
        ).create_grand_total()

        self.assertEqual(20000, grand_total.net_total)
        self.assertEqual(24600, grand_total.gross_total)

    @staticmethod
    def _create_cancellation_receipt(fiscal_receipt: FiscalReceipt, cancellation_time):
        with freeze_time(cancellation_time):
            return FiscalReceiptService.cancel_fiscal_receipt(
                fiscal_receipt, reason='', operator_id=None
            )
