import calendar
import datetime
import logging
from abc import ABC, abstractmethod
from collections import defaultdict
from collections.abc import Iterable

from dateutil.relativedelta import relativedelta
from django.db import transaction
from django.db.models import Sum, F, Q
from django.db.models.functions import Coalesce

from lib.feature_flag.adapter import UserData
from lib.feature_flag.bug import FrenchCertificationYearPeriodsRefactor
from webapps.business.models import Business
from webapps.french_certification.enums import (
    FiscalReceiptType,
    ClosePeriodType,
)
from webapps.french_certification.exceptions import (
    CannotClosePeriod,
    PeriodInTheFutureError,
)
from webapps.french_certification.models import (
    FiscalReceipt,
    FiscalReceiptDuplicate,
    JET,
    GrandTotal,
    TotalPerTaxRate,
    FiscalArchive,
)
from webapps.french_certification.services.fiscal_archive import FiscalArchiveService
from webapps.french_certification.services.integrity import IntegrityService
from webapps.french_certification.services.jet import JETService
from webapps.french_certification.services.software_version import SoftwareVersionService
from webapps.french_certification.utils import get_start_closing_from

log = logging.getLogger(__name__)


class ClosePeriodService:
    @classmethod
    def close_period(
        cls,
        business: Business,
        closing_time: datetime.datetime,
        period_type: ClosePeriodType,
    ) -> GrandTotal:
        factory = cls.get_factory(business, closing_time, period_type)
        factory.check_period_not_in_future()
        factory.check_previous_period_is_closed()
        factory.check_period_not_older_than_threshold()
        factory.check_all_previous_lesser_periods_closed()
        with transaction.atomic():
            grand_total = factory.create_grand_total()
            factory.verify_period_chain()
            factory.add_period_closed_jet_event()
            fiscal_archive = factory.create_fiscal_archive(grand_total=grand_total)
            factory.send_fiscal_archive_data_to_storage(fiscal_archive=fiscal_archive)
        return grand_total

    @staticmethod
    def get_factory(
        business: Business,
        closing_time: datetime.datetime,
        period_type: ClosePeriodType,
    ):
        factories = {
            ClosePeriodType.DAY: DailyClosePeriodFactory,
            ClosePeriodType.MONTH: MonthlyClosePeriodFactory,
            ClosePeriodType.YEAR: YearlyClosePeriodFactory,
        }
        # pylint: disable=C0209
        log.info(
            "closing '{}' '{}' period for business {} will take timezone into account".format(
                closing_time.isoformat(), period_type, business.id
            )
        )

        factory = factories[period_type]
        return factory(business, closing_time)


class ClosePeriodFactory(ABC):
    def __init__(self, business, _time: datetime.datetime, close_period_type: ClosePeriodType):
        self.business = business
        self._time = _time
        self._date = _time.date()
        self.close_period_type = close_period_type

    @property
    def start_closing_from(self) -> datetime.datetime:
        closing_time = get_start_closing_from(self.business.id)
        return closing_time

    def check_previous_period_is_closed(self):
        if self._should_previous_period_be_closed() and not self._is_previous_period_closed():
            raise CannotClosePeriod(self.business.id, self.period_start, self.close_period_type)

    def check_period_not_older_than_threshold(self):
        if self.start_closing_from >= self.period_end:
            raise CannotClosePeriod(self.business.id, self.period_start, self.close_period_type)

    @abstractmethod
    def check_all_previous_lesser_periods_closed(self): ...

    def check_period_not_in_future(self):
        if self.period_end > self.business.tznow:
            raise PeriodInTheFutureError(
                business_id=self.business.id,
                period_type=self.close_period_type,
                period_start=self.period_start,
                period_end=self.period_end,
            )

    @property
    @abstractmethod
    def period_start(self) -> datetime.datetime: ...

    @property
    @abstractmethod
    def period_end(self) -> datetime.datetime: ...

    @abstractmethod
    def create_grand_total(self) -> GrandTotal: ...

    def verify_period_chain(self):
        fiscal_receipt_qs = FiscalReceipt.objects.filter(
            business_id=self.business.id,
            created__gte=self.period_start,
            created__lt=self.period_end,
        )
        IntegrityService.verify_chain(fiscal_receipt_qs)

        fiscal_receipt_duplicate_qs = FiscalReceiptDuplicate.objects.filter(
            business_id=self.business.id,
            created__gte=self.period_start,
            created__lt=self.period_end,
        )
        IntegrityService.verify_chain(fiscal_receipt_duplicate_qs)

        jet_qs = JET.objects.filter(
            business_id=self.business.id,
            created__gte=self.period_start,
            created__lt=self.period_end,
        )
        IntegrityService.verify_chain(jet_qs)

        previous_grand_total_qs = GrandTotal.objects.filter(
            business_id=self.business.id,
            period_end=self.period_start,
            type=self.close_period_type,
        )
        IntegrityService.verify_chain(previous_grand_total_qs)

        previous_fiscal_archive_qs = FiscalArchive.objects.filter(
            business_id=self.business.id,
            grand_total__period_end=self.period_start,
            type=self.close_period_type,
        )
        IntegrityService.verify_chain(previous_fiscal_archive_qs)

    @abstractmethod
    def add_period_closed_jet_event(self): ...

    @abstractmethod
    def _should_previous_period_be_closed(self): ...

    @abstractmethod
    def _is_previous_period_closed(self): ...

    @staticmethod
    def create_fiscal_archive(grand_total):
        return FiscalArchiveService.create_fiscal_archive(grand_total=grand_total)

    @staticmethod
    def send_fiscal_archive_data_to_storage(fiscal_archive):
        return FiscalArchiveService.send_fiscal_archive_data_to_storage(
            fiscal_archive=fiscal_archive
        )


class DailyClosePeriodFactory(ClosePeriodFactory):
    def __init__(
        self,
        business: Business,
        closing_time: datetime.datetime,
    ):
        super().__init__(business, closing_time, ClosePeriodType.DAY)

    def check_all_previous_lesser_periods_closed(self):
        return True

    def check_period_not_older_than_threshold(self):
        day_being_closed = self.period_start.date()
        start_closing_from_day = self.start_closing_from.date()
        if start_closing_from_day > day_being_closed:
            raise CannotClosePeriod(self.business.id, self.period_start, self.close_period_type)

    def _is_previous_period_closed(self):
        return (
            GrandTotal.objects.filter(
                business_id=self.business.id,
                type=ClosePeriodType.DAY,
                period_end=self._time,
            )
            .order_by("-period_start")
            .exists()
        )

    def _should_previous_period_be_closed(self):
        return self.start_closing_from < self.period_start

    def add_period_closed_jet_event(self):
        JETService.day_closed_event(
            business_id=self.business.id,
            closing_date=self._date,
        )

    @property
    def period_start(self):
        return self._time

    @property
    def period_end(self):
        return self.period_start + datetime.timedelta(days=1)

    def create_grand_total(self):
        fiscal_receipts_from_period = self._get_fiscal_receipts()
        totals_per_tax_rate = self._get_totals_per_tax_rate(fiscal_receipts_from_period)
        net_total, gross_total = self._get_totals(totals_per_tax_rate)
        return GrandTotal.objects.create(
            business_id=self.business.id,
            net_total=net_total,
            gross_total=gross_total,
            period_start=self.period_start,
            period_end=self.period_end,
            type=self.close_period_type,
            total_gross_by_tax_rate=totals_per_tax_rate,
            software_version=SoftwareVersionService.get_version(business_id=self.business.id),
        )

    @classmethod
    def _get_totals_per_tax_rate(cls, fiscal_receipts: Iterable[FiscalReceipt]):
        totals_per_tax_rate_dict = defaultdict(lambda: defaultdict(int))
        for fiscal_receipt in fiscal_receipts:
            for total_per_tax_rate in fiscal_receipt.tax_summary:
                tax_rate = total_per_tax_rate['tax_rate']
                if fiscal_receipt.type == FiscalReceiptType.SALE:
                    totals_per_tax_rate_dict[tax_rate]['net_total'] += total_per_tax_rate[
                        'net_total'
                    ]
                    totals_per_tax_rate_dict[tax_rate]['gross_total'] += total_per_tax_rate[
                        'gross_total'
                    ]
                else:
                    totals_per_tax_rate_dict[tax_rate]['net_total'] -= total_per_tax_rate[
                        'net_total'
                    ]
                    totals_per_tax_rate_dict[tax_rate]['gross_total'] -= total_per_tax_rate[
                        'gross_total'
                    ]

        result = []
        for tax_rate, net_gross_totals in totals_per_tax_rate_dict.items():
            result.append(
                TotalPerTaxRate(
                    tax_rate=tax_rate,
                    net_total=net_gross_totals['net_total'],
                    gross_total=net_gross_totals['gross_total'],
                )
            )
        return result

    def _get_fiscal_receipts(self):
        return FiscalReceipt.objects.filter(
            business_id=self.business.id,
            created__gte=self.period_start,
            created__lt=self.period_end,
        )

    @classmethod
    def _get_totals(cls, totals_per_tax_rate: list[TotalPerTaxRate]):
        net_total = sum(x.net_total for x in totals_per_tax_rate)
        gross_total = sum(x.gross_total for x in totals_per_tax_rate)
        return net_total, gross_total


class GrandTotalClosePeriodFactory(ClosePeriodFactory):
    def __init__(
        self,
        business: Business,
        _time: datetime.datetime,
        close_period_type: ClosePeriodType,
    ):
        super().__init__(business, _time, close_period_type)
        types_map = {
            ClosePeriodType.MONTH: ClosePeriodType.DAY,
            ClosePeriodType.YEAR: ClosePeriodType.MONTH,
        }
        self.close_period_type_to_sum_over = types_map[self.close_period_type]

    def create_grand_total(self) -> GrandTotal:
        sums = self._get_grand_total_sums()
        total_gross_by_tax_rate = self._get_total_per_tax_rate()
        return GrandTotal.objects.create(
            business_id=self.business.id,
            net_total=sums['net_total'],
            gross_total=sums['gross_total'],
            period_start=self.period_start,
            period_end=self.period_end,
            total_gross_by_tax_rate=total_gross_by_tax_rate,
            type=self.close_period_type,
            software_version=SoftwareVersionService.get_version(business_id=self.business.id),
        )

    def _get_grand_total_sums(self):
        return GrandTotal.objects.filter(
            business_id=self.business.id,
            type=self.close_period_type_to_sum_over,
            period_start__gte=self.period_start,
            period_end__lte=self.period_end,
        ).aggregate(
            net_total=Coalesce(Sum('net_total'), 0),
            gross_total=Coalesce(Sum('gross_total'), 0),
        )

    def _get_total_per_tax_rate(self):
        totals_per_tax_rate = (
            GrandTotal.objects.filter(
                business_id=self.business.id,
                type=self.close_period_type_to_sum_over,
                period_start__gte=self.period_start,
                period_end__lte=self.period_end,
            )
            .values(tax_rate=F('totals_per_tax_rate__tax_rate'))
            .annotate(
                net_total=Coalesce(Sum('totals_per_tax_rate__net_total'), 0),
                gross_total=Coalesce(Sum('totals_per_tax_rate__gross_total'), 0),
            )
        )
        return [TotalPerTaxRate(**total_per_tax_rate) for total_per_tax_rate in totals_per_tax_rate]

    def verify_period_chain(self):
        super().verify_period_chain()

        grand_total_qs = GrandTotal.objects.filter(
            business_id=self.business.id,
            period_start__gte=self.period_start,
            period_end__lte=self.period_end,
            type=self.close_period_type_to_sum_over,
        )
        IntegrityService.verify_chain(grand_total_qs)


class MonthlyClosePeriodFactory(GrandTotalClosePeriodFactory):
    def __init__(
        self,
        business: Business,
        _time: datetime.datetime,
    ):
        super().__init__(business, _time, ClosePeriodType.MONTH)

    def _are_all_days_closed(self):
        current_year = self.period_start.year
        current_month = self.period_start.month
        last_day_of_month = calendar.monthrange(current_year, current_month)[1]
        return GrandTotal.objects.filter(
            business_id=self.business.id,
            type=ClosePeriodType.DAY,
            period_start__date=datetime.date(current_year, current_month, last_day_of_month),
        ).exists()

    def check_all_previous_lesser_periods_closed(self):
        if not self._are_all_days_closed():
            raise CannotClosePeriod(self.business.id, self.period_start, self.close_period_type)

    def _is_previous_period_closed(self):
        return GrandTotal.objects.filter(
            business_id=self.business.id,
            type=self.close_period_type,
            period_end=self._time,
        ).exists()

    def _should_previous_period_be_closed(self):
        return self.start_closing_from < self.period_start

    def add_period_closed_jet_event(self):
        JETService.month_closed_event(
            business_id=self.business.id,
            closing_date=self._date,
        )

    @property
    def period_start(self):
        if not self.start_closing_from < self._time:
            return self._time.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        return self._time

    @property
    def period_end(self):
        """
        Businesses have different times and period_start fields.
        That's way specially day and time should be the same, only extended by day.
        Chain of those dates needs to be continued.
        E.g. when period_start is 31.10.2024 23:00, period_end should be 31.11.2024 23:00
        """
        return self.period_start + relativedelta(months=1)


class YearlyClosePeriodFactory(GrandTotalClosePeriodFactory):
    def __init__(self, business, _date):
        super().__init__(business, _date, ClosePeriodType.YEAR)

    def check_all_previous_lesser_periods_closed(self):
        if not self._are_all_months_closed():
            raise CannotClosePeriod(self.business.id, self.period_start, self.close_period_type)

    def _is_previous_period_closed(self):
        return GrandTotal.objects.filter(
            business_id=self.business.id,
            type=self.close_period_type,
            period_start=self.period_start - relativedelta(years=1),
            period_end=self.period_end - relativedelta(years=1),
        ).exists()

    def _should_previous_period_be_closed(self):
        return self.start_closing_from.year < self.period_start.year

    def add_period_closed_jet_event(self):
        JETService.year_closed_event(
            business_id=self.business.id,
            closing_date=self._date,
        )

    @property
    def period_start(self):
        utc = datetime.timezone.utc
        period_start = datetime.datetime(year=self._date.year, month=1, day=1, tzinfo=utc)
        return period_start

    @property
    def period_end(self):
        return self.period_start + relativedelta(years=1)

    def _are_all_months_closed(self):
        if FrenchCertificationYearPeriodsRefactor(UserData(subject_key=self.business.id)):
            # only check if december was closed
            current_year = self.period_start.year
            return GrandTotal.objects.filter(
                Q(period_start__year=current_year, period_start__month=12)
                | Q(period_start__year=current_year, period_start__month=11, period_start__day=30),
                business_id=self.business.id,
                type=ClosePeriodType.MONTH,
            ).exists()
        start = max(self.start_closing_from, self.period_start).date()
        delta = relativedelta(self.period_end.date(), start)
        number_of_months_that_should_be_closed = delta.years * 12 + delta.months
        if delta.days > 0:
            number_of_months_that_should_be_closed += 1
        return (
            GrandTotal.objects.filter(
                business_id=self.business.id,
                type=ClosePeriodType.MONTH,
                period_start__gte=self.period_start,
                period_end__lte=self.period_end,
            ).count()
            == number_of_months_that_should_be_closed
        )
