# Generated by Django 4.0.9 on 2023-02-14 09:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('french_certification', '0005_jet'),
    ]

    operations = [
        migrations.AddField(
            model_name='fiscalreceipt',
            name='cancelled_sale',
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name='cancellation',
                to='french_certification.fiscalreceipt',
            ),
        ),
        migrations.AddConstraint(
            model_name='fiscalreceipt',
            constraint=models.UniqueConstraint(
                fields=('basket_id', 'type'),
                name='french_certification_fiscalreceipt_basket_id_type_unique',
            ),
        ),
    ]
