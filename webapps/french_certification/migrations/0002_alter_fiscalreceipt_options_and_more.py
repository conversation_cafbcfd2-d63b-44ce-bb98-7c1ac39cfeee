# Generated by Django 4.0.8 on 2022-11-25 10:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('french_certification', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='fiscalreceipt',
            options={'ordering': ('created',)},
        ),
        migrations.AddField(
            model_name='fiscalreceipt',
            name='business_id',
            field=models.IntegerField(db_index=True, null=True),
        ),
        migrations.AddField(
            model_name='fiscalreceipt',
            name='number',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='fiscalreceipt',
            name='signature',
            field=models.TextField(null=True),
        ),
        migrations.AlterUniqueTogether(
            name='fiscalreceipt',
            unique_together={('business_id', 'number')},
        ),
    ]
