# Generated by Django 4.1.7 on 2023-03-28 05:58

from django.db import migrations, models
import django.db.models.deletion
import uuid
import webapps.french_certification.models


class Migration(migrations.Migration):
    dependencies = [
        ("french_certification", "0013_fiscalreceipt_cancellation_reason"),
    ]

    operations = [
        migrations.CreateModel(
            name="FiscalArchive",
            fields=[
                (
                    "created",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created (UTC)"),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="Updated (UTC)"
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(blank=True, null=True, verbose_name="Deleted (UTC)"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("signature", models.TextField()),
                ("signature_timestamp", models.DateTimeField()),
                ("business_id", models.IntegerField(db_index=True)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("d", "Daily closure"),
                            ("m", "Monthly closure"),
                            ("y", "Yearly closure"),
                        ],
                        max_length=1,
                    ),
                ),
                (
                    "grand_total",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="fiscal_archive",
                        to="french_certification.grandtotal",
                    ),
                ),
            ],
            options={
                "ordering": ("created",),
            },
            managers=[
                ("objects", webapps.french_certification.models.FiscalArchiveManager()),
            ],
        ),
    ]
