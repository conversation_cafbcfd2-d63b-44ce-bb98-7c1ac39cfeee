# Generated by Django 4.0.9 on 2023-02-15 13:31

from django.db import migrations, models
import django.db.models.deletion
import lib.models
import uuid
import webapps.french_certification.models


class Migration(migrations.Migration):

    dependencies = [
        ('french_certification', '0006_fiscalreceipt_cancelled_sale_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='GrandTotal',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('signature', models.TextField()),
                ('business_id', models.IntegerField(db_index=True)),
                ('net_total', models.IntegerField()),
                ('gross_total', models.IntegerField()),
                ('cumulative_gross_total', models.IntegerField()),
                (
                    'type',
                    models.CharField(
                        choices=[('d', 'DAY'), ('m', 'MONTH'), ('y', 'YEAR')], max_length=1
                    ),
                ),
                ('period_start', models.DateTimeField()),
                ('period_end', models.DateTimeField()),
            ],
            options={
                'ordering': ('created',),
            },
            managers=[
                ('objects', webapps.french_certification.models.GrandTotalManager()),
            ],
        ),
        migrations.CreateModel(
            name='TotalPerTaxRate',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('net_total', models.IntegerField()),
                ('gross_total', models.IntegerField()),
                ('tax_rate', models.IntegerField()),
                (
                    'grand_total',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='totals_per_tax_rate',
                        to='french_certification.grandtotal',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
    ]
