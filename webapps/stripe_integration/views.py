from abc import ABCMeta
import logging
import stripe
from django.conf import settings
from rest_framework import status
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.response import Response
from rest_framework.views import APIView
from stripe.error import SignatureVerificationError

from lib.abc import abstractclassattribute
from lib.feature_flag.feature.payment import (
    ShouldUseNewPosRefactoredAccountWebhook,
)
from lib.pos.utils import pos_refactor_stage2_enabled, txn_refactor_stage2_enabled
from webapps.stripe_integration.models import (
    StripeAccount,
    StripeAccountNotification,
    StripeConnectNotification,
    StripePaymentIntent,
)
from webapps.stripe_integration.webhooks_account import (
    handle_account_stripe_event,
)
from webapps.stripe_integration.webhooks_connect import (
    handle_connect_stripe_event,
)
from webapps.payment_providers.consts.stripe import (
    NEW_STRUCTURE_ONLY_EVENTS,
    StripeEventCode,
)
from webapps.payment_providers.models.stripe import StripePaymentIntent as StripePaymentIntentV2

logger_connect = logging.getLogger('booksy.stripe_connect_view')
logger_account = logging.getLogger('booksy.stripe_account_view')


class BaseStripeWebhookView(APIView, metaclass=ABCMeta):
    endpoint_secret: abstractclassattribute()
    throttle_classes = []

    def get_event(self, request):
        signature = request.headers.get("stripe-signature")
        try:
            return stripe.Webhook.construct_event(
                payload=request.body,
                sig_header=signature,
                secret=self.endpoint_secret,
            )
        except (SignatureVerificationError, ValueError) as err:
            raise AuthenticationFailed from err


class StripeConnectNotificationView(BaseStripeWebhookView):
    """
    Endpoint exposed for incoming stripe connect notifications.
    """

    endpoint_secret = settings.STRIPE_CONNECT_WEBHOOK_SECRET

    def post(self, request):
        event = self.get_event(request=request)
        event_type = event.type
        external_id = event.account
        account = StripeAccount.objects.filter(external_id=external_id).first()
        if not account:
            return Response(status=status.HTTP_200_OK)  # silence error
        if event_type in [
            StripeEventCode.ACCOUNT_UPDATED,
        ]:
            logger_connect.warning('stripe_account_updated', extra={'pos': account.pos.id})
            logger_connect.warning(
                'still_not_handle_with_new_webhook',
                extra={
                    'event_type': event_type,
                    'event_id': event.id,
                    'account': account,
                },
            )
            notification, _ = StripeConnectNotification.objects.get_or_create(
                external_id=event.id,
                defaults={
                    'type': event.type,
                    'data': event.to_dict(),
                    'account': account,
                },
            )

            handle_connect_stripe_event(notification)
            return Response(status=status.HTTP_200_OK)

        from webapps.payment_providers.views.webhooks.stripe import StripeWebhookConnectView

        # pylint: disable=protected-access
        return StripeWebhookConnectView.as_view()(request._request)


class StripeAccountNotificationView(BaseStripeWebhookView):
    """
    Endpoint exposed for incoming stripe account notifications.
    """

    endpoint_secret = settings.STRIPE_ACCOUNT_WEBHOOK_SECRET

    def post(self, request):
        if ShouldUseNewPosRefactoredAccountWebhook():
            return self._handle_webhook_event_refactored(request)
        return self._handle_webhook_event_old(request)

    def _handle_webhook_event_refactored(self, request):
        event = self.get_event(request=request)
        event_type = event.type
        payment_intent = None
        if event_type.startswith('payment_intent.'):
            external_id = event.data.object.id
            # payment_intent = StripePaymentIntent.get_by_external_id(external_id)
            payment_intent = StripePaymentIntentV2.objects.filter(external_id=external_id).exists()
        elif event_type.startswith('charge.'):
            external_id = event.data.object.payment_intent
            # payment_intent = StripePaymentIntent.get_by_external_id(external_id)
            payment_intent = StripePaymentIntentV2.objects.filter(external_id=external_id).exists()

        # new events only handled by new code
        if event_type in NEW_STRUCTURE_ONLY_EVENTS or payment_intent:
            from webapps.payment_providers.views.webhooks.stripe import StripeWebhookAccountView

            # pylint: disable=protected-access
            return StripeWebhookAccountView.as_view()(request._request)

        logger_account.warning(
            'still_not_handle_with_new_webhook',
            extra={
                'payment_intent': payment_intent,
                'event_type': event_type,
                'event_id': event.id,
            },
        )
        notification, _ = StripeAccountNotification.objects.get_or_create(
            external_id=event.id,
            defaults={
                'type': event_type,
                'data': event.to_dict(),
                'payment_intent': payment_intent,
            },
        )
        handle_account_stripe_event(notification)

        return Response(status=status.HTTP_200_OK)

    def _handle_webhook_event_old(self, request):
        event = self.get_event(request=request)

        event_type = event.type
        payment_intent = None
        payment_intent_v2_exists = None
        if event_type.startswith('payment_intent.'):
            external_id = event.data.object.id
            payment_intent = StripePaymentIntent.get_by_external_id(external_id)
            payment_intent_v2_exists = StripePaymentIntentV2.objects.filter(
                external_id=external_id
            ).exists()
        elif event_type.startswith('charge.'):
            external_id = event.data.object.payment_intent
            payment_intent = StripePaymentIntent.get_by_external_id(external_id)
            payment_intent_v2_exists = StripePaymentIntentV2.objects.filter(
                external_id=external_id
            ).exists()

        handle_with_new_webhook = False
        # new events only handled by new code
        if event_type in NEW_STRUCTURE_ONLY_EVENTS:
            handle_with_new_webhook = True
        elif payment_intent:
            txn = payment_intent.payment_row and payment_intent.payment_row.receipt.transaction
            if txn:
                handle_with_new_webhook = txn_refactor_stage2_enabled(txn=txn)
            else:
                handle_with_new_webhook = pos_refactor_stage2_enabled(
                    pos=payment_intent.account.pos
                )
        elif payment_intent_v2_exists:
            handle_with_new_webhook = True

        if handle_with_new_webhook:
            from webapps.payment_providers.views.webhooks.stripe import StripeWebhookAccountView

            # pylint: disable=protected-access
            return StripeWebhookAccountView.as_view()(request._request)

        logger_account.warning(
            'still_not_handle_with_new_webhook',
            extra={
                'payment_intent_external_id': (
                    payment_intent.external_id if payment_intent else None
                ),
                'pos_id': payment_intent.account.pos.id if payment_intent else None,
                'payment_intent_v2_exists': payment_intent_v2_exists,
                'event_type': event_type,
                'event_id': event.id,
            },
        )
        notification, _ = StripeAccountNotification.objects.get_or_create(
            external_id=event.id,
            defaults={
                'type': event_type,
                'data': event.to_dict(),
                'payment_intent': payment_intent,
            },
        )
        handle_account_stripe_event(notification)

        return Response(status=status.HTTP_200_OK)
