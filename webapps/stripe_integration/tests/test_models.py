from django.test import (
    TestCase,
    override_settings,
)
from model_bakery import baker

from webapps.business.models import Business
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import (
    POS,
    PaymentRow,
    Receipt,
    Transaction,
)
from webapps.stripe_integration.enums import (
    StripeAccountOnboardingInfo,
    StripeAccountOnboardingStatus,
)
from webapps.stripe_integration.models import (
    StripeAccount,
    StripePaymentIntent,
)
from webapps.stripe_terminal.enums import StripeStatusType
from webapps.stripe_terminal.models import (
    Order,
    OrderPayment,
)


class StripeAccountTestCase(TestCase):
    def setUp(self):
        self.pos = baker.make(POS, business=baker.make(Business))
        txn = baker.make(
            Transaction,
            pos=self.pos,
        )
        self.payment_row = baker.make(
            PaymentRow,
            receipt=baker.make(Receipt, transaction=txn),
            payment_type__code=PaymentTypeEnum.PAY_BY_APP,
        )
        self.stripe_account = baker.make(
            StripeAccount,
            pos=self.pos,
        )

        intent = baker.make(
            StripePaymentIntent,
            account=self.stripe_account,
        )
        intent.payment_rows.add(self.payment_row)

    @override_settings(POS__STRIPE_TERMINAL=True)
    def test_onboarding_skip(self):
        self.stripe_account.onboarding_info = StripeAccountOnboardingInfo.SKIP
        self.stripe_account.save()
        self.assertEqual(
            self.stripe_account.bcr_onboarding_status,
            StripeAccountOnboardingStatus.FIRST_SUCCEEDED_TRANSACTION,
        )

    @override_settings(POS__STRIPE_TERMINAL=True)
    def test_bcr_succeeded(self):
        baker.make(
            OrderPayment,
            business_id=self.pos.business_id,
            stripe_status=OrderPayment.StripePaymentStatusType.SUCCEEDED,
            order=baker.make(
                Order,
                business_id=self.pos.business_id,
                stripe_status=StripeStatusType.DRAFT,
                cell_phone='+***********',
            ),
        )

        self.payment_row.payment_type.code = PaymentTypeEnum.STRIPE_TERMINAL
        self.payment_row.payment_type.pos = self.pos
        self.payment_row.payment_type.save()
        self.assertEqual(
            self.stripe_account.bcr_onboarding_status,
            StripeAccountOnboardingStatus.FIRST_SUCCEEDED_TRANSACTION,
        )

    @override_settings(POS__STRIPE_TERMINAL=True)
    def test_shipped_terminal(self):
        baker.make(
            OrderPayment,
            business_id=self.pos.business_id,
            stripe_status=OrderPayment.StripePaymentStatusType.SUCCEEDED,
            order=baker.make(
                Order,
                business_id=self.pos.business_id,
                stripe_status=StripeStatusType.DRAFT,
                cell_phone='+***********',
            ),
        )

        baker.make(
            Order,
            business_id=self.pos.business_id,
            stripe_status=StripeStatusType.SHIPPED,
            cell_phone='+***********',
        )
        self.assertEqual(
            self.stripe_account.bcr_onboarding_status,
            StripeAccountOnboardingStatus.TERMINAL_SHIPPED,
        )

    @override_settings(POS__STRIPE_TERMINAL=True)
    def test_ordered_terminal(self):
        baker.make(
            OrderPayment,
            business_id=self.pos.business_id,
            stripe_status=OrderPayment.StripePaymentStatusType.SUCCEEDED,
            order=baker.make(
                Order,
                business_id=self.pos.business_id,
                stripe_status=StripeStatusType.DRAFT,
                cell_phone='+***********',
            ),
        )
        self.assertEqual(
            self.stripe_account.bcr_onboarding_status,
            StripeAccountOnboardingStatus.TERMINAL_ORDERED,
        )
