from django.test import override_settings
from django.urls import reverse
from model_bakery import baker
from rest_framework.status import HTTP_200_OK, HTTP_404_NOT_FOUND, HTTP_403_FORBIDDEN

from webapps.stripe_terminal.models import HardwareFeature, Hardware
from webapps.stripe_terminal.tests.views.base import BaseStripeTerminalTestView


class TestHardwareFeaturesListView(BaseStripeTerminalTestView):
    def setUp(self):
        super().setUp()
        self.hardware_features = baker.make(HardwareFeature, 2)
        self.url = reverse('stripe_terminal_hardware_features_list', args=(self.business.id,))

    def test_fetching_list(self):
        response = self.client.get(path=self.url, **self.headers)
        assert response.status_code == HTTP_200_OK
        assert len(response.json()['hardware_features']) == 2

    @override_settings(POS=False)
    def test_inactive_pos_setting(self):
        response = self.client.get(path=self.url, **self.headers)
        assert response.status_code == HTTP_404_NOT_FOUND
        assert not response.json().get('hardware_features')

    def test_invalid_api_key(self):
        response = self.client.get(path=self.url, **{'HTTP_X-API-KEY': 'foo'})
        assert response.status_code == HTTP_403_FORBIDDEN
        assert not response.json().get('hardware_features')

    def test_invalid_access_token(self):
        response = self.client.get(path=self.url, **{'HTTP_X-ACCESS-TOKEN': 'foo'})
        assert response.status_code == HTTP_403_FORBIDDEN
        assert not response.json().get('hardware_features')


class TestHardwareListView(BaseStripeTerminalTestView):
    @classmethod
    def setUpTestData(cls):
        cls.hardware = baker.make(Hardware, 3, is_active=True)
        cls.inactive_hardware = baker.make(Hardware, is_active=False)

    def setUp(self):
        super().setUp()
        self.url = reverse('stripe_terminal_hardware_list', args=(self.business.id,))

    def test_fetching_list(self):
        response = self.client.get(path=self.url, **self.headers)
        assert response.status_code == HTTP_200_OK
        assert len(response.json()['hardware']) == 3
        assert not any(h['id'] == self.inactive_hardware.id for h in response.json()['hardware'])

    @override_settings(POS=False)
    def test_inactive_pos_setting(self):
        response = self.client.get(path=self.url, **self.headers)
        assert response.status_code == HTTP_404_NOT_FOUND
        assert not response.json().get('hardware')

    def test_invalid_api_key(self):
        response = self.client.get(path=self.url, **{'HTTP_X-API-KEY': 'foo'})
        assert response.status_code == HTTP_403_FORBIDDEN
        assert not response.json().get('hardware')

    def test_invalid_access_token(self):
        response = self.client.get(path=self.url, **{'HTTP_X-ACCESS-TOKEN': 'foo'})
        assert response.status_code == HTTP_403_FORBIDDEN
        assert not response.json().get('hardware')

    def test_manager_permission(self):
        self.set_session(self.staffer_user)
        response = self.client.get(path=self.url, **self.headers)
        assert response.status_code == HTTP_404_NOT_FOUND
        assert not response.json().get('hardware')


class TestHardwareDetailsView(BaseStripeTerminalTestView):
    @classmethod
    def setUpTestData(cls):
        cls.hardware = baker.make(Hardware, is_active=True)
        cls.inactive_hardware = baker.make(Hardware, is_active=False)

    def setUp(self):
        super().setUp()
        self.hardware_url = reverse(
            'stripe_terminal_hardware_details',
            args=(self.business.id, self.hardware.id),
        )
        self.inactive_hardware_url = reverse(
            'stripe_terminal_hardware_details',
            args=(self.business.id, self.inactive_hardware.id),
        )

    def test_fetching_active(self):
        response = self.client.get(path=self.hardware_url, **self.headers)
        assert response.status_code == HTTP_200_OK
        assert set(response.json().keys()) == {
            'price',
            'amount',
            'max_per_order',
            'image_url',
            'product_type',
            'name',
            'id',
            'features',
            'is_active',
        }
        assert response.json()['id'] == self.hardware.id

    def test_fetching_inactive(self):
        response = self.client.get(path=self.inactive_hardware_url, **self.headers)
        assert response.status_code == HTTP_404_NOT_FOUND
        assert not response.json().get('id')

    @override_settings(POS=False)
    def test_inactive_pos_setting(self):
        response = self.client.get(path=self.hardware_url, **self.headers)
        assert response.status_code == HTTP_404_NOT_FOUND
        assert not response.json().get('id')

    def test_invalid_api_key(self):
        response = self.client.get(
            path=self.hardware_url,
            **{'HTTP_X-API-KEY': 'foo'},
        )
        assert response.status_code == HTTP_403_FORBIDDEN
        assert not response.json().get('id')

    def test_invalid_access_token(self):
        response = self.client.get(
            path=self.hardware_url,
            **{'HTTP_X-ACCESS-TOKEN': 'foo'},
        )
        assert response.status_code == HTTP_403_FORBIDDEN
        assert not response.json().get('id')

    def test_manager_permission(self):
        self.set_session(self.staffer_user)
        response = self.client.get(path=self.hardware_url, **self.headers)
        assert response.status_code == HTTP_404_NOT_FOUND
        assert not response.json().get('id')
