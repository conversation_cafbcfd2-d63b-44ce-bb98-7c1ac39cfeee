from django.test import override_settings
from model_bakery import baker
from rest_framework.test import APITestCase

from lib.baker_utils import get_or_create_booking_source
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import basic_staffer_recipe, business_recipe, region_recipe
from webapps.business.models import Resource
from webapps.consts import WEB
from webapps.stripe_terminal.models import Hardware, Order, OrderItem
from webapps.structure.models import Region
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User


class BaseStripeTerminalTestView(APITestCase):
    @override_settings(POS=True)
    def setUp(self):
        super().setUp()

        self.booking_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=WEB,
        )
        self.headers = {'HTTP_X_API_KEY': self.booking_source.api_key}
        self.business = business_recipe.make(has_new_billing=True)

        self.owner_user = baker.make(
            User,
            first_name='Owner',
            last_name='User',
            email="<EMAIL>",
        )
        self.owner = basic_staffer_recipe.make(
            business=self.business,
            staff_user=self.owner_user,
            staff_email=self.owner_user.email,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )
        self.staffer_user = baker.make(
            User,
            first_name='Staffer',
            last_name='User',
            email="<EMAIL>",
        )
        self.staffer = basic_staffer_recipe.make(
            business=self.business,
            staff_user=self.staffer_user,
            staff_email=self.staffer_user.email,
        )
        self.set_session(self.owner_user)

    def set_session(self, user):
        self.user = user
        self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.client.credentials(HTTP_X_ACCESS_TOKEN=self.session.session_key)


class BaseOrderTestView(BaseStripeTerminalTestView):
    def setUp(self):
        super().setUp()
        self.hardware = baker.make(
            Hardware,
            name='POS 1',
            price=10.0,
            amount=10.0,
            is_active=True,
        )
        self.shipping_zipcode = region_recipe.make(
            type=Region.Type.ZIP,
            name='12345',
        )
        self.shipping_state = region_recipe.make(
            type=Region.Type.STATE,
            abbrev='WX',
        )

    def _create_order(self) -> Order:
        return baker.make(
            Order,
            business_id=self.business.id,
            user_id=self.user.id,
            shipping_first_name=self.user.first_name,
            shipping_last_name=self.user.last_name,
            billing_first_name=self.user.first_name,
            billing_last_name=self.user.last_name,
            email=self.user.email,
            cell_phone='7732298863',
            shipping_method=Order.DeprecatedStripeShippingMethodType.STANDARD,
            shipping_address_line_1='Foo',
            shipping_city='Bar',
            shipping_zipcode=self.shipping_zipcode,
            shipping_state=self.shipping_state,
            copy_shipping_to_billing=True,
            items=[OrderItem(hardware=self.hardware, quantity=1, amount=self.hardware.price)],
            amount=1 * self.hardware.price,
            status=Order.StatusType.PENDING,
            stripe_status=None,
        )
