import json
from unittest.mock import patch

import pytest
import stripe
from model_bakery import baker

from lib.test_utils import timezone_for_tests
from webapps.booking.tests.test_appointment_base import BaseTestAppointment
from webapps.stripe_terminal.models import (
    Hardware,
    Order,
    OrderItem,
    OrderPayment,
)
from webapps.stripe_terminal.serializers import (
    OrderItemSerializer,
    OrderSerializer,
    OrderPaymentSerializer,
    StripeHardwareOrderSerializer,
    StripeHardwareOrderResponseSerializer,
    StripePaymentIntentSerializer,
    StripePaymentIntentResponseSerializer,
)
from webapps.stripe_terminal.tests.sample_data import sample_shipping_method_list_data
from webapps.structure.models import Region


class BaseTestStripeSerializer(BaseTestAppointment):
    def setUp(self):
        super().setUp()
        self.shipping_zipcode = baker.make(
            Region,
            time_zone_name=timezone_for_tests(),
            type=Region.Type.ZIP,
            name='12345',
        )
        self.shipping_state = baker.make(
            Region,
            time_zone_name=timezone_for_tests(),
            type=Region.Type.STATE,
            abbrev='WX',
        )
        self.hardware = baker.make(
            Hardware,
            external_id='stripe-pos-1',
            product_type='pos-1',
            name='POS 1',
            price=101.99,
            amount=101.99,
            currency='USD',
            max_per_order=10,
            is_active=True,
        )


class BaseOrderTestStripeSerializer(BaseTestStripeSerializer):
    def setUp(self):
        super().setUp()
        self.order = baker.make(
            Order,
            business_id=self.business.id,
            user_id=self.user.id,
            billing_first_name=self.user.first_name,
            billing_last_name=self.user.last_name,
            shipping_first_name=self.user.first_name,
            shipping_last_name=self.user.last_name,
            email=self.user.email,
            cell_phone='7732298863',
            shipping_method='thsm_MfuTjLaPEgXMa4',
            shipping_address_line_1='Foo',
            shipping_city='Bar',
            shipping_zipcode=self.shipping_zipcode,
            shipping_state=self.shipping_state,
            copy_shipping_to_billing=True,
            items=[OrderItem(hardware=self.hardware, quantity=1, amount=self.hardware.price)],
            amount=1 * self.hardware.price,
        )


@pytest.mark.django_db
class TestOrderItemSerializer(BaseTestStripeSerializer):
    def test_max_quantity_validation(self):
        serializer = OrderItemSerializer(
            data={
                'hardware_id': self.hardware.id,
                'quantity': 11,
                'amount': self.hardware.price,
            }
        )
        assert not serializer.is_valid()
        assert serializer.errors.get('quantity')

    def test_quantity_validation_without_limit(self):
        other_hardware = baker.make(
            Hardware,
            external_id='stripe-pos-2',
            product_type='pos-2',
            name='POS 2',
            price=101.99,
            amount=101.99,
            currency='USD',
            is_active=True,
        )
        serializer = OrderItemSerializer(
            data={
                'hardware_id': other_hardware.id,
                'quantity': 11,
                'amount': other_hardware.price,
            }
        )
        assert not other_hardware.max_per_order
        assert serializer.is_valid(raise_exception=True)
        assert not serializer.errors.get('quantity')


class TestOrderSerializer(BaseTestStripeSerializer):
    def _order_data(self):
        return {
            "billing_first_name": self.user.first_name,
            "billing_last_name": self.user.last_name,
            "shipping_first_name": self.user.first_name,
            "shipping_last_name": self.user.last_name,
            "email": self.user.email,
            "cell_phone": "7732298863",
            "shipping_address_line_1": "Street 1",
            "shipping_city": "City",
            "shipping_zipcode": self.shipping_zipcode.name,
            "shipping_state": self.shipping_state.abbrev,
            "shipping_method": "standard",
            "copy_shipping_to_billing": "true",
            "billing_address_line_1": "Street 1",
            "billing_city": "City",
            "billing_zipcode": self.shipping_zipcode.name,
            "billing_state": self.shipping_state.abbrev,
            "items": [{"hardware_id": self.hardware.id, "quantity": 2}],
            "shipment_tracking": [{"carrier": "ups", "tracking_number": "1Z5A5A5123456"}],
        }

    def test_saving_with_valid_data(self):
        data = self._order_data()
        serializer = OrderSerializer(
            data=data,
            context={
                'business': self.business,
                'user': self.user,
                'stripe_account': None,
            },
        )
        assert serializer.is_valid(raise_exception=True)
        assert serializer.save()
        assert Order.objects.count() == 1
        assert OrderItem.objects.count() == 1

    def test_saving_with_invalid_data(self):
        data = self._order_data()
        data['items'] = []
        serializer = OrderSerializer(
            data=data,
            context={
                'business': self.business,
                'user': self.user,
                'stripe_account': None,
            },
        )
        assert not serializer.is_valid()
        assert Order.objects.count() == 0
        assert OrderItem.objects.count() == 0

    def test_json_representation(self):
        data = self._order_data()
        serializer = OrderSerializer(
            data=data,
            context={
                'business': self.business,
                'user': self.user,
                'stripe_account': None,
            },
        )
        assert serializer.is_valid(raise_exception=True)
        order = serializer.save()
        result = serializer.to_representation(order)
        assert result.get('shipping_zipcode') == self.shipping_zipcode.name
        assert result.get('shipping_state') == self.shipping_state.abbrev
        assert result.get('billing_zipcode') == self.shipping_zipcode.name
        assert result.get('billing_state') == self.shipping_state.abbrev
        tracking_list = result.get("shipment_tracking", [])
        assert len(tracking_list) == 1
        self.assertDictEqual(
            dict(tracking_list[0]),
            {
                "carrier": "ups",
                "tracking_number": "1Z5A5A5123456",
                "tracking_url": "https://www.ups.com/track?tracknum=1Z5A5A5123456",
            },
        )


class TestOrderPaymentSerializer(BaseOrderTestStripeSerializer):
    def _order_payment_data(self):
        return {'amount': self.order.amount}

    def test_saving_with_valid_data(self):
        data = self._order_payment_data()
        serializer = OrderPaymentSerializer(
            data=data,
            context={
                'business': self.business,
                'user': self.user,
                'order': self.order,
                'stripe_account': None,
            },
        )
        assert serializer.is_valid(raise_exception=True)
        assert serializer.save()
        assert OrderPayment.objects.count() == 1


class TestStripeHardwareOrderSerializer(BaseOrderTestStripeSerializer):
    def _sample_stripe_data(self, status):
        return {
            "id": "thor_1J8MZGH0H9FNnrB7LM4sk8mv",
            "object": "terminal.hardware_order",
            "amount": 29906,
            "created": **********,
            "currency": "usd",
            "hardware_order_items": [
                {
                    "amount": 26400,
                    "currency": "usd",
                    "quantity": 1,
                    "terminal_hardware_sku": {
                        "id": "thsku_Fmpas6Naavpnsy",
                        "amount": 26400,
                        "country": "US",
                        "currency": "usd",
                        "product_type": "verifone_p400",
                    },
                }
            ],
            "invoice": "",
            "livemode": "false",
            "metadata": {"order_id": self.order.id},
            "payment_type": "monthly_invoice",
            "shipment_tracking": [],
            "shipping": {
                "address": {
                    "city": "Gardena",
                    "country": "US",
                    "line1": "2212",
                    "line2": "w. el segundo blvd",
                    "postal_code": "90249",
                    "state": "CA",
                },
                "amount": 800,
                "company": "Company",
                "currency": "usd",
                "email": "<EMAIL>",
                "name": "John Doe",
                "phone": "+12133248218",
            },
            "shipping_method": "thsm_MfuTjLaPEgXMa4",
            "status": status,
            "tax": 2706,
            "total_tax_amounts": [
                {
                    "amount": 792,
                    "inclusive": "false",
                    "rate": {
                        "display_name": "Sales Tax",
                        "jurisdiction": "Special District",
                        "percentage": 3,
                    },
                },
                {
                    "amount": 264,
                    "inclusive": "false",
                    "rate": {
                        "display_name": "Sales Tax",
                        "jurisdiction": "LOS ANGELES COUNTY",
                        "percentage": 1,
                    },
                },
                {
                    "amount": 1650,
                    "inclusive": "false",
                    "rate": {"display_name": "Sales Tax", "jurisdiction": "CA", "percentage": 6.25},
                },
            ],
            "updated": **********,
        }

    def test_json_representation(self):
        serializer = StripeHardwareOrderSerializer(instance=self.order)
        response = json.loads(json.dumps(serializer.to_representation(self.order)))
        expected = {
            "hardware_order_items": [
                {"terminal_hardware_sku": self.hardware.external_id, "quantity": 1}
            ],
            "shipping": {
                "address": {
                    "city": "Bar",
                    "country": "US",
                    "line1": "Foo",
                    "postal_code": self.shipping_zipcode.name,
                    "state": self.shipping_state.abbrev,
                },
                "name": " ".join([self.user.first_name, self.user.last_name]),
                "email": self.user.email,
                "phone": "7732298863",
            },
            "payment_type": "monthly_invoice",
            "shipping_method": "thsm_MfuTjLaPEgXMa4",
            "metadata": {"order_id": self.order.id},
            "idempotency_key": str(self.order.idempotency_key),
        }
        self.assertDictEqual(response, expected)

    @patch('stripe.terminal.HardwareOrder.create')
    @patch('stripe.terminal.HardwareShippingMethod.list')
    def test_generating_remote_order(self, shipping_method_list_mock, create_mock):
        create_mock.return_value = stripe.terminal.HardwareOrder.construct_from(
            self._sample_stripe_data("pending"),
            stripe.api_key,
        )
        shipping_method_list_mock.return_value = (
            stripe.terminal.HardwareShippingMethod.construct_from(
                sample_shipping_method_list_data,
                stripe.api_key,
            )
        )
        assert not self.order.external_id
        StripeHardwareOrderSerializer.generate_order(self.order)
        assert self.order.stripe_status == "pending"
        assert self.order.status == "paid"
        assert self.order.external_id == "thor_1J8MZGH0H9FNnrB7LM4sk8mv"
        assert self.order.tax_amount == 27.06
        assert self.order.shipping_amount == 8.00
        assert create_mock.call_count == 1

    @patch('stripe.terminal.HardwareOrder.create')
    def test_generating_remote_order_already_generated(self, mock):
        self.order.external_id = 'some-id'
        self.order.stripe_status = 'pending'
        self.order.save()
        StripeHardwareOrderSerializer.generate_order(self.order)
        assert not mock.assert_not_called()

    @patch('stripe.terminal.HardwareOrder.cancel')
    @patch('stripe.terminal.HardwareShippingMethod.list')
    def test_cancelling_remote_order(self, shipping_method_list_mock, cancel_mock):
        self.order.external_id = 'some-id'
        self.order.stripe_status = 'pending'
        self.order.save()
        cancel_mock.return_value = stripe.terminal.HardwareOrder.construct_from(
            self._sample_stripe_data("canceled"),
            stripe.api_key,
        )
        shipping_method_list_mock.return_value = (
            stripe.terminal.HardwareShippingMethod.construct_from(
                sample_shipping_method_list_data,
                stripe.api_key,
            )
        )
        StripeHardwareOrderSerializer.cancel_order(self.order)
        assert self.order.stripe_status == "canceled"

    @patch('stripe.terminal.HardwareOrder.cancel')
    def test_cancelling_remote_order_already_canceled(self, mock):
        self.order.external_id = 'some-id'
        self.order.stripe_status = 'canceled'
        self.order.save()
        StripeHardwareOrderSerializer.cancel_order(self.order)
        assert not mock.assert_not_called()

    @patch('stripe.terminal.HardwareOrder.cancel')
    def test_cancelling_remote_order_not_exists(self, mock):
        assert not self.order.external_id
        with self.assertRaises(AssertionError):
            StripeHardwareOrderSerializer.cancel_order(self.order)
            assert not mock.assert_not_called()

    @patch('stripe.terminal.HardwareOrder.cancel')
    def test_cancelling_remote_order_already_shipped(self, mock):
        self.order.external_id = 'some-id'
        self.order.stripe_status = 'shipped'
        self.order.save()
        with self.assertRaises(AssertionError):
            StripeHardwareOrderSerializer.cancel_order(self.order)
            assert not mock.assert_not_called()


class TestStripeHardwareOrderResponseSerializer(BaseOrderTestStripeSerializer):
    @patch('stripe.terminal.HardwareShippingMethod.list')
    def test_internal_value(self, shipping_method_list_mock):
        data = {
            "id": "thor_1J8MZGH0H9FNnrB7LM4sk8mv",
            "object": "terminal.hardware_order",
            "amount": 29906,
            "created": **********,
            "currency": "usd",
            "hardware_order_items": [
                {
                    "amount": 26400,
                    "currency": "usd",
                    "quantity": 1,
                    "terminal_hardware_sku": {
                        "id": "thsku_Fmpas6Naavpnsy",
                        "amount": 26400,
                        "country": "US",
                        "currency": "usd",
                        "product_type": "verifone_p400",
                    },
                }
            ],
            "invoice": "",
            "livemode": "false",
            "metadata": {"order_id": self.order.id},
            "payment_type": "monthly_invoice",
            "shipment_tracking": [],
            "shipping": {
                "address": {
                    "city": "Gardena",
                    "country": "US",
                    "line1": "2212",
                    "line2": "w. el segundo blvd",
                    "postal_code": "90249",
                    "state": "CA",
                },
                "amount": 800,
                "company": "Company",
                "currency": "usd",
                "email": "<EMAIL>",
                "name": "John Doe",
                "phone": "+12133248218",
            },
            "shipping_method": "thsm_MfuTjLaPEgXMa4",
            "status": "draft",
            "tax": 2706,
            "total_tax_amounts": [
                {
                    "amount": 792,
                    "inclusive": "false",
                    "rate": {
                        "display_name": "Sales Tax",
                        "jurisdiction": "Special District",
                        "percentage": 3,
                    },
                },
                {
                    "amount": 264,
                    "inclusive": "false",
                    "rate": {
                        "display_name": "Sales Tax",
                        "jurisdiction": "LOS ANGELES COUNTY",
                        "percentage": 1,
                    },
                },
                {
                    "amount": 1650,
                    "inclusive": "false",
                    "rate": {"display_name": "Sales Tax", "jurisdiction": "CA", "percentage": 6.25},
                },
            ],
            "updated": **********,
        }
        shipping_method_list_mock.return_value = (
            stripe.terminal.HardwareShippingMethod.construct_from(
                sample_shipping_method_list_data,
                stripe.api_key,
            )
        )

        serializer = StripeHardwareOrderResponseSerializer(data=data)
        response = dict(serializer.to_internal_value(data=data))
        expected = {
            "external_id": "thor_1J8MZGH0H9FNnrB7LM4sk8mv",
            "stripe_status": "draft",
            "tax_amount": 27.06,
            "shipment_tracking": [],
            "shipping_amount": 8.00,
        }
        self.assertDictEqual(response, expected)


class TestStripePaymentIntentSerializer(BaseOrderTestStripeSerializer):
    def setUp(self):
        super().setUp()
        self.order_payment = baker.make(
            OrderPayment,
            business_id=self.business.id,
            user_id=self.user.id,
            order=self.order,
            amount=self.order.amount,
        )

    def _sample_stripe_data(self, status="requires_payment_method"):
        return {
            "id": "pi_1J8MZGH0H9FNnrB7qMvKRdyx",
            "object": "payment_intent",
            "amount": 29900,
            "amount_capturable": 0,
            "amount_received": 0,
            "application": "",
            "application_fee_amount": "",
            "canceled_at": "",
            "cancellation_reason": "",
            "capture_method": "automatic",
            "charges": {
                "object": "list",
                "data": [],
                "has_more": "false",
                "total_count": 0,
                "url": "/v1/charges?payment_intent=pi_1J8MZGH0H9FNnrB7qMvKRdyx",
            },
            "client_secret": "pi_1J8MZGH0H9FNnrB7qMvKRdyx_secret_sXOUdNVIsRU21hpYOtsVWMhQT",
            "confirmation_method": "automatic",
            "created": **********,
            "currency": "usd",
            "customer": "",
            "description": "1x Verifone P400\nShipping",
            "invoice": "",
            "last_payment_error": "",
            "livemode": "false",
            "metadata": {"order_id": self.order.id, "order_payment_id": self.order_payment.id},
            "next_action": "",
            "on_behalf_of": "",
            "payment_method": "",
            "payment_method_options": {
                "card": {"installments": "", "network": "", "request_three_d_secure": "automatic"}
            },
            "payment_method_types": ["card"],
            "receipt_email": "<EMAIL>",
            "review": "",
            "setup_future_usage": "",
            "shipping": {
                "address": {
                    "city": "Gardena",
                    "country": "US",
                    "line1": "2212",
                    "line2": "w. el segundo blvd",
                    "postal_code": "90249",
                    "state": "CA",
                },
                "carrier": "",
                "name": "John Doe",
                "phone": "(*************",
                "tracking_number": "",
            },
            "source": "",
            "statement_descriptor": "",
            "statement_descriptor_suffix": "",
            "status": status,
            "transfer_data": "",
            "transfer_group": "",
        }

    def test_json_representation(self):
        serializer = StripePaymentIntentSerializer(instance=self.order_payment)
        response = json.loads(json.dumps(serializer.to_representation(self.order_payment)))
        expected = {
            "amount": 10199,
            "currency": "usd",
            "payment_method_types": ["card"],
            "shipping": {
                "address": {
                    "city": "Bar",
                    "country": "US",
                    "line1": "Foo",
                    "postal_code": self.shipping_zipcode.name,
                    "state": self.shipping_state.abbrev,
                },
                "name": " ".join([self.user.first_name, self.user.last_name]),
                "phone": "7732298863",
            },
            "receipt_email": self.user.email,
            "description": f'{1}x {self.hardware.name}',
            "confirm": False,
            "metadata": {"order_id": self.order.id, "order_payment_id": self.order_payment.id},
            "idempotency_key": str(self.order_payment.idempotency_key),
        }
        self.assertDictEqual(response, expected)

    @patch('stripe.PaymentIntent.create')
    def test_generating_remote_payment(self, mock):
        mock.return_value = stripe.PaymentIntent.construct_from(
            self._sample_stripe_data(),
            stripe.api_key,
        )
        assert not self.order_payment.external_id
        StripePaymentIntentSerializer.generate_payment(self.order_payment)
        assert self.order_payment.stripe_status == "requires_payment_method"
        assert self.order_payment.external_id
        assert self.order_payment.client_secret

    @patch('stripe.PaymentIntent.create')
    def test_generating_remote_payment_already_exists(self, mock):
        self.order_payment.external_id = 'some-id'
        self.order_payment.save()
        StripePaymentIntentSerializer.generate_payment(self.order_payment)
        assert not mock.assert_not_called()


class TestStripePaymentIntentResponseSerializer(BaseOrderTestStripeSerializer):
    def setUp(self):
        super().setUp()
        self.order_payment = baker.make(
            OrderPayment,
            business_id=self.business.id,
            user_id=self.user.id,
            order=self.order,
            amount=self.order.amount,
        )

    def test_internal_value(self):
        data = {
            "id": "pi_1J8MZGH0H9FNnrB7qMvKRdyx",
            "object": "payment_intent",
            "amount": 29900,
            "amount_capturable": 0,
            "amount_received": 0,
            "application": "",
            "application_fee_amount": "",
            "canceled_at": "",
            "cancellation_reason": "",
            "capture_method": "automatic",
            "charges": {
                "object": "list",
                "data": [],
                "has_more": "false",
                "total_count": 0,
                "url": "/v1/charges?payment_intent=pi_1J8MZGH0H9FNnrB7qMvKRdyx",
            },
            "client_secret": "pi_1J8MZGH0H9FNnrB7qMvKRdyx_secret_sXOUdNVIsRU21hpYOtsVWMhQT",
            "confirmation_method": "automatic",
            "created": **********,
            "currency": "usd",
            "customer": "",
            "description": "1x Verifone P400\nShipping",
            "invoice": "",
            "last_payment_error": "",
            "livemode": "false",
            "metadata": {"order_id": self.order.id, "order_payment_id": self.order_payment.id},
            "next_action": "",
            "on_behalf_of": "",
            "payment_method": "",
            "payment_method_options": {
                "card": {"installments": "", "network": "", "request_three_d_secure": "automatic"}
            },
            "payment_method_types": ["card"],
            "receipt_email": "<EMAIL>",
            "review": "",
            "setup_future_usage": "",
            "shipping": {
                "address": {
                    "city": "Gardena",
                    "country": "US",
                    "line1": "2212",
                    "line2": "w. el segundo blvd",
                    "postal_code": "90249",
                    "state": "CA",
                },
                "carrier": "",
                "name": "John Doe",
                "phone": "(*************",
                "tracking_number": "",
            },
            "source": "",
            "statement_descriptor": "",
            "statement_descriptor_suffix": "",
            "status": "requires_payment_method",
            "transfer_data": "",
            "transfer_group": "",
        }
        serializer = StripePaymentIntentResponseSerializer()
        response = dict(serializer.to_internal_value(data))
        expected = {
            "external_id": "pi_1J8MZGH0H9FNnrB7qMvKRdyx",
            "stripe_status": "requires_payment_method",
            "client_secret": "pi_1J8MZGH0H9FNnrB7qMvKRdyx_secret_sXOUdNVIsRU21hpYOtsVWMhQT",
        }
        self.assertDictEqual(response, expected)
