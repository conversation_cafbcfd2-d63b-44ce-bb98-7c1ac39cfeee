# Generated by Django 4.1.7 on 2023-07-04 07:15

from django.db import migrations, models
import django.core.validators
import lib.models
import webapps.stripe_terminal.enums


class Migration(migrations.Migration):
    dependencies = [
        ('stripe_terminal', '0014_alter_order_stripe_status_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShippingMethod',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'method_type',
                    models.CharField(
                        choices=[
                            ('standard', 'Standard'),
                            ('express', 'Express'),
                            ('priority', 'Priority'),
                        ],
                        max_length=30,
                        unique=True,
                    ),
                ),
                (
                    'price',
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[django.core.validators.MinValueValidator(0.01)],
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.AlterField(
            model_name='order',
            name='shipping_method',
            field=models.CharField(
                choices=[
                    ('standard', 'Standard'),
                    ('express', 'Express'),
                    ('priority', 'Priority'),
                    ('standard', 'Standard'),
                    ('three_day', 'Three day'),
                    ('two_day', 'Two day'),
                    ('next_day', 'Next day'),
                ],
                default=webapps.stripe_terminal.enums.StripeShippingMethodType['STANDARD'],
                max_length=20,
            ),
        ),
    ]
