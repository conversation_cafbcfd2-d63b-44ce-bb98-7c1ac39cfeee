# Generated by Django 4.1.7 on 2023-03-09 20:13

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("stripe_terminal", "0012_change_business_and_user_from_fk_to_integer"),
    ]

    operations = [
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="hardware",
            name="is_active",
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name="hardware",
            name="product_type",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddConstraint(
            model_name="hardware",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_active", True)),
                fields=("product_type",),
                name="product_type_unique_is_active",
            ),
        ),
    ]
