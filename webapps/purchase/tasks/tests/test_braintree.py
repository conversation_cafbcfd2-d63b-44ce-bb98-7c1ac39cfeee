from datetime import datetime
from unittest.mock import MagicMock, patch

import braintree
import pytest
from django.test import TestCase, override_settings
from model_bakery import baker
from pytz import UTC

from webapps.business.baker_recipes import business_recipe
from webapps.business.models import Business
from webapps.purchase.models import Subscription
from webapps.purchase.tasks.brain_tree import (
    BraintreeSubscriptionRestoreTask,
    update_staff_charges,
)


@override_settings(CHARGE_FOR_STAFFERS_ENABLED=True)
@pytest.mark.freeze_time(datetime(2019, 1, 15, tzinfo=UTC))
class TestUpdateStaffCharges(TestCase):
    @patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay')
    @patch('webapps.purchase.tasks.brain_tree.BraintreeSubscriptionRestoreTask')
    def test__new_billing(self, task_patched, _mock):
        business = business_recipe.make(has_new_billing=True)
        baker.make(
            Subscription,
            business=business,
            source=Business.PaymentSource.BRAINTREE,
            start=datetime(2019, 1, 1, tzinfo=UTC),
            expiry=datetime(2019, 2, 28, tzinfo=UTC),
            receipt={'status': braintree.Subscription.Status.Active},
        )
        update_staff_charges.run(business.id)
        self.assertEqual(task_patched.call_count, 0)


@patch.object(braintree.Customer, 'find', MagicMock())
@patch('webapps.purchase.tasks.brain_tree.update_transactions')
class TestBraintreeSubscriptionRestoreTask(TestCase):
    def test__new_billing(self, braintree_patched):
        business = business_recipe.make(has_new_billing=True)
        self.assertEqual(BraintreeSubscriptionRestoreTask(business.id), [])
        self.assertEqual(braintree_patched.call_count, 0)

    def test__old_billing(self, braintree_patched):
        business = business_recipe.make(has_new_billing=False)
        self.assertEqual(BraintreeSubscriptionRestoreTask(business.id), [])
        self.assertEqual(braintree_patched.call_count, 1)
