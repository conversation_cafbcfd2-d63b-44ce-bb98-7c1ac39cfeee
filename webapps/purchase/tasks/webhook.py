import logging
from lib.celery_tools import post_transaction_task

logger = logging.getLogger('booksy.purchase')


@post_transaction_task
def ProcessSingleSubscription(**data):
    """
    Process single subscription object from Braintree

    :param data:
        1) braintree_id: str . Subscription plan_id
        2) kind: str, enum from braintree.WebhookNotification.Kind
        3) receipt_id: str. Id of Subscription object braintree
        5) business_id: int. Id of Business
        6) start: serialized datetime object. Date of subscription start
        7) expiry: serialized datetime object. Date of subscription end
        8) trial_period: bool. Is trial period
        9) balance: str.
        10) customer_id: int.
        11) renewing: bool
        12) metadata: dict or None
    :return: None
    """
    import braintree
    from django.db import IntegrityError
    from rest_framework import serializers
    from webapps.business.models import Business
    from webapps.purchase.models import SubscriptionListing
    from webapps.purchase.tasks import ComputeBusinessStatusTask
    from webapps.purchase.tasks.brain_tree import (
        update_or_create_subscription,
        update_transactions,
    )

    date_time_field = serializers.DateTimeField()

    braintree_id = data['braintree_id']
    kind = data['kind']
    receipt_id = data['receipt_id']
    business_id = data['business_id']
    subscription_status = data['subscription_status']
    start = date_time_field.to_internal_value(data['start'])
    expiry = date_time_field.to_internal_value(data['expiry'])
    trial_period = data['trial_period']
    balance = data['balance']
    customer_id = data['customer_id']
    renewing = data['renewing']

    try:
        Business.objects.get(id=business_id, has_new_billing=False)
    except Business.DoesNotExist:
        logger.error(
            '[Business - DoesNotExist] business with ID: %s, not exists or has new billing',
            business_id,
        )
        return

    if subscription_status == braintree.Subscription.Status.Canceled:
        renewing = False

    try:
        product = SubscriptionListing.objects.get(braintree_id=braintree_id)
    except SubscriptionListing.DoesNotExist:
        logger.error(
            '[POST - %s] unexpected product %s',
            kind,
            braintree_id,
        )
        return
    except SubscriptionListing.MultipleObjectsReturned:
        logger.error(
            '[POST - %s] more than one product return %s',
            kind,
            braintree_id,
        )
        return

    try:
        sub = update_or_create_subscription(
            None,
            defaults=dict(
                business_id=business_id,
                product=product,
                start=start,
                expiry=expiry,
                renewing=renewing,
                receipt={
                    'id': receipt_id,
                    'status': subscription_status,
                    'trial': trial_period,
                    'balance': balance,
                },
            ),
        )
    except IntegrityError:
        logger.error(
            '[Subscription IntegrityError] business ID: %s, subscription ID: %s',
            business_id,
            receipt_id,
        )
        sub = None

    update_transactions(customer_id)

    if subscription_status == braintree.Subscription.Status.Active:
        Business.objects.filter(id=business_id).update(
            payment_source=Business.PaymentSource.BRAINTREE
        )

    ComputeBusinessStatusTask.apply_async(
        kwargs=dict(
            business_id=business_id,
            metadata=data.get('metadata'),
            ignore_blocked=True,
        )
    )
