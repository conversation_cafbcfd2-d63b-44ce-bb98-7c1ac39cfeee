# Generated by Django 3.1.2 on 2021-03-12 06:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0094_auto_20210218_1600'),
    ]

    operations = [
        migrations.AlterField(
            model_name='addon',
            name='source',
            field=models.CharField(
                choices=[
                    ('U', 'Unknown'),
                    ('O', 'Offline'),
                    ('B', 'Braintree'),
                    ('R', 'Booksy Billing'),
                    ('I', 'Apple iTunes'),
                    ('P', 'Google Play'),
                ],
                max_length=1,
            ),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='source',
            field=models.CharField(
                choices=[
                    ('U', 'Unknown'),
                    ('O', 'Offline'),
                    ('B', 'Braintree'),
                    ('R', 'Booksy Billing'),
                    ('I', 'Apple iTunes'),
                    ('P', 'Google Play'),
                ],
                max_length=1,
            ),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='source',
            field=models.Char<PERSON>ield(
                choices=[
                    ('U', 'Unknown'),
                    ('O', 'Offline'),
                    ('B', 'Braintree'),
                    ('R', '<PERSON>y Billing'),
                    ('I', 'Apple iTunes'),
                    ('P', 'Google Play'),
                ],
                max_length=1,
                verbose_name='Payment with',
            ),
        ),
    ]
