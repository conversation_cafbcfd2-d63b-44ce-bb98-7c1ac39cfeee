# Generated by Django 3.2.7 on 2022-01-11 08:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0112_subscriptionbuyer_payment_due_days'),
    ]

    operations = [
        migrations.AddField(
            model_name='subscription',
            name='navision_invoicing_allowed',
            field=models.BooleanField(
                default=True, help_text='Include subscription in navision invoicing'
            ),
        ),
        migrations.AddField(
            model_name='subscription',
            name='navision_invoicing_exclusion_reason',
            field=models.TextField(
                blank=True,
                help_text='Why subscription is excluded from navision invoicing?',
                null=True,
            ),
        ),
    ]
