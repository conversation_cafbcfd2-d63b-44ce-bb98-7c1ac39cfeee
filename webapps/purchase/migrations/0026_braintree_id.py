from django.db import migrations


FORWARD = """CREATE UNIQUE INDEX purchase_subscription__braintree_id
ON purchase_subscription ((receipt->>'id'))
WHERE source='B';
"""
ROLLBACK = """DROP INDEX purchase_subscription__braintree_id;"""


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0025_subscriptionhistory'),
    ]

    operations = [
        migrations.RunSQL(sql=FORWARD, reverse_sql=ROLLBACK),
    ]
