# Generated by Django 3.1.12 on 2021-07-05 11:42

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0099_80126_remove_unused_fields_subscription_buyer'),
    ]

    operations = [
        migrations.AddField(
            model_name='subscription',
            name='agreement_months',
            field=models.PositiveSmallIntegerField(
                blank=True,
                default=None,
                null=True,
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(12),
                ],
            ),
        ),
        migrations.AddField(
            model_name='subscription',
            name='agreement_signed_date',
            field=models.DateField(blank=True, default=None, null=True),
        ),
        migrations.AddField(
            model_name='subscription',
            name='instalments',
            field=models.PositiveSmallIntegerField(
                blank=True, choices=[(1, 'ONE'), (2, 'TWO')], default=None, null=True
            ),
        ),
    ]
