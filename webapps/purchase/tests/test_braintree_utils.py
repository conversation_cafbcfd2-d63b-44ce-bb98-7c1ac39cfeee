#!/usr/bin/env python

import datetime
from collections import namedtuple

import braintree
import pytest
from dateutil.relativedelta import relativedelta
from django.test import TestCase, override_settings
from mock import MagicMock, patch
from model_bakery import baker
from pytz import UTC

from webapps.business.models import Business
from webapps.kill_switch.models import KillSwitch
from webapps.purchase.braintree.utils import (
    Braintree3DSVerificationManager,
    add_billing_skipped_transactions,
)
from webapps.purchase.models import (
    Subscription,
    SubscriptionListing,
    SubscriptionTransaction,
)
from webapps.purchase.utils import adjust_billing_date


@pytest.mark.freeze_time(datetime.datetime(2018, 11, 15, 12, tzinfo=UTC))
class TestAddBillingSkipped(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = baker.make(Business)
        cls.product = baker.make(
            SubscriptionListing,
            braintree_id='123',
            active=True,
        )
        cls.subscription = baker.make(
            Subscription,
            source=Business.PaymentSource.BRAINTREE,
            business=cls.business,
            product=cls.product,
            start=datetime.datetime(2016, 5, 31, tzinfo=UTC),
            expiry=datetime.datetime(2018, 11, 30, tzinfo=UTC),
            receipt={
                'id': 'sub_123',
                'status': braintree.Subscription.Status.Active,
            },
        )
        cls.braintree_subscription_type = namedtuple(
            'BraintreeSubscriptionMock', ['id', 'transactions']
        )

    def get_transactions(self):
        transactions = []
        SubscriptionDetails = namedtuple(
            'SubscriptionDetailsMock', ['billing_period_start_date', 'billing_period_end_date']
        )
        Transaction = namedtuple(
            'TransactionMock', ['id', 'subscription_id', 'created_at', 'subscription_details']
        )
        transaction_date = self.subscription.start
        # Transactions obtained from Braintree API are tz unaware
        transaction_date.replace(tzinfo=None)
        for i in range(1, 31):
            subscription_details = SubscriptionDetails(
                billing_period_start_date=transaction_date,
                billing_period_end_date=adjust_billing_date(
                    transaction_date + relativedelta(months=1), self.subscription.start.day
                ),
            )
            transaction = Transaction(
                id=i,
                subscription_id='sub_123',
                created_at=transaction_date,
                subscription_details=subscription_details,
            )
            transactions.append(transaction)
            if i % 2:
                transaction_2 = Transaction(
                    id=i * 100,
                    subscription_id='sub_123',
                    created_at=transaction_date + relativedelta(days=5),
                    subscription_details=subscription_details,
                )
                transactions.append(transaction_2)
            transaction_date = adjust_billing_date(
                transaction_date + relativedelta(months=1), self.subscription.start.day
            )
        return list(reversed(transactions))

    def test_all_skipped(self):
        braintree_subscription = self.braintree_subscription_type(id='sub_123', transactions=[])
        add_billing_skipped_transactions(braintree_subscription)
        skipped_count = SubscriptionTransaction.objects.filter(
            business_id=self.business.id, state=SubscriptionTransaction.State.SKIPPED
        ).count()
        self.assertEqual(skipped_count, 30)

    def test_no_skipped__all_transactions(self):
        transactions = self.get_transactions()
        braintree_subscription = self.braintree_subscription_type(
            id='sub_123', transactions=transactions
        )
        add_billing_skipped_transactions(braintree_subscription)
        self.assertFalse(SubscriptionTransaction.objects.exists())

    def test_no_skipped__latest_transactions(self):
        transactions = self.get_transactions()[:20]
        braintree_subscription = self.braintree_subscription_type(
            id='sub_123', transactions=transactions
        )
        add_billing_skipped_transactions(braintree_subscription)
        self.assertFalse(SubscriptionTransaction.objects.exists())

    def test_skipped_left(self):
        # Cut out 1 oldest transaction
        transactions = self.get_transactions()[:-1]
        braintree_subscription = self.braintree_subscription_type(
            id='sub_123', transactions=transactions
        )
        add_billing_skipped_transactions(braintree_subscription)
        # We can't say if it was skipped or limited transaction count
        # in braintree response
        self.assertFalse(SubscriptionTransaction.objects.exists())

    def test_skipped_right_1(self):
        # Cut out 1 newest transaction
        transactions = self.get_transactions()[1:]
        braintree_subscription = self.braintree_subscription_type(
            id='sub_123', transactions=transactions
        )
        add_billing_skipped_transactions(braintree_subscription)
        skipped = SubscriptionTransaction.objects.filter(
            state=SubscriptionTransaction.State.SKIPPED, subscription=self.subscription
        )
        self.assertEqual(skipped.count(), 1)
        self.assertEqual(skipped.get().charged_on, datetime.datetime(2018, 10, 31, tzinfo=UTC))

    def test_skipped_right_2(self):
        # Cut out 3 newest transactions, 2nd and 3rd have the same
        # billing period
        transactions = self.get_transactions()[3:]
        braintree_subscription = self.braintree_subscription_type(
            id='sub_123', transactions=transactions
        )
        add_billing_skipped_transactions(braintree_subscription)
        skipped = SubscriptionTransaction.objects.filter(
            state=SubscriptionTransaction.State.SKIPPED, subscription=self.subscription
        )
        self.assertEqual(skipped.count(), 2)
        older, newer = skipped.order_by('charged_on')
        self.assertEqual(older.charged_on, datetime.datetime(2018, 9, 30, tzinfo=UTC))
        self.assertEqual(newer.charged_on, datetime.datetime(2018, 10, 31, tzinfo=UTC))

    def test_skipped_middle_1(self):
        transactions = self.get_transactions()
        # Cut out 1 transaction in the middle (and there's no other transaction
        # for the same billing cycle)
        transactions = transactions[:3] + transactions[4:]
        braintree_subscription = self.braintree_subscription_type(
            id='sub_123', transactions=transactions
        )
        add_billing_skipped_transactions(braintree_subscription)
        skipped = SubscriptionTransaction.objects.filter(
            state=SubscriptionTransaction.State.SKIPPED, subscription=self.subscription
        )
        self.assertEqual(skipped.count(), 1)
        self.assertEqual(skipped.get().charged_on, datetime.datetime(2018, 8, 31, tzinfo=UTC))

    def test_skipped_middle_2(self):
        transactions = self.get_transactions()
        transactions = transactions[:3] + transactions[6:]
        braintree_subscription = self.braintree_subscription_type(
            id='sub_123', transactions=transactions
        )
        add_billing_skipped_transactions(braintree_subscription)
        skipped = SubscriptionTransaction.objects.filter(
            state=SubscriptionTransaction.State.SKIPPED, subscription=self.subscription
        )
        self.assertEqual(skipped.count(), 2)
        older, newer = skipped.order_by('charged_on')
        self.assertEqual(older.charged_on, datetime.datetime(2018, 7, 31, tzinfo=UTC))
        self.assertEqual(newer.charged_on, datetime.datetime(2018, 8, 31, tzinfo=UTC))


class TestBraintree3DSVerificationManager(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = baker.make(Business)
        cls.kill_switch = baker.make(
            KillSwitch,
            name=KillSwitch.System.THREE_DS_ENABLED,
            is_killed=False,
        )

    def test_3d_secure_is_killed(self):
        self.kill_switch.is_killed = True
        self.kill_switch.save()
        self.kill_switch.refresh_from_db()
        assert not Braintree3DSVerificationManager.is_3d_secure_enabled()

    @override_settings(BRAINTREE_3D_SECURE_ELIGIBLE=True)
    def test_3d_secure_is_enabled(self):
        assert Braintree3DSVerificationManager.is_3d_secure_enabled()

    @override_settings(BRAINTREE_3D_SECURE_ELIGIBLE=False)
    def test_3d_secure_is_disabled(self):
        assert not Braintree3DSVerificationManager.is_3d_secure_enabled()

    @patch.object(Braintree3DSVerificationManager, 'is_3d_secure_enabled', lambda: True)
    def test_update_flag_verified(self):
        three_d_secure_info_mock = MagicMock(status='authenticate_successful')
        Braintree3DSVerificationManager.check_and_update_flag(
            three_d_secure_info_mock, self.business.id
        )
        self.business.refresh_from_db()
        assert (
            Braintree3DSVerificationManager.get_status(self.business)
            == Braintree3DSVerificationManager.VerificationStatus.SUCCESS
        )

    @patch.object(Braintree3DSVerificationManager, 'is_3d_secure_enabled', lambda: True)
    def test_update_flag_not_verified(self):
        three_d_secure_info_mock = MagicMock(status='any_other_status')
        Braintree3DSVerificationManager.check_and_update_flag(
            three_d_secure_info_mock, self.business.id
        )
        self.business.refresh_from_db()
        assert (
            Braintree3DSVerificationManager.get_status(self.business)
            == Braintree3DSVerificationManager.VerificationStatus.NOT_VERIFIED
        )

    def test_business_not_eligible(self):
        assert (
            Braintree3DSVerificationManager.get_status(self.business)
            == Braintree3DSVerificationManager.VerificationStatus.NOT_ELIGIBLE
        )

    @patch.object(Braintree3DSVerificationManager, 'is_3d_secure_enabled', lambda: True)
    def test_update_flag_verification_empty(self):
        three_d_info = None
        Braintree3DSVerificationManager.check_and_update_flag(three_d_info, self.business.id)
        self.business.refresh_from_db()
        assert (
            Braintree3DSVerificationManager.get_status(self.business)
            == Braintree3DSVerificationManager.VerificationStatus.NOT_VERIFIED
        )
