import re
from datetime import datetime
from unittest.mock import patch

import pytest
from freezegun import freeze_time
from model_bakery import baker
from pytz import UTC

from webapps.business.models import Business
from webapps.purchase.models import Subscription
from webapps.purchase.serializers import SubscriptionSerializer
from webapps.purchase.serializers.google import GoogleReceiptSerializer
from webapps.structure.models import Region


@pytest.mark.parametrize(
    'payload,expected',
    (
        pytest.param(
            'pwantulok1.t1.booksy.pm-us-413287-df9df563ce7c44f6a4140d4173d1c8a9',
            {
                'deployment': 'pwantulok1.t1.booksy.pm',
                'cc': 'us',
                'biz': '413287',
                'token': 'df9df563ce7c44f6a4140d4173d1c8a9',
            },
            id='valid t1 - new delpoyment_level format',
        ),
        pytest.param(
            'test_pwantulok1-t1-us-413287-df9df563ce7c44f6a4140d4173d1c8a9',
            {
                'deployment': 'test_pwantulok1-t1',
                'cc': 'us',
                'biz': '413287',
                'token': 'df9df563ce7c44f6a4140d4173d1c8a9',
            },
            id='valid t1 - legacy delpoyment_level format',
        ),
        pytest.param(
            'pwantulok1-t1-us-413287',
            {},
            id='test deployment without prefix',
        ),
        pytest.param(
            'test_pwantulok1-t1-usa-413287',
            {},
            id='country code too long',
        ),
        pytest.param(
            'test_pwantulok1-t1-u-413287',
            {},
            id='country code too short',
        ),
        pytest.param(
            'test_pwantulok1-t1-us-a123',
            {},
            id='invalid business',
        ),
        pytest.param(
            'test_pwantulok1-t1-u1-413287',
            {},
            id='unexpected chars in country code',
        ),
        pytest.param(
            'test_e2e-tests-t1-us-12345-4w216btcwxn9dfw5k4nwhukbxy806hag',
            {
                'deployment': 'test_e2e-tests-t1',
                'cc': 'us',
                'biz': '12345',
                'token': '4w216btcwxn9dfw5k4nwhukbxy806hag',
            },
            id='digits and hyphens in deployment',
        ),
        pytest.param(
            'gb-123-uegau7mathxuhc8k5e2507ndckz3skne',
            {
                'deployment': None,
                'cc': 'gb',
                'biz': '123',
                'token': 'uegau7mathxuhc8k5e2507ndckz3skne',
            },
            id='valid live',
        ),
        *(
            pytest.param(
                f'{deployment}-pl-123-ojx9ir35q789xan2zohyjx6y7zxxatcd',
                {
                    'deployment': deployment,
                    'cc': 'pl',
                    'biz': '123',
                    'token': 'ojx9ir35q789xan2zohyjx6y7zxxatcd',
                },
                id=f'deployment {deployment}',
            )
            for deployment in ('live', 'dev')
        ),
        pytest.param(
            'us-123-uegau7mathxuhc8k5e2507ndckz3#kne',
            {},
            id='unexpected chars in token',
        ),
        pytest.param(
            'us-123-uegau7mathxuhc8k5e2507ndckz3sknea',
            {},
            id='token too long',
        ),
        pytest.param(
            'us-123-uegau7mathxuhc8k5e2507ndckz3skn',
            {},
            id='token too short',
        ),
    ),
)
def test_google_receipt_developer_payload_regex_v1(payload, expected):
    match = re.match(GoogleReceiptSerializer.DEVELOPER_PAYLOAD_RE, payload)
    if expected:
        assert match.groupdict() == expected
    else:
        assert match is None


@pytest.mark.parametrize(
    'payload,expected',
    (
        pytest.param(
            'test_pwantulok1-t1-us-413287',
            {
                'deployment': 'test_pwantulok1-t1',
                'cc': 'us',
                'biz': '413287',
            },
            id='valid t1',
        ),
        pytest.param(
            'test_pwantulok1-t1-usa-413287',
            {},
            id='country code too long',
        ),
        pytest.param(
            'test_pwantulok1-t1-u-413287',
            {},
            id='country code too short',
        ),
        pytest.param(
            'test_pwantulok1-t1-us-a123',
            {},
            id='invalid business',
        ),
        pytest.param(
            'test_pwantulok1-t1-u1-413287',
            {},
            id='unexpected chars in country code',
        ),
        pytest.param(
            'test_e2e-tests-t1-us-12345',
            {
                'deployment': 'test_e2e-tests-t1',
                'cc': 'us',
                'biz': '12345',
            },
            id='digits and hyphens in deployment',
        ),
        pytest.param(
            'gb-123',
            {
                'deployment': None,
                'cc': 'gb',
                'biz': '123',
            },
            id='valid live',
        ),
        *(
            pytest.param(
                f'{deployment}-pl-123',
                {
                    'deployment': deployment,
                    'cc': 'pl',
                    'biz': '123',
                },
                id=f'deployment {deployment}',
            )
            for deployment in ('live', 'dev')
        ),
    ),
)
def test_google_receipt_obfuscated_account_id_regex_v4(payload, expected):
    match = re.match(GoogleReceiptSerializer.OBFUSCATED_ACCOUNT_ID_RE, payload)
    if expected:
        assert match.groupdict() == expected
    else:
        assert match is None


@pytest.mark.parametrize(
    'freeze_dt,expected_start,expected_end',
    (
        (
            datetime(2021, 5, 25, tzinfo=UTC),
            '2021-05-15T00:00:00+00:00',
            '2021-06-15T00:00:00+00:00',
        ),
        (
            datetime(2021, 2, 25, tzinfo=UTC),
            '2021-02-15T00:00:00+00:00',
            '2021-03-15T00:00:00+00:00',
        ),
        (
            datetime(2021, 9, 25, tzinfo=UTC),
            '2021-09-15T00:00:00+00:00',
            '2021-10-15T00:00:00+00:00',
        ),
    ),
)
@pytest.mark.freeze_time()
@pytest.mark.django_db
def test_subscription_billing_cycle(freeze_dt, expected_start, expected_end):
    region = baker.make(Region, time_zone_name='UTC')
    business = baker.make(
        Business,
        region=region,
        time_zone_name=region.time_zone_name,
    )

    with (
        freeze_time(freeze_dt),
        patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'),
    ):
        subscription = baker.make(
            Subscription,
            source=Business.PaymentSource.OFFLINE,
            start=datetime(2021, 1, 15, tzinfo=UTC),
            business=business,
        )

        data = SubscriptionSerializer(instance=subscription).data

    assert data['billing_cycle']['start'] == expected_start
    assert data['billing_cycle']['end'] == expected_end
