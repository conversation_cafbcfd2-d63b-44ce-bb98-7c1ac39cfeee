import typing as t
import unittest
from unittest.mock import (
    MagicMock,
    patch,
    PropertyMock,
)

import pytest
from django.contrib.admin import AdminSite
from django.contrib.auth.models import Permission
from django.test import override_settings
from django.urls.base import reverse
from model_bakery import baker
from parameterized import parameterized

from lib.feature_flag.feature.admin import ShowInvoiceAddressInBuyerAdminFlag
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from webapps.admin_extra.tests import DjangoTestCase
from webapps.business.models import Business
from webapps.navision.baker_recipes import navision_settings_recipe
from webapps.navision.models import Merchant, NavisionSettings
from webapps.purchase.admin import (
    SubscriptionAdmin,
    SubscriptionCountFilter as SubsCntFiltr,
    SubscriptionInlineForm,
)
from webapps.purchase.models import (
    InvoiceAddress,
    Subscription,
    SubscriptionBuyer,
)
from webapps.purchase.query_sets import (
    get_queryset_with_subscription_count_annotation as get_qs,
)
from webapps.purchase.recipes import sales_subscription_buyer_recipe
from webapps.purchase.tests.utils_test import (
    create_subscriptions_with_products,
)
from webapps.structure.models import Region
from webapps.user.baker_recipes import user_recipe
from webapps.user.groups import GroupName


@pytest.mark.django_db
class TestSubscriptionCountFilterInAdminPanel(unittest.TestCase):
    def setUp(self):
        self.business1 = baker.make(Business)
        self.business2 = baker.make(Business)

    def test_no_subscriptions_with_count_of_one_should_be_found(self):
        create_subscriptions_with_products(self.business1, 2)
        create_subscriptions_with_products(self.business2, 2)
        filtr = SubsCntFiltr(
            None,
            {'subscription_count': SubsCntFiltr.SUBSCRIPTION_COUNT_ONE},
            Subscription,
            SubscriptionAdmin,
        )
        self.assertEqual(len(filtr.queryset(None, get_qs().all())), 0)

    def test_no_subscriptions_with_count_of_more_than_one_should_be_found(self):
        create_subscriptions_with_products(self.business1, 1)
        create_subscriptions_with_products(self.business2, 1)
        filtr = SubsCntFiltr(
            None,
            {
                'subscription_count': (SubsCntFiltr.SUBSCRIPTION_COUNT_MORE_THAN_ONE),
            },
            Subscription,
            SubscriptionAdmin,
        )
        self.assertEqual(len(filtr.queryset(None, get_qs().all())), 0)

    def test_result_found_has_count_of_one_when_filtering_subscriptions_of_count_one(
        self,
    ):  # pylint: disable=line-too-long
        create_subscriptions_with_products(self.business1, 1)
        create_subscriptions_with_products(self.business2, 3)
        filtr = SubsCntFiltr(
            None,
            {'subscription_count': SubsCntFiltr.SUBSCRIPTION_COUNT_ONE},
            Subscription,
            SubscriptionAdmin,
        )
        self.assertEqual(filtr.queryset(None, get_qs().all())[0].subscription_count, 1)

    def test_result_found_has_count_more_than_1_when_filtering_subscriptions_more_than_1(
        self,
    ):  # pylint: disable=line-too-long
        create_subscriptions_with_products(self.business1, 1)
        create_subscriptions_with_products(self.business2, 3)
        filtr = SubsCntFiltr(
            None,
            {
                'subscription_count': (SubsCntFiltr.SUBSCRIPTION_COUNT_MORE_THAN_ONE),
            },
            Subscription,
            SubscriptionAdmin,
        )
        self.assertEqual(filtr.queryset(None, get_qs().all())[0].subscription_count, 3)


@patch.object(SubscriptionBuyer, 'navision_invoicing_allowed', PropertyMock(return_value=True))
class TestSubscriptionBuyerAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.user = self.login_admin()
        self.invoice_address = baker.make(
            InvoiceAddress,
            zipcode=baker.make(
                Region,
                name='01-100',
                type=Region.Type.ZIP,
            ),
            state=baker.make(
                Region,
                name='mazowieckie',
                abbrev='mazowieckie',
                type=Region.Type.STATE,
            ),
            address_details1='al. Róż 1',
            city='Warszawa',
        )

    @staticmethod
    def get_payload(instance: t.Optional['SubscriptionBuyer'] = None) -> dict:
        payload = {
            'invoice_email': '',
            'tax_id': '',
            'entity_name': '',
            'payment_due_days': 7,
            'invoice_address': str(instance.invoice_address_id) if instance else '',
            'is_verified': False,
            'active': True,
            'businesses-INITIAL_FORMS': 0,
            'businesses-TOTAL_FORMS': 0,
            'subscriptions-INITIAL_FORMS': 0,
            'subscriptions-TOTAL_FORMS': 0,
            'invoicing_exclusion_reason': '',
            'invoicing_allowed': True,
        }
        if instance and instance.invoice_address:
            payload.update(
                {
                    'address_details1': instance.invoice_address.address_details1,
                    'city': instance.invoice_address.city,
                    'zipcode': instance.invoice_address.zipcode.id,
                    'state': instance.invoice_address.state.id,
                }
            )

        for field in [
            'invoice_email',
            'tax_id',
            'entity_name',
            'invoice_address',
            'is_verified',
            'active',
        ]:
            if instance:
                payload[field] = (
                    getattr(instance, "invoice_address_id" if field == "invoice_address" else field)
                    or ""
                )
        return payload

    def test_changelist_works(self):
        url = reverse('admin:purchase_subscriptionbuyer_changelist')

        baker.make(SubscriptionBuyer, entity_name="My lovely business")

        resp = self.client.get(url)

        self.assertContains(resp, 'My lovely business')

    def test_change_works_for_staff(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='My lovely business',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            invoice_address=self.invoice_address,
        )

        user = user_recipe.make(
            is_superuser=False,
            is_staff=True,
        )

        self.user = self.login_admin(admin=user)
        self.user.user_permissions.add(
            Permission.objects.get_by_natural_key(
                'change_subscriptionbuyer', 'purchase', 'subscriptionbuyer'
            )
        )

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        resp = self.client.get(
            url,
            follow=True,
        )

        self.assertEqual(resp.status_code, 200)

        data = self.get_payload(instance)
        data["entity_name"] = 'changed name'
        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )
        self.assertEqual(resp.status_code, 200)
        self.assertContains(resp, 'changed successfully')

        instance.refresh_from_db()
        self.assertEqual(instance.entity_name, 'changed name')

    def test_cs_is_assigned_after_verification(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            invoice_address=self.invoice_address,
        )

        self.assertIsNone(instance.verified_by)
        self.assertIsNone(instance.verified_at)

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        # without verification (user is not saved)
        resp = self.client.post(
            url,
            data=self.get_payload(instance),
            follow=True,
        )

        self.assertContains(resp, 'changed successfully')

        instance.refresh_from_db()
        self.assertIsNone(instance.verified_by)
        self.assertIsNone(instance.verified_at)

        # with verification (user is saved)
        data = self.get_payload(instance)
        data["is_verified"] = True
        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )

        self.assertContains(resp, 'changed successfully')

        instance.refresh_from_db()
        self.assertEqual(instance.verified_by, self.user)
        self.assertTrue(instance.verified_at)

    def test_cant_verify_twice(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            is_verified=True,
            verified_by=baker.make('user.User'),
            verified_at=tznow(),
            invoice_address=self.invoice_address,
        )

        verified_by = instance.verified_by
        verified_at = instance.verified_at

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        resp = self.client.post(
            url,
            data=self.get_payload(instance),
            follow=True,
        )

        self.assertContains(resp, 'changed successfully')

        instance.refresh_from_db()
        self.assertNotEqual(verified_by, self.user)
        self.assertEqual(instance.verified_by, verified_by)
        self.assertEqual(instance.verified_at, verified_at)

    @parameterized.expand(
        [
            (False, False),
            (True, True),
        ]
    )
    def test_is_verified_field_disabled_after_verification(self, is_verified, exp_value):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Test Co.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            is_verified=is_verified,
            verified_by=baker.make('user.User'),
            verified_at=tznow(),
        )

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        resp = self.client.get(url)

        form = resp.context['adminform'].form
        self.assertEqual(form.fields["is_verified"].disabled, exp_value)

    @parameterized.expand(
        [
            (False, False, False),
            (True, False, False),
            (True, True, True),
        ]
    )
    def test_tax_id_field_disabled_or_not(self, merchant_exists, merchant_synced, exp_value):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Test Co.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            is_verified=True,
            verified_by=baker.make('user.User'),
            verified_at=tznow(),
        )
        if merchant_exists:
            instance.merchant = baker.make(Merchant, sent_to_production=merchant_synced)
            instance.save()

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        resp = self.client.get(url)

        form = resp.context['adminform'].form
        self.assertEqual(form.fields["tax_id"].disabled, exp_value)

    @parameterized.expand(
        [
            (False, False, '4974807788'),
            (True, False, '4974807788'),
            (True, True, '1222038222'),
        ]
    )
    def test_change_tax_id_if_entered(self, merchant_exists, merchant_synced, exp_value):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id='1222038222',
            invoice_email='<EMAIL>',
            is_verified=True,
            verified_by=baker.make('user.User'),
            verified_at=tznow(),
            invoice_address=self.invoice_address,
        )

        if merchant_exists:
            instance.merchant = baker.make(Merchant, sent_to_production=merchant_synced)
            instance.save()

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        data = self.get_payload(instance)
        data["tax_id"] = '4974807788'
        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )

        self.assertContains(resp, 'changed successfully')

        instance.refresh_from_db()
        self.assertEqual(instance.tax_id, exp_value)

    def test_check_required_fields_when_no_tax_id(self):
        instance = baker.make(
            SubscriptionBuyer,
            invoice_email='<EMAIL>',
        )

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        data = self.get_payload(instance)
        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )

        self.assertEqual(resp.status_code, 200)

        form = resp.context['adminform'].form

        self.assertFalse(form.is_valid())
        self.assertIn('entity_name', form.errors)

        # Add them and save
        data['entity_name'] = 'Company Ltd.'
        data['invoice_address'] = baker.make(
            InvoiceAddress, zipcode=baker.make(Region, type=Region.Type.ZIP, name='66100')
        ).pk

        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )
        self.assertContains(resp, 'changed successfully')

    @parameterized.expand(
        [
            ('123', True),
            (' 112233   ', True),
            ('', False),
            (' ', False),
        ]
    )
    def test_update_new_invoice_details_vat_registered(self, tax_id, expected_value):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            is_verified=False,
            invoice_address=self.invoice_address,
        )

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))
        data = self.get_payload(instance)
        data['tax_id'] = tax_id

        self.client.post(url, data=data, follow=True)

        self.assertEqual(1, SubscriptionBuyer.objects.count())
        subbuyer = SubscriptionBuyer.objects.first()
        self.assertEqual(expected_value, subbuyer.vat_registered)

    def test_update_new_invoice_details_vat_registered_no_changes_in_tax(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            is_verified=False,
            invoice_address=self.invoice_address,
            vat_registered=None,
        )

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))
        data = self.get_payload(instance)
        data['entity_name'] = 'aaa'

        self.client.post(url, data=data, follow=True)

        self.assertEqual(1, SubscriptionBuyer.objects.count())
        self.assertEqual(True, SubscriptionBuyer.objects.first().vat_registered)

    def test_add_new(self):
        url = reverse('admin:purchase_subscriptionbuyer_add')

        data = self.get_payload()
        data['entity_name'] = 'Company Ltd.'
        data['tax_id'] = '4974807788'
        data['invoice_email'] = '<EMAIL>'
        data['invoice_address'] = str(self.invoice_address.id)

        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )

        self.assertContains(resp, 'added successfully')

        self.assertEqual(SubscriptionBuyer.objects.count(), 1)

    @parameterized.expand(
        [
            ('123', True),
            (' 112233   ', True),
            ('', False),
            (' ', False),
        ]
    )
    def test_add_new_invoice_details_vat_registered(self, tax_id, expected_value):
        url = reverse('admin:purchase_subscriptionbuyer_add')
        data = self.get_payload()
        data['entity_name'] = 'Company Ltd.'
        data['tax_id'] = tax_id
        data['invoice_email'] = '<EMAIL>'
        data['invoice_address'] = str(self.invoice_address.id)

        self.client.post(url, data=data, follow=True)

        self.assertEqual(1, SubscriptionBuyer.objects.count())
        subbuyer = SubscriptionBuyer.objects.first()
        self.assertEqual(expected_value, subbuyer.vat_registered)

    def test_its_impossible_to_set_payment_due_days_outside_of_predefined_ones(self):
        url = reverse('admin:purchase_subscriptionbuyer_add')

        data = self.get_payload()
        data['payment_due_days'] = 9

        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )

        self.assertContains(resp, 'Select a valid choice. 9 is not one of the available choices.')

        self.assertEqual(SubscriptionBuyer.objects.count(), 0)

    @override_settings(API_COUNTRY='pl')
    def test_nip_validation_for_pl_works(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            invoice_email='<EMAIL>',
            invoice_address=self.invoice_address,
        )

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        # invalid NIP
        data = self.get_payload(instance)
        data['tax_id'] = 'abc'

        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )

        self.assertEqual(resp.status_code, 200)

        form = resp.context['adminform'].form

        self.assertFalse(form.is_valid())
        self.assertIn('tax_id', form.errors)

        # valid NIP
        data['tax_id'] = '4974807788'

        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )

        self.assertContains(resp, 'changed successfully')

    def test_create_tax_merchant(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            invoice_address=self.invoice_address,
        )

        self.assertIsNone(instance.merchant)

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        data = self.get_payload(instance)
        data["is_verified"] = True
        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )

        self.assertContains(resp, 'changed successfully')

        instance.refresh_from_db()

        merchant = Merchant.objects.get()
        self.assertEqual(instance.merchant, merchant)

    @override_settings(API_COUNTRY="us")
    def test_create_non_tax_merchant(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            invoice_email="<EMAIL>",
            invoice_address=self.invoice_address,
        )

        self.assertIsNone(instance.merchant)

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        data = self.get_payload(instance)
        data["is_verified"] = True
        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )

        self.assertContains(resp, 'changed successfully')

        instance.refresh_from_db()

        merchant = Merchant.objects.get()
        self.assertEqual(instance.merchant, merchant)

        # Checks if data were properly assigned
        self.assertIsNone(merchant.tax_id)
        self.assertEqual(merchant.entity_name, "Company Ltd.")
        self.assertEqual(merchant.invoice_emails, ["<EMAIL>"])
        self.assertEqual(merchant.address_details1, "al. Róż 1")
        self.assertEqual(merchant.city, "Warszawa")
        self.assertEqual(merchant.zip_code, "01-100")
        self.assertEqual(merchant.state, "mazowieckie")
        self.assertEqual(merchant.country_code, "us")

    def test_update_merchant(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            is_verified=True,
            merchant=baker.make(Merchant, invoice_emails=["<EMAIL>"]),
            invoice_address=self.invoice_address,
        )

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        data = self.get_payload(instance)
        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )

        self.assertContains(resp, 'changed successfully')

        instance.refresh_from_db()

        merchant = Merchant.objects.get()
        self.assertEqual(instance.merchant, merchant)
        self.assertEqual(merchant.invoice_emails, ["<EMAIL>"])

    def test_cannot_set_invoicing_allowed_false_without_reason(self):
        instance = baker.make(
            SubscriptionBuyer,
            tax_id="1222038222",
            invoice_email="<EMAIL>",
        )

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        data = self.get_payload(instance)
        data["invoicing_allowed"] = False
        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )

        self.assertContains(resp, 'Reason is required if Invoicing Allowed is set to False.')

        instance.refresh_from_db()

        self.assertTrue(instance.invoicing_allowed)
        self.assertIsNone(instance.invoicing_exclusion_reason)

    def test_set_invoicing_allowed_false_with_reason(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            invoice_address=self.invoice_address,
        )

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))

        data = self.get_payload(instance)
        data["invoicing_allowed"] = False
        data["invoicing_exclusion_reason"] = "Don't invoice me!"
        resp = self.client.post(
            url,
            data=data,
            follow=True,
        )

        self.assertContains(resp, 'changed successfully')

        instance.refresh_from_db()

        self.assertFalse(instance.invoicing_allowed)
        self.assertEqual(instance.invoicing_exclusion_reason, "Don't invoice me!")

    def test_invoicing_allowed_filters(self):
        # Buyer withour merchant should be excluded
        baker.make(
            SubscriptionBuyer,
            businesses=[
                baker.make(Business, status=Business.Status.PAID),
            ],
        )
        # Inactive buyers should be excluded
        baker.make(
            SubscriptionBuyer,
            businesses=[
                baker.make(Business, status=Business.Status.PAID),
            ],
            active=False,
            merchant=baker.make(Merchant),
        )
        # Buyers without any business should be excluded
        baker.make(SubscriptionBuyer, _quantity=2)
        # Buyers with invoicing_allowed=False should be excluded
        baker.make(
            SubscriptionBuyer,
            businesses=[
                baker.make(Business, status=Business.Status.PAID),
                baker.make(Business, status=Business.Status.SETUP),
            ],
            invoicing_allowed=False,
            merchant=baker.make(Merchant),
        )
        # Enterprises should be excluded
        baker.make(
            SubscriptionBuyer,
            businesses=[
                baker.make(Business, status=Business.Status.PAID),
                baker.make(
                    Business,
                    integrations={
                        "importer": "enterprise_importer",
                    },
                ),
            ],
            merchant=baker.make(Merchant),
        )
        # Buyers with invoicing_exclusion_reason only should not be excluded
        baker.make(
            SubscriptionBuyer,
            businesses=[
                baker.make(Business, status=Business.Status.PAID),
            ],
            invoicing_exclusion_reason='Exclude me!',
            merchant=baker.make(Merchant),
        )

        url = reverse('admin:purchase_subscriptionbuyer_changelist')

        resp = self.client.get(url)
        result_list = resp.context['cl'].result_list
        self.assertEqual(result_list.count(), 7)

        resp = self.client.get(url + "?navision_invoicing_allowed=yes")
        result_list = resp.context['cl'].result_list
        self.assertEqual(result_list.count(), 1)

        resp = self.client.get(url + "?navision_invoicing_allowed=no")
        result_list = resp.context['cl'].result_list
        self.assertEqual(result_list.count(), 6)

    def test_owned_businesses_filter(self):
        # no business
        baker.make(
            SubscriptionBuyer,
            businesses=[],
            _quantity=3,
        )

        for _ in range(2):
            # one business
            baker.make(
                SubscriptionBuyer,
                businesses=[baker.make(Business)],
            )

        baker.make(
            SubscriptionBuyer,
            businesses=[baker.make(Business), baker.make(Business)],
        )

        url = reverse('admin:purchase_subscriptionbuyer_changelist')

        resp = self.client.get(url)
        result_list = resp.context['cl'].result_list
        self.assertEqual(result_list.count(), 6)

        resp = self.client.get(url + '?owned_businesses=zero')
        result_list = resp.context['cl'].result_list
        self.assertEqual(result_list.count(), 3, result_list)

        resp = self.client.get(url + '?owned_businesses=one')
        result_list = resp.context['cl'].result_list
        self.assertEqual(result_list.count(), 2)

        resp = self.client.get(url + '?owned_businesses=multiple')
        result_list = resp.context['cl'].result_list
        self.assertEqual(result_list.count(), 1)

    @override_eppo_feature_flag({ShowInvoiceAddressInBuyerAdminFlag.flag_name: True})
    def test_update_to_different_invoicing_address(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            is_verified=True,
            merchant=baker.make(Merchant, invoice_emails=["<EMAIL>"]),
            invoice_address=self.invoice_address,
        )
        self.assertEqual(instance.invoice_address, self.invoice_address)
        data = self.get_payload(instance)
        new_invoice_address = baker.make(
            InvoiceAddress, zipcode=self.invoice_address.zipcode, state=self.invoice_address.state
        )

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))
        data['invoice_address'] = new_invoice_address.pk
        self.client.post(
            url,
            data=data,
            follow=True,
        )
        instance.refresh_from_db()
        self.assertEqual(instance.invoice_address, new_invoice_address)

    @override_eppo_feature_flag({ShowInvoiceAddressInBuyerAdminFlag.flag_name: True})
    def test_update_invoicing_address_values(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            is_verified=True,
            merchant=baker.make(Merchant, invoice_emails=["<EMAIL>"]),
            invoice_address=self.invoice_address,
        )
        data = self.get_payload(instance)
        data['city'] = 'new city'
        data['address_details1'] = 'new details'
        data['zipcode'] = baker.make(Region, type=Region.Type.ZIP, name='13373').id
        data['state'] = baker.make(Region, type=Region.Type.STATE, name='new state').id

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))
        self.client.post(
            url,
            data=data,
            follow=True,
        )
        instance.refresh_from_db()

        self.assertEqual(instance.invoice_address.city, 'new city')
        self.assertEqual(instance.invoice_address.address_details1, 'new details')
        self.assertEqual(instance.invoice_address.zipcode.name, '13373')
        self.assertEqual(instance.invoice_address.state.name, 'new state')

    @override_eppo_feature_flag({ShowInvoiceAddressInBuyerAdminFlag.flag_name: True})
    def test_update_invoicing_address_with_incorrect_values(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            is_verified=True,
            merchant=baker.make(Merchant, invoice_emails=["<EMAIL>"]),
            invoice_address=self.invoice_address,
        )
        data = self.get_payload(instance)
        data['city'] = 'new city'
        data['address_details1'] = 'new details'
        data['zipcode'] = '999'
        data['state'] = '991'

        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))
        response = self.client.post(
            url,
            data=data,
            follow=True,
        )
        self.assertContains(response, 'Select a valid choice.')

        # old details kept
        self.assertEqual(instance.invoice_address.city, 'Warszawa')
        self.assertEqual(instance.invoice_address.address_details1, 'al. Róż 1')
        self.assertEqual(instance.invoice_address.zipcode.name, '01-100')
        self.assertEqual(instance.invoice_address.state.name, 'mazowieckie')

    @override_eppo_feature_flag({ShowInvoiceAddressInBuyerAdminFlag.flag_name: True})
    def test_edit_and_change_invoice_address(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            is_verified=True,
            merchant=baker.make(Merchant, invoice_emails=["<EMAIL>"]),
            invoice_address=self.invoice_address,
        )
        data = self.get_payload(instance)
        data['city'] = 'new city'
        data['invoice_address'] = baker.make(InvoiceAddress).id
        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))
        response = self.client.post(
            url,
            data=data,
            follow=True,
        )
        self.assertContains(
            response,
            'Cannot edit the reference to an invoice address and its details at the same time',
        )

    @override_eppo_feature_flag({ShowInvoiceAddressInBuyerAdminFlag.flag_name: True})
    def test_try_to_change_and_edit_invoicing_address(self):
        instance = baker.make(
            SubscriptionBuyer,
            entity_name='Company Ltd.',
            tax_id="1222038222",
            invoice_email="<EMAIL>",
            is_verified=True,
            merchant=baker.make(Merchant, invoice_emails=["<EMAIL>"]),
        )
        data = self.get_payload(instance)
        data['city'] = 'new city'
        data['address_details1'] = 'new details'
        data['zipcode'] = baker.make(Region, type=Region.Type.ZIP, name='13373').id
        data['state'] = baker.make(Region, type=Region.Type.STATE, name='new state').id
        self.assertEqual(InvoiceAddress.objects.count(), 1)  # setup one
        url = reverse('admin:purchase_subscriptionbuyer_change', args=(instance.pk,))
        self.client.post(
            url,
            data=data,
            follow=True,
        )
        self.assertEqual(InvoiceAddress.objects.count(), 2)

        instance.refresh_from_db()
        self.assertEqual(instance.invoice_address.city, 'new city')
        self.assertEqual(instance.invoice_address.address_details1, 'new details')
        self.assertEqual(instance.invoice_address.zipcode.name, '13373')
        self.assertEqual(instance.invoice_address.state.name, 'new state')


@override_settings(LIVE_DEPLOYMENT=True)
class ActionsAdminDjangoPermissions(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.buyer = baker.make(SubscriptionBuyer, entity_name="My lovely business")
        self.change_url = reverse('admin:purchase_subscriptionbuyer_changelist')

    @parameterized.expand(
        [
            ('invoice_boost_online',),
            ('invoice_boost_offline',),
            ('invoice_saas_offline',),
            ('invoice_saas_online',),
        ]
    )
    def test_action_with_permission(self, action_name):
        user = user_recipe.make(
            groups=[baker.make('Group', name=GroupName.NAVISION_ADMIN)],
            is_superuser=False,
            is_staff=True,
        )
        self.user = self.login_admin(admin=user)

        self.user.user_permissions.add(
            Permission.objects.get_by_natural_key(
                'change_subscriptionbuyer', 'purchase', 'subscriptionbuyer'
            )
        )

        data = {
            'action': action_name,
            '_selected_action': [self.buyer.id],
        }
        response = self.client.post(self.change_url, data, follow=True)
        self.assertEqual(200, response.status_code)

    @parameterized.expand(
        [
            ('invoice_boost_online',),
            ('invoice_boost_offline',),
            ('invoice_saas_offline',),
            ('invoice_saas_online',),
        ]
    )
    def test_action_without_permission(self, action_name):
        user = user_recipe.make(
            is_staff=True,
            is_superuser=False,
        )
        self.user = self.login_admin(user)

        data = {
            'action': action_name,
            '_selected_action': [self.buyer.id],
        }
        response = self.client.post(self.change_url, data, follow=True)
        self.assertEqual(403, response.status_code)


@patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay')
class TestSubscriptionInlineForm(DjangoTestCase):
    @staticmethod
    def _included_subscription(payment_source: Business.PaymentSource) -> Subscription:
        return baker.make(
            Subscription,
            source=payment_source,
            navision_invoicing_allowed=True,
            navision_invoicing_exclusion_reason=None,
        )

    @staticmethod
    def _excluded_subscription(payment_source: Business.PaymentSource) -> Subscription:
        return baker.make(
            Subscription,
            source=payment_source,
            navision_invoicing_allowed=False,
            navision_invoicing_exclusion_reason='**** Exclusion Reason ****',
        )

    def test_exclusion_fields_are_disabled_for_non_offline_subscription(self, _mock):
        offline_subscription = self._included_subscription(Business.PaymentSource.OFFLINE)

        offline_form = SubscriptionInlineForm(instance=offline_subscription)

        self.assertFalse(offline_form.fields['navision_invoicing_allowed'].disabled)
        self.assertFalse(offline_form.fields['navision_invoicing_exclusion_reason'].disabled)

        online_subscription = self._included_subscription(Business.PaymentSource.BRAINTREE)

        online_form = SubscriptionInlineForm(instance=online_subscription)

        self.assertTrue(online_form.fields['navision_invoicing_allowed'].disabled)
        self.assertTrue(online_form.fields['navision_invoicing_exclusion_reason'].disabled)

    def test_exclusion_reason_is_mandatory_if_subscription_is_excluded_from_invoicing(self, _mock):
        subscription = self._included_subscription(Business.PaymentSource.OFFLINE)

        form = SubscriptionInlineForm(
            instance=subscription,
            data={
                'navision_invoicing_allowed': False,
            },
        )

        self.assertFalse(form.is_valid())
        self.assertEqual(
            form.errors['navision_invoicing_exclusion_reason'],
            ['Please provide exclusion reason'],
        )

    def test_exclusion_reason_is_never_mandatory_for_online_subscriptions(self, _mock):
        # I mean. It would be really bad to lock SubscriptionBuyer screen because
        # some script or import tool somehow changed that flag

        subscription = self._excluded_subscription(Business.PaymentSource.BRAINTREE)

        form = SubscriptionInlineForm(
            instance=subscription,
            data={
                'navision_invoicing_allowed': False,
                'navision_invoicing_exclusion_reason': None,
            },
        )

        self.assertTrue(form.is_valid())
        self.assertEqual(form.errors, {})

    def test_switch_included_subscription_to_excluded(self, _mock):
        subscription = self._included_subscription(Business.PaymentSource.OFFLINE)
        exclusion_reason = 'Exclusion reason'

        form = SubscriptionInlineForm(
            instance=subscription,
            data={
                'navision_invoicing_allowed': False,
                'navision_invoicing_exclusion_reason': exclusion_reason,
            },
        )

        self.assertTrue(form.is_valid(), form.errors)

        form.save()

        subscription.refresh_from_db()

        self.assertFalse(subscription.navision_invoicing_allowed)
        self.assertEqual(subscription.navision_invoicing_exclusion_reason, exclusion_reason)

    def test_switch_excluded_subscription_to_included(self, _mock):
        subscription = self._excluded_subscription(Business.PaymentSource.OFFLINE)

        form = SubscriptionInlineForm(
            instance=subscription,
            data={
                'navision_invoicing_allowed': True,
            },
        )

        self.assertTrue(form.is_valid(), form.errors)

        form.save()

        subscription.refresh_from_db()

        self.assertTrue(subscription.navision_invoicing_allowed)


class TestNavisionIntegrationDisabledAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.user = self.login_admin()
        self.instance = baker.make(
            Subscription,
            source=Business.PaymentSource.OFFLINE,
            navision_invoicing_allowed=True,
            navision_invoicing_exclusion_reason=None,
            buyer=sales_subscription_buyer_recipe.make(),
            product=baker.make('purchase.SubscriptionListing'),
        )
        self.offline_form = SubscriptionInlineForm(instance=self.instance)
        self.user = baker.make(
            'user.User',
            groups=[
                baker.make(
                    'Group',
                    name=GroupName.SUBSCRIPTION_EDITOR,
                ),
            ],
        )
        self.request = MagicMock(
            user=self.user,
        )
        navision_settings_recipe.make()

    def update_subscription_model(self, navision_integration_enabled: bool = True):
        NavisionSettings.objects.update(navision_integration_enabled=navision_integration_enabled)
        admin = SubscriptionAdmin(Subscription, AdminSite())
        admin.save_model(
            obj=self.instance,
            request=self.request,
            form=self.offline_form,
            change=None,
        )

    def test_navision_integration_enabled(self):
        self.update_subscription_model(navision_integration_enabled=True)
        expected_message = 'Merchant will be send to navision in 2-3 hours'
        self.assertIn(expected_message, self.request.method_calls[0][1][1])

    def test_navision_integration_disabled(self):
        self.update_subscription_model(navision_integration_enabled=False)
        expected_message = (
            'navision_integration_enabled field disabled integration of all merchants.'
            'To enable synchronization of Merchant,'
            'NavisionSettings navision_integration_enabled field needs to be changed to True'
        )
        self.assertIn(expected_message, self.request.method_calls[0][1][1])
