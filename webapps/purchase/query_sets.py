from django.db.models import Count

from webapps.purchase.models import Subscription


def get_queryset_with_subscription_count_annotation(qs=Subscription.objects):
    '''
    :param qs: QuerySet on model Subscription
    :return: QuerySet with subscription_count which will set attribute for Subscription instances
    '''
    return qs.prefetch_related(
        'transactions',
    ).annotate(subscription_count=Count('business__subscriptions'))
