from datetime import timedelta
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils.translation import gettext as _

from lib.tools import datetimeinfinity
from lib.tools import tznow
from webapps.c2b_referral.models import RewardC2B
from webapps.notification.models import NotificationHistory
from webapps.notification.push import notification_receivers_list
from webapps.notification.tasks.push import send_push_notification
from webapps.pop_up_notification.models import (
    RewardC2BStatusChangeNotification,
)


def biz_1st_subsc_c2b(sender, **kwargs):
    """Func changes status of RewardC2B and sends notification to referrer"""

    sub = kwargs.get('instance', None)

    if not sub or not sub.is_first_paid_cycle:
        return

    reward = RewardC2B.objects.filter(
        business=sub.business,
        status=RewardC2B.STATUS_PENDING,
    ).first()

    if not reward:
        return

    reward.status = RewardC2B.STATUS_PAYING
    reward.end_date = tznow() + timedelta(
        days=(reward.required_paying_months - 1) * 33,
    )
    # 33 days to be sure, that previous subscription cycle ended.
    # required_paying_months certainly won't be longer than 6 months,
    # so this few extra days won't make the difference.
    reward.save()

    user_id = reward.user.id

    rc2bsc_notification = RewardC2BStatusChangeNotification(
        user=reward.user,
        valid_till=datetimeinfinity(),
        status=RewardC2BStatusChangeNotification.STATUS_PAYING,
        business_name=reward.business.name,
        required_paying_months=reward.required_paying_months,
        reward_text=reward.reward,
        reward_text_short=reward.reward_short,
        business=reward.business,
    )
    rc2bsc_notification.save()

    history_data = {
        'sender': NotificationHistory.SENDER_SYSTEM,
        'task_id': 'c2b_ref_biz_paying:biz_id={};user_id={}'.format(
            sub.business.id,
            user_id,
        ),
    }

    body_push = _(
        "{} has subscribed to Booksy! Once they use Booksy for {} months you will receive {}!"
    ).format(
        reward.business.name,
        reward.required_paying_months,
        reward.reward,
    )

    send_push_notification(
        (
            notification_receivers_list(user_id=reward.user.id, customer=True)
            if reward.user is not None
            else []
        ),
        body_push,
        target=('referral_c2b_reward_change',),
        history_data=history_data,
        push_ios=True,
    )
