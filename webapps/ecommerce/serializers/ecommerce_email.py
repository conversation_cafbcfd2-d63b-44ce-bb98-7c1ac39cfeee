# pylint: disable=no-name-in-module
from booksy_proto_ecommerce.pubsub.v1.ecommerce_email_pb2 import EcommerceEmail

# pylint: enable=no-name-in-module
from django_socio_grpc.proto_serializers import ProtoSerializer
from rest_framework import serializers


class EcommerceEmailPDfDataSerializer(ProtoSerializer):
    name = serializers.CharField()
    body_content = serializers.CharField()
    body_style = serializers.CharField()


class EcommerceEmailMessageSerializer(ProtoSerializer):
    class Meta:
        proto_class = EcommerceEmail

    to_addresses = serializers.ListSerializer(child=serializers.Char<PERSON><PERSON>())
    body = serializers.CharField()
    subject = serializers.Char<PERSON>ield()
    pdf_data = EcommerceEmailPDfDataSerializer(required=False)
