import pytest

from webapps.ecommerce.serializers.copy_to_inventory import CopyToInventoryMessageSerializer


class TestCopyToInventorySerializer:
    def message(self, with_supplier: bool = True) -> dict:
        supplier = (
            {
                'id': '0a70f296-98e1-447f-b593-387d3be1f837',
                'name': 'Supplier',
                'tax_id': '012345',
                'email': '<EMAIL>',
                'phone': '*********',
                'country': 'PL',
                'zip_code': '11222',
                'city': 'Katowice',
                'address': 'Prosta 12/34',
            }
            if with_supplier
            else None
        )
        return {
            'business_id': 1,
            'catalog_products': [
                {
                    'name': 'Lakier',
                    'leaf_categories_ids': [
                        '835bc22d-9346-4eec-8008-7ef0fac1817c',
                        '6c7c315d-84d7-440b-be52-ea930cdb4099',
                    ],
                    'all_categories': [
                        {
                            'id': 'e6853fba-1257-4812-8c1f-62a632cf3271',
                            'name': '<PERSON><PERSON><PERSON><PERSON>',
                            'parent_id': None,
                        },
                        {
                            'id': '0469031f-6730-47b7-b4fb-6962322a11c4',
                            'name': 'Lakiery',
                            'parent_id': 'e6853fba-1257-4812-8c1f-62a632cf3271',
                        },
                        {
                            'id': '835bc22d-9346-4eec-8008-7ef0fac1817c',
                            'name': 'Lakiery hybrydowe',
                            'parent_id': '0469031f-6730-47b7-b4fb-6962322a11c4',
                        },
                        {
                            'id': '6c7c315d-84d7-440b-be52-ea930cdb4099',
                            'name': 'Lakiery kolorowe',
                            'parent_id': '0469031f-6730-47b7-b4fb-6962322a11c4',
                        },
                    ],
                    'sku': '12345',
                    'tax_rate': '12.50',
                    'gross_price': '100.00',
                    'net_price': '87.50',
                    'supplier': supplier,
                    'ean': '879118681983',
                    'volume_unit': 'gram',
                    'description': 'Lakier do paznokci',
                    'main_photo_url': 'http://photo.com/photo.jpg',
                    'photo_urls': [
                        'http://photo1.com/photo.jpg',
                        'http://photo2.com/photo.jpg',
                        'http://photo3.com/photo.jpg',
                    ],
                    'brand': 'Semilac',
                    'supplier_product_id': 'dbc50176-25e6-40bf-953b-9a9026c023eb',
                    'catalog_product_id': 'b18184b9-0533-4237-90b0-510d5a12b47c',
                    'catalog_product_variant_id': '4e11767c-70b1-4df6-a5ec-7835977beecd',
                }
            ],
        }

    @pytest.mark.parametrize('with_supplier', [True, False])
    def test_valid_message(self, with_supplier: bool):
        serializer = CopyToInventoryMessageSerializer(data=self.message(with_supplier))
        assert serializer.is_valid()

    @pytest.mark.parametrize(
        'missing_field',
        ['name', 'sku', 'ean', 'volume_unit', 'description', 'brand', 'catalog_product_id'],
    )
    def test_missing_required_product_fields(self, missing_field):
        message = self.message()

        message['catalog_products'][0][missing_field] = ''
        serializer = CopyToInventoryMessageSerializer(data=message)
        assert not serializer.is_valid()
        errors = serializer.errors['catalog_products']
        assert len(errors) == 1
        field_errors = errors[0]
        assert len(field_errors) == 1
        assert missing_field in field_errors

        message['catalog_products'][0][missing_field] = None
        serializer = CopyToInventoryMessageSerializer(data=message)
        assert not serializer.is_valid()
        errors = serializer.errors['catalog_products']
        assert len(errors) == 1
        field_errors = errors[0]
        assert len(field_errors) == 1
        assert missing_field in field_errors

        message['catalog_products'][0].pop(missing_field)
        serializer = CopyToInventoryMessageSerializer(data=message)
        assert not serializer.is_valid()
        errors = serializer.errors['catalog_products']
        assert len(errors) == 1
        field_errors = errors[0]
        assert len(field_errors) == 1
        assert missing_field in field_errors

    @pytest.mark.parametrize('missing_field', ['id', 'name'])
    def test_missing_required_supplier_fields(self, missing_field: str):
        message = self.message()

        message['catalog_products'][0]['supplier'][missing_field] = ''
        serializer = CopyToInventoryMessageSerializer(data=message)
        assert not serializer.is_valid()
        errors = serializer.errors['catalog_products'][0]['supplier']
        assert len(errors) == 1
        assert missing_field in errors

        message['catalog_products'][0]['supplier'][missing_field] = None
        serializer = CopyToInventoryMessageSerializer(data=message)
        assert not serializer.is_valid()
        errors = serializer.errors['catalog_products'][0]['supplier']
        assert len(errors) == 1
        assert missing_field in errors

        message['catalog_products'][0]['supplier'].pop(missing_field)
        serializer = CopyToInventoryMessageSerializer(data=message)
        assert not serializer.is_valid()
        errors = serializer.errors['catalog_products'][0]['supplier']
        assert len(errors) == 1
        assert missing_field in errors

    @pytest.mark.parametrize('missing_field', ['id', 'name'])
    def test_missing_required_category_fields(self, missing_field: str):
        message = self.message()

        message['catalog_products'][0]['all_categories'][0][missing_field] = ''
        serializer = CopyToInventoryMessageSerializer(data=message)
        assert not serializer.is_valid()
        errors = serializer.errors['catalog_products'][0]['all_categories']
        field_errors = errors[0]
        assert len(field_errors) == 1
        assert missing_field in field_errors

        message['catalog_products'][0]['all_categories'][0][missing_field] = None
        serializer = CopyToInventoryMessageSerializer(data=message)
        assert not serializer.is_valid()
        errors = serializer.errors['catalog_products'][0]['all_categories']
        field_errors = errors[0]
        assert len(field_errors) == 1
        assert missing_field in field_errors

        message['catalog_products'][0]['all_categories'][0].pop(missing_field)
        serializer = CopyToInventoryMessageSerializer(data=message)
        assert not serializer.is_valid()
        errors = serializer.errors['catalog_products'][0]['all_categories']
        field_errors = errors[0]
        assert len(field_errors) == 1
        assert missing_field in field_errors

    def test_missing_business_field(self):
        message = self.message()
        message['business_id'] = ''
        serializer = CopyToInventoryMessageSerializer(data=message)
        assert not serializer.is_valid()
        errors = serializer.errors
        assert len(errors) == 1
        assert 'business_id' in errors
