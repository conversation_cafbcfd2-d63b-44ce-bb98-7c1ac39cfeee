from mock import patch

# pylint: disable=no-name-in-module
from booksy_proto_ecommerce.pubsub.v1.ecommerce_email_pb2 import EcommerceEmail

# pylint: enable=no-name-in-module
from webapps.ecommerce.subscribers.ecommerce_email import EcommerceEmailSubscriber
from webapps.ecommerce.serializers.ecommerce_email import EcommerceEmailMessageSerializer


class TestEcommerceEmailSubscriber:

    @patch('lib.email.tasks.send_email_task.delay')
    def test_subscriber_schedules_email_without_pdf(self, send_email_mock):
        to_mail = ['<EMAIL>']
        subject = 'Subject'
        body = 'html'
        serializer = EcommerceEmailMessageSerializer(
            data={
                'subject': subject,
                'body': body,
                'to_addresses': to_mail,
            }
        )
        serializer.is_valid()
        message = serializer.message
        subscriber = EcommerceEmailSubscriber()
        subscriber.handle(message)
        send_email_mock.assert_called_once()
        mock_call_args = send_email_mock.call_args[0][0]
        assert mock_call_args['to'] == to_mail
        assert mock_call_args['subject'] == subject
        assert mock_call_args['content'] == body
        assert mock_call_args['attachments'] == []

    @patch('lib.email.tasks.send_email_task.delay')
    def test_subscriber_schedules_email_with_pdf(self, send_email_mock):
        to_mail = ['<EMAIL>']
        subject = 'Subject'
        body = 'html'
        serializer = EcommerceEmailMessageSerializer(
            data={
                'subject': subject,
                'body': body,
                'to_addresses': to_mail,
                'pdf_data': {
                    'name': 'name',
                    'body_content': '<body></body>',
                    'body_style': '<style></style>',
                },
            }
        )
        serializer.is_valid()
        message = serializer.message
        subscriber = EcommerceEmailSubscriber()
        subscriber.handle(message)
        send_email_mock.assert_called_once()
        mock_call_args = send_email_mock.call_args[0][0]
        assert mock_call_args['to'] == to_mail
        assert mock_call_args['subject'] == subject
        assert mock_call_args['content'] == body
        assert len(mock_call_args['attachments']) == 1

    @patch('lib.email.tasks.send_email_task.delay')
    def test_subscriber_recieves_invalid_message(self, send_email_mock):
        message = EcommerceEmail()
        subscriber = EcommerceEmailSubscriber()
        with patch('webapps.ecommerce.subscribers.ecommerce_email.logger') as logger_mock:
            subscriber.handle(message)
        send_email_mock.assert_not_called()
        logger_mock.error.assert_called_once()

    @patch('lib.email.tasks.send_email_task.delay')
    def test_subscriber_exception_when_scheduling_email(self, send_email_mock):
        send_email_mock.side_effect = Exception
        to_mail = ['<EMAIL>']
        subject = 'Subject'
        body = 'html'
        serializer = EcommerceEmailMessageSerializer(
            data={'subject': subject, 'body': body, 'to_addresses': to_mail}
        )
        serializer.is_valid()
        message = serializer.message
        subscriber = EcommerceEmailSubscriber()
        with patch('webapps.ecommerce.subscribers.ecommerce_email.logger') as logger_mock:
            subscriber.handle(message)
        send_email_mock.assert_called_once()
        logger_mock.error.assert_called_once()
