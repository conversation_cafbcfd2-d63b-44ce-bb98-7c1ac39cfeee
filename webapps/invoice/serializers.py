from datetime import datetime

from dateutil import parser
from rest_framework import serializers

from lib.serializers import get_attribute
from webapps.business.models import Business
from webapps.invoice.helpers import business_id_from_path
from webapps.invoice.models import (
    BusinessInvoice,
    upload_to_handler,
)


class S3InvoiceObjectSerializer(serializers.ModelSerializer):

    class Meta:
        model = BusinessInvoice
        fields = ('business', 'key', 'last_modified')

    business = serializers.PrimaryKeyRelatedField(
        queryset=Business.objects.all(),
    )
    key = serializers.CharField(source='invoice')
    last_modified = serializers.DateTimeField()

    @staticmethod
    def to_datetime(date_var) -> datetime:
        if isinstance(date_var, str):
            date_var = parser.parse(date_var)
        return datetime(date_var.year, date_var.month, date_var.day)

    def to_internal_value(self, data):
        key = get_attribute(data, ['key'])
        last_modified = self.to_datetime(get_attribute(data, ['last_modified']))
        _data = {
            'business': business_id_from_path(key),
            'key': key,
            'last_modified': last_modified,
        }
        return super().to_internal_value(_data)

    @staticmethod
    def validate_key(attr):
        return upload_to_handler(instance=None, filename=attr)
