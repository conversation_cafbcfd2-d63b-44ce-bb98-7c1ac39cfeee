import pytest
from model_bakery import baker
from webapps.business.models import Business

from webapps.b2b_referral.models import B2BReferralSetting
from webapps.structure.models import Region, RegionGraph


@pytest.fixture(scope='function')
def prepare_regions():
    usa = baker.make(
        Region,
        name='USA',
        type=Region.Type.COUNTRY,
        latitude=39.828127,
        longitude=-98.579404,
    )
    city = baker.make(Region, type=Region.Type.CITY)
    zip_code = baker.make(Region, type=Region.Type.ZIP)

    baker.make(
        RegionGraph,
        region=city,
        related_region=zip_code,
    )
    baker.make(
        RegionGraph,
        region=usa,
        related_region=city,
    )


@pytest.mark.usefixtures('prepare_regions')
@pytest.mark.django_db
def test_get_direct_setting():
    """Returns setting for this particular business"""

    business = baker.make(Business)
    business2 = baker.make(Business)

    baker.make(
        B2BReferralSetting,
        business=business2,
    )

    setting = baker.make(
        B2BReferralSetting,
        business=business,
    )

    returned_setting = B2BReferralSetting._get_direct_setting(
        business
    )  # pylint: disable=protected-access

    assert returned_setting == setting


@pytest.mark.usefixtures('prepare_regions')
@pytest.mark.django_db
def test_get_region_setting():
    """Tests if child region has priority over parent region."""

    usa = Region.objects.get(type=Region.Type.COUNTRY)
    zip_code = Region.objects.get(type=Region.Type.ZIP)

    business = baker.make(Business, region=zip_code)

    baker.make(
        B2BReferralSetting,
        region=usa,
    )

    setting_narrow = baker.make(B2BReferralSetting, region=zip_code)

    returned_setting = B2BReferralSetting._get_region_setting(
        business
    )  # pylint: disable=protected-access
    assert returned_setting == setting_narrow


@pytest.mark.usefixtures('prepare_regions')
@pytest.mark.django_db
def test_get_mixed_setting():
    """Tests if business_setting has priority over region."""

    zip_code = Region.objects.get(type=Region.Type.ZIP)
    business = baker.make(Business, region=zip_code)

    business_setting = baker.make(
        B2BReferralSetting,
        business=business,
    )

    returned_setting = B2BReferralSetting.get_setting(business)
    assert returned_setting == business_setting


@pytest.mark.django_db
def test_get_no_setting():
    """Tests if None is returned if there is no created setting for business."""

    business = baker.make(Business)
    returned_setting = B2BReferralSetting.get_setting(business)
    assert returned_setting is None


@pytest.mark.usefixtures('prepare_regions')
@pytest.mark.django_db
def test_get_affected_businesses():
    country = Region.objects.get(type=Region.Type.COUNTRY)
    city = Region.objects.get(type=Region.Type.CITY)

    zip_code1 = baker.make(Region, type=Region.Type.ZIP)
    zip_code2 = baker.make(Region, type=Region.Type.ZIP)

    baker.make(
        RegionGraph,
        region=city,
        related_region=zip_code1,
    )
    baker.make(
        RegionGraph,
        region=city,
        related_region=zip_code2,
    )

    business1 = baker.make(Business, region=zip_code1)
    business2 = baker.make(Business, region=zip_code1)
    business3 = baker.make(Business, region=zip_code2)

    setting1 = baker.make(B2BReferralSetting, region=zip_code1)

    setting2 = baker.make(
        B2BReferralSetting,
        business=business2,
    )

    setting3 = baker.make(
        B2BReferralSetting,
        region=country,
    )

    assert setting1.get_affected_businesses() == [business1]
    assert setting2.get_affected_businesses() == [business2]
    assert setting3.get_affected_businesses() == [business3]
