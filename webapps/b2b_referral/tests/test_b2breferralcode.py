import pytest
from model_bakery import baker

from webapps.b2b_referral.models import B2BReferralCode
from webapps.business.models import Business


@pytest.mark.django_db
def test_generate_code():
    """Tests simples case for code generator.
    Result: ID padded with 0. Name stripped.
    """
    business = baker.make(
        Business,
        name='newbusiness',
    )

    referral_code = B2BReferralCode.generate_code(business)
    assert len(referral_code.code) == 8
