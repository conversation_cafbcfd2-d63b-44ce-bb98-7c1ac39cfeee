from django.core.exceptions import ObjectDoesNotExist
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from webapps.b2b_referral.models import B2BReferralCode


class B2BReferralCodeField(serializers.CharField):
    default_error_messages = {
        'required': _('This field is required.'),
        'does_not_exist': _('Invalid referral code "{code}" - this code does ' 'not exist.'),
        'incorrect_type': _('Incorrect type. Expected string value, received {' 'data_type}.'),
    }

    def to_internal_value(self, data):
        try:
            return B2BReferralCode.objects.get(
                code=data,
                usage_limit__gt=0,
            )
        except ObjectDoesNotExist:
            self.fail('does_not_exist', code=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)
