import pytest

from webapps.intro_screen.contst import GENERIC
from webapps.intro_screen.data import (
    BusinessCategoryType,
    GENERIC_SCREENS,
    get_screens,
)
from webapps.intro_screen.enums import (
    IntroScreenOrder,
    IntroScreenSize,
    IntroScreenType,
)
from webapps.intro_screen.serializers import ScreenDataSerializer


def test_screen_data_serializer():
    instance = GENERIC_SCREENS.get(
        IntroScreenType.INTRO,
    ).get(IntroScreenOrder.THIRD)

    serializer = ScreenDataSerializer(instance=instance)
    expected_keys = {'header', 'body', 'image_url'}

    assert serializer.data.keys() == expected_keys


@pytest.mark.parametrize(
    'category, scree_size, screen_type, screens_count',
    (
        pytest.param(
            GENERIC, IntroScreenSize.MOBILE, IntroScreenType.INTRO, 3, id='generic mobile intro'
        ),
        pytest.param(
            GENERIC, IntroScreenSize.WEB, IntroScreenType.INTRO, 3, id='generic web intro'
        ),
        pytest.param(
            GENERIC, IntroScreenSize.MOBILE, IntroScreenType.SPLASH, 3, id='generic mobile splash'
        ),
        pytest.param(
            GENERIC, IntroScreenSize.WEB, IntroScreenType.SPLASH, 3, id='generic web splash'
        ),
        pytest.param(
            GENERIC,
            IntroScreenSize.MOBILE,
            IntroScreenType.WELCOME_BACK,
            1,
            id='generic mobile welcome back',
        ),
        pytest.param(
            GENERIC,
            IntroScreenSize.WEB,
            IntroScreenType.WELCOME_BACK,
            1,
            id='generic web welcome back',
        ),
    ),
)
def test_intro_screen_data_serializer(
    category: BusinessCategoryType,
    scree_size: IntroScreenSize,
    screen_type: IntroScreenType,
    screens_count: int,
):
    instance = get_screens(category, scree_size, screen_type)

    serializer = ScreenDataSerializer(instance, many=True)
    assert len(serializer.data) == screens_count
    assert all(screen for screen in serializer.data)
