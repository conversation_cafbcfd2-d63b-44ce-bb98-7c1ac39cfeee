# Generated by Django 4.1.7 on 2023-04-04 14:42

from django.db import migrations, models
import django.db.models.deletion
import webapps.structure.enums


class Migration(migrations.Migration):
    dependencies = [
        (
            "user",
            "0061_userinternaldata_account_deletion_cancellation_datetime_and_more",
        ),
        ("structure", "0021_alter_region_time_zone_name"),
        ("boost", "0004_alter_boostappointment_status"),
    ]

    operations = [
        migrations.CreateModel(
            name="BoostSettings",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created (UTC)"),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="Updated (UTC)"
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(blank=True, null=True, verbose_name="Deleted (UTC)"),
                ),
                ("days_threshold", models.PositiveIntegerField(default=99)),
                ("total_threshold", models.PositiveIntegerField(default=9999)),
                ("days_threshold_enabled", models.BooleanField(default=False)),
                ("total_threshold_enabled", models.BooleanField(default=False)),
                (
                    "region",
                    models.OneToOneField(
                        limit_choices_to=models.Q(
                            ("type", webapps.structure.enums.RegionType["COUNTRY"])
                        ),
                        on_delete=django.db.models.deletion.PROTECT,
                        to="structure.region",
                    ),
                ),
            ],
            options={
                "verbose_name": "Settings",
                "verbose_name_plural": "Settings",
            },
        ),
        migrations.CreateModel(
            name="BoostSettingsHistory",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created (UTC)"),
                ),
                ("data", models.TextField()),
                ("metadata", models.TextField()),
                (
                    "model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="history",
                        to="boost.boostsettings",
                    ),
                ),
                (
                    "operator",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="user.user",
                    ),
                ),
            ],
            options={
                "verbose_name": "Settings history",
                "verbose_name_plural": "Settings history",
            },
        ),
    ]
