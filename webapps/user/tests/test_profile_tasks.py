import pytest
from model_bakery import baker

from webapps.user.models import UserProfile
from webapps.user.tasks.profile import bulk_update_profile_language_task


@pytest.mark.django_db
class TestBulkUpdateProfileTask:
    def test_profile_update_language(self):
        profile = baker.make(UserProfile, language='en')

        bulk_update_profile_language_task([profile.id], 'en-gb')

        profile.refresh_from_db()
        assert profile.language == 'en-gb'

    def test_invalid_language(self):
        with pytest.raises(ValueError):
            bulk_update_profile_language_task([1], 'not-supported-language')
