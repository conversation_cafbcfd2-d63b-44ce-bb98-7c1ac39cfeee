from bo_obs.datadog.enums import BooksyTeams
from rest_framework.generics import UpdateAPIView
from rest_framework.permissions import IsAuthenticated

from drf_api.base_views import BaseBooksySessionAPIView
from webapps.user.serializers import ColorPaletteSerializer


class ColorPaletteView(BaseBooksySessionAPIView, UpdateAPIView):
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)
    permission_classes = (IsAuthenticated,)
    serializer_class = ColorPaletteSerializer
    http_method_names = ["put"]

    def get_object(self):
        return self.user
