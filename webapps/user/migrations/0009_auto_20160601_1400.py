# Generated by Django 1.9.5 on 2016-06-01 12:00
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0008_merge'),
    ]

    operations = [
        migrations.CreateModel(
            name='UnsubscribedEmail',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateField(auto_now_add=True)),
                ('email', models.EmailField(db_index=True, max_length=254, unique=True)),
            ],
        ),
        migrations.AlterField(
            model_name='emailtoken',
            name='expiry_date',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='emailtoken',
            name='token',
            field=models.CharField(max_length=45, unique=True),
        ),
        migrations.AlterUniqueTogether(
            name='emailtoken',
            unique_together=set([]),
        ),
        migrations.RemoveField(
            model_name='emailtoken',
            name='active',
        ),
        migrations.RemoveField(
            model_name='emailtoken',
            name='type',
        ),
    ]
