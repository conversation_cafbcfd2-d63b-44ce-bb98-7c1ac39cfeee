from lib.enums import StrEnum
from lib.payment_providers.enums import ProviderAccountHolderStatus


class BGCBusinessStatus(StrEnum):
    NO_SUBSCRIPTION = 'no_subscription'
    NOT_KYC = 'not_kyc'
    PENDING_KYC = 'pending_kyc'
    ACTIVE = 'active'
    DISABLED = 'disabled'


PROVIDER_ACCOUNT_HOLDER_STATUS_TO_BGC_STATUS = {
    ProviderAccountHolderStatus.VERIFIED.value: BGCBusinessStatus.ACTIVE.value,
    ProviderAccountHolderStatus.TURNED_OFF.value: BGCBusinessStatus.DISABLED.value,
    ProviderAccountHolderStatus.NOT_VERIFIED.value: BGCBusinessStatus.NOT_KYC.value,
    ProviderAccountHolderStatus.VERIFICATION_PENDING.value: BGCBusinessStatus.PENDING_KYC.value,
}
