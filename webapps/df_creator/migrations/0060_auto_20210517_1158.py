# Generated by Django 3.1.8 on 2021-05-17 11:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0327_remove_businesspromotion_suspension_pending'),
        ('images', '0078_digital_flyer_render'),
        ('df_creator', '0059_merge_20210517_1158'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='digitalflyercategory',
            options={'verbose_name_plural': 'Digital Flyer categories'},
        ),
        migrations.RemoveField(
            model_name='digitalflyercategory',
            name='image',
        ),
        migrations.RemoveField(
            model_name='digitalflyercategory',
            name='image_booksy_3',
        ),
        migrations.AlterField(
            model_name='digitalflyergroup',
            name='active_from',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Active from (UTC)'),
        ),
        migrations.AlterField(
            model_name='digitalflyergroup',
            name='active_till',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Active till (UTC)'),
        ),
        migrations.CreateModel(
            name='DigitalFlyerCategoryImage',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                (
                    'business_category',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to='business.businesscategory',
                    ),
                ),
                (
                    'df_category',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='images',
                        to='df_creator.digitalflyercategory',
                    ),
                ),
                (
                    'image',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='df_category_image',
                        to='images.image',
                    ),
                ),
                (
                    'image_booksy_3',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='df_category_image_3',
                        to='images.image',
                    ),
                ),
            ],
            options={
                'unique_together': {('df_category', 'business_category')},
            },
        ),
    ]
