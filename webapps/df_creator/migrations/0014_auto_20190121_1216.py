# Generated by Django 1.11.17 on 2019-01-21 12:16
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0195_merge_20190115_1442'),
        ('df_creator', '0013_df_creator_fixture'),
    ]

    operations = [
        migrations.AddField(
            model_name='digitalflyerbackground',
            name='categories',
            field=models.ManyToManyField(
                blank=True, related_name='df_backgrounds', to='business.BusinessCategory'
            ),
        ),
        migrations.AddField(
            model_name='digitalflyertext',
            name='categories',
            field=models.ManyToManyField(
                blank=True, related_name='df_texts', to='business.BusinessCategory'
            ),
        ),
    ]
