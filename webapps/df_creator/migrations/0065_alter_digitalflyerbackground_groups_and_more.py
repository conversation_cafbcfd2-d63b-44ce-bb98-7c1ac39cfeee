# Generated by Django 4.0.4 on 2022-07-07 16:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('df_creator', '0064_alter_digitalflyers_make_codenames_unique'),
    ]

    operations = [
        migrations.AlterField(
            model_name='digitalflyerbackground',
            name='groups',
            field=models.ManyToManyField(
                related_name='backgrounds',
                to='df_creator.digitalflyergroup',
                verbose_name='Subcategories',
            ),
        ),
        migrations.AlterField(
            model_name='digitalflyertext',
            name='groups',
            field=models.ManyToManyField(
                related_name='texts',
                to='df_creator.digitalflyergroup',
                verbose_name='Subcategories',
            ),
        ),
    ]
