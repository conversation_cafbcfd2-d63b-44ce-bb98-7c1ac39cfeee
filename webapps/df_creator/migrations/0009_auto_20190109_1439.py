# Generated by Django 1.11.17 on 2019-01-09 14:39
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('df_creator', '0008_auto_20190108_1304'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='digitalflyertext',
            name='text',
        ),
        migrations.AddField(
            model_name='digitalflyertext',
            name='text_id',
            field=models.IntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='digitalflyercategory',
            name='type',
            field=models.CharField(
                choices=[
                    ('generic', 'Generic'),
                    ('review', 'Review'),
                    ('special_occasions', 'Special Occasions'),
                    ('random', 'Random'),
                ],
                max_length=30,
            ),
        ),
    ]
