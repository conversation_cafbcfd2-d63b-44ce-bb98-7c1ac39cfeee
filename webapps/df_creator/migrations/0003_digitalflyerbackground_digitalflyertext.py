# Generated by Django 1.11.11 on 2018-12-12 15:14
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('images', '0034_auto_20181212_1514'),
        ('df_creator', '0002_digitalflyergroup'),
    ]

    operations = [
        migrations.CreateModel(
            name='DigitalFlyerBackground',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('text_color', models.Char<PERSON>ield(default='#ffffff', max_length=7)),
                (
                    'groups',
                    models.ManyToManyField(
                        related_name='backgrounds', to='df_creator.DigitalFlyerGroup'
                    ),
                ),
                (
                    'image',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to='images.Image'
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='DigitalFlyerText',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('text', models.CharField(max_length=500)),
                ('text_size', models.IntegerField()),
                (
                    'groups',
                    models.ManyToManyField(related_name='texts', to='df_creator.DigitalFlyerGroup'),
                ),
            ],
        ),
    ]
