from functools import partial

from django.utils.encoding import force_str
from django.utils.translation import (
    gettext_lazy,
    override,
)

from webapps.df_creator.enums import DFCategoryCodename, DFGroupCodename
from webapps.df_creator.marketing_communication import helpers
from webapps.df_creator.models import (
    DigitalFlyerBackground,
    DigitalFlyerCategory,
    DigitalFlyerText,
)
from webapps.notification.push import notification_receivers_list
from webapps.notification.tasks import commence_send_sms
from webapps.notification.tasks.push import send_push_notification
from webapps.pop_up_notification.consts import (
    DfDisplayType,
    DfEvType,
)
from webapps.pop_up_notification.models import DigitalFlyerNotification

EV_TYPE = DfEvType.BUSINESS_ACTIVATED


def _get_df_parts_ids():
    return (
        DigitalFlyerCategory.id_for_codename(
            DFCategoryCodename.ENCOURAGE_BOOKINGS,
        ),
        DigitalFlyerBackground.id_for_codename(
            f'{DFGroupCodename.BOOKING_WITH_BOOKSY}_15',
        ),
        DigitalFlyerText.id_for_codename(
            f'{DFGroupCodename.BOOKING_WITH_BOOKSY}_1',
        ),
    )


def sms(business):
    task_id = 'df:business_activated:sms:business_id=%d'
    target = helpers.get_deeplink_target(*_get_df_parts_ids())

    with override(business.owner_lang):
        message = gettext_lazy(
            'Advertise your business like a pro! '
            'Create and share beautiful social media '
            'posts with just a few taps! %(deeplink)s'
        ) % {'deeplink': helpers.get_df_deeplink(target, EV_TYPE)}

        commence_send_sms(
            message,
            phone_numbers=[business.phone],
            history_data=helpers.get_history_data(business, task_id),
        )


def popup(business, display_type=DfDisplayType.POPUP):
    DigitalFlyerNotification.create_df_notification(
        business=business,
        event_type=EV_TYPE,
        display_type=display_type,
        df_category_id=DigitalFlyerCategory.id_for_codename(
            DFCategoryCodename.ENCOURAGE_BOOKINGS,
        ),
        df_background_id=DigitalFlyerBackground.id_for_codename(
            f'{DFGroupCodename.BOOKING_WITH_BOOKSY}_15',
        ),
        df_text_id=DigitalFlyerText.id_for_codename(
            f'{DFGroupCodename.BOOKING_WITH_BOOKSY}_1',
        ),
    )


hint = partial(popup, display_type=DfDisplayType.HINT)


def push(business):
    task_id = 'df:business_activated:push:business_id=%d'
    receivers = (
        notification_receivers_list(user_id=business.owner.id, customer=False)
        if business.owner is not None
        else []
    )

    with override(business.owner_lang):
        alert = gettext_lazy(
            'Advertise your business like a pro! '
            'Create and share beautiful social media '
            'posts with just a few taps!'
        )
        alert = force_str(alert)

    send_push_notification(
        receivers=receivers,
        alert=alert,
        target=helpers.get_deeplink_target(*_get_df_parts_ids()),
        history_data=helpers.get_history_data(business, task_id),
        push_data={'event_type': EV_TYPE},
    )
