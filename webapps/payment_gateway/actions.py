import importlib
import uuid
from logging import getLogger

from dacite import from_dict
from django.conf import settings

from lib.events import event_receiver
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import SubjectType
from lib.feature_flag.feature.payment import SendTransactionEventToBooksyWalletFlag
from lib.kafka_events import booksy_event_publishers
from lib.payment_gateway.events import (
    payment_gateway_balance_transaction_created_event,
    payment_gateway_balance_transaction_updated_event,
)
from lib.payment_providers.entities import (
    PaymentEntity,
    PaymentOperationEntity,
    PayoutEntity,
    TransferFundEntity,
)
from lib.payment_providers.enums import PaymentOperationType
from lib.payment_providers.events import (
    payment_providers_payment_created_event,
    payment_providers_payment_operation_created_event,
    payment_providers_payment_operation_updated_event,
    payment_providers_payment_updated_event,
    payment_providers_payout_created_event,
    payment_providers_payout_updated_event,
    payment_providers_transfer_fund_updated_event,
)
from lib.feature_flag.feature.data_streaming import EnablePublishingEventsToKafka
from webapps.payment_gateway.adapters import PaymentProvidersAdapter
from webapps.payment_gateway.consts import (
    PAYMENT_PROVIDERS_PAYMENT_OPERATION_STATUS_TO_BT_STATUS_MAPPING,
    PAYMENT_PROVIDERS_PAYMENT_OPERATION_TYPE_TO_DISPUTE_TYPE_MAPPING,
    PAYMENT_PROVIDERS_PAYMENT_STATUS_TO_PAYMENT_STATUS_MAPPING,
    PAYMENT_PROVIDERS_TRANSFER_FUND_STATUS_TO_BT_STATUS_MAPPING,
)
from webapps.payment_gateway.messages.balance_transaction import (
    BalanceTransactionEventMessage,
)
from webapps.payment_gateway.messages.transaction_created_or_updated import (
    construct_transaction_created_or_updated_event_payload,
    TransactionCreatedOrUpdatedEventKey,
)
from webapps.payment_gateway.models import BalanceTransaction
from webapps.payment_gateway.services.balance_transaction import BalanceTransactionService
from webapps.payment_gateway.services.dispute import DisputeService
from webapps.payment_gateway.services.payment import PaymentService
from webapps.payment_gateway.services.payout import PayoutService
from webapps.payment_gateway.services.refund import RefundService
from webapps.payment_gateway.services.wallet import WalletService

logger = getLogger('invoicing.core_pubsub')


def send_balance_transaction_message(balance_transaction: dict):
    balance_transaction = BalanceTransaction.objects.get(id=balance_transaction.get('id'))
    logger.info(
        'Sending balance transaction message: %s in %s',
        balance_transaction.id,
        settings.API_COUNTRY,
    )
    BalanceTransactionEventMessage(balance_transaction).publish()


def publish_transaction_created_or_updated_event(balance_transaction_id: uuid.UUID):
    importlib.reload(booksy_event_publishers)

    message = construct_transaction_created_or_updated_event_payload(balance_transaction_id)
    business_id = message.sender.business_id or (
        message.receiver.business_id if message.receiver else None
    )
    key = TransactionCreatedOrUpdatedEventKey(
        business_id=business_id, balance_transaction_id=message.id
    )

    # later refactor to write it to outbox instead
    booksy_event_publishers.transaction_created_or_updated_event_publisher.produce(
        key=key, message=message
    )


@event_receiver(payment_gateway_balance_transaction_created_event)
@event_receiver(payment_gateway_balance_transaction_updated_event)
def send_message_balance_transaction_created_or_updated_handler(
    balance_transaction: dict, **kwargs
):
    if EnablePublishingEventsToKafka() and (
        SendTransactionEventToBooksyWalletFlag(
            UserData(
                subject_key=str(balance_transaction.get('receiver_id')),
                subject_type=SubjectType.WALLET_ID.value,
            )
        )
        or SendTransactionEventToBooksyWalletFlag(
            UserData(
                subject_key=str(balance_transaction.get('sender_id')),
                subject_type=SubjectType.WALLET_ID.value,
            )
        )
    ):
        publish_transaction_created_or_updated_event(balance_transaction.get('id'))


@event_receiver(payment_gateway_balance_transaction_created_event)
def send_message_balance_transaction_created(balance_transaction: dict, **kwargs):
    send_balance_transaction_message(balance_transaction)


@event_receiver(payment_gateway_balance_transaction_updated_event)
def send_message_balance_transaction_updated(balance_transaction: dict, **kwargs):
    send_balance_transaction_message(balance_transaction)


@event_receiver(payment_providers_payment_created_event)
def payment_providers_payment_created_handler(_payment: dict, **kwargs):
    # for now ignore, maybe use when standalone terminals will be introduced
    pass


@event_receiver(payment_providers_payment_updated_event)
def payment_providers_payment_updated_handler(payment: dict, **kwargs):
    payment = from_dict(
        data_class=PaymentEntity,
        data=payment,
    )

    balance_transaction = BalanceTransactionService.get_balance_transaction_by_external_id(
        external_id=payment.id
    )

    status = PAYMENT_PROVIDERS_PAYMENT_STATUS_TO_PAYMENT_STATUS_MAPPING.get(payment.status)
    if not status:
        raise RuntimeError(  # pylint: disable=broad-exception-raised
            f"PaymentStatus {payment.status} couldn't be mapped to BalanceTransactionStatus"
        )

    PaymentService.update_payment(
        payment=balance_transaction.payment,
        new_status=payment.status,
        auto_capture=payment.auto_capture,
        new_amount=payment.amount,
        error_code=payment.error_code,
        action_required_details=payment.action_required_details,
        metadata=payment.metadata,
    )


@event_receiver(payment_providers_payment_operation_created_event)
def payment_providers_payment_operation_created_handler(payment_operation: dict, **kwargs):
    payment_operation = from_dict(
        data_class=PaymentOperationEntity,
        data=payment_operation,
    )

    if payment_operation.type in [
        PaymentOperationType.CHARGEBACK,
        PaymentOperationType.REVERSED_CHARGEBACK,
        PaymentOperationType.SECOND_CHARGEBACK,
    ]:
        payment_balance_transaction = (
            BalanceTransactionService.get_balance_transaction_by_external_id(
                external_id=payment_operation.payment_id,
            )
        )

        dispute_type = PAYMENT_PROVIDERS_PAYMENT_OPERATION_TYPE_TO_DISPUTE_TYPE_MAPPING.get(
            payment_operation.type
        )
        if not dispute_type:
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                f"PaymentOperationType {payment_operation.type} couldn't be mapped to DisputeType"
            )

        DisputeService.add_dispute(
            payment_balance_transaction=payment_balance_transaction,
            amount=payment_operation.amount,
            dispute_external_id=payment_operation.id,
            dispute_type=dispute_type,
        )
        return
    if payment_operation.type == PaymentOperationType.REFUND:
        # event of refund creation won't be handled because
        # we expect it to be created via initialize_refund
        return


@event_receiver(payment_providers_payment_operation_updated_event)
def payment_providers_payment_operation_updated_handler(payment_operation: dict, **kwargs):
    payment_operation = from_dict(
        data_class=PaymentOperationEntity,
        data=payment_operation,
    )

    status = PAYMENT_PROVIDERS_PAYMENT_OPERATION_STATUS_TO_BT_STATUS_MAPPING.get(
        payment_operation.status
    )
    if not status:
        raise RuntimeError(  # pylint: disable=broad-exception-raised
            f"PaymentOperationStatus {payment_operation.status}"
            " couldn't be mapped to BalanceTransactionStatus",
        )

    if payment_operation.type in [
        PaymentOperationType.CHARGEBACK,
        PaymentOperationType.REVERSED_CHARGEBACK,
        PaymentOperationType.SECOND_CHARGEBACK,
    ]:
        DisputeService.update_dispute(
            balance_transaction=BalanceTransactionService.get_balance_transaction_by_external_id(
                external_id=payment_operation.id,
            ),
            new_status=status,
        )
        return

    if payment_operation.type == PaymentOperationType.REFUND:
        RefundService.update_refund(
            balance_transaction=BalanceTransactionService.get_balance_transaction_by_external_id(
                external_id=payment_operation.id,
            ),
            new_status=status,
        )
        return


@event_receiver(payment_providers_payout_created_event)
def payment_providers_payout_created_event_handler(payout: dict, **kwargs):
    payout = from_dict(
        data_class=PayoutEntity,
        data=payout,
    )

    details = PaymentProvidersAdapter.get_payout_details(payout_id=payout.id)
    external_ids = []
    external_ids.extend(i.id for i in details.payments)
    external_ids.extend(i.id for i in details.payment_operations)
    external_ids.extend(i.id for i in details.transfer_funds)

    paid_out_balance_transactions_qs = (
        BalanceTransactionService.get_balance_transactions_by_external_ids(
            external_ids=external_ids,
        )
    )

    wallet = WalletService.get_account_holders_wallet(account_holder_id=payout.account_holder_id)
    PayoutService.add_or_update_payout(
        external_id=payout.id,
        wallet=wallet,
        payment_provider_code=payout.payment_provider_code,
        amount=payout.amount,
        status=payout.status,
        expected_arrival_date=payout.expected_arrival_date,
        payout_type=payout.payout_type,
        paid_out_balance_transactions_qs=paid_out_balance_transactions_qs,
    )


@event_receiver(payment_providers_payout_updated_event)
def payment_providers_payout_updated_event_handler(payout: dict, **kwargs):
    payout = from_dict(
        data_class=PayoutEntity,
        data=payout,
    )

    wallet = WalletService.get_account_holders_wallet(account_holder_id=payout.account_holder_id)
    PayoutService.add_or_update_payout(
        external_id=payout.id,
        wallet=wallet,
        payment_provider_code=payout.payment_provider_code,
        amount=payout.amount,
        status=payout.status,
        expected_arrival_date=payout.expected_arrival_date,
        payout_type=payout.payout_type,
    )


@event_receiver(payment_providers_transfer_fund_updated_event)
def payment_providers_transfer_fund_updated_event_handler(transfer_fund: dict, **kwargs):
    transfer_funds = from_dict(data_class=TransferFundEntity, data=transfer_fund)
    new_status = PAYMENT_PROVIDERS_TRANSFER_FUND_STATUS_TO_BT_STATUS_MAPPING.get(
        transfer_funds.status,
    )
    BalanceTransactionService.update_bt_status(
        balance_transaction=BalanceTransactionService.get_balance_transaction_by_external_id(
            external_id=transfer_funds.id,
        ),
        new_status=new_status,
    )
