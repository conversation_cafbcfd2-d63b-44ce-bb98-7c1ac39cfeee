from decimal import Decimal

from django.test import TestCase
from parameterized import parameterized

from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    PaymentSplitsEntity,
    RefundSplitsEntity,
)


class SplitsEntityTests(TestCase):
    @parameterized.expand(
        [
            (PaymentSplitsEntity(percentage_fee=Decimal('2.21'), fixed_fee=12), 1000, 34),
            (RefundSplitsEntity(percentage_fee=Decimal('0'), fixed_fee=13), 100, 13),
            (DisputeSplitsEntity(percentage_fee=Decimal('13.50'), fixed_fee=0), 10, 1),
        ]
    )
    def test_calculate_fee(self, splits, amount, expected_fee):
        self.assertEqual(splits.calculate_fee(amount=amount), expected_fee)

    @parameterized.expand(
        [
            (
                PaymentSplitsEntity(percentage_fee=Decimal('2.21'), fixed_fee=12),
                {"percentage_fee": '2.21', "fixed_fee": 12},
            ),
            (
                RefundSplitsEntity(percentage_fee=Decimal('0'), fixed_fee=13),
                {"percentage_fee": '0.00', "fixed_fee": 13},
            ),
            (
                DisputeSplitsEntity(percentage_fee=Decimal('13.50'), fixed_fee=0),
                {"percentage_fee": '13.50', "fixed_fee": 0},
            ),
        ]
    )
    def test_as_data__payment(self, splits, expected_as_dict):
        self.assertDictEqual(splits.as_dict(), expected_as_dict)
