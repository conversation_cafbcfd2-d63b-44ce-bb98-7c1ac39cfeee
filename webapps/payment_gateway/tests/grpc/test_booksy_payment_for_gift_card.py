from random import randint

import pytest
import stripe
from google.protobuf.json_format import MessageToDict
from mock import patch, mock
from model_bakery import baker

from lib.payment_gateway.enums import WalletOwnerType, PaymentMethodType
from lib.payments.enums import PaymentProviderCode
from webapps.payment_gateway.models import Wallet
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_gateway.protobuf.core.booksy_payment_pb2 import (  # pylint: disable=no-name-in-module
    BooksyPaymentRequest,
    PaymentMethodDetails,
    ProductInfoRow,
)
from webapps.payment_gateway.tests.grpc.base_booksy_payment_test_case import (
    BaseBooksyPaymentStubTestCase,
)
from webapps.payment_providers.models import (
    TokenizedPaymentMethod,
    StripeTokenizedPaymentMethod,
    Customer,
    StripeCustomer,
    AccountHolder,
    StripeAccountHolder,
)
from webapps.pos.tests.pos_refactor.helpers_stripe import StripeTestEventBody
from webapps.user.models import User


@pytest.mark.django_db
class TestBooksyPayment(BaseBooksyPaymentStubTestCase):
    @classmethod
    def setUpTestData(cls):
        account_holder = baker.make(AccountHolder)
        baker.make(
            StripeAccountHolder,
            account_holder=account_holder,
            payouts_enabled=True,
            charges_enabled=True,
            blocked=False,
            charge_fee_for_tips=True,
        )
        cls.booksy_wallet = baker.make(
            Wallet, owner_type=WalletOwnerType.BOOKSY, account_holder_id=account_holder.id
        )
        cls.random_id = ''.join(str(randint(0, 9)) for i in range(16))
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='*********',
        )
        customer_wallet = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=cls.user.id,
            email=cls.user.email,
            phone=cls.user.cell_phone,
            statement_name='',
        )[0]
        cls.customer = Customer.objects.get(id=customer_wallet.customer_id)
        baker.make(
            StripeCustomer,
            customer=cls.customer,
        )
        cls.tpm = baker.make(
            TokenizedPaymentMethod,
            customer=cls.customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            default=True,
            details={
                'brand': 'visa',
                'last_digits': '1234',
                'expiry_month': 1,
                'expiry_year': 2032,
                'cardholder_name': 'cardholder name',
            },
        )
        baker.make(
            StripeTokenizedPaymentMethod,
            external_id='1233333',
            tokenized_payment_method=cls.tpm,
        )
        cls.provider_code = PaymentProviderCode.STRIPE
        super().setUpTestData()

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_booksy_payment(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        confirm_payment_intent_mock.return_value = stripe.SetupIntent().construct_from(
            StripeTestEventBody.payment_intent_amount_capturable_updated(
                payment_intent_external_id=self.random_id,
            )['data']['object'],
            key=stripe.api_key,
        )

        request = BooksyPaymentRequest(
            user_id=self.user.id,
            amount=10000,
            payment_method_details=PaymentMethodDetails(tokenized_pm_id=str(self.tpm.id)),
            product_info=[
                ProductInfoRow(key='booksy_gift_card_id', value='random_uuid'),
                ProductInfoRow(key='booksy_gift_card_external_id', value='another_random_uuid'),
            ],
        )

        response = MessageToDict(self.stub.Payment(request), preserving_proto_field_name=True)

        self.assertIsNotNone(response['balance_transaction_id'])
        self.assertEqual(response['response_message'], 'Payment has been initialized')
        self.assertIsNone(response.get('error_code'))
        self.assertIsNone(response.get('three_d_data'))

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_booksy_payment_no_payment_method_data(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        confirm_payment_intent_mock.return_value = stripe.SetupIntent().construct_from(
            StripeTestEventBody.payment_intent_amount_capturable_updated(
                payment_intent_external_id=self.random_id,
            )['data']['object'],
            key=stripe.api_key,
        )

        request = BooksyPaymentRequest(
            user_id=self.user.id,
            amount=10000,
            payment_method_details=PaymentMethodDetails(tokenized_pm_id=None),
            product_info=[
                ProductInfoRow(key='booksy_gift_card_id', value='random_uuid'),
                ProductInfoRow(key='booksy_gift_card_external_id', value='another_random_uuid'),
            ],
        )

        response = MessageToDict(self.stub.Payment(request), preserving_proto_field_name=True)

        self.assertEqual(response['response_message'], 'Invalid payment methods details')
        self.assertEqual(response['error_code'], 'invalid_payment_method')
        self.assertIsNone(response.get('balance_transaction_id'))
        self.assertIsNone(response.get('three_d_data', None))

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_booksy_payment_3ds(
        self, capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        confirm_payment_intent_mock.return_value = stripe.SetupIntent().construct_from(
            StripeTestEventBody.confirm_payment_intent_3ds_required(self.random_id),
            key=stripe.api_key,
        )
        request = BooksyPaymentRequest(
            user_id=self.user.id,
            amount=10000,
            payment_method_details=PaymentMethodDetails(tokenized_pm_id=str(self.tpm.id)),
            product_info=[
                ProductInfoRow(key='booksy_gift_card_id', value='random_uuid'),
                ProductInfoRow(key='booksy_gift_card_external_id', value='another_random_uuid'),
            ],
        )

        response = MessageToDict(self.stub.Payment(request), preserving_proto_field_name=True)

        self.assertIsNotNone(response['balance_transaction_id'])
        self.assertEqual(response['response_message'], 'Payment has been initialized')
        self.assertIsNone(response.get('error_code'))
        self.assertIsNotNone(response['three_d_data']['client_secret'])
