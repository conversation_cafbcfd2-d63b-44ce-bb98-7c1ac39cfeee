# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from webapps.payment_gateway.protobuf.core import booksy_payment_pb2 as booksy__payment__pb2


class BooksyPaymentStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Payment = channel.unary_unary(
                '/booksy_payment_service.BooksyPayment/Payment',
                request_serializer=booksy__payment__pb2.BooksyPaymentRequest.SerializeToString,
                response_deserializer=booksy__payment__pb2.BooksyPaymentResponse.FromString,
                )
        self.OrderStripeTransferToBusiness = channel.unary_unary(
                '/booksy_payment_service.BooksyPayment/OrderStripeTransferToBusiness',
                request_serializer=booksy__payment__pb2.OrderStripeTransferRequest.SerializeToString,
                response_deserializer=booksy__payment__pb2.OrderStripeTransferResponse.FromString,
                )
        self.OrderStripeTransferFromBusiness = channel.unary_unary(
                '/booksy_payment_service.BooksyPayment/OrderStripeTransferFromBusiness',
                request_serializer=booksy__payment__pb2.OrderStripeTransferRequest.SerializeToString,
                response_deserializer=booksy__payment__pb2.OrderStripeTransferResponse.FromString,
                )
        self.RefundBooksyGiftCard = channel.unary_unary(
                '/booksy_payment_service.BooksyPayment/RefundBooksyGiftCard',
                request_serializer=booksy__payment__pb2.RefundBooksyGiftCardRequest.SerializeToString,
                response_deserializer=booksy__payment__pb2.RefundBooksyGiftCardResponse.FromString,
                )
        self.CheckBGCTransferStatus = channel.unary_unary(
                '/booksy_payment_service.BooksyPayment/CheckBGCTransferStatus',
                request_serializer=booksy__payment__pb2.CheckBGCTransferStatusRequest.SerializeToString,
                response_deserializer=booksy__payment__pb2.CheckBGCTransferStatusResponse.FromString,
                )


class BooksyPaymentServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Payment(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OrderStripeTransferToBusiness(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OrderStripeTransferFromBusiness(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RefundBooksyGiftCard(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CheckBGCTransferStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_BooksyPaymentServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Payment': grpc.unary_unary_rpc_method_handler(
                    servicer.Payment,
                    request_deserializer=booksy__payment__pb2.BooksyPaymentRequest.FromString,
                    response_serializer=booksy__payment__pb2.BooksyPaymentResponse.SerializeToString,
            ),
            'OrderStripeTransferToBusiness': grpc.unary_unary_rpc_method_handler(
                    servicer.OrderStripeTransferToBusiness,
                    request_deserializer=booksy__payment__pb2.OrderStripeTransferRequest.FromString,
                    response_serializer=booksy__payment__pb2.OrderStripeTransferResponse.SerializeToString,
            ),
            'OrderStripeTransferFromBusiness': grpc.unary_unary_rpc_method_handler(
                    servicer.OrderStripeTransferFromBusiness,
                    request_deserializer=booksy__payment__pb2.OrderStripeTransferRequest.FromString,
                    response_serializer=booksy__payment__pb2.OrderStripeTransferResponse.SerializeToString,
            ),
            'RefundBooksyGiftCard': grpc.unary_unary_rpc_method_handler(
                    servicer.RefundBooksyGiftCard,
                    request_deserializer=booksy__payment__pb2.RefundBooksyGiftCardRequest.FromString,
                    response_serializer=booksy__payment__pb2.RefundBooksyGiftCardResponse.SerializeToString,
            ),
            'CheckBGCTransferStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.CheckBGCTransferStatus,
                    request_deserializer=booksy__payment__pb2.CheckBGCTransferStatusRequest.FromString,
                    response_serializer=booksy__payment__pb2.CheckBGCTransferStatusResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'booksy_payment_service.BooksyPayment', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class BooksyPayment(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Payment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/booksy_payment_service.BooksyPayment/Payment',
            booksy__payment__pb2.BooksyPaymentRequest.SerializeToString,
            booksy__payment__pb2.BooksyPaymentResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def OrderStripeTransferToBusiness(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/booksy_payment_service.BooksyPayment/OrderStripeTransferToBusiness',
            booksy__payment__pb2.OrderStripeTransferRequest.SerializeToString,
            booksy__payment__pb2.OrderStripeTransferResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def OrderStripeTransferFromBusiness(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/booksy_payment_service.BooksyPayment/OrderStripeTransferFromBusiness',
            booksy__payment__pb2.OrderStripeTransferRequest.SerializeToString,
            booksy__payment__pb2.OrderStripeTransferResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RefundBooksyGiftCard(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/booksy_payment_service.BooksyPayment/RefundBooksyGiftCard',
            booksy__payment__pb2.RefundBooksyGiftCardRequest.SerializeToString,
            booksy__payment__pb2.RefundBooksyGiftCardResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CheckBGCTransferStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/booksy_payment_service.BooksyPayment/CheckBGCTransferStatus',
            booksy__payment__pb2.CheckBGCTransferStatusRequest.SerializeToString,
            booksy__payment__pb2.CheckBGCTransferStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
