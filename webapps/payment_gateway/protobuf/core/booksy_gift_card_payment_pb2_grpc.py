# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from webapps.payment_gateway.protobuf.core import booksy_gift_card_payment_pb2 as booksy__gift__card__payment__pb2


class PaymentDetailsStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetPaymentDetails = channel.unary_unary(
                '/PaymentDetails/GetPaymentDetails',
                request_serializer=booksy__gift__card__payment__pb2.PaymentDetailsRequest.SerializeToString,
                response_deserializer=booksy__gift__card__payment__pb2.PaymentDetailsResponse.FromString,
                )
        self.GetPaymentDetailsList = channel.unary_unary(
                '/PaymentDetails/GetPaymentDetailsList',
                request_serializer=booksy__gift__card__payment__pb2.ListPaymentDetailsRequest.SerializeToString,
                response_deserializer=booksy__gift__card__payment__pb2.ListPaymentDetailsResponse.FromString,
                )


class PaymentDetailsServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetPaymentDetails(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPaymentDetailsList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PaymentDetailsServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetPaymentDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPaymentDetails,
                    request_deserializer=booksy__gift__card__payment__pb2.PaymentDetailsRequest.FromString,
                    response_serializer=booksy__gift__card__payment__pb2.PaymentDetailsResponse.SerializeToString,
            ),
            'GetPaymentDetailsList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPaymentDetailsList,
                    request_deserializer=booksy__gift__card__payment__pb2.ListPaymentDetailsRequest.FromString,
                    response_serializer=booksy__gift__card__payment__pb2.ListPaymentDetailsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'PaymentDetails', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class PaymentDetails(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetPaymentDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/PaymentDetails/GetPaymentDetails',
            booksy__gift__card__payment__pb2.PaymentDetailsRequest.SerializeToString,
            booksy__gift__card__payment__pb2.PaymentDetailsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetPaymentDetailsList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/PaymentDetails/GetPaymentDetailsList',
            booksy__gift__card__payment__pb2.ListPaymentDetailsRequest.SerializeToString,
            booksy__gift__card__payment__pb2.ListPaymentDetailsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
