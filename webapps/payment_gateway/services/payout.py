import datetime
import uuid
from dataclasses import asdict
from typing import Op<PERSON>, Tuple

from django.db import transaction
from django.db.models import QuerySet

from lib.db import PAYMENTS_DB
from lib.payment_gateway.enums import (
    BalanceTransactionStatus,
    BalanceTransactionType,
    FeeType,
)
from lib.payment_gateway.events import (
    payment_gateway_balance_transaction_created_event,
    payment_gateway_balance_transaction_updated_event,
)
from lib.payments.enums import PaymentProviderCode, PayoutStatus, PayoutType
from webapps.payment_gateway.adapters import PaymentProvidersAdapter
from webapps.payment_gateway.consts import MINIMAL_TRANSFER_AMOUNT
from webapps.payment_gateway.exceptions import InvalidAmount
from webapps.payment_gateway.models import BalanceTransaction, Payout, Wallet
from webapps.payment_gateway.services.balance_transaction import BalanceTransactionService
from webapps.payment_gateway.services.wallet import WalletService
from webapps.payment_gateway.tasks import initialize_fee_task


class PayoutService:
    @staticmethod
    def _map_payout_status_to_bt_status(payout_status: PayoutStatus) -> BalanceTransactionStatus:
        return {
            PayoutStatus.IN_PAYMENT_PROCESSOR: BalanceTransactionStatus.PROCESSING,
            PayoutStatus.IN_TRANSIT: BalanceTransactionStatus.PROCESSING,
            PayoutStatus.ARRIVED: BalanceTransactionStatus.SUCCESS,
            PayoutStatus.FAILED: BalanceTransactionStatus.FAILED,
        }.get(payout_status)

    @staticmethod
    def _get_or_add_payout(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        external_id: uuid.UUID,
        wallet: Wallet,
        payment_provider_code: PaymentProviderCode,
        amount: int,
        fee_amount: int,
        status: Optional[PayoutStatus] = PayoutStatus.IN_PAYMENT_PROCESSOR,
        expected_arrival_date: Optional[datetime.datetime] = None,
        payout_type: PayoutType = PayoutType.REGULAR,
        paid_out_balance_transactions_qs: Optional[QuerySet[BalanceTransaction]] = None,
        splits: Optional[dict] = None,
    ) -> Tuple[BalanceTransaction, bool]:
        """
        for this to work correctly (thread-safely) db-level
        unique constraint is required
        """
        balance_transaction, created = BalanceTransaction.objects.get_or_create(
            external_id=external_id,
            payment_provider_code=payment_provider_code,
            transaction_type=BalanceTransactionType.PAYOUT,
            defaults={
                "sender": wallet,
                "receiver": None,
                "amount": amount,
                "fee_amount": fee_amount,
                "status": PayoutService._map_payout_status_to_bt_status(status),
                "payment_method": None,
                "paid_out_in_payout": None,
            },
        )
        if not created:
            return balance_transaction, False

        with transaction.atomic(using=PAYMENTS_DB):
            payout = Payout(
                status=status,
                balance_transaction=balance_transaction,
                expected_arrival_date=expected_arrival_date,
                splits=splits or {},
                payout_type=payout_type,
            )
            payout.save()
            balance_transaction.paid_out_in_payout = payout
            balance_transaction.save(update_fields=['paid_out_in_payout'])
            if paid_out_balance_transactions_qs is not None:
                paid_out_balance_transactions_qs.filter(paid_out_in_payout__isnull=True).update(
                    paid_out_in_payout=balance_transaction.payout,
                )
            BalanceTransactionService.save_to_history(balance_transaction)

        payment_gateway_balance_transaction_created_event.send(asdict(balance_transaction.entity))
        return balance_transaction, True

    @staticmethod
    def calculate_payout_fee(
        wallet: Wallet,
        payment_provider_code: PaymentProviderCode,
        payout_type: PayoutType,
        amount: int,
    ) -> int:
        """
        rewritten logic from
        webapps.stripe_integration.provider.StripeProvider.calculate_fast_payout_splits

        payout_provision is a percent of the total amount subtracted from it
        """

        fee_settings = WalletService.get_wallet_fee_settings(
            wallet=wallet,
            payment_provider_code=payment_provider_code,
        )
        if payout_type == PayoutType.REGULAR:
            provision = fee_settings.regular_payout_provision_percentage / 100
            fixed_fee = fee_settings.regular_payout_provision_fee
        elif payout_type == PayoutType.FAST:
            provision = fee_settings.fast_payout_provision_percentage / 100
            fixed_fee = fee_settings.fast_payout_provision_fee
        else:
            raise NotImplementedError

        payout_provision = (amount * provision) / (1 + provision)
        payout_provision = round(payout_provision)

        return payout_provision + fixed_fee

    @staticmethod
    def _validate_payout_amount(
        wallet: Wallet,
        payment_provider_code: PaymentProviderCode,
        amount: int,
        payout_type: PayoutType,
    ):
        available_payout_amount: int = PaymentProvidersAdapter.get_available_payout_amount(
            account_holder_id=wallet.account_holder_id,
            payment_provider_code=payment_provider_code,
        ).get(payout_type, 0)
        if amount <= 0:
            raise InvalidAmount(f"Invalid amount {amount}; has to be greater than zero")

        if amount > available_payout_amount:
            raise InvalidAmount(
                f"Invalid amount {amount};"
                f" is greater than available amount {available_payout_amount}"
            )

    @staticmethod
    def initialize_payout(
        wallet: Wallet,
        payment_provider_code: PaymentProviderCode,
        amount: int,
        payout_type: PayoutType,
    ) -> BalanceTransaction:
        """
        Manual payouts (usually fast/instant, but not always - specify payout_type)
        """
        fee_amount = PayoutService.calculate_payout_fee(
            wallet=wallet,
            amount=amount,
            payment_provider_code=payment_provider_code,
            payout_type=payout_type,
        )
        payout_amount = amount - fee_amount

        PayoutService._validate_payout_amount(
            wallet=wallet,
            payment_provider_code=payment_provider_code,
            amount=payout_amount,
            payout_type=payout_type,
        )

        payout_obj = PaymentProvidersAdapter.initialize_payout(
            account_holder_id=wallet.account_holder_id,
            payment_provider_code=payment_provider_code,
            amount=payout_amount,
            payout_type=payout_type,
        )

        balance_transaction = PayoutService.add_or_update_payout(
            wallet=wallet,
            payment_provider_code=payment_provider_code,
            amount=payout_amount,
            fee_amount=0,  # this field indicates fee charged using Stripe system, thus 0
            expected_arrival_date=None,
            external_id=payout_obj.id,
            payout_type=payout_type,
            splits={"payout_fee": fee_amount},
        )
        return balance_transaction

    @staticmethod
    def add_or_update_payout(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        external_id: uuid.UUID,
        wallet: Wallet,
        payment_provider_code: PaymentProviderCode,
        amount: int,
        payout_type: PayoutType = PayoutType.REGULAR,
        fee_amount: int = 0,
        status: Optional[PayoutStatus] = None,
        expected_arrival_date: Optional[datetime.datetime] = None,
        paid_out_balance_transactions_qs: Optional[QuerySet[BalanceTransaction]] = None,
        splits: Optional[dict] = None,
    ) -> BalanceTransaction:
        """
        should be called (only) from Payment Provider webhook
        """
        balance_transaction, created = PayoutService._get_or_add_payout(
            external_id=external_id,
            amount=amount,
            fee_amount=fee_amount,
            payment_provider_code=payment_provider_code,
            status=status,
            wallet=wallet,
            expected_arrival_date=expected_arrival_date,
            payout_type=payout_type,
            paid_out_balance_transactions_qs=paid_out_balance_transactions_qs,
            splits=splits,
        )

        if created:
            PayoutService.initialize_fees_if_applicable(balance_transaction)
            return balance_transaction

        # update existing balance transaction
        with transaction.atomic(using=PAYMENTS_DB):
            bt_fields = ['amount', 'fee_amount']
            payout_fields = []
            balance_transaction.amount = amount
            balance_transaction.fee_amount = fee_amount
            if expected_arrival_date is not None:
                balance_transaction.payout.expected_arrival_date = expected_arrival_date
                payout_fields.append('expected_arrival_date')
            if status is not None:
                balance_transaction.status = PayoutService._map_payout_status_to_bt_status(
                    payout_status=status
                )
                balance_transaction.payout.status = status
                bt_fields.append('status')
                payout_fields.append('status')
            if splits is not None:
                balance_transaction.payout.splits = splits
                payout_fields.append('splits')

            balance_transaction.save(update_fields=bt_fields)
            balance_transaction.payout.save(update_fields=payout_fields)

            if paid_out_balance_transactions_qs is not None:
                paid_out_balance_transactions_qs.filter(paid_out_in_payout__isnull=True).update(
                    paid_out_in_payout=balance_transaction.payout,
                )
            BalanceTransactionService.save_to_history(balance_transaction)

        payment_gateway_balance_transaction_updated_event.send(asdict(balance_transaction.entity))
        PayoutService.initialize_fees_if_applicable(balance_transaction)
        return balance_transaction

    @staticmethod
    def initialize_fees_if_applicable(payout_balance_transaction: BalanceTransaction):
        if payout_balance_transaction.payout.status != PayoutStatus.ARRIVED:
            # only charge fees if payout is in the final status
            return

        wallet = payout_balance_transaction.sender
        fee_amount = payout_balance_transaction.payout.splits.get('payout_fee')
        if fee_amount is None:
            fee_amount = PayoutService.calculate_payout_fee(
                wallet=wallet,
                amount=payout_balance_transaction.amount,
                payment_provider_code=PaymentProviderCode(
                    payout_balance_transaction.payment_provider_code
                ),
                payout_type=PayoutType(payout_balance_transaction.payout.payout_type),
            )
        if fee_amount < MINIMAL_TRANSFER_AMOUNT:
            return
        initialize_fee_task.delay(
            amount=fee_amount,
            fee_type=FeeType.PAYOUT,
            sender_id=payout_balance_transaction.sender.id,
            receiver_id=WalletService.get_booksy_wallet().id,
            payment_provider_code=payout_balance_transaction.payment_provider_code,
            parent_balance_transaction_id=payout_balance_transaction.id,
        )

    @staticmethod
    def get_payout_transactions_count(payout_id: uuid.UUID) -> Optional[int]:
        payout = Payout.objects.get(id=payout_id)
        return (
            payout.paid_out_balance_transactions.count() - 1
        )  # because payout BalanceTransaction is also included

    @staticmethod
    def get_payouts_count(wallet_id: uuid.UUID) -> int:
        return Payout.objects.filter(balance_transaction__sender__id=wallet_id).count()
