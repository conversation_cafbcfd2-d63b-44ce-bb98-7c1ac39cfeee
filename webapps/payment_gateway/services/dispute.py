import uuid
from dataclasses import asdict

from django.db import transaction

from lib.db import PAYMENTS_DB
from lib.payment_gateway.enums import (
    BalanceTransactionStatus,
    BalanceTransactionType,
    DisputeType,
    FeeType,
)
from lib.payment_gateway.events import payment_gateway_balance_transaction_created_event
from lib.payments.enums import PaymentProviderCode
from lib.smartlock import SmartLock
from webapps.payment_gateway.consts import MINIMAL_TRANSFER_AMOUNT
from webapps.payment_gateway.exceptions import OperationNotAllowed
from webapps.payment_gateway.models import BalanceTransaction, Dispute
from webapps.payment_gateway.services.balance_transaction import BalanceTransactionService
from webapps.payment_gateway.services.wallet import WalletService
from webapps.payment_gateway.tasks import initialize_fee_task


class DisputeService:
    providers_charging_booksy_with_payment_amount = [
        # Payment providers that after chargeback for 100$
        # charge Booksy with 100$ instead of charging
        # the disputed merchant's account
        PaymentProviderCode.STRIPE,
    ]

    @staticmethod
    def add_dispute(
        payment_balance_transaction: BalanceTransaction,  # payment being disputed
        amount: int,
        dispute_type: DisputeType,
        dispute_external_id: uuid.UUID,
    ) -> BalanceTransaction:
        """
        should be called (only) from Payment Provider webhook
        """
        if payment_balance_transaction.transaction_type != BalanceTransactionType.PAYMENT:
            raise OperationNotAllowed

        with SmartLock(key=str(payment_balance_transaction.id)):
            payment_balance_transaction.refresh_from_db()

            if dispute_type in [DisputeType.CHARGEBACK, DisputeType.SECOND_CHARGEBACK]:
                receiver = payment_balance_transaction.sender
                sender = payment_balance_transaction.receiver
            elif dispute_type in [DisputeType.REVERSED_CHARGEBACK]:
                # "reverse" means that the merchant won the case and funds are coming back to him
                receiver = payment_balance_transaction.receiver
                sender = payment_balance_transaction.sender
            else:
                raise ValueError(f"Invalid dispute type: {dispute_type}")

            with transaction.atomic(using=PAYMENTS_DB):
                balance_transaction = BalanceTransaction(
                    receiver=receiver,
                    sender=sender,
                    amount=amount,
                    status=BalanceTransactionStatus.SUCCESS,
                    payment_method=payment_balance_transaction.payment_method,
                    payment_provider_code=payment_balance_transaction.payment_provider_code,
                    transaction_type=BalanceTransactionType.DISPUTE,
                    external_id=dispute_external_id,
                    payout=None,
                    parent_balance_transaction=payment_balance_transaction,
                )
                balance_transaction.save()
                dispute = Dispute(
                    balance_transaction=balance_transaction,
                    type=dispute_type,
                )
                dispute.save()
                BalanceTransactionService.save_to_history(balance_transaction)

            payment_gateway_balance_transaction_created_event.send(
                asdict(balance_transaction.entity)
            )

            if dispute_type == DisputeType.CHARGEBACK:
                DisputeService.charge_fees(dispute_balance_transaction=balance_transaction)

            if dispute_type == DisputeType.REVERSED_CHARGEBACK:
                DisputeService.reverse_fees(dispute_balance_transaction=balance_transaction)

            return balance_transaction

    @staticmethod
    def charge_fees(dispute_balance_transaction: BalanceTransaction):
        payment_bt = dispute_balance_transaction.parent_balance_transaction

        if (
            dispute_balance_transaction.payment_provider_code
            in DisputeService.providers_charging_booksy_with_payment_amount
        ):
            # when chargeback occurs, money for the client is taken from Booksy's
            # account, thus, we need to get our money back from the merchant
            initialize_fee_task.delay(
                amount=payment_bt.amount,
                fee_type=FeeType.PAYMENT_REVERSAL,
                sender_id=dispute_balance_transaction.sender.id,
                receiver_id=WalletService.get_booksy_wallet().id,
                payment_provider_code=dispute_balance_transaction.payment_provider_code,
                parent_balance_transaction_id=dispute_balance_transaction.id,
            )

        dispute_splits = payment_bt.payment.dispute_splits_entity
        fee_amount = dispute_splits.calculate_fee(
            amount=payment_bt.amount,
        )
        if fee_amount < MINIMAL_TRANSFER_AMOUNT:
            return

        initialize_fee_task.delay(
            amount=fee_amount,
            fee_type=FeeType.DISPUTE,
            sender_id=dispute_balance_transaction.sender.id,
            receiver_id=WalletService.get_booksy_wallet().id,
            payment_provider_code=dispute_balance_transaction.payment_provider_code,
            parent_balance_transaction_id=dispute_balance_transaction.id,
        )

    @staticmethod
    def reverse_fees(dispute_balance_transaction: BalanceTransaction):
        # give back the PAYMENT_REVERSAL fee to the merchant
        dispute_type = dispute_balance_transaction.dispute.type
        if not dispute_type == DisputeType.REVERSED_CHARGEBACK:
            raise ValueError(f"Can't reverse fees for dispute type: {dispute_type}")

        payment_bt = dispute_balance_transaction.parent_balance_transaction

        if (
            dispute_balance_transaction.payment_provider_code
            not in DisputeService.providers_charging_booksy_with_payment_amount
        ):
            return

        initialize_fee_task.delay(
            amount=payment_bt.amount,
            fee_type=FeeType.FUNDS_REINSTATED,
            sender_id=WalletService.get_booksy_wallet().id,
            receiver_id=dispute_balance_transaction.receiver.id,
            payment_provider_code=dispute_balance_transaction.payment_provider_code,
            parent_balance_transaction_id=dispute_balance_transaction.id,
        )

    @staticmethod
    def update_dispute(
        balance_transaction: BalanceTransaction,
        new_status: BalanceTransactionStatus,
    ):
        BalanceTransactionService.update_bt_status(
            balance_transaction=balance_transaction,
            new_status=new_status,
        )
