import pytest
from model_bakery import baker

from webapps.business.models.bci import BusinessCustomerInfo
from webapps.notification.recipients import Recipient
from webapps.user.models import User, UserProfile


@pytest.mark.django_db
def test_get_recipient_from_customer_returns_none_for_not_visible_in_business():
    bci = baker.make(
        BusinessCustomerInfo,
        visible_in_business=False,
        email='<EMAIL>',
        cell_phone='+**************',
        user=baker.make(User),
    )
    recipient = Recipient.from_customer(customer=bci)
    assert recipient == Recipient(
        name='',
        email='',
        profile_type=UserProfile.Type.CUSTOMER,
    )


@pytest.mark.django_db
def test_get_recipient_from_customers_in_bulk():
    # no user and not visible in business
    # this one should be filtered out
    baker.make(
        BusinessCustomerInfo,
        visible_in_business=False,
        email='<EMAIL>',
        cell_phone='+**************',
        user=None,
    )
    # not visible in business
    # this one should be filtered out
    baker.make(
        BusinessCustomerInfo,
        visible_in_business=False,
        email='<EMAIL>',
        cell_phone='+**************',
        user=baker.make(User),
    )

    bci_without_user = baker.make(
        BusinessCustomerInfo,
        email='<EMAIL>',
        cell_phone='+**************',
        user=None,
    )

    bci_with_user = baker.make(
        BusinessCustomerInfo,
        user=baker.make(
            User,
            email='<EMAIL>',
        ),
    )
    bci_with_user_2 = baker.make(
        BusinessCustomerInfo,
        user=baker.make(
            User,
            email='<EMAIL>',
        ),
    )
    bci_with_user_2_profile = baker.make(
        UserProfile,
        profile_type=UserProfile.Type.CUSTOMER,
        user=bci_with_user_2.user,
        language='ff',
    )

    bcis = BusinessCustomerInfo.objects.all()
    recipients = Recipient.from_customers_in_bulk(bcis=bcis)

    recipients_expected = [
        Recipient(
            profile_type=UserProfile.Type.CUSTOMER,
            name='',
            email=bci_without_user.email,
            phone=bci_without_user.cell_phone,
            # language='en'
        ),
        Recipient(
            user_id=bci_with_user.id,
            profile_type=UserProfile.Type.CUSTOMER,
            name='',
            email=bci_with_user.user.email,
            phone=bci_with_user.user.cell_phone,
            # language='en'
        ),
        Recipient(
            user_id=bci_with_user_2.id,
            profile_type=UserProfile.Type.CUSTOMER,
            name='',
            email=bci_with_user_2.user.email,
            phone=bci_with_user_2.user.cell_phone,
            language=bci_with_user_2_profile.language,
        ),
    ]
    assert recipients == recipients_expected
