from unittest import mock
from webapps.notification.admin import NotificationHistoryAdmin, NotificationHistoryDocumentAdmin


@mock.patch('webapps.notification.admin.format_html')
def test_content_preview_is_hidden_on_production(mock_format_html):
    obj = mock.MagicMock()
    obj.task_id = "BGC_Purchase:fdsdfdar143"
    result = NotificationHistoryAdmin.content_preview(obj)
    document_result = NotificationHistoryDocumentAdmin.content_preview(obj)

    assert result == "-"
    assert document_result == "-"

    obj.task_id = "Appointment_Reserved:fdsdfdar143"
    _ = NotificationHistoryAdmin.content_preview(obj)
    _ = NotificationHistoryDocumentAdmin.content_preview(obj)

    mock_calls = [mock.call(obj.content), mock.call(obj.content)]
    mock_format_html.assert_has_calls(mock_calls)
