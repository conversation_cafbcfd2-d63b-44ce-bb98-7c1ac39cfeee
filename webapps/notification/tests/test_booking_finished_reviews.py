import pytest
from model_bakery import baker

from lib.test_utils import create_subbooking
from webapps.consts import WEB
from webapps.booking.models import Appointment
from webapps.business.models import Business
from webapps.notification.scenarios import BookingFinishedScenario
from webapps.notification.scenarios.base import ScenarioSkipped
from webapps.pop_up_notification.models import ShortReviewNotification


@pytest.mark.parametrize(
    ('have_bci', 'business_kwargs', 'booking_source', 'bci_with_user', 'review_exists', 'expected'),
    (
        pytest.param(
            False,
            None,
            WEB,
            True,
            False,
            {'recipient_for_review': None},
            id='Booking without bci',
        ),
        pytest.param(
            True,
            dict(
                active=False,
            ),
            WEB,
            True,
            False,
            {'business.active': False},
            id='Booking from inactive business',
        ),
        pytest.param(
            True,
            dict(
                visible=False,
            ),
            WEB,
            True,
            False,
            {'business.active': False},
            id='Booking from invisible business',
        ),
        pytest.param(
            True,
            dict(
                status=Business.Status.PAID,
                custom_data={'review_request_disabled': True},
            ),
            WEB,
            True,
            False,
            {'appointment.needed': False},
            id='Booking from business with blocked review request',
        ),
        pytest.param(
            True,
            None,
            WEB,
            False,
            False,
            {'appointment.needed': False},
            id='Booking from not our user',
        ),
        pytest.param(
            True,
            None,
            WEB,
            False,
            True,
            {'appointment.needed': False},
            id='Review already exists',
        ),
    ),
)
@pytest.mark.django_db
def test_check_review_request_validity_failure(
    have_bci, business_kwargs, booking_source, bci_with_user, review_exists, expected
):
    booking = prepare_booking(
        have_bci=have_bci,
        business_kwargs=business_kwargs,
        booking_source=booking_source,
        bci_with_user=bci_with_user,
        review_exists=review_exists,
    )[0]
    with pytest.raises(ScenarioSkipped) as err:
        BookingFinishedScenario.check_review_request_validity(booking.appointment)
    assert err.value.message == expected


@pytest.mark.django_db
def test_check_review_request_validity_failure_if_bci_not_visible():
    booking = prepare_booking(
        have_bci=True,
        business_kwargs=None,
        booking_source=WEB,
        bci_with_user=False,
        review_exists=False,
    )[0]
    booking.appointment.booked_for.visible_in_business = False
    with pytest.raises(ScenarioSkipped) as err:
        BookingFinishedScenario.check_review_request_validity(booking.appointment)
    assert err.value.message == {
        'appointment.booked_for.visible_in_business': False,
        'appointment.booked_for.user': None,
    }


@pytest.mark.parametrize(
    ('create_srn',),
    (
        pytest.param(
            False,
            id='Create ShortReviewNotification',
        ),
        pytest.param(
            True,
            id='ShortReviewNotification exists',
        ),
    ),
)
@pytest.mark.django_db
def test_create_short_review_notifications(create_srn):
    booking, business, user = prepare_booking()
    if create_srn:
        baker.make(
            "pop_up_notification.ShortReviewNotification",
            user_id=user.id,
            business_id=business.id,
            subbooking=booking,
        )

    BookingFinishedScenario.create_short_review_notifications(booking.id)

    assert (
        ShortReviewNotification.objects.filter(
            business_id=business.id,
            user_id=user.id,
            subbooking=booking,
        ).count()
        == 1
    )


def prepare_booking(
    business_kwargs=None,
    have_bci=True,
    bci_with_user=True,
    booking_source=WEB,
    review_exists=False,
):
    user = baker.make("user.User")

    if business_kwargs is None:
        business_kwargs = dict(
            status=Business.Status.PAID,
        )

    business = baker.make(Business, **business_kwargs)

    bci = (
        baker.make(
            "business.BusinessCustomerInfo",
            business=business,
            user=user if bci_with_user else None,
        )
        if have_bci
        else None
    )
    booking, *_ = create_subbooking(
        business=business,
        booking_kws=dict(
            updated_by=user,
            booked_for=bci,
            source=baker.make(
                "booking.BookingSources",
                name=booking_source,
            ),
            type=Appointment.TYPE.CUSTOMER,
        ),
    )

    if review_exists:
        baker.make("reviews.Review", business=business, user=user)
    return booking, business, user
