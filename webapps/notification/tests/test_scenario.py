import json
import unittest
from datetime import timed<PERSON>ta

import pytest
from django.test.utils import override_settings
from mock import patch
from model_bakery import baker

from country_config import Country
from lib.test_utils import create_subbooking
from lib.tools import tznow

# pylint: disable=redefined-outer-name
# noinspection PyUnresolvedReferences
from webapps.booking.tests.conftest import (  # pylint: disable=unused-import
    business,
    staffer,
    combo_service_variant,
    combo_appointment,
    booked_date,
    get_booked_time,
    appliance,
    _service_variant,
    service_variant,
    multibooking_with_combo,
    frozen_time,
)
from webapps.business.baker_recipes import basic_staffer_recipe
from webapps.business.models import (
    Business,
    ServiceVariant,
    Service,
    ServiceVariantPayment,
)
from webapps.kill_switch.models import KillSwitch
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.notification.enums import ScheduleState
from webapps.notification.models import (
    NotificationSMSStatistics,
    NotificationSchedule,
    NotificationHistory,
    UserNotification,
    Reciever,
)
from webapps.notification.scenarios import (
    start_scenario,
    task_result_appended,
    NoShowInstructionScenario,
    NoShowPropositionScenario,
    BusinessActivityScenario,
    BookingChangedScenario,
)
from webapps.notification.scenarios.base import ScenarioSkipped
from webapps.notification.scenarios.scenarios_account import (
    AccountAddedScenario,
)
from webapps.notification.scenarios.sms_limits import sms_limits_notification
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import POS, PaymentType
from webapps.reviews.models import Review
from webapps.user.models import User, UserProfile


def test_task_result_appended():
    assert task_result_appended(None, {'a': 1}) == [{'a': 1}]
    assert task_result_appended('  ', {'a': 1}) == [{'a': 1}]
    assert task_result_appended('{]', {'a': 1}) == [{'a': 1}]
    assert task_result_appended('[]', {'a': 1}) == [{'a': 1}]
    assert task_result_appended('{}', {'a': 1}) == [{}, {'a': 1}]
    assert task_result_appended('[{"b": 2}]', {'a': 1}) == [{'b': 2}, {'a': 1}]


@pytest.mark.django_db
@patch('webapps.notification.tasks.push.send_push_notification')
def test_scenario_flow__review_added(push_mock):
    """
    This test mocks push sending because of CELERY_ALWAYS_EAGER and
    tasks being executed locally by blocking until the task returns.
    Without mocking task raises RuntimeError.

    """
    user = baker.make(User)
    review = baker.make(Review, user=user)
    start_scenario('review_added', review=review)

    # scenario is executed eagerly in single_notification_schedule_task

    push_mock.assert_called_once()

    nschedule = NotificationSchedule.objects.get()
    result = json.loads(nschedule.result)[0]
    assert nschedule.task_id == f'review_added:now:review_id={review.id}'
    assert nschedule.parameters == {'review_id': review.id}
    assert result['state'] == ScheduleState.SUCCESS


def test_scenario_get_booking_template_variables(combo_appointment):
    booking = combo_appointment.subbookings[0]
    scenario = BookingChangedScenario
    language = 'en'

    variables = scenario.get_booking_template_variables(booking, language, {})
    body = scenario.render_template(
        scenario.SCENARIO_NAME,
        'business_booking_reschedule_response',
        template_variables=variables,
        language=language,
    )
    expected_staffer_name = booking.combo_children[0].staffer.name
    expected_price = booking.service_price.price_only
    test_booking = variables['booking_info']['bookings_info']['subbookings'][0]
    assert test_booking['staffers'] == expected_staffer_name
    assert test_booking['price'] == expected_price
    assert body


def test_scenario_template_variables_change_staffer(multibooking_with_combo):
    scenario = BookingChangedScenario
    language = 'en'

    extra_staffer = basic_staffer_recipe.make(business=multibooking_with_combo.business)

    multibooking_with_combo.subbookings[1].combo_children[0].staffer = extra_staffer

    variables = scenario.get_booking_template_variables(
        multibooking_with_combo.subbookings[0], language, {}
    )
    body = scenario.render_template(
        scenario.SCENARIO_NAME,
        'business_booking_reschedule_response',
        template_variables=variables,
        language=language,
    )

    expected_single_staffer = multibooking_with_combo.subbookings[0].staffer.name
    expected_combo_staffer = ", ".join(
        sorted(
            set(
                _subbooking.staffer.name
                for _subbooking in multibooking_with_combo.subbookings[1].combo_children
            )
        )
    )
    test_booking = variables['booking_info']['bookings_info']
    assert test_booking['subbookings'][0]['staffers'] == expected_single_staffer
    assert test_booking['subbookings'][1]['staffers'] == expected_combo_staffer
    assert body


@pytest.mark.django_db
class TestScenarioFlowNoShowInformation(unittest.TestCase):

    @override_settings(NOSHOW_FEATURE_EDU_MAIL=True)
    @override_settings(POS__PREPAYMENTS=True)
    @patch('webapps.notification.models.tznow')
    def test_success(self, tznow_mock):
        tznow_mock.return_value = tznow() - timedelta(days=1)
        business = baker.make(
            Business,
            owner__email='<EMAIL>',
        )
        NotificationHistoryDocument.tasks_clear()

        start_scenario(NoShowInstructionScenario, business_id=business.id)

        nschedule = NotificationSchedule.objects.get()

        assert (
            nschedule.task_id == f'no_show_information:educational_mail:business_id={business.id}'
        )

        assert nschedule.parameters == {
            'business_id': business.id,
            'scenario_name': NoShowInstructionScenario.SCENARIO_NAME,
        }
        assert nschedule.state == ScheduleState.PENDING

        # run event manually
        NoShowInstructionScenario.event_educational_mail(nschedule.parameters, nschedule.task_id)

        assert (
            NotificationHistoryDocument.task_count(
                task_type=NotificationHistory.TASK_TYPE__NO_SHOW_INFORMATION
            )
            == 1
        )

    @override_settings(NOSHOW_FEATURE_EDU_MAIL=False)
    @override_settings(POS__PREPAYMENTS=True)
    @patch('webapps.notification.models.tznow')
    def test_false_mail_flag(self, tznow_mock):
        tznow_mock.return_value = tznow() - timedelta(days=1)
        business = baker.make(Business)

        start_scenario(NoShowInstructionScenario, business_id=business.id)

        nschedule = NotificationSchedule.objects.last()
        assert nschedule is None

    @override_settings(NOSHOW_FEATURE_EDU_MAIL=True)
    @override_settings(POS__PREPAYMENTS=False)
    @patch('webapps.notification.models.tznow')
    def test_false_prepayment_flag(self, tznow_mock):
        tznow_mock.return_value = tznow() - timedelta(days=1)
        business = baker.make(Business)

        start_scenario(NoShowInstructionScenario, business_id=business.id)

        nschedule = NotificationSchedule.objects.last()
        assert nschedule is None


@pytest.mark.django_db
class TestScenarioNowShowProposition(unittest.TestCase):

    @override_settings(NOSHOW_MAIL=True)
    @override_settings(POS__PREPAYMENTS=True)
    @override_settings(POS__PAY_BY_APP=True)
    @patch(
        'webapps.notification.scenarios.scenarios_noshowprotection.'
        'NoShowPropositionScenario.notification_required'
    )
    def test_success(self, notification_required_mock):
        notification_required_mock.return_value = None

        owner = baker.make(
            User,
            email='<EMAIL>',
        )
        profile = baker.make(
            UserProfile,
            source=None,
            profile_type=UserProfile.Type.BUSINESS,
            user=owner,
        )
        user_notification = baker.make(
            UserNotification,
            profile=profile,
            type=UserNotification.PUSH_NOTIFICATION,
            badge=2,
        )
        baker.make(
            Reciever,
            customer_notifications=user_notification,
            identifier='existing',
            device=Reciever.IOS,
        )
        business = baker.make(Business, owner=owner, phone='+***********')
        booking, *_ = create_subbooking(
            business=business,
        )
        start_scenario(
            NoShowPropositionScenario,
            business_id=business.id,
            booking_id=booking.id,
        )

        nschedule = NotificationSchedule.objects.all().order_by('created')
        assert nschedule.count() == 3

        parameters = {
            'business_id': business.id,
            'scenario_name': NoShowPropositionScenario.SCENARIO_NAME,
        }
        ordinal = ['first', 'second', 'third']
        for i in range(3):
            assert nschedule[i].task_id == (
                f'no_show_proposition:cancellation_fee_{ordinal[i]}_encouragement'
                f':booking_id={booking.id}'
            )

            assert nschedule[i].parameters == parameters
            assert nschedule[i].state == ScheduleState.PENDING

        # Run first event manually
        NoShowPropositionScenario.event_cancellation_fee_first_encouragement(
            parameters, nschedule[0].task_id
        )

        assert (
            NotificationHistoryDocument.task_count(
                task_type=NotificationHistory.TASK_TYPE__NO_SHOW_PROPOSITION,
                type=UserNotification.PUSH_NOTIFICATION,
            )
            == 1
        )

        # Run second event manually
        NoShowPropositionScenario.event_cancellation_fee_second_encouragement(
            parameters, nschedule[1].task_id
        )

        assert (
            NotificationHistoryDocument.task_count(
                task_type=NotificationHistory.TASK_TYPE__NO_SHOW_PROPOSITION,
                type=UserNotification.SMS_NOTIFICATION,
            )
            == 1
        )

        # Run third event manually
        NoShowPropositionScenario.event_cancellation_fee_third_encouragement(
            parameters, nschedule[2].task_id
        )

        assert (
            NotificationHistoryDocument.task_count(
                task_type=NotificationHistory.TASK_TYPE__NO_SHOW_PROPOSITION,
                type=UserNotification.EMAIL_NOTIFICATION,
            )
            == 1
        )
        assert not NotificationSMSStatistics.objects.filter(business_id=business.id).exists()

    @override_settings(POS__PAY_BY_APP=True)
    def test_notification_required(self):
        business = baker.make(Business)
        pos = baker.make(
            POS,
            business=business,
            active=True,
            pay_by_app_status=POS.PAY_BY_APP_ENABLED,
        )

        assert NoShowPropositionScenario.notification_required(business) is None

        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PAY_BY_APP)
        assert business.pos_pay_by_app_enabled

        sv = baker.make(
            ServiceVariant,
            duration='0100',
            service=baker.make(Service, business=business, gap_time=None),
            gap_hole_duration='0030',
            gap_hole_start_after='0001',
        )
        baker.make(
            ServiceVariantPayment,
            service_variant=sv,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
        )

        with self.assertRaises(ScenarioSkipped):
            NoShowPropositionScenario.notification_required(business)

        sv.active = False
        sv.save()

        assert NoShowPropositionScenario.notification_required(business) is None

        sv = baker.make(
            ServiceVariant,
            duration='0100',
            service=baker.make(Service, business=business, gap_time=None),
            gap_hole_duration='0030',
            gap_hole_start_after='0001',
        )
        baker.make(
            ServiceVariantPayment,
            service_variant=sv,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
        )

        with self.assertRaises(ScenarioSkipped):
            NoShowPropositionScenario.notification_required(business)

    @override_settings(POS__PAY_BY_APP=True)
    @override_settings(UNVERIFIED_PUSH_PAYMENTS=False)
    def test_notifications_required_pba_disabled(self):
        business = baker.make(Business)
        baker.make(
            POS,
            business=business,
            active=True,
            pay_by_app_status=POS.PAY_BY_APP_DISABLED,
        )

        with self.assertRaises(ScenarioSkipped):
            NoShowPropositionScenario.notification_required(business)

    @override_settings(POS__PAY_BY_APP=True)
    @override_settings(UNVERIFIED_PUSH_PAYMENTS=True)
    def test_notifications_required_pba_disabled_with_setting(self):
        business = baker.make(Business)
        baker.make(
            POS,
            business=business,
            active=True,
            pay_by_app_status=POS.PAY_BY_APP_DISABLED,
        )

        assert NoShowPropositionScenario.notification_required(business) is None


@pytest.mark.django_db
class TestSMSLimitsNotification(unittest.TestCase):

    def setUp(self):
        self.business = baker.make(
            Business,
            status=Business.Status.PAID,
            owner=baker.make(
                User,
                email='<EMAIL>',
            ),
        )

    def tearDown(self):
        super().tearDown()

        NotificationHistoryDocument.tasks_clear()

    def _mock_sms_stats(
        self, mock, sms_status='paid', sms_sent_count=0, sms_limit_free=0, sms_limit_payable=0
    ):
        mock.return_value = (sms_status, sms_sent_count, sms_limit_free, sms_limit_payable)

    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    def test_expired(self, sms_stats_mock):
        self._mock_sms_stats(sms_stats_mock, 'expired')
        sms_limits_notification(self.business)
        assert NotificationHistoryDocument.task_count(business_id=self.business.id) == 0

    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    def test_demo(
        self,
        sms_stats_mock,
    ):
        self._mock_sms_stats(
            sms_stats_mock,
            'demo',
            sms_sent_count=1,
            sms_limit_free=100,
        )
        sms_limits_notification(self.business)
        assert NotificationHistoryDocument.task_count(business_id=self.business.id) == 0

        NotificationHistoryDocument.tasks_clear(business_id=self.business.id)

        self._mock_sms_stats(
            sms_stats_mock,
            'demo',
            sms_sent_count=100,
            sms_limit_free=100,
        )
        sms_limits_notification(self.business)
        sms_limits_notification(self.business)
        assert (
            NotificationHistoryDocument.task_count(
                business_id=self.business.id, type=UserNotification.EMAIL_NOTIFICATION
            )
            == 1
        )

    @patch('webapps.notification.models.NotificationSMSStatistics.sms_stats')
    def test_paid(self, sms_stats_mock):
        self._mock_sms_stats(
            sms_stats_mock,
            'paid',
            sms_sent_count=1,
            sms_limit_payable=100,
        )
        sms_limits_notification(self.business)
        assert NotificationHistoryDocument.task_count(business_id=self.business.id) == 0

        self._mock_sms_stats(
            sms_stats_mock,
            'paid',
            sms_sent_count=1,
            sms_limit_free=100,
        )
        sms_limits_notification(self.business)
        sms_limits_notification(self.business)
        assert NotificationHistoryDocument.task_count(business_id=self.business.id) == 0

        # exhausted the paid limit
        self._mock_sms_stats(
            sms_stats_mock,
            'paid',
            sms_sent_count=100,
            sms_limit_payable=100,
        )
        sms_limits_notification(self.business)
        sms_limits_notification(self.business)
        assert NotificationHistoryDocument.task_count(business_id=self.business.id) == 1

        NotificationHistoryDocument.tasks_clear(business_id=self.business.id)

        # exhausted the free limit
        self._mock_sms_stats(
            sms_stats_mock,
            'paid',
            sms_sent_count=100,
            sms_limit_free=100,
        )
        sms_limits_notification(self.business)
        sms_limits_notification(self.business)
        assert (
            NotificationHistoryDocument.task_count(
                business_id=self.business.id, type=UserNotification.EMAIL_NOTIFICATION
            )
            == 1
        )


class BaseTestScenario(unittest.TestCase):

    @staticmethod
    def _get_scenario_names(add_to_schedule_mock_tasks_list):
        return [x[0].split(':')[1] for x in add_to_schedule_mock_tasks_list]

    def tearDown(self):
        super().tearDown()
        NotificationSMSStatistics.objects.all().delete()


@pytest.mark.django_db
class TestAccountAddedScenarioScenario(BaseTestScenario):

    def setUp(self):
        super().setUp()
        self.business = baker.make(
            Business,
            status=Business.Status.PAID,
            owner=baker.make(
                User,
                email='<EMAIL>',
            ),
        )
        self.user_profile = baker.make(
            UserProfile,
            user=baker.make(User),
            profile_type=UserProfile.Type.CUSTOMER,
            language='pl',
        )

    @override_settings(API_COUNTRY=Country.US)
    @patch('webapps.notification.scenarios.scenarios_account.AccountAddedScenario.add_to_schedule')
    def test_account_added_scenario_us(self, add_to_schedule_mock):
        AccountAddedScenario.plan(
            {
                'user_profile_id': self.user_profile.id,
            }
        )
        assert add_to_schedule_mock.call_count == 0

    @override_settings(API_COUNTRY=Country.BR)
    @patch('webapps.notification.scenarios.scenarios_account.AccountAddedScenario.add_to_schedule')
    def test_account_added_scenario_br(self, add_to_schedule_mock):
        AccountAddedScenario.plan(
            {
                'user_profile_id': self.user_profile.id,
            }
        )
        assert add_to_schedule_mock.call_count == 1
        scenario_names = self._get_scenario_names(add_to_schedule_mock.call_args_list[0][0][0])
        assert 'now' in scenario_names

    @override_settings(API_COUNTRY=Country.BR)
    @patch('webapps.notification.scenarios.scenarios_account.AccountAddedScenario.add_to_schedule')
    def test_account_added_killswitch(self, add_to_schedule_mock):
        baker.make(
            KillSwitch,
            is_killed=True,
            name=KillSwitch.System.OLD_WELCOME_MAIL_ACCOUNT_ADDED,
            type='M',
        )

        AccountAddedScenario.plan(
            {
                'user_profile_id': self.user_profile.id,
            }
        )
        assert add_to_schedule_mock.call_count == 0


@pytest.mark.django_db
class TestBusinessActivityScenario(BaseTestScenario):

    def setUp(self):
        super().setUp()
        self.business = baker.make(
            Business,
            status=Business.Status.PAID,
            owner=baker.make(
                User,
                email='<EMAIL>',
            ),
        )

    @override_settings(API_COUNTRY=Country.US)
    @patch(
        'webapps.notification.scenarios.scenarios_business.'
        'BusinessActivityScenario.add_to_schedule'
    )
    def test_business_activity(self, add_to_schedule_mock):
        BusinessActivityScenario.plan(
            {
                'business_id': self.business.id,
            }
        )
        assert add_to_schedule_mock.call_count == 1

    @override_settings(API_COUNTRY=Country.US)
    @patch(
        'webapps.notification.scenarios.scenarios_business.'
        'BusinessActivityScenario.add_to_schedule'
    )
    def test_business_activity_killswitch(self, add_to_schedule_mock):
        baker.make(
            KillSwitch,
            name=KillSwitch.System.OLD_WELCOME_MAIL_INACTIVITY_LOGIN,
            is_killed=True,
        )

        BusinessActivityScenario.plan(
            {
                'business_id': self.business.id,
            }
        )
        assert add_to_schedule_mock.call_count == 0

    @override_settings(API_COUNTRY=Country.US)
    @patch('webapps.notification.models.tznow')
    def test_create_tasks_for_business_activity_event(self, tznow_mock):
        tznow_mock.return_value = tznow() - timedelta(days=1)
        business = baker.make(
            Business,
            owner__email='<EMAIL>',
            status=Business.Status.PAID,
        )
        NotificationHistoryDocument.tasks_clear()

        start_scenario(BusinessActivityScenario, business_id=business.id)

        nschedule = NotificationSchedule.objects.first()

        assert nschedule.task_id == (
            f'business_activity:inactivity_login:days=7:business_id={business.id}'
        )

        assert nschedule.parameters == {
            'business_id': business.id,
            'days': 7,
        }
        assert nschedule.state == ScheduleState.PENDING

        BusinessActivityScenario.event_inactivity_login(nschedule.parameters, nschedule.task_id)

        assert (
            NotificationHistoryDocument.task_count(
                task_type=NotificationHistory.TASK_TYPE__BUSINESS_ACTIVITY,
                type=UserNotification.EMAIL_NOTIFICATION,
            )
            == 1
        )

    @override_settings(API_COUNTRY=Country.US)
    @patch('webapps.notification.models.tznow')
    def test_skip_business_activity_event_on_killswitch(self, tznow_mock):
        tznow_mock.return_value = tznow() - timedelta(days=1)
        business = baker.make(
            Business,
            owner__email='<EMAIL>',
            status=Business.Status.PAID,
        )
        NotificationHistoryDocument.tasks_clear()

        start_scenario(BusinessActivityScenario, business_id=business.id)

        nschedule = NotificationSchedule.objects.last()

        assert nschedule.task_id == (
            f'business_activity:inactivity_login:days=14:business_id={business.id}'
        )

        assert nschedule.parameters == {
            'business_id': business.id,
            'days': 14,
        }
        assert nschedule.state == ScheduleState.PENDING

        baker.make(
            KillSwitch,
            name=KillSwitch.System.OLD_WELCOME_MAIL_INACTIVITY_LOGIN,
            is_killed=True,
        )

        with self.assertRaisesRegex(
            expected_exception=ScenarioSkipped,
            expected_regex='Skipped due to old_mail_inactivity_login killswitch',
        ):
            BusinessActivityScenario.event_inactivity_login(nschedule.parameters, nschedule.task_id)


@override_settings(LANGUAGE_CODE='en-ie')
def test_ie_has_gb_date_format(frozen_time, combo_appointment):
    booking = combo_appointment.subbookings[0]
    scenario = BookingChangedScenario
    language = scenario.get_language(None, None)

    variables = scenario.get_booking_template_variables(booking, language, {})
    assert variables['booking_info']['booking_date_short'] == '01/06/2024'
