from __future__ import annotations

from django.conf import settings
from django.db.models import Exists, F, OuterRef, Q, QuerySet
from django.db.models.query import Prefetch

from lib.feature_flag.bug import UseNoReplyAddressInReplyToFieldFlag
from lib.tools import firstof
from settings.defaults import NO_REPLY_EMAIL_PRETTY
from webapps.business.models import Business, Resource
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.message_blast.models import BlockedPhoneNumber
from webapps.notification.base import Recipient as BaseRecipient
from webapps.notification.base import Recipients<PERSON>rovider, Sender, SenderProvider
from webapps.notification.models import NotificationHistory
from webapps.user.models import User, UserProfile


class Recipient(BaseRecipient):
    @classmethod
    def from_user(
        cls,
        user: User,
        profile_type: UserProfile.Type,
        name=None,
        customer_id=None,
    ) -> Recipient:
        return cls(
            user_id=user.id,
            profile_type=profile_type,
            name=name or user.get_full_name(),
            email=user.email,
            phone=user.cell_phone,
            language=user.get_language(profile_type),
            customer_id=customer_id,
        )

    @classmethod
    def from_users_in_bulk(
        cls,
        users: QuerySet[User],
        profile_type: UserProfile.Type,
        name=None,
        estimation_purpose=False,
    ) -> list[Recipient]:
        default_lang = settings.LANGUAGE_CODE[:2]
        fields_to_select_only = ['language', 'user_id']
        prefetch = Prefetch(
            'profiles',
            queryset=UserProfile.objects.exclude(language=default_lang).only(
                *fields_to_select_only
            ),
            to_attr='profile',
        )

        fields_to_select_only = ['email', 'cell_phone']
        if not estimation_purpose:
            fields_to_select_only.extend(['first_name', 'last_name', 'deleted'])
        users_with_prefetch = users.prefetch_related(prefetch).only(*fields_to_select_only)
        return [
            cls(
                user_id=user.id,
                name='<estimation stub>' if estimation_purpose else (name or user.full_name),
                email=user.email,
                phone=user.cell_phone,
                profile_type=profile_type,
                language=user.profile[0].language if user.profile else default_lang,
                customer_id=user.customer_id if hasattr(user, 'customer_id') else None,
                sms_marketing_consent=(
                    user.sms_marketing_consent if hasattr(user, 'sms_marketing_consent') else None
                ),
                has_blocked_phone_number=(
                    user.has_blocked_phone_number
                    if hasattr(user, 'has_blocked_phone_number')
                    else False
                ),
            )
            for user in users_with_prefetch
        ]

    @classmethod
    def from_customer(cls, customer: BusinessCustomerInfo) -> Recipient:
        if not customer.visible_in_business:
            return cls.empty_customer_recipient()
        return cls._from_customer(customer)

    @classmethod
    def from_appointment_customer(cls, customer: BusinessCustomerInfo) -> Recipient:
        if not customer.visible_in_business and customer.user is None:
            return cls.empty_customer_recipient()
        return cls._from_customer(customer)

    @classmethod
    def empty_customer_recipient(cls):
        return cls(
            name='',
            email='',
            profile_type=UserProfile.Type.CUSTOMER,
        )

    @classmethod
    def _from_customer(cls, customer: BusinessCustomerInfo) -> Recipient:
        profile_type = UserProfile.Type.CUSTOMER
        if customer.user:
            return cls.from_user(
                user=customer.user,
                profile_type=profile_type,
                customer_id=customer.id,
            )
        return cls(
            name=customer.full_name,
            email=customer.email,
            phone=customer.cell_phone,
            profile_type=profile_type,
            customer_id=customer.id,
        )

    @classmethod
    def from_customers_in_bulk(
        cls,
        bcis: QuerySet[BusinessCustomerInfo],
        estimation_purpose: bool = False,
    ) -> list[Recipient]:
        bcis_without_user = (
            bcis.annotate_has_blocked_phone_number()
            .annotate_sms_marketing_consent()
            .filter(
                user__isnull=True,
                visible_in_business=True,
            )
        )
        fields_to_retrieve = [
            'id',
            'email',
            'cell_phone',
            'web_communication_agreement',
            'has_blocked_phone_number',
            'sms_marketing_consent',
        ]
        if not estimation_purpose:
            fields_to_retrieve.extend(['first_name', 'last_name'])
        bcis_without_user = bcis_without_user.values(*fields_to_retrieve)
        recipients_without_user = [
            cls(
                name=(
                    '<estimation stub>'
                    if estimation_purpose
                    else BusinessCustomerInfo.build_full_name(
                        customer['first_name'], customer['last_name']
                    )
                ),
                email=customer['email'],
                phone=customer['cell_phone'],
                profile_type=UserProfile.Type.CUSTOMER,
                push_receivers=[],
                language=settings.LANGUAGE_CODE[:2],
                customer_id=customer['id'],
                sms_marketing_consent=customer['sms_marketing_consent'],
                has_blocked_phone_number=customer['has_blocked_phone_number'],
            )
            for customer in bcis_without_user
        ]

        users = (
            User.objects.filter(
                business_customer_infos__in=bcis,
                business_customer_infos__visible_in_business=True,
            )
            .annotate_sms_marketing_consent()
            .annotate(
                customer_id=F('business_customer_infos'),
                has_blocked_phone_number=Exists(
                    BlockedPhoneNumber.objects.filter(
                        Q(cell_phone=OuterRef('cell_phone'))
                        | Q(cell_phone=OuterRef('business_customer_infos__cell_phone'))
                    )
                ),
            )
        )
        recipients_with_user = cls.from_users_in_bulk(
            users=users,
            profile_type=UserProfile.Type.CUSTOMER,
            estimation_purpose=estimation_purpose,
        )

        return recipients_without_user + recipients_with_user

    @classmethod
    def from_staffer(cls, staffer: Resource, business_name=None):
        profile_type = UserProfile.Type.BUSINESS
        recipient = (
            cls.from_user(staffer.staff_user, profile_type, business_name)
            if staffer.staff_user
            else cls(
                name=business_name or staffer.name,
                email=staffer.staff_email,
                phone=staffer.staff_cell_phone,
                profile_type=profile_type,
            )
        )
        recipient.staffer = staffer
        return recipient


class NotificationUser(RecipientsProvider):
    user: User

    def get_recipients(self):
        return [Recipient.from_user(self.user, UserProfile.Type.BUSINESS)]


class NotificationCustomerUser(RecipientsProvider):
    customer_card: BusinessCustomerInfo
    customer: User

    def get_recipients(self):
        if self.customer_card:
            return [Recipient.from_customer(self.customer_card)]
        return [Recipient.from_user(self.customer, UserProfile.Type.CUSTOMER)]


class NotificationStaffer(RecipientsProvider):
    staffer: Resource

    def get_recipients(self):
        if not self.staffer:
            return []
        return [Recipient.from_staffer(self.staffer)]


class NotificationCustomer(RecipientsProvider):
    customer: BusinessCustomerInfo

    def get_recipients(self):
        if not self.customer:
            return []
        return [Recipient.from_customer(self.customer)]


class NotificationCustomers(RecipientsProvider):
    customers: list[BusinessCustomerInfo]

    def get_recipients(self):
        return Recipient.from_customers_in_bulk(bcis=self.customers)


class _AccessLevelRecipients(RecipientsProvider):
    business: Business
    staff_access_levels = None

    def get_recipients(self):
        return [
            Recipient.from_staffer(staffer, self.business.name)
            for staffer in Resource.objects.filter(
                business_id=self.business.id,
                type=Resource.STAFF,
                active=True,
                staff_access_level__in=self.staff_access_levels,
                deleted__isnull=True,
            )
        ]


class Managers(_AccessLevelRecipients):
    """Managers (and Owner of course)"""

    staff_access_levels = (
        Resource.STAFF_ACCESS_LEVEL_MANAGER,
        Resource.STAFF_ACCESS_LEVEL_OWNER,
    )

    def get_recipients(self):
        recipients = super().get_recipients()
        if not recipients:
            recipients = [
                Recipient.from_user(
                    self.business.owner,
                    UserProfile.Type.BUSINESS,
                    self.business.name,
                )
            ]
        return recipients


class OwnerOnly(_AccessLevelRecipients):
    staff_access_levels = (Resource.STAFF_ACCESS_LEVEL_OWNER,)


class Reception(_AccessLevelRecipients):
    staff_access_levels = (Resource.STAFF_ACCESS_LEVEL_RECEPTION,)


class SystemSender(SenderProvider):
    history_data_sender = NotificationHistory.SENDER_SYSTEM

    def get_sender(self):
        return Sender(
            name='Booksy.com',
            email=settings.NO_REPLY_EMAIL,
        )


class ServicesConciergeSender(SenderProvider):
    history_data_sender = NotificationHistory.SENDER_SYSTEM

    def get_sender(self):
        return Sender(
            name='Booksy.com',
            email=settings.SERVICES_CONCIERGE_EMAIL,
        )


class BusinessSender(SenderProvider):
    history_data_sender = NotificationHistory.SENDER_BUSINESS
    business: Business

    def get_sender(self):
        sender = firstof(Managers(self.notification).get_recipients())
        if UseNoReplyAddressInReplyToFieldFlag():
            sender.email = settings.NO_REPLY_EMAIL
        return sender


class MessageBlastSender(BusinessSender):
    def get_sender(self):
        sender = super().get_sender()
        sender.from_email = settings.NO_REPLY_MB_NOTIFICATION_EMAIL
        return sender


class UserSender(SenderProvider):
    history_data_sender = NotificationHistory.SENDER_CUSTOMER
    user: User

    def get_sender(self):
        return Sender(
            name=self.user.get_full_name(),
            email=NO_REPLY_EMAIL_PRETTY,
        )


class GDPRRecipient(Managers):
    """Redirects owners notification to GDPR email address"""

    staff_access_levels = (Resource.STAFF_ACCESS_LEVEL_OWNER,)

    def get_recipients(self):
        recipients = super().get_recipients()
        for recipient in recipients:
            recipient.email = settings.COUNTRY_TO_EMAIL_MAP.get(settings.API_COUNTRY)
        return recipients
