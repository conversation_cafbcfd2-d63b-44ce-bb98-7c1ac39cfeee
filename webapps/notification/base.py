import inspect
import logging
from abc import ABC, abstractmethod
from collections import defaultdict
from contextlib import contextmanager
from dataclasses import dataclass, field, replace
from datetime import datetime, timedelta
from itertools import groupby
from typing import Callable, Dict, List, Optional, Set, Tuple, Type

from django.conf import settings
from django.db.models import Model
from django.utils.encoding import force_str
from django.utils.functional import Promise, cached_property
from django.utils.translation import override

from lib.abc import abstractclassattribute
from lib.enums import StrEnum
from lib.events import serialize_instance
from lib.serializers import safe_get
from lib.feature_flag.feature import TaskTypeNotificationCategoryEnumValueFlag
from lib.tools import tznow
from webapps.business.models import Business
from webapps.notification.enums import (
    NotificationCategory,
    NotificationGroup,
    NotificationIcon,
    NotificationSize,
    NotificationTarget,
    ScheduleState,
)
from webapps.notification.models import NotificationSchedule
from webapps.notification.push import notification_receivers_list_in_bulk
from webapps.notification.schedule_record import ScheduleRecord
from webapps.user.models import UserProfile

log = logging.getLogger('booksy.notifications')


class FinalMethodException(Exception): ...


@dataclass
class PushTarget:
    type: str
    id: Optional = None  # pylint: disable=invalid-name
    title: Optional[str] = None


class NotificationComponent(ABC):
    """Provides partial functionality to Notification

    Used to define Notification`s logic through composition
    """

    def __init__(self, notification: 'BaseNotification'):
        self.notification = notification
        if hasattr(self, '__annotations__'):
            for attr in self.__annotations__:
                if hasattr(self, attr):
                    continue
                if not hasattr(self.notification, attr):
                    raise RuntimeError(  # pylint: disable=broad-exception-raised
                        f'Annotated attr `{attr} for {self.__class__} '
                        f'is missing on {self.notification}'
                    )
                setattr(self, attr, getattr(self.notification, attr))


@dataclass
class PopupTemplate:
    messages: List[str]
    icon: NotificationIcon = None
    photo: str = ''
    crucial: bool = False
    relevance: int = 1
    group: NotificationGroup = NotificationGroup.NOTIFICATION
    size: NotificationSize = NotificationSize.NORMAL
    params: Dict = None
    image: str = ''

    def resolve(self):
        """Resolve translation promises in messages
        :return: PopupTemplate
        """
        return replace(self, messages=[force_str(message) for message in self.messages])

    def __call__(self, **kwargs):
        return replace(self, **kwargs)


class Channel(NotificationComponent):
    class Type(StrEnum):
        EMAIL = 'email'
        PUSH = 'push'
        SMS = 'sms'
        POPUP = 'popup'

    type: Type = abstractclassattribute()

    @abstractmethod
    def render_template(self, template, context, recipient):
        """Render template

        Always called within context of the recipient's language.
        Should return lazy translations resolved.
        """
        # pylint: disable=unnecessary-ellipsis
        ...

    @abstractmethod
    def send(self, recipients): ...

    @staticmethod
    def recipients_by_language(recipients):
        def by_lang(recipient):
            return recipient.language

        recipients = sorted(recipients, key=by_lang)
        for lang, recipients_group in groupby(recipients, by_lang):
            with override(lang):
                yield recipients_group

    @abstractmethod
    def is_valid_recipient(self, recipient: 'Recipient') -> bool: ...

    def get_content(self, recipient=None):
        content = self.notification.get_custom_content(self.type)
        if content is not NotImplemented:
            if isinstance(content, Promise):
                content = force_str(content)
            return content

        template = self.notification.get_template(self.type)
        context = self.notification.context
        return self.render_template(template, context, recipient)


class SchedulePlanner(NotificationComponent):
    MIN_HOUR = 7
    MAX_HOUR = 22

    """Selects schedule time"""

    @abstractmethod
    def get_time(self, **kwargs) -> datetime: ...

    def apply_global_limit(self, when):
        if when.hour < self.MIN_HOUR:
            return when.replace(hour=self.MIN_HOUR)
        if when.hour > self.MAX_HOUR:
            return when.replace(hour=self.MAX_HOUR)
        return when


@dataclass
class Sender:
    name: str
    email: str
    from_email: str = field(default=None, init=False)


@dataclass
class Recipient(Sender):
    name: str
    email: str
    profile_type: UserProfile.Type
    phone: str = None
    user_id: Optional[int] = None
    customer_id: Optional[int] = None
    language: str = settings.LANGUAGE_CODE[:2]
    push_receivers: list = field(default_factory=list)
    sms_marketing_consent: bool = False
    has_blocked_phone_number: bool = False

    def __hash__(self):
        return hash(self.email)

    def __eq__(self, other):
        if isinstance(other, Recipient):
            return self.email == other.email
        return False


class RecipientsProvider(NotificationComponent):
    @abstractmethod
    def get_recipients(self) -> List[Recipient]: ...


class SenderProvider(NotificationComponent):
    # NotificationHistory.SENDER_TYPES
    # It is used to calculate sms limits. Better to rename
    # to some more clear name like sms_billing...
    history_data_sender = abstractclassattribute()

    @abstractmethod
    def get_sender(self) -> Sender: ...


class UniqueRecipientsMixin:
    """Mixin with private methods for filtering unique recipients for notifications channels."""

    @classmethod
    def _filter_unique_recipients(cls, channel_recipients: Dict[Channel.Type, List[Recipient]]):
        if Channel.Type.EMAIL in channel_recipients:
            recipients = channel_recipients[Channel.Type.EMAIL]
            unique_recipients = cls._get_unique_recipients_for_email_channel(recipients)
            channel_recipients[Channel.Type.EMAIL] = unique_recipients
        if Channel.Type.SMS in channel_recipients:
            recipients = channel_recipients[Channel.Type.SMS]
            unique_recipients = cls._get_unique_recipients_for_sms_channel(recipients)
            channel_recipients[Channel.Type.SMS] = unique_recipients

    @staticmethod
    def _get_unique_recipients_for_email_channel(recipients: List[Recipient]) -> List[Recipient]:
        return list(set(recipients))

    @staticmethod
    def _get_unique_recipients_for_sms_channel(recipients: List[Recipient]) -> List[Recipient]:
        recipients_dict = {}

        for recipient in recipients:
            if recipient.phone not in recipients_dict:
                recipients_dict[recipient.phone] = recipient

        return list(recipients_dict.values())


class ChannelSelector(UniqueRecipientsMixin, NotificationComponent):
    def get_recipients_by_channel(self, channels, recipients):
        recipients = self.maybe_load_push_receivers_in_bulk(
            channels=channels,
            recipients=recipients,
        )
        channel_recipients = defaultdict(list)
        for recipient in recipients:
            selected = self.select(recipient, channels)
            for channel_type in selected:
                channel_recipients[channel_type].append(recipient)

        self._filter_unique_recipients(channel_recipients)

        return channel_recipients

    def select(self, recipient: Recipient, channels: List[Channel]) -> Set[Channel.Type]:
        possible_channels = {
            channel.type for channel in channels if channel.is_valid_recipient(recipient)
        }
        possible_channels = self.filter_by_priority(possible_channels)

        return possible_channels

    @classmethod
    def maybe_load_push_receivers_in_bulk(
        cls,
        channels: list[Channel],
        recipients: list[Recipient],
    ):
        if Channel.Type.PUSH not in [channel.type for channel in channels]:
            return recipients

        if not recipients:
            return recipients

        # Never mixed Recipient.profile_type's
        profile_type = recipients[0].profile_type

        user_ids = {recipient.user_id for recipient in recipients if recipient.user_id is not None}

        push_receivers = cls.load_push_receivers_in_bulk(
            user_ids=user_ids,
            profile_type=profile_type,
        )

        for recipient in recipients:
            if recipient.user_id is not None:
                recipient.push_receivers = push_receivers.get(recipient.user_id, [])
            else:
                recipient.push_receivers = []

        return recipients

    @staticmethod
    def load_push_receivers_in_bulk(user_ids, profile_type, excluded_devices=None):
        return notification_receivers_list_in_bulk(
            user_ids=user_ids,
            profile_type=profile_type,
            excluded_devices=excluded_devices,
        )

    @cached_property
    def sms_priority(self):
        if hasattr(self.notification, 'business'):
            return self.notification.business.sms_priority
        if hasattr(self, 'business'):
            return self.business.sms_priority
        return None

    def filter_by_priority(self, channel_types) -> list[Channel.Type]:
        sms_priority = self.sms_priority
        if not sms_priority:
            return channel_types

        if sms_priority == Business.SMSPriority.ONLY_PUSH:
            self.safe_remove(Channel.Type.SMS, channel_types)
        elif sms_priority == Business.SMSPriority.PREFER_SMS and Channel.Type.SMS in channel_types:
            self.safe_remove(Channel.Type.PUSH, channel_types)
        elif (
            sms_priority == Business.SMSPriority.PREFER_PUSH and Channel.Type.PUSH in channel_types
        ):
            self.safe_remove(Channel.Type.SMS, channel_types)
        return channel_types

    @staticmethod
    def safe_remove(channel_type: Channel.Type, channel_types: list[Channel.Type]):
        try:
            channel_types.remove(channel_type)
        except KeyError:
            pass


class Context(NotificationComponent):
    """Provides context to resolve variables in templates

    Ensure to cache common data as it will be called for every recipient
    """

    @abstractmethod
    def get_context(self): ...


class Attachment(NotificationComponent):
    """Attachments for email"""

    @abstractmethod
    def get_attachments(self): ...


# AbstractScenario replacement
class BaseNotification(ABC):
    """Base Notification (replaces Scenario)

    Based on some object(s), usually a model/sender of the event:
    - defines notification content for one or more channels
    - defines notification recipient
    - ev. selects channels to use
    - ev. checks if notification should be skipped at the moment of
      sending

    example draft
    >>> # webapps/booking/events.py
    >>> booking_created_event = EventSignal(event_type='booking_created')
    >>>
    >>> # service/booking/appointments.py#RequestHandler
    >>>     booking_created_event.send(booking)
    >>>
    >>> # webapps/booking/notifications.py
    >>> class BookingCreatedNotification(BaseNotification):
    >>>     channels = [EmailChannel, PushChannel]
    >>>
    >>>     def __init__(self, booking):
    >>>         self.booking = booking
    >>>
    >>>     def get_context(self):
    >>>         return dict(status=self.booking.appointment.status)
    >>>
    >>> # webapps/booking/actions.py
    >>> @lazy_event_receiver(booking_created_event)
    >>> def booking_created_receiver(sender, **kwargs):
    >>>     BookingCreatedNotification(sender).send()

    Notification can be send instantly:
    >>> Notification(event_sender, **parameters).send()
    asynchronously (via celery):
    >>> Notification(event_sender, **parameters).async_send()
    or scheduled:
    >>> Notification(event_sender, **parameters).schedule(when=some_time)

    All notifications must be imported at startup to register in the base class.
    Otherwise async_send will not work. Simply put them in `notifications`
    module. The `notifications`, `receivers` and `actions' modules
    in every django app are autodiscovered.
    """

    channels: Tuple[Type[Channel], ...] = abstractclassattribute()
    channel_selector = ChannelSelector

    sender: Optional[Type[SenderProvider]]
    recipients: Optional[Tuple[Type[RecipientsProvider]]]
    get_recipients: Optional[Callable]
    get_language: Optional[Callable]

    contexts: Tuple[Type[Context], ...] = tuple()
    get_context: Optional[Callable]

    attachments: Tuple[Type[Attachment], ...] = tuple()
    get_attachments: Optional[Callable]

    email_template_name: Optional[str]
    push_template: Optional[str]
    sms_template: Optional[str]
    popup_template: Optional[Dict]

    get_email_content: Optional[Callable]
    get_push_content: Optional[Callable]
    get_sms_content: Optional[Callable]
    get_popup_content: Optional[Callable]

    category: NotificationCategory = abstractclassattribute()
    schedule_planner: Optional[Type[SchedulePlanner]]

    target: Optional[NotificationTarget]

    email_bcc: Optional[tuple[str]] = tuple()
    email_easy_unsubscribe_url: str | None = None

    # filled in init
    event_sender: Optional[Model]
    parameters: Dict

    # this methods must not be overridden
    _final_methods = (
        'send',
        'async_send',
        'schedule',
    )

    # registry of all defined notifications
    _registry = {}

    # instance attributes
    event_sender: Model
    parameters: Dict

    def __init_subclass__(subclass, **kwargs):  # pylint: disable=bad-classmethod-argument
        if not inspect.isabstract(subclass) and not subclass.is_spy():
            subclass.register()

        if kwargs.get('channel'):
            return

        for meth in BaseNotification._final_methods:
            if meth in subclass.__dict__:
                raise FinalMethodException(f'Override of final method `{meth}`')

    def __setattr__(self, key, value):
        if key not in ['event_sender', 'parameters'] and key in self.__annotations__:
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                'Setting predefined Notification attributes not allowed'
            )
        super().__setattr__(key, value)

    @classmethod
    def is_spy(cls) -> bool:
        """Spy notifications may be used in tests"""
        return cls.__name__.endswith("Spy")

    @classmethod
    def register(cls):
        notif_type = cls.__name__
        if notif_type in BaseNotification._registry:
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                f'Duplicated Notification name: {notif_type}'
            )
        BaseNotification._registry[notif_type] = cls

    @classmethod
    def get_type(cls, notif_type):
        """Load Notification class from registry by notif_type"""
        return cls._registry.get(notif_type)

    @property
    def notif_type(self):
        return self.__class__.__name__

    def __init__(self, event_sender: Optional[Model], **parameters) -> None:
        self.event_sender = event_sender
        # all parameters must be serializable
        self.parameters = parameters
        # current recipient; dynamically changed during send
        self._recipient = NotImplemented
        self._recipient_used = False

        for channel in self.channels:
            if channel.type == 'email' and not hasattr(self, 'sender'):
                # pylint: disable=broad-exception-raised
                raise RuntimeError('Sender is required for email channel')
                # pylint: enable=broad-exception-raised

    @cached_property
    def datetime(self):
        tz = 'UTC'
        if hasattr(self, 'business'):
            tz = self.business.get_timezone()  # pylint: disable=no-member
        return tznow(tz)

    @property
    def identity(self) -> str:
        """Identity of the notification

        The first element must be always the notif_type.
        Some or all of the self.parameters can be appended.
        The same notification should not be send twice. Reschedule of the
        same notification replaces previously planed/scheduled notification.
        """
        return f'{self.notif_type}'

    # final
    @cached_property
    def schedule_record(self):
        return ScheduleRecord(self)

    def should_skip_with_plea(self) -> Tuple[bool, Optional[str]]:
        """Is it still valid on the scheduled time?

        :returns Tuple[should: bool, reason: str]
        """
        # temp during migration to 3.0
        from webapps.notification.channels import PopupChannel  # pylint: disable=cyclic-import

        if not settings.PYTEST and PopupChannel in self.channels:
            business = getattr(self, 'business', None)
            if business is not None:
                if not business.can_use_frontdesk:
                    return True, 'Business cannot use frontdesk'
        return False, None

    def maybe_remove_recipients(self, recipients: List[Recipient]) -> List[Recipient]:
        # TODO: uncomment after fixing notification senders
        # from webapps.notification.recipients import BusinessSender, SystemSender
        business = getattr(self, 'business', None)
        if business is None:
            return recipients

        if (
            # self.sender is BusinessSender and
            business.customer_notifications_disabled
        ):
            recipients = tuple(
                recipient
                for recipient in recipients
                if recipient.profile_type != UserProfile.Type.CUSTOMER
            )
        if (
            # self.sender is SystemSender and
            business.business_notifications_disabled
        ):
            recipients = tuple(
                recipient
                for recipient in recipients
                if recipient.profile_type != UserProfile.Type.BUSINESS
            )

        return recipients

    # final
    def send(self):
        should_skip, reason = self.should_skip_with_plea()
        if should_skip:
            self.schedule_record.log_skip(reason)
            self.schedule_record.save()
            return

        channels = [channel(self) for channel in self.channels]
        channel_selector = self.channel_selector(self)
        channel_recipients = channel_selector.get_recipients_by_channel(
            channels, self.maybe_remove_recipients(self.resolved_recipients)
        )

        if not channel_recipients:
            self.schedule_record.log_skip('No recipients: ignored')
            self.schedule_record.save()
            return

        try:
            for channel in channels:
                channel.send(channel_recipients[channel.type])
        except Exception as e:  # pylint: disable=broad-except
            self.schedule_record.log_exception(e)
            raise
        else:
            self.schedule_record.log_success()
            self.send_analytics()
        finally:
            self.schedule_record.save()

    # final
    def async_send(self):
        from webapps.notification.tasks import async_send_notification_task

        task_kwargs = serialize_instance(self.event_sender, **self.parameters)
        async_send_notification_task.delay(self.notif_type, **task_kwargs)

    # final
    def schedule(self, when=None, **kwargs):
        if when is None:
            planner = getattr(self, 'schedule_planner')
            if not planner:
                # pylint: disable=broad-exception-raised
                raise RuntimeError('Schedule time is missing')
                # pylint: enable=broad-exception-raised
            when = planner(self).get_time(**kwargs)
            if when is None:
                self.schedule_record.log_skip('No result from schedule planner')
                self.schedule_record.save()
                return

        self.schedule_record.upsert(when)

    def get_history_data(self):
        return {
            'task_id': self.identity,
            'task_type': (
                self.category.name.lower()
                if not TaskTypeNotificationCategoryEnumValueFlag()
                else self.category
            ),
            'sender': self.sender.history_data_sender,
            'business_id': safe_get(self, ['business', 'id']),
            'booking_id': safe_get(self, ['appointment', 'subbookings', 0, 'id']),
            'appointment_id': safe_get(self, ['appointment', 'id']),
        }

    def get_sender(self):
        return self.sender(self)  # pylint: disable=not-callable

    @cached_property
    def resolved_recipients(self):
        if hasattr(self, 'get_recipients'):
            return self.get_recipients()

        if hasattr(self, 'recipients'):
            return [
                recipient
                for provider in self.recipients
                for recipient in provider(self).get_recipients()
            ]

        raise NotImplementedError('get_recipients method or recipients property should be set')

    def get_custom_content(self, channel: Channel.Type):
        custom_fn = f'get_{channel}_content'
        if hasattr(self, custom_fn):
            return getattr(self, custom_fn)()
        return NotImplemented

    def get_template(self, channel: Channel.Type):
        suffix = '_name' if channel == 'email' else ''
        attr = f'{channel}_template{suffix}'
        try:
            return getattr(self, attr)
        except AttributeError as error:
            raise NotImplementedError from error

    @cached_property
    def context(self):
        """Context for template rendering

        Usually it is cached, but if context functions use get_recipient,
        the cache is cleared after each recipient
        """
        context = {}
        for mixin in getattr(self, 'contexts', tuple()):
            context.update(mixin(self).get_context())

        if hasattr(self, 'get_context'):
            context.update(self.get_context())
        return context

    @property
    def attachment_list(self):
        """Attachments for email channel"""
        attachment_list = []
        attachments = getattr(self, 'attachments', tuple())
        for mixin in attachments:
            attachment_list.extend(mixin(self).get_attachments())

        if hasattr(self, 'get_attachments'):
            attachment_list.extend(self.get_attachments())
        return attachment_list

    def get_target(self) -> PushTarget:
        if hasattr(self, 'target'):
            return PushTarget(type=self.target.value)
        return NotImplemented

    @classmethod
    def from_admin(cls, parameters):
        """Create instance based on parameters provided in admin form

        We return parsed parameters here. Subclass should return real instance

        :param parameters: comma separated list of parameters
        :return: Notification instance
        """
        return list(
            map(
                lambda x: int(x) if x.isnumeric() else x,
                parameters.split(','),
            )
        )

    @classmethod
    def latest_schedules(cls, days=1):
        notif_type = cls.__name__
        threshold = tznow() - timedelta(days=days)

        latest = NotificationSchedule.objects.filter(
            task_id__startswith=notif_type,
            state=ScheduleState.SUCCESS,
            finished__gt=threshold,
        ).only('task_id', 'parameters')

        return [
            {
                'identity': schedule.task_id,
                'parameters': schedule.parameters or None,
            }
            for schedule in latest
        ]

    @contextmanager
    def set_recipient(self, recipient):
        self._recipient = recipient
        try:
            yield recipient
        finally:
            self._recipient = NotImplemented
            if self._recipient_used:
                self._recipient_used = False
                if 'context' in self.__dict__:
                    delattr(self, 'context')

    def get_recipient(self, raise_exception=False):
        self._recipient_used = True
        if self._recipient is NotImplemented:
            if raise_exception:
                raise NotImplementedError
            return None
        return self._recipient

    def send_analytics(self):
        """
        Implement analytic events here.
        """
