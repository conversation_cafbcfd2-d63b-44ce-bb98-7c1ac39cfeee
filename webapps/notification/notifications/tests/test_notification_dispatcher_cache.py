from webapps.notification.notifications.cache_dispatcher import NotificationDispatch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_notification_history_limit():
    NotificationDispatcherHistoryCache.clear_all_history()
    history_list = list(list(j for j in range(i, i + 10)) for i in range(1, 150, 10))
    history_list_2 = list(list(j for j in range(i, i + 10)) for i in range(1, 100, 10))

    for ids in history_list:
        NotificationDispatcherHistoryCache.add_to_history(ids)

    assert NotificationDispatcherHistoryCache.get_history() == set(list(range(51, 151)))

    for ids in history_list_2:
        NotificationDispatcherHistoryCache.add_to_history(ids)

    assert NotificationDispatcherHistoryCache.get_history() == set(list(range(1, 101)))

    NotificationDispatcherHistoryCache.clear_all_history()
