# Generated by Django 1.11.7 on 2017-12-07 13:12
from django.db import migrations
from django.db.models.expressions import RawSQL


def forward_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    Template = apps.get_model('marketing', 'Template')

    Template.objects.using(db_alias).filter(recipients__birthdayUnit='weeks').update(
        recipients=RawSQL("""jsonb_set(recipients, '{birthdayUnit}', '"WEEK"'::jsonb)""", ())
    )

    Template.objects.using(db_alias).filter(recipients__birthdayUnit='days').update(
        recipients=RawSQL("""jsonb_set(recipients, '{birthdayUnit}', '"DAY"'::jsonb)""", ())
    )

    Template.objects.using(db_alias).filter(recipients__periodUnit='months').update(
        recipients=RawSQL("""jsonb_set(recipients, '{periodUnit}', '"MONTH"'::jsonb)""", ())
    )

    Template.objects.using(db_alias).filter(recipients__periodUnit='years').update(
        recipients=RawSQL("""jsonb_set(recipients, '{periodUnit}', '"YEAR"'::jsonb)""", ())
    )


def no_op(apps, schema_editor):
    pass


class Migration(migrations.Migration):
    atomic = False

    dependencies = [
        ('marketing', '0015_digitalflyer'),
    ]

    operations = [migrations.RunPython(forward_func, no_op)]
