# Generated by Django 2.0.13 on 2020-03-16 09:32

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('marketing', '0027_auto_20190630_1628'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='smsinvitationevent',
            options={'get_latest_by': 'updated'},
        ),
        migrations.AddField(
            model_name='smsinvitationevent',
            name='created',
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now, verbose_name='Created (UTC)'
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='smsinvitationevent',
            name='deleted',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
        ),
        migrations.AddField(
            model_name='smsinvitationevent',
            name='updated',
            field=models.DateTimeField(auto_now=True, db_index=True, verbose_name='Updated (UTC)'),
        ),
    ]
