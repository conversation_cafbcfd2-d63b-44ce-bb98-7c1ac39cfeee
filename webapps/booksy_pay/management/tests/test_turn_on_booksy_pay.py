import pytest
from django.core.management import call_command

from webapps.pos.baker_recipes import pos_recipe
from webapps.pos.models import POS, POSChangeLog


@pytest.mark.django_db
@pytest.mark.parametrize(
    'command, log_rows_count',
    [
        (('turn_on_booksy_pay',), 0),
        (('turn_on_booksy_pay', '--add-history'), 2),
    ],
)
def test_turn_on_bp__all(command, log_rows_count):
    pos_recipe.make(booksy_pay_enabled=False)
    pos_recipe.make(booksy_pay_enabled=False)

    call_command(*command)

    assert POS.objects.filter(booksy_pay_enabled=True).count() == 2
    assert POSChangeLog.objects.count() == log_rows_count


@pytest.mark.django_db
def test_turn_on_bp__selected_business():
    pos_1 = pos_recipe.make(booksy_pay_enabled=False)
    pos_2 = pos_recipe.make(booksy_pay_enabled=False)

    call_command('turn_on_booksy_pay', '--business-ids', pos_1.business_id)

    pos_1.refresh_from_db()
    pos_2.refresh_from_db()

    assert pos_1.booksy_pay_enabled is True
    assert pos_2.booksy_pay_enabled is False
