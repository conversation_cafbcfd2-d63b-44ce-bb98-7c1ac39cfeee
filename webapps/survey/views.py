from copy import copy

from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.mixins import RetrieveModelMixin
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import Request, Response

from drf_api.base_views import BaseBooksySessionGenericAPIView, BaseBooksySessionGenericViewSet
from drf_api.mixins import (
    BusinessViewValidatorMixin,
    QuerySerializerMixin,
    ResponseSerializerMixin,
)
from drf_api_utils.serializers import EmptyRequestSerializer, EmptyResponseSerializer
from lib.tools import get_object_or_404, sasrt
from service.mixins.validation import ValidationMixin
from webapps.survey.models import Poll, PollChoice, PollResponse
from webapps.survey.serializer import (
    CustomerPollAnswerSerializer,
    PollAnswerSerializer,
    PollListQuerySerializer,
    PollListRequestSerializer,
    PollListResponseSerializer,
    PollQuerySerializer,
    PollResponseSerializer,
    PollSerializer,
)


class PollViewSet(BaseBooksySessionGenericViewSet, RetrieveModelMixin, ValidationMixin):
    # pylint: disable=too-many-ancestors
    permission_classes = (IsAuthenticated,)

    def get_object(self):
        return get_object_or_404(Poll, name=self.request.query_params.get('poll_name', ''))

    def get_serializer_class(self):
        serializer_action_mapping = {
            'retrieve': PollSerializer,
            'create': PollAnswerSerializer,
        }
        return serializer_action_mapping.get(self.action, EmptyResponseSerializer)

    def query_serializer_class(self, *args, **kwargs):
        serializer_action_mapping = {
            'retrieve': PollQuerySerializer,
            'create': EmptyRequestSerializer,
        }
        return serializer_action_mapping.get(self.action, EmptyRequestSerializer)(*args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        """Get choices for a poll."""

        query_serializer = self.query_serializer_class(data=self.request.query_params.dict())
        self.validate_serializer(query_serializer)

        return super().retrieve(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        """Vote for a chosen option."""
        serializer = self.get_serializer(data=request.data)
        self.validate_serializer(serializer)

        choice_id = self.request.data['choice_id']
        data = copy(self.request.data)
        data['user_id'] = self.user.id
        Poll.reply(choice_id=choice_id, business_id=kwargs['business_pk'], data=data)

        return Response({}, status=status.HTTP_200_OK)


class CustomerPollViewSet(PollViewSet):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)

    def get_serializer_class(self):
        serializer_action_mapping = {
            'retrieve': PollSerializer,
            'create': CustomerPollAnswerSerializer,
        }
        return serializer_action_mapping.get(self.action, EmptyResponseSerializer)

    def create(self, request, *args, **kwargs):
        """Vote for a chosen option. For customer polls `business_pk` is not available."""
        kwargs['business_pk'] = None
        return super().create(request, *args, **kwargs)


class PollListView(
    ResponseSerializerMixin, BusinessViewValidatorMixin, BaseBooksySessionGenericAPIView
):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)
    permission_classes = (IsAuthenticated,)

    swagger_mark_deprecated = True
    action = 'list'
    serializer_class = PollListRequestSerializer
    response_serializer_class = PollListResponseSerializer

    def post(self, request: Request, *args, **kwargs) -> Response:
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        poll_prefix = request.data.get('group_name', '')
        polls = Poll.objects.filter(name__startswith=poll_prefix)
        response_serializer = self.response_serializer_class(instance=polls, many=True)
        return Response(response_serializer.data, status=status.HTTP_200_OK)


class PollDetailsViewSet(BusinessViewValidatorMixin, BaseBooksySessionGenericViewSet):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)
    permission_classes = (IsAuthenticated,)

    def get_serializer_class(self):
        serializer_action_mapping = {
            'create': PollResponseSerializer,
        }
        return serializer_action_mapping.get(self.action, EmptyResponseSerializer)

    def validate_poll_choices(self, poll: Poll, choice_ids: list[int]):
        sasrt(
            len(choice_ids) <= poll.max_selections,
            status.HTTP_400_BAD_REQUEST,
            [
                {
                    'code': 'exceeded_choices',
                    'type': 'validation',
                    'description': f'Exceeded max selections {Poll.max_selections}',
                }
            ],
        )
        valid_choices = PollChoice.objects.filter(poll=poll).values_list('id', flat=True)
        invalid_choices = [choice for choice in choice_ids if choice not in valid_choices]
        sasrt(
            not any(invalid_choices),
            status.HTTP_400_BAD_REQUEST,
            [
                {
                    'code': 'invalid_choices',
                    'type': 'validation',
                    'description': f'Invalid choices {invalid_choices} for poll {poll.name}',
                }
            ],
        )

    def create(self, request: Request, *args, **kwargs) -> Response:
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        poll = get_object_or_404(Poll, name=serializer.validated_data.get('poll_name'))

        previous_poll_responses = PollResponse.objects.filter(choice__poll=poll, user=self.user)
        if previous_poll_responses.exists():
            previous_poll_responses.delete()

        if choice_ids := serializer.validated_data.get('choice_ids'):
            self.validate_poll_choices(poll, choice_ids)
            check_region = not poll.name == 'registration_number_of_appointments'
            business = self.get_business(check_region=check_region)
            data = copy(self.request.data)
            for choice_id in choice_ids:
                Poll.reply(business_id=business.id, choice_id=choice_id, data=data)

        return Response({}, status=status.HTTP_201_CREATED)


class PollsV3ViewSet(BaseBooksySessionGenericViewSet, RetrieveModelMixin, QuerySerializerMixin):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)
    permission_classes = (IsAuthenticated,)
    serializer_class = PollListResponseSerializer
    query_serializer_class = PollListQuerySerializer
    queryset = Poll.objects.all()
    lookup_field = 'name'
    response_wrapped_with_custom_keyword = 'polls'
    response_only_list_wrapped = True

    def list(self, request, **kwargs):
        query_serializer = self.query_serializer_class(data=self.request.query_params)
        query_serializer.is_valid(raise_exception=True)
        queryset = self.get_queryset()
        if group_name := query_serializer.validated_data.get('group_name'):
            queryset = queryset.filter(name__startswith=group_name)
        serializer = self.get_serializer(instance=queryset, many=True)

        return Response({'polls': serializer.data}, status=status.HTTP_200_OK)
