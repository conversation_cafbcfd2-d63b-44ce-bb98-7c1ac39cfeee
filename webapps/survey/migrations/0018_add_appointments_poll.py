# Generated by Django 4.2.18 on 2025-04-22 10:11

from django.db import migrations


def forwards_func(apps, schema_editor):
    Poll = apps.get_model('survey', 'Poll')
    db_alias = schema_editor.connection.alias

    poll = Poll(
        name='registration_number_of_appointments',
        description='What does your weekly schedule look like?',
        additional_description='This will help us to personalize your experience.',
        max_selections=1,
        response_model_name='RegistrationPollResponse',
        ordered=True,
    )
    poll.save(using=db_alias)
    choices = [
        (
            'just_starting_out',
            {'choice': "I'm just starting out", 'order': 1, 'visible': True},
        ),
        ('14_appointments', {'choice': "1-4 appointments a week", 'order': 2, 'visible': True}),
        ('59_appointments', {'choice': "5-9 appointments a week", 'order': 3, 'visible': True}),
        (
            '1019_appointments',
            {'choice': "10-19 appointments a week", 'order': 4, 'visible': True},
        ),
        ('20+_appointments', {'choice': "20+ appointments a week", 'order': 5, 'visible': True}),
    ]
    PollChoice = apps.get_model('survey', 'PollChoice')
    for choice in choices:
        choice_db = PollChoice(
            poll=poll,
            internal_name=choice[0],
            choice=choice[1]['choice'],
            order=choice[1]['order'],
            visible=choice[1]['visible'],
        )
        choice_db.save(using=db_alias)


def backwards_func(apps, schema_editor):
    Poll = apps.get_model('survey', 'Poll')
    PollChoice = apps.get_model('survey', 'PollChoice')
    RegistrationPollResponse = apps.get_model('survey', 'RegistrationPollResponse')

    db_alias = schema_editor.connection.alias

    poll = Poll.objects.using(db_alias).get(name='registration_number_of_appointments')
    poll_choices = PollChoice.objects.using(db_alias).filter(poll=poll)
    poll_responses = RegistrationPollResponse.objects.filter(choice__in=poll_choices)

    poll_responses.delete()
    poll_choices.delete()
    poll.delete()


class Migration(migrations.Migration):

    dependencies = [
        ('survey', '0017_poll_additional_description_and_more'),
    ]

    operations = [migrations.RunPython(forwards_func, backwards_func)]
