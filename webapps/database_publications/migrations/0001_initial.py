# Generated by Django 2.2.13 on 2020-12-21 10:04
from django.conf import settings
from django.db import migrations

from webapps.stats_and_reports.db_operations import (
    ConnectionDetails,
    CreateExtensionDevDeploymentOnly,
    CreatePgLogicalNodeDevDeploymentOnly,
)

FORWARD_SQL = '''
-- Create replication set and select tables for replication
SELECT pglogical.create_replication_set(
    set_name := 'reports_replication_set'
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'business_businesscustomerinfo',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'business_resource',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'business_servicecategory',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'business_service',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'business_servicevariant',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'booking_appointment',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'booking_subbooking',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'pos_pos',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'pos_paymenttype',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'pos_receipt',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'pos_paymentrow',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'pos_transaction',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'warehouse_commodity',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'voucher_vouchertemplate',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'voucher_voucher',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'pos_transactionrow',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'booking_bookingresource',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'auth_user',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'user_user',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'pos_transactiontip',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'business_tag',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'business_businesscustomerinfotag',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'wait_list_waitlist',
    synchronize_data := FALSE
);
SELECT pglogical.replication_set_add_table(
    set_name := 'reports_replication_set',
    relation := 'business_business',
    synchronize_data := FALSE
);
'''

ROLLBACK_SQL = '''
SELECT pglogical.drop_replication_set(
    set_name := 'reports_replication_set'
);
'''


def get_provider_node_connection_details():
    default_db_conf = settings.DATABASES['default']
    return ConnectionDetails(
        host=default_db_conf['HOST'],
        port=default_db_conf['PORT'],
        user=default_db_conf['USER'],
        password=default_db_conf['PASSWORD'],
        dbname=default_db_conf['NAME'],
    )


class Migration(migrations.Migration):
    dependencies = [
        ('booking', '0107_frontdesk_bookingsource'),
    ]

    operations = [
        CreateExtensionDevDeploymentOnly('pglogical'),
        CreatePgLogicalNodeDevDeploymentOnly(
            node_name='provider',
            conn_details=get_provider_node_connection_details(),
        ),
        migrations.RunSQL(sql=FORWARD_SQL, reverse_sql=ROLLBACK_SQL),
    ]
