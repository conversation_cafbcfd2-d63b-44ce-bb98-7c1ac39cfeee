import pytest
from django.test import override_settings
from mock.mock import patch

from lib.locks import InvoicingLock, SyncWithNavisionLock
from webapps.business.models import Business
from webapps.navision.baker_recipes import (
    navision_integration_enabled_recipe,
    navision_settings_recipe,
)
from webapps.navision.decorators import (
    celery_one_run_at_time,
    navision_enabled,
    navision_invoicing_enabled,
    navision_sync_invoices_enabled,
    prevent_double_sync_with_navision,
)
from webapps.navision.models import Invoice


# TODO split into smaller files


@pytest.mark.django_db
def test_navision_enabled_function_executes_when_integration_enabled():
    @navision_enabled
    def navision_enabled_function():
        raise ValueError('Boom!')

    navision_settings_recipe.make(navision_integration_enabled=True)
    with pytest.raises(ValueError):
        navision_enabled_function()


@pytest.mark.django_db
def test_navision_enabled_function_do_nothing_when_integration_disabled():
    @navision_enabled
    def navision_enabled_function():
        raise ValueError('Boom!')

    navision_settings_recipe.make()
    navision_enabled_function()


@pytest.mark.django_db
@pytest.mark.parametrize("sync_enabled", [False, True])
@pytest.mark.parametrize("invoicing_enabled", [False, True])
def test_sync_with_navision_ignored_when_integration_disabled(invoicing_enabled, sync_enabled):
    @navision_sync_invoices_enabled()
    def navision_invoicing_enabled_function():
        raise ValueError('Boom!')

    navision_settings_recipe.make(
        navision_integration_enabled=False,
        navision_invoicing_enabled=invoicing_enabled,
        navision_sync_invoices_enabled=sync_enabled,
    )

    navision_invoicing_enabled_function()


@pytest.mark.django_db
@pytest.mark.parametrize("invoicing_enabled", [False, True])
def test_sync_with_navision_executed_when_integration_enabled(invoicing_enabled):
    @navision_sync_invoices_enabled()
    def navision_invoicing_enabled_function():
        raise ValueError('Boom!')

    navision_settings_recipe.make(
        navision_integration_enabled=True,
        navision_invoicing_enabled=invoicing_enabled,
        navision_sync_invoices_enabled=True,
    )
    with pytest.raises(ValueError):
        navision_invoicing_enabled_function()


@pytest.mark.django_db
@pytest.mark.parametrize("invoicing_enabled", [False, True])
def test_sync_with_navision_ignored_when_sync_disabled(invoicing_enabled):
    @navision_sync_invoices_enabled()
    def navision_invoicing_enabled_function():
        raise ValueError('Boom!')

    navision_settings_recipe.make(
        navision_integration_enabled=True,
        navision_invoicing_enabled=invoicing_enabled,
        navision_sync_invoices_enabled=False,
    )
    navision_invoicing_enabled_function()


@pytest.mark.django_db
@pytest.mark.parametrize(
    "payment_method",
    [
        Business.PaymentSource.OFFLINE,
        Business.PaymentSource.BRAINTREE,
    ],
)
def test_navision_invoicing_enabled_function_do_nothing_when_payment_method_is_not_supported(
    payment_method,
):
    @navision_invoicing_enabled(payment_method)
    def navision_invoicing_enabled_function():
        raise ValueError('Boom!')

    navision_integration_enabled_recipe.make()

    supported_payment_methods = {
        Business.PaymentSource.OFFLINE: {},
        Business.PaymentSource.BRAINTREE: {},
        Business.PaymentSource.BRAINTREE_BILLING: {},
    }

    supported_payment_methods.pop(payment_method)

    with override_settings(SUPPORTED_INVOICE_PAYMENT_METHODS=supported_payment_methods):
        navision_invoicing_enabled_function()


@pytest.mark.django_db
@pytest.mark.parametrize(
    "payment_method",
    [
        Business.PaymentSource.OFFLINE,
        Business.PaymentSource.BRAINTREE,
        Business.PaymentSource.BRAINTREE_BILLING,
    ],
)
def test_navision_invoicing_enabled_function_executes_when_payment_method_is_supported(
    payment_method,
):
    @navision_invoicing_enabled(payment_method)
    def navision_invoicing_enabled_function():
        raise ValueError('Boom!')

    navision_integration_enabled_recipe.make()

    supported_payment_methods = {
        payment_method: {},
    }

    with override_settings(SUPPORTED_INVOICE_PAYMENT_METHODS=supported_payment_methods):
        with pytest.raises(ValueError):
            navision_invoicing_enabled_function()


@pytest.mark.django_db
@pytest.mark.parametrize(
    "payment_method",
    [
        Business.PaymentSource.OFFLINE,
        Business.PaymentSource.BRAINTREE,
        Business.PaymentSource.BRAINTREE_BILLING,
    ],
)
@pytest.mark.parametrize(
    "service",
    [
        Invoice.Service.SAAS,
        Invoice.Service.BOOST,
    ],
)
def test_navision_invoicing_enabled_does_not_execute_when_service_is_locked(
    payment_method,
    service,
):
    @navision_invoicing_enabled(payment_method, service)
    def navision_invoicing_enabled_function():
        raise ValueError('Boom!')

    navision_integration_enabled_recipe.make()

    supported_payment_methods = {
        Business.PaymentSource.OFFLINE: {},
        Business.PaymentSource.BRAINTREE: {},
        Business.PaymentSource.BRAINTREE_BILLING: {},
        payment_method: {
            'SaaS',
            'Boost',
        },
    }

    supported_payment_methods[payment_method].remove(service)

    with override_settings(SUPPORTED_INVOICE_PAYMENT_METHODS=supported_payment_methods):
        navision_invoicing_enabled_function()


@pytest.mark.django_db
@pytest.mark.parametrize(
    "payment_method",
    [
        Business.PaymentSource.OFFLINE,
        Business.PaymentSource.BRAINTREE,
        Business.PaymentSource.BRAINTREE_BILLING,
    ],
)
@pytest.mark.parametrize(
    "service",
    [
        Invoice.Service.SAAS,
        Invoice.Service.BOOST,
    ],
)
def test_navision_invoicing_enabled_execute_when_service_is_unlocked(
    payment_method,
    service,
):
    @navision_invoicing_enabled(payment_method, service)
    def navision_invoicing_enabled_function():
        raise ValueError('Boom!')

    navision_integration_enabled_recipe.make()

    supported_payment_methods = {
        payment_method: {
            service,
        }
    }

    with override_settings(SUPPORTED_INVOICE_PAYMENT_METHODS=supported_payment_methods):
        with pytest.raises(ValueError):
            navision_invoicing_enabled_function()


@patch('webapps.navision.decorators.logging.Logger.warning')
def test_decorator_locked_task(log_msg):
    @celery_one_run_at_time
    def navision_invoicing_lock_wrapper():
        raise ValueError('Boom!')

    lock = InvoicingLock.lock('navision_invoicing_lock_wrapper')
    navision_invoicing_lock_wrapper()
    InvoicingLock.try_to_unlock(lock)
    log_msg.assert_called_once_with(
        'Skip %s (task is already running)', 'navision_invoicing_lock_wrapper'
    )


def test_decorator_unlocked_task():
    @celery_one_run_at_time
    def navision_invoicing_lock_wrapper():
        raise ValueError('Boom!')

    with pytest.raises(ValueError):
        navision_invoicing_lock_wrapper()


@patch('webapps.navision.decorators.logging.Logger.warning')
def test_prevent_double_sync_with_navision_locked_task(log_msg):
    @prevent_double_sync_with_navision
    def navision_invoicing_lock_wrapper(invoice_id):
        raise ValueError(f'{invoice_id} Boom!')

    temp_invoice_id = 1

    lock = SyncWithNavisionLock.lock(temp_invoice_id)

    navision_invoicing_lock_wrapper(temp_invoice_id)

    SyncWithNavisionLock.try_to_unlock(lock)

    log_msg.assert_called_once_with('Skip invoice id %s (already scheduled)', temp_invoice_id)


def test_prevent_double_sync_with_navision_unlocked_task():
    @prevent_double_sync_with_navision
    def navision_invoicing_lock_wrapper(invoice_id):
        raise ValueError(f'{invoice_id} Boom!')

    temp_invoice_id = 1

    lock = SyncWithNavisionLock.lock(temp_invoice_id + 1)  # lock on another invoice_id

    with pytest.raises(ValueError) as err:
        navision_invoicing_lock_wrapper(temp_invoice_id)

    SyncWithNavisionLock.try_to_unlock(lock)

    assert err.value.args[0] == '1 Boom!'
