from django.test import TestCase
from google.protobuf.json_format import MessageToDict
from model_bakery import baker

from webapps.navision.messages.invoice_details import InvoiceDetailsEventMessage


class InvoiceDetailsEventMessageTestCase(TestCase):
    def setUp(self):
        self.business = baker.make('business.Business')
        self.buyer = baker.make('purchase.SubscriptionBuyer')

    def test_buyer_with_optional_fields(self):
        request_data = {
            'form_id': 'TaxIDIfVatRegistered',
            'entity_name': 'My company',
            'address_details': 'Address 1',
            'city': 'City',
            'zipcode': '90111',
            'invoice_email': '<EMAIL>',
            'tax_group': 'Some group',
            'vat_registered': True,
            'tax_id': '12345',
        }
        invoice_details_event_data = {
            'id': self.buyer.id,
            'business_id': self.business.id,
            'tax_id': None,
            **request_data,
        }
        message_data = InvoiceDetailsEventMessage(invoice_details_event_data).message
        self.assertDictEqual(
            MessageToDict(message_data),
            {
                'id': self.buyer.id,
                'vatRegistered': True,
                'taxId': '12345',
                'taxGroup': 'Some group',
                'businessId': self.business.id,
                'entityName': request_data['entity_name'],
                'addressDetails': request_data['address_details'],
                'city': request_data['city'],
                'zipcode': request_data['zipcode'],
                'invoiceEmail': request_data['invoice_email'],
                'formId': 'TaxIDIfVatRegistered',
            },
        )

    def test_buyer_without_optional_fields(self):
        request_data = {
            'form_id': 'TaxIDIfVatRegistered',
            'entity_name': 'My company',
            'address_details': 'Address 1',
            'city': 'City',
            'zipcode': '90111',
            'invoice_email': '<EMAIL>',
            'vat_registered': False,
            'tax_group': None,
            'tax_id': None,
        }
        invoice_details_event_data = {
            'id': None,
            'business_id': self.business.id,
            'tax_id': '1223',  # this will be overwritten by None from request data
            **request_data,
        }
        message_data = InvoiceDetailsEventMessage(invoice_details_event_data).message
        self.assertDictEqual(
            MessageToDict(message_data),
            {
                'businessId': self.business.id,
                'entityName': request_data['entity_name'],
                'addressDetails': request_data['address_details'],
                'city': request_data['city'],
                'zipcode': request_data['zipcode'],
                'invoiceEmail': request_data['invoice_email'],
                'vatRegistered': False,
                'formId': 'TaxIDIfVatRegistered',
            },
        )
