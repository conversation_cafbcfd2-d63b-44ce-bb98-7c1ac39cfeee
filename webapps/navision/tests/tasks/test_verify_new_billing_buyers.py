from datetime import datetime
from unittest.mock import patch

from django.test import TestCase
from freezegun import freeze_time
from model_bakery import baker

from lib.tools import tznow
from webapps.business.models import Business
from webapps.navision.baker_recipes import navision_integration_enabled_recipe
from webapps.navision.tasks.subscription_buyer import verify_active_providers_task
from webapps.purchase.models import SubscriptionBuyer


@freeze_time(datetime(2024, 10, 1))
class TestVerifyActiveProvidersTask(TestCase):
    @classmethod
    def setUpTestData(cls) -> None:
        super().setUpTestData()
        navision_integration_enabled_recipe.make()

    @patch('webapps.navision.processes.active_businesses')
    @patch('webapps.navision.processes.stripe_terminal_owners')
    def test_verify_buyer_if_provider_active(
        self,
        stripe_terminal_owners_mock,
        active_business_mock,
    ):
        buyer = baker.make(SubscriptionBuyer, is_verified=False)
        business = baker.make(Business, buyer=buyer)
        active_business_mock.return_value = Business.objects.filter(id=business.id)
        stripe_terminal_owners_mock.return_value = Business.objects.none()

        verify_active_providers_task()

        buyer.refresh_from_db()

        self.assertTrue(buyer.is_verified)
        self.assertEqual(buyer.verified_at, tznow())
        self.assertEqual(buyer.verified_by.email, '<EMAIL>')

    @patch('webapps.navision.processes.active_businesses')
    @patch('webapps.navision.processes.stripe_terminal_owners')
    def test_verify_buyer_if_provider_not_active(
        self,
        _stripe_terminal_owners_mock,
        active_business_mock,
    ):
        buyer = baker.make(SubscriptionBuyer, is_verified=False)
        _business = baker.make(Business, buyer=buyer)
        active_business_mock.return_value = Business.objects.none()

        verify_active_providers_task()

        buyer.refresh_from_db()

        self.assertFalse(buyer.is_verified)
        self.assertIsNone(buyer.verified_at)
        self.assertIsNone(buyer.verified_at)
