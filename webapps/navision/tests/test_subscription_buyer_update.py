from unittest.mock import MagicMock, patch

from django.conf import settings
from django.test import TestCase, override_settings
from parameterized import parameterized

from lib.tools import tznow
from webapps.business.models import Business
from webapps.navision.baker_recipes import navision_integration_enabled_recipe
from webapps.navision.models import Merchant
from webapps.navision.ports import MerchantNavisionAPIData, MerchantData
from webapps.navision.processes import update_subscription_buyer
from webapps.navision.tasks.subscription_buyer import update_buyer_with_business_data_task
from webapps.purchase.models import InvoiceAddress, SubscriptionBuyer
from webapps.structure.baker_recipes import bake_region_graphs
from webapps.structure.models import Region, get_parents_of_region
from webapps.user.models import User
from webapps.user.tools import get_system_user


def create_or_update_merchant_mock(merchant_data: MerchantNavisionAPIData):
    now = tznow()
    return {
        'merchant_id': merchant_data.merchant_id,
        'created': str(now),
        'updated': str(now),
        'deleted': str(now.replace(year=1)),
        'tax_id': merchant_data.tax_id,
        'email': merchant_data.invoice_emails[0],
        'business_name': merchant_data.entity_name,
        'Billing_Account_No': '88249010283548900000000009',
        'accounting_group': '',
        'price_with_vat': False,
        'billingAddress': {
            'created': str(now),
            'updated': str(now),
            'deleted': str(now.replace(year=1)),
            'address_details1': merchant_data.address_details1,
            'city': merchant_data.city,
            'zip_code': merchant_data.zip_code,
            'state': merchant_data.state,
            'country_code': merchant_data.country_code.upper(),
        },
    }


def create_or_update_merchant_fail_mock():
    raise Exception('Failed to insert merchant')


class _UpdateSubscriptionBuyerTest(TestCase):
    # pylint: disable=too-many-instance-attributes
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        navision_integration_enabled_recipe.make()

    @patch(
        'webapps.navision.api_clients.navision_v1.NavisionMerchantAPIClient.'
        'create_or_update_merchant',
        MagicMock(side_effect=create_or_update_merchant_mock),
    )
    def setUp(self) -> None:
        self.zip_42200 = Region.objects.create(type=Region.Type.ZIP, name='42200')

        self.nevada = Region.objects.create(type=Region.Type.STATE, name='Nevada', abbrev='NV')

        self.zip_48200 = Region.objects.create(
            type=Region.Type.ZIP,
            name='48200',
        )

        bake_region_graphs(self.nevada, self.zip_48200)

        self.old_addr1 = "Old Addr1"
        self.old_addr2 = "Old Addr2"
        self.invoice_address_old_address = "Old Addr1 Old Addr2"
        self.old_zip = self.zip_42200
        self.old_state = None
        self.old_city = "Old City"

        self.invoice_address = InvoiceAddress.objects.create(
            address_details1=self.invoice_address_old_address,
            zipcode=self.old_zip,
            zipcode_textual=self.old_zip.name,
            state=self.old_state,
            city=self.old_city,
        )

        self.signer = get_system_user('system', '<EMAIL>')

        self.old_entity_name = "Old entity name"
        self.old_email = "<EMAIL>"

        self.merchant = Merchant.create_or_update(
            MerchantData(
                emails=self.old_email,
                tax_id=None,
                address_details1=self.invoice_address_old_address,
                city=self.old_city,
                zip_code=self.old_zip.name,
                state=None,
                entity_name=self.old_entity_name,
                country_code=settings.API_COUNTRY,
            )
        )
        self.buyer: SubscriptionBuyer = SubscriptionBuyer.objects.create(
            entity_name=self.old_entity_name,
            invoice_email=self.old_email,
            invoice_address=self.invoice_address,
            merchant=self.merchant,
            active=True,
            is_verified=True,
            verified_at=tznow(),
            verified_by=self.signer,
        )

        owner: User = User.objects.create_user(
            "username",
            email=self.old_email,
            password=None,
            first_name='User first name',
            last_name='User last name',
        )

        self.business: Business = Business.objects.create(
            name=self.old_entity_name,
            status=Business.Status.PAID,
            address=self.old_addr1,
            address2=self.old_addr2,
            city=self.old_city,
            zipcode=self.old_zip.name,
            region=self.old_zip,
            buyer=self.buyer,
            owner=owner,
        )

        self.buyer_history_count = self.buyer.history.count()
        self.address_history_count = self.buyer.invoice_address.history.count()
        self.merchant_history_count = self.buyer.merchant.history.count()

        get_parents_of_region.cache_clear()

    def tearDown(self) -> None:
        super().tearDown()
        get_parents_of_region.cache_clear()

    def _set_new_zipcode(self, save=True):
        self.business.zipcode = self.zip_48200.name
        self.business.region_id = self.zip_48200.id

        if save:
            self.business.save(
                update_fields=[
                    'zipcode',
                    'region_id',
                ]
            )


@override_settings(SAVE_HISTORY=True)
class TestUpdateSubscriptionBuyer(_UpdateSubscriptionBuyerTest):
    def test_unchanged_buyer_is_not_updated(self):
        buyer_last_updated = self.buyer.updated
        merchant_last_updated = self.buyer.updated

        update_subscription_buyer(self.buyer)

        self.assertEqual(self.buyer.history.count(), self.buyer_history_count)
        self.assertEqual(
            self.buyer.updated,
            buyer_last_updated,
        )
        self.assertGreater(
            self.buyer.merchant.updated,
            merchant_last_updated,
        )

    def test_merchant_is_updated_critical_fields(self):
        self._set_new_zipcode()

        update_subscription_buyer(self.buyer)
        self.buyer.refresh_from_db()

        merchant = self.buyer.merchant
        merchant.refresh_from_db()

        self.assertEqual(self.buyer.merchant_id, merchant.id)
        self.assertEqual(self.buyer.invoice_address.zipcode, self.zip_48200)
        self.assertEqual(self.buyer.invoice_address.zipcode_textual, self.zip_48200.name)
        self.assertEqual(self.buyer.invoice_address.state, self.nevada)

        self.assertEqual(merchant.country_code, settings.API_COUNTRY)
        self.assertEqual(merchant.zip_code, self.zip_48200.name)
        self.assertEqual(merchant.state, self.nevada.abbrev)
        self.assertGreater(merchant.history.count(), self.merchant_history_count)

    @parameterized.expand(
        [
            (False, False),
            (True, False),
            (False, True),
            (True, True),
        ]
    )
    def test_merchant_is_updated_optional_fields(self, zipcode_updated, fields_updated):
        new_entity_name = "New Entity Name"
        new_email = "<EMAIL>"
        new_address1 = "New Addr1"
        new_address2 = "New Addr2"
        expected_address = "New Addr1 New Addr2"

        if zipcode_updated:
            self._set_new_zipcode()

        if fields_updated:
            self.business.name = new_entity_name
            self.business.address = new_address1
            self.business.address2 = new_address2
            self.business.owner.email = new_email
            self.business.save(update_fields=['name', 'address', 'address2'])
            self.business.owner.save(update_fields=['email'])

        update_subscription_buyer(self.buyer)
        self.buyer.refresh_from_db()

        merchant = self.buyer.merchant
        merchant.refresh_from_db()

        if zipcode_updated and fields_updated:
            self.assertEqual(self.buyer.entity_name, new_entity_name)
            self.assertEqual(self.buyer.invoice_email, new_email)
            self.assertEqual(self.buyer.invoice_address.address_details1, expected_address)
            self.assertEqual(merchant.entity_name, new_entity_name)
            self.assertEqual(merchant.invoice_emails, [new_email])
            self.assertEqual(merchant.address_details1, expected_address)
        else:
            self.assertEqual(self.buyer.entity_name, self.old_entity_name)
            self.assertEqual(self.buyer.invoice_email, self.old_email)
            self.assertEqual(
                self.buyer.invoice_address.address_details1, self.invoice_address_old_address
            )
            self.assertEqual(merchant.entity_name, self.old_entity_name)
            self.assertEqual(merchant.invoice_emails, [self.old_email])
            self.assertEqual(self.merchant.address_details1, self.invoice_address_old_address)

        if zipcode_updated:
            self.assertGreater(merchant.history.count(), self.merchant_history_count)
        else:
            self.assertEqual(merchant.history.count(), self.merchant_history_count)

    def test_merchant_is_updated_optional_fields__deleted_zipcode(self):
        self.business.zipcode = None
        self.business.city = ''
        self.business.region = None
        self.business.save(update_fields=['zipcode', 'city', 'region'])

        update_subscription_buyer(self.buyer)
        self.buyer.refresh_from_db()

        merchant = self.buyer.merchant
        merchant.refresh_from_db()

        self.assertEqual(self.buyer.merchant_id, merchant.id)
        self.assertEqual(self.buyer.invoice_address.zipcode, self.zip_42200)
        self.assertEqual(self.buyer.invoice_address.zipcode_textual, self.zip_42200.name)
        self.assertIsNone(self.buyer.invoice_address.state)
        self.assertEqual(self.buyer.invoice_address.city, self.old_city)
        self.assertEqual(self.buyer.history.count(), self.buyer_history_count)

        self.assertEqual(merchant.country_code, settings.API_COUNTRY)
        self.assertEqual(merchant.zip_code, self.zip_42200.name)
        self.assertIsNone(merchant.state)
        self.assertEqual(merchant.city, self.old_city)
        self.assertEqual(merchant.history.count(), self.merchant_history_count)

    def test_buyer_history_model_update(self):
        new_entity_name = 'New Entity Name'
        hist_count = self.buyer_history_count

        self._set_new_zipcode()

        self.business.name = new_entity_name
        self.business.save(update_fields=['name'])

        update_subscription_buyer(self.buyer)
        self.buyer.refresh_from_db()

        self.assertEqual(self.buyer.entity_name, new_entity_name)
        self.assertGreater(self.buyer.history.count(), hist_count)


class TestUpdateSubscriptionBuyerTask(_UpdateSubscriptionBuyerTest):
    def test_merchant_sync_does_nothing_for_unchanged_businesses(self):
        buyer_last_updated = self.buyer.updated

        update_buyer_with_business_data_task()

        self.buyer.refresh_from_db()

        self.assertEqual(self.buyer.updated, buyer_last_updated)

        self.merchant.refresh_from_db()

        self.assertEqual(self.merchant.invoice_emails, [self.old_email])
        self.assertEqual(self.merchant.address_details1, self.invoice_address_old_address)
        self.assertEqual(self.merchant.zip_code, self.old_zip.name)
        self.assertIsNone(self.merchant.state)

    def test_merchant_sync_doesnt_run_on_in_unauthorized_countries(self):
        self.business.owner.email = '<EMAIL>'
        self.business.region = None
        self.business.zipcode = self.zip_48200.name
        self.business.save(update_fields=['region', 'zipcode'])
        self.business.owner.save(update_fields=['email'])

        with override_settings(NAVISION_TAX_CALCULATED_FROM_ZIPCODE=False):
            update_buyer_with_business_data_task()
            self.merchant.refresh_from_db()
            self.assertEqual(self.merchant.invoice_emails, [self.old_email])

        with override_settings(NAVISION_TAX_CALCULATED_FROM_ZIPCODE=True):
            new_email = '<EMAIL>'
            new_zipcode = self.zip_48200.name
            self.business.owner.email = new_email
            self.business.region = None
            self.business.zipcode = new_zipcode
            self.business.save(update_fields=['region', 'zipcode'])
            self.business.owner.save(update_fields=['email'])

            update_buyer_with_business_data_task()

            self.merchant.refresh_from_db()

            self.assertEqual(self.merchant.zip_code, new_zipcode)
            self.assertEqual(self.merchant.invoice_emails, [new_email])
