# pylint: disable=no-name-in-module
from booksy_proto_invoicing.pubsub.v1.invoice_details_pb2 import InvoiceDetailsEvent
from django_socio_grpc.proto_serializers import ProtoSerializer
from rest_framework import serializers

from webapps.pubsub.base import TopicOptions
from webapps.pubsub.message import Message


class InvoiceDetailsEventSerializer(ProtoSerializer):
    class Meta:
        proto_class = InvoiceDetailsEvent

    id = serializers.IntegerField(required=False)
    business_id = serializers.IntegerField()
    vat_registered = serializers.BooleanField(required=False)
    tax_id = serializers.CharField(required=False)
    entity_name = serializers.CharField()
    address_details = serializers.CharField()
    city = serializers.CharField()
    zipcode = serializers.CharField()
    tax_group = serializers.CharField(required=False)
    invoice_email = serializers.CharField()
    form_id = serializers.CharField()


class InvoiceDetailsEventMessage(Message):
    topic_proto_class = InvoiceDetailsEvent
    topic_options = TopicOptions(by_country=True)
    serializer = InvoiceDetailsEventSerializer
