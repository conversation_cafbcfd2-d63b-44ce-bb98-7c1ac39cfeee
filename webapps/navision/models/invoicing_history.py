import logging
from decimal import Decimal
import typing as t

import datetime
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models import Case, DecimalField, F, Sum, Value, When, QuerySet
from django.db.models.functions import Coalesce, Greatest

from lib.cache import lru_booksy_cache
from lib.models import ArchiveModel, HistoryModel, AutoAddHistoryModel
from webapps.billing.models.managers import AutoUpdateAndAutoAddHistoryManager
from webapps.business.models import Business
from webapps.navision.enums import (
    InvoicingErrorStatus,
    InvoicingErrorCategory,
    InvoicePaymentSourceType,
)
from webapps.navision.models.invoice import Invoice

_logger = logging.getLogger('booksy.navision')


class InvoicingSummary(ArchiveModel):
    id: int
    invoicing_date = models.DateTimeField()
    invoicing_target = models.DateTimeField(
        null=True,
        blank=True,
    )
    operator = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        db_constraint=False,
        on_delete=models.DO_NOTHING,
        help_text='User who performed the invoicing action',
    )

    service = models.CharField(
        max_length=20,
        choices=Invoice.Service.choices(),
        null=True,
    )

    source = models.CharField(
        max_length=10,
        choices=InvoicePaymentSourceType.choices(),
        null=True,
    )

    class Meta:
        verbose_name_plural = 'Invoicing summaries'

    def __str__(self):
        email = self.operator.email if self.operator else None
        return f'{self.invoicing_date} Operator: {email}'

    @lru_booksy_cache(timeout=30 * 60, skip_in_pytest=True)
    def calculate_totals(self):
        totals = (
            BusinessInvoiceSummary.objects.filter(
                pk__in=self.business_invoices.distinct('invoice').values_list('pk', flat=True)
            )
            .annotate(
                gross_times_quantity=Case(
                    When(
                        invoice__items__service=Invoice.Service.DISCOUNT,
                        then=Coalesce(F('invoice__items__base_gross_value'), Value(0)) * Value(-1),
                    ),
                    default=Greatest(
                        Coalesce(F('invoice__items__base_gross_value'), Value(0))
                        - Coalesce(F('invoice__items__discount_gross_value'), Value(0)),
                        Value(0),
                        output_field=DecimalField(),
                    )
                    * F('invoice__items__quantity'),
                    output_field=DecimalField(),
                ),
                tax_times_quantity=F('invoice__items__base_tax_value')
                * F('invoice__items__quantity'),
            )
            .aggregate(
                invoice_items_gross_total=Coalesce(
                    Sum('gross_times_quantity'), Decimal(0), output_field=DecimalField()
                ),
                invoice_items_tax_total=Coalesce(
                    Sum('tax_times_quantity'), Decimal(0), output_field=DecimalField()
                ),
            )
        )

        gross_total = totals.get('invoice_items_gross_total')
        tax_total = totals.get('invoice_items_tax_total')
        net_total = gross_total - tax_total

        return gross_total, tax_total, net_total

    @property
    def invoice_items_gross_total(self):
        return self.calculate_totals()[0]

    @property
    def invoice_items_tax_total(self):
        return self.calculate_totals()[1]

    @property
    def invoice_items_net_total(self):
        return self.calculate_totals()[2]


class SubscriptionInvoiceSummary(ArchiveModel):
    id: int
    report = models.ForeignKey(
        InvoicingSummary,
        on_delete=models.CASCADE,
        related_name='subscription_invoices',
    )

    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='subscription_summary',
    )

    subscription = models.ForeignKey(
        'purchase.Subscription',
        on_delete=models.CASCADE,
        verbose_name='Invoiced Subscription',
        related_name='invoicing_summaries',
    )


class BusinessInvoiceSummary(ArchiveModel):
    id: int
    report = models.ForeignKey(
        InvoicingSummary,
        on_delete=models.CASCADE,
        related_name='business_invoices',
    )

    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='business_summary',
    )
    business = models.ForeignKey(
        Business,
        on_delete=models.CASCADE,
        related_name='invoicing_summaries',
    )


class InvoicingError(AutoAddHistoryModel, ArchiveModel):
    """
    Exceptions and Errors raised during invoicing process
    """

    objects = AutoUpdateAndAutoAddHistoryManager()

    id: int
    report = models.ForeignKey(
        InvoicingSummary,
        on_delete=models.CASCADE,
        related_name='invoicing_errors',
    )
    service = models.CharField(
        max_length=20,
        choices=Invoice.Service.choices(),
    )
    source = models.CharField(
        max_length=10,
        choices=InvoicePaymentSourceType.choices(),
        null=True,
    )
    invoicing_target = models.DateTimeField(
        null=True,
        blank=True,
    )
    buyer = models.ForeignKey(
        'purchase.SubscriptionBuyer',
        on_delete=models.CASCADE,
        related_name='invoicing_errors',
        null=True,
        blank=True,
    )
    category = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        choices=InvoicingErrorCategory.choices(),
    )
    message = models.TextField(
        null=True,
        blank=True,
    )
    error = models.TextField()
    status = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        choices=InvoicingErrorStatus.choices(),
    )
    is_fixed = models.BooleanField(default=False)

    content_type = models.ForeignKey(ContentType, blank=True, null=True, on_delete=models.SET_NULL)
    object_id = models.PositiveIntegerField(blank=True, null=True)
    invoiced_object = GenericForeignKey('content_type', 'object_id')


class InvoicingErrorHistory(HistoryModel):
    model = models.ForeignKey(
        InvoicingError,
        on_delete=models.DO_NOTHING,
        related_name='history',
    )

    class Meta:
        verbose_name = 'Invoicing error history'
        verbose_name_plural = 'Invoicing error history'

    @classmethod
    def extract_vars_from_instances(  # pylint: disable=unnecessary-dict-index-lookup
        cls,
        instances: t.Union[QuerySet, list[int]],
        fields: t.Optional[t.Iterable] = None,
    ) -> dict[int, dict]:
        _vars = super().extract_vars_from_instances(instances, fields)
        for object_id, data in _vars.items():
            for field_name in data.keys():
                if isinstance(_vars[object_id][field_name], datetime.datetime):
                    _vars[object_id][field_name] = str(_vars[object_id][field_name])
        return _vars

    @classmethod
    def extract_vars_from_instance(
        cls,
        obj: models.Model,
        fields: t.Optional[t.Iterable] = None,
    ) -> dict:
        _vars = super().extract_vars_from_instance(obj, fields)
        for key in _vars.keys():
            if isinstance(_vars[key], datetime.datetime):
                _vars[key] = str(_vars[key])
        return _vars
