import logging
import typing as t
from dataclasses import asdict
from decimal import Decimal

from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.fields import ArrayField
from django.db import models, transaction
from django.db.models import Sum, QuerySet, Case, When, F, Value, FloatField
from django.db.models.functions import Coalesce, Greatest

from lib.cache import lru_booksy_cache


from lib.enums import StrChoicesEnum
from lib.models import ArchiveModel, AutoUpdateManager
from settings.storage import GCPNavisionInvoiceStorage
from webapps.navision.enums import InvoiceService, InvoicePaymentSource
from webapps.navision.models.tax_rate import TaxRate
from webapps.navision.models.merchant import Merchant
from webapps.navision.ports import InvoiceData

_logger = logging.getLogger('booksy.navision')


class ApprovedInvoicesManager(AutoUpdateManager):
    def get_queryset(self) -> QuerySet:
        return super().get_queryset().filter(approved=True)


class Invoice(ArchiveModel):
    objects = AutoUpdateManager()
    approved_objects = ApprovedInvoicesManager()

    class Meta:
        verbose_name = 'Invoice'
        verbose_name_plural = 'Invoices'
        ordering = ('-created',)

    class Status(StrChoicesEnum):
        INIT = 'init', 'init'
        SENT = 'sent', 'sent'
        ERROR = 'error', 'error'

    class DocumentType(StrChoicesEnum):
        VAT = 'VAT', 'VAT'

    Service = InvoiceService

    id: int

    # region To Navision
    merchant = models.ForeignKey(Merchant, on_delete=models.PROTECT, related_name='invoices')
    document_type = models.CharField(
        max_length=50,
        choices=DocumentType.choices(),
        default=DocumentType.VAT,
    )
    service = models.CharField(
        max_length=20,
        choices=Service.choices(),
        null=True,
    )
    bank_code = models.CharField(
        max_length=20,
        null=True,
    )

    business_id = models.PositiveIntegerField(
        blank=True,
        null=True,
    )

    business_name = models.CharField(
        max_length=80,
        null=True,
    )
    payment_due_date = models.DateField()  # nav_offline
    source = models.CharField(
        max_length=50,
        choices=InvoicePaymentSource.choices(),
    )
    sales_date = models.DateField()
    invoice_date = models.DateField()
    currency = models.CharField(max_length=3)
    invoice_emails = ArrayField(
        models.EmailField(),
        size=5,
        blank=True,
        null=True,
    )
    invoice_note = models.CharField(max_length=250, blank=True, null=True)
    # endregion

    # region Returned from Navision
    navision_id = models.CharField(max_length=50, blank=True, null=True)
    invoice_number = models.CharField(max_length=50, blank=True, null=True)
    invoice_file = models.FileField(
        null=True,
        blank=True,
        storage=GCPNavisionInvoiceStorage(),
    )
    invoice_file_exists = models.BooleanField(
        default=False,
    )
    # endregion

    aggregation_uuid = models.UUIDField(
        null=True,
        db_index=True,
    )

    # region Internal statuses
    status = models.CharField(
        max_length=50,
        choices=Status.choices(),
        default=Status.INIT,
    )
    approved = models.BooleanField(
        null=True,
        default=False,
    )
    # endregion

    # Datetime when the task was submitted
    queued_at = models.DateTimeField(null=True, blank=True, default=None)
    # Datetime of creation or update `Invoice` in Navision
    synced_at = models.DateTimeField(null=True, blank=True, default=None)

    is_production = models.BooleanField(null=True, default=None)

    def __str__(self):
        return f'{self.service}: {self.source} from {self.invoice_date} (ID {self.id})'

    @property
    def booksy_id(self) -> str:
        """
        Invoice identifier for navison, max 30 characters
        <2-8 prefix>-<2 country code>-<4 year>-<2 month>-<10 id>
        """

        prefix = settings.NAVISION_PREFIX
        country = settings.API_COUNTRY
        year = self.invoice_date.year
        month = self.invoice_date.month

        identifier = f'{prefix}-{country}-{year:04d}-{month:02d}-{self.id:010d}'

        return identifier.upper()

    @property
    def invoice_gcs_file_path(self) -> t.Optional[str]:
        if self.invoice_number is not None and settings.NAVISION_GCP_COUNTRY_CODE is not None:
            # pylint: disable=consider-using-f-string
            return (
                '{country_code}/{year:04d}/{month:02d}/{day:02d}/{merchant_id}/'
                '{invoice_number}.pdf'.format(
                    country_code=settings.NAVISION_GCP_COUNTRY_CODE,
                    merchant_id=self.merchant.merchant_id,
                    year=self.invoice_date.year,
                    month=self.invoice_date.month,
                    day=self.invoice_date.day,
                    invoice_number=self.invoice_number.replace('/', ''),
                )
            )

    @property
    def final_gross_amount(self) -> Decimal:
        return self.items.all().aggregate(Sum('final_gross_value'))[
            'final_gross_value__sum'
        ] or Decimal('0')

    @classmethod
    @transaction.atomic
    def create(cls, invoice_data: InvoiceData):
        invoice_data_dict = asdict(invoice_data)
        invoice_data_dict.pop('invoice_items')

        invoice: Invoice = cls.objects.create(**invoice_data_dict)

        items = []
        for item in invoice_data.invoice_items:
            item_data = asdict(item)
            item_data.pop('currency_code')

            items.append(InvoiceItem(invoice_id=invoice.id, **item_data))

        InvoiceItem.objects.bulk_create(items)

        return invoice

    @staticmethod
    @lru_booksy_cache(timeout=30 * 60, skip_in_pytest=True)
    def get_first_item(invoice_id: int) -> 'InvoiceItem':
        if invoice := Invoice.objects.filter(id=invoice_id).first():
            return invoice.items.first()

    @classmethod
    def cycle_start(cls, invoice_id: int):
        if item := cls.get_first_item(invoice_id):
            return item.billing_cycle_start

    @classmethod
    def cycle_end(cls, invoice_id: int):
        if item := cls.get_first_item(invoice_id):
            return item.billing_cycle_end

    @classmethod
    @lru_booksy_cache(timeout=30 * 60, skip_in_pytest=True)
    def total_amount(cls, invoice_id: int):
        if invoice := Invoice.objects.filter(id=invoice_id).first():
            return format(
                invoice.items.aggregate(
                    total=Sum(
                        Case(
                            When(
                                service=Invoice.Service.DISCOUNT,
                                then=-Coalesce(F('base_gross_value'), Value(0)),
                            ),
                            default=Greatest(
                                Coalesce(F('base_gross_value'), Value(0))
                                - Coalesce(F('discount_gross_value'), Value(0)),
                                Value(0),
                                output_field=FloatField(),
                            )
                            * F('quantity'),
                            output_field=FloatField(),
                        ),
                    )
                )['total']
                or 0.0,
                '.2f',
            )

    @classmethod
    @lru_booksy_cache(timeout=30 * 60, skip_in_pytest=True)
    def number_of_items(cls, invoice_id: int):
        if invoice := Invoice.objects.filter(id=invoice_id).first():
            return invoice.items.exclude(service=Invoice.Service.DISCOUNT).count()


class InvoiceItem(ArchiveModel):
    class Meta:
        verbose_name = 'Invoice item'
        verbose_name_plural = 'Invoice items'
        ordering = ('-created',)

    class Status(StrChoicesEnum):
        INIT = 'init', 'init'
        SENT = 'sent', 'sent'
        ERROR = 'error', 'error'

    id: int
    # It's not unique because we are going to implement correction invoices (most likely)
    product_identifier = models.CharField(max_length=255, null=True, db_index=True)

    # region To Navision
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='items')
    service = models.CharField(
        max_length=20,
        choices=Invoice.Service.choices(),
    )
    product = models.CharField(max_length=150)
    quantity = models.PositiveIntegerField(default=1, null=False)  # nav_offline
    base_gross_value = models.DecimalField(max_digits=13, decimal_places=5)
    payment_source = models.CharField(
        max_length=50,
        choices=InvoicePaymentSource.choices(),
        null=True,
    )
    charge_completed = models.BooleanField(
        default=None,
        null=True,
        help_text='True or False for online, NULL for offline transactions',
    )  # boost transaction may not be charged yet, other services set as None
    update_status = models.CharField(
        max_length=50,
        choices=Status.choices(),
        default=None,
        null=True,
        blank=True,
    )  # sync through API status after update (from not charged to charged OR refund)
    transaction_id = models.CharField(max_length=100, blank=True, null=True)
    charging_dt = models.DateTimeField(blank=True, null=True)
    transfer_dt = models.DateTimeField(blank=True, null=True)  # nav_offline  # deprecated?
    billing_cycle_start = models.DateTimeField()
    billing_cycle_end = models.DateTimeField()
    # endregion

    # region Internal use only
    discount_gross_value = models.DecimalField(
        max_digits=13,
        decimal_places=5,
        blank=True,
        null=True,
    )  # nav_offline
    base_tax_value = models.DecimalField(max_digits=13, decimal_places=5, blank=True, null=True)
    discount_tax_value = models.DecimalField(
        max_digits=13,
        decimal_places=5,
        blank=True,
        null=True,
    )  # nav_offline
    tax_rate = models.ForeignKey(TaxRate, on_delete=models.SET_NULL, null=True, blank=True)
    tax_additional_data = models.JSONField(null=True, blank=True)
    # endregion

    # region Returned from Navision
    document_id = models.CharField(max_length=50, blank=True, null=True)
    final_gross_value = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    final_tax_value = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    base_net_value = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    discounts_net_value = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    final_net_value = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    # endregion

    # Keeps info about object (subscription, transaction etc)
    # for which the invoice item was created
    content_type = models.ForeignKey(ContentType, blank=True, null=True, on_delete=models.SET_NULL)
    object_id = models.PositiveIntegerField(blank=True, null=True)
    invoiced_object = GenericForeignKey('content_type', 'object_id')

    def __str__(self):
        return f'{self.product} (Invoice ID: {self.invoice_id})'

    @property
    def booksy_item_id(self):
        return f'{InvoiceItem.booksy_item_id_prefix()}{self.id}'

    @classmethod
    def booksy_item_id_prefix(cls) -> str:
        return f'{settings.API_COUNTRY}-'.upper()


class InvoiceErrorResponse(ArchiveModel):
    class Meta:
        verbose_name = 'Invoice error response'
        verbose_name_plural = 'Invoice error responses'
        ordering = ('-created',)

    id: int

    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='errors')
    response = models.TextField()
