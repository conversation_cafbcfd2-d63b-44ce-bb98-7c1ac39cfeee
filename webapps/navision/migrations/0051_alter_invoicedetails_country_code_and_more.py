# Generated by Django 4.0.9 on 2023-03-20 10:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('navision', '0050_add_new_field_approved_invoice_model'),
    ]

    operations = [
        migrations.AlterField(
            model_name='invoicedetails',
            name='country_code',
            field=models.CharField(
                choices=[
                    ('br', 'Brasil'),
                    ('ca', 'Canada'),
                    ('es', 'Spain'),
                    ('fr', 'France'),
                    ('gb', 'Great Britain'),
                    ('ie', 'Ireland'),
                    ('mx', 'Mexico'),
                    ('pl', 'Poland'),
                    ('us', 'United States'),
                    ('za', 'South Africa'),
                    ('au', 'Australia'),
                    ('nl', 'Netherlands'),
                    ('pt', 'Portugal'),
                    ('ar', 'Argentina'),
                    ('cl', 'Chile'),
                    ('co', 'Colombia'),
                    ('it', 'Italy'),
                    ('ng', 'Nigeria'),
                    ('de', 'Germany'),
                    ('in', 'India'),
                    ('my', 'Malaysia'),
                    ('ru', 'Russia'),
                    ('se', 'Sweden'),
                    ('sg', 'Singapore'),
                ],
                editable=False,
                max_length=2,
            ),
        ),
        migrations.AlterField(
            model_name='merchant',
            name='country_code',
            field=models.CharField(
                blank=True,
                choices=[
                    ('br', 'Brasil'),
                    ('ca', 'Canada'),
                    ('es', 'Spain'),
                    ('fr', 'France'),
                    ('gb', 'Great Britain'),
                    ('ie', 'Ireland'),
                    ('mx', 'Mexico'),
                    ('pl', 'Poland'),
                    ('us', 'United States'),
                    ('za', 'South Africa'),
                    ('au', 'Australia'),
                    ('nl', 'Netherlands'),
                    ('pt', 'Portugal'),
                    ('ar', 'Argentina'),
                    ('cl', 'Chile'),
                    ('co', 'Colombia'),
                    ('it', 'Italy'),
                    ('ng', 'Nigeria'),
                    ('de', 'Germany'),
                    ('in', 'India'),
                    ('my', 'Malaysia'),
                    ('ru', 'Russia'),
                    ('se', 'Sweden'),
                    ('sg', 'Singapore'),
                ],
                max_length=2,
                null=True,
            ),
        ),
    ]
