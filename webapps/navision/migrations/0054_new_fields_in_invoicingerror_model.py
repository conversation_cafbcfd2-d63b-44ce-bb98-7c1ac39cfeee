# Generated by Django 4.1.7 on 2023-04-20 10:41

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('navision', '0053_invoicingsummary_operator'),
    ]

    operations = [
        migrations.AddField(
            model_name='invoicingerror',
            name='category',
            field=models.CharField(
                blank=True,
                choices=[
                    ('business', 'Business'),
                    ('buyer', 'Subscription Buyer'),
                    ('merchant', 'Merchant'),
                    ('invoiced', 'Already Invoiced'),
                    ('other', 'Other'),
                ],
                max_length=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name='invoicingerror',
            name='invoicing_target',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='invoicingerror',
            name='message',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='invoicingerror',
            name='source',
            field=models.CharField(
                choices=[('offline', 'Offline'), ('online', 'Online')], max_length=10, null=True
            ),
        ),
        migrations.AddField(
            model_name='invoicingerror',
            name='status',
            field=models.CharField(
                blank=True,
                choices=[
                    ('created', 'Created'),
                    ('rejected', 'Rejected'),
                    ('processed', 'Processed'),
                ],
                max_length=10,
                null=True,
            ),
        ),
    ]
