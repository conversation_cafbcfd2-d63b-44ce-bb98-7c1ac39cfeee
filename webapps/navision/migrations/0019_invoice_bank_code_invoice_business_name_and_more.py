# Generated by Django 4.0.2 on 2022-03-03 13:47

from django.db import migrations, models
import webapps.purchase.validators


class Migration(migrations.Migration):

    dependencies = [
        ('navision', '0018_auto_20211213_1213'),
    ]

    operations = [
        migrations.AddField(
            model_name='invoice',
            name='bank_code',
            field=models.CharField(max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='invoice',
            name='business_name',
            field=models.CharField(max_length=80, null=True),
        ),
        migrations.AddField(
            model_name='merchant',
            name='payment_due_days',
            field=models.PositiveSmallIntegerField(
                default=7, validators=[webapps.purchase.validators.valid_payment_due_days]
            ),
        ),
    ]
