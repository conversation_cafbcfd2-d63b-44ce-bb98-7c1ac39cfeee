from django.db.models import Q
from lib.celery_tools import celery_task


@celery_task
def approve_invoices_in_selected_invoice_summary_task(summary_id: int):
    from webapps.navision.models import Invoice

    Invoice.objects.filter(
        Q(approved=False) | Q(approved__isnull=True),
        business_summary__report__id=summary_id,
    ).update(approved=True)


@celery_task
def remove_unapproved_invoices_from_summaries_task(summary_id: int):
    # pylint: disable=cyclic-import
    from webapps.navision.admin import InvoicingSummaryAdmin
    from webapps.navision.models import Invoice, InvoicingSummary

    Invoice.objects.filter(
        Q(approved=False) | Q(approved__isnull=True),
        business_summary__report__id=summary_id,
        status=Invoice.Status.INIT,
    ).delete()

    InvoicingSummary.calculate_totals.cache_clear()
    InvoicingSummaryAdmin.billing_cycle_calculations.cache_clear()


@celery_task
def remove_test_invoices_from_invoicing_summary_task(summary_id: int):
    from webapps.navision.models import Invoice

    Invoice.objects.filter(
        is_production=False,
        business_summary__report__id=summary_id,
    ).delete()
