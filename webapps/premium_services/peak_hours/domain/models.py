from dataclasses import dataclass, field
from datetime import datetime, time
from decimal import Decimal

from dataclasses_json import DataClassJsonMixin, config
from django.conf import settings
from django.utils.translation import gettext as _

from lib.tools import format_currency
from webapps.premium_services.enums import (
    DayOfWeek,
    FeatureLabel,
    FeatureStatus,
    FeatureStatusColor,
)
from webapps.premium_services.peak_hours.domain.exceptions import (
    ElevationRateOutOufRangeError,
    ManyOfValidationError,
    StartHourAfterEndHourError,
)


@dataclass
class Service:
    elevation_rate: Decimal
    hour_from: time
    hour_till: time
    service_variant_id: int

    def __post_init__(self):
        errors = []
        if self.hour_till < self.hour_from:
            errors.append(StartHourAfterEndHourError())
        if not 1 <= self.elevation_rate <= 200:
            errors.append(ElevationRateOutOufRangeError())

        if errors:
            raise ManyOfValidationError(errors=errors)


@dataclass
class PeakHour:
    business_id: int
    day_of_week: DayOfWeek
    service_variants: list[Service] = field(default_factory=list)

    created: datetime | None = None
    deleted: datetime | None = None


@dataclass
class PeakHoursFeatureStatus(DataClassJsonMixin):
    estimated_revenue: Decimal = field(metadata=config(encoder=format_currency, mm_field=None))
    appointments_count: int
    status: FeatureStatus
    status_color: FeatureStatusColor
    label: FeatureLabel
    last_action_date: None | datetime = field(
        default=None,
        metadata=config(
            encoder=lambda dt: dt.strftime(settings.DATE_FORMAT) if dt else None, mm_field=None
        ),
    )

    def __post_init__(self):
        self.label = _(self.label)
