from http import HTTPStatus

import pytest

from webapps.google_business_profile.domain.exceptions import (
    AuthenticationErrorGoogle,
    ValidationErrorGoogle,
    GoogleApiError,
    PermissionDeniedErrorGoogle,
    ResourceNotFoundErrorGoogle,
    RateLimitExceededErrorGoogle,
)
from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    ApiResponse,
)
from webapps.google_business_profile.infrastructure.gateways.api_error_handler import (
    ApiErrorHandler,
)


class TestApiErrorHandler:
    def test_handle_error_too_many_requests(self):
        response = ApiResponse(
            status_code=HTTPStatus.TOO_MANY_REQUESTS,
            data={'error': {'message': 'Rate limit exceeded'}},
        )

        with pytest.raises(RateLimitExceededErrorGoogle):
            ApiErrorHandler.handle_error(response)

    def test_handle_error_unauthorized(self):
        response = ApiResponse(
            status_code=HTTPStatus.UNAUTHORIZED, data={'error': {'message': 'Invalid credentials'}}
        )

        with pytest.raises(AuthenticationErrorGoogle):
            ApiErrorHandler.handle_error(response)

    def test_handle_error_forbidden(self):
        response = ApiResponse(
            status_code=HTTPStatus.FORBIDDEN, data={'error': {'message': 'Permission denied'}}
        )

        with pytest.raises(PermissionDeniedErrorGoogle):
            ApiErrorHandler.handle_error(response)

    def test_handle_error_not_found(self):
        response = ApiResponse(
            status_code=HTTPStatus.NOT_FOUND, data={'error': {'message': 'Resource not found'}}
        )

        with pytest.raises(ResourceNotFoundErrorGoogle):
            ApiErrorHandler.handle_error(response)

    def test_handle_error_bad_request(self):
        response = ApiResponse(
            status_code=HTTPStatus.BAD_REQUEST,
            data={'error': {'message': 'Invalid request', 'details': {'field': 'Invalid value'}}},
        )

        with pytest.raises(ValidationErrorGoogle) as exc_info:
            ApiErrorHandler.handle_error(response)

        assert exc_info.value.errors == {'field': 'Invalid value'}

    def test_handle_error_bad_request_no_details(self):
        response = ApiResponse(
            status_code=HTTPStatus.BAD_REQUEST, data={'error': {'message': 'Invalid request'}}
        )

        with pytest.raises(ValidationErrorGoogle) as exc_info:
            ApiErrorHandler.handle_error(response)

        assert exc_info.value.errors == {}

    def test_handle_error_server_error(self):
        response = ApiResponse(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            data={'error': {'message': 'Internal server error'}},
        )

        with pytest.raises(GoogleApiError):
            ApiErrorHandler.handle_error(response)

    def test_handle_error_unknown_error(self):
        response = ApiResponse(
            status_code=599,  # Non-standard status code
            data={'error': {'message': 'Unknown error'}},
        )

        with pytest.raises(GoogleApiError):
            ApiErrorHandler.handle_error(response)

    def test_handle_error_empty_response(self):
        response = ApiResponse(status_code=HTTPStatus.BAD_GATEWAY, data={})

        with pytest.raises(GoogleApiError):
            ApiErrorHandler.handle_error(response)

    def test_handle_error_with_code_and_status(self):
        response = ApiResponse(
            status_code=HTTPStatus.BAD_REQUEST,
            data={
                'error': {
                    'message': 'Invalid request',
                    'code': 400,
                    'status': 'INVALID_ARGUMENT',
                    'details': {'field': 'Invalid value'},
                }
            },
        )

        with pytest.raises(ValidationErrorGoogle):
            ApiErrorHandler.handle_error(response)
