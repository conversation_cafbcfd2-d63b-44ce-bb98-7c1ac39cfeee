import abc
from dataclasses import dataclass
from typing import Any

from webapps.google_business_profile.domain.value_objects import (
    LocationAddress,
    LocationCategories,
    LocationPhoneNumbers,
)
from webapps.google_business_profile.shared import RegionCode, LanguageCode, WebsiteUri


@dataclass(frozen=True)
class ApiResponse:
    status_code: int
    data: dict[str, Any]


@dataclass(frozen=True)
class CreateLocationParams:
    title: str
    storefront_address: LocationAddress
    categories: LocationCategories
    phone_numbers: LocationPhoneNumbers
    language_code: LanguageCode
    website_uri: WebsiteUri | None = None
    validate_only: bool = False


@dataclass(frozen=True)
class UpdateLocationParams:
    title: str
    categories: LocationCategories
    storefront_address: LocationAddress | None = None
    validate_only: bool = False


@dataclass(frozen=True)
class CategoryQueryParams:
    region_code: RegionCode
    language_code: LanguageCode
    filter: str
    page_size: int = 20
    view: int = 1


@dataclass(frozen=True)
class AddressCheckParams:
    address: LocationAddress


class GoogleBusinessProfileAbstractClient(abc.ABC):
    @abc.abstractmethod
    def get_gbp_accounts(self) -> ApiResponse: ...

    @abc.abstractmethod
    def get_gbp_locations(self) -> ApiResponse: ...

    @abc.abstractmethod
    def create_gbp_location(self, location_data: CreateLocationParams) -> ApiResponse: ...

    @abc.abstractmethod
    def get_particular_location(self) -> ApiResponse: ...

    @abc.abstractmethod
    def update_gbp_location(self, location_data: UpdateLocationParams) -> ApiResponse: ...

    @abc.abstractmethod
    def get_gbp_location_status(self) -> ApiResponse: ...

    @abc.abstractmethod
    def get_gbp_location_verifications(self) -> ApiResponse: ...

    @abc.abstractmethod
    def get_categories(self, data: CategoryQueryParams) -> ApiResponse: ...

    @abc.abstractmethod
    def delete_gbp_location(self) -> ApiResponse: ...
