from webapps.google_business_profile.application.dtos.account_dto import (
    AccountPresentationDTO,
)
from webapps.google_business_profile.domain.models import GoogleAccount


class AccountDTOConverter:

    @staticmethod
    def to_presentation_dto(entity: GoogleAccount) -> AccountPresentationDTO:
        return AccountPresentationDTO(
            account_id=entity.id,
            account_name=entity.account_name,
        )
