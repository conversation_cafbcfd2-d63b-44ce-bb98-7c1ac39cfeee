import random
from datetime import timedelta, time

from django.conf import settings

from webapps.business.enums import CustomData
from webapps.business.models import Business, Resource
from webapps.business.models.category import BusinessCategory
from webapps.business.serializers import BusinessSerializer
from webapps.business.serializers.serializers import ResourceCreateUpdateSerializer
from webapps.business.business_categories.service_suggestion import (
    ServiceSuggestionUtil,
    SuggestionsParams,
)
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import POS
from webapps.purchase.models import SubscriptionListing, Subscription
from webapps.schedule.enums import DayOfWeek
from webapps.schedule.ports import get_resource_default_hours, set_resource_default_hours
from webapps.script_runner.common.exceptions import (
    User<PERSON>reateError,
    BusinessCreateError,
    ResourceCreateError,
)
from webapps.structure.models import Region
from webapps.user.models import User
from webapps.user.serializers import BusinessAccountCreateSerializer


EXPIRY_IN_DAYS = timedelta(days=3650)


class CreateUser:
    def __init__(self, booking_source, user_data, profile_type, recreate=False):
        self.booking_source = booking_source
        self.user_data = user_data
        self.profile_type = profile_type
        self.recreate = recreate

    def create_user(self):
        user = User.objects.filter(email=self.user_data['email']).first()
        if user and not self.recreate:
            return user
        if self.recreate:
            user.delete()

        serializer = BusinessAccountCreateSerializer(
            data=self.user_data,
            context={
                'booking_source': self.booking_source,
                'process_photo': False,
                'profile_type': self.profile_type,
                'language': settings.LANGUAGE_CODE[:2],
                'photo': None,
            },
        )

        if serializer.is_valid():
            return serializer.save()
        raise UserCreateError(f'Error creating user: {serializer.errors}')


class CreateBusiness:
    # pylint: disable=too-many-arguments
    def __init__(self, booking_source, user, business_data, staff_number=None, reindex=True):
        self.booking_source = booking_source
        self.user = user
        self.business_data = business_data
        self.staff_number = staff_number or 0
        self.business = None
        self.reindex = reindex
        self.region_zips = Region.objects.filter(type='zip').values_list('id', flat=True)
        self.business_category_ids = BusinessCategory.objects.filter(
            order__isnull=False
        ).values_list('id', flat=True)

    # pylint: enable=too-many-arguments

    def create_business(self):
        self.business = self.user.businesses.filter(
            owner_id=self.user.id, name=self.business_data['name']
        ).first()
        if self.business:
            return self.business

        business_serializer = BusinessSerializer(
            data=self.business_data,
            context={
                'owner': self.user,
                'booking_source': self.booking_source,
                'fingerprint': 'script-runner-create-business',
            },
        )

        if business_serializer.is_valid():
            self.business = business_serializer.save()
        else:
            raise BusinessCreateError(f'Error creating business: {business_serializer.errors}')

        # Add subscription
        self._subscribe_for_decade()

        # Add resources: owner + optional staffers
        self._create_staffers_with_working_hours()

        # Set business categories
        self._set_business_categories()

        # Create services for all staffers
        self._create_default_services()

        # Set region and geo data
        self._set_region_geo_data()

        # Fill in missing business parameters
        self.business.status = Business.Status.PAID
        self.business.active_from = self.business.created
        self.business.visible = True
        self.business.custom_data[CustomData.CAN_USE_FRONTDESK] = True
        self.business.include_in_analysis = False

        # Create POS for business
        self._create_pos_for_business()

        self.business.save()
        if self.reindex:
            self.business.reindex()

        return self.business

    def _subscribe_for_decade(self):
        product = (
            SubscriptionListing.objects.filter(
                active=True,
            )
            .order_by('-price_amount')
            .first()
        )
        Subscription.objects.get_or_create(
            business=self.business,
            product=product,
            source=Business.PaymentSource.OFFLINE,
            start=self.business.created,
            expiry=self.business.created + EXPIRY_IN_DAYS,
        )

    def _create_staffers_with_working_hours(self):
        # Create owner with working hours
        self._create_staffer(Resource.STAFF_ACCESS_LEVEL_OWNER)

        # Create additional staffers with working hours
        for staffer_id in range(self.staff_number):
            self._create_staffer(Resource.STAFF_ACCESS_LEVEL_STAFF, staffer_id)

    def _create_staffer(self, staffer_type, staffer_id=None):
        if staffer_type == Resource.STAFF_ACCESS_LEVEL_OWNER:
            user = self.business.owner
            name = self.business.owner.full_name
        else:
            user = None
            name = f'Staffer {staffer_id} {self.business.owner.full_name}'
        serializer = ResourceCreateUpdateSerializer(
            data={
                'business': self.business.id,
                'type': Resource.STAFF,
                'name': name,
                'active': True,
            },
            partial=True,
            context={
                'user': user,
                'business': self.business,
                'send_invitation': False,
            },
        )
        if not serializer.is_valid():
            raise ResourceCreateError(f'Error creating resource: {serializer.errors}')
        resource = serializer.save()
        self._create_working_hours_for_staffer(resource)

    def _create_working_hours_for_staffer(self, resource):
        if not get_resource_default_hours(business_id=self.business.id, resource_id=resource.id):
            set_resource_default_hours(
                business_id=self.business.id,
                resource_id=resource.id,
                hours={dow: [[time(6, 0), time(22, 0)]] for dow in DayOfWeek},
                tz=self.business.get_timezone(),
            )

    def _set_business_categories(self):
        business_category_ids = random.sample(sorted(self.business_category_ids), 5)
        business_categories = BusinessCategory.objects.filter(id__in=business_category_ids)
        self.business.categories.set(business_categories)
        self.business.primary_category = random.choice(business_categories.all())

    def _create_default_services(self):
        staffers = self.business.resources.filter(type=Resource.STAFF)
        used_names = set(
            self.business.services.filter(
                active=True,
            ).values_list('name', flat=True)
        )

        suggestions = ServiceSuggestionUtil.get_suggestions(
            SuggestionsParams(
                category_ids=self.business.categories.values_list('id', flat=True),
                skip_names=used_names,
            )
        )

        created_services = ServiceSuggestionUtil.create_suggested_services(
            self.business, suggestions
        )

        ServiceSuggestionUtil.attach_services_to_staffers(
            services=created_services, staffers=staffers
        )

    def _set_region_geo_data(self):
        random_region_zip = Region.objects.get(id=random.choice(self.region_zips))
        self.business.region = random_region_zip
        self.business.latitude = random_region_zip.latitude
        self.business.longitude = random_region_zip.longitude
        self.business.zipcode = random_region_zip.name

    def _create_pos_for_business(self):
        pos, _ = POS.objects.get_or_create(
            active=True,
            business=self.business,
        )
        pos.load_defaults()
        pos.payment_types.get_or_create(code=PaymentTypeEnum.PAY_BY_APP)
