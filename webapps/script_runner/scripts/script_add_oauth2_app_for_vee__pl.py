from django.conf import settings

from webapps.public_partners.models import OAuth2Application, OAuth2ApplicationConfig
from webapps.script_runner.mixins import ReusableScript
from webapps.script_runner.runners import DBScriptRunner
from webapps.user.tools import get_system_user


class Script(ReusableScript, DBScriptRunner):
    version = 1

    def run(self):
        oauth2_app = OAuth2Application.objects.filter(
            client_id=settings.VEE_APPLICATION_CLIENT_ID,
        ).first() or OAuth2Application(
            client_id=settings.VEE_APPLICATION_CLIENT_ID,
            client_secret='5YnqQJCG1Y0jFmA',
        )
        oauth2_app.user = get_system_user()
        oauth2_app.client_type = OAuth2Application.CLIENT_PUBLIC
        oauth2_app.authorization_grant_type = OAuth2Application.GRANT_PASSWORD
        oauth2_app.name = 'VEE'
        oauth2_app.company_name = "Vee AI"
        oauth2_app.is_catalogable = False
        oauth2_app.save()

        OAuth2ApplicationConfig.objects.get_or_create(
            application=oauth2_app,
            defaults={'ownership': OAuth2ApplicationConfig.OWNERSHIP.BUSINESS},
        )
