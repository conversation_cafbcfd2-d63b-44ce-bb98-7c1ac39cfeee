from decimal import Decimal
from lib.tools import tznow

from webapps.navision.enums import TaxRateService
from webapps.navision.models import TaxGroup, TaxRate
from webapps.script_runner.runners import DBScriptRunner
from webapps.structure.models import Region


class Script(DBScriptRunner):
    def run(self):
        france, _ = TaxGroup.objects.get_or_create(
            name='France Métropolitaine',
            accounting_group='domestic',
            region=Region.objects.get(name='France', type=Region.Type.COUNTRY),
        )

        guadeloupe, _ = TaxGroup.objects.get_or_create(
            name='Guadeloupe',
            accounting_group='domestic-gp',
            region=Region.objects.get(name='Guadeloupe', type=Region.Type.STATE),
        )

        martinique, _ = TaxGroup.objects.get_or_create(
            name='Martinique',
            accounting_group='domestic-mq',
            region=Region.objects.get(name='Martinique', type=Region.Type.STATE),
        )

        la_reunion, _ = TaxGroup.objects.get_or_create(
            name='La Réunion',
            accounting_group='domestic-re',
            region=Region.objects.get(name='La Réunion', type=Region.Type.STATE),
        )

        mayotte, _ = TaxGroup.objects.get_or_create(
            name='Mayotte',
            accounting_group='domestic-yt',
            region=Region.objects.get(name='Mayotte', type=Region.Type.STATE),
        )

        st_barthelemy, _ = TaxGroup.objects.get_or_create(
            name='St Barthélemy',
            accounting_group='domestic-bl',
        )

        st_martin, _ = TaxGroup.objects.get_or_create(
            name='St Martin',
            accounting_group='domestic-mf',
        )

        st_pierre_et_miquelon, _ = TaxGroup.objects.get_or_create(
            name='St Pierre et Miquelon',
            accounting_group='domestic-pm',
        )

        for service in TaxRateService:
            TaxRate.update_or_create(
                service=service,
                valid_from=tznow(),
                tax_rate=Decimal(0.2000),
                tax_group=france,
            )

        non_zero_tax_groups = [guadeloupe, martinique, la_reunion]

        for group in non_zero_tax_groups:
            for service in TaxRateService:
                TaxRate.update_or_create(
                    service=service, valid_from=tznow(), tax_rate=Decimal(0.0850), tax_group=group
                )

        zero_tax_groups = [mayotte, st_barthelemy, st_martin, st_pierre_et_miquelon]

        for group in zero_tax_groups:
            for service in TaxRateService:
                TaxRate.update_or_create(
                    service=service, valid_from=tznow(), tax_rate=Decimal(0), tax_group=group
                )
