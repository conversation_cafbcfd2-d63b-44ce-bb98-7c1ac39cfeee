from datetime import datetime

from unittest.mock import patch


import pytest
from dateutil.relativedelta import relativedelta
from django.test import TestCase
from django.utils.timezone import make_aware
from model_bakery import baker
from webapps.billing.models import BillingCycle
from webapps.business.baker_recipes import business_recipe
from webapps.business.models import Business
from webapps.purchase.models import InvoiceAddress, SubscriptionBuyer

# pylint: disable=line-too-long
from webapps.script_runner.scripts.script__populate_old_subs_data__all import (
    Script,
)
from webapps.structure.baker_recipes import usa_recipe
from webapps.structure.models import get_parents_of_region, Region, RegionGraph, RegionType
from webapps.user.models import User


@pytest.mark.freeze_time('2024-08-23')
class TestPopulateOldSubsDataScript(TestCase):
    def setUp(self) -> None:
        super().setUp()

        self.john = baker.make(User, first_name='<PERSON>', last_name='<PERSON><PERSON>', email='<EMAIL>')

        self.usa = usa_recipe.make()

        self.florida = Region.objects.create(type=RegionType.STATE, name='Florida', abbrev='FL')
        RegionGraph.objects.create(region=self.usa, related_region=self.florida)

        self.largo = Region.objects.create(type=RegionType.CITY, name='Largo')
        RegionGraph.objects.create(region=self.florida, related_region=self.largo)

        self.largo_zip = Region.objects.create(type=RegionType.ZIP, name='33770')
        RegionGraph.objects.create(region=self.largo, related_region=self.largo_zip)

        get_parents_of_region.cache_clear()

    def tearDown(self) -> None:
        super().tearDown()
        get_parents_of_region.cache_clear()

    @staticmethod
    def _add_billing_cycle(business: Business, date_start: datetime, date_end: datetime):
        baker.make(
            BillingCycle,
            business=business,
            date_start=date_start,
            date_end=date_end,
        )

    @patch('webapps.navision.processes.active_businesses')
    def test_fill_buyer_data_creates_subscription_buyer(self, mock_active_businesses):
        business = business_recipe.make(
            name='Company',
            region=self.largo_zip,
            city='Largo',
            invoice_address=None,
            owner=self.john,
            address='Address',
            active=True,
            buyer=None,
        )

        mock_active_businesses.return_value = Business.objects.filter(id=business.id)

        Script().run()

        buyer = SubscriptionBuyer.objects.get(businesses=business)

        self.assertTrue(buyer.is_verified)
        self.assertIsNotNone(buyer.verified_at)
        self.assertEqual(buyer.verified_by.email, '<EMAIL>')

    @patch('webapps.navision.processes.active_businesses')
    def test_no_subscription_buyer_created_when_business_not_in_active_businesses(
        self, mock_active_businesses
    ):
        business = Business.objects.create(
            name='Inactive Company',
            region=self.largo_zip,
            city='Largo',
            invoice_address=None,
            owner=self.john,
            address='Address',
            active=True,
            buyer=None,
        )

        mock_active_businesses.return_value = Business.objects.none()

        Script().run()

        self.assertFalse(SubscriptionBuyer.objects.filter(businesses=business).exists())

    def test_billing_cycle_created_in_period(self):
        business = business_recipe.make(
            name='Company',
            region=self.largo_zip,
            city='Largo',
            invoice_address=None,
            owner=self.john,
            address='Address',
            active=True,
        )

        self._add_billing_cycle(
            business,
            make_aware(datetime(2023, 5, 1)),
            make_aware(datetime(2023, 6, 1)),
        )

        self.assertEqual(InvoiceAddress.objects.all().count(), 0)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 0)

        Script().run()

        self.assertEqual(InvoiceAddress.objects.all().count(), 1)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 1)

        business.refresh_from_db()
        self.assertIsNotNone(business.buyer)

    def test_billing_cycle_created_beginning_of_period(self):
        business = business_recipe.make(
            name='Company',
            region=self.largo_zip,
            city='Largo',
            invoice_address=None,
            owner=self.john,
            address='Address',
            active=True,
        )

        self._add_billing_cycle(
            business,
            make_aware(datetime(2022, 12, 7)),
            make_aware(datetime(2023, 1, 7)),
        )

        self.assertEqual(InvoiceAddress.objects.all().count(), 0)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 0)

        Script().run()

        self.assertEqual(InvoiceAddress.objects.all().count(), 1)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 1)

        business.refresh_from_db()
        self.assertIsNotNone(business.buyer)
        self.assertIsNotNone(business.buyer.invoice_address)

    def test_billing_cycle_created_outside_period(self):
        business = business_recipe.make(
            name='Company',
            region=self.largo_zip,
            city='Largo',
            invoice_address=None,
            owner=self.john,
            address='Address',
            active=True,
        )

        self._add_billing_cycle(
            business,
            make_aware(datetime(2022, 11, 18)),
            make_aware(datetime(2022, 12, 18)),
        )

        self.assertEqual(InvoiceAddress.objects.all().count(), 0)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 0)

        Script().run()

        self.assertEqual(InvoiceAddress.objects.all().count(), 0)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 0)

    def test_multiple_business_created_in_period(self):
        businesses = business_recipe.make(
            name='Company',
            region=self.largo_zip,
            city='Largo',
            invoice_address=None,
            owner=self.john,
            address='Address',
            active=True,
            _quantity=5,
        )

        dates = [
            make_aware(datetime(2023, 1, 1)),
            make_aware(datetime(2023, 4, 15)),
            make_aware(datetime(2023, 8, 1)),
            make_aware(datetime(2024, 1, 1)),
            make_aware(datetime(2024, 8, 18)),
        ]
        for business, date_start in zip(businesses, dates):
            self._add_billing_cycle(
                business,
                date_start,
                date_start + relativedelta(months=1),
            )

        self.assertEqual(InvoiceAddress.objects.all().count(), 0)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 0)

        Script().run()

        self.assertEqual(InvoiceAddress.objects.all().count(), 5)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 5)

        for business in businesses:
            business.refresh_from_db()
            self.assertIsNotNone(business.buyer)
            self.assertIsNotNone(business.buyer.invoice_address)

    def test_no_businesses_created(self):
        Script().run()

        self.assertEqual(InvoiceAddress.objects.all().count(), 0)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 0)
