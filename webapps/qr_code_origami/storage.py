from urllib.parse import urljoin

from django.conf import settings
from storages.backends.gcloud import GoogleCloudStorage


class QRCodeFileStorage(GoogleCloudStorage):  # pylint: disable=abstract-method
    def get_default_settings(self):
        bucket_settings = super().get_default_settings()
        bucket_settings['project_id'] = settings.QR_CODE_GCS_PROJECT_ID
        bucket_settings['bucket_name'] = settings.QR_CODE_GCS_BUCKET_NAME
        return bucket_settings

    @staticmethod
    def domain_url(file_storage_path):
        return urljoin(f'https://{settings.QR_CODE_GCS_DOMAIN_NAME}', file_storage_path)
