from django.contrib import admin
from lib.admin_helpers import BaseModelAdmin
from webapps.pop_up_notification.enums import B2BReferralNotifEventType

from webapps.pop_up_notification.forms import (
    BusinessYouMayLikeForm,
    CategoryYouMayLikeForm,
    ConsentsInline,
    ConsentsNotificationForm,
    ShortReviewForm,
    LateCancellationForm,
    MaxLeadTimeForm,
    MobilePaymentsIntroductionForm,
    MobilePaymentsMigrationForm,
    MobilePaymentsTurnOnPrepaymentsForm,
    FamilyAndFriendsInvitationNotificationForm,
    FamilyAndFriendsInvitationResponseNotificationForm,
    FamilyAndFriendsUnlinkNotificationForm,
)
from webapps.pop_up_notification.models import (
    BusinessYouMayLikeNotification,
    CategoryYouMayLikeNotification,
    ConsentsNotification,
    DigitalFlyerNotification,
    FamilyAndFriendsBusinessSetupNotification,
    GenericPopUpNotificationModel,
    RewardC2BStatusChangeNotification,
    ShortReviewNotification,
    LateCancellationNotification,
    MaxLeadTimeNotification,
    MobilePaymentsIntroduction,
    MobilePaymentsMigration,
    B2BReferralNotification,
    MobilePaymentsTurnOnPrepayments,
    FamilyAndFriendsInvitationNotification,
    FamilyAndFriendsInvitationResponseNotification,
    FamilyAndFriendsUnlinkNotification,
)


class BaseNotificationAdmin(BaseModelAdmin):
    raw_id_fields = ['user', 'business']
    readonly_fields = ('deleted',)
    search_fields = [
        '=id',
        'user__last_name',
        '=user__id',
        '=notification_type',
    ]
    list_per_page = 100
    list_filter = ['used']
    list_display = ['id', 'user', 'created', 'valid_till', 'used', 'deleted']


admin.site.register(GenericPopUpNotificationModel, BaseNotificationAdmin)


class BusinessYouMayLikeAdmin(BaseModelAdmin):
    form = BusinessYouMayLikeForm
    search_fields = [
        '=id',
        'user__last_name',
        '=user__id',
        '=notification_type',
    ]
    list_per_page = 100
    list_filter = ['used']
    list_display = [
        'id',
        'user',
        'created',
        'valid_till',
        'dismissed',
        'used',
    ]
    raw_id_fields = (
        'business',
        'user',
    )

    @staticmethod
    def dismissed(obj):
        if obj.deleted:
            return obj.deleted.strftime('%Y-%m-%d %H:%M')
        return obj.deleted

    dismissed.short_description = 'DateTime Dismissed'

    def get_queryset(self, request):
        # show deleted items also
        qs = BusinessYouMayLikeNotification.all_objects.filter(
            notification_type=GenericPopUpNotificationModel.TYPE_BUSINESS_YOU_MAY_LIKE
        ).select_related('user')
        return qs.only(
            'id',
            'user',
            'created',
            'deleted',
            'valid_till',
            'used',
        )


admin.site.register(BusinessYouMayLikeNotification, BusinessYouMayLikeAdmin)


class CategoryYouMayLikeAdmin(BaseModelAdmin):
    form = CategoryYouMayLikeForm
    raw_id_fields = (
        'user',
        'business',
    )
    search_fields = [
        '=id',
        'user__last_name',
        '=user__id',
        '=notification_type',
    ]
    list_per_page = 100
    list_filter = ['used']
    list_display = [
        'id',
        'user',
        'created',
        'valid_till',
        'dismissed',
        'used',
    ]

    @staticmethod
    def dismissed(obj):
        if obj.deleted:
            return obj.deleted.strftime('%Y-%m-%d %H:%M')
        return obj.deleted

    dismissed.short_description = 'DateTime Dismissed'

    def get_queryset(self, request):
        # show deleted items also
        qs = CategoryYouMayLikeNotification.all_objects.filter(
            notification_type=GenericPopUpNotificationModel.TYPE_CATEGORY_YOU_MAY_LIKE
        ).select_related('user')
        return qs.only(
            'id',
            'user',
            'created',
            'deleted',
            'valid_till',
            'used',
        )


admin.site.register(CategoryYouMayLikeNotification, CategoryYouMayLikeAdmin)


class ShortReviewAdmin(BaseModelAdmin):
    form = ShortReviewForm
    raw_id_fields = (
        'user',
        'subbooking',
        'business',
    )
    search_fields = [
        '=id',
        'user__last_name',
        '=user__id',
        '=notification_type',
    ]
    list_per_page = 100
    list_filter = ['used']
    list_display = [
        'id',
        'user',
        'created',
        'valid_till',
        'used',
    ]

    def get_queryset(self, request):
        # show deleted items also
        qs = (
            ShortReviewNotification.all_objects.filter(
                notification_type=GenericPopUpNotificationModel.TYPE_SHORT_REVIEW
            )
            .select_related('user')
            .only(
                'id',
                'user',
                'created',
                'valid_till',
                'used',
            )
        )
        return qs


admin.site.register(ShortReviewNotification, ShortReviewAdmin)


class LateCancellationAdmin(BaseNotificationAdmin):
    form = LateCancellationForm

    def get_queryset(self, request):
        # show deleted items also
        qs = LateCancellationNotification.all_objects.all().select_related('user', 'business')
        return qs


admin.site.register(LateCancellationNotification, LateCancellationAdmin)


class RewardC2BStatusChangeNotificationAdmin(BaseModelAdmin):
    list_per_page = 100
    list_filter = ['used', 'status']
    raw_id_fields = ['user', 'business']
    list_display = [
        'id',
        'user',
        'status',
        'created',
        'valid_till',
        'used',
    ]
    readonly_fields = ('_created', '_updated')


admin.site.register(
    RewardC2BStatusChangeNotification,
    RewardC2BStatusChangeNotificationAdmin,
)


class MaxLeadTimeNotificationAdmin(BaseNotificationAdmin):
    form = MaxLeadTimeForm


admin.site.register(MaxLeadTimeNotification, MaxLeadTimeNotificationAdmin)


class DigitalFlyerNotificationAdmin(BaseModelAdmin):
    list_per_page = 100
    list_filter = ['used']
    raw_id_fields = ['user', 'business']
    list_display = ['id', 'used', 'valid_till', 'event_type', 'display_type']
    readonly_fields = ('_created', '_updated')


admin.site.register(DigitalFlyerNotification, DigitalFlyerNotificationAdmin)


class MobilePaymentsIntrodutionAdmin(BaseNotificationAdmin):
    form = MobilePaymentsIntroductionForm


admin.site.register(MobilePaymentsIntroduction, MobilePaymentsIntrodutionAdmin)


class MobilePaymentsMigrationAdmin(BaseNotificationAdmin):
    form = MobilePaymentsMigrationForm


admin.site.register(MobilePaymentsMigration, MobilePaymentsMigrationAdmin)


class MobilePaymentsTurnOnPrepaymentsAdmin(BaseNotificationAdmin):
    form = MobilePaymentsTurnOnPrepaymentsForm


admin.site.register(
    MobilePaymentsTurnOnPrepayments,
    MobilePaymentsTurnOnPrepaymentsAdmin,
)


class ConsentsNotificationAdmin(BaseNotificationAdmin):
    form = ConsentsNotificationForm
    search_fields = ['=id', 'user__email', '=user__id', '=notification_type', '=consents__uuid']
    list_per_page = 100
    list_filter = ['used']
    list_display = [
        'id',
        'user',
        'business_name',
        'created',
        'valid_till',
        'dismissed',
        'used',
    ]
    readonly_fields = ['deleted']
    inlines = [ConsentsInline]

    @staticmethod
    def dismissed(obj):
        if obj.deleted:
            return obj.deleted.strftime('%Y-%m-%d %H:%M')
        return obj.deleted

    dismissed.short_description = 'DateTime Dismissed'

    def get_queryset(self, request):
        # show deleted items also
        return (
            ConsentsNotification.all_objects.filter(
                notification_type=ConsentsNotification.TYPE_CONSENTS
            )
            .select_related('user', 'business')
            .only(
                'id',
                'user',
                'created',
                'deleted',
                'valid_till',
                'valid_since',
                'updated',
                'notification_type',
                'used',
                'business',
            )
        )

    def get_form(self, request, obj=None, change=False, **kwargs):
        # ConsentsInline uses obj reference to limit choices
        request.object = obj
        return super().get_form(request, obj, change, **kwargs)

    @staticmethod
    def business_name(obj):
        return obj.business.name if obj.business else ''

    business_name.short_description = 'Business'


admin.site.register(ConsentsNotification, ConsentsNotificationAdmin)


class FamilyAndFriendsInvitationNotificationAdmin(BaseNotificationAdmin):
    form = FamilyAndFriendsInvitationNotificationForm


admin.site.register(
    FamilyAndFriendsInvitationNotification, FamilyAndFriendsInvitationNotificationAdmin
)


class FamilyAndFriendsInvitationResponseNotificationAdmin(BaseNotificationAdmin):
    form = FamilyAndFriendsInvitationResponseNotificationForm


admin.site.register(
    FamilyAndFriendsInvitationResponseNotification,
    FamilyAndFriendsInvitationResponseNotificationAdmin,
)


class FamilyAndFriendsUnlinkNotificationAdmin(BaseNotificationAdmin):
    form = FamilyAndFriendsUnlinkNotificationForm


admin.site.register(FamilyAndFriendsUnlinkNotification, FamilyAndFriendsUnlinkNotificationAdmin)


class B2BReferralNotificationAdmin(BaseNotificationAdmin):
    exclude = (
        'deleted',
        'notification_type',
    )

    search_fields = [
        '=id',
        'user__email',
        '=user__id',
        '=notification_type',
    ]
    list_display = [
        'id',
        'event_type',
        'receiver_type',
        'user',
        'business',
        'created',
        'valid_till',
        'used',
    ]
    readonly_fields = [
        'receiver_type',
    ]

    @staticmethod
    def receiver_type(obj):
        if obj.event_type in [
            B2BReferralNotifEventType.SIGNED_UP,
            B2BReferralNotifEventType.REWARD_PAID_OUT,
            B2BReferralNotifEventType.REWARD_PROCESSING,
            B2BReferralNotifEventType.REWARD_PENDING,
        ]:
            return 'Referrer'
        return 'Invited'


admin.site.register(B2BReferralNotification, B2BReferralNotificationAdmin)


class FamilyAndFriendsBusinessSetupNotificationAdmin(BaseNotificationAdmin):
    exclude = (
        'deleted',
        'notification_type',
    )
    raw_id_fields = (
        'user',
        'business',
    )
    readonly_fields = ('deleted',)
    search_fields = (
        '=id',
        '=user__id',
        '=business__id',
    )
    list_filter = ('used',)
    list_display = (
        'id',
        'user',
        'business',
        'created',
        'valid_till',
        'used',
        'deleted',
    )


admin.site.register(
    FamilyAndFriendsBusinessSetupNotification, FamilyAndFriendsBusinessSetupNotificationAdmin
)
