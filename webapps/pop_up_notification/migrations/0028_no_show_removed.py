# Generated by Django 2.0.13 on 2019-09-23 12:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pop_up_notification', '0027_payments_migration'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='noshowprotectionnotification',
            name='genericpopupnotificationmodel_ptr',
        ),
        migrations.AlterField(
            model_name='genericpopupnotificationmodel',
            name='notification_type',
            field=models.CharField(
                choices=[
                    ('meet_me_again', 'Meet me Again!'),
                    ('business_like', 'Business You may like'),
                    ('category_like', 'Category You may like'),
                    ('booking_pattern', ''),
                    ('short_review', 'Short Review'),
                    ('referral_c2b_reward_change', 'Reward C2B Status Change'),
                    ('late_cancellation', 'Late Cancellation'),
                    ('cross_booking_after_first', 'Cross booking after first booking'),
                    ('digital_flyer', 'Digital Flyer'),
                    ('mobile_payments_pp50', 'Mobile Payments PP 50'),
                    ('mobile_payments_pp100', 'Mobile Payments PP 100'),
                    ('subdomain', 'Subdomain'),
                    ('mobile_payments_introduction', 'Mobile Payments Introduction'),
                    ('mobile_payments_migration', 'Mobile Payments Migration'),
                ],
                default='',
                max_length=30,
            ),
        ),
        migrations.DeleteModel(
            name='NoShowProtectionNotification',
        ),
    ]
