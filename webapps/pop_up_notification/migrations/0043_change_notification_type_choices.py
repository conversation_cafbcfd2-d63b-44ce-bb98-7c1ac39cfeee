# Generated by Django 2.2.12 on 2020-06-24 07:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pop_up_notification', '0042_merge_20200529_0916'),
    ]

    operations = [
        migrations.AlterField(
            model_name='genericpopupnotificationmodel',
            name='notification_type',
            field=models.CharField(
                choices=[
                    ('meet_me_again', 'Meet me Again!'),
                    ('business_like', 'Business You may like'),
                    ('category_like', 'Category You may like'),
                    ('booking_pattern', ''),
                    ('short_review', 'Short Review'),
                    ('referral_c2b_reward_change', 'Reward C2B B2BReferralStatus Change'),
                    ('late_cancellation', 'Late Cancellation'),
                    ('cross_booking_after_first', 'Cross booking after first booking'),
                    ('consents', 'Consents'),
                    ('digital_flyer', 'Digital Flyer'),
                    ('mobile_payments_introduction', 'Mobile Payments Introduction'),
                    ('mobile_payments_migration', 'Mobile Payments Migration'),
                    ('b2b_referral', 'B2B Referral'),
                ],
                default='',
                max_length=30,
            ),
        ),
    ]
