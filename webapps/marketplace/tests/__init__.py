import datetime
from datetime import time, timedelta
from unittest.mock import patch

import braintree
import pytest
from dateutil.relativedelta import relativedelta
from model_bakery import baker

from lib.test_utils import create_subbooking
from lib.tools import tznow
from service.tests import BaseAsyncHTTPTest
from webapps.booking.enums import AppointmentStatus, AppointmentType
from webapps.booking.models import (
    Appointment,
    BookingSources,
    SubBooking,
)
from webapps.booking.tests.test_appointment_base import (
    BaseTestAppointment,
)
from webapps.booking.tests.utils import create_appointment
from webapps.boost.payment.base import TransactionResponse
from webapps.business.enums import PriceType
from webapps.business.models import (
    Business,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.staffer_name_generator import generate_user_name
from webapps.marketplace.models import MarketplaceCommission
from webapps.marketplace.tasks import set_chargeable_task
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User


def get_dummy_transaction_response(is_success=True, status=braintree.Transaction.Status.Settled):
    return TransactionResponse(
        is_success,
        status,
        1,
        0,
        'foo',
        'bar',
    )


class MarketplaceBusinessesBaseTest(BaseAsyncHTTPTest, BaseTestAppointment):
    @property
    def hour(self):
        hour = self.hour_val
        self.hour_val += 1
        return hour

    @hour.setter
    def hour(self, value):
        self.hour_val = value

    def setUp(self):
        super(BaseAsyncHTTPTest, self).setUp()  # pylint: disable=bad-super-call,bad-option-value
        # access_level='owner'
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.biz_booking_src = baker.make(
            BookingSources,
            name='xxxxx',
            app_type=BookingSources.BUSINESS_APP,
            api_key='biz_key',
            chargeable=True,
        )
        self.variant = baker.make(
            ServiceVariant,
            service=self.service,
            active=True,
            duration=relativedelta(minutes=30),
            time_slot_interval=relativedelta(minutes=15),
            type='X',
            price=10.23,
        )
        self.hour = 10
        self.date = datetime.datetime(2018, 1, 1)
        self.business.description = 'x' * 150
        self.business.save()


class BoostBookingsBaseTest(BaseTestAppointment):
    @pytest.mark.django_db
    def setUp(self):
        super().setUp()
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            client_type=BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW,
        )
        with patch('webapps.marketplace.tasks.make_sure_merchant_can_pay_for_boost_task'):
            baker.make(
                MarketplaceCommission,
                marketplace=True,
                commission=10,
                region=self.region,
                max=5,
                bottom_cap=1,
            )
            self.business.boost_status = Business.BoostStatus.ENABLED
            self.business.save()
        self.variant = baker.make(
            ServiceVariant,
            service=self.service,
            active=True,
            duration=relativedelta(minutes=30),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=10,
        )
        for i in range(4):
            booking, *_ = create_subbooking(
                booking_kws={
                    'updated_by': self.user,
                    'status': Appointment.STATUS.FINISHED if i < 3 else Appointment.STATUS.CANCELED,
                    'booked_for': bci,
                    'service_variant': self.variant,
                },
                booking_type=Appointment.TYPE.CUSTOMER,
                business=self.business,
                source=self.booking_source,
            )
            set_chargeable_task(booking.appointment.id)
