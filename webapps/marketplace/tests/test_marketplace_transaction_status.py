from model_bakery import baker
import pytest

from webapps.boost.enums import MarketplaceTransactionType
from webapps.boost.payment.base import TransactionResponse
from webapps.marketplace.models import (
    MarketplaceTransaction,
    MarketplaceTransactionStatus,
)


@pytest.mark.django_db
def test_transaction_set_status_success():
    transaction = baker.make(MarketplaceTransaction)
    tran_resp = TransactionResponse(
        is_success=True,
        id=123,
        status='xxx',
        response_code=123,
        response_text=123,
        response_type=123,
        gateway_rejection_reason=123,
    )

    transaction.set_status(tran_resp, None, [], MarketplaceTransactionType.CHARGE)
    assert MarketplaceTransactionStatus.objects.count() == 1


@pytest.mark.django_db
def test_transaction_set_status_fail():
    transaction = baker.make(MarketplaceTransaction)
    tran_resp = TransactionResponse(
        is_success=False,
        id=None,
        status='xxx',
        response_code=123,
        response_text=123,
        response_type=123,
        gateway_rejection_reason=123,
    )

    transaction.set_status(tran_resp, None, [], MarketplaceTransactionType.CHARGE)
    assert MarketplaceTransactionStatus.objects.count() == 1
