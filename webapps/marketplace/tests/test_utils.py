from datetime import datetime, timedelta
from unittest.mock import patch

import pytest
from dateutil.relativedelta import relativedelta
from dateutil.tz import gettz
from django.conf import settings
from django.test import TestCase, override_settings
from freezegun import freeze_time
from mock import MagicMock
from model_bakery import baker
from pytz import UTC

from lib.datetime_utils import min_max_range_from_date
from lib.tools import tznow
from settings.boost import BoostConfig
from webapps.billing.enums import PaymentPeriod
from webapps.billing.models import BillingCycle, BillingSubscription
from webapps.boost.baker_recipes import boosted_business_recipe
from webapps.boost.helpers import is_boost_overdue
from webapps.business.baker_recipes import business_recipe
from webapps.business.enums import BoostPaymentSource
from webapps.business.models import Business
from webapps.marketplace.utils import (
    get_billing_cycle,
    get_billing_cycles,
    get_business_id,
    get_open_billing_cycle_start_date,
)
from webapps.purchase.models import Subscription, SubscriptionListing


class TestBillingCycle(TestCase):
    def test_get_billing_cycle__billing_subscription(self):
        business = business_recipe.make(
            boost_payment_source=BoostPaymentSource.ONLINE,
            has_new_billing=True,
        )
        baker.make(
            BillingSubscription,
            business=business,
            payment_period=PaymentPeriod.ONE_MONTH.value[0],
            next_billing_date=datetime(2021, 4, 5, tzinfo=UTC),
            date_start=datetime(2021, 3, 13, tzinfo=UTC),
            balance=0,
        )

        billing_cycle = get_billing_cycle(business)
        start, end = billing_cycle
        self.assertEqual(start, datetime(2021, 3, 5, tzinfo=UTC))
        self.assertEqual(end, datetime(2021, 4, 5, tzinfo=UTC))

    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    @patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay')
    @pytest.mark.freeze_time(datetime(2021, 4, 1, tzinfo=UTC))
    def test_get_billing_cycle__subscription(self, _mock):
        business = business_recipe.make(
            boost_payment_source=BoostPaymentSource.ONLINE,
            has_new_billing=False,
        )
        baker.make(
            Subscription,
            business=business,
            current_billing_cycle_start=datetime(2021, 3, 5, tzinfo=UTC),
            current_billing_cycle_end=datetime(2021, 4, 5, tzinfo=UTC),
            source=Business.PaymentSource.PLAY,
            renewing=True,
        )

        billing_cycle = get_billing_cycle(business)
        start, _ = min_max_range_from_date(datetime(2021, 4, 1, tzinfo=UTC))
        _, end = min_max_range_from_date(datetime(2021, 4, 30, tzinfo=UTC))
        self.assertEqual(billing_cycle[0], start)
        self.assertEqual(billing_cycle[1], end)

    @pytest.mark.freeze_time(datetime(2021, 4, 1, tzinfo=UTC))
    def test_get_billing_cycle__offline(self):
        business = business_recipe.make(
            boost_payment_source=BoostPaymentSource.OFFLINE,
        )
        billing_cycle = get_billing_cycle(business)
        start, _ = min_max_range_from_date(datetime(2021, 4, 1, tzinfo=UTC))
        _, end = min_max_range_from_date(datetime(2021, 4, 30, tzinfo=UTC))
        self.assertEqual(billing_cycle[0], start)
        self.assertEqual(billing_cycle[1], end)


@pytest.mark.django_db
def test_billing_cycles():
    business = baker.make(Business, has_new_billing=True)
    tz = business.get_timezone()
    billing_cycles_dates = [
        {
            'start': datetime(2021, 2, 1, 0, 0, 0, tzinfo=tz),
            'end': datetime(2021, 3, 1, 0, 0, 0, tzinfo=tz),
        },
        {
            'start': datetime(2021, 1, 1, 0, 0, 0, tzinfo=tz),
            'end': datetime(2021, 2, 1, 0, 0, 0, tzinfo=tz),
        },
    ]

    subscription = baker.make(
        BillingSubscription, business=business, payment_period=relativedelta(months=1)
    )
    for date_ in billing_cycles_dates:
        baker.make(
            BillingCycle,
            business=business,
            date_start=date_['start'],
            date_end=date_['end'],
            subscription=subscription,
        )
    billing_cycles = get_billing_cycles(business.id)
    assert len(billing_cycles) == len(billing_cycles_dates)
    for resp_dates, dict_dates in zip(billing_cycles, billing_cycles_dates):
        assert resp_dates['start'] == dict_dates['start'].date()
        assert resp_dates['end'] == dict_dates['end'].date() - timedelta(days=1)


@pytest.mark.django_db
def test_billing_cycles_no_new_billing():
    business = baker.make(Business, has_new_billing=False)
    tz = business.get_timezone()
    subscription = baker.make(
        BillingSubscription, business=business, payment_period=relativedelta(months=1)
    )
    baker.make(
        BillingCycle,
        business=business,
        date_start=datetime(2021, 2, 1, 0, 0, 0, tzinfo=tz),
        date_end=datetime(2021, 3, 1, 0, 0, 0, tzinfo=tz),
        subscription=subscription,
    )
    assert not get_billing_cycles(business.id)


@pytest.mark.django_db
def test_empty_billing_cycles():
    business = baker.make(Business)
    assert not get_billing_cycles(business.id)


class TestHardBlockByBillingCycles(TestCase):
    tz = None

    @classmethod
    def setUpTestData(cls):
        cls.business = boosted_business_recipe.make(has_new_billing=True)
        cls.tz = cls.business.get_timezone()
        billing_cycles_dates = [
            (datetime(2022, 7, 10, tzinfo=cls.tz), datetime(2022, 8, 10, tzinfo=cls.tz)),
            (datetime(2022, 8, 10, tzinfo=cls.tz), datetime(2022, 9, 10, tzinfo=cls.tz)),
            (datetime(2022, 9, 10, tzinfo=cls.tz), datetime(2022, 10, 10, tzinfo=cls.tz)),
        ]

        subscription = baker.make(
            BillingSubscription, business=cls.business, payment_period=relativedelta(months=1)
        )
        for start_date, end_date in billing_cycles_dates:
            baker.make(
                BillingCycle,
                business=cls.business,
                date_start=start_date,
                date_end=end_date,
                subscription=subscription,
            )

    @freeze_time(datetime(2022, 10, 5, tzinfo=tz))
    def test_near_end_of_cycle(self):
        bc_start_datetime = get_open_billing_cycle_start_date(self.business)
        assert bc_start_datetime == datetime(2022, 9, 10, tzinfo=self.tz)

    @freeze_time(datetime(2022, 9, 13, tzinfo=tz))
    def test_just_after_new_cycle_started(self):
        bc_start_datetime = get_open_billing_cycle_start_date(self.business)
        assert bc_start_datetime == datetime(2022, 8, 10, tzinfo=self.tz)

    @override_settings(BOOST=BoostConfig(COUNTRY='br', CLAIM_DEADLINE=10))
    @freeze_time(datetime(2022, 9, 18, tzinfo=tz))
    def test_hard_limit_depends_on_country_config(self):
        bc_start_datetime = get_open_billing_cycle_start_date(self.business)
        assert bc_start_datetime == datetime(2022, 8, 10, tzinfo=self.tz)


class TestHardBlockByMonths(TestCase):
    tz = None

    @classmethod
    def setUpTestData(cls):
        cls.business = boosted_business_recipe.make(has_new_billing=False)
        cls.tz = cls.business.get_timezone()

    @freeze_time(datetime(2022, 10, 30, tzinfo=tz))
    def test_near_end_of_cycle(self):
        bc_start_datetime = get_open_billing_cycle_start_date(self.business)
        assert bc_start_datetime == datetime(2022, 10, 1, tzinfo=self.tz)

    @freeze_time(datetime(2022, 11, 5, tzinfo=tz))
    def test_just_after_new_cycle_started(self):
        bc_start_datetime = get_open_billing_cycle_start_date(self.business)
        assert bc_start_datetime == datetime(2022, 10, 1, tzinfo=self.tz)

    @override_settings(BOOST=BoostConfig(COUNTRY='br', CLAIM_DEADLINE=10))
    @freeze_time(datetime(2022, 11, 9, tzinfo=tz))
    def test_hard_limit_depends_on_country_config(self):
        bc_start_datetime = get_open_billing_cycle_start_date(self.business)
        assert bc_start_datetime == datetime(2022, 10, 1, tzinfo=self.tz)


@patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
class TestCalculateBillingCycleCreatedInBeginningOfMonth(TestCase):
    tz = gettz(settings.COUNTRY_CONFIG.default_time_zone)

    @classmethod
    @freeze_time(datetime(2023, 1, 1, tzinfo=tz))
    def setUpTestData(cls):
        cls.business = boosted_business_recipe.make(has_new_billing=True, time_zone_name=cls.tz)
        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            baker.make(
                Subscription,
                business=cls.business,
                start=tznow(),
                expiry=datetime(2023, 2, 4, tzinfo=cls.tz),
                product=baker.make(SubscriptionListing),
            )
        baker.make(
            BillingSubscription,
            date_start=datetime(2023, 2, 4, tzinfo=cls.tz),
            date_expiry=None,
            business=cls.business,
            payment_period=relativedelta(months=1),
            next_billing_date=datetime(2023, 3, 4, tzinfo=cls.tz),
        )
        baker.make(
            BillingCycle,
            business=cls.business,
            date_start=datetime(2023, 2, 4, tzinfo=cls.tz),
            date_end=datetime(2023, 3, 4, tzinfo=cls.tz),
        )

    @freeze_time(datetime(2023, 2, 6, 10, tzinfo=tz))
    def test_date_in_old_cycle_in_7_days_of_new_month(self):
        self.assertEqual(
            datetime(2023, 1, 1, tzinfo=self.tz), get_open_billing_cycle_start_date(self.business)
        )

    @freeze_time(datetime(2023, 2, 6, 10, tzinfo=tz))
    def test_is_visit_from_last_month_overdue_in_7_days_of_next_month(self):
        self.assertEqual(
            False,
            is_boost_overdue(
                datetime(2023, 1, 15, tzinfo=self.tz),
                self.business,
            ),
        )

    @freeze_time(datetime(2023, 2, 8, 10, tzinfo=tz))
    def test_date_in_old_cycle_after_7_days_of_month_before_deadline(self):
        self.assertEqual(
            datetime(2023, 2, 1, tzinfo=self.tz), get_open_billing_cycle_start_date(self.business)
        )

    @freeze_time(datetime(2023, 2, 8, 10, tzinfo=tz))
    def test_is_vist_from_last_month_overdue_after_7_days_of_next_month(self):
        self.assertEqual(
            True,
            is_boost_overdue(
                datetime(2023, 1, 15, tzinfo=self.tz),
                self.business,
            ),
        )

    @freeze_time(datetime(2023, 2, 8, 10, tzinfo=tz))
    def test_is_visit_from_previous_cycle_overdue_before_deadline(self):
        self.assertEqual(
            False,
            is_boost_overdue(
                datetime(2023, 2, 2, tzinfo=self.tz),
                self.business,
            ),
        )

    @freeze_time(datetime(2023, 2, 12, 10, tzinfo=tz))
    def test_date_in_new_cycle_after_7_days_of_month_after_deadline(self):
        self.assertEqual(
            datetime(2023, 2, 4, tzinfo=self.tz), get_open_billing_cycle_start_date(self.business)
        )

    @freeze_time(datetime(2023, 2, 12, 10, tzinfo=tz))
    def test_is_visit_from_old_cycle_overdue_after_deadline(self):
        self.assertEqual(
            True,
            is_boost_overdue(
                datetime(2023, 2, 2, tzinfo=self.tz),
                self.business,
            ),
        )

    @freeze_time(datetime(2023, 2, 12, 10, tzinfo=tz))
    def test_is_visit_from_new_cycle_overdue(self):
        self.assertEqual(
            False,
            is_boost_overdue(
                datetime(2023, 2, 9, tzinfo=self.tz),
                self.business,
            ),
        )


class TestCalculateBillingCycleCreatedInTheMiddleOfMonth(TestCase):
    tz = gettz(settings.COUNTRY_CONFIG.default_time_zone)

    @classmethod
    @freeze_time(datetime(2023, 1, 1, tzinfo=tz))
    def setUpTestData(cls):
        cls.business = boosted_business_recipe.make(has_new_billing=True, time_zone_name=cls.tz)
        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            baker.make(
                Subscription,
                business=cls.business,
                start=tznow(),
                expiry=datetime(2023, 2, 20, tzinfo=cls.tz),
                product=baker.make(SubscriptionListing),
            )
        baker.make(
            BillingSubscription,
            date_start=datetime(2023, 2, 20, tzinfo=cls.tz),
            date_expiry=None,
            business=cls.business,
            payment_period=relativedelta(months=1),
            next_billing_date=datetime(2023, 3, 20, tzinfo=cls.tz),
        )
        baker.make(
            BillingCycle,
            business=cls.business,
            date_start=datetime(2023, 2, 20, tzinfo=cls.tz),
            date_end=datetime(2023, 3, 20, tzinfo=cls.tz),
        )

    @freeze_time(datetime(2023, 2, 22, tzinfo=tz))
    def test_date_from_old_cycle_before_deadline(self):
        self.assertEqual(
            datetime(2023, 2, 1, tzinfo=self.tz), get_open_billing_cycle_start_date(self.business)
        )

    @freeze_time(datetime(2023, 2, 28, 10, tzinfo=tz))
    def test_date_from_new_cycle_after_deadline(self):
        self.assertEqual(
            datetime(2023, 2, 20, tzinfo=self.tz), get_open_billing_cycle_start_date(self.business)
        )

    @freeze_time(datetime(2023, 2, 22, 10, tzinfo=tz))
    def test_is_visit_from_old_cycle_overdue_before_deadline(self):
        self.assertEqual(
            False,
            is_boost_overdue(
                datetime(2023, 2, 15, tzinfo=self.tz),
                self.business,
            ),
        )

    @freeze_time(datetime(2023, 2, 28, 10, tzinfo=tz))
    def test_is_visit_from_old_cycle_overdue_after_deadline(self):
        self.assertEqual(
            True,
            is_boost_overdue(
                datetime(2023, 2, 15, tzinfo=self.tz),
                self.business,
            ),
        )

    @freeze_time(datetime(2023, 2, 20, 10, tzinfo=tz))
    def test_new_is_boost_overdue_date_in_future(self):
        self.assertEqual(
            False,
            is_boost_overdue(
                datetime(2023, 3, 25, tzinfo=self.tz),
                self.business,
            ),
        )


@pytest.mark.django_db
def test_get_business_id_db_object():
    business = baker.make(Business)
    assert get_business_id(business) == business.id


def test_get_business_id_primitive():
    assert get_business_id(123) == 123
    assert get_business_id(None) is None
