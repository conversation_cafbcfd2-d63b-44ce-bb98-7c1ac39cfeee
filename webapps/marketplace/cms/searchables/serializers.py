from rest_framework import serializers

from lib.elasticsearch.hit_serializer import HitSerializer
from webapps.marketplace.cms.elasticsearch.content import SeoCmsContentDataDocument
from webapps.marketplace.cms.elasticsearch.feature_flag import SeoFeatureFlagDocument
from webapps.marketplace.cms.elasticsearch.region_category_listing import SeoRegionCategoryDocument
from webapps.marketplace.cms.elasticsearch.region_homepage import SeoRegionHomepageDocument
from webapps.marketplace.cms.enums import SeoFeatureFlagValueType


class SeoFeatureFlagHitSerializer(HitSerializer):
    class Meta:
        document = SeoFeatureFlagDocument
        fields = ('key', 'value')

    _dynamic_field_mappings = {
        SeoFeatureFlagValueType.BOOL: serializers.BooleanField,
    }
    value = serializers.SerializerMethodField(method_name='get_value_')

    def get_value_(self, instance):
        field = self._dynamic_field_mappings.get(
            instance.get('value_type'),
            SeoFeatureFlagValueType.BOOL,
        )
        return field().to_representation(instance.get('value'))


class SeoRecommended4UHitSerializer(HitSerializer):
    class Meta:
        document = SeoCmsContentDataDocument
        fields = ('position', 'title', 'image_url', 'image_alt', 'image_title', 'target_url')


class SeoCategoryContentHitSerializer(HitSerializer):
    class Meta:
        document = SeoCmsContentDataDocument
        fields = (
            'position',
            'title',
            'body',
            'image_url',
            'image_alt',
            'image_title',
        )


class SeoRegionHomepageHitSerializer(HitSerializer):
    class Meta:
        document = SeoRegionHomepageDocument
        fields = (
            'categories',
            'region',
        )


class SeoRegionCategoryHitSerializer(HitSerializer):
    class Meta:
        document = SeoRegionCategoryDocument
        fields = ('regions', 'category_id')
