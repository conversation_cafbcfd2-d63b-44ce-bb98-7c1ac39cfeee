from datetime import date, datetime

from dateutil.tz import gettz
from django.conf import settings

from lib.feature_flag.feature.boost import BoostOverduesBasedOnCutoffCustomTestDateFlag
from lib.tools import tznow
from webapps.billing.models import BillingCycle


DEFAULT_GLOBAL_CUTOFF_DATE = date(2023, 10, 1)


def get_global_cutoff_date() -> datetime.date:
    if settings.LIVE_DEPLOYMENT:
        return DEFAULT_GLOBAL_CUTOFF_DATE
    return datetime.fromisoformat(
        BoostOverduesBasedOnCutoffCustomTestDateFlag(  # pylint: disable=unsubscriptable-object
            defaults={'cutoff_date': DEFAULT_GLOBAL_CUTOFF_DATE.isoformat()}
        )['cutoff_date']
    ).date()


def is_the_global_cutoff_date_valid() -> bool:
    cutoff_date = get_global_cutoff_date()

    country_tz = gettz(settings.COUNTRY_CONFIG.default_time_zone)
    return datetime(
        cutoff_date.year,
        cutoff_date.month,
        cutoff_date.day,
        tzinfo=country_tz,
    ) <= tznow(country_tz)


def get_cutoff_date_for_business(business_id: int) -> datetime.date:
    cutoff_date = get_global_cutoff_date()

    bc_date_start = (
        BillingCycle.objects.filter(
            date_start__date__lt=cutoff_date,
            date_end__date__gt=cutoff_date,
            business_id=business_id,
            business__has_new_billing=True,
        )
        .values_list('date_start', flat=True)
        .order_by('date_start')
        .first()
    )

    if bc_date_start:
        return bc_date_start.date()
    return cutoff_date
