# Generated by Django 2.2.13 on 2020-09-09 17:08

from django.db import migrations, models
import webapps.marketplace.models


def forwards_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    MarketplaceTransactionRow = apps.get_model('marketplace', 'MarketplaceTransactionRow')

    MarketplaceTransactionRow.objects.using(db_alias).filter(status='no show').update(
        status='no_show'
    )
    MarketplaceTransactionRow.objects.using(db_alias).filter(status='claim pending').update(
        status='claim_pending'
    )
    MarketplaceTransactionRow.objects.using(db_alias).filter(status='claim denied').update(
        status='claim_denied'
    )
    MarketplaceTransactionRow.objects.using(db_alias).filter(status='claim overdue').update(
        status='claim_overdue'
    )


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0110_add_marketplace_stages'),
    ]

    operations = [
        migrations.AlterField(
            model_name='marketplacetransactionrow',
            name='status',
            field=models.Char<PERSON>ield(
                choices=[
                    ('free_trial', 'Free trial'),
                    ('no_show', 'No show'),
                    ('claimed', 'Claimed'),
                    ('claim_pending', 'Claim pending'),
                    ('claim_denied', 'Claim denied'),
                    ('claim_overdue', 'Claim overdue'),
                    ('offline', 'Offline'),
                    ('payable', 'Payable'),
                ],
                default='payable',
                max_length=16,
            ),
        ),
        migrations.RunPython(forwards_func, reverse_code=migrations.RunPython.noop),
    ]
