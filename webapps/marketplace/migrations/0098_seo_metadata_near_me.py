# Generated by Django 2.0.13 on 2020-03-23 07:41

from django.conf import settings
from django.db import migrations, models

from country_config import Country
from webapps.marketplace.enums import (
    SEO_METADATA_TYPE__FIXED,
    SEO_METADATA_SUBTYPE__CATEGORY__LOCATION_GEO,
    SEO_METADATA_SUBTYPE__TREATMENT__LOCATION_GEO,
)

PATTERNS = {
    Country.PL: {
        SEO_METADATA_SUBTYPE__CATEGORY__LOCATION_GEO: dict(
            title_pattern='{{category|title}} {{city}}{% if neighborhood %}, '
            '{{neighborhood}}{% endif %} - Booksy',
            description_pattern='{{category|title}} w rozsądnej cenie? ® '
            '{% if neighborhood %}{{neighborhood}}, '
            '{% endif %}{{city}} ® Odkryj najlepsze '
            'salony w Twojej okolicy i zarezerwuj wizytę '
            'online przez Booksy!',
            h1_pattern='{{category|title}} {{city}}{% if neighborhood %}, '
            '{{neighborhood}}{% endif %}',
        ),
        SEO_METADATA_SUBTYPE__TREATMENT__LOCATION_GEO: dict(
            title_pattern='{{treatment|title}} {{city}}{% if neighborhood %}, '
            '{{neighborhood}}{% endif %} - Booksy',
            description_pattern='{{treatment|title}} w rozsądnej cenie? ® '
            '{% if neighborhood %}{{neighborhood}}, '
            '{% endif %}{{city}} ® Wybierz spośród '
            '{{results_number - 1}} ofert i zarezerwuj '
            'wizytę online przez Booksy!',
            h1_pattern='{{treatment|title}} {{city}}{% if neighborhood %}, '
            '{{neighborhood}}{% endif %}',
        ),
    },
    Country.US: {
        SEO_METADATA_SUBTYPE__CATEGORY__LOCATION_GEO: dict(
            title_pattern='{{category_plural|title}} in {% if neighborhood %}'
            '{{neighborhood}}, {% endif %}{{city}} - Booksy',
            description_pattern='Looking for the best {{category|lower}} in '
            '{% if neighborhood %}{{neighborhood}}, '
            '{% endif %}{{city}}? Look no further, '
            'Booksy lays them out for you!',
            h1_pattern='{{category_plural_title}} in {% if neighborhood %}'
            '{{neighborhood}} - {% endif %}{{city_abbv}}',
        ),
        SEO_METADATA_SUBTYPE__TREATMENT__LOCATION_GEO: dict(
            title_pattern='{{treatment|title}} in {% if neighborhood %}'
            '{{neighborhood}}, {% endif %}{{city}} - Booksy ',
            description_pattern='Looking for the best {{treatment|lower}} in '
            '{% if neighborhood %}{{neighborhood}}, '
            '{% endif %}{{city}} and overwhelmed by the '
            'options? Let Booksy help you decide with '
            'over {{results_number - 1}} to choose from.',
            h1_pattern='{{treatment|title}} in {% if neighborhood %}'
            '{{neighborhood}} - {% endif %}{{city_abbv}}',
        ),
    },
}


def forward_seo_metadata_near_me(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    SeoMetadata = apps.get_model('marketplace', 'SeoMetadata')
    try:
        # because we accidentally created 'LG' for all countries
        SeoMetadata.objects.using(db_alias).get(
            type=SEO_METADATA_TYPE__FIXED,
            sub_type='LG',
        ).delete()
    except SeoMetadata.DoesNotExist:
        pass

    patterns = PATTERNS.get(settings.API_COUNTRY)
    if patterns:
        for sub_type, data in patterns.items():
            SeoMetadata.objects.using(db_alias).create(
                type=SEO_METADATA_TYPE__FIXED, sub_type=sub_type, **data
            )


def backward_seo_metadata_near_me(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    patterns = PATTERNS.get(settings.API_COUNTRY)
    if patterns:
        SeoMetadata = apps.get_model('marketplace', 'SeoMetadata')
        for sub_type in patterns:
            try:
                SeoMetadata.objects.using(db_alias).get(
                    type=SEO_METADATA_TYPE__FIXED,
                    sub_type=sub_type,
                ).delete()
            except SeoMetadata.DoesNotExist:
                pass

        SeoMetadata.objects.using(db_alias).create(
            type=SEO_METADATA_TYPE__FIXED,
            sub_type='LG',
            title_pattern='Nearest {{category}}',
            description_pattern='Check {{category_plural}} Nearby [distance & '
            'time estimation]. Book quickly appointment '
            'Near You!',
            h1_pattern='Nearest {{category}}',
        )


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0097_merge_20200129_1010'),
    ]

    operations = [
        migrations.AlterField(
            model_name='seometadata',
            name='sub_type',
            field=models.CharField(
                choices=[
                    ('CC', 'Category + City'),
                    ('CD', 'Category + District'),
                    ('TC', 'Treatment + City'),
                    ('TD', 'Treatment + District'),
                    ('DL', 'Default listing'),
                    ('T', 'Treatment'),
                    ('C', 'Category'),
                    ('CG', 'Category + Location Geo'),
                    ('TG', 'Treatment + Location Geo'),
                ],
                max_length=2,
            ),
        ),
        migrations.RunPython(
            forward_seo_metadata_near_me,
            reverse_code=backward_seo_metadata_near_me,
        ),
    ]
