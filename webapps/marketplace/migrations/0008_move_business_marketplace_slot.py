# Generated by Django 1.11.11 on 2018-06-20 14:44
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import webapps.images.tools


class Migration(migrations.Migration):

    dependencies = [
        ('structure', '0006_auto_20180221_1738'),
        ('business', '0141_move_business_marketplace_slot'),
        ('marketplace', '0007_auto_20180606_1325'),
    ]

    state_operations = [
        migrations.CreateModel(
            name='BusinessMarketPlaceSlot',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                (
                    'image',
                    models.ImageField(
                        blank=True, null=True, upload_to=webapps.images.tools.get_mp_slots_path
                    ),
                ),
                ('title', models.CharField(max_length=120)),
                ('description', models.CharField(max_length=1200)),
                (
                    'slot_number',
                    models.PositiveSmallIntegerField(
                        validators=[django.core.validators.MaxValueValidator(7)]
                    ),
                ),
                ('redirect_url', models.URLField(blank=True, null=True)),
                ('button_text', models.CharField(default='Book appointment', max_length=120)),
                (
                    'business',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='marketplace_slot',
                        to='business.Business',
                    ),
                ),
                (
                    'region',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='marketplace_region_slot',
                        to='structure.Region',
                    ),
                ),
            ],
            options={
                'db_table': 'marketplace_businessmarketplaceslot',
                'ordering': ['region_id', 'slot_number'],
                'verbose_name': 'Business Marketplace Slot',
                'verbose_name_plural': 'Business Marketplace Slots',
            },
        ),
        migrations.AlterUniqueTogether(
            name='businessmarketplaceslot',
            unique_together=set([('region', 'slot_number')]),
        ),
    ]

    operations = [migrations.SeparateDatabaseAndState(state_operations=state_operations)]
