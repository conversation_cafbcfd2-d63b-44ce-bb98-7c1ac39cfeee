# Generated by Django 2.0.13 on 2019-07-05 14:30
from django.db import migrations


def forwards_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias

    stages = (
        ('marketplace_notification_sent', True, 1, 'MP-HINT-1'),
        ('marketplace_notification_sent', False, 2, 'MP-HINT-1'),
        ('marketplace_notification_clicked', True, 3, 'MP-HINT-1'),
        ('marketplace_notification_clicked', False, 4, 'MP-HINT-1'),
        ('waitinglist', True, 5, 'MP - LET - ME - KNOW'),
        ('commision_enabled_notification_sent', True, 6, 'MP-REGION-ENABLED'),
        ('commision_enabled_notification_sent', False, 7, 'MP-REGION-ENABLED'),
        ('value_1_seen', False, 8, 'INT-VALUE1'),
        ('value_2_seen', False, 9, 'INT-VALUE2'),
        ('value_1_seen', True, 10, 'INT-VALUE1'),
        ('value_2_seen', True, 11, 'INT-VALUE2'),
        ('boost_enable_seen', True, 12, 'INT-ACCEPT'),
        ('boost_enable_seen', False, 13, 'INT-ACCEPT'),
        ('payment_message_sent', False, 14, 'INT-MAIL'),
        ('payment_message_sent', True, 15, 'INT-MAIL'),
        ('form_opened', False, 16, 'INT-FORM'),
        ('form_opened', True, 17, 'INT-FORM'),
        ('trial_conditions', True, 18, 'MP - ENABLE - 5 - FOR - FREE'),
        ('regular_conditions_no_trial', False, 19, 'MP - ENABLE - REGULAR'),
        ('todo_list', True, 20, 'MP - BUSINESS - TODO'),
        ('todo_list', False, 21, 'MP - BUSINESS - TODO'),
        ('visibility_boost_todolist_completed', True, 22, 'INT-TODOLIST'),
        ('visibility_boost_todolist_completed', False, 23, 'INT-TODOLIST'),
        ('trial_enabled', False, 24, 'MP - JUST - ENABLED - 0'),
        ('trial_finished', False, 25, 'MP - ONBOARDING - 5 - DELIVERED'),
        ('regular_conditions_trial', True, 26, 'MP - ENABLE - REGULAR'),
        ('marketplace_enabled', True, 27, 'MP - JUST - ENABLED - 0'),
        ('marketplace_enabled', False, 28, 'MP - JUST - ENABLED - 0'),
        ('marketplace_disable_seen', True, 29, 'MP-TURNOFF-CONFIRM'),
        (
            'marketplace_disable_confirmed',
            False,
            30,
            'MP - TURNOFF - CONFIRM (button end my promotion clicked)',
        ),
        ('marketplace_disable_seen', False, 31, 'MP-TURNOFF-CONFIRM'),
        (
            'marketplace_disable_confirmed',
            True,
            32,
            'MP-TURNOFF-CONFIRM (button end my promotion clicked)',
        ),
        ('visibility_boost_todolist_failed', True, 33, 'INT-TODOLIST'),
        ('visibility_boost_todolist_failed', False, 34, 'INT-TODOLIST'),
    )

    MarketplaceStage = apps.get_model('marketplace', 'MarketplaceStage')

    for stage_name, marketplace_stage, stage_cardinality, view_name in stages:
        ms, created = MarketplaceStage.objects.using(db_alias).get_or_create(
            stage_name=stage_name,
            marketplace_stage=marketplace_stage,
            view_name=view_name,
            defaults={'stage_cardinality': stage_cardinality},
        )
        if not created:
            ms.stage_cardinality = stage_cardinality
            ms.save()


def backwards_func(apps, schema_editor):
    pass


class Migration(migrations.Migration):
    dependencies = [
        ('marketplace', '0073_auto_20190618_1303'),
    ]

    operations = [
        migrations.RunPython(forwards_func, reverse_code=backwards_func),
    ]
