# Generated by Django 1.11.17 on 2019-01-24 07:17
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0055_merge_20181102_1341'),
    ]

    operations = [
        migrations.AlterField(
            model_name='marketplacetransaction',
            name='business',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name='marketplace_transactions',
                to='business.Business',
            ),
        ),
        migrations.AlterField(
            model_name='marketplacetransactionrow',
            name='booking',
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.PROTECT,
                related_name='marketplace_transactions',
                to='booking.Booking',
            ),
        ),
        migrations.AlterField(
            model_name='marketplacetransactionrow',
            name='business_promotion',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name='marketplace_transactions',
                to='business.BusinessPromotion',
            ),
        ),
        migrations.AlterField(
            model_name='marketplacetransactionrow',
            name='transaction',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name='rows',
                to='marketplace.MarketplaceTransaction',
            ),
        ),
        migrations.AlterField(
            model_name='marketplacetransactionstatus',
            name='transaction',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name='statuses',
                to='marketplace.MarketplaceTransaction',
            ),
        ),
        migrations.AlterField(
            model_name='matchtreatmentbatch',
            name='moderator',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='moderated_batches',
                to='user.User',
            ),
        ),
        migrations.AlterField(
            model_name='matchtreatmentlog',
            name='treatment',
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='business.BusinessCategory',
            ),
        ),
    ]
