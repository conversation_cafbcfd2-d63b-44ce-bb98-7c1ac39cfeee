from rest_framework import serializers
from rest_framework_dataclasses.serializers import DataclassSerializer

from webapps.instagram_integration.data_types import BusinessInstagramImage


class InstagramAuthSerializer(serializers.Serializer):
    code = serializers.CharField()
    redirect_uri = serializers.URLField()


class AccessTokenSerializer(serializers.Serializer):
    access_token = serializers.CharField()
    limit = serializers.IntegerField(required=False)
    page_token = serializers.CharField(required=False)


class InstagramImageSerializer(DataclassSerializer):
    class Meta:
        dataclass = BusinessInstagramImage

    instagram_post_permalink = serializers.CharField(default=None)
    instagram_image_caption = serializers.Char<PERSON>ield(default=None)


class InstagramImageRetrieveSerializer(serializers.Serializer):
    images = InstagramImageSerializer(many=True)
    next_page = serializers.CharField(required=False)
    previous_page = serializers.Char<PERSON>ield(required=False)


class InstagramImageUploadSerializer(serializers.Serializer):
    image = InstagramImageSerializer()
    category = serializers.CharField()
    inspiration_categories = serializers.ListField(child=serializers.IntegerField())


class InstagramProfileSerializer(serializers.Serializer):
    username = serializers.CharField(read_only=True)
