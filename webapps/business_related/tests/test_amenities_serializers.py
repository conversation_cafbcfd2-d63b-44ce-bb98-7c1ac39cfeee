# pylint: disable=no-value-for-parameter

import pytest

from webapps.business_related.enums import AmenityEnum
from webapps.business_related.serializers import AmenitiesSerializer


@pytest.mark.django_db
def test_objects_as_list_of_objects(business):
    serializer = AmenitiesSerializer(instance=business.amenities)

    expected_keys = {'key', 'value', 'label'}
    assert all(item.keys() == expected_keys for item in serializer.data_as_list)


@pytest.mark.django_db
def test_update_amenities_from_list(business):
    assert not business.amenities.animals
    assert not business.amenities.wifi

    serializer = AmenitiesSerializer.from_request(
        instance=business.amenities,
        data={
            'amenities': [
                {'key': 'animals', 'value': True},
                {'key': 'wifi', 'value': True},
            ],
        },
    )

    assert serializer.is_valid(), serializer.errors
    serializer.save()

    assert business.amenities.animals
    assert business.amenities.wifi
    assert serializer.data['animals']
    assert serializer.data['wifi']

    animals = AmenityEnum('animals')
    wifi = AmenityEnum('wifi')

    for enum_ in (animals, wifi):
        data = {'key': enum_.value, 'label': enum_.label, 'value': True}
        assert data in serializer.data_as_list
