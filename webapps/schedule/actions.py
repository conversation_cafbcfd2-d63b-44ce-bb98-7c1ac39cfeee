from django.conf import settings
from django.dispatch import receiver

from webapps.booking.models import Appointment
from webapps.business.models import Business
from webapps.schedule.notifications import (
    OpeningHoursExtendedSuggestion,
    TimeOffAddedSuggestion,
)
from webapps.schedule.ports import get_business_default_hours
from webapps.schedule.signals import before_business_hours_update, before_schedule_update
from webapps.schedule.tools import (
    WorkingHoursUpdater,
    is_opening_hours_extension,
)
from webapps.schedule.working_hours import BulkTimeOffsCreate


if settings.POPUP_NOTIFICATIONS_ENABLED:
    if settings.POPUP_PHASE2:

        @receiver(before_schedule_update)
        def working_hours_suggest_on_schedule_update(  # pylint: disable=unused-argument
            sender,
            updater: BulkTimeOffsCreate | WorkingHoursUpdater = None,
            **kwargs,
        ):
            try:
                business = updater.business
            except AttributeError:
                business = Business.objects.get(pk=updater.business_id)
            if _business_with_10cb(business):
                if (
                    hasattr(updater, 'is_working_hours_extension')
                    and updater.is_working_hours_extension()
                ):
                    OpeningHoursExtendedSuggestion(updater.business).async_send()

        @receiver(before_schedule_update)
        def timeoff_suggest(  # pylint: disable=unused-argument
            sender,
            updater: BulkTimeOffsCreate | WorkingHoursUpdater = None,
            **kwargs,
        ):
            try:
                business = updater.business
            except AttributeError:
                business = Business.objects.get(pk=updater.business_id)
            if _business_with_10cb(business):
                if updater.is_holiday_added():
                    TimeOffAddedSuggestion(business).async_send()

        @receiver(before_business_hours_update)
        def working_hours_suggest_on_business_update(  # pylint: disable=unused-argument
            sender,
            business: Business = None,
            business_hours=None,
            **kwargs,
        ):
            if _business_with_10cb(business):
                if is_opening_hours_extension(
                    get_business_default_hours(business_id=business.id),
                    business_hours,
                ):
                    OpeningHoursExtendedSuggestion(business).async_send()

        def _business_with_10cb(business):
            cb_count = business.appointments.filter(
                status=Appointment.STATUS.FINISHED,
                type=Appointment.TYPE.CUSTOMER,
            ).count()
            return cb_count >= 10
