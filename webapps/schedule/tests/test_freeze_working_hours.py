from datetime import date, datetime, time

import pytest
from dateutil.tz import gettz

from lib.test_utils import timezone_for_tests
from webapps.schedule.models import Schedule
from webapps.schedule.ports import (
    set_business_default_hours,
    set_resource_default_hours,
)
from webapps.schedule.working_hours import (
    freeze_business_opening_hours,
    freeze_resources_working_hours,
)


TESTTZ = gettz(timezone_for_tests())


@pytest.mark.freeze_time(datetime(2020, 6, 9, 11, 0, tzinfo=TESTTZ))
def test_freeze_business_opening_hours(
    business,
    business_opening_hours,
    schedule_recipe,
):
    start_date = date(2020, 6, 9)
    middle_date = date(2020, 6, 15)
    end_date = date(2020, 6, 19)

    # make a deleted schedule context
    schedule = schedule_recipe.make(date=middle_date, hours=[(time(12), time(20))])
    schedule.soft_delete()

    schedule_recipe.make(date=middle_date, hours=[(time(12), time(20))])
    freeze_business_opening_hours(
        business_id=business.id,
        start_date=start_date,
        end_date=end_date,
    )

    default_hours = business_opening_hours.hours[1]  # monday

    # freeze start date
    assert (
        Schedule.objects.filter(
            business=business,
            resource__isnull=True,
            date=start_date,
        )
        .first()
        .hours
        == default_hours
    )

    # freeze end date
    assert (
        Schedule.objects.filter(
            business=business,
            resource__isnull=True,
            date=end_date,
        )
        .first()
        .hours
        == default_hours
    )

    # preserved existing schedule
    assert Schedule.objects.filter(
        business=business,
        resource__isnull=True,
        date=middle_date,
    ).first().hours == [(time(12), time(20))]


@pytest.mark.freeze_time(datetime(2020, 6, 9, 11, 0, tzinfo=TESTTZ))
def test_freeze_waiting_hours(business, business_opening_hours):
    start_date = date(2020, 6, 9)
    middle_date = date(2020, 6, 15)
    end_date = date(2020, 6, 19)

    default_hours = business_opening_hours.hours[1]

    set_business_default_hours(
        business_id=business.id,
        hours={1: [(time(11), time(19))]},
        tz=business.get_timezone(),
        apply_from=middle_date,
        partial=True,
    )

    freeze_business_opening_hours(
        business_id=business.id,
        start_date=start_date,
        end_date=end_date,
    )

    assert (
        Schedule.objects.filter(
            business=business, resource__isnull=True, date=date(2020, 6, 12)  # friday
        )
        .first()
        .hours
        == default_hours
    )

    assert Schedule.objects.filter(
        business=business, resource__isnull=True, date=date(2020, 6, 15)  # new monday
    ).first().hours == [(time(11), time(19))]


@pytest.mark.freeze_time(datetime(2020, 6, 9, 11, 0, tzinfo=TESTTZ))
def test_freeze_resource_working_hours(
    business,
    staffer,
    staffer_recipe,
    resource_working_hours,
    schedule_recipe,
):
    staffer2 = staffer_recipe.make()
    # must be working days, otherwise the test fails
    start_date = date(2020, 6, 9)
    sunday_date = date(2020, 6, 14)
    middle_date = date(2020, 6, 15)
    end_date = date(2020, 6, 19)

    # make a deleted schedule context
    schedule = schedule_recipe.make(
        date=middle_date,
        hours=[],
        resource=staffer,
    )
    schedule.soft_delete()

    schedule_recipe.make(
        date=middle_date,
        hours=[(time(12), time(20))],
        resource=staffer,
    )
    freeze_resources_working_hours(
        resource_ids=[staffer.id, staffer2.id],
        business_id=business.id,
        start_date=start_date,
        end_date=end_date,
    )

    default_hours = resource_working_hours.hours[1]

    # freeze start date
    assert (
        Schedule.objects.filter(
            business=business,
            resource=staffer,
            date=start_date,
        )
        .first()
        .hours
        == default_hours
    )

    assert (
        Schedule.objects.filter(
            business=business,
            resource=staffer,
            date=sunday_date,
        )
        .first()
        .hours
        == []
    )

    # freeze end date
    assert (
        Schedule.objects.filter(
            business=business,
            resource=staffer,
            date=end_date,
        )
        .first()
        .hours
        == default_hours
    )

    # preserved existing schedule
    assert Schedule.objects.filter(
        business=business,
        resource=staffer,
        date=middle_date,
    ).first().hours == [(time(12), time(20))]


@pytest.mark.freeze_time(datetime(2020, 6, 9, 11, 0, tzinfo=TESTTZ))
def test_resource_apply_from(staffer, business, resource_working_hours):
    start_date = date(2020, 6, 9)
    middle_date = date(2020, 6, 15)  # monday
    end_date = date(2020, 6, 19)

    default_hours = resource_working_hours.hours[1]

    set_resource_default_hours(
        business_id=business.id,
        resource_id=staffer.id,
        hours={1: [(time(11), time(19))]},
        tz=business.get_timezone(),
        apply_from=middle_date,
    )

    freeze_resources_working_hours(
        resource_ids=[staffer.id],
        business_id=business.id,
        start_date=start_date,
        end_date=end_date,
    )

    assert (
        Schedule.objects.filter(
            business=business, resource=staffer, date=date(2020, 6, 12)  # friday
        )
        .first()
        .hours
        == default_hours
    )

    assert Schedule.objects.filter(
        business=business,
        resource=staffer,
        date=middle_date,
    ).first().hours == [(time(11), time(19))]
