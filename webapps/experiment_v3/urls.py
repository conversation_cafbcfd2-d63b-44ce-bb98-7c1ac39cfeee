from django.urls import path

from webapps.experiment_v3.views.variant_for_user import DeprecatedExperimentVariantForUserView
from webapps.experiment_v3.views.variant_for_user_or_fingerprint import (
    DeprecatedExperimentVariantForUserOrFingerprintView,
)

urlpatterns = [
    path(
        '<str:experiment_name>/variant_for_user/',
        DeprecatedExperimentVariantForUserView.as_view(),
        name='deprecated_experiment_variant_for_user',
    ),
    path(
        '<str:experiment_name>/variant_for_user_or_fingerprint/',
        DeprecatedExperimentVariantForUserOrFingerprintView.as_view(),
        name='deprecated_experiment_variant_for_user_or_fingerprint',
    ),
]
