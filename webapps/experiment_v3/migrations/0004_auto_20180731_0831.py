# Generated by Django 1.11.11 on 2018-07-31 08:31
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('experiment_v3', '0003_migrate_old_experiments'),
    ]

    operations = [
        migrations.CreateModel(
            name='Switch',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                (
                    'name',
                    models.CharField(
                        max_length=64,
                        unique=True,
                        validators=[django.core.validators.RegexValidator(regex='^[a-z0-9_]+$')],
                    ),
                ),
                ('active', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.AlterField(
            model_name='businessregion',
            name='experiment',
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='experiment_v3.Experiment',
            ),
        ),
    ]
