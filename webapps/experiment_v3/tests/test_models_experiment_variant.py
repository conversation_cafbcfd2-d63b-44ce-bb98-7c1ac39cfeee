import pytest

from webapps.experiment_v3.models import (
    ExperimentVariant,
    Experiment,
)


@pytest.mark.parametrize('control_group, name', ((False, 'a'), (True, None)))
def test_usable_name(control_group, name):
    var = ExperimentVariant(
        control_group=control_group,
        name='a',
        experiment=Experiment(name='a'),
    )
    assert var.usable_name == name


def test_to_json():
    var = ExperimentVariant(
        control_group=False,
        name='a',
        experiment=Experiment(name='a'),
        weight=0.7,
    )

    assert var.to_json() == {
        'name': 'a',
        'weight': 0.7,
        'control_group': False,
        'usable_name': 'a',
    }
