import pytest

from django.test.utils import TestCase

from webapps.business.baker_recipes import business_recipe, staffer_recipe
from webapps.experiment_v3.filters.business import (
    DoNotHaveNoShowProtection,
    MinimumActiveStaffersFilter,
)


def test_raises_if_required_key_is_missing():
    with pytest.raises(TypeError) as exc:
        MinimumActiveStaffersFilter()
    assert MinimumActiveStaffersFilter.min_staffers_key in str(exc.value)


@pytest.mark.django_db
def test_returns_false_if_relation_is_none():
    assert (
        MinimumActiveStaffersFilter(
            relation_id=None,
            cfg={'minimum_active_staffers': 10},
        ).allowed()
        is False
    )


@pytest.mark.django_db
def test_returns_true_if_has_enough_active_staffers():
    business = business_recipe.make()
    staffer_recipe.make(business=business, _quantity=2)

    assert (
        MinimumActiveStaffersFilter(
            relation_id=business.id,
            cfg={'minimum_active_staffers': 2},
        ).allowed()
        is True
    )

    assert (
        MinimumActiveStaffersFilter(
            relation_id=business.id,
            cfg={'minimum_active_staffers': 3},
        ).allowed()
        is False
    )


class TestDoNotHaveNoShowProtection(TestCase):

    @pytest.mark.django_db
    def test_return_true_for_basic_business(self):
        business = business_recipe.make()

        assert DoNotHaveNoShowProtection(relation_id=business.id, cfg=None).allowed()
