from lib.enums import StrChoicesEnum
from webapps.experiment_v3.exp.base import BaseExperimentV3


class ConsentAdyenStripeCalendarTextExperiment(BaseExperimentV3):
    class Variants(StrChoicesEnum):
        VARIANT_A = 'variant_A', 'variant_A'
        VARIANT_B = 'variant_B', 'variant_B'
        VARIANT_C = 'variant_C', 'variant_C'

    name = 'payment_processor_update'
    config = {
        'active': False,
        'variants': [
            {
                'name': Variants.VARIANT_A.value,
                'weight': 1,
                'control_group': True,
            },
            {
                'name': Variants.VARIANT_B.value,
                'weight': 1,
            },
            {
                'name': Variants.VARIANT_C.value,
                'weight': 1,
            },
        ],
    }
