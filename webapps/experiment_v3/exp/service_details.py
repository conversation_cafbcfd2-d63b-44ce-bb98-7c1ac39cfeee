from lib.enums import StrEnum
from webapps.experiment_v3.exp.base import BaseExperimentV3, UniversalControlGroupExperimentMixin
from webapps.experiment_v3.filters.not_null_relation import NotNullRelationFilter


class ServiceDetailsExperiment(UniversalControlGroupExperimentMixin, BaseExperimentV3):
    """
    Experiment on the customer apps to show service details directly on listing.

    Variant A is the most basic implementation - extra modal, no pagination, no staff ratings.
    """

    class Variant(StrEnum):
        CONTROL = 'control'
        A = 'a'

    name = 'service_details'
    config = {
        'active': False,
        'variants': [
            {
                'name': Variant.CONTROL,
                'control_group': True,
                'weight': 0.8,
            },
            {
                'name': Variant.A,
                'weight': 0.2,
            },
        ],
    }
    filters = [
        (NotNullRelationFilter, None),
    ]

    @property
    def related_user_id(self):
        return self.relation_id
