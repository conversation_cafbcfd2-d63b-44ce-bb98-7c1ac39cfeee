from datetime import timedelta

from lib.enums import StrEnum
from lib.tools import tznow

from webapps.experiment_v3.exp.base import BaseExperimentV3
from webapps.experiment_v3.messages import BusinessExperimentDrawnMessage


class HintAndWalkthroughExperiment(BaseExperimentV3):
    class Variants(StrEnum):
        CONTROL = 'control'
        VERSION_A = 'version_A'

    name = 'hint_and_walkthrough_onboarding'
    config = {
        'active': False,
        'variants': [
            {
                'name': Variants.CONTROL,
                'weight': 3,
                'control_group': True,
            },
            {
                'name': Variants.VERSION_A,
                'weight': 7,
            },
        ],
    }

    # below BaseExperimentV3 methods with changed default values

    def get_variant_and_joined_before(
        self,
        draw=True,
        ignore_experiment_activity=False,
        return_usable_name=True,
    ):
        from webapps.business.models import Business

        business = Business.objects.get(pk=self.relation_id)

        if not self.is_business_created_in_past_6_days(business):
            return None, None

        variant, joined_before = super().get_variant_and_joined_before(
            draw=draw,
            ignore_experiment_activity=ignore_experiment_activity,
            return_usable_name=return_usable_name,
        )

        if variant and variant != self.Variants.CONTROL and not joined_before:
            BusinessExperimentDrawnMessage(
                business, context={'experiment_name': self.name, 'variant_name': variant}
            ).publish()

        return variant, joined_before

    @staticmethod
    def is_business_created_in_past_6_days(business) -> bool:
        return business.created >= tznow() - timedelta(days=6)
