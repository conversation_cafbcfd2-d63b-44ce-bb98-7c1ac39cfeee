from lib.enums import StrEnum
from webapps.experiment_v3.exp.base import BaseExperimentV3, UniversalControlGroupExperimentMixin
from webapps.experiment_v3.filters.not_null_relation import NotNullRelationFilter


class NewMyBooksyLayoutExperiment(UniversalControlGroupExperimentMixin, BaseExperimentV3):
    """
    Evaluate redesign of my_booksy page

    There are 4 variant to allow different probabilities for different users,
    but there are only 2 variants that can be explicitly returned.
    """

    class Variant(StrEnum):
        CONTROL_FOR_OLD_USER = 'control_for_old_user'
        CONTROL_FOR_NEW_USER = 'control_for_new_user'
        A_FOR_OLD_USER = 'a_for_old_user'
        A_FOR_NEW_USER = 'a_for_new_user'

    name = 'new_my_booksy_layout'
    config = {
        'active': False,
        'variants': [
            {
                'name': Variant.CONTROL_FOR_OLD_USER,
                'control_group': True,
                'weight': 95,
            },
            {
                'name': Variant.A_FOR_OLD_USER,
                'weight': 5,
            },
            {
                'name': Variant.CONTROL_FOR_NEW_USER,
                'control_group': True,
                'weight': 70,
            },
            {
                'name': Variant.A_FOR_NEW_USER,
                'weight': 30,
            },
        ],
    }
    filters = [
        (NotNullRelationFilter, None),
    ]

    class ReturnableVariant(StrEnum):
        CONTROL = 'control'
        A = 'a'

    RETURNABLE_VARIANT_MAPPING = {
        Variant.CONTROL_FOR_OLD_USER: ReturnableVariant.CONTROL,
        Variant.CONTROL_FOR_NEW_USER: ReturnableVariant.CONTROL,
        Variant.A_FOR_OLD_USER: ReturnableVariant.A,
        Variant.A_FOR_NEW_USER: ReturnableVariant.A,
    }

    def get_variant_and_joined_before(
        self,
        draw=True,
        ignore_experiment_activity=False,
        return_usable_name=True,
    ):
        variant, joined_before = super().get_variant_and_joined_before(
            draw=draw,
            ignore_experiment_activity=ignore_experiment_activity,
            return_usable_name=return_usable_name,
        )
        returnable_variant = self.RETURNABLE_VARIANT_MAPPING.get(variant)
        return returnable_variant, joined_before

    OLD_USER_VARIANTS = [
        Variant.CONTROL_FOR_OLD_USER,
        Variant.A_FOR_OLD_USER,
    ]
    NEW_USER_VARIANTS = [
        Variant.CONTROL_FOR_NEW_USER,
        Variant.A_FOR_NEW_USER,
    ]

    def _get_variants_conf(self):
        all_variants_conf = super()._get_variants_conf()
        valid_variants = self.NEW_USER_VARIANTS if self._is_new_user() else self.OLD_USER_VARIANTS
        return [
            variant_conf
            for variant_conf in all_variants_conf
            if variant_conf['name'] in valid_variants
        ]

    def _is_new_user(self):
        from webapps.user.models import User

        date_boundary = self._proxy.experiment_object.start_time
        if not date_boundary:
            return False

        try:
            user = User.objects.only('date_joined').get(id=self.relation_id)
        except User.DoesNotExist:
            return False

        return date_boundary <= user.date_joined

    @property
    def related_user_id(self):
        return self.relation_id

    def is_enabled(self, draw=False):
        return self.get_variant(draw=draw) == self.ReturnableVariant.A
