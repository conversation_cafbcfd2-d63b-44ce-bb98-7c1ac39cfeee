from webapps.experiment_v3.filters import BaseFilter


class MinimumActiveStaffersFilter(BaseFilter):
    """Applies a filter to experiments involving a 'business_id' relation field. This filter
    permits only those businesses that have a specified number of active staff members.
    """

    min_staffers_key = 'minimum_active_staffers'
    required_cfg_keys = [min_staffers_key]

    def allowed(self):
        if self.relation_id is None:
            return False

        from webapps.business.models import Resource  # circular import

        minimum_active_staffers = self.cfg[self.min_staffers_key]
        active_staffers = Resource.objects.filter(
            type=Resource.STAFF,
            business_id=self.relation_id,
            active=True,
        ).count()
        if active_staffers < minimum_active_staffers:
            return False
        return True


class DoNotHaveNoShowProtection(BaseFilter):
    def allowed(self):
        if self.relation_id is None:
            return False

        from webapps.pos.utils import is_no_show_protection_active

        return not is_no_show_protection_active(self.relation_id)
