class BaseFilter:
    order = 99

    # List or required keys.
    # Validation will not pass, if any of these keys is missing.
    required_cfg_keys = []

    def __init__(self, exp=None, relation_id=None, cfg=None):
        self.exp = exp
        self.relation_id = relation_id
        self.cfg = cfg or {}
        self.validate_cfg()

    @classmethod
    def initialize(cls, cfg):
        """
        Initialize filter on app startup
        """

    def validate_cfg(self):
        """
        Validates given config

        :raise TypeError: if config dict is invalid
        """
        for key in self.required_cfg_keys:
            if key not in self.cfg:
                raise TypeError(f'required key "{key}" is missing in filter configuration')

    def allowed(self):
        """
        :rtype: bool
        :return: Is object allowed in experiment
        """
        raise NotImplementedError()
