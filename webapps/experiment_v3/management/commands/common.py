import urllib.parse
from lib.deeplink import BranchIOAppTypes, read_deeplink_details


def get_app_type_and_read_deeplink_details(branchio_url):
    parsed_url = urllib.parse.urlparse(branchio_url)
    if parsed_url.netloc == 'dl.booksy.com':
        app_type = BranchIOAppTypes.BUSINESS
    elif parsed_url.netloc == 'cdl.booksy.com':
        app_type = BranchIOAppTypes.CUSTOMER
    else:
        app_type = BranchIOAppTypes.BOOKSY_BUSINESS_FRONTDESK
    return read_deeplink_details(
        app_type=app_type,
        deeplink_url=branchio_url,
    )


def read_and_print_deeplink_details(branchio_url):
    deeplink_details = get_app_type_and_read_deeplink_details(branchio_url)
    print(f"Deeplink ({branchio_url}) details:\n{deeplink_details}")
    return deeplink_details
