# Generated by Django 2.0.13 on 2019-07-17 10:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0235_merge_20190625_1318'),
        ('warehouse', '0017_merge_20190712_1314'),
    ]

    operations = [
        migrations.CreateModel(
            name='Supply',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('supplier_name', models.CharField(blank=True, max_length=200, null=True)),
                ('supply_date', models.DateField(null=True)),
                ('numbers_from_suppliers', models.CharField(blank=True, max_length=100)),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='supplies',
                        to='business.Business',
                    ),
                ),
                (
                    'staffer',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='supplies',
                        to='business.Resource',
                    ),
                ),
                (
                    'supplier',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='supplies',
                        to='warehouse.Supplier',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SupplyRow',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('commodity_name', models.CharField(blank=True, max_length=200, null=True)),
                ('ordered_quantity', models.IntegerField(default=1)),
                ('delivered_quantity', models.IntegerField(null=True)),
                ('unit_price', models.DecimalField(decimal_places=3, max_digits=10, null=True)),
                (
                    'status',
                    models.CharField(
                        choices=[('p', 'Pending'), ('a', 'Accepted')], default='p', max_length=1
                    ),
                ),
                (
                    'commodity',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='warehouse.Commodity',
                    ),
                ),
                (
                    'supply',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='rows',
                        to='warehouse.Supply',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
        migrations.RemoveField(
            model_name='warehouse',
            name='history_change',
        ),
    ]
