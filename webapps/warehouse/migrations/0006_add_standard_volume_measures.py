from django.db import migrations

from webapps.warehouse.volume_measures import STANDARD_VOLUME_MEASURES


def add_volume_measures(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    VolumeMeasure = apps.get_model('warehouse', 'VolumeMeasure')
    measures = [VolumeMeasure(**vm_data) for vm_data in STANDARD_VOLUME_MEASURES]
    VolumeMeasure.objects.using(db_alias).bulk_create(measures)


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0005_extend_volume_measure'),
    ]

    operations = [
        migrations.RunPython(
            add_volume_measures,
            migrations.RunPython.noop,
        ),
    ]
