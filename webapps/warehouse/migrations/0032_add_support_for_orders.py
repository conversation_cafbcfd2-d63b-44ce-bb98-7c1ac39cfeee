# Generated by Django 2.0.13 on 2019-08-14 14:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('warehouse', '0031_merge_20190813_1502'),
    ]

    operations = [
        migrations.RenameField(
            model_name='supplyrow',
            old_name='ordered_quantity',
            new_name='quantity',
        ),
        migrations.RemoveField(
            model_name='supplyrow',
            name='accepting_staffer',
        ),
        migrations.RemoveField(
            model_name='supplyrow',
            name='delivered_quantity',
        ),
        migrations.RemoveField(
            model_name='supplyrow',
            name='status',
        ),
        migrations.RemoveField(
            model_name='warehousedocument',
            name='note',
        ),
        migrations.AddField(
            model_name='basewarehousedocument',
            name='note',
            field=models.TextField(blank=True),
        ),
        migrations.RemoveField(
            model_name='supply',
            name='supply_date',
        ),
        migrations.AddField(
            model_name='supply',
            name='order',
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name='supply',
                to='warehouse.Supply',
            ),
        ),
        migrations.AlterField(
            model_name='basewarehousedocument',
            name='type',
            field=models.CharField(
                choices=[
                    ('WZ', 'Stock issue confirmation'),
                    ('RW', 'Internal expenditure'),
                    ('MM', 'Inter stock transfer'),
                    ('PZ', 'External stock reception'),
                    ('ORD', 'Order'),
                ],
                max_length=3,
            ),
        ),
    ]
