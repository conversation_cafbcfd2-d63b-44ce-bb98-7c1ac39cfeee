# Generated by Django 4.1.10 on 2023-12-14 10:56

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('best_of_booksy', '0001_initial'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='bestofbooksyaward',
            name='name',
            field=models.CharField(
                choices=[
                    ('most_buzz', 'Most buzz on Booksy'),
                    ('greatest_loyalty', 'Greatest loyalty on Booksy'),
                    ('most_booked', 'Most booked on Booksy'),
                    ('busiest', 'Busiest on Booksy'),
                    ('best_portfolio', 'Best portfolio on Booksy'),
                    ('best_year_app', 'Best of the Year App Booksy 2023'),
                ],
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name='bestofbooksyawardperiod',
            name='period_name',
            field=models.CharField(
                choices=[
                    ('q1_2022', 'Q1 2022'),
                    ('q2_2022', 'Q2 2022'),
                    ('q3_2022', 'Q3 2022'),
                    ('q4_2022', 'Q4 2022'),
                    ('q1_2023', 'Q1 2023'),
                    ('q2_2023', 'Q2 2023'),
                    ('q3_2023', 'Q3 2023'),
                    ('q4_2023', 'Q4 2023'),
                    ('y_2023', 'Y 2023'),
                ],
                max_length=7,
            ),
        ),
    ]
