from django.db import models
from django.db.models import Q

from lib.models import ArchiveModel, ArchiveManager
from webapps.best_of_booksy.enums import BO<PERSON><PERSON>, BOBPeriodNames


class BestOfBooksyAwardPeriod(ArchiveModel):
    objects = ArchiveManager()
    all_objects = models.Manager()

    visible_from = models.DateField(verbose_name='Badge award in customer app visible from')
    visible_till = models.DateField(
        blank=True,
        null=True,
        verbose_name='Badge award in customer app visible till',
    )
    period_name = models.CharField(max_length=7, choices=BOBPeriodNames.choices())

    @property
    def badge_year(self):
        return self.period_name.split('_').pop()

    def __str__(self):
        return f'{self.id}, {self.period_name}'


class BestOfBooksyAward(ArchiveModel):
    class Meta:
        unique_together = [('name', 'award_period')]

    objects = ArchiveManager()
    all_objects = models.Manager()

    name = models.Char<PERSON>ield(max_length=20, choices=BOBAward.choices())
    award_period = models.ForeignKey(
        BestOfBooksyAwardPeriod,
        on_delete=models.CASCADE,
    )

    def __str__(self):
        return f'{self.id}, {self.name}, {self.award_period.period_name}'

    @property
    def badge_name(self):
        """
        change Business.best_of_booksy_business_award_names if badge name has changed
        """
        return f'{self.name}_{self.award_period.period_name}'


class BestOfBooksyBusinessAward(ArchiveModel):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=('business', 'award'),
                condition=Q(deleted__isnull=True),
                name='unique_award_per_business',
            ),
        ]

    objects = ArchiveManager()
    all_objects = models.Manager()

    business = models.ForeignKey(
        'business.Business',
        on_delete=models.CASCADE,
        related_name='best_of_booksy_business_awards',
    )
    # TODO: Update related model when UTT2 will be fully operational
    business_category = models.ForeignKey('business.BusinessCategory', on_delete=models.DO_NOTHING)
    award = models.ForeignKey(BestOfBooksyAward, on_delete=models.CASCADE)
    business_category_name = models.CharField(max_length=50)
    region_name = models.CharField(max_length=100)
    business_name = models.CharField(
        max_length=250,
        help_text='For strange business name we can manually add name to be printed on certificate',
    )
    certificate = models.ForeignKey(
        'images.Image',
        on_delete=models.SET_NULL,
        null=True,
    )

    score = models.DecimalField(decimal_places=4, default=0, max_digits=9)

    had_equal_score = models.BooleanField(default=False)

    def __str__(self):
        return f'{self.id}, business: {self.business_id}, award: {self.award.badge_name}'
