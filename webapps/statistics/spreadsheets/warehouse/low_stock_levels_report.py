from django.db.models import F
from django.utils.encoding import force_str
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy, gettext

from webapps.statistics.reports import SpreadsheetReport
from webapps.warehouse.models import Warehouse, CommodityStockLevel


def _(text):
    return force_str(gettext(text))


class LowStockLevelsReport(SpreadsheetReport):
    SHEETS = [
        ('low_stock_levels', gettext_lazy('Low stock')),
    ]

    def __init__(self, *args, warehouse=None, **kwargs):
        self.single_warehouse_id = warehouse
        super().__init__(*args, **kwargs)

    @cached_property
    def currency_format(self):
        return self.base_currency_format(True)

    def stock_levels_below_minimum(self):
        return (
            CommodityStockLevel.objects.filter(
                warehouse__business=self.stats.business,
                minimum_packages__gt=0,
                remaining_volume__lt=F('minimum_packages') * F('commodity__total_pack_capacity'),
            )
            .annotate(
                brand_name=F('commodity__brand__name'),
                category_name=F('commodity__category__name'),
                unit_symbol=F('commodity__volume_unit__symbol'),
            )
            .select_related(
                'commodity',
            )
            .order_by(
                'commodity__name',
            )
        )

    @property
    def warehouses(self):
        warehouse_ids = (
            self.stock_levels_below_minimum().values_list('warehouse', flat=True).distinct()
        )

        qs = Warehouse.objects.filter(
            id__in=warehouse_ids,
            business=self.stats.business,
            deleted__isnull=True,
        ).order_by(
            'name',
        )
        if self.single_warehouse_id:
            qs = qs.filter(id=self.single_warehouse_id)
        return qs

    def report_header(self):
        self.ws['B1'] = self.ws.title
        self.format_title(self.ws['B1'])

        self.row = 2
        self.ws['B2'] = self.business_label
        self.format_address(self.range('B:M'))
        self.append([''])

    def section_header(self, section_name):
        self.append(['', section_name])
        self.format_header(self.range('B:M'))

        self.append(
            [
                '',
                '#',
                _('Category'),
                _('Product'),
                _('SKU'),
                _('Brand'),
                _('Single package volume'),
                '',  # unit
                _('Remaining volume'),
                '',  # unit
                _('Minimum packages'),
                _('Maximum packages'),
                _('Full packages remaining'),
            ]
        )
        self.set_columns_width(self.range('A:M'), [5, 5, 18, 26, 14, 18, 13, 4, 12, 4] + [12] * 20)
        self.format_subheader(self.range('B:M'), number_column=5)
        self.set_rows_height(self.range('B:B'), [40])
        self.format_alignment(
            self.range('H:H'), vertical='center', horizontal='left', wrapText=True
        )
        self.format_alignment(
            self.range('J:J'), vertical='center', horizontal='left', wrapText=True
        )

    def render_warehouse_table(self, warehouse):
        self.section_header(warehouse.name)
        low_stock_levels = self.stock_levels_below_minimum().filter(
            warehouse=warehouse,
        )

        for i, stock_level in enumerate(low_stock_levels, start=1):
            self.append(
                [
                    '',
                    i,
                    stock_level.category_name,
                    stock_level.commodity.name,
                    stock_level.commodity.product_code,
                    stock_level.brand_name,
                    stock_level.commodity.total_pack_capacity,
                    stock_level.unit_symbol,
                    stock_level.remaining_volume,
                    stock_level.unit_symbol,
                    stock_level.minimum_packages,
                    stock_level.maximum_packages,
                    stock_level.full_packages_left,
                ]
            )
            self.format_data_row(self.range('B:M'), number_column=20)
            self.format_alignment(self.range('H:H'), vertical='center', horizontal='left')
            self.format_alignment(self.range('J:J'), vertical='center', horizontal='left')

        self.append([''])
        self.format_total_row(self.range('B:M'))

    def sheet_low_stock_levels(self):
        self.report_header()

        for warehouse in self.warehouses:
            self.render_warehouse_table(warehouse)

        self.generated_info(empty_cols=1)
