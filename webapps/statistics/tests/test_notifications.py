from decimal import Decimal

import pytest

from webapps.business.baker_recipes import business_recipe
from webapps.notification.channels import PopupChannel
from webapps.notification.enums import (
    NotificationIcon,
    NotificationSize,
    ScheduleState,
)
from webapps.statistics.notifications import (
    TodayRevenuePerformanceNotification,
)
from webapps.statistics.notifications.bookings import (
    Last30DaysFullyBookedNotification,
    MonthBookingsGrowthNotification,
    Next7DaysFullyBookedNotification,
    TodayFullyBookedNotification,
)
from webapps.statistics.notifications.most_reviewed import (
    MostReviewedInCityLastMonthNotification,
)


@pytest.mark.django_db
@pytest.mark.parametrize(
    'notif_class,extra_params',
    [
        (TodayRevenuePerformanceNotification, dict(revenue=Decimal('25.50'))),
        (
            TodayFullyBookedNotification,
            None,
        ),
        (
            Next7DaysFullyBookedNotification,
            None,
        ),
        (
            Last30DaysFullyBookedNotification,
            None,
        ),
        (MonthBookingsGrowthNotification, dict(growth=10, month=2)),
    ],
)
def test_notifications(notif_class, extra_params):
    business = business_recipe.make()
    parameters = extra_params or {}
    notification = notif_class(None, business_id=business.id, **parameters)
    notification.send()
    assert notification.schedule_record.state == ScheduleState.SUCCESS


@pytest.mark.django_db
def test_most_reviewed_in_city():
    business = business_recipe.make()
    data = dict(
        business_id=business.id,
        city_name='Warszawa',
        position=1,
        reviews_count=50,
        year=2020,
        month=4,
    )
    notification = MostReviewedInCityLastMonthNotification(None, **data)

    popup_content = PopupChannel(notification).get_content()
    assert popup_content
    assert popup_content.icon == NotificationIcon.RECOGNITION
    assert not popup_content.crucial
    assert popup_content.relevance == 3
    assert popup_content.content.size == NotificationSize.BIG
    assert 'made you the 1 most reviewed in Warszawa' in popup_content.content.messages[1]
