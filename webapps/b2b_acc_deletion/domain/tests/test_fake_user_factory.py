import unittest
from webapps.b2b_acc_deletion.domain.factories import FakeUserFactory
from webapps.b2b_acc_deletion.shared.types import BusinessId


class TestFakeUserFactory(unittest.TestCase):
    def test_create_fake_owner(self):
        business_id = BusinessId(123)
        fake_user = FakeUserFactory.create_fake_owner(business_id)

        self.assertEqual(fake_user.username, f"fakeuser_b2bdeletion_{business_id}")
        self.assertEqual(fake_user.first_name, "Fake")
        self.assertEqual(fake_user.last_name, "User")
        self.assertEqual(fake_user.email, f"fakeuser_b2bdeletion_{business_id}@example.com")
        self.assertFalse(fake_user.include_in_analysis)

    def test_create_fake_staffer(self):
        business_id = BusinessId(123)
        index = 2
        fake_user = FakeUserFactory.create_fake_staffer(business_id, index)

        self.assertEqual(fake_user.username, f"fakeuser_b2bdeletion_{business_id}_s{index}")
        self.assertEqual(fake_user.first_name, "Fake")
        self.assertEqual(fake_user.last_name, "User")
        self.assertEqual(
            fake_user.email, f"fakeuser_b2bdeletion_{business_id}_s{index}@example.com"
        )
        self.assertFalse(fake_user.include_in_analysis)

    def test_unique_usernames(self):
        business_id = BusinessId(123)
        fake_owner = FakeUserFactory.create_fake_owner(business_id)
        fake_staffer = FakeUserFactory.create_fake_staffer(business_id, 1)

        self.assertNotEqual(fake_owner.username, fake_staffer.username)
        self.assertNotEqual(fake_owner.email, fake_staffer.email)
