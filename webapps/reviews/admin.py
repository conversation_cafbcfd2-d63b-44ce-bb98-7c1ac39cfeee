from django.contrib import admin
from django.contrib import messages
from django.contrib.admin import helpers, SimpleListFilter
from django.contrib.admin.utils import get_deleted_objects, model_ngettext
from django.core.exceptions import PermissionDenied
from django.template.response import TemplateResponse
from django.urls import re_path as url
from django.urls import reverse
from django.utils.translation import gettext as _

from lib.admin_helpers import BaseModelAdmin, NoRowsInListViewMixin
from webapps.admin_extra.views.review import ReviewMarkFakeView, ReviewPhotoUploadView
from webapps.reviews import forms
from .models import Review, ReviewFeedback, ReviewPhoto


class RankListFilter(SimpleListFilter):
    title = 'Rank'
    parameter_name = 'rank'

    def lookups(self, request, model_admin):
        return ((1, 1), (2, 2), (3, 3), (4, 4), (5, 5))

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(rank=self.value())
        return queryset


class ReviewAdmin(NoRowsInListViewMixin, BaseModelAdmin):
    form = forms.ReviewForm
    list_display = [
        'id',
        'user',
        'business',
        'rank',
        'created',
        'title',
        'review',
        'services',
        'staff',
        'reply_content',
        'reply_updated',
    ]
    raw_id_fields = ['user', 'business', 'subbooking']
    list_per_page = 100
    search_fields = [
        '=id',
        '=business__id',
        'user__email',
        'user__cell_phone',
        'user__first_name',
        'user__last_name',
        'title',
        'review',
    ]
    list_filter = [RankListFilter]
    date_hierarchy = 'created'
    readonly_fields = ['created', 'updated', 'deleted']
    exclude = ['reason_for_deletion']
    change_form_template = 'admin/change_forms/change_form__review.html'

    def has_add_permission(self, request):
        return False

    def get_form(self, request, obj=None, change=False, **kwargs):
        form = super().get_form(request, obj, change, **kwargs)
        self.review_photo_upload = self.add_review_photos(obj)
        self.review = obj
        return form

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'(?P<review_id>\d+)/review_photo_upload/$',
                self.admin_site.admin_view(ReviewPhotoUploadView.as_view()),
                name='review_photo_upload',
            ),
            url(
                r'(?P<pk>\d+)/mark_review_as_fake/$',
                self.admin_site.admin_view(ReviewMarkFakeView.as_view()),
                name='mark_review_as_fake',
            ),
        ]
        return additional_urls + urls

    @staticmethod
    def add_review_photos(obj=None):
        if obj is None:
            return ''
        return f"""<a href="{reverse("admin:review_photo_upload", args=(obj.id,))}" class="btn ">
                 Review photo upload</a>"""

    def get_actions(self, request):
        actions = super().get_actions(request)
        actions['delete_selected'] = (
            self.delete_selected_reviews,
            'delete_selected',
            'delete selected reviews',
        )

        actions['mark_as_fake'] = (
            lambda *args: self.delete_selected_reviews(*args, mark_as_fake=True),
            'mark_as_fake',
            'mark selected reviews as fake',
        )
        return actions

    @staticmethod
    def delete_selected_reviews(modeladmin, request, queryset, mark_as_fake=False):
        opts = modeladmin.model._meta
        app_label = opts.app_label

        # Populate deletable_objects, a data structure of all
        # related objects that will also be deleted.
        deletable_objects, model_count, perms_needed, protected = get_deleted_objects(
            queryset, request, modeladmin.admin_site
        )

        # The user has already confirmed the deletion.
        # Do the deletion and return None to display
        # the change list view again.
        if request.POST.get('post') and not protected:
            if perms_needed:
                raise PermissionDenied
            qs_count = queryset.count()
            mark_as_fake = request.POST.get('mark_as_fake')

            if qs_count:
                for obj in queryset:
                    obj_display = str(obj)
                    modeladmin.log_deletion(request, obj, obj_display)
                    # Perform delete on individual models
                    # instead of whole queryset

                    if mark_as_fake:
                        obj.reason_for_deletion = ReviewMarkFakeView.FAKE_REVIEW_MARK
                    obj.delete()

                modeladmin.message_user(
                    request,
                    _("Successfully{} deleted {} {}.").format(
                        " marked as fake and" if mark_as_fake else "",
                        qs_count,
                        model_ngettext(modeladmin.opts, qs_count),
                    ),
                    messages.SUCCESS,
                )
            # Return None to display the change list page again.
            return None

        objects_name = model_ngettext(queryset)

        if perms_needed or protected:
            title = _(f"Cannot delete {objects_name}")
        else:
            title = _("Are you sure?")

        context = dict(
            modeladmin.admin_site.each_context(request),
            title=title,
            objects_name=str(objects_name),
            deletable_objects=[deletable_objects],
            model_count=dict(model_count).items(),
            queryset=queryset,
            perms_lacking=perms_needed,
            mark_as_fake=mark_as_fake,
            protected=protected,
            opts=opts,
            action_checkbox_name=helpers.ACTION_CHECKBOX_NAME,
            media=modeladmin.media,
        )

        request.current_app = modeladmin.admin_site.name

        # Display the confirmation page
        html_file = "delete_selected_confirmation.html"
        return TemplateResponse(
            request,
            modeladmin.delete_selected_confirmation_template
            or [
                f"admin/{app_label}/{opts.model_name}/{html_file}",
                f"admin/{app_label}/{html_file}",
                f"admin/{html_file}",
            ],
            context,
        )


class ReviewFeedbackAdmin(BaseModelAdmin):
    list_display = [
        'id',
        'user',
        'review',
        'feedback',
        'created',
        'fingerprint',
    ]
    search_fields = (
        '=id',
        '=review__id',
        'user__email',
        'user__cell_phone',
        'user__first_name',
        'user__last_name',
    )
    list_per_page = 100
    list_filter = ['feedback']
    raw_id_fields = ('user', 'review')
    date_hierarchy = 'created'
    readonly_fields = ['created', 'updated', 'deleted']

    def has_add_permission(self, request):
        return False


class ReviewPhotoAdmin(BaseModelAdmin):
    list_display = [
        'id',
        'review',
        'photo',
        'created',
    ]
    search_fields = (
        '=id',
        '=review__id',
    )
    list_per_page = 100
    raw_id_fields = ('review', 'photo')
    readonly_fields = ['created', 'updated', 'deleted']

    def has_add_permission(self, request):
        return False


admin.site.register(Review, ReviewAdmin)
admin.site.register(ReviewFeedback, ReviewFeedbackAdmin)
admin.site.register(ReviewPhoto, ReviewPhotoAdmin)
