from django.db import models, migrations
import django.contrib.postgres.fields.jsonb


class Migration(migrations.Migration):

    dependencies = [
        ('photo', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Review',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                ('id', models.AutoField(serialize=False, primary_key=True, db_column='review_id')),
                ('rank', models.SmallIntegerField()),
                ('title', models.CharField(max_length=250)),
                ('review', models.TextField()),
                ('services', django.contrib.postgres.fields.jsonb.JSONField(default=dict)),
                ('staff', django.contrib.postgres.fields.jsonb.JSONField(default=dict)),
                ('reply_title', models.Char<PERSON>ield(max_length=250, null=True, blank=True)),
                ('reply_content', models.TextField(null=True, blank=True)),
                ('reply_updated', models.DateTimeField(null=True, blank=True)),
            ],
            options={
                'ordering': ['-created'],
            },
        ),
        migrations.CreateModel(
            name='ReviewFeedback',
            fields=[
                (
                    'id',
                    models.AutoField(
                        verbose_name='ID', serialize=False, auto_created=True, primary_key=True
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                (
                    'feedback',
                    models.CharField(
                        max_length=1, choices=[('Y', 'Yes'), ('N', 'No'), ('I', 'Inappropriate')]
                    ),
                ),
                ('fingerprint', models.CharField(max_length=50, null=True, blank=True)),
                (
                    'review',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        related_name='review_feedbacks',
                        to='reviews.Review',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
            },
        ),
        migrations.CreateModel(
            name='ReviewPhoto',
            fields=[
                (
                    'id',
                    models.AutoField(
                        verbose_name='ID', serialize=False, auto_created=True, primary_key=True
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                (
                    'photo',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        related_name='review_photos',
                        to='photo.Photo',
                    ),
                ),
                (
                    'review',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        related_name='review_photos',
                        to='reviews.Review',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
            },
        ),
    ]
