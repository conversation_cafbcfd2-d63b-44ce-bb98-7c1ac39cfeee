import logging
from django.core.validators import (
    ValidationError,
)
from django.db import models

from webapps.billing.enums import (
    ProductType,
)
from webapps.billing.models.base import (
    BillingHistoryModel,
    BillingProductBase,
)
from webapps.business.models import Business
from webapps.billing.models.managers import (
    BillingProductManager,
)

_logger = logging.getLogger('booksy.billing')


class BillingProduct(BillingProductBase):
    """
    Definition of product to purchase - ex. "Booksy subscription for 100$",
    "Additional staff member for 10$". Some products are required add-ons
    and are not selected directly by merchant as separate products, but are
    seen as part of main product.

    All products are placed in one table to speed up calculations.
    """

    class Meta:
        verbose_name = 'Product'
        verbose_name_plural = 'Products'

    EXTERNAL_SOURCES = {
        Business.PaymentSource.BRAINTREE,
        Business.PaymentSource.PLAY,
        Business.PaymentSource.ITUNES,
    }
    valid_product_type = None

    active = models.BooleanField(default=False)
    # Valid only for SaaS & Busy
    staff_add_on = models.ForeignKey(
        'billing.BillingProduct',
        on_delete=models.PROTECT,
        verbose_name='Staff add-on',
        null=True,
        blank=True,
        related_name='+',
    )
    # Valid only for SaaS
    sms_add_on = models.ForeignKey(
        'billing.BillingProduct',
        on_delete=models.PROTECT,
        verbose_name='Postpaid SMS price',
        null=True,
        blank=True,
        related_name='+',
    )

    # Future fields

    source = models.CharField(
        max_length=1,
        choices=Business.PaymentSource.choices(),
        default=Business.PaymentSource.BRAINTREE_BILLING,
    )
    # Valid only for gp & itunes
    external_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
    )
    # end of future fields
    objects = BillingProductManager()

    def clean(self):
        if self.source in self.EXTERNAL_SOURCES and not self.external_id:
            raise ValidationError('Please provide external_id or change source')

    def save(
        self,
        force_insert=False,
        force_update=False,
        using=None,
        update_fields=None,
        **kwargs,
    ):
        self.clean()
        return super().save(
            force_insert=False,
            force_update=False,
            using=None,
            update_fields=None,
            **kwargs,
        )


class BillingProductHistory(BillingHistoryModel):
    model = models.ForeignKey(
        BillingProduct,
        on_delete=models.CASCADE,
        related_name='history',
    )


#####################################################
# Proxy models, created to use default admin views  #
#####################################################


class BillingSaaSProduct(BillingProduct):
    class Meta:
        proxy = True
        verbose_name = 'SaaS product'
        verbose_name_plural = 'SaaS products'

    valid_product_type = ProductType.SAAS

    def __str__(self):
        basic = super().__str__()
        if self.staff_add_on:
            basic += f" +{self.staff_add_on}"

        if self.sms_add_on:
            basic += f" +{self.sms_add_on}"
        return basic


class BillingStafferSaaSProduct(BillingProduct):
    class Meta:
        proxy = True
        verbose_name = 'SaaS staffer add-on'
        verbose_name_plural = 'SaaS staffer add-ons'

    valid_product_type = ProductType.STAFFER_SAAS


class BillingBusyProduct(BillingProduct):
    class Meta:
        proxy = True
        verbose_name = '"Busy" product'
        verbose_name_plural = '"Busy" products'

    valid_product_type = ProductType.BUSY


class BillingStafferBusyProduct(BillingProduct):
    class Meta:
        proxy = True
        verbose_name = '"Busy" staffer add-on'
        verbose_name_plural = '"Busy" staffer add-ons'

    valid_product_type = ProductType.STAFFER_BUSY


class BillingPostpaidSMSProduct(BillingProduct):
    class Meta:
        proxy = True
        verbose_name = 'Postpaid SMS product'
        verbose_name_plural = 'Postpaid SMS product'

    valid_product_type = ProductType.POSTPAID_SMS


class BillingPrepaidSMSProduct(BillingProduct):
    class Meta:
        proxy = True
        verbose_name = 'Prepaid SMS package'
        verbose_name_plural = 'Postpaid SMS packages'

    valid_product_type = ProductType.PREPAID_SMS


####################################
#       end of proxy models        #
####################################


PRODUCT_TYPE_TO_MODEL_MAP = {
    ProductType.SAAS: BillingSaaSProduct,
    ProductType.STAFFER_SAAS: BillingStafferSaaSProduct,
    ProductType.BUSY: BillingBusyProduct,
    ProductType.STAFFER_BUSY: BillingStafferBusyProduct,
    ProductType.POSTPAID_SMS: BillingPostpaidSMSProduct,
    ProductType.PREPAID_SMS: BillingPrepaidSMSProduct,
}
