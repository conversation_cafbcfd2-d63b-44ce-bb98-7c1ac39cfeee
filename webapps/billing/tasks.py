import logging
import typing as t
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

from billiard.exceptions import SoftTimeLimitExceeded
from django.conf import settings
from lib.celery_tools import celery_task
from lib.feature_flag.feature.billing import (
    SwitchBraintreeToStripePaymentProcessorFlag,
    BillingAutoRetryChargeDateExtension,
)
from lib.locks import (
    BillingBoostLock,
    BillingInitializeRetryChargeSubscriptionLock,
    BillingInitializeSubscriptionLock,
    BillingMigratedSubscriptionInitialTaskLock,
    BillingSubscriptionLock,
    RedlockError,
    BillingOneOffChargeLock,
    BillingCycleSwitchLock,
)
from lib.tools import tznow
from webapps.billing.billing_cycle_switch import BillingCycleSwitch
from webapps.billing.business_status import (
    ReferenceSubscriptionData,
    compute_business_status,
)
from webapps.billing.long_subscription import LongSubscriptionRenewal

from webapps.billing.churn import BusinessChurn
from webapps.billing.close_subscription import (
    CloseSubscription,
    SubscriptionState,
)
from webapps.billing.enums import (
    BillingRefund<PERSON>tatus,
    MigratedSubscriptionInitialTaskType,
    PaymentActionType,
    PaymentProcessorType,
    SubscriptionStatus,
    TransactionSource,
)
from webapps.billing.error_handling.errors import (
    BILLING_120_ERR,
    BILLING_130_ERR,
    BILLING_131_ERR,
    BILLING_202_ERR,
    BILLING_133_ERR,
)
from webapps.billing.interfaces.boost import total_overdue_info
from webapps.billing.interfaces.stripe import cancel_payment_intent
from webapps.billing.models import (
    BillingSubscription,
    BillingTransaction,
    MigratedSubscriptionInitialTask,
    SubscriptionPurchaseRequest,
    SubscriptionRetryChargeRequest,
)
from webapps.billing.models.business import BillingBusiness, BillingBusinessSettings
from webapps.billing.models.payment.refund import BillingTransactionRefund
from webapps.billing.one_off_charge import OneOffCharge
from webapps.billing.payment_processor import PaymentProcessor
from webapps.billing.retry_charge import RetryCharge
from webapps.billing.services.boost import OverdueBoostFlowService
from webapps.billing.services.charge import StripeChargeService
from webapps.billing.services.migrated_subscription import (
    MigratedSubscriptionInitialTaskException,
    MigratedSubscriptionInitialTaskService,
)
from webapps.billing.services.payment_method import StripePaymentMethodService
from webapps.billing.subscription_creator import SubscriptionCreator
from webapps.braintree_app.payment_processor import BraintreePaymentProcessor
from webapps.business.models import Business, CancellationReason
from webapps.kill_switch.models import KillSwitch
from webapps.stripe_app.apis.charge import PaymentIntentApi
from webapps.stripe_app.apis.payment_method import PaymentMethodApi
from webapps.stripe_app.enums import Subscriber

_logger = logging.getLogger('booksy.billing_tasks')

AUTO_RETRY_CHARGE_DAYS = [3, 7, 11, 13]
STEP_MODULO = 10
COUNTDOWN_INCREMENT = 2


@celery_task(ignore_result=False)
def purchase_subscription_task(
    business_id: int,
    offer_id: int,
    product_ids: t.Iterable[int],
    operator_id: t.Optional[int] = None,
):
    error_result = {
        'message': BILLING_130_ERR.message,
        'error_code': BILLING_130_ERR.code,
    }

    try:
        lock_ = BillingSubscriptionLock.lock(business_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip subscription purchase for business %s', business_id)
        return error_result

    if BillingSubscription.get_current_or_pending_subscription(business_id):
        result = error_result
    else:
        result = SubscriptionCreator.new_purchase(business_id, offer_id, product_ids, operator_id)
    BillingSubscriptionLock.try_to_unlock(lock_)

    return result


@celery_task(ignore_result=False)
def initialize_purchase_subscription_task(
    business_id: int,
    offer_id: int,
    product_ids: list[int],
    operator_id: int | None = None,
):
    try:
        lock_ = BillingInitializeSubscriptionLock.lock(business_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip subscription purchase for business %s', business_id)
        result = {
            'message': BILLING_130_ERR.message,
            'error_code': BILLING_130_ERR.code,
        }
        return result

    offer_items = SubscriptionCreator.get_products(offer_id, product_ids)
    charges = SubscriptionCreator.calculate_charges(business_id, offer_id, offer_items)
    currency = SubscriptionCreator.saas_currency(offer_items)

    payment_result = StripeChargeService.initialize_charge(
        business_id=business_id,
        amount=charges['charge_amount'],
        currency=currency,
        description=TransactionSource.BILLING_SUBSCRIPTION.label,
        metadata={
            'operator_id': operator_id,
            'action': PaymentActionType.INITIAL_PAYMENT_WITH_TRANSACTION_AUTH.value,
            'transaction_source': TransactionSource.BILLING_SUBSCRIPTION.value,
        },
    )

    if payment_result.is_success:
        SubscriptionPurchaseRequest.objects.create(
            business_id=business_id,
            total_price=charges['charge_amount'],
            currency=currency,
            subscription_params={
                'offer_id': offer_id,
                'product_ids': product_ids,
            },
            action_type=PaymentActionType.INITIAL_PAYMENT_WITH_TRANSACTION_AUTH.value,
            purchase_key=payment_result.transaction_id,
            operator_id=operator_id,
        )

        result = {
            'user_data': {
                'token': payment_result.client_secret,
                'metadata': {
                    'subscriber': Subscriber.BILLING,
                },
            }
        }
    else:
        result = StripeChargeService.parse_error_response(charge_result=payment_result)

    BillingInitializeSubscriptionLock.try_to_unlock(lock_)
    return result


@celery_task(ignore_result=False)
def finalize_purchase_subscription_task(
    purchase_request_id: int,
    operator_id: int | None = None,
):
    error_result = {
        'message': BILLING_130_ERR.message,
        'error_code': BILLING_130_ERR.code,
    }

    purchase_request = SubscriptionPurchaseRequest.objects.get(id=purchase_request_id)

    conflicting_process = (
        purchase_request.purchased_at
        or BillingSubscription.get_current_or_pending_subscription(purchase_request.business_id)
    )

    if conflicting_process:
        return error_result

    try:
        lock_ = BillingSubscriptionLock.lock(purchase_request.business_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip subscription purchase for business %s', purchase_request.business_id)
        return error_result

    payment_result = StripeChargeService.retrieve_charge(stripe_id=purchase_request.purchase_key)

    purchase_request.used_at = tznow()

    if payment_result.is_success:
        result = SubscriptionCreator.new_purchase(
            business_id=purchase_request.business_id,
            offer_id=purchase_request.subscription_params['offer_id'],
            product_ids=purchase_request.subscription_params['product_ids'],
            operator_id=operator_id,
            payment_result=payment_result,
        )
        purchase_request.purchased_at = tznow()
    else:
        result = SubscriptionCreator.handle_unsuccessful_charge(
            business_id=purchase_request.business_id,
            payment_result=payment_result,
        )

    purchase_request.save()

    BillingSubscriptionLock.try_to_unlock(lock_)
    return result


@celery_task(ignore_result=False)
def initialize_retry_charge_subscription_task(
    business_id: int,
    subscription_id: int,
    operator_id: int | None = None,
):
    try:
        lock_ = BillingInitializeRetryChargeSubscriptionLock.lock(business_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip subscription retry charge for business %s', business_id)
        result = {
            'message': BILLING_130_ERR.message,
            'error_code': BILLING_130_ERR.code,
        }
        return result

    subscription = BillingSubscription.objects.active_subscriptions().get(
        id=subscription_id,
        business_id=business_id,
        status=SubscriptionStatus.BLOCKED,
        balance__gt=Decimal(0),
    )
    latest_cycle = subscription.latest_cycle

    payment_result = StripeChargeService.initialize_charge(
        business_id=business_id,
        amount=subscription.balance,
        currency=subscription.currency,
        description=TransactionSource.BILLING_SUBSCRIPTION.label,
        metadata={
            'billing_cycle_id': latest_cycle.id,
            'subscription_id': subscription.id,
            'operator_id': operator_id,
            'action': PaymentActionType.RETRY_CHARGE_WITH_TRANSACTION_AUTH.value,
            'transaction_source': TransactionSource.BILLING_SUBSCRIPTION.value,
        },
    )

    if payment_result.is_success:
        SubscriptionRetryChargeRequest.objects.create(
            business_id=business_id,
            subscription=subscription,
            billing_cycle=latest_cycle,
            total_price=subscription.balance,
            purchase_key=payment_result.transaction_id,
            operator_id=operator_id,
            action_type=PaymentActionType.RETRY_CHARGE_WITH_TRANSACTION_AUTH.value,
        )

        result = {
            'user_data': {
                'token': payment_result.client_secret,
                'metadata': {
                    'subscriber': Subscriber.BILLING,
                },
            }
        }
    else:
        result = StripeChargeService.parse_error_response(charge_result=payment_result)

    BillingInitializeRetryChargeSubscriptionLock.try_to_unlock(lock_)
    return result


@celery_task(ignore_result=False)
def finalize_retry_charge_subscription_task(
    retry_charge_request_id: int,
    operator_id: int | None = None,
):
    error_result = {
        'message': BILLING_130_ERR.message,
        'error_code': BILLING_130_ERR.code,
    }

    retry_charge_request = SubscriptionRetryChargeRequest.objects.select_related(
        'subscription'
    ).get(id=retry_charge_request_id)

    if retry_charge_request.purchased_at:
        return error_result

    try:
        lock_ = BillingSubscriptionLock.lock(retry_charge_request.business_id)
    except RedlockError:
        lock_ = None

    if not lock_:
        _logger.warning(
            'Skip subscription retry charge for business %s', retry_charge_request.business_id
        )
        return error_result

    payment_result = StripeChargeService.retrieve_charge(
        stripe_id=retry_charge_request.purchase_key
    )

    result = RetryCharge.retry_for_subscription(
        subscription=retry_charge_request.subscription,
        payment_result=payment_result,
        operator_id=operator_id,
    )
    if payment_result.is_success:
        retry_charge_request.purchased_at = tznow()

    retry_charge_request.used_at = tznow()
    retry_charge_request.save()

    BillingSubscriptionLock.try_to_unlock(lock_)
    return result


@celery_task(ignore_result=False)
def retry_charge_task(
    business_id: int,
    subscription_id: int,
    is_auto_retry_charge: bool = False,
    operator_id: int = None,
):

    try:
        lock_ = BillingSubscriptionLock.lock(business_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip retry charge for business %s', business_id)
        result = {
            'message': BILLING_131_ERR.message,
            'error_code': BILLING_131_ERR.code,
        }
        return result

    try:
        subscription = BillingSubscription.objects.get(id=subscription_id)
    except BillingSubscription.DoesNotExist:
        result = {
            'message': BILLING_120_ERR.message,
            'error_code': BILLING_120_ERR.code,
        }
    else:
        result = RetryCharge.retry_for_subscription(
            subscription=subscription,
            operator_id=operator_id,
            is_auto_retry_charge=is_auto_retry_charge,
        )
    finally:
        BillingSubscriptionLock.try_to_unlock(lock_)

    return result


@celery_task
def auto_retry_charges() -> None:

    auto_retry_charge_days = (
        settings.BILLING_AUTO_RETRY_CHARGE_DAYS
        if BillingAutoRetryChargeDateExtension()
        else AUTO_RETRY_CHARGE_DAYS
    )

    for days_since_cycle_change in auto_retry_charge_days:
        to_auto_retry_charge = BillingSubscription.objects.to_auto_retry_charge(
            days_since_cycle_change
        ).only('id', 'business_id')

        countdown = 0

        for step, subscription in enumerate(to_auto_retry_charge.iterator()):
            if step % STEP_MODULO == 0:
                countdown += COUNTDOWN_INCREMENT
            retry_charge_task.apply_async(
                kwargs={
                    'business_id': subscription.business_id,
                    'subscription_id': subscription.pk,
                    'is_auto_retry_charge': True,
                },
                countdown=countdown,
            )


@celery_task
def switch_cycle_single_task(business_id, subscription_id):
    """Move single subscription into new billing cycle."""

    try:
        lock_ = BillingSubscriptionLock.lock(business_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip billing cycle switch for business %s', business_id)
        return

    BillingCycleSwitch.switch_cycle(subscription_id=subscription_id)
    BillingSubscriptionLock.try_to_unlock(lock_)


@celery_task
def renew_long_subscription_task(business_id, subscription_id):
    """Renew single subscription longer than month."""
    try:
        lock_ = BillingSubscriptionLock.lock(business_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip renewing long subscription for business %s', business_id)
        return

    if LongSubscriptionRenewal.should_be_renew_for_long_subscription(subscription_id):
        LongSubscriptionRenewal.renew_subscription(business_id, subscription_id)
    BillingSubscriptionLock.try_to_unlock(lock_)


@celery_task
def switch_billing_cycles_task():
    """Move subscriptions to new billing cycles."""
    try:
        lock_ = BillingCycleSwitchLock.lock(switch_billing_cycles_task.__name__)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning(
            'Task: %s skipped, previous one is still in progress.',
            switch_billing_cycles_task.__name__,
        )

        return
    to_switch = BillingSubscription.objects.to_switch()

    countdown = 0

    for step, subscription in enumerate(to_switch.iterator()):
        if SubscriptionState.should_not_be_renewed_for_business(
            business=subscription.business,
        ):
            close_subscriptions.delay(business_id=subscription.business_id)
        elif BusinessChurn.should_be_churned_at_request(
            business=subscription.business,
            pending_churn_requests=subscription.pending_churn_requests,
        ):
            requested_churn.delay(
                business_id=subscription.business_id,
                subscription_id=subscription.pk,
            )
        elif BusinessChurn.should_be_auto_churned(subscription=subscription):
            auto_churn.delay(
                business_id=subscription.business_id,
                subscription_id=subscription.pk,
            )
        elif LongSubscriptionRenewal.should_be_renew_for_long_subscription(subscription):
            renew_long_subscription_task.delay(
                business_id=subscription.business_id,
                subscription_id=subscription.pk,
            )
        else:
            if step % STEP_MODULO == 0:
                countdown += COUNTDOWN_INCREMENT
            switch_cycle_single_task.apply_async(
                kwargs={
                    'business_id': subscription.business_id,
                    'subscription_id': subscription.pk,
                },
                countdown=countdown,
            )

    BillingCycleSwitchLock.try_to_unlock(lock_)


@celery_task(ignore_result=False)
def add_or_update_billing_info_task(business_id, payment_method_data):
    business = Business.objects.get(id=business_id)
    customer_results = PaymentProcessor.get_or_create_customer(business)
    if not customer_results.is_success:
        return customer_results.to_dict()

    result = PaymentProcessor.create_payment_method(
        nonce=payment_method_data['nonce'],
        business_id=business_id,
        billing_address=payment_method_data.get('billing_address'),
    )
    if result.is_success:
        return {
            'message': result.message,
            'payment_method_id': result.payment_method.id,
        }

    return result.to_dict()


# region churn
@celery_task
def auto_churn(business_id: int, subscription_id: int) -> None:
    """
    Marks business as churned due to non-payment.
    """

    try:
        lock_ = BillingSubscriptionLock.lock(business_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip auto churn for business %s', business_id)
        return

    # Check once again for double sure
    if BusinessChurn.should_be_auto_churned(subscription=subscription_id):
        BusinessChurn.auto_churn(subscription=subscription_id)

    BillingSubscriptionLock.try_to_unlock(lock_)


@celery_task
def requested_churn(business_id: int, subscription_id: t.Optional[int] = None) -> None:
    """
    Marks business as churned due to Merchant request.
    """

    try:
        lock_ = BillingSubscriptionLock.lock(business_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip requested churn for business %s', business_id)
        return

    # Check once again for double sure
    if BusinessChurn.should_be_churned_at_request(business=business_id):
        BusinessChurn.requested_churn(
            business=business_id,
            subscription=subscription_id,
        )

    BillingSubscriptionLock.try_to_unlock(lock_)


@celery_task
def pending_requested_churn() -> None:
    """
    Gets pending requested churn and apply them immediately.
    """
    business_status = [
        Business.Status.OVERDUE,
        Business.Status.BLOCKED_OVERDUE,
        Business.Status.PAID,
    ]

    cancellations = (
        CancellationReason.objects.filter(
            churn_done=False,
            deleted__isnull=True,
            cancellation_date__lte=tznow(),
            business__has_new_billing=True,
            business__status__in=business_status,
        )
        .distinct(
            # Don't worry about double entries for the same business -
            # `BusinessChurn.requested_churn` will close them all.
            'business_id'
        )
        .order_by()
    )

    for cancellation in cancellations.iterator():
        requested_churn.delay(business_id=cancellation.business_id)


@celery_task
def update_not_refreshed_businesses_task() -> None:
    businesses_need_update = (
        BillingSubscription.objects.active_subscriptions()
        .filter(
            business__status__in=[
                BillingBusiness.Status.TRIAL_BLOCKED,
                BillingBusiness.Status.TRIAL,
            ],
            business__has_new_billing=True,
            created__lt=tznow() - timedelta(minutes=5),
        )
        .values_list('business_id', flat=True)
    )

    for business_id in businesses_need_update.iterator():
        compute_business_status(
            business_id=business_id,
            metadata={'task': 'update_not_refreshed_businesses_task'},
        )


# endregion


@celery_task
def close_subscriptions(business_id: int) -> None:
    """
    Closes all not-closed subscriptions of blocked/invalid business.
    """

    try:
        lock_ = BillingSubscriptionLock.lock(business_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip close subscription for business %s', business_id)
        return

    # Check once again for double sure
    if SubscriptionState.should_not_be_renewed_for_business(business=business_id):
        CloseSubscription.close_subscriptions(business_id=business_id)

    BillingSubscriptionLock.try_to_unlock(lock_)


@celery_task
def mismatched_report_task(recipient_email: str) -> None:
    # pylint: disable=cyclic-import
    from webapps.billing.reports import MismatchedBusinessReport

    MismatchedBusinessReport.prepare_and_send([recipient_email])


##########
# Stripe
##########


def _update_payment_processor(business_id: int) -> None:
    if not KillSwitch.alive(KillSwitch.System.BILLING_FORCE_SWITCH_TO_STRIPE):
        return
    business_settings, _ = BillingBusinessSettings.objects.get_or_create(business_id=business_id)
    if business_settings.payment_processor in (PaymentProcessorType.BRAINTREE, None):
        business_settings.payment_processor = PaymentProcessorType.STRIPE
        business_settings.save()


@celery_task(ignore_result=False)
def setup_stripe_payment_method_task(business_id: int) -> dict:
    business = Business.objects.get(id=business_id)
    result = StripePaymentMethodService.initialize_setup(business=business)

    if result.is_success:
        return {
            'user_data': {
                'token': result.client_secret,
                'metadata': {
                    'subscriber': Subscriber.BILLING,
                },
            }
        }

    return StripePaymentMethodService.parse_error_response(result)


@celery_task(ignore_result=False)
def finalize_stripe_payment_method_task(
    business_id: int,
    stripe_id: str,
    default: bool = True,
) -> dict:
    business = Business.objects.get(id=business_id)
    result = StripePaymentMethodService.finalize_setup(
        business=business,
        stripe_id=stripe_id,
        default=default,
    )

    if result.is_success:
        _update_payment_processor(business_id)

        return {
            'user_data': {
                'payment_method_id': result.payment_method_id,
            }
        }

    return StripePaymentMethodService.parse_error_response(result)


@celery_task
def delete_stripe_payment_method_task(stripe_id: str) -> None:
    from webapps.stripe_app.services.events import EventProcessException

    deleted_payment = PaymentMethodApi.delete(
        stripe_config=settings.BILLING_STRIPE_APP_CONFIG, stripe_id=stripe_id
    )
    if not deleted_payment.is_success:
        raise EventProcessException(
            f'Unable to delete payment method: {stripe_id}, error: {deleted_payment.error.message}'
        )


@celery_task(ignore_result=False)
def migrate_stripe_payment_methods_task(business_id: int) -> dict:
    business = Business.objects.get(id=business_id)
    result = StripePaymentMethodService.migrate_payment_methods(business=business)

    if result.is_success:
        return result.to_dict()

    return {
        "message": BILLING_202_ERR.message,
        "error": BILLING_202_ERR.code,
        "internal_errors": result.to_dict(),
    }


@celery_task(ignore_result=False)
def billing_refund_task(
    billing_transaction_refund_id: int,
    metadata: dict | None = None,
) -> dict:
    transaction_refund = BillingTransactionRefund.objects.select_related('transaction').get(
        id=billing_transaction_refund_id
    )

    payment_processor = transaction_refund.transaction.payment_processor

    external_id = None
    errors = None
    status = None

    match payment_processor:
        case PaymentProcessorType.BRAINTREE:
            result = BraintreePaymentProcessor.make_refund(
                business_id=transaction_refund.transaction.business_id,
                transaction_id=transaction_refund.transaction.external_id,
                amount=transaction_refund.amount,
            )

            if not result.is_success:
                errors = [e.to_dict() for e in result.errors]
                transaction_refund.status = BillingRefundStatus.FAILED
                transaction_refund.save(
                    _history={
                        'metadata': errors,
                    },
                    update_fields=['status'],
                )

                return {
                    'is_success': False,
                    'errors': errors,
                }

            external_id = result.transaction_id or ''
            status = BillingRefundStatus.SUCCEEDED

        case PaymentProcessorType.STRIPE:
            result = PaymentIntentApi.refund(
                stripe_id=transaction_refund.transaction.external_id,
                decimal_amount=transaction_refund.amount,
                stripe_config=settings.BILLING_STRIPE_APP_CONFIG,
                subscriber=Subscriber.BILLING,
                metadata=metadata,
            )

            if not result.is_success:
                errors = result.error.to_dict() if result.error else ''
                transaction_refund.status = BillingRefundStatus.FAILED
                transaction_refund.save(
                    _history={
                        'metadata': errors,
                    },
                    update_fields=['status'],
                )

                return {'is_success': False, 'errors': errors}

            external_id = result.refund.stripe_id if result.refund else ''
            status = result.refund.status if result.refund else ''

    transaction_refund.status = status
    transaction_refund.external_id = external_id
    transaction_refund.save(update_fields=['status', 'external_id'])
    return {'is_success': True, 'billing_refund_id': transaction_refund.id}


@celery_task(ignore_result=False)
def compute_business_status_task(
    business_id: int,
    metadata: t.Optional[dict] = None,
    ignore_blocked: t.Optional[bool] = False,
    reference_data: t.Optional[ReferenceSubscriptionData] = None,
):
    return compute_business_status(
        business_id=business_id,
        metadata=metadata,
        ignore_blocked=ignore_blocked,
        reference_data=reference_data,
    )


@celery_task
def cancel_stripe_payment_intent_task(
    retry_charge_request_id: int = None,
    purchase_request_id: int = None,
) -> None:
    if retry_charge_request_id:
        request = SubscriptionRetryChargeRequest.objects.get(id=retry_charge_request_id)
    elif purchase_request_id:
        request = SubscriptionPurchaseRequest.objects.get(id=purchase_request_id)
    else:
        return

    request.attempted_to_cancel = True

    cancel_payment_intent_result = cancel_payment_intent(
        stripe_id=request.purchase_key,
    )
    if not cancel_payment_intent_result.is_success:
        request.save()
        return

    request.canceled_at = tznow()
    request.save()


@celery_task
def cancel_abandoned_purchase_request_task() -> None:
    purchase_request_to_cancel = SubscriptionPurchaseRequest.objects.abandoned().iterator()

    for request_to_cancel in purchase_request_to_cancel:
        cancel_stripe_payment_intent_task.delay(purchase_request_id=request_to_cancel.id)


@celery_task
def cancel_abandoned_retry_charge_task() -> None:
    retry_charge_request_to_cancel = SubscriptionRetryChargeRequest.objects.abandoned().iterator()

    for request_to_cancel in retry_charge_request_to_cancel:
        cancel_stripe_payment_intent_task.delay(retry_charge_request_id=request_to_cancel.id)


@celery_task(ignore_result=False)
def batch_boost_overdue_charge_task(
    business_id: int,
    selected_boost_ids: list | None = None,
    operator_id: int | None = None,
) -> dict:
    try:
        lock_ = BillingBoostLock.lock(business_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip retry boost charge for business %s', business_id)
        return {
            'message': BILLING_131_ERR.message,
            'error_code': BILLING_131_ERR.code,
        }

    result = OverdueBoostFlowService.charge_overdue(
        business_id=business_id,
        selected_boost_ids=selected_boost_ids,
        operator_id=operator_id,
    )

    total_overdue_info.cache_clear(business_id=business_id)

    BillingBoostLock.try_to_unlock(lock_)
    return result


@celery_task(ignore_result=False)
def finish_batch_boost_overdue_charge_task(
    billing_transaction_id: int,
) -> dict:
    """
    Finishes interrupted processes (e.g. db timeout) of batch boost charge.
    Data comes from Stripe webhook
    """

    billing_transaction = BillingTransaction.objects.get(id=billing_transaction_id)

    try:
        lock_ = BillingBoostLock.lock(billing_transaction.external_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip retry boost charge for business %s', billing_transaction.business_id)
        return {
            'message': BILLING_131_ERR.message,
            'error_code': BILLING_131_ERR.code,
        }

    payment_response = StripeChargeService.retrieve_charge(
        stripe_id=billing_transaction.external_id
    )

    result = OverdueBoostFlowService.finish_interrupted_process(
        payment_request=int(payment_response.transaction.metadata['boost_payment_request_id']),
        billing_transaction=billing_transaction,
        payment_response=payment_response,
    )

    total_overdue_info.cache_clear(business_id=billing_transaction.business_id)

    BillingBoostLock.try_to_unlock(lock_)
    return result


@celery_task(ignore_result=False)
def create_one_off_transaction_task(
    business_id: int,
    one_off_request_id: int,
    billing_cycle_id: int,
    operator_id: int | None = None,
):
    """Create one off transaction"""
    try:
        lock_ = BillingOneOffChargeLock.lock(business_id)
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip one of charge transaction for business %s', business_id)
        result = {
            'message': BILLING_133_ERR.message,
            'error_code': BILLING_133_ERR.code,
        }
        return result

    result = OneOffCharge.make_charge(
        operator_id=operator_id,
        one_off_request_id=one_off_request_id,
        billing_cycle_id=billing_cycle_id,
    )

    BillingOneOffChargeLock.try_to_unlock(lock_)

    return result


@celery_task
def migrated_subscription_initial_task() -> None:
    if not settings.BILLING_MIGRATED_SUBSCRIPTION_INITIAL_TASK:
        return

    try:
        lock_ = BillingMigratedSubscriptionInitialTaskLock.lock(
            'migrated_subscription_initial_task'
        )
    except RedlockError:
        lock_ = None
    if not lock_:
        _logger.warning('Skip migrated_subscription_initial_task task')
        return

    qs = (
        MigratedSubscriptionInitialTask.objects.select_related('subscription')
        .filter(
            task_done__isnull=True,
            error_traceback__isnull=True,
        )
        .order_by('id')
    )

    for migrated_subscription in qs.iterator():
        try:
            match migrated_subscription.initial_task_type:
                case MigratedSubscriptionInitialTaskType.PREPARE_REGULAR_SUBSCRIPTION:
                    MigratedSubscriptionInitialTaskService.prepare_regular_subscription(
                        subscription=migrated_subscription.subscription,
                        regular_offer=migrated_subscription.offer,
                    )
                case _:
                    raise MigratedSubscriptionInitialTaskException(
                        f'Unrecognized action for entry '
                        f'MigratedSubscriptionInitialTask={migrated_subscription.id}'
                    )
        except SoftTimeLimitExceeded:
            raise
        except Exception as exc:  # pylint: disable=broad-except
            migrated_subscription.error_traceback = str(exc)
            migrated_subscription.save()
            _logger.exception(
                'Cant process migrated_subscription [ID=%s]',
                migrated_subscription.id,
            )
            continue
        else:
            migrated_subscription.mark_as_done()

    BillingMigratedSubscriptionInitialTaskLock.try_to_unlock(lock_)


@celery_task(time_limit=50 * 60, soft_time_limit=60 * 60)
def auto_switch_payment_processor_for_inactive_businesses_task() -> None:
    if not SwitchBraintreeToStripePaymentProcessorFlag():
        return
    billing_settings_braintree_inactive = BillingBusinessSettings.objects.filter(
        business__active=False,
        business__status__in=(
            Business.Status.BLOCKED_OVERDUE,
            Business.Status.TRIAL_BLOCKED,
            Business.Status.BLOCKED,
            Business.Status.CHURNED,
            Business.Status.SUSPENDED,
        ),
        payment_processor=PaymentProcessorType.BRAINTREE,
    )[:1000]
    for billing_settings in billing_settings_braintree_inactive.iterator():
        try:
            lock_ = BillingSubscriptionLock.lock(billing_settings.business_id)
        except RedlockError:
            lock_ = None
        if not lock_:
            _logger.warning(
                'Skip auto_switch_billing_payment_processor_for_inactive_businesses'
                ' for business %s',
                billing_settings.business_id,
            )
            return

        billing_settings.payment_processor = PaymentProcessorType.STRIPE
        billing_settings.save(
            _history={
                'metadata': {
                    'endpoint': 'auto_switch_payment_processor_for_inactive_businesses_task',
                },
            },
        )

        BillingSubscriptionLock.try_to_unlock(lock_)
