import logging
import typing as t
from datetime import datetime
from decimal import Decimal

from django.conf import settings
from django.db import transaction

from lib.feature_flag.feature.billing import (
    BillingDisableBTPaymentProcessor,
    SwitchBraintreeToStripePaymentProcessorFlag,
)
from lib.tools import (
    sget_v2,
    tznow,
)
from webapps.billing.business_discounts import BusinessDiscount
from webapps.billing.enums import (
    BillingErrorEventType,
    PaymentActionType,
    PaymentProcessorType,
    SubscriptionStatus,
    TransactionSource,
)
from webapps.billing.models import (
    BillingBusinessSettings,
    BillingCycle,
    BillingCycleProductCharge,
    BillingSubscribedProduct,
    BillingSubscription,
    BillingTransaction,
    PaymentProcessorError,
)
from webapps.billing.business_status import compute_business_status
from webapps.billing.payment_processor import PaymentProcessorBridge, StripePaymentProcessor
from webapps.marketplace.tasks import set_has_braintree_task

_logger = logging.getLogger('booksy.billing')


class BillingCycleSwitch:
    # TODO: Future optimisation -> create subscribed product snapshots
    #  and remove expired ones from subscription
    '''
    @classmethod
    def create_product_snapshots(
        cls,
        subscription: BillingSubscription,
        billing_cycle: BillingCycle,
    ):
        pass
    '''

    @classmethod
    def get_product_quantity(
        cls,
        product: BillingSubscribedProduct,
        billing_cycle: BillingCycle,
    ) -> int:
        """
        Product quantity can change during billing cycle switch when it's not
        explicitly chosen by user but is set based on usage, staff count etc.
        """
        quantity = product.quantity
        if product.is_charged_per_staffer:
            quantity = product.get_payable_staff_by_biz_id(product.business_id)
        elif product.is_postpaid_sms:
            quantity = max(
                0,
                # Sms usage was set during billing cycle closure
                billing_cycle.sms_usage - billing_cycle.sms_allowance,
            )
        return quantity

    @classmethod
    def update_future_products(
        cls,
        quantity: int,
        product: BillingSubscribedProduct,
    ):
        """
        In case merchant purchases subscription with discount for non-infinite
        billing cycles amount, we create 2 BillingSubscribedProduct objects
        - see SubscriptionCreator._prepare_subscribed_products() for details.

        If product quantity is set dynamically, based ex. on current staff
        members count, we need to change also future subscribed product quantity
        to have everything up-to-date.
        """
        if product.date_end is not None:
            # Product set up for future billing cycles may exist
            # (ex. for period after discount) - it should be updated, too.
            future_products = BillingSubscribedProduct.objects.filter(
                subscription_id=product.subscription_id,
                product_id=product.product_id,
                date_start__gte=product.date_end,
            )
            for future_product in future_products.iterator():
                price_info = future_product.get_product_prices(
                    quantity=quantity,
                    # This condition was checked during subscription purchase,
                    # product can still have discount type "No discount"
                    discount_applicable=True,
                    product=future_product,
                )
                BillingSubscribedProduct.objects.filter(
                    id=future_product.id,
                ).update(
                    **price_info,
                    _history={
                        'metadata': {'endpoint': 'BillingCycleSwitch.update_future_products'},
                    },
                )

    @classmethod
    def update_already_started(
        cls,
        product: BillingSubscribedProduct,
        price_info: dict,
        billing_cycle: BillingCycle,
        new_date_end: t.Optional[datetime],
    ) -> BillingSubscribedProduct:
        """
        update_product() helper.
        Updates subscribed product which date_start already passed.
        """
        product.date_end = billing_cycle.date_start
        product.save(
            update_fields=['date_end'],
            _history={
                'metadata': {'endpoint': 'BillingCycleSwitch.update_already_started'},
            },
        )
        # All params except pricing & quantity are the same
        copied_product_data = {
            attr: getattr(product, attr)
            for attr in (
                BillingSubscribedProduct.COPIED_FROM_PRODUCT
                + BillingSubscribedProduct.COPIED_FROM_OFFER
            )
        }
        new_product = BillingSubscribedProduct.objects.create(
            business_id=product.business_id,
            subscription_id=product.subscription_id,
            product_id=product.product_id,
            date_start=billing_cycle.date_start,
            date_end=new_date_end,
            **copied_product_data,
            **price_info,
        )
        return new_product

    @classmethod
    def update_starting(
        cls,
        product: BillingSubscribedProduct,
        price_info: dict,
    ) -> BillingSubscribedProduct:
        """
        update_product() helper.
        Updates subscribed product which will start along with new billing cycle
        """
        # Not using .update() as we need those params later
        for attr_name, value in price_info.items():
            setattr(product, attr_name, value)
        product.save(
            _history={
                'metadata': {'endpoint': 'BillingCycleSwitch.update_starting'},
            },
        )
        return product

    @classmethod
    def update_product(
        cls,
        product: BillingSubscribedProduct,
        billing_cycle: BillingCycle,
    ) -> BillingSubscribedProduct:
        """
        Updates product quantity. Returns new BillingSubscribedProduct
        if quantity changed, old object otherwise.

        In case of postpaid sms we will create new SubscribedProduct almost
        each billing cycle, unless merchant doesn't exceed his prepaid limit.
        """
        # TODO: Postpaid sms is based on usage -> we need to reset quantity to 0
        #  for future period (or do not display quantity to user before bc
        #  closure).
        # TODO: Pending product subscriptions case (no product in current cycle,
        #  but quantity based on staff count). As in products based on usage
        #  we could hide quantity for future products instead of updating them
        #  each time.
        quantity = cls.get_product_quantity(
            product=product,
            billing_cycle=billing_cycle,
        )
        # Products with future date start shouldn't be here, so only == and
        # < cases are considered
        if quantity != product.quantity and product.date_start <= billing_cycle.date_start:
            # Old product info is used as base
            price_info = product.get_product_prices(
                quantity=quantity,
                # This condition was checked during subscription purchase,
                # product can still have discount type "No discount"
                discount_applicable=True,
                product=product,
            )
            # See get_and_update_future_product() docstring for details.
            cls.update_future_products(quantity, product)
            # Product subscription starts with our billing cycle, so we can
            # update quantity & pricing on existing object
            if product.date_start == billing_cycle.date_start:
                product = cls.update_starting(product=product, price_info=price_info)
            # Product subscription started some time ago, so we need to set date
            # end for current subscribed product and create new one
            elif product.date_start < billing_cycle.date_start:
                product = cls.update_already_started(
                    product=product,
                    price_info=price_info,
                    billing_cycle=billing_cycle,
                    new_date_end=product.date_end,
                )
        return product

    @classmethod
    def update_product_charges(
        cls,
        charge_cycle_id: int,
        billing_cycle: BillingCycle,
        subscribed_products: t.Iterable[BillingSubscribedProduct],
    ):
        """
        :param charge_cycle_id: ID of BillingCycle object to which charge will
        be assigned.
        :param billing_cycle: BillingCycle object in which product was
        subscribed. ID will be the same as charge_cycle_id in case of prepaid
        products.
        :param subscribed_products: prepaid if from new billing cycle, postpaid
        if from old billing cycle. Always belong to passed billing_cycle.
        """
        products_cost = Decimal(0)
        # Indicates if list of BillingSubscribedProduct objects assigned to
        # BillingCycle has changed - will be needed later to calculate
        # sms allowance
        product_list_changed = False
        for product in subscribed_products:
            old_product_id = product.id
            product = cls.update_product(product, billing_cycle)
            if product.id != old_product_id:
                product_list_changed = True
            if product.quantity:
                charge = cls.save_product_to_charge(
                    product,
                    usage_from=billing_cycle.date_start,
                    usage_to=billing_cycle.date_end,
                    billing_cycle_id=charge_cycle_id,
                )

                if settings.CHARGE_GROSS_PRICE:
                    products_cost += charge.gross_final_price or charge.final_price
                else:
                    products_cost += charge.final_price

        return products_cost, product_list_changed

    @classmethod
    def update_charges(
        cls,
        subscription: BillingSubscription,
        new_billing_cycle: BillingCycle,
    ):
        """
        Update charges based on prepaid products from new billing cycle &
        postpaid products from previous one.

        new_cycle_ids_changed variable is used later to indicate if list of
        subscribed products has to be fetched once again from db in order to
        calculate sms allowance for new billing cycle.
        """
        old_billing_cycle = subscription.latest_cycle
        # Step 1: Get cost of prepaid products
        new_cycle_cost, new_cycle_ids_changed = cls.update_product_charges(
            charge_cycle_id=new_billing_cycle.id,
            billing_cycle=new_billing_cycle,
            subscribed_products=new_billing_cycle.prepaid_products,
        )
        products_cost = new_cycle_cost

        # Step 2: Get cost of postpaid products from previous cycle
        # PS. `old_billing_cycle` may be NULL for a first launch of PENDING
        #     subscription.
        if old_billing_cycle is not None:
            old_cycle_cost, _ = cls.update_product_charges(
                charge_cycle_id=new_billing_cycle.id,
                billing_cycle=old_billing_cycle,
                subscribed_products=old_billing_cycle.postpaid_products,
            )
            products_cost += old_cycle_cost
        charge_amount = products_cost + subscription.balance
        return charge_amount, new_cycle_ids_changed

    @classmethod
    def save_product_to_charge(
        cls,
        product: BillingSubscribedProduct,
        usage_from: datetime,
        usage_to: datetime,
        billing_cycle_id: int,
    ) -> BillingCycleProductCharge:
        charge = BillingCycleProductCharge(
            billing_cycle_id=billing_cycle_id,
            product_id=product.id,
            name=product.name,
            usage_from=usage_from,
            usage_to=usage_to,
            quantity=product.quantity,
            unit_price=product.unit_price,
            total_price=product.total_price,
            discount_granted=product.discount_granted,
            final_price=product.final_price,
            discounted_price=product.discounted_price,
            currency=product.currency,
        )

        charge.refresh_tax_related_data(save=False)

        charge.save()

        return charge

    @classmethod
    def get_subscription(
        cls,
        subscription_id: int,
    ):
        try:
            subscription = (
                BillingSubscription.objects.to_switch(
                    with_pending_churn_requests=False,
                )
                .filter(
                    id=subscription_id,
                )
                .select_related(
                    'previous',
                )
                .get()
            )
        except BillingSubscription.DoesNotExist:
            _logger.warning(
                '[BILLING CYCLE SWITCH], no new cycle to start, check '
                'subscription id, next billing date and expiry. Subscription id'
                ' %s',
                subscription_id,
            )
            return
        return subscription

    @classmethod
    def create_new_billing_cycle(
        cls,
        subscription: BillingSubscription,
        previous_cycle: t.Optional[BillingCycle],
    ) -> BillingCycle:
        if previous_cycle is None:
            # First launch of a PENDING subscription
            date_start = subscription.current_cycle_end
            number_of_cycle = 1
        else:
            date_start = previous_cycle.date_end
            number_of_cycle = previous_cycle.number_of_cycle + 1
        date_end = date_start + subscription.payment_period
        # Do not set sms allowance here, it has to be done after product
        # quantity update
        new_billing_cycle = BillingCycle.objects.create(
            business_id=subscription.business_id,
            subscription_id=subscription.id,
            date_start=date_start,
            date_end=date_end,
            number_of_cycle=number_of_cycle,
        )
        return new_billing_cycle

    @classmethod
    def handle_successful_charge(
        cls,
        subscription: BillingSubscription,
        next_billing_date: datetime,
        charge_amount: Decimal,
    ):
        subscription.next_billing_date = next_billing_date
        subscription.paid_through_date = subscription.next_billing_date
        if charge_amount >= Decimal(0):
            subscription.balance = Decimal(0)
        # Credits were granted
        else:
            subscription.balance = charge_amount
        subscription.status = SubscriptionStatus.ACTIVE
        subscription.save(
            update_fields=[
                'next_billing_date',
                'paid_through_date',
                'balance',
                'status',
            ],
            _history={
                'metadata': {'endpoint': 'BillingCycleSwitch.handle_successful_charge'},
            },
        )

        # Run trigger for Boost (e.g. PENDING subscriptions)
        if not subscription.business.has_braintree:
            set_has_braintree_task.delay(
                business_id=subscription.business_id,
                metadata=dict(
                    task='BillingCycleSwitch.handle_successful_charge',
                ),
            )

    @classmethod
    def handle_unsuccessful_charge(
        cls,
        subscription: BillingSubscription,
        next_billing_date: datetime,
        charge_amount: Decimal,
    ):
        subscription.next_billing_date = next_billing_date
        # Charge amount already contains previous balance
        subscription.balance = charge_amount
        subscription.status = SubscriptionStatus.BLOCKED
        subscription.save(
            update_fields=[
                'next_billing_date',
                'balance',
                'status',
            ],
            _history={
                'metadata': {'endpoint': 'BillingCycleSwitch.handle_unsuccessful_charge'},
            },
        )

    @classmethod
    def save_transaction(
        cls,
        subscription: BillingSubscription,
        billing_cycle_id: int,
        charge_amount: Decimal,
        payment_result,
    ):
        if getattr(payment_result, 'transaction', None) is not None:
            payment_processor = StripePaymentProcessor if BillingDisableBTPaymentProcessor() else PaymentProcessorBridge

            payment_processor.save_transaction(
                business_id=subscription.business_id,
                transaction=payment_result.transaction,
                transaction_source=TransactionSource.BILLING_SUBSCRIPTION.value,
                subscription_id=subscription.id,
                billing_cycle_id=billing_cycle_id,
            )
        elif charge_amount <= Decimal(0):
            # No real transaction was made, but we want to save info about that
            BillingTransaction.create_skipped(
                business_id=subscription.business_id,
                subscription_id=subscription.id,
                billing_cycle_id=billing_cycle_id,
                amount=charge_amount,
                currency=subscription.currency,
            )
        else:
            PaymentProcessorError.create_from_any_response(
                business_id=subscription.business_id,
                response=payment_result,
                event_type=BillingErrorEventType.CYCLE_SWITCH,
            )

    @classmethod
    def close_previous_subscription(
        cls,
        subscription: BillingSubscription,
    ) -> None:
        """
        Marks subscription as CLOSED (e.g. previous subscription of currently
        running PENDING subscription).
        """
        if (
            subscription.date_expiry is not None
            and subscription.date_expiry <= tznow()
            and subscription.status == SubscriptionStatus.ACTIVE
        ):
            subscription.status = SubscriptionStatus.CLOSED
            subscription.save(
                update_fields=['status'],
                _history={
                    'metadata': {'endpoint': 'BillingCycleSwitch.close_previous_subscription'},
                },
            )

            if subscription.latest_cycle:
                subscription.latest_cycle.close()

    @classmethod
    def switch_cycle(cls, subscription_id: int):
        """
        Main function.
        """
        subscription = cls.get_subscription(subscription_id=subscription_id)
        if not subscription:
            return

        old_billing_cycle = subscription.latest_cycle

        BusinessDiscount.apply_pending_discounts(subscription)

        with transaction.atomic():
            # ACHTUNG! PENDING subscription has not an old billing cycle
            if old_billing_cycle is not None:
                old_billing_cycle.close()

            if subscription.previous:
                cls.close_previous_subscription(subscription.previous)

            new_billing_cycle = cls.create_new_billing_cycle(
                subscription=subscription,
                previous_cycle=old_billing_cycle,
            )

            charge_amount, new_cycle_ids_changed = cls.update_charges(
                subscription,
                new_billing_cycle,
            )
            if new_cycle_ids_changed:
                # List of products has to be refreshed before calculating sms
                # allowance
                del new_billing_cycle.products
            new_billing_cycle.set_sms_allowance()

            if charge_amount > Decimal(0):
                # In case of unexpected error we want to rollback everything
                payment_processor = StripePaymentProcessor if BillingDisableBTPaymentProcessor() else PaymentProcessorBridge

                payment_result = payment_processor.create_transaction(
                    business_id=subscription.business_id,
                    amount=charge_amount,
                    currency=subscription.currency,
                    description=TransactionSource.BILLING_SUBSCRIPTION.label,
                    metadata={
                        'billing_cycle_id': new_billing_cycle.id,
                        'subscription_id': subscription.id,
                        'action': PaymentActionType.CYCLE_SWITCH.value,
                        'transaction_source': TransactionSource.BILLING_SUBSCRIPTION.value,
                    },
                )
            else:
                # Credits (or free subscription) were granted
                payment_result = None

            if payment_result is None or payment_result.is_success:
                cls.handle_successful_charge(
                    subscription=subscription,
                    next_billing_date=new_billing_cycle.date_end,
                    charge_amount=charge_amount,
                )
            else:
                cls.handle_unsuccessful_charge(
                    subscription=subscription,
                    next_billing_date=new_billing_cycle.date_end,
                    charge_amount=charge_amount,
                )
        # Transaction object is not necessary for subscription flow & we should
        # be able to restore it later
        cls.save_transaction(
            subscription=subscription,
            payment_result=payment_result,
            billing_cycle_id=new_billing_cycle.id,
            charge_amount=charge_amount,
        )
        compute_business_status(
            business_id=subscription.business_id,
            metadata=dict(task='BillingCycleSwitch.start_new_cycle'),
        )
        cls.switch_payment_processor(subscription.business_id)

    @classmethod
    def switch_payment_processor(cls, business_id):
        if not SwitchBraintreeToStripePaymentProcessorFlag():
            return
        billing_settings = BillingBusinessSettings.objects.filter(
            business_id=business_id,
        ).first()
        if sget_v2(billing_settings, ['payment_processor']) != PaymentProcessorType.BRAINTREE:
            return
        billing_settings.payment_processor = PaymentProcessorType.STRIPE
        billing_settings.save(
            _history={
                'metadata': {
                    'endpoint': 'switch_payment_processor_after_last_braintree_cycle',
                },
            },
        )
