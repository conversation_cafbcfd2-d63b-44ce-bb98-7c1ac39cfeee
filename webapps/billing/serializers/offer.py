import typing as t
from decimal import Decimal
from django.conf import settings
from rest_framework import serializers

from webapps import consts
from webapps.pos.calculations import round_currency
from webapps.billing.enums import ProductType
from webapps.billing.error_handling.exceptions import BillingException
from webapps.billing.models import (
    BillingProductOfferItem,
    BillingDiscountCodeUsage,
)


__all__ = [
    'ProductOfferItemSerializer',
    'ProductOfferItemsSerializer',
    'BusinessOfferSerializer',
]


class ProductOfferItemSerializer(serializers.ModelSerializer):
    """
    Merges product offer item (discount info etc) along with product
    and its add-ons. Remember to select all related objects beforehand.
    """

    class Meta:
        model = BillingProductOfferItem
        fields = (
            # ACHTUNG: Additional fields are added in .to_representation()
            # Base product fields
            'product_id',
            'product_type',
            'name',
            'full_price',
            'full_price_gross',
            'currency',
            'commission',
            'saas_tax_rate_percent',
            'saas_tax',
            # Base product fields - discounts
            'discount_type',
            'discount_amount',
            'discount_percentage',
            'discount_duration',
            'has_infinite_discount',
            # Staffer add-on
            'staff_add_on_id',
            'staffers_included',
            'staff_charge_limit',
            'staff_full_price',
            'staff_full_price_gross',
            'staff_tax_rate_percent',
            'staff_tax',
            # Staffer add-on - discounts (discount_duration & discount_infinite
            # will be taken from base product)
            'staff_discount_type',
            'staff_discount_amount',
            'staff_discount_amount_gross',
            'staff_discount_percentage',
            # Postpaid sms
            'sms_price',
            'sms_add_on_id',
            'sms_price_gross',
            'sms_tax_rate_percent',
            'sms_tax',
            # Free sms allowance - single product, staff add on & total
            'sms_amount',
            'staff_sms_amount',
        )

    product_type = serializers.CharField(source='product.product_type')
    name = serializers.CharField(source='product.name')
    full_price = serializers.DecimalField(
        source='product.unit_price',
        decimal_places=consts.PRICE_MAX_DECIMALS,
        max_digits=consts.PRICE_MAX_DIGITS,
    )
    full_price_gross = serializers.SerializerMethodField()
    saas_tax = serializers.SerializerMethodField()
    saas_tax_rate_percent = serializers.SerializerMethodField()
    commission = serializers.SerializerMethodField()
    discount_percentage = serializers.CharField(
        source='get_discount_frac_display',
    )
    currency = serializers.CharField(source='product.currency')

    # Staff add-on
    staff_add_on_id = serializers.IntegerField(
        source='product.staff_add_on.id',
    )
    staffers_included = serializers.IntegerField(
        source='product.staff_add_on.free_staff_qty',
    )
    staff_charge_limit = serializers.IntegerField(source='product.staff_add_on.max_qty')
    staff_full_price = serializers.DecimalField(
        source='product.staff_add_on.unit_price',
        decimal_places=consts.PRICE_MAX_DECIMALS,
        max_digits=consts.PRICE_MAX_DIGITS,
    )
    staff_full_price_gross = serializers.SerializerMethodField()
    staff_tax = serializers.SerializerMethodField()
    staff_tax_rate_percent = serializers.SerializerMethodField()
    staff_discount_type = serializers.CharField(source='product.staff_add_on.discount_type')
    staff_discount_amount = serializers.DecimalField(
        source='product.staff_add_on.discount_amount',
        decimal_places=consts.PRICE_MAX_DECIMALS,
        max_digits=consts.PRICE_MAX_DIGITS,
    )
    staff_discount_amount_gross = serializers.SerializerMethodField()
    staff_discount_percentage = serializers.CharField(
        source='staff_offer_item.get_discount_frac_display',
    )

    # Postpaid sms
    sms_price = serializers.DecimalField(
        source='product.sms_add_on.unit_price',
        decimal_places=consts.PRICE_MAX_DECIMALS + 1,
        max_digits=consts.PRICE_MAX_DIGITS + 1,
    )
    sms_price_gross = serializers.SerializerMethodField()
    sms_tax = serializers.SerializerMethodField()
    sms_tax_rate_percent = serializers.SerializerMethodField()
    sms_add_on_id = serializers.IntegerField(source='product.sms_add_on.id')

    # Prepaid/free sms
    sms_amount = serializers.IntegerField(source='product.sms_amount')
    staff_sms_amount = serializers.IntegerField(
        source='product.staff_add_on.sms_amount',
    )

    def to_representation(self, instance):
        # Some fields are added here to avoid redundant calculations
        ret = super().to_representation(instance)
        summary = instance.get_summary_with_add_ons(
            staff_count=self.context['staff_count'],
        )
        if summary['staff_discounted_price'] is not None:
            staff_discounted_formatted = format(summary['staff_discounted_price'], '.2f')
        else:
            staff_discounted_formatted = None

        ret.update(
            {
                'discounted_price': format(summary['main_discounted_price'], '.2f'),
                'staff_discounted_price': staff_discounted_formatted,
                'total_price': format(summary['price_with_add_ons'], '.2f'),
                'total_final_price': format(summary['final_price_with_add_ons'], '.2f'),
                'total_sms_amount': summary['sms_with_add_ons'],
                'total_discount': format(summary['discount_with_add_ons'], '.2f'),
            }
        )

        gross_summary = instance.get_gross_summary_with_add_ons(
            staff_count=self.context['staff_count'],
            tax_matrix=self.context['tax_matrix'],
        )

        if gross_summary['staff_discounted_price'] is not None:
            staff_discounted_gross_formatted = format(
                gross_summary['staff_discounted_price'], '.2f'
            )
        else:
            staff_discounted_gross_formatted = None

        ret.update(
            {
                'discounted_price_gross': format(gross_summary['main_discounted_price'], '.2f'),
                'staff_discounted_price_gross': staff_discounted_gross_formatted,
                'total_price_gross': format(gross_summary['price_with_add_ons'], '.2f'),
                'total_final_price_gross': format(gross_summary['final_price_with_add_ons'], '.2f'),
                'total_discount_gross': format(gross_summary['discount_with_add_ons'], '.2f'),
            }
        )

        return ret

    # TODO: Boost team
    def get_commission(self, obj):  # pylint: disable=unused-argument
        return None

    def calculate_gross_price(self, product_type, price):
        if price is None:
            return

        if tax_matrix := self.context.get('tax_matrix'):
            if service := ProductType.as_tax_rate_service(product_type):
                if tax_info := tax_matrix.tax_from_net(service, price):
                    return tax_info.gross_price

        return price

    def get_tax_percentage(self, product_type):
        if tax_matrix := self.context.get('tax_matrix'):
            if service := ProductType.as_tax_rate_service(product_type):
                if (tax_percent := tax_matrix.tax_percent(service)) is not None:
                    return str(tax_percent)

    def get_sms_tax_rate_percent(self, obj):
        if obj.product.sms_add_on is None:
            return

        return self.get_tax_percentage(obj.product.sms_add_on.product_type)

    def get_staff_tax_rate_percent(self, obj):
        if obj.product.staff_add_on is None:
            return

        return self.get_tax_percentage(obj.product.staff_add_on.product_type)

    def get_tax(self, product_type, price):
        if tax_matrix := self.context.get('tax_matrix'):
            if service := ProductType.as_tax_rate_service(product_type):
                if tax_summary := tax_matrix.tax_from_net(service, price):
                    return tax_summary.tax

    def get_saas_tax_rate_percent(self, obj):
        return self.get_tax_percentage(obj.product.product_type)

    def get_saas_tax(self, obj):
        price = self.get_tax(obj.product.product_type, obj.product.unit_price)

        if price is not None:
            return str(round_currency(price))

    def get_staff_tax(self, obj):
        if obj.product.staff_add_on is None:
            return

        price = self.get_tax(
            obj.product.staff_add_on.product_type,
            obj.product.staff_add_on.unit_price,
        )

        if price is not None:
            return str(round_currency(price))

    def get_sms_tax(self, obj):
        if obj.product.sms_add_on is None:
            return

        price = self.get_tax(obj.product.sms_add_on.product_type, obj.product.sms_add_on.unit_price)

        if price is not None:
            return str(price.quantize(Decimal('0.0001')))

    def get_sms_price_gross(self, obj):
        if obj.product.sms_add_on is None:
            return

        price = self.calculate_gross_price(
            obj.product.sms_add_on.product_type,
            obj.product.sms_add_on.unit_price,
        )

        if price is not None:
            return str(price.quantize(Decimal('0.0001')))

    def get_staff_full_price_gross(self, obj):
        if obj.product.staff_add_on is None:
            return

        price = self.calculate_gross_price(
            obj.product.staff_add_on.product_type,
            obj.product.staff_add_on.unit_price,
        )

        if price:
            return str(round_currency(price))

    def get_full_price_gross(self, obj):
        price = self.calculate_gross_price(
            obj.product.product_type,
            obj.product.unit_price,
        )

        if price:
            return str(round_currency(price))

    def get_staff_discount_amount_gross(self, obj):
        if obj.product.staff_add_on is None or obj.product.staff_add_on.discount_amount is None:
            return

        price = self.calculate_gross_price(
            obj.product.staff_add_on.product_type,
            obj.product.staff_add_on.discount_amount,
        )

        if price is not None:
            return str(round_currency(price))


class ProductOfferItemsSerializer(serializers.Serializer):
    """
    Used to represent product offer details.

    Details of products are grouped by product types, but only standalone
    types are considered. Add-ons are treated as part of standalone products.

    Expected output structure:

    {
        "SA": [
            {
                "name": "Booksy Subscription",
                "full_price": "20.00",
                ...
            },
        ],
        "BU": [...],
    }

    "SA" represents SAAS product type, "BU" represents BUSY product type etc.
    We assume that there's always 1 SAAS in single product offer (no more,
    no less). However, as other product types may be added in the future,
    product details are kept in lists. Standalone products can be merged with
    add-ons in terms of offer display only if desired standalone product
    quantity is 1 - otherwise we can't calculate prices.
    """

    def __init__(self, offer_id: int, exclude_ids: t.Optional[t.Iterable] = None, **kwargs):
        super().__init__(**kwargs)
        self.offer_items = self.get_offer_items(offer_id, exclude_ids)

    @property
    def data(self):
        if not hasattr(self, '_data'):
            self._data = self.to_representation()
        return self._data

    def get_offer_items(
        self,
        offer_id: int,
        exclude_ids: t.Optional[t.Iterable] = None,
    ):
        # Get all products included in offer.
        offer_items_qs = BillingProductOfferItem.objects.filter(
            offer_id=offer_id,
            active=True,
        )
        # Some ids may be excluded when ex. we want to show items not yet
        # subscribed by the user
        if exclude_ids:
            offer_items_qs = offer_items_qs.exclude(product_id__in=exclude_ids)

        offer_items = offer_items_qs.select_related('product')
        return offer_items

    def split_items(
        self,
        standalone_types: t.Iterable[str],
    ):
        """
        Divides product offer items into standalone ones and add-ons.
        Sets additional params, if needed.
        """
        standalone_items = {}
        add_ons = {}
        for item in self.offer_items:
            if item.product.product_type in standalone_types:
                standalone_items[item.product_id] = item
            else:
                # Needed for fields based on assigned add-ons
                item.product.discount_type = item.discount_type
                item.product.discount_amount = item.discount_amount
                item.product.discount_frac = item.discount_frac

                add_ons[item.product_id] = item
        return standalone_items, add_ons

    def set_add_ons(
        self,
        standalone_items: dict,
        add_ons: dict,
    ):
        """Matches standalone products with add-ons."""
        for offer_item in standalone_items.values():
            product = offer_item.product

            # staff_add_on_id - id of BillingProduct
            # .staff_add_on - BillingProduct object
            if product.staff_add_on_id:
                # Staff add-on must be included in product offer, if
                # standalone product staff_add_on_id is not empty.
                try:
                    staff_offer_item = add_ons[product.staff_add_on_id]
                except KeyError as err:
                    raise BillingException(
                        'Wrong offer configuration - staff add-on required by'
                        ' product but missing in offer'
                    ) from err
                offer_item.staff_offer_item = staff_offer_item

                product.staff_add_on = staff_offer_item.product

            if product.sms_add_on_id:
                # Sms add-on must be included in product offer, too
                try:
                    sms_offer_item = add_ons[product.sms_add_on_id]
                except KeyError as err:
                    raise BillingException(
                        'Wrong offer configuration - sms add-on required by'
                        ' product but missing in offer'
                    ) from err
                offer_item.sms_offer_item = sms_offer_item

                product.sms_add_on = sms_offer_item.product

    def serialize_product_details(
        self,
        standalone_types: t.Iterable[str],
        standalone_items: dict,
    ):
        products_by_type = {
            # Product type, ex. SA (saas) : list of serialized products
            t_.value: []
            for t_ in standalone_types
        }
        for item in standalone_items.values():
            serialized = ProductOfferItemSerializer(
                instance=item,
                context=self.context,
            )
            products_by_type[item.product.product_type].append(serialized.data)
        return products_by_type

    def to_representation(self, **kwargs):  # pylint: disable=unused-argument, arguments-differ
        """
        Get product offer items details, divided by type.
        """
        standalone_types = ProductType.standalone()
        # Match add-ons (ex. sms, staffers) with standalone products (the ones,
        # that user can subscribe by himself). Add-ons should be included in
        # product offer (=> no need to do additional .select_related).
        standalone_items, add_ons = self.split_items(
            standalone_types=standalone_types,
        )
        self.set_add_ons(standalone_items=standalone_items, add_ons=add_ons)
        products_by_type = self.serialize_product_details(
            standalone_types=standalone_types,
            # Not all standalone types have to be included in offer, but we
            # want full list of types in output
            standalone_items=standalone_items,
        )

        return products_by_type


class BusinessOfferSerializer(serializers.Serializer):
    offer_id = serializers.IntegerField(source='id')
    offer_valid_to = serializers.SerializerMethodField()
    products = serializers.SerializerMethodField()
    staff_count = serializers.SerializerMethodField()
    subscription_period = serializers.SerializerMethodField()
    discount_code = serializers.SerializerMethodField()

    def get_products(self, obj):
        product_serializer = ProductOfferItemsSerializer(
            offer_id=obj.id,
            context=self.context,
        )
        return product_serializer.data

    def get_discount_code(self, obj):
        if (
            discount_code_used := BillingDiscountCodeUsage.objects.filter(
                business_id=self.context['business_id'],
                offer_id=obj.id,
            )
            .select_related('discount_code')
            .first()
        ):
            return discount_code_used.discount_code.discount_code

    def get_subscription_period(self, _):
        return self.context.get('subscription_duration', settings.DEFAULT_SUBSCRIPTION_DURATION)

    def get_staff_count(self, _):
        return self.context.get('staff_count')

    def get_offer_valid_to(self, _):
        return self.context.get('offer_valid_to')
