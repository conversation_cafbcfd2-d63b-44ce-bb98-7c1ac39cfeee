from lib.events import EventSignal
from webapps.stripe_app.enums import StripeEventType, Subscriber


stripe_app_payment_method_attached = EventSignal(
    event_type=f'{Subscriber.BILLING}_stripe_app_{StripeEventType.PAYMENT_METHOD_ATTACHED}'
)

stripe_app_payment_intent_failed = EventSignal(
    event_type=f'{Subscriber.BILLING}_stripe_app_{StripeEventType.PAYMENT_INTENT_PAYMENT_FAILED}'
)

stripe_app_payment_intent_succeeded = EventSignal(
    event_type=f'{Subscriber.BILLING}_stripe_app_{StripeEventType.PAYMENT_INTENT_SUCCEEDED}'
)

stripe_app_charge_refund_update = EventSignal(
    event_type=f'{Subscriber.BILLING}_stripe_app_{StripeEventType.CHARGE_REFUND_UPDATED}'
)

stripe_app_customer_created = EventSignal(
    event_type=f'{Subscriber.BILLING}_stripe_app_{StripeEventType.CUSTOMER_CREATED}'
)

stripe_app_charge_refunded = EventSignal(
    event_type=f'{Subscriber.BILLING}_stripe_app_{StripeEventType.CHARGE_REFUNDED}'
)

stripe_app_payment_method_automatically_update = EventSignal(
    event_type=f'{Subscriber.BILLING}_stripe_app_{StripeEventType.PAYMENT_METHOD_AUTO_UPDATE}'
)

stripe_app_dispute_closed = EventSignal(
    event_type=f'{Subscriber.BILLING}_stripe_app_{StripeEventType.DISPUTE_CLOSED}'
)

stripe_app_dispute_created = EventSignal(
    event_type=f'{Subscriber.BILLING}_stripe_app_{StripeEventType.DISPUTE_CREATED}'
)

stripe_app_dispute_updated = EventSignal(
    event_type=f'{Subscriber.BILLING}_stripe_app_{StripeEventType.DISPUTE_UPDATED}'
)

billing_subscription_created = EventSignal(event_type=f'{Subscriber.BILLING}_subscripiton_created')

new_billing_subscription = EventSignal(event_type=f'{Subscriber.BILLING}_new_subscription')

business_churn_scheduled = EventSignal(event_type=f"{Subscriber.BILLING}_business_churn_scheduled")
