import logging

from webapps.billing.models import BillingTransaction
from webapps.billing.models.payment.dispute import BillingTransactionDispute
from webapps.stripe_app.services.refund import DisputeResult

logger = logging.getLogger('booksy.billing')


def handle_dispute_from_webhook(dispute_result: dict) -> BillingTransactionDispute | None:
    dispute_result = DisputeResult.from_dict(dispute_result)

    if not (
        transaction := BillingTransaction.objects.filter(
            external_id=dispute_result.dispute.payment_intent_stripe_id
        ).first()
    ):
        logger.error(
            'Unable to match Dispute with Transaction id: %s.',
            dispute_result.dispute.payment_intent_stripe_id,
        )
        return

    dispute, created = BillingTransactionDispute.objects.get_or_create(
        external_id=dispute_result.dispute.stripe_id,
        defaults=dict(
            transaction=transaction,
            amount=dispute_result.dispute.decimal_amount,
            currency=dispute_result.dispute.currency,
            status=dispute_result.dispute.status,
            reason=dispute_result.dispute.reason,
            disputed_on=dispute_result.dispute.disputed_on,
            respond_by=dispute_result.dispute.respond_by,
        ),
    )

    if not created:
        dispute.amount = dispute_result.dispute.decimal_amount
        dispute.currency = dispute_result.dispute.currency
        dispute.status = dispute_result.dispute.status
        dispute.reason = dispute_result.dispute.reason
        dispute.disputed_on = dispute_result.dispute.disputed_on
        dispute.respond_by = dispute_result.dispute.respond_by
        dispute.save()

    return dispute
