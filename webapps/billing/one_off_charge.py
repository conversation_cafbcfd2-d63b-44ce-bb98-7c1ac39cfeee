from lib.feature_flag.feature.billing import BillingDisableBTPaymentProcessor
from lib.tools import tznow

from webapps.billing.enums import (
    BillingErrorEventType,
    PaymentActionType,
    BillingOneOffStatus,
    TransactionSource,
    TransactionDescription,
)
from webapps.billing.models import (
    PaymentProcessorError,
    BillingOneOffChargeRequest,
)
from webapps.billing.payment_processor import PaymentProcessorBridge, StripePaymentProcessor


class OneOffCharge:
    """One-off (one-time) charges utils."""

    @staticmethod
    def handle_successful_charge(one_off_request: BillingOneOffChargeRequest) -> dict:
        one_off_request.status = BillingOneOffStatus.SUCCEEDED
        one_off_request.save(update_fields=['status'])
        one_off_request.one_off_charge.charged_at = tznow()
        one_off_request.one_off_charge.save(update_fields=['charged_at'])
        return {
            'message': 'One-off charge has been charged successfully.',
        }

    @staticmethod
    def handle_unsuccessful_charge(
        one_off_request: BillingOneOffChargeRequest,
        payment_result,
    ) -> dict:
        one_off_request.status = BillingOneOffStatus.FAILED
        one_off_request.save(update_fields=['status'])
        return StripePaymentProcessor.parse_error(
            response=payment_result,
        )

    @classmethod
    def make_charge(
        cls,
        billing_cycle_id: int,
        one_off_request_id: int,
        operator_id: int | None = None,
    ):
        one_off_request = (
            BillingOneOffChargeRequest.objects.filter(id=one_off_request_id)
            .select_related('one_off_charge')
            .first()
        )

        one_off_charge = one_off_request.one_off_charge
        payment_processor = (
            StripePaymentProcessor if BillingDisableBTPaymentProcessor() else PaymentProcessorBridge
        )

        charge_result = payment_processor.create_transaction(
            business_id=one_off_charge.business_id,
            amount=one_off_charge.total_gross_price,
            currency=one_off_charge.currency,
            description=TransactionDescription.ONE_OFF_CHARGE,
            metadata={
                'billing_cycle_id': billing_cycle_id,
                'operator_id': operator_id,
                'one_off_request_id': one_off_request_id,
                'action': PaymentActionType.ONE_OFF_CHARGE.value,
                'transaction_source': TransactionSource.ONE_OFF_CHARGE.value,
            },
        )

        if charge_result.is_success:
            message = cls.handle_successful_charge(
                one_off_request=one_off_request,
            )
        else:
            message = cls.handle_unsuccessful_charge(
                one_off_request=one_off_request,
                payment_result=charge_result,
            )
        if getattr(charge_result, 'transaction', None) is not None:
            payment_processor = (
                StripePaymentProcessor
                if BillingDisableBTPaymentProcessor()
                else PaymentProcessorBridge
            )
            payment_processor.save_transaction(
                billing_cycle_id=billing_cycle_id,
                business_id=one_off_charge.business_id,
                transaction=charge_result.transaction,
                transaction_source=TransactionSource.ONE_OFF_CHARGE,
            )
        else:
            PaymentProcessorError.create_from_any_response(
                business_id=one_off_charge.business.id,
                response=charge_result,
                event_type=BillingErrorEventType.ONE_OFF_PAYMENT,
            )

        return message
