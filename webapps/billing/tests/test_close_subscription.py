# pylint: disable=protected-access
import datetime

from django.test import TestCase, override_settings
from freezegun import freeze_time
from mock import (
    call,
    patch,
)
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC

from lib.locks import BillingSubscriptionLock
from lib.tools import tznow
from webapps.billing.close_subscription import CloseSubscription, SubscriptionState
from webapps.billing.enums import SubscriptionStatus
from webapps.billing.models import (
    BillingCycle,
    BillingSubscription,
)
from webapps.billing.tasks import close_subscriptions
from webapps.business.models import Business


class TestSubscriptionState(TestCase):
    def test_get_business(self):
        business = baker.make(Business)

        self.assertEqual(SubscriptionState._get_business(business), business)
        self.assertEqual(SubscriptionState._get_business(business.pk), business)

        with self.assertRaises(Business.DoesNotExist):
            SubscriptionState._get_business(business.pk + 1)

    @parameterized.expand(
        [
            (
                status,
                status  # pylint: disable=undefined-variable
                in [
                    Business.Status.BLOCKED,
                    Business.Status.CHURNED,
                ],
            )
            for status in Business.Status.choices_map().keys()
        ]
    )
    def test_should_not_be_renewed_for_business(self, status, result):
        business = baker.make(Business, status=status)
        self.assertEqual(SubscriptionState.should_not_be_renewed_for_business(business), result)
        self.assertEqual(SubscriptionState.should_not_be_renewed_for_business(business.id), result)


class TestCloseSubscription(TestCase):
    def test_close_subscription_when_already_closed(self):
        subscription = baker.make(
            BillingSubscription,
            date_expiry=tznow(),
            status=SubscriptionStatus.CLOSED,
        )
        updated = subscription.updated

        CloseSubscription._close_subscription(subscription)

        subscription.refresh_from_db()
        self.assertEqual(subscription.updated, updated)

    @parameterized.expand(
        [
            (SubscriptionStatus.ACTIVE, SubscriptionStatus.CLOSED),
            (SubscriptionStatus.PENDING, SubscriptionStatus.CLOSED),
            (SubscriptionStatus.CLOSED, SubscriptionStatus.CLOSED),
            (SubscriptionStatus.BLOCKED, SubscriptionStatus.CLOSED),
            (SubscriptionStatus.SUSPENDED, SubscriptionStatus.CLOSED),
            (SubscriptionStatus.SUSPENSION_PENDING, SubscriptionStatus.CLOSED),
        ]
    )
    @override_settings(SAVE_HISTORY=True)
    def test_close_active_subscription(self, status, final_status):
        subscription = baker.make(
            BillingSubscription,
            next_billing_date=tznow(),
            date_expiry=None,
            status=status,
        )
        billing_cycle = baker.make(
            BillingCycle,
            subscription=subscription,
        )

        CloseSubscription._close_subscription(subscription)

        subscription.refresh_from_db()
        self.assertEqual(
            subscription.date_expiry,
            subscription.next_billing_date,
        )
        self.assertEqual(subscription.status, final_status)

        history = subscription.history.all().first()
        self.assertIn('CloseSubscription._close_subscription', history.metadata)

        billing_cycle.refresh_from_db()
        self.assertTrue(billing_cycle.is_closed)

    def test_close_pending_subscription(self):
        date_start = tznow() + datetime.timedelta(days=3)
        subscription = baker.make(
            BillingSubscription,
            date_start=date_start,
            next_billing_date=date_start,
            date_expiry=None,
            status=SubscriptionStatus.PENDING,
        )

        CloseSubscription._close_subscription(subscription)

        subscription.refresh_from_db()
        self.assertEqual(subscription.date_expiry, date_start)

    def test_close_active_subscription_with_tznow_dt(self):
        subscription = baker.make(
            BillingSubscription,
            date_start=datetime.datetime(2023, 2, 20, tzinfo=UTC),
            next_billing_date=datetime.datetime(2023, 3, 20, tzinfo=UTC),
            date_expiry=None,
            status=SubscriptionStatus.ACTIVE,
        )

        with freeze_time(datetime.datetime(2023, 3, 1, tzinfo=UTC)):
            CloseSubscription._close_subscription(subscription)

        subscription.refresh_from_db()
        self.assertEqual(
            subscription.date_expiry,
            datetime.datetime(2023, 3, 1, tzinfo=UTC),
        )

    def test_close_subscription_when_no_billing_cycle(self):
        subscription = baker.make(
            BillingSubscription,
            next_billing_date=tznow(),
            date_expiry=None,
            status=SubscriptionStatus.PENDING,
        )

        CloseSubscription._close_subscription(subscription)

        subscription.refresh_from_db()
        self.assertEqual(subscription.status, SubscriptionStatus.CLOSED)

    @patch.object(CloseSubscription, '_close_subscription')
    def test_close_subscriptions_without_current_subscription(
        self,
        close_subscription_mock,
    ):
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            has_new_billing=True,
        )
        baker.make(  # already closed
            BillingSubscription,
            business=business,
            date_expiry=tznow(),
            status=SubscriptionStatus.CLOSED,
        )
        subscription2 = baker.make(
            BillingSubscription,
            business=business,
            date_expiry=tznow(),
            status=SubscriptionStatus.ACTIVE,
        )
        subscription3 = baker.make(
            BillingSubscription,
            business=business,
            date_expiry=None,
            status=SubscriptionStatus.PENDING,
        )
        baker.make(  # other business
            BillingSubscription,
            date_expiry=None,
            status=SubscriptionStatus.PENDING,
        )

        CloseSubscription.close_subscriptions(business_id=business.id, current_subscription=None)

        self.assertEqual(
            close_subscription_mock.mock_calls,
            [call(subscription2), call(subscription3)],
        )

    @patch.object(CloseSubscription, '_close_subscription')
    def test_close_subscriptions_with_current_subscription(
        self,
        close_subscription_mock,
    ):
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            has_new_billing=True,
        )
        subscription = baker.make(
            BillingSubscription,
            business=business,
            date_expiry=None,
            status=SubscriptionStatus.ACTIVE,
        )

        CloseSubscription.close_subscriptions(
            business_id=business.id, current_subscription=subscription
        )

        self.assertEqual(close_subscription_mock.mock_calls, [call(subscription)])


class TestCloseSubscriptionsTask(TestCase):
    @patch.object(CloseSubscription, 'close_subscriptions')
    def test_close_subscriptions__allowed(self, close_subscriptions_mock):
        business = baker.make(
            Business,
            status=Business.Status.BLOCKED,
            has_new_billing=True,
        )

        close_subscriptions.run(business_id=business.id)
        self.assertEqual(close_subscriptions_mock.mock_calls, [call(business_id=business.id)])

    @patch.object(CloseSubscription, 'close_subscriptions')
    def test_close_subscriptions__not_allowed(self, close_subscriptions_mock):
        business = baker.make(
            Business,
            status=Business.Status.BLOCKED_OVERDUE,
            has_new_billing=True,
        )

        close_subscriptions.run(business_id=business.id)
        self.assertEqual(close_subscriptions_mock.call_count, 0)

    @patch.object(CloseSubscription, 'close_subscriptions')
    def test_close_subscriptions__locked(self, close_subscriptions_mock):
        business = baker.make(
            Business,
            status=Business.Status.BLOCKED_OVERDUE,
            has_new_billing=True,
        )
        BillingSubscriptionLock.lock(business.pk)

        close_subscriptions.run(business_id=business.id)
        self.assertEqual(close_subscriptions_mock.call_count, 0)
