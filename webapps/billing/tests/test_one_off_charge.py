from unittest.mock import patch, MagicMock, call
from django.urls.base import reverse
from model_bakery import baker

from lib.tests.utils import override_feature_flag, override_eppo_feature_flag
from lib.feature_flag.feature.billing import (
    BillingAllowMakeOneOffTransactionsFlag,
    BillingDisableBTPaymentProcessor,
)
from lib.locks import BillingOneOffChargeLock

from webapps.billing.tasks import create_one_off_transaction_task
from webapps.stripe_app.errors import ErrorObject

from webapps.billing.services.charge import ChargeResult

from webapps.billing.payment_processor import PaymentProcessorBridge, StripePaymentProcessor

from webapps.admin_extra.tests import DjangoTestCase
from webapps.billing.enums import (
    BillingOneOffStatus,
    PaymentActionType,
    TransactionSource,
)
from webapps.billing.models import (
    BillingOneOffCharge,
    BillingOneOffChargeRequest,
    BillingCycle,
)
from webapps.billing.one_off_charge import OneOffCharge
from webapps.business.models import Business
from webapps.user.models import User


class TestOneOffCharge(DjangoTestCase):
    def setUp(self) -> None:
        super().setUp()
        self.business = baker.make(
            Business,
            has_new_billing=False,
        )
        self.billing_cycle = baker.make(BillingCycle, business=self.business, is_open=True)
        self.one_off_charge = baker.make(BillingOneOffCharge, business=self.business)
        self.one_off_request = baker.make(
            BillingOneOffChargeRequest,
            one_off_charge=self.one_off_charge,
        )
        self.user = baker.make(User)

    @patch.object(PaymentProcessorBridge, 'save_transaction')
    @patch.object(PaymentProcessorBridge, 'create_transaction')
    def test_make_charge_ok_with_transaction(self, create_transaction, save_transaction):
        stripe_transaction = MagicMock()
        create_transaction.return_value = ChargeResult(
            is_success=True,
            transaction=stripe_transaction,
        )

        result = OneOffCharge.make_charge(
            operator_id=self.user.id,
            one_off_request_id=self.one_off_request.id,
            billing_cycle_id=self.billing_cycle.id,
        )

        one_off_request = BillingOneOffChargeRequest.objects.get()

        self.assertEqual(result.get('message'), 'One-off charge has been charged successfully.')
        self.assertEqual(one_off_request.status, BillingOneOffStatus.SUCCEEDED)
        self.assertIsNotNone(one_off_request.one_off_charge.charged_at)

        self.assertEqual(
            create_transaction.call_args,
            call(
                business_id=self.business.id,
                amount=self.one_off_charge.total_gross_price,
                currency=self.one_off_charge.currency,
                description='One-Off Charge',
                metadata={
                    'operator_id': self.user.id,
                    'one_off_request_id': one_off_request.id,
                    'action': PaymentActionType.ONE_OFF_CHARGE.value,
                    'billing_cycle_id': self.billing_cycle.id,
                    'transaction_source': TransactionSource.ONE_OFF_CHARGE.value,
                },
            ),
        )
        self.assertEqual(
            save_transaction.call_args,
            call(
                business_id=self.business.id,
                transaction=stripe_transaction,
                billing_cycle_id=self.billing_cycle.id,
                transaction_source=TransactionSource.ONE_OFF_CHARGE,
            ),
        )

    @override_eppo_feature_flag({BillingDisableBTPaymentProcessor.flag_name: True})
    @patch.object(StripePaymentProcessor, 'save_transaction')
    @patch.object(StripePaymentProcessor, 'create_transaction')
    @patch.object(PaymentProcessorBridge, 'save_transaction')
    @patch.object(PaymentProcessorBridge, 'create_transaction')
    def test_disabling_payment_processor_bridge_make_charge_ok(
        self,
        bridge_create_transaction,
        bridge_save_transaction,
        create_transaction,
        save_transaction,
    ):
        stripe_transaction = MagicMock()
        create_transaction.return_value = ChargeResult(
            is_success=True,
            transaction=stripe_transaction,
        )

        result = OneOffCharge.make_charge(
            operator_id=self.user.id,
            one_off_request_id=self.one_off_request.id,
            billing_cycle_id=self.billing_cycle.id,
        )

        one_off_request = BillingOneOffChargeRequest.objects.get()

        self.assertEqual(
            'One-off charge has been charged successfully.',
            result.get('message'),
        )
        self.assertEqual(BillingOneOffStatus.SUCCEEDED, one_off_request.status)
        self.assertIsNotNone(one_off_request.one_off_charge.charged_at)

        self.assertEqual(
            call(
                business_id=self.business.id,
                amount=self.one_off_charge.total_gross_price,
                currency=self.one_off_charge.currency,
                description='One-Off Charge',
                metadata={
                    'operator_id': self.user.id,
                    'one_off_request_id': one_off_request.id,
                    'action': PaymentActionType.ONE_OFF_CHARGE.value,
                    'billing_cycle_id': self.billing_cycle.id,
                    'transaction_source': TransactionSource.ONE_OFF_CHARGE.value,
                },
            ),
            create_transaction.call_args,
        )
        self.assertEqual(
            call(
                business_id=self.business.id,
                transaction=stripe_transaction,
                billing_cycle_id=self.billing_cycle.id,
                transaction_source=TransactionSource.ONE_OFF_CHARGE,
            ),
            save_transaction.call_args,
        )
        self.assertFalse(bridge_create_transaction.called)
        self.assertFalse(bridge_save_transaction.called)

    @patch.object(PaymentProcessorBridge, 'create_transaction')
    def test_make_charge_error_without_transaction(
        self,
        create_transaction,
    ):
        create_transaction.return_value = ChargeResult(
            is_success=False, error=ErrorObject(message="test error")
        )

        result = OneOffCharge.make_charge(
            operator_id=self.user.id,
            one_off_request_id=self.one_off_request.id,
            billing_cycle_id=self.billing_cycle.id,
        )

        one_off_request = BillingOneOffChargeRequest.objects.get()
        self.assertEqual(result['internal_errors']['message'], "test error")
        self.assertEqual(one_off_request.status, BillingOneOffStatus.FAILED)
        self.assertEqual(
            create_transaction.call_args,
            call(
                business_id=self.business.id,
                amount=self.one_off_charge.total_gross_price,
                currency=self.one_off_charge.currency,
                description='One-Off Charge',
                metadata={
                    'operator_id': self.user.id,
                    'one_off_request_id': one_off_request.id,
                    'action': PaymentActionType.ONE_OFF_CHARGE.value,
                    'billing_cycle_id': self.billing_cycle.id,
                    'transaction_source': TransactionSource.ONE_OFF_CHARGE.value,
                },
            ),
        )

    @patch.object(PaymentProcessorBridge, 'save_transaction')
    @patch.object(PaymentProcessorBridge, 'create_transaction')
    def test_make_charge_error_with_transaction(self, create_transaction, save_transaction):
        stripe_transaction = MagicMock()
        create_transaction.return_value = ChargeResult(
            is_success=False,
            transaction=stripe_transaction,
            error=ErrorObject(message="test error"),
        )

        result = OneOffCharge.make_charge(
            one_off_request_id=self.one_off_request.id,
            billing_cycle_id=self.billing_cycle.id,
        )

        one_off_request = BillingOneOffChargeRequest.objects.get()
        self.assertEqual(result['internal_errors']['message'], "test error")
        self.assertEqual(one_off_request.status, BillingOneOffStatus.FAILED)

        self.assertEqual(
            create_transaction.call_args,
            call(
                business_id=self.business.id,
                amount=self.one_off_charge.total_gross_price,
                currency=self.one_off_charge.currency,
                description='One-Off Charge',
                metadata={
                    'operator_id': None,
                    'one_off_request_id': one_off_request.id,
                    'action': PaymentActionType.ONE_OFF_CHARGE.value,
                    'billing_cycle_id': self.billing_cycle.id,
                    'transaction_source': TransactionSource.ONE_OFF_CHARGE.value,
                },
            ),
        )
        self.assertEqual(
            save_transaction.call_args,
            call(
                business_id=self.business.id,
                transaction=stripe_transaction,
                billing_cycle_id=self.billing_cycle.id,
                transaction_source=TransactionSource.ONE_OFF_CHARGE,
            ),
        )

    @patch('webapps.billing.tasks.OneOffCharge.make_charge')
    def test_one_off_task_locked(self, mock_make_charge):
        BillingOneOffChargeLock.lock(self.business.pk)

        create_one_off_transaction_task.run(
            business_id=self.business.id,
            operator_id=self.user.id,
            one_off_request_id=self.one_off_request.id,
            billing_cycle_id=self.billing_cycle.id,
        )
        self.assertFalse(mock_make_charge.called)

    @patch('webapps.billing.tasks.OneOffCharge.make_charge')
    def test_one_off_task_ok(self, mock_make_charge):
        create_one_off_transaction_task.run(
            business_id=self.business.id,
            operator_id=self.user.id,
            one_off_request_id=self.one_off_request.id,
            billing_cycle_id=self.billing_cycle.id,
        )
        self.assertEqual(
            mock_make_charge.call_args,
            call(
                operator_id=self.user.id,
                one_off_request_id=self.one_off_request.id,
                billing_cycle_id=self.billing_cycle.id,
            ),
        )

    @override_feature_flag({BillingAllowMakeOneOffTransactionsFlag.flag_name: True})
    @patch('webapps.billing.admin.one_off_charge.BillingOneOffChargeAdmin.has_add_permission')
    def test_make_transaction_button_visible(self, mock_has_add_permission):
        one_off_charge = baker.make(BillingOneOffCharge)
        mock_has_add_permission.return_value = True
        self.login_admin()
        url = reverse('admin:billing_billingoneoffcharge_change', args=(one_off_charge.id,))
        resp = self.client.get(url)
        self.assertContains(resp, 'Make or Retry Charge')

    @override_feature_flag({BillingAllowMakeOneOffTransactionsFlag.flag_name: False})
    @patch('webapps.billing.admin.one_off_charge.BillingOneOffChargeAdmin.has_add_permission')
    def test_make_transaction_button_visible_future_flag(self, mock_has_add_permission):
        one_off_charge = baker.make(BillingOneOffCharge)
        mock_has_add_permission.return_value = True
        self.login_admin()
        url = reverse('admin:billing_billingoneoffcharge_change', args=(one_off_charge.id,))
        resp = self.client.get(url)
        self.assertNotContains(resp, 'Make or Retry Charge')

    @patch('webapps.billing.admin.one_off_charge.BillingOneOffChargeAdmin.has_add_permission')
    def test_make_transaction_button_invisible_already_charged(self, mock_has_add_permission):
        one_off_charge = baker.make(BillingOneOffCharge)
        baker.make(BillingOneOffChargeRequest, one_off_charge=one_off_charge)
        mock_has_add_permission.return_value = True
        self.login_admin()
        url = reverse('admin:billing_billingoneoffcharge_change', args=(one_off_charge.id,))
        resp = self.client.get(url)
        self.assertNotContains(resp, 'Make or Retry Charge')

    @patch('webapps.billing.admin.one_off_charge.create_one_off_transaction_task.delay')
    def test_make_transaction_business_validation_error(self, mock_transaction_task):
        one_off_charge = baker.make(BillingOneOffCharge)
        self.login_admin()
        url = reverse(
            'admin:make_transaction',
            args=(one_off_charge.id,),
        )
        resp = self.client.get(url, follow=True)
        self.assertFalse(mock_transaction_task.called)
        self.assertContains(resp, 'Only businesses with new billing allowed.')

    @patch('webapps.billing.admin.one_off_charge.create_one_off_transaction_task.delay')
    def test_make_transaction_ok(self, mock_transaction_task):
        one_off_charge = baker.make(BillingOneOffCharge)
        self.login_admin()
        url = reverse(
            'admin:make_transaction',
            args=(one_off_charge.id,),
        )
        self.client.get(url, follow=True)
        one_off_request = BillingOneOffChargeRequest.objects.get()
        mock_transaction_task(
            call(
                business_id=self.business.id,
                one_off_request_id=one_off_request.id,
                billing_cycle_id=self.billing_cycle.id,
                operator_id=None,
            )
        )
        self.assertEqual(one_off_request.status, BillingOneOffStatus.INITIAL)
