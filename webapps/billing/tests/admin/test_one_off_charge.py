from datetime import timedelta
from decimal import Decimal
from model_bakery import baker

from lib.tools import tznow

from webapps.admin_extra.tests import DjangoTestCase
from webapps.billing.enums import PaymentProcessorType
from webapps.billing.forms import BillingOneOffChargeForm, BillingOneOffTransactionForm
from webapps.billing.models import (
    BillingOneOffChargeProduct,
    BillingOneOffCharge,
    BillingPaymentMethod,
    BillingCycle,
    BillingSubscription,
)
from webapps.business.models import Business
from webapps.user.models import User


class TestBillingOneOffChargeForm(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.business = baker.make(
            Business,
            has_new_billing=True,
            status=Business.Status.PAID,
            billing_settings__payment_processor=PaymentProcessorType.STRIPE,
        )
        self.one_off_product = baker.make(
            BillingOneOffChargeProduct, suggested_net_price=Decimal('10'), active=True
        )
        self.one_off_charge = baker.make(BillingOneOffCharge, business_id=self.business.id)
        self.payment_method = baker.make(
            BillingPaymentMethod,
            business_id=self.business.pk,
            default=True,
            payment_processor=PaymentProcessorType.STRIPE,
        )
        self.subscription = baker.make(
            BillingSubscription, business=self.business, date_start=tznow() - timedelta(days=1)
        )
        self.billing_cycle = baker.make(
            BillingCycle,
            business_id=self.business.pk,
            subscription=self.subscription,
        )
        self.operator = baker.make(User)

    def test_form_invalid_business_has_not_new_billing(self):
        self.business.has_new_billing = False
        self.business.save()
        form = BillingOneOffTransactionForm(
            data=dict(
                business=self.business.id,
                product=self.one_off_product.id,
                quantity=0,
            ),
        )
        self.assertFalse(form.is_valid())
        self.assertEqual(form.errors['business'][0], 'Only businesses with new billing allowed.')

    def test_form_invalid_business_has_blocked_status(self):
        self.business.status = Business.Status.BLOCKED
        self.business.save()
        form = BillingOneOffTransactionForm(
            data=dict(
                business=self.business,
            ),
        )
        self.assertFalse(form.is_valid())
        self.assertEqual(
            form.errors['business'][0], 'Business has wrong status, only paid allowed.'
        )

    def test_validate_without_subscription(self):
        self.subscription.date_expiry = tznow()
        self.subscription.save()
        form = BillingOneOffTransactionForm(
            data=dict(
                business=self.business,
            ),
        )
        self.assertFalse(form.is_valid())
        self.assertEqual(
            form.errors['business'][0],
            'There is no active subscription assigned to this account.',
        )

    def test_validate_wrong_payment_processor(self):
        self.business.billing_settings.payment_processor = PaymentProcessorType.BRAINTREE
        self.business.billing_settings.save()
        form = BillingOneOffTransactionForm(
            data=dict(
                business=self.business,
            ),
        )
        self.assertFalse(form.is_valid())
        self.assertEqual(
            form.errors['business'][0],
            'Only businesses with Stripe allowed.',
        )

    def test_validate_business_does_not_have_payment_method(self):
        self.payment_method.default = False
        self.payment_method.save()
        self.payment_method.save()
        form = BillingOneOffTransactionForm(
            data=dict(
                business=self.business,
            ),
        )
        self.assertFalse(form.is_valid())
        self.assertEqual(
            form.errors['business'][0], 'Business does not have assigned payment method.'
        )

    def test_form_valid(self):
        form = BillingOneOffTransactionForm(
            data=dict(
                business=self.business,
            ),
        )
        self.assertTrue(form.is_valid())
        self.assertEqual(form.billing_cycle_id, self.billing_cycle.id)

    def test_only_active_products(self):
        self.one_off_product.active = False
        self.one_off_product.save()
        form = BillingOneOffChargeForm(
            data=dict(
                business=self.business,
                product=self.one_off_product,
            ),
        )
        self.assertFalse(form.is_valid())
        self.assertEqual(
            form.errors['product'][0],
            'Select a valid choice. That choice is not one of the available choices.',
        )

    def test_form_invalid_quantity(self):
        form = BillingOneOffChargeForm(
            data=dict(
                business=self.business,
                product=self.one_off_product,
                quantity=0,
            ),
        )
        self.assertFalse(form.is_valid())
        self.assertEqual(
            form.errors['quantity'][0], 'Ensure this value is greater than or equal to 1.'
        )

    def test_form_valid_without_unit_price(self):
        form = BillingOneOffChargeForm(
            data=dict(
                business=self.business,
                product=self.one_off_product,
                quantity=1,
            ),
        )
        form.is_valid()
        one_off_charge = form.save()
        self.assertEqual(form.billing_cycle_id, self.billing_cycle.id)
        self.assertEqual(self.one_off_product.suggested_net_price, one_off_charge.unit_net_price)
        self.assertEqual(self.one_off_product.currency, one_off_charge.currency)

    def test_form_valid_with_unit_price(self):
        form = BillingOneOffChargeForm(
            data=dict(
                business=self.business,
                product=self.one_off_product,
                quantity=2,
                unit_net_price=34,
            ),
        )
        form.is_valid()
        one_off_charge = form.save()
        self.assertEqual(form.billing_cycle_id, self.billing_cycle.id)
        self.assertEqual(Decimal('34'), one_off_charge.unit_net_price)
        self.assertEqual(self.one_off_product.currency, one_off_charge.currency)
