from datetime import datetime
import itertools
from unittest.mock import MagicMock, patch
from typing import Any

import pytest
from django.contrib.auth.models import Group, Permission
from django.test import override_settings
from django.urls.base import reverse
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status

from lib.feature_flag.feature.billing import BillingAllowModifyCardNetworkFlag
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import relativedelta, tznow
from webapps.admin_extra.tests import DjangoTestCase
from webapps.billing.admin import BillingTransactionAdmin
from webapps.billing.admin.payment import BillingPaymentMethodForm
from webapps.billing.enums import (
    CreditNetworkType,
    CreditCardType,
    PaymentMethodType,
    PaymentProcessorType,
    TransactionStatus,
    TransactionSource,
)
from webapps.billing.models import (
    BillingBusinessSettings,
    BillingCountryConfig,
    BillingCreditCardInfo,
    BillingCycle,
    BillingPaymentMethod,
    BillingTransaction,
)
from webapps.billing.tests.utils import billing_business
from webapps.stripe_app.interfaces.payment_method import StripePayment<PERSON>ethodResult
from webapps.stripe_app.models.payment_method import PaymentMethod
from webapps.stripe_app.tests.utils import stripe_api_payment_method
from webapps.user.groups import GroupName


class TestBillingTransactionAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.user = self.login_admin()

    def test_changelist_works(self):
        url = reverse('admin:billing_billingtransaction_changelist')
        baker.make(BillingTransaction, status=TransactionStatus.CHARGED)
        resp = self.client.get(url)
        self.assertContains(resp, 'Charged')

    def test_changeview_works(self):
        instance = baker.make(BillingTransaction, status=TransactionStatus.PENDING)
        url = reverse('admin:billing_billingtransaction_change', args=(instance.pk,))
        resp = self.client.get(url)
        form = resp.context['adminform'].form
        self.assertEqual(form.instance, instance)
        self.assertFalse(form.fields)  # readonly

    def test_search_view_works(self):
        instance = baker.make(BillingTransaction)
        instance2 = baker.make(BillingTransaction)
        self.url = reverse('admin:billing_billingtransaction_changelist')
        resp = self.client.get(self.url + f'?q={instance.business.name}')
        self.assertContains(resp, instance.business.name)
        self.assertNotContains(resp, instance2.business.name)

    def test_changeview_all_fields_readonly(self):
        self.assertCountEqual(
            set(
                itertools.chain.from_iterable(
                    [row[1]['fields'] for row in BillingTransactionAdmin.fieldsets]
                )
            ),
            set(BillingTransactionAdmin.readonly_fields),
        )

    @parameterized.expand(
        [
            (
                PaymentProcessorType.BRAINTREE,
                (
                    'https://developer.paypal.com/braintree/articles/control-panel/transactions'
                    '/declines#code-'
                ),
                '2013',
            ),
            (
                PaymentProcessorType.STRIPE,
                'https://stripe.com/docs/error-codes#',
                'insufficient-founds',
            ),
        ]
    )
    def test_error_link_works(self, payment_processor, error_url, response_message):
        url = reverse('admin:billing_billingtransaction_changelist')
        if payment_processor == PaymentProcessorType.BRAINTREE:
            baker.make(
                BillingTransaction,
                status=TransactionStatus.FAILED,
                payment_processor=payment_processor.value,
                response_code=response_message,
                response_text=None,
            )
        if payment_processor == PaymentProcessorType.STRIPE:
            baker.make(
                BillingTransaction,
                status=TransactionStatus.FAILED,
                payment_processor=payment_processor.value,
                response_code=None,
                response_text='error',
                response_errors={
                    'last_payment_error': {'doc_url': f'{error_url}{response_message}'}
                },
            )

        resp = self.client.get(url)
        self.assertContains(resp, f'{error_url}{response_message}')
        self.assertContains(resp, response_message)

    def test_error_link_stripe_without_response_error(self):
        url = reverse('admin:billing_billingtransaction_changelist')
        baker.make(
            BillingTransaction,
            status=TransactionStatus.FAILED,
            payment_processor=PaymentProcessorType.STRIPE.value,
            response_code=None,
            response_text='insufficient-founds',
        )

        resp = self.client.get(url)
        self.assertContains(resp, 'https://stripe.com/docs/error-codes#')
        self.assertContains(resp, 'insufficient-founds')

    def test_refund_link_visible(self):
        instance = baker.make(
            BillingTransaction,
            status=TransactionStatus.CHARGED,
            transaction_source=TransactionSource.BILLING_SUBSCRIPTION,
        )
        url = reverse('admin:billing_billingtransaction_change', args=(instance.pk,))
        resp = self.client.get(url)
        self.assertContains(resp, 'Make Refund')

    @parameterized.expand(
        (
            ("after_stripe_migration", tznow() - relativedelta.relativedelta(days=7)),
            ("before_stripe_migration", tznow() + relativedelta.relativedelta(days=7)),
            ("migration_never_done", None),
        )
    )
    def test_refund_link_invisible_wrong_transaction_source(
        self, test_name: str, migration_date: datetime | None
    ):
        mock_config = MagicMock(spec=BillingCountryConfig)
        mock_config.stripe_migration_date = migration_date
        instance = baker.make(
            BillingTransaction,
            status=TransactionStatus.CHARGED,
            transaction_source=TransactionSource.BOOST_OVERDUE,
        )
        url = reverse('admin:billing_billingtransaction_change', args=(instance.pk,))
        with patch(
            'webapps.billing.models.BillingCountryConfig.get_from_cache'
        ) as get_from_cache_patch:
            get_from_cache_patch.return_value = mock_config
            resp = self.client.get(url)
        self.assertNotContains(resp, 'Make Refund')

    def test_refund_link_invisible_wrong_status(self):
        instance = baker.make(
            BillingTransaction,
            status=TransactionStatus.PENDING,
            transaction_source=TransactionSource.BILLING_SUBSCRIPTION,
        )
        url = reverse('admin:billing_billingtransaction_change', args=(instance.pk,))
        resp = self.client.get(url)
        self.assertNotContains(resp, 'Make Refund')

    @parameterized.expand(
        (
            ("after_stripe_migration", tznow() - relativedelta.relativedelta(days=7), True),
            ("before_stripe_migration", tznow() + relativedelta.relativedelta(days=7), False),
            ("migration_never_done", None, True),
        )
    )
    def test_refund_link_visibility_right_status(
        self, test_name: str, migration_date: Any, should_be_enabled: bool
    ):
        mock_config = MagicMock(spec=BillingCountryConfig)
        mock_config.stripe_migration_date = migration_date

        instance = baker.make(
            BillingTransaction,
            status=TransactionStatus.CHARGED,
            transaction_source=TransactionSource.BILLING_SUBSCRIPTION,
        )

        url = reverse('admin:billing_billingtransaction_change', args=(instance.pk,))
        with patch(
            'webapps.billing.models.BillingCountryConfig.get_from_cache'
        ) as get_from_cache_patch:
            get_from_cache_patch.return_value = mock_config
            resp = self.client.get(url)
        if should_be_enabled:
            self.assertContains(resp, 'Make Refund')
        else:
            self.assertContains(resp, 'Refund Disabled')
            self.assertContains(
                resp,
                'The transaction creation date is before the Stripe account migration, '
                'therefore, the refund is only possible via the Stripe app.',
            )

    @parameterized.expand(
        [
            (
                PaymentProcessorType.BRAINTREE,
                (
                    'https://developer.paypal.com/braintree/articles/control-panel/transactions'
                    '/declines#code-'
                ),
            ),
            (
                PaymentProcessorType.STRIPE,
                'https://stripe.com/docs/error-codes#',
            ),
        ]
    )
    def test_error_link_works_no_response_code(self, payment_processor, error_url):
        url = reverse('admin:billing_billingtransaction_changelist')
        baker.make(
            BillingTransaction,
            status=TransactionStatus.FAILED,
            payment_processor=payment_processor,
            response_code=None,
            response_text=None,
            response_errors=None,
        )
        resp = self.client.get(url)
        self.assertNotContains(resp, error_url)

    @parameterized.expand(
        [
            (True, True),
            (False, False),
        ]
    )
    def test_billing_cycle_link(self, has_billing_cycle, contains_link):
        instance = baker.make(
            BillingTransaction,
            billing_cycle=baker.make(BillingCycle) if has_billing_cycle else None,
        )

        url = reverse('admin:billing_billingtransaction_change', args=(instance.pk,))
        resp = self.client.get(url)
        if contains_link:
            self.assertContains(resp, 'Billing cycle</a>')
        else:
            self.assertNotContains(resp, 'Billing cycle</a>')


class TestBillingPaymentMethodAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.user = self.login_admin()

    def test_changelist_works(self):
        url = reverse('admin:billing_billingpaymentmethod_changelist')
        baker.make(BillingPaymentMethod, business__name='Business ABC!')
        response = self.client.get(url)
        self.assertContains(response, 'Business ABC!')

    def test_changeview_works(self):
        instance = baker.make(BillingPaymentMethod)
        url = reverse('admin:billing_billingpaymentmethod_change', args=(instance.pk,))
        response = self.client.get(url)
        self.assertEqual(response.context['adminform'].form.instance, instance)

    def test_search_view_works(self):
        instance1 = baker.make(BillingPaymentMethod)
        instance2 = baker.make(BillingPaymentMethod)
        self.url = reverse('admin:billing_billingpaymentmethod_changelist')
        response = self.client.get(self.url + f'?business_id={instance1.business_id}')
        self.assertContains(response, instance1.business.name)
        self.assertNotContains(response, instance2.business.name)

    def test_billing_user_can_change_flag(self):
        self.user.is_superuser = False
        self.user.save()
        self.user.groups.add(Group.objects.get_or_create(name=GroupName.BILLING_USER)[0])
        instance = baker.make(BillingPaymentMethod)
        url = reverse('admin:billing_billingpaymentmethod_change', args=(instance.pk,))
        resp = self.client.get(url)
        self.assertIn('default', resp.context['adminform'].form.fields)

    def test_regular_user_cant_change_flag(self):
        self.user.is_superuser = False
        self.user.save()
        self.user.user_permissions.add(
            Permission.objects.get_by_natural_key(
                'change_billingpaymentmethod', 'billing', 'billingpaymentmethod'
            )
        )

        instance = baker.make(BillingPaymentMethod)
        url = reverse('admin:billing_billingpaymentmethod_change', args=(instance.pk,))
        resp = self.client.get(url)
        self.assertNotIn('default', resp.context['adminform'].form.fields)

    def test_preferred_network_choicefield(self):
        credit_card_info = baker.make(
            BillingCreditCardInfo,
            preferred_network=CreditNetworkType.CARTES_BANCAIRES,
            available_networks=[
                CreditNetworkType.VISA,
                CreditNetworkType.CARTES_BANCAIRES,
                CreditNetworkType.MASTERCARD,
            ],
            card_type=CreditCardType.MASTERCARD,
            cardholder_name='Christian Bale',
        )
        bpm_instance = baker.make(BillingPaymentMethod, credit_card=credit_card_info)
        url = reverse('admin:billing_billingpaymentmethod_change', args=(bpm_instance.pk,))
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        self.assertEqual(
            {
                ('cartes_bancaires', 'cartes_bancaires'),
                ('mastercard', 'mastercard'),
                ('visa', 'visa'),
            },
            set(resp.context['adminform'].form.fields["preferred_network"].choices),
        )
        self.assertEqual(
            credit_card_info.preferred_network.value,
            resp.context['adminform'].form.fields["preferred_network"].initial,
        )


class TestAddStripePaymentMethodView(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.business = billing_business()
        baker.make(
            BillingBusinessSettings,
            business=self.business,
            payment_processor=PaymentProcessorType.STRIPE,
        )
        self.url = reverse('admin:add_payment_method', args=(self.business.id,))
        self.login_admin()
        self.payment_method = baker.make(BillingPaymentMethod)

    @patch('webapps.billing.admin_views.setup_stripe_payment_method_task')
    def test_display_change_view(self, stripe_task_mocked):
        resp = self.client.get(self.url, follow=True)
        self.assertContains(resp, 'const stripe = Stripe')
        self.assertEqual(stripe_task_mocked.run.call_count, 1)

    @patch('webapps.billing.admin_views.setup_stripe_payment_method_task')
    def test_cant_add_payment_method_unlogged(self, stripe_task_mocked):
        self.client.logout()
        resp = self.client.get(
            self.url,
            data={},
            content_type='application/json',
            follow=True,
        )
        self.assertEqual(resp.status_code, 200)
        self.assertNotContains(resp, 'const stripe = Stripe')
        self.assertEqual(stripe_task_mocked.run.call_count, 0)

    @patch('webapps.billing.admin_views.finalize_stripe_payment_method_task')
    def test_add_successful(self, stripe_task_mocked):
        stripe_task_mocked.run.return_value = {
            'user_data': {
                'payment_method_id': self.payment_method.id,
            },
        }
        data = {
            'setup_id': '1',
            'payment_id': '2',
            'default': True,
        }
        resp = self.client.post(
            self.url,
            data=data,
            content_type='application/json',
            follow=True,
        )
        self.assertEqual(stripe_task_mocked.run.call_count, 1)
        self.assertEqual(resp.status_code, 201)
        self.assertEqual(
            resp.json(),
            {'payment_method_id': self.payment_method.id},
        )

    @patch('webapps.billing.admin_views.finalize_stripe_payment_method_task')
    def test_add_no_data(self, stripe_task_mocked):
        stripe_task_mocked.run.return_value = MagicMock()
        data = {}
        resp = self.client.post(
            self.url,
            data=data,
            content_type='application/json',
            follow=True,
        )
        self.assertEqual(stripe_task_mocked.run.call_count, 0)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            resp.json(),
            {
                'payment_id': ['This field is required.'],
                'setup_id': ['This field is required.'],
            },
        )


class TestBillingPaymentMethodForm(DjangoTestCase):
    def setUp(self):
        self.card_data = {
            "brand": "Visa",
            "country": "PL",
            "exp_month": 1,
            "exp_year": 2023,
            "last4": "1234",
            "networks": {
                "available": ["cartes_bancaires", "mastercard", "visa"],
                "preferred": "cartes_bancaires",
            },
        }
        self.credit_card = baker.make(
            BillingCreditCardInfo,
            preferred_network=CreditNetworkType.CARTES_BANCAIRES,
            available_networks=[
                CreditNetworkType.VISA,
                CreditNetworkType.CARTES_BANCAIRES,
                CreditNetworkType.MASTERCARD,
            ],
            card_type=CreditCardType.MASTERCARD,
            cardholder_name='Tonny Soprano',
        )
        self.bpm_instance = baker.make(
            BillingPaymentMethod,
            payment_method_type=PaymentMethodType.CREDIT_CARD,
            credit_card=self.credit_card,
            default=True,
        )
        self.spm_instance = stripe_api_payment_method(
            stripe_id=self.bpm_instance.token,
            card=self.card_data,
            stripe_api='pm_123',
            customer='cu_123',
            type='card',
            metadata=None,
        )
        self.pm_stripe_model = baker.make(
            PaymentMethod,
            card=self.card_data,
            stripe_id=self.bpm_instance.token,
        )

    def test_form_invalid_with_initial_value(self):
        form = BillingPaymentMethodForm(
            instance=self.bpm_instance, data={'preferred_network': "invalid"}
        )
        self.assertFalse(form.is_valid())

    @override_settings(BILLING_ALLOW_CHANGE_CARD_NETWORK=True)
    @override_eppo_feature_flag({BillingAllowModifyCardNetworkFlag.flag_name: True})
    @patch('webapps.stripe_app.interfaces.payment_method.StripePaymentMethodInterface.modify')
    def test_form_save_updates_credit_card(self, modify_task):
        new_preferred_network = CreditNetworkType.MASTERCARD
        modify_task.return_value = StripePaymentMethodResult(
            is_success=True, payment_method=self.spm_instance
        )
        form = BillingPaymentMethodForm(
            instance=self.bpm_instance,
            data={'preferred_network': new_preferred_network.value},
        )
        self.assertTrue(form.is_valid())
        form.save()
        self.assertEqual(
            new_preferred_network.value, self.bpm_instance.credit_card.preferred_network
        )
        self.assertEqual(True, self.bpm_instance.default)
        self.assertEqual(self.card_data, self.pm_stripe_model.card)
        self.assertTrue(modify_task.called)

    @override_settings(BILLING_ALLOW_CHANGE_CARD_NETWORK=False)
    @override_eppo_feature_flag({BillingAllowModifyCardNetworkFlag.flag_name: True})
    def test_form_save_updates_credit_card_dont_allow_change_ff_on(self):
        form = BillingPaymentMethodForm(
            instance=self.bpm_instance,
            data={'preferred_network': CreditNetworkType.MASTERCARD.value},
        )
        self.assertFalse(form.is_valid())
        with pytest.raises(ValueError):
            form.save()

    @override_settings(BILLING_ALLOW_CHANGE_CARD_NETWORK=False)
    @override_eppo_feature_flag({BillingAllowModifyCardNetworkFlag.flag_name: False})
    def test_form_save_updates_credit_card_dont_allow_change_ff_off(self):
        form = BillingPaymentMethodForm(
            instance=self.bpm_instance,
            data={'preferred_network': CreditNetworkType.MASTERCARD.value},
        )
        self.assertFalse(form.is_valid())
        with pytest.raises(ValueError):
            form.save()

    @override_settings(BILLING_ALLOW_CHANGE_CARD_NETWORK=True)
    @override_eppo_feature_flag({BillingAllowModifyCardNetworkFlag.flag_name: True})
    @patch('webapps.stripe_app.interfaces.payment_method.StripePaymentMethodInterface.modify')
    def test_form_save_updates_credit_card_allow_change_ff_on(self, modify_task):
        new_preferred_network = CreditNetworkType.MASTERCARD
        modify_task.return_value = StripePaymentMethodResult(
            is_success=True, payment_method=self.spm_instance
        )
        form = BillingPaymentMethodForm(
            instance=self.bpm_instance,
            data={'preferred_network': new_preferred_network.value},
        )

        self.assertTrue(form.is_valid())
        form.save()
        self.assertEqual(
            new_preferred_network.value, self.bpm_instance.credit_card.preferred_network
        )
        self.assertEqual(True, self.bpm_instance.default)
        self.assertEqual(self.card_data, self.pm_stripe_model.card)
        self.assertTrue(modify_task.called)

    @override_settings(BILLING_ALLOW_CHANGE_CARD_NETWORK=True)
    @override_eppo_feature_flag({BillingAllowModifyCardNetworkFlag.flag_name: False})
    def test_form_save_updates_credit_card_allow_change_ff_off(self):
        form = BillingPaymentMethodForm(
            instance=self.bpm_instance,
            data={'preferred_network': CreditNetworkType.MASTERCARD.value},
        )
        with pytest.raises(ValueError):
            form.save()
        self.assertEqual(True, self.bpm_instance.default)
        self.assertEqual(
            CreditNetworkType.CARTES_BANCAIRES, self.bpm_instance.credit_card.preferred_network
        )

    @override_settings(BILLING_ALLOW_CHANGE_CARD_NETWORK=True)
    @override_eppo_feature_flag({BillingAllowModifyCardNetworkFlag.flag_name: True})
    @patch('webapps.stripe_app.interfaces.payment_method.StripePaymentMethodInterface.modify')
    def test_form_save_updates_credit_idempotent(self, modify_task):
        form = BillingPaymentMethodForm(
            instance=self.bpm_instance,
            data={'preferred_network': CreditNetworkType.VISA},
        )
        modify_task.return_value = StripePaymentMethodResult(
            is_success=True,
            payment_method=stripe_api_payment_method(
                stripe_id=self.bpm_instance.token, card=self.card_data
            ),
        )
        self.assertTrue(form.is_valid())
        form.save()
        self.assertEqual(
            CreditNetworkType.VISA.value,
            self.bpm_instance.credit_card.preferred_network,
        )
        self.assertEqual(True, self.bpm_instance.default)
        self.assertEqual(self.card_data, self.pm_stripe_model.card)
        self.assertTrue(modify_task.called)

    @override_settings(BILLING_ALLOW_CHANGE_CARD_NETWORK=False)
    @override_eppo_feature_flag({BillingAllowModifyCardNetworkFlag.flag_name: True})
    def test_form_save_outside_enabled_countries_ff_on(self):
        form = BillingPaymentMethodForm(
            instance=self.bpm_instance,
            data={'preferred_network': CreditNetworkType.VISA},
        )
        self.assertFalse(form.is_valid())
        with pytest.raises(ValueError):
            form.save()
        self.assertEqual(
            CreditNetworkType.CARTES_BANCAIRES.value,
            self.bpm_instance.credit_card.preferred_network,
        )
        self.assertEqual(True, self.bpm_instance.default)

    @override_settings(BILLING_ALLOW_CHANGE_CARD_NETWORK=True)
    @override_eppo_feature_flag({BillingAllowModifyCardNetworkFlag.flag_name: True})
    @patch('webapps.stripe_app.interfaces.payment_method.StripePaymentMethodInterface.modify')
    def test_form_save_inside_enabled_countries_ff_on(self, modify_task):
        new_preferred_network = CreditNetworkType.VISA
        self.card_data['networks']['preferred'] = new_preferred_network
        modify_task.return_value = StripePaymentMethodResult(
            is_success=True, payment_method=self.spm_instance
        )
        form = BillingPaymentMethodForm(
            instance=self.bpm_instance,
            data={'preferred_network': new_preferred_network},
        )
        self.assertTrue(form.is_valid())
        form.save()
        self.assertEqual(
            new_preferred_network.value, self.bpm_instance.credit_card.preferred_network
        )
        self.assertEqual(True, self.bpm_instance.default)
        self.assertEqual(self.card_data, self.pm_stripe_model.card)
        self.assertTrue(modify_task.called)
