from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from collections import OrderedDict

import pytest
from dateutil.relativedelta import relativedelta
from django.test import TestCase, override_settings
from django.conf import settings
from mock import patch
from model_bakery import baker
from parameterized import parameterized


from country_config import Country
from lib.enums import PaymentMethodType
from lib.tools import UTC, tznow
from service.exceptions import ServiceError
from webapps.admin_extra.tests import DjangoTestCase
from webapps.billing.enums import (
    BillingLongSubscriptionDuration,
    DiscountProductType,
    DiscountReason,
    DiscountType,
    PaymentPeriod,
    PaymentProcessorType,
    ProductType,
    SubscriptionStatus,
    TransactionSource,
    TransactionStatus,
)
from webapps.billing.error_handling.exceptions import BillingException
from webapps.billing.models import (
    BillingBusinessDiscount,
    BillingBusinessOffer,
    BillingCycle,
    BillingCycleProductCharge,
    BillingDiscountCode,
    BillingDiscountCodeUsage,
    BillingPaymentMethod,
    BillingProduct,
    BillingProductOffer,
    BillingProductOfferItem,
    BillingSubscribedProduct,
    BillingSubscription,
    BillingTransaction,
)
from webapps.billing.models.business import BillingBusinessSettings
from webapps.billing.models.offer import BillingLongSubMapper
from webapps.billing.models.offline_migration import BillingOfflineMigration
from webapps.billing.serializers import (
    AdditionalStaffersSerializer,
    CreditCardInfoSerializer,
    InvoiceDetailsSerializer,
    InvoiceListHeaderSerializer,
    LongSubscriptionNextPaymentDetailsSerializer,
    NextPaymentDetailsSerializer,
    PaymentMethodSerializer,
    ProductOfferItemsSerializer,
    SubscribedProductsSerializer,
    TextMessageUsageSerializer,
    BusinessOfferSerializer,
)
from webapps.billing.serializers.churn import BusinessCancellationReasonSerializer
from webapps.billing.serializers.other import BillingBusinessSettingsSerializer
from webapps.billing.serializers.overdue import BillingOverdueSubscriptionSerializer
from webapps.billing.serializers.utils import get_saas_summary_with_business_discounts
from webapps.billing.tests.test_subscription_creator import OfferCreatorMixin
from webapps.billing.views.subscription.overdue import SubscriptionOverdueDetailsView
from webapps.billing.tests.utils import billing_business, create_active_subscription
from webapps.braintree_app.tests.utils import (
    credit_card_details,
    credit_card_verification,
    payment_method_credit_card_success,
)
from webapps.business.enums import AutoCancellationReasonType, BoostPaymentSource, CancellationType
from webapps.business.models import Business, CancellationReason, Resource
from webapps.kill_switch.models import KillSwitch
from webapps.navision.baker_recipes import navision_settings_recipe, zipcode_tax_rate
from webapps.navision.models import TaxRate
from webapps.navision.ports.tax_rates import TaxMatrix
from webapps.notification.models import NotificationSMSStatistics
from webapps.structure.baker_recipes import bake_region_graphs
from webapps.structure.models import Region


class TestPaymentMethodSerializer(DjangoTestCase):
    def setUp(self):
        self.business = billing_business()
        self.payment_method = payment_method_credit_card_success().payment_method
        self.billing_payment_method = baker.make(
            BillingPaymentMethod,
            default=True,
            business=self.business,
        )

    def test_serializer_valid(self):
        serializer = PaymentMethodSerializer.from_braintree_payment_method(
            self.business.id,
            self.payment_method,
        )
        self.assertTrue(serializer.is_valid())
        billing_payment_method_new = serializer.save()
        self.assertEqual(billing_payment_method_new.business, self.business)
        self.assertEqual(
            billing_payment_method_new.payment_method_type,
            PaymentMethodType.CREDIT_CARD.value,
        )
        self.assertEqual(billing_payment_method_new.token, self.payment_method.token)
        self.assertTrue(billing_payment_method_new.default)
        # check if old payment method for business is not default
        self.billing_payment_method.refresh_from_db()
        self.assertFalse(self.billing_payment_method.default)

    def test_serializer_no_data(self):
        serializer = PaymentMethodSerializer(data={})
        self.assertFalse(serializer.is_valid())
        self.assertIn('business_id', serializer.errors)
        self.assertIn('payment_method_type', serializer.errors)
        self.assertIn('token', serializer.errors)


class TestCreditCardInfoSerializer(DjangoTestCase):
    def setUp(self):
        self.cc_verification = credit_card_verification()
        self.cc_braintree = credit_card_details()

    def test_serializer_valid(self):
        serializer = CreditCardInfoSerializer.from_braintree_instance(
            self.cc_verification,
        )
        self.assertTrue(serializer.is_valid())
        cc_info = serializer.save()
        self.assertEqual(
            cc_info.card_type.label,
            self.cc_verification.credit_card['card_type'],
        )
        self.assertEqual(
            cc_info.cardholder_name,
            self.cc_verification.credit_card['cardholder_name'],
        )
        self.assertEqual(
            cc_info.first_6_digits,
            self.cc_verification.credit_card['bin'],
        )
        self.assertEqual(
            cc_info.last_4_digits,
            self.cc_verification.credit_card['last_4'],
        )
        self.assertEqual(
            cc_info.expiration_date.strftime('%y'),
            self.cc_verification.credit_card['expiration_year'],
        )
        self.assertEqual(
            cc_info.expiration_date.strftime('%m'),
            self.cc_verification.credit_card['expiration_month'],
        )
        self.assertEqual(
            cc_info.country_of_issuance,
            self.cc_verification.credit_card['country_of_issuance'],
        )
        self.assertEqual(
            cc_info.address_line_1,
            self.cc_verification.billing['street_address'],
        )
        self.assertEqual(
            cc_info.address_line_2,
            self.cc_verification.billing['extended_address'],
        )
        self.assertEqual(
            cc_info.city,
            self.cc_verification.billing['locality'],
        )
        self.assertEqual(
            cc_info.zipcode,
            self.cc_verification.billing['postal_code'],
        )
        self.assertEqual(
            cc_info.country,
            self.cc_verification.billing['country_name'],
        )

    def test_serializer_instance_without_verification_valid(self):
        serializer = CreditCardInfoSerializer.from_braintree_instance_without_verification(
            self.cc_braintree
        )

        self.assertTrue(serializer.is_valid())
        cc_info = serializer.save()
        self.assertEqual(
            cc_info.card_type.label,
            self.cc_braintree.card_type,
        )
        self.assertEqual(
            cc_info.cardholder_name,
            self.cc_braintree.cardholder_name,
        )
        self.assertEqual(
            cc_info.first_6_digits,
            self.cc_braintree.bin,
        )
        self.assertEqual(
            cc_info.last_4_digits,
            self.cc_braintree.last_4,
        )
        self.assertEqual(
            cc_info.expiration_date.strftime('%y'),
            self.cc_braintree.expiration_year,
        )
        self.assertEqual(
            cc_info.expiration_date.strftime('%m'),
            self.cc_braintree.expiration_month,
        )


@override_settings(
    API_COUNTRY=Country.US,
    NAVISION_TAX_AREA=Region.Type.ZIP,
)
class BaseOfferItemsSetup(TestCase):
    def setUp(self):
        self.state = baker.make(
            Region,
            type=Region.Type.STATE,
            name='Arkansas',
        )
        self.zipcode = baker.make(
            Region,
            type=Region.Type.ZIP,
            name='09123',
        )
        bake_region_graphs(self.state, self.zipcode)
        self.saas_tax_rate = zipcode_tax_rate.make(
            region=self.zipcode,
            tax_rate=Decimal('0.2300'),
            service=TaxRate.Service.SAAS,
        )
        self.business = baker.make(Business, zipcode=self.zipcode.name, region=self.zipcode)
        self.postpaid_sms = baker.make(
            BillingProduct,
            currency='USD',
            product_type=ProductType.POSTPAID_SMS,
            unit_price=Decimal('0.01'),
        )
        self.staffer_saas = baker.make(
            BillingProduct,
            product_type=ProductType.STAFFER_SAAS,
            unit_price=Decimal('10.00'),
            free_staff_qty=1,
            max_qty=9,
            sms_amount=25,
        )
        self.saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
            sms_add_on_id=self.postpaid_sms.id,
            staff_add_on_id=self.staffer_saas.id,
            name='Best plan ever',
            unit_price=Decimal('149.99'),
            currency='USD',
            sms_amount=200,
        )
        self.offer = baker.make(BillingProductOffer)
        # Saas is always present in offer
        self.saas_item = baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.saas.id,
            active=True,
        )
        # Same for postpaid sms
        self.sms_item = baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.postpaid_sms.id,
            active=True,
        )


class TestProductOfferItemsSerializer(BaseOfferItemsSetup):
    def test_staff_add_on__no_discount(self):
        baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.staffer_saas.id,
            active=True,
        )
        result = ProductOfferItemsSerializer(
            offer_id=self.offer.id,
            context=dict(
                staff_count=3,
                business_id=self.business.id,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        self.assertIn(ProductType.SAAS.value, result)
        # Staff add-on should be nested in main product
        self.assertNotIn(ProductType.STAFFER_SAAS.value, result)
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('product_id'), self.saas.id)
        self.assertEqual(saas_entry.get('product_type'), self.saas.product_type)
        self.assertEqual(saas_entry.get('name'), 'Best plan ever')
        self.assertEqual(saas_entry.get('full_price'), '149.99')
        self.assertEqual(saas_entry.get('full_price_gross'), '184.49')
        self.assertEqual(saas_entry.get('currency'), self.saas.currency)
        self.assertEqual(saas_entry.get('discount_type'), DiscountType.NO_DISCOUNT.value)
        self.assertIsNone(saas_entry.get('discount_amount'))
        self.assertIsNone(saas_entry.get('discount_percentage'))
        self.assertIsNone(saas_entry.get('discount_duration'))
        self.assertFalse(saas_entry.get('has_infinite_discount'))
        self.assertEqual(saas_entry.get('staff_add_on_id'), self.staffer_saas.id)
        self.assertEqual(saas_entry.get('staffers_included'), 1)
        self.assertEqual(saas_entry.get('staff_charge_limit'), 9)
        self.assertEqual(saas_entry.get('staff_full_price'), '10.00')
        self.assertEqual(saas_entry.get('staff_full_price_gross'), '12.30')
        self.assertEqual(saas_entry.get('staff_discount_type'), DiscountType.NO_DISCOUNT.value)
        self.assertIsNone(saas_entry.get('staff_discount_amount'))
        self.assertIsNone(saas_entry.get('staff_discount_amount_gross'))
        self.assertIsNone(saas_entry.get('staff_discount_percentage'))
        self.assertEqual(saas_entry.get('sms_price'), '0.010')
        self.assertEqual(saas_entry.get('sms_price_gross'), '0.0123')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)
        self.assertEqual(saas_entry.get('sms_amount'), 200)
        self.assertEqual(saas_entry.get('staff_sms_amount'), 25)
        self.assertEqual(saas_entry.get('discounted_price'), '149.99')
        self.assertEqual(saas_entry.get('discounted_price_gross'), '184.49')
        self.assertEqual(saas_entry.get('staff_discounted_price'), '10.00')
        self.assertEqual(saas_entry.get('staff_discounted_price_gross'), '12.30')
        self.assertEqual(saas_entry.get('total_price'), '169.99')
        # Tax-free SaaS for 149.99 dollars + 2 x paid staffers for (10 dollars + 1.50 tax)
        self.assertEqual(saas_entry.get('total_price_gross'), '209.09')
        self.assertEqual(saas_entry.get('total_final_price'), '169.99')
        self.assertEqual(saas_entry.get('total_final_price_gross'), '209.09')
        self.assertEqual(saas_entry.get('total_sms_amount'), 250)
        self.assertEqual(saas_entry.get('total_discount'), '0.00')

    # pylint: disable=too-many-statements
    def test_staff_add_on__saas_discount(self):
        self.saas_tax_rate = TaxRate.update_or_create(
            region=self.zipcode,
            tax_rate=Decimal('0.4500'),
            service=TaxRate.Service.SAAS,
            valid_from=tznow(),
        )

        self.saas_item.discount_type = DiscountType.FIXED
        self.saas_item.discount_amount = Decimal('50.00')
        self.saas_item.discount_duration = 3
        self.saas_item.save()
        baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.staffer_saas.id,
            active=True,
        )
        result = ProductOfferItemsSerializer(
            offer_id=self.offer.id,
            context=dict(
                staff_count=3,
                business_id=self.business.id,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        self.assertIn(ProductType.SAAS.value, result)
        # Staff add-on should be nested in main product
        self.assertNotIn(ProductType.STAFFER_SAAS.value, result)
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('product_id'), self.saas.id)
        self.assertEqual(saas_entry.get('product_type'), self.saas.product_type)
        self.assertEqual(saas_entry.get('name'), 'Best plan ever')
        self.assertEqual(saas_entry.get('full_price'), '149.99')
        self.assertEqual(saas_entry.get('currency'), self.saas.currency)
        self.assertEqual(saas_entry.get('discount_type'), DiscountType.FIXED.value)
        self.assertEqual(saas_entry.get('discount_amount'), '50.00')
        self.assertIsNone(saas_entry.get('discount_percentage'))
        self.assertEqual(saas_entry.get('discount_duration'), 3)
        self.assertFalse(saas_entry.get('has_infinite_discount'))
        self.assertEqual(saas_entry.get('staff_add_on_id'), self.staffer_saas.id)
        self.assertEqual(saas_entry.get('staffers_included'), 1)
        self.assertEqual(saas_entry.get('staff_charge_limit'), 9)
        self.assertEqual(saas_entry.get('staff_full_price'), '10.00')
        self.assertEqual(saas_entry.get('staff_full_price_gross'), '14.50')
        self.assertEqual(saas_entry.get('staff_discount_type'), DiscountType.NO_DISCOUNT.value)
        self.assertIsNone(saas_entry.get('staff_discount_amount'))
        self.assertIsNone(saas_entry.get('staff_discount_percentage'))
        self.assertEqual(saas_entry.get('sms_price'), '0.010')
        self.assertEqual(saas_entry.get('sms_price_gross'), '0.0145')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)
        self.assertEqual(saas_entry.get('sms_amount'), 200)
        self.assertEqual(saas_entry.get('staff_sms_amount'), 25)
        self.assertEqual(saas_entry.get('discounted_price'), '99.99')
        self.assertEqual(saas_entry.get('discounted_price_gross'), '144.99')
        self.assertEqual(saas_entry.get('staff_discounted_price'), '10.00')
        self.assertEqual(saas_entry.get('staff_discounted_price_gross'), '14.50')
        self.assertEqual(saas_entry.get('total_price'), '169.99')
        self.assertEqual(saas_entry.get('total_price_gross'), '246.49')
        self.assertEqual(saas_entry.get('total_final_price'), '119.99')
        self.assertEqual(saas_entry.get('total_final_price_gross'), '173.99')
        self.assertEqual(saas_entry.get('total_sms_amount'), 250)
        self.assertEqual(saas_entry.get('total_discount'), '50.00')
        self.assertEqual(saas_entry.get('total_discount_gross'), '72.50')

        self.assertEqual(saas_entry['saas_tax_rate_percent'], '45.00')
        self.assertEqual(saas_entry.get('staff_tax_rate_percent'), '45.00')
        self.assertEqual(saas_entry.get('sms_tax_rate_percent'), '45.00')

        self.assertEqual(saas_entry['saas_tax'], '67.50')
        self.assertEqual(saas_entry.get('staff_tax'), '4.50')
        self.assertEqual(saas_entry.get('sms_tax'), '0.0045')

    def test_staff_add_on__staff_discount(self):
        # TODO: Verify with admin panel
        self.saas_item.discount_duration = 3
        self.saas_item.save()
        baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.staffer_saas.id,
            discount_type=DiscountType.PERCENTAGE,
            discount_frac=Decimal('0.500'),
            active=True,
        )
        result = ProductOfferItemsSerializer(
            offer_id=self.offer.id,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        self.assertIn(ProductType.SAAS.value, result)
        # Staff add-on should be nested in main product
        self.assertNotIn(ProductType.STAFFER_SAAS.value, result)
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('product_id'), self.saas.id)
        self.assertEqual(saas_entry.get('product_type'), self.saas.product_type)
        self.assertEqual(saas_entry.get('name'), 'Best plan ever')
        self.assertEqual(saas_entry.get('full_price'), '149.99')
        self.assertEqual(saas_entry.get('currency'), self.saas.currency)
        self.assertEqual(saas_entry.get('discount_type'), DiscountType.NO_DISCOUNT.value)
        self.assertIsNone(saas_entry.get('discount_amount'))
        self.assertIsNone(saas_entry.get('discount_percentage'))
        self.assertEqual(saas_entry.get('discount_duration'), 3)
        self.assertFalse(saas_entry.get('has_infinite_discount'))
        self.assertEqual(saas_entry.get('staff_add_on_id'), self.staffer_saas.id)
        self.assertEqual(saas_entry.get('staffers_included'), 1)
        self.assertEqual(saas_entry.get('staff_charge_limit'), 9)
        self.assertEqual(saas_entry.get('staff_full_price'), '10.00')
        self.assertEqual(saas_entry.get('staff_discount_type'), DiscountType.PERCENTAGE.value)
        self.assertIsNone(saas_entry.get('staff_discount_amount'))
        self.assertEqual(saas_entry.get('staff_discount_percentage'), '50.0')
        self.assertEqual(saas_entry.get('sms_price'), '0.010')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)
        self.assertEqual(saas_entry.get('sms_amount'), 200)
        self.assertEqual(saas_entry.get('staff_sms_amount'), 25)
        self.assertEqual(saas_entry.get('discounted_price'), '149.99')
        self.assertEqual(saas_entry.get('discounted_price_gross'), '184.49')
        self.assertEqual(saas_entry.get('staff_discounted_price'), '5.00')
        self.assertEqual(saas_entry.get('total_price'), '169.99')
        self.assertEqual(saas_entry.get('total_price_gross'), '209.09')
        self.assertEqual(saas_entry.get('total_final_price'), '159.99')
        self.assertEqual(saas_entry.get('total_final_price_gross'), '196.79')
        self.assertEqual(saas_entry.get('total_sms_amount'), 250)
        self.assertEqual(saas_entry.get('total_discount'), '10.00')
        self.assertEqual(saas_entry.get('total_discount_gross'), '12.30')

        self.assertEqual(saas_entry['saas_tax_rate_percent'], '23.00')
        self.assertEqual(saas_entry.get('staff_tax_rate_percent'), '23.00')
        self.assertEqual(saas_entry.get('sms_tax_rate_percent'), '23.00')

        self.assertEqual(saas_entry['saas_tax'], '34.50')
        self.assertEqual(saas_entry.get('staff_tax'), '2.30')
        self.assertEqual(saas_entry.get('sms_tax'), '0.0023')

    def test_staff_add_on__both_discounted(self):
        KillSwitch.objects.get_or_create(
            name=KillSwitch.System.NAVISION_USE_TAX_RATE_TABLE,
            defaults={
                'is_killed': True,
            },
        )

        self.saas_item.discount_type = DiscountType.FIXED
        self.saas_item.discount_amount = Decimal('50.00')
        self.saas_item.discount_duration = 3
        self.saas_item.save()
        baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.staffer_saas.id,
            discount_type=DiscountType.FIXED,
            discount_amount=Decimal('5.00'),
            active=True,
        )
        result = ProductOfferItemsSerializer(
            offer_id=self.offer.id,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        self.assertIn(ProductType.SAAS.value, result)
        # Staff add-on should be nested in main product
        self.assertNotIn(ProductType.STAFFER_SAAS.value, result)
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('product_id'), self.saas.id)
        self.assertEqual(saas_entry.get('product_type'), self.saas.product_type)
        self.assertEqual(saas_entry.get('name'), 'Best plan ever')
        self.assertEqual(saas_entry.get('full_price'), '149.99')
        self.assertEqual(saas_entry.get('currency'), self.saas.currency)
        self.assertEqual(saas_entry.get('discount_type'), DiscountType.FIXED.value)
        self.assertEqual(saas_entry.get('discount_amount'), '50.00')
        self.assertIsNone(saas_entry.get('discount_percentage'))
        self.assertEqual(saas_entry.get('discount_duration'), 3)
        self.assertFalse(saas_entry.get('has_infinite_discount'))
        self.assertEqual(saas_entry.get('staff_add_on_id'), self.staffer_saas.id)
        self.assertEqual(saas_entry.get('staffers_included'), 1)
        self.assertEqual(saas_entry.get('staff_charge_limit'), 9)
        self.assertEqual(saas_entry.get('staff_full_price'), '10.00')
        self.assertEqual(saas_entry.get('staff_discount_type'), DiscountType.FIXED.value)
        self.assertEqual(saas_entry.get('staff_discount_amount'), '5.00')
        self.assertIsNone(saas_entry.get('staff_discount_percentage'))
        self.assertEqual(saas_entry.get('sms_price'), '0.010')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)
        self.assertEqual(saas_entry.get('sms_amount'), 200)
        self.assertEqual(saas_entry.get('staff_sms_amount'), 25)
        self.assertEqual(saas_entry.get('discounted_price'), '99.99')
        self.assertEqual(saas_entry.get('discounted_price_gross'), '99.99')
        self.assertEqual(saas_entry.get('staff_discounted_price'), '5.00')
        self.assertEqual(saas_entry.get('staff_discounted_price_gross'), '5.00')
        self.assertEqual(saas_entry.get('total_price'), '169.99')
        self.assertEqual(saas_entry.get('total_final_price'), '109.99')
        self.assertEqual(saas_entry.get('total_sms_amount'), 250)
        self.assertEqual(saas_entry.get('total_discount'), '60.00')
        self.assertIsNone(saas_entry['saas_tax_rate_percent'])
        self.assertIsNone(saas_entry['staff_tax_rate_percent'])
        self.assertIsNone(saas_entry['sms_tax_rate_percent'])

    def test_no_staff_add_on(self):
        self.saas.staff_add_on_id = None
        self.saas.save()
        self.saas_item.discount_type = DiscountType.FIXED
        self.saas_item.discount_amount = Decimal('50.00')
        self.saas_item.save()
        result = ProductOfferItemsSerializer(
            offer_id=self.offer.id,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        self.assertIn(ProductType.SAAS.value, result)
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('product_id'), self.saas.id)
        self.assertEqual(saas_entry.get('product_type'), self.saas.product_type)
        self.assertEqual(saas_entry.get('name'), 'Best plan ever')
        self.assertEqual(saas_entry.get('full_price'), '149.99')
        self.assertEqual(saas_entry.get('currency'), self.saas.currency)
        self.assertEqual(saas_entry.get('discount_type'), DiscountType.FIXED.value)
        self.assertEqual(saas_entry.get('discount_amount'), '50.00')
        self.assertIsNone(saas_entry.get('discount_percentage'))
        self.assertIsNone(saas_entry.get('discount_duration'))
        self.assertTrue(saas_entry.get('has_infinite_discount'))
        self.assertIsNone(saas_entry['staff_add_on_id'])
        self.assertIsNone(saas_entry['staffers_included'])
        self.assertIsNone(saas_entry['staff_charge_limit'])
        self.assertIsNone(saas_entry['staff_full_price'])
        self.assertIsNone(saas_entry['staff_discount_type'])
        self.assertIsNone(saas_entry['staff_discount_amount'])
        self.assertIsNone(saas_entry['staff_discount_percentage'])

        self.assertEqual(saas_entry.get('sms_price'), '0.010')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)
        self.assertEqual(saas_entry.get('sms_amount'), 200)
        self.assertIsNone(saas_entry['staff_sms_amount'])
        self.assertEqual(saas_entry.get('discounted_price'), '99.99')
        self.assertIsNone(saas_entry['staff_discounted_price'])
        self.assertEqual(saas_entry.get('total_price'), '149.99')
        self.assertEqual(saas_entry.get('total_final_price'), '99.99')
        self.assertEqual(saas_entry.get('total_sms_amount'), 200)
        self.assertEqual(saas_entry.get('total_discount'), '50.00')

    def test_postpaid_sms_rounding(self):
        self.saas.staff_add_on_id = None
        self.saas.save()
        self.postpaid_sms.unit_price = Decimal('0.005')
        self.postpaid_sms.save()
        result = ProductOfferItemsSerializer(
            offer_id=self.offer.id,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        self.assertIn(ProductType.SAAS.value, result)
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('sms_price'), '0.005')
        self.assertEqual(saas_entry.get('sms_price_gross'), '0.0062')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)

    def test_inactive_items(self):
        self.saas.staff_add_on_id = None
        self.saas.save()
        saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
            sms_add_on_id=self.postpaid_sms.id,
            staff_add_on_id=None,
            unit_price=Decimal('100.00'),
            currency='USD',
            sms_amount=200,
        )
        baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=saas.id,
            active=False,
        )

        result = ProductOfferItemsSerializer(
            offer_id=self.offer.id,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)


class TestSubscribedProductsSerializer(TestCase):
    def setUp(self):
        self.business = baker.make(Business)
        self.postpaid_sms = baker.make(
            BillingProduct,
            product_type=ProductType.POSTPAID_SMS,
        )
        self.staffer_saas = baker.make(
            BillingProduct,
            product_type=ProductType.STAFFER_SAAS,
        )
        self.saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
        )
        self.subscription = baker.make(
            BillingSubscription,
            business_id=self.business.id,
            date_start=datetime(2021, 1, 1, tzinfo=UTC),
        )
        self.subscribed_sms = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            product_id=self.postpaid_sms.id,
            product_type=ProductType.POSTPAID_SMS,
            date_start=self.subscription.date_start,
            unit_price=Decimal('0.01'),
            quantity=0,
        )
        self.subscribed_staffer = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            product_id=self.staffer_saas.id,
            product_type=ProductType.STAFFER_SAAS,
            date_start=self.subscription.date_start,
            # Price will be later calculated for 2
            quantity=1,
            unit_price=Decimal('10.00'),
            discounted_price=Decimal('10.00'),
            free_staff_qty=1,
            max_qty=9,
            sms_amount=25,
        )
        # Saas is always present in subscription
        self.subscribed_saas = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            product_id=self.saas.id,
            product_type=ProductType.SAAS,
            quantity=1,
            sms_add_on_id=self.postpaid_sms.id,
            staff_add_on_id=self.staffer_saas.id,
            date_start=self.subscription.date_start,
            name='Best plan ever',
            unit_price=Decimal('149.99'),
            total_price=Decimal('149.99'),
            final_price=Decimal('149.99'),
            discounted_price=Decimal('149.99'),
            discount_granted=Decimal(0),
            currency='USD',
            sms_amount=200,
        )
        self.subscribed_from = self.subscription.date_start + self.subscription.payment_period
        self.subscribed_to = self.subscribed_from + self.subscription.payment_period

    def test_staff_add_on__no_discount(self):
        result = SubscribedProductsSerializer(
            instance=self.subscription,
            subscribed_from=self.subscribed_from,
            subscribed_to=self.subscribed_to,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(business_id=self.business.id),
            ),
        ).data
        self.assertIn(ProductType.SAAS.value, result)
        # Staff add-on should be nested in main product
        self.assertNotIn(ProductType.STAFFER_SAAS.value, result)
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('product_id'), self.saas.id)
        self.assertEqual(saas_entry.get('product_type'), self.saas.product_type)
        self.assertEqual(saas_entry.get('name'), 'Best plan ever')
        self.assertEqual(saas_entry.get('full_price'), '149.99')
        self.assertEqual(saas_entry.get('currency'), 'USD')
        self.assertEqual(saas_entry.get('discount_type'), DiscountType.NO_DISCOUNT.value)
        self.assertIsNone(saas_entry.get('discount_amount'))
        self.assertIsNone(saas_entry.get('discount_percentage'))
        self.assertIsNone(saas_entry.get('discount_duration'))
        self.assertFalse(saas_entry.get('has_infinite_discount'))
        self.assertEqual(saas_entry.get('staff_add_on_id'), self.staffer_saas.id)
        self.assertEqual(saas_entry.get('staffers_included'), 1)
        self.assertEqual(saas_entry.get('staff_charge_limit'), 9)
        self.assertEqual(saas_entry.get('staff_full_price'), '10.00')
        self.assertEqual(saas_entry.get('staff_discount_type'), DiscountType.NO_DISCOUNT.value)
        self.assertIsNone(saas_entry.get('staff_discount_amount'))
        self.assertIsNone(saas_entry.get('staff_discount_percentage'))
        self.assertEqual(saas_entry.get('sms_price'), '0.010')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)
        self.assertEqual(saas_entry.get('sms_amount'), 200)
        self.assertEqual(saas_entry.get('staff_sms_amount'), 25)
        self.assertEqual(saas_entry.get('discounted_price'), '149.99')
        self.assertEqual(saas_entry.get('staff_discounted_price'), '10.00')
        self.assertEqual(saas_entry.get('total_price'), '169.99')
        self.assertEqual(saas_entry.get('total_final_price'), '169.99')
        self.assertEqual(saas_entry.get('total_sms_amount'), 250)
        self.assertEqual(saas_entry.get('total_discount'), '0.00')

    def test_staff_add_on__saas_discount(self):
        self.subscribed_saas.discount_type = DiscountType.FIXED
        self.subscribed_saas.discount_amount = Decimal('50.00')
        self.subscribed_saas.discount_duration = 3
        self.subscribed_saas.final_price = Decimal('99.99')
        self.subscribed_saas.discounted_price = Decimal('99.99')
        self.subscribed_saas.save()
        result = SubscribedProductsSerializer(
            instance=self.subscription,
            subscribed_from=self.subscribed_from,
            subscribed_to=self.subscribed_to,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(business_id=self.business.id),
            ),
        ).data
        self.assertIn(ProductType.SAAS.value, result)
        # Staff add-on should be nested in main product
        self.assertNotIn(ProductType.STAFFER_SAAS.value, result)
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('product_id'), self.saas.id)
        self.assertEqual(saas_entry.get('product_type'), self.saas.product_type)
        self.assertEqual(saas_entry.get('name'), 'Best plan ever')
        self.assertEqual(saas_entry.get('full_price'), '149.99')
        self.assertEqual(saas_entry.get('currency'), 'USD')
        self.assertEqual(saas_entry.get('discount_type'), DiscountType.FIXED.value)
        self.assertEqual(saas_entry.get('discount_amount'), '50.00')
        self.assertIsNone(saas_entry.get('discount_percentage'))
        self.assertEqual(saas_entry.get('discount_duration'), 3)
        self.assertFalse(saas_entry.get('has_infinite_discount'))
        self.assertEqual(saas_entry.get('staff_add_on_id'), self.staffer_saas.id)
        self.assertEqual(saas_entry.get('staffers_included'), 1)
        self.assertEqual(saas_entry.get('staff_charge_limit'), 9)
        self.assertEqual(saas_entry.get('staff_full_price'), '10.00')
        self.assertEqual(saas_entry.get('staff_discount_type'), DiscountType.NO_DISCOUNT.value)
        self.assertIsNone(saas_entry.get('staff_discount_amount'))
        self.assertIsNone(saas_entry.get('staff_discount_percentage'))
        self.assertEqual(saas_entry.get('sms_price'), '0.010')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)
        self.assertEqual(saas_entry.get('sms_amount'), 200)
        self.assertEqual(saas_entry.get('staff_sms_amount'), 25)
        self.assertEqual(saas_entry.get('discounted_price'), '99.99')
        self.assertEqual(saas_entry.get('staff_discounted_price'), '10.00')
        self.assertEqual(saas_entry.get('total_price'), '169.99')
        self.assertEqual(saas_entry.get('total_final_price'), '119.99')
        self.assertEqual(saas_entry.get('total_sms_amount'), 250)
        self.assertEqual(saas_entry.get('total_discount'), '50.00')

    def test_staff_add_on__staff_discount(self):
        # TODO: Verify with admin panel
        self.subscribed_saas.discount_duration = 3
        self.subscribed_saas.save()
        self.subscribed_staffer.discount_type = DiscountType.PERCENTAGE
        self.subscribed_staffer.discount_frac = Decimal('0.500')
        self.subscribed_staffer.discounted_price = Decimal('5.00')
        self.subscribed_staffer.save()
        result = SubscribedProductsSerializer(
            instance=self.subscription,
            subscribed_from=self.subscribed_from,
            subscribed_to=self.subscribed_to,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        self.assertIn(ProductType.SAAS.value, result)
        # Staff add-on should be nested in main product
        self.assertNotIn(ProductType.STAFFER_SAAS.value, result)
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('product_id'), self.saas.id)
        self.assertEqual(saas_entry.get('product_type'), self.saas.product_type)
        self.assertEqual(saas_entry.get('name'), 'Best plan ever')
        self.assertEqual(saas_entry.get('full_price'), '149.99')
        self.assertEqual(saas_entry.get('currency'), 'USD')
        self.assertEqual(saas_entry.get('discount_type'), DiscountType.NO_DISCOUNT.value)
        self.assertIsNone(saas_entry.get('discount_amount'))
        self.assertIsNone(saas_entry.get('discount_percentage'))
        self.assertEqual(saas_entry.get('discount_duration'), 3)
        self.assertFalse(saas_entry.get('has_infinite_discount'))
        self.assertEqual(saas_entry.get('staff_add_on_id'), self.staffer_saas.id)
        self.assertEqual(saas_entry.get('staffers_included'), 1)
        self.assertEqual(saas_entry.get('staff_charge_limit'), 9)
        self.assertEqual(saas_entry.get('staff_full_price'), '10.00')
        self.assertEqual(saas_entry.get('staff_discount_type'), DiscountType.PERCENTAGE.value)
        self.assertIsNone(saas_entry.get('staff_discount_amount'))
        self.assertEqual(saas_entry.get('staff_discount_percentage'), '50.0')
        self.assertEqual(saas_entry.get('sms_price'), '0.010')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)
        self.assertEqual(saas_entry.get('sms_amount'), 200)
        self.assertEqual(saas_entry.get('staff_sms_amount'), 25)
        self.assertEqual(saas_entry.get('discounted_price'), '149.99')
        self.assertEqual(saas_entry.get('staff_discounted_price'), '5.00')
        self.assertEqual(saas_entry.get('total_price'), '169.99')
        self.assertEqual(saas_entry.get('total_final_price'), '159.99')
        self.assertEqual(saas_entry.get('total_sms_amount'), 250)
        self.assertEqual(saas_entry.get('total_discount'), '10.00')

    def test_staff_add_on__both_discounted(self):
        self.subscribed_saas.discount_type = DiscountType.FIXED
        self.subscribed_saas.discount_amount = Decimal('50.00')
        self.subscribed_saas.discount_duration = 3
        self.subscribed_saas.final_price = Decimal('99.99')
        self.subscribed_saas.discounted_price = Decimal('99.99')
        self.subscribed_saas.save()
        self.subscribed_staffer.discount_type = DiscountType.FIXED
        self.subscribed_staffer.discount_amount = Decimal('5.00')
        self.subscribed_staffer.discounted_price = Decimal('5.00')
        self.subscribed_staffer.save()
        result = SubscribedProductsSerializer(
            instance=self.subscription,
            subscribed_from=self.subscribed_from,
            subscribed_to=self.subscribed_to,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(business_id=self.business.id),
            ),
        ).data
        self.assertIn(ProductType.SAAS.value, result)
        # Staff add-on should be nested in main product
        self.assertNotIn(ProductType.STAFFER_SAAS.value, result)
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('product_id'), self.saas.id)
        self.assertEqual(saas_entry.get('product_type'), self.saas.product_type)
        self.assertEqual(saas_entry.get('name'), 'Best plan ever')
        self.assertEqual(saas_entry.get('full_price'), '149.99')
        self.assertEqual(saas_entry.get('currency'), 'USD')
        self.assertEqual(saas_entry.get('discount_type'), DiscountType.FIXED.value)
        self.assertEqual(saas_entry.get('discount_amount'), '50.00')
        self.assertIsNone(saas_entry.get('discount_percentage'))
        self.assertEqual(saas_entry.get('discount_duration'), 3)
        self.assertFalse(saas_entry.get('has_infinite_discount'))
        self.assertEqual(saas_entry.get('staff_add_on_id'), self.staffer_saas.id)
        self.assertEqual(saas_entry.get('staffers_included'), 1)
        self.assertEqual(saas_entry.get('staff_charge_limit'), 9)
        self.assertEqual(saas_entry.get('staff_full_price'), '10.00')
        self.assertEqual(saas_entry.get('staff_discount_type'), DiscountType.FIXED.value)
        self.assertEqual(saas_entry.get('staff_discount_amount'), '5.00')
        self.assertIsNone(saas_entry.get('staff_discount_percentage'))
        self.assertEqual(saas_entry.get('sms_price'), '0.010')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)
        self.assertEqual(saas_entry.get('sms_amount'), 200)
        self.assertEqual(saas_entry.get('staff_sms_amount'), 25)
        self.assertEqual(saas_entry.get('discounted_price'), '99.99')
        self.assertEqual(saas_entry.get('staff_discounted_price'), '5.00')
        self.assertEqual(saas_entry.get('total_price'), '169.99')
        self.assertEqual(saas_entry.get('total_final_price'), '109.99')
        self.assertEqual(saas_entry.get('total_sms_amount'), 250)
        self.assertEqual(saas_entry.get('total_discount'), '60.00')

    def test_no_staff_add_on(self):
        self.subscribed_saas.discount_type = DiscountType.FIXED
        self.subscribed_saas.discount_amount = Decimal('50.00')
        self.subscribed_saas.final_price = Decimal('99.99')
        self.subscribed_saas.discounted_price = Decimal('99.99')
        self.subscribed_saas.staff_add_on_id = None
        self.subscribed_saas.save()
        self.subscribed_staffer.delete()
        result = SubscribedProductsSerializer(
            instance=self.subscription,
            subscribed_from=self.subscribed_from,
            subscribed_to=self.subscribed_to,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(business_id=self.business.id),
            ),
        ).data
        self.assertIn(ProductType.SAAS.value, result)
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('product_id'), self.saas.id)
        self.assertEqual(saas_entry.get('product_type'), self.saas.product_type)
        self.assertEqual(saas_entry.get('name'), 'Best plan ever')
        self.assertEqual(saas_entry.get('full_price'), '149.99')
        self.assertEqual(saas_entry.get('currency'), 'USD')
        self.assertEqual(saas_entry.get('discount_type'), DiscountType.FIXED.value)
        self.assertEqual(saas_entry.get('discount_amount'), '50.00')
        self.assertIsNone(saas_entry.get('discount_percentage'))
        self.assertIsNone(saas_entry.get('discount_duration'))
        self.assertTrue(saas_entry.get('has_infinite_discount'))
        self.assertIsNone(saas_entry['staff_add_on_id'])
        self.assertIsNone(saas_entry['staffers_included'])
        self.assertIsNone(saas_entry['staff_charge_limit'])
        self.assertIsNone(saas_entry['staff_full_price'])
        self.assertIsNone(saas_entry['staff_discount_type'])
        self.assertIsNone(saas_entry['staff_discount_amount'])
        self.assertIsNone(saas_entry['staff_discount_percentage'])
        self.assertEqual(saas_entry.get('sms_price'), '0.010')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)
        self.assertEqual(saas_entry.get('sms_amount'), 200)
        self.assertIsNone(saas_entry['staff_sms_amount'])
        self.assertEqual(saas_entry.get('discounted_price'), '99.99')
        self.assertIsNone(saas_entry['staff_discounted_price'])
        self.assertEqual(saas_entry.get('total_price'), '149.99')
        self.assertEqual(saas_entry.get('total_final_price'), '99.99')
        self.assertEqual(saas_entry.get('total_sms_amount'), 200)
        self.assertEqual(saas_entry.get('total_discount'), '50.00')

    def test_sms_price_rounding(self):
        self.subscribed_sms.unit_price = Decimal('0.005')
        self.subscribed_sms.discounted_price = Decimal('0.005')
        self.subscribed_sms.save()
        result = SubscribedProductsSerializer(
            instance=self.subscription,
            subscribed_from=self.subscribed_from,
            subscribed_to=self.subscribed_to,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(business_id=self.business.id),
            ),
        ).data
        self.assertIn(ProductType.SAAS.value, result)
        saas_data = result[ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('sms_price'), '0.005')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)


class ProductMixin:
    def _prepare_offer(self, **kwargs):
        offer = baker.make(
            BillingProductOffer,
            **kwargs,
        )
        baker.make(
            BillingProductOfferItem,
            offer_id=offer.id,
            product_id=self.staffer_saas.id,
            active=True,
        )
        baker.make(
            BillingProductOfferItem,
            offer_id=offer.id,
            product_id=self.postpaid_sms.id,
            active=True,
        )
        baker.make(
            BillingProductOfferItem,
            offer_id=offer.id,
            product_id=self.saas.id,
            active=True,
        )
        return offer

    def setUp(self):  # pylint: disable=invalid-name
        self.business = baker.make(Business)
        self.offer = baker.make(BillingProductOffer)
        self.postpaid_sms = baker.make(
            BillingProduct,
            product_type=ProductType.POSTPAID_SMS,
        )
        self.staffer_saas = baker.make(
            BillingProduct,
            product_type=ProductType.STAFFER_SAAS,
        )
        self.saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
        )
        self.subscription = baker.make(
            BillingSubscription,
            business_id=self.business.id,
            date_start=datetime(2021, 1, 20, tzinfo=UTC),
            next_billing_date=datetime(2021, 3, 20, tzinfo=UTC),
            paid_through_date=datetime(2021, 3, 20, tzinfo=UTC),
            offer_id=self.offer.id,
            payment_period=PaymentPeriod.ONE_MONTH.value[0],
            currency='USD',
            status=SubscriptionStatus.ACTIVE,
        )
        self.billing_cycle = baker.make(
            BillingCycle,
            subscription_id=self.subscription.id,
            business_id=self.business.id,
            date_start=datetime(2021, 2, 20, tzinfo=UTC),
            date_end=datetime(2021, 3, 20, tzinfo=UTC),
        )
        self.subscribed_sms = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            product_id=self.postpaid_sms.id,
            product_type=ProductType.POSTPAID_SMS,
            date_start=self.subscription.date_start,
            unit_price=Decimal('0.01'),
            currency='USD',
            quantity=0,
        )
        self.subscribed_staffer = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            product_id=self.staffer_saas.id,
            product_type=ProductType.STAFFER_SAAS,
            date_start=self.subscription.date_start,
            # Price will be later calculated for 2
            quantity=1,
            unit_price=Decimal('10.00'),
            discount_type=DiscountType.FIXED,
            discount_amount=Decimal('5.00'),
            discounted_price=Decimal('5.00'),
            discount_granted=Decimal('5.00'),
            total_price=Decimal('10.00'),
            final_price=Decimal('5.00'),
            free_staff_qty=1,
            max_qty=9,
            sms_amount=25,
        )
        # Saas is always present in subscription
        self.subscribed_saas = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            product_id=self.saas.id,
            product_type=ProductType.SAAS,
            quantity=1,
            sms_add_on_id=self.postpaid_sms.id,
            staff_add_on_id=self.staffer_saas.id,
            date_start=self.subscription.date_start,
            name='Best plan ever',
            discount_type=DiscountType.FIXED,
            discount_amount=Decimal('50.00'),
            unit_price=Decimal('149.00'),
            total_price=Decimal('149.00'),
            final_price=Decimal('99.00'),
            discounted_price=Decimal('99.00'),
            discount_granted=Decimal('50.00'),
            currency='USD',
            sms_amount=200,
        )


class ChargeProductMixin(ProductMixin):
    def setUp(self):
        super().setUp()
        self.transaction = baker.make(
            BillingTransaction,
            billing_cycle=self.billing_cycle,
            status=TransactionStatus.FAILED,
            subscription=self.subscription,
            transaction_source=TransactionSource.BILLING_SUBSCRIPTION,
        )

        self.charge_saas = baker.make(
            BillingCycleProductCharge,
            billing_cycle=self.billing_cycle,
            product=self.subscribed_saas,
            usage_from=self.billing_cycle.date_start,
            usage_to=self.billing_cycle.date_end,
            quantity=1,
            gross_final_price=Decimal('90.00'),
        )
        baker.make(
            BillingCycleProductCharge,
            billing_cycle=self.billing_cycle,
            product=self.subscribed_staffer,
            usage_from=self.billing_cycle.date_start,
            usage_to=self.billing_cycle.date_end,
            gross_final_price=Decimal('40.0'),
            quantity=2,
        )

        baker.make(
            BillingCycleProductCharge,
            billing_cycle=self.billing_cycle,
            product=self.subscribed_sms,
            usage_from=self.billing_cycle.date_start,
            usage_to=self.billing_cycle.date_end,
            gross_final_price=Decimal('35.0'),
            quantity=50,
        )


@override_settings(
    API_COUNTRY=Country.US,
    NAVISION_TAX_AREA=Region.Type.ZIP,
)
@pytest.mark.freeze_time(datetime(2021, 3, 15, 12, tzinfo=UTC))
@patch.object(NotificationSMSStatistics, 'get_parts_count_in_period')
class TestNextPaymentDetailsSerializer(OfferCreatorMixin, TestCase):
    def setUp(self):
        super().setUp()
        self.business.region = self.zipcode
        self.business.save(update_fields=['region'])
        self.subscription = baker.make(
            BillingSubscription,
            business_id=self.business.id,
            date_start=datetime(2021, 1, 20, tzinfo=UTC),
            next_billing_date=datetime(2021, 3, 20, tzinfo=UTC),
            paid_through_date=datetime(2021, 3, 20, tzinfo=UTC),
            offer_id=self.offer.id,
            payment_period=PaymentPeriod.ONE_MONTH.value[0],
            currency='USD',
        )
        self.billing_cycle = baker.make(
            BillingCycle,
            subscription_id=self.subscription.id,
            business_id=self.business.id,
            date_start=datetime(2021, 2, 20, tzinfo=UTC),
            date_end=datetime(2021, 3, 20, tzinfo=UTC),
        )
        self.subscribed_sms = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            product_id=self.postpaid_sms.id,
            product_type=ProductType.POSTPAID_SMS,
            date_start=self.subscription.date_start,
            unit_price=Decimal('0.01'),
            currency='USD',
            quantity=0,
        )
        self.subscribed_staffer = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            product_id=self.staffer_saas.id,
            product_type=ProductType.STAFFER_SAAS,
            date_start=self.subscription.date_start,
            # Price will be later calculated for 2
            quantity=1,
            unit_price=Decimal('10.00'),
            discount_type=DiscountType.FIXED,
            discount_amount=Decimal('5.00'),
            discounted_price=Decimal('5.00'),
            discount_granted=Decimal('5.00'),
            total_price=Decimal('10.00'),
            final_price=Decimal('5.00'),
            free_staff_qty=1,
            max_qty=9,
            sms_amount=25,
        )
        self.subscribed_saas = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            product_id=self.saas.id,
            product_type=ProductType.SAAS,
            quantity=1,
            sms_add_on_id=self.postpaid_sms.id,
            staff_add_on_id=self.staffer_saas.id,
            date_start=self.subscription.date_start,
            name='Best plan ever',
            discount_type=DiscountType.FIXED,
            discount_amount=Decimal('50.00'),
            unit_price=Decimal('149.00'),
            total_price=Decimal('149.00'),
            final_price=Decimal('99.00'),
            discounted_price=Decimal('99.00'),
            discount_granted=Decimal('50.00'),
            currency='USD',
            sms_amount=200,
        )

    def test_no_staff_add_ons(self, sms_stats_mock):
        self.subscribed_saas.staff_add_on_id = None
        self.subscribed_saas.save()
        self.subscribed_staffer.delete()
        sms_stats_mock.return_value = 5
        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data

        # Subscription details
        self.assertEqual(result.get('subscription_id'), self.subscription.id)
        self.assertEqual(result.get('currency'), 'USD')
        self.assertEqual(
            result.get('next_cycle_start'),
            datetime(2021, 3, 20, tzinfo=UTC),
        )
        self.assertEqual(result.get('next_cycle_end'), datetime(2021, 4, 20, tzinfo=UTC))
        # Total cost: saas + staff add on + postpaid sms usage
        self.assertEqual(result.get('total_cost'), '99.05')
        # Saas details
        self.assertIn('saas', result)
        saas_details = result['saas']
        self.assertEqual(saas_details.get('amount'), '149.00')
        self.assertEqual(saas_details.get('currency'), 'USD')
        # Discount details
        self.assertIn('discount', result)
        discount_details = result['discount']
        self.assertEqual(discount_details.get('amount'), '-50.00')
        self.assertEqual(discount_details.get('currency'), 'USD')
        # Text message usage
        self.assertIn('text_message_usage', result)
        sms_details = result['text_message_usage']
        self.assertEqual(sms_details.get('amount'), '0.05')
        self.assertEqual(sms_details.get('currency'), 'USD')
        self.assertEqual(sms_details.get('unit_price'), '0.010')
        # Mock calls
        self.assertEqual(sms_stats_mock.call_count, 1)

    def test_no_discount(self, sms_stats_mock):
        sms_stats_mock.return_value = 5
        self.subscribed_saas.discount_type = DiscountType.NO_DISCOUNT
        self.subscribed_saas.discounted_price = Decimal('149.00')
        self.subscribed_saas.final_price = Decimal('149.00')
        self.subscribed_saas.save()
        self.subscribed_staffer.discount_type = DiscountType.NO_DISCOUNT
        self.subscribed_staffer.discounted_price = Decimal('10.00')
        self.subscribed_staffer.save()

        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data

        # Subscription details
        self.assertEqual(result.get('subscription_id'), self.subscription.id)
        self.assertEqual(result.get('currency'), 'USD')
        self.assertEqual(
            result.get('next_cycle_start'),
            datetime(2021, 3, 20, tzinfo=UTC),
        )
        self.assertEqual(result.get('next_cycle_end'), datetime(2021, 4, 20, tzinfo=UTC))
        # Total cost: saas + staff add on + postpaid sms usage
        self.assertEqual(result.get('total_cost'), '169.05')
        # Saas details
        self.assertIn('saas', result)
        saas_details = result['saas']
        self.assertEqual(saas_details.get('amount'), '169.00')
        self.assertEqual(saas_details.get('currency'), 'USD')
        # Discount details
        self.assertIn('discount', result)
        discount_details = result['discount']
        self.assertEqual(discount_details.get('amount'), '0.00')
        self.assertEqual(discount_details.get('currency'), 'USD')
        # Text message usage
        self.assertIn('text_message_usage', result)
        sms_details = result['text_message_usage']
        self.assertEqual(sms_details.get('amount'), '0.05')
        self.assertEqual(sms_details.get('currency'), 'USD')
        self.assertEqual(sms_details.get('unit_price'), '0.010')
        # Mock calls
        self.assertEqual(sms_stats_mock.call_count, 1)

    # pylint: disable=unused-variable
    def test_no_saas_subscribed(self, sms_stats_mock):
        self.subscribed_saas.delete()
        with self.assertRaises(BillingException):
            result = NextPaymentDetailsSerializer(
                instance=self.subscription,
                context=dict(
                    staff_count=3,
                    tax_matrix=TaxMatrix.for_business_id(self.business.id),
                ),
            ).data
        self.assertEqual(sms_stats_mock.call_count, 0)

    # pylint: enable=unused-variable
    # pylint: disable=too-many-statements
    def test_no_sms_add_on(self, sms_stats_mock):
        self.subscribed_saas.sms_add_on_id = None
        self.subscribed_saas.save()
        self.subscribed_sms.delete()
        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data

        self.assertEqual(len(result['subscribed_products']), 2)

        if result['subscribed_products'][0]['product_type'] == ProductType.SAAS:
            saas_product = result['subscribed_products'][0]
            staffer_product = result['subscribed_products'][1]
        else:
            saas_product = result['subscribed_products'][1]
            staffer_product = result['subscribed_products'][0]

        self.assertEqual(saas_product['product_type'], ProductType.SAAS)

        self.assertEqual(saas_product['unit_price_net'], '149.00')
        self.assertEqual(saas_product['unit_price_gross'], '151.24')

        self.assertEqual(saas_product['unit_price_after_discount_net'], '99.00')
        self.assertEqual(saas_product['unit_price_after_discount_gross'], '100.49')

        self.assertEqual(saas_product['quantity'], 1)

        self.assertEqual(saas_product['price_net'], '149.00')
        self.assertEqual(saas_product['price_gross'], '151.24')

        self.assertEqual(saas_product['price_after_discount_net'], '99.00')
        self.assertEqual(saas_product['price_after_discount_gross'], '100.49')

        self.assertEqual(saas_product['discount_net'], '50.00')
        self.assertEqual(saas_product['discount_gross'], '50.75')

        self.assertEqual(saas_product['tax_percent'], '1.50')

        self.assertEqual(staffer_product['product_type'], ProductType.STAFFER_SAAS)

        self.assertEqual(staffer_product['unit_price_net'], '10.00')
        self.assertEqual(staffer_product['unit_price_gross'], '10.15')

        self.assertEqual(staffer_product['unit_price_after_discount_net'], '5.00')
        self.assertEqual(staffer_product['unit_price_after_discount_gross'], '5.08')

        self.assertEqual(staffer_product['quantity'], 1)

        self.assertEqual(staffer_product['price_net'], '10.00')
        self.assertEqual(staffer_product['price_gross'], '10.15')

        self.assertEqual(staffer_product['price_after_discount_net'], '5.00')
        self.assertEqual(staffer_product['price_after_discount_gross'], '5.08')

        self.assertEqual(staffer_product['discount_net'], '5.00')
        self.assertEqual(staffer_product['discount_gross'], '5.07')

        self.assertEqual(staffer_product['tax_percent'], '1.50')

        # Subscription details
        self.assertEqual(result.get('subscription_id'), self.subscription.id)
        self.assertEqual(result.get('currency'), 'USD')
        self.assertEqual(
            result.get('next_cycle_start'),
            datetime(2021, 3, 20, tzinfo=UTC),
        )
        self.assertEqual(result.get('next_cycle_end'), datetime(2021, 4, 20, tzinfo=UTC))
        # Total cost: saas + staff add on + postpaid sms usage
        self.assertEqual(result.get('total_cost'), '109.00')
        # Saas details
        self.assertIn('saas', result)
        saas_details = result['saas']
        self.assertEqual(saas_details.get('amount'), '169.00')
        self.assertEqual(saas_details.get('amount_gross'), '171.54')
        self.assertEqual(saas_details.get('currency'), 'USD')
        # Discount details
        self.assertIn('discount', result)
        discount_details = result['discount']
        self.assertEqual(discount_details.get('amount'), '-60.00')
        self.assertEqual(discount_details.get('amount_gross'), '-60.89')
        self.assertEqual(discount_details.get('currency'), 'USD')
        # Text message usage
        self.assertFalse(result['text_message_usage'])
        # Mock calls
        self.assertEqual(sms_stats_mock.call_count, 0)

    def test_with_discount(self, sms_stats_mock):
        sms_stats_mock.return_value = 5

        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data

        # Subscription details
        self.assertEqual(result.get('subscription_id'), self.subscription.id)
        self.assertEqual(result.get('currency'), 'USD')
        self.assertEqual(
            result.get('next_cycle_start'),
            datetime(2021, 3, 20, tzinfo=UTC),
        )
        self.assertEqual(result.get('next_cycle_end'), datetime(2021, 4, 20, tzinfo=UTC))
        # Total cost: saas + staff add on + postpaid sms usage
        self.assertEqual(result.get('total_cost'), '109.05')
        # Saas details
        self.assertIn('saas', result)
        saas_details = result['saas']
        self.assertEqual(saas_details.get('amount'), '169.00')
        self.assertEqual(saas_details.get('currency'), 'USD')
        # Discount details
        self.assertIn('discount', result)
        discount_details = result['discount']
        self.assertEqual(discount_details.get('amount'), '-60.00')
        self.assertEqual(discount_details.get('currency'), 'USD')
        # Text message usage
        self.assertIn('text_message_usage', result)
        sms_details = result['text_message_usage']
        self.assertEqual(sms_details.get('amount'), '0.05')
        self.assertEqual(sms_details.get('currency'), 'USD')
        self.assertEqual(sms_details.get('unit_price'), '0.010')
        # Mock calls
        self.assertEqual(sms_stats_mock.call_count, 1)

    def test_with_business_discount(self, sms_stats_mock):
        zipcode_tax_rate.make(
            region=self.zipcode,
            tax_rate=Decimal('0.2300'),
            service=TaxRate.Service.SAAS,
        )

        sms_stats_mock.return_value = 5

        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.FIXED,
            amount=Decimal(3),
            used=None,
        )
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.STAFFER_SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.FIXED,
            amount=Decimal(3),
            used=None,
        )

        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data

        # Total cost:
        #   saas + staff add on + postpaid sms usage - DISCOUNTS[$3 sass + 2 staffers * $3]
        self.assertEqual(result.get('total_cost'), '100.05')
        self.assertEqual(result.get('tax_percent'), '23.00')
        # Saas details
        saas_details = result['saas']
        self.assertEqual(saas_details.get('amount'), '169.00')
        self.assertEqual(saas_details.get('amount_gross'), '207.87')
        self.assertEqual(saas_details.get('currency'), 'USD')
        # Discount details
        discount_details = result['discount']
        self.assertEqual(discount_details.get('amount'), '-69.00')
        self.assertEqual(discount_details.get('amount_gross'), '-84.87')
        self.assertEqual(discount_details.get('currency'), 'USD')
        # Text message usage
        self.assertIn('text_message_usage', result)
        sms_details = result['text_message_usage']
        self.assertEqual(sms_details.get('amount'), '0.05')
        self.assertEqual(sms_details.get('amount_gross'), '0.06')
        self.assertEqual(sms_details.get('tax'), '0.01')
        self.assertEqual(sms_details.get('currency'), 'USD')
        self.assertEqual(sms_details.get('unit_price'), '0.010')
        self.assertEqual(sms_details.get('unit_price_gross'), '0.0123')
        self.assertEqual(sms_details.get('tax_per_unit'), '0.0023')
        self.assertEqual(sms_details.get('tax_percent'), '23.00')
        # Mock calls
        self.assertEqual(sms_stats_mock.call_count, 1)

    def test_with_business_discount__percentage_max(self, sms_stats_mock):
        sms_stats_mock.return_value = 5
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.PERCENTAGE,
            frac=Decimal(0.8),
            used=None,
        )
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.PERCENTAGE,
            frac=Decimal(0.8),
            used=None,
        )

        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data

        self.assertEqual(result['subscription_offer']['discount_percentage'], '100.0')

    def test_with_discount_code__for_subscription(self, sms_stats_mock):
        sms_stats_mock.return_value = 0

        # 3 current discounts, 2 distinct discount codes
        discount_code1 = baker.make(
            BillingDiscountCode,
            discount_code='CURRENT.CODE.1',
            discount_duration=5,
        )
        # first current DC, 3 more Billing Cycles to end
        baker.make(  # current (doubled)
            BillingBusinessDiscount,
            business=self.business,
            subscription=self.subscription,
            product_type=DiscountProductType.SAAS,
            discount_type=DiscountType.FIXED,
            amount=Decimal(3),
            duration=5,
            reason=DiscountReason.DISCOUNT_CODE,
            discount_code=discount_code1,
            date_start=datetime(2021, 1, 20, tzinfo=UTC),
            date_end=datetime(2021, 6, 20, tzinfo=UTC),
            used=datetime(2021, 2, 20, tzinfo=UTC),
        )
        baker.make(  # current (doubled)
            BillingBusinessDiscount,
            business=self.business,
            subscription=self.subscription,
            product_type=DiscountProductType.SAAS,
            discount_type=DiscountType.FIXED,
            amount=Decimal(3),
            duration=5,
            reason=DiscountReason.DISCOUNT_CODE,
            discount_code=discount_code1,
            date_start=datetime(2021, 1, 20, tzinfo=UTC),
            date_end=datetime(2021, 6, 20, tzinfo=UTC),
            used=datetime(2021, 2, 20, tzinfo=UTC),
        )
        baker.make(  # current (sort by `-updated`)
            BillingBusinessDiscount,
            business=self.business,
            subscription=self.subscription,
            product_type=DiscountProductType.STAFFER_SAAS,
            discount_type=DiscountType.FIXED,
            amount=Decimal(2),
            duration=5,
            reason=DiscountReason.DISCOUNT_CODE,
            discount_code=discount_code1,
            date_start=datetime(2021, 1, 20, tzinfo=UTC),
            date_end=datetime(2021, 6, 20, tzinfo=UTC),
            used=datetime(2021, 2, 20, tzinfo=UTC),
        )
        baker.make(  # current, ending in this Billing Cycle
            BillingBusinessDiscount,
            business=self.business,
            subscription=self.subscription,
            duration=3,
            reason=DiscountReason.DISCOUNT_CODE,
            discount_code=baker.make(BillingDiscountCode, discount_code='CURRENT.CODE.2'),
            product_type=DiscountProductType.STAFFER_SAAS,
            date_start=datetime(2020, 12, 20, tzinfo=UTC),
            date_end=self.billing_cycle.date_end,
            discount_type=DiscountType.FIXED,
            amount=Decimal(3),
            used=datetime(2021, 2, 20, tzinfo=UTC),
        )
        # 1 future discount starting in next billing cycle
        baker.make(
            BillingBusinessDiscount,
            business=self.business,
            subscription=self.subscription,
            duration=7,
            product_type=DiscountProductType.SAAS,
            discount_type=DiscountType.FIXED,
            amount=Decimal(5),
            reason=DiscountReason.DISCOUNT_CODE,
            discount_code=baker.make(BillingDiscountCode, discount_code='FUTURE.CODE.1'),
            used=None,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 10, 20, tzinfo=UTC),
        )
        # 1 past discount
        baker.make(
            BillingBusinessDiscount,
            business=self.business,
            subscription=self.subscription,
            duration=3,
            product_type=DiscountProductType.SAAS,
            discount_type=DiscountType.FIXED,
            amount=Decimal(5),
            reason=DiscountReason.DISCOUNT_CODE,
            discount_code=baker.make(BillingDiscountCode, discount_code='PAST.CODE.1'),
            used=datetime(2021, 1, 1, tzinfo=UTC),
            date_end=datetime(2021, 2, 20, tzinfo=UTC),
        )

        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        self.assertIn('discount_codes', result)
        discount_codes = result['discount_codes']

        discount_codes_ongoing = discount_codes['current']
        discount_codes_future = discount_codes['future']
        self.assertCountEqual(
            [
                dict(
                    code='CURRENT.CODE.1',
                    discount_duration=4,
                ),
                dict(
                    code='CURRENT.CODE.2',
                    discount_duration=1,
                ),
            ],
            discount_codes_ongoing,
        )
        self.assertCountEqual(
            [
                dict(
                    code='CURRENT.CODE.1',
                    discount_duration=3,
                ),
                dict(
                    code='FUTURE.CODE.1',
                    discount_duration=7,
                ),
            ],
            discount_codes_future,
        )
        self.assertIsNone(discount_codes.get('special'))
        # Mock calls
        self.assertEqual(sms_stats_mock.call_count, 1)

    def test_with_discount_code__special_discount_info(self, sms_stats_mock):
        sms_stats_mock.return_value = 0

        discount_code1 = baker.make(
            BillingDiscountCode,
            discount_code='SPECIAL.OFFER',
            discount_duration=4,
        )

        baker.make(
            BillingDiscountCodeUsage,
            business=self.business,
            offer=self.offer,
            discount_code=discount_code1,
            offer_discount_type='F',
            offer_discount_duration=5,
        )
        # mismatched
        baker.make(BillingDiscountCodeUsage, business=self.business, offer=None)
        baker.make(
            BillingDiscountCodeUsage,
            business=self.business,
            offer=baker.make(BillingProductOffer),
        )

        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        self.assertIn('discount_codes', result)
        discount_codes = result['discount_codes']

        self.assertIn('current', discount_codes)
        self.assertIn('future', discount_codes)
        self.assertIsNone(discount_codes.get('special'))
        # Mock calls
        self.assertEqual(sms_stats_mock.call_count, 1)

    @parameterized.expand(
        [
            (datetime(2020, 1, 20, tzinfo=UTC), 0),
            (datetime(2020, 12, 15, 0, tzinfo=UTC), 1),  # discount ends 15.4.2021
            (datetime(2020, 12, 16, tzinfo=UTC), 2),  # discount ends 16.4.2021
            (datetime(2020, 12, 20, tzinfo=UTC), 2),
            (datetime(2020, 12, 30, tzinfo=UTC), 2),
            (datetime(2021, 1, 10, tzinfo=UTC), 2),
            (datetime(2021, 1, 14, tzinfo=UTC), 2),
            (datetime(2021, 1, 15, tzinfo=UTC), 2),
            (datetime(2021, 1, 16, tzinfo=UTC), 3),
            (datetime(2021, 2, 10, tzinfo=UTC), 3),
            (datetime(2021, 3, 10, tzinfo=UTC), 4),
            (datetime(2021, 3, 15, tzinfo=UTC), 4),
        ]
    )
    def test_with_discount_code__for_offer(
        self,
        sms_stats_mock,
        subscription_date_start,
        current_discount_duration,
    ):
        sms_stats_mock.return_value = 0

        self.subscription.date_start = subscription_date_start
        self.subscription.save()

        discount_code1 = baker.make(
            BillingDiscountCode,
            discount_code='OFFER.CODE.1',
        )

        baker.make(
            BillingDiscountCodeUsage,
            business=self.business,
            offer=self.offer,
            offer_discount_type='F',
            offer_discount_duration=4,
            discount_code=discount_code1,
        )

        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        self.assertIn('discount_codes', result)
        discount_codes = result['discount_codes']

        discount_codes_current = discount_codes['current']
        discount_codes_future = discount_codes['future']

        self.assertIsNone(discount_codes.get('special'))

        if current_discount_duration:
            self.assertCountEqual(
                [
                    dict(
                        code='OFFER.CODE.1',
                        discount_duration=current_discount_duration,
                    ),
                ],
                discount_codes_current,
            )
        else:
            self.assertFalse(discount_codes_current)

        if current_discount_duration > 1:
            self.assertCountEqual(
                [
                    dict(
                        code='OFFER.CODE.1',
                        discount_duration=current_discount_duration - 1,
                    ),
                ],
                discount_codes_future,
            )
        else:
            self.assertFalse(discount_codes_future)

        # Mock calls
        self.assertEqual(sms_stats_mock.call_count, 1)

    def test_with_discount_code__for_offer__infinite_discount(
        self,
        sms_stats_mock,
    ):
        sms_stats_mock.return_value = 0

        discount_code1 = baker.make(
            BillingDiscountCode,
            discount_code='OFFER.CODE.1',
        )

        baker.make(
            BillingDiscountCodeUsage,
            business=self.business,
            offer=self.offer,
            offer_discount_type='F',
            offer_discount_duration=None,
            discount_code=discount_code1,
        )
        # mismatched
        baker.make(BillingDiscountCodeUsage, business=self.business, offer=None)
        baker.make(
            BillingDiscountCodeUsage,
            business=self.business,
            offer=baker.make(BillingProductOffer),
        )

        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        self.assertIn('discount_codes', result)
        discount_codes = result['discount_codes']

        self.assertEqual(
            discount_codes['special'],
            dict(
                code='OFFER.CODE.1',
                discount_type='F',
                discount_duration=None,
            ),
        )

        self.assertFalse(discount_codes['current'])
        self.assertFalse(discount_codes['future'])

        # Mock calls
        self.assertEqual(sms_stats_mock.call_count, 1)

    def test_with_discount_code__for_offer__no_discount(
        self,
        sms_stats_mock,
    ):
        sms_stats_mock.return_value = 0

        discount_code1 = baker.make(
            BillingDiscountCode,
            discount_code='OFFER.CODE.1',
        )

        baker.make(
            BillingDiscountCodeUsage,
            business=self.business,
            offer=self.offer,
            offer_discount_type='X',
            offer_discount_duration=None,
            discount_code=discount_code1,
        )

        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data
        self.assertIn('discount_codes', result)
        discount_codes = result['discount_codes']

        self.assertEqual(
            discount_codes['special'],
            dict(
                code='OFFER.CODE.1',
                discount_type='X',
                discount_duration=None,
            ),
        )

        self.assertFalse(discount_codes['current'])
        self.assertFalse(discount_codes['future'])

        # Mock calls
        self.assertEqual(sms_stats_mock.call_count, 1)

    def test_message_usage_serializer(self, sms_stats_mock):
        sms_stats_mock.return_value = 777
        self.billing_cycle.sms_allowance = 50
        self.billing_cycle.save()
        result = TextMessageUsageSerializer(
            instance=self.billing_cycle,
            context={
                'tax_matrix': TaxMatrix.for_business_id(self.business.id),
            },
        ).data
        self.assertEqual(result.get('period_start'), '2021-02-20T00:00:00Z')
        self.assertEqual(result.get('period_end'), '2021-03-20T00:00:00Z')
        self.assertEqual(result.get('free_limit'), 50)
        self.assertEqual(result.get('sent_count'), 777)
        self.assertEqual(result.get('paid_count'), 727)  # 77 - 50
        self.assertEqual(result.get('unit_price'), Decimal('0.01'))
        self.assertEqual(result.get('tax_percent'), '1.50')
        self.assertEqual(result.get('tax'), Decimal('0.15'))
        self.assertEqual(result.get('unit_price_gross'), Decimal('0.0102'))
        self.assertEqual(result.get('tax_per_unit'), Decimal('0.0002'))
        self.assertEqual(result.get('amount'), Decimal('7.27'))  # 727 * 0.01
        # 727 * 0.0102 rounded afterwards 7,42
        self.assertEqual(result.get('amount_gross'), Decimal('7.42'))

    @parameterized.expand(
        [
            (1, '0.004', '0.00'),
            (2, '0.004', '0.01'),
            (6, '0.004', '0.02'),
            (1, '0.005', '0.00'),
            (2, '0.005', '0.01'),
            (3, '0.005', '0.02'),  # 15 even --> up
            (5, '0.005', '0.02'),
            (7, '0.005', '0.04'),  # 35 even --> up
            (9, '0.005', '0.04'),
            (11, '0.005', '0.06'),  # 55 even --> up
        ]
    )
    def test_sms_price_rounding(self, sms_stats_mock, quantity, unit_price, amount):
        sms_stats_mock.return_value = quantity

        self.subscribed_sms.unit_price = Decimal(unit_price)
        self.subscribed_sms.save()

        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data

        # Other parameters were tested in previous tests
        # Total cost: saas + staff add on + postpaid sms usage
        # postpaid sms: amount
        self.assertEqual(result.get('total_cost'), str(Decimal('109.00') + Decimal(amount)))
        sms_details = result['text_message_usage']
        self.assertEqual(sms_details.get('amount'), amount)
        self.assertEqual(sms_details.get('currency'), 'USD')
        self.assertEqual(sms_details.get('unit_price'), unit_price)

    def test_additional_staffers_serializer(self, sms_stats_mock):
        result = AdditionalStaffersSerializer(
            instance=self.subscription,
            context={
                'tax_matrix': TaxMatrix.for_business_id(self.business.id),
                'staff_count': 3,
            },
        ).data

        self.assertEqual(result.get('staff_qty'), '2')
        self.assertEqual(result.get('unit_price'), '10.00')
        self.assertEqual(result.get('unit_price_discounted'), '5.00')
        self.assertEqual(result.get('final_price'), '10.00')
        self.assertEqual(result.get('final_price_gross'), '10.15')
        self.assertEqual(result.get('tax_percent'), '1.50')

    def test_cycles_dates(self, sms_stats_mock):
        sms_stats_mock.return_value = 5
        result = NextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data

        self.assertEqual(result.get('subscription_id'), self.subscription.id)
        self.assertEqual(result.get('next_cycle_start'), datetime(2021, 3, 20, tzinfo=UTC))
        self.assertEqual(result.get('next_cycle_end'), datetime(2021, 4, 20, tzinfo=UTC))
        self.assertEqual(result.get('cycle_start'), datetime(2021, 3, 20, tzinfo=UTC))
        self.assertEqual(result.get('cycle_end'), datetime(2021, 4, 20, tzinfo=UTC))

        self.assertIn('text_message_usage', result)
        sms_details = result['text_message_usage']
        self.assertEqual(sms_details.get('period_start'), '2021-02-20T00:00:00Z')
        self.assertEqual(sms_details.get('period_end'), '2021-03-20T00:00:00Z')

        self.assertIn('additional_staffers_usage', result)
        staffers_details = result['additional_staffers_usage']
        self.assertEqual(staffers_details.get('period_start'), datetime(2021, 2, 20, tzinfo=UTC))
        self.assertEqual(staffers_details.get('period_end'), datetime(2021, 3, 20, tzinfo=UTC))


@override_settings(
    API_COUNTRY=Country.US,
    NAVISION_TAX_AREA=Region.Type.ZIP,
)
@pytest.mark.freeze_time(datetime(2021, 3, 15, 12, tzinfo=UTC))
@patch.object(NotificationSMSStatistics, 'get_parts_count_in_period')
class TestLongSubscriptionNextPaymentDetailsSeriazlier(ProductMixin, TestCase):
    def setUp(self):
        super().setUp()
        self.state = baker.make(
            Region,
            type=Region.Type.STATE,
            name='Arkansas',
        )
        self.zipcode = baker.make(
            Region,
            type=Region.Type.ZIP,
            name='09123',
        )
        bake_region_graphs(self.state, self.zipcode)
        self.saas_tax_rate = zipcode_tax_rate.make(
            region=self.zipcode,
            tax_rate=Decimal('0.2300'),
            service=TaxRate.Service.SAAS,
            valid_from=tznow() - timedelta(days=1),
        )
        self.business.region = self.zipcode
        self.business.save(update_fields=['region'])

        self.subscription.subscription_duration = 3
        self.subscription.start_date = datetime(2021, 3, 20, tzinfo=UTC)
        self.subscription.date_end = datetime(2021, 6, 20, tzinfo=UTC)
        self.subscription.save()

        self.saas.sms_amount = 200
        self.saas.unit_price = Decimal('100')
        self.saas.save()

        self.staffer_saas.free_staff_qty = 3
        self.staffer_saas.save()

        self.new_offer = self._prepare_offer(subscription_duration=3)
        self.saas_offer_item = self.new_offer.offer_items.filter(product_id=self.saas.id).first()
        self.saas_offer_item.discount_frac = Decimal('0.15')
        self.saas_offer_item.discount_type = DiscountType.PERCENTAGE
        self.saas_offer_item.save()

        baker.make(
            BillingLongSubMapper, offer=self.new_offer, subscription_duration=3, free_staff_qty=3
        )
        baker.make(
            Resource, business_id=self.business.id, type=Resource.STAFF, active=True, _quantity=3
        )

    def test_with_long_subscription(self, sms_stats_mock):
        sms_stats_mock.return_value = 5
        result = LongSubscriptionNextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
                business_id=self.business.id,
            ),
        ).data

        self.assertEqual(result.get('subscription_id'), self.subscription.id)
        self.assertEqual(result.get('is_long_subscription'), True)
        self.assertEqual(result.get('subscription_period'), 3)
        self.assertEqual(result.get('renewal_date'), datetime(2021, 6, 20, tzinfo=UTC))
        self.assertEqual(result.get('currency'), 'USD')
        self.assertEqual(result.get('next_cycle_start'), datetime(2021, 3, 20, tzinfo=UTC))
        self.assertEqual(result.get('next_cycle_end'), datetime(2021, 4, 20, tzinfo=UTC))
        self.assertEqual(result.get('total_cost'), '109.05')
        self.assertEqual(result.get('cycle_start'), datetime(2021, 6, 20, tzinfo=UTC))
        self.assertEqual(result.get('cycle_end'), datetime(2021, 9, 20, tzinfo=UTC))

        self.assertIn('saas', result)
        self.assertIn('discount', result)
        self.assertIn('text_message_usage', result)
        self.assertIn('additional_staffers_usage', result)

        self.assertEqual(len(result.get('subscribed_products')), 3)

        subscription_offer = result['subscription_offer']
        self.assertEqual(subscription_offer['sms_amount'], 200)
        self.assertEqual(subscription_offer['staffers_included'], 3)
        self.assertEqual(subscription_offer['discount_type'], 'P')
        self.assertIsNone(subscription_offer['discount_amount'])
        self.assertEqual(subscription_offer['discount_percentage'], '15.0')
        self.assertEqual(subscription_offer['discounted_price'], '85.00')
        self.assertEqual(subscription_offer['discounted_price_gross'], '104.55')

    def test_cycles_dates(self, sms_stats_mock):
        sms_stats_mock.return_value = 5
        result = LongSubscriptionNextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
                business_id=self.business.id,
            ),
        ).data

        self.assertEqual(result.get('subscription_id'), self.subscription.id)
        self.assertEqual(result.get('subscription_period'), 3)
        self.assertEqual(result.get('next_cycle_start'), datetime(2021, 3, 20, tzinfo=UTC))
        self.assertEqual(result.get('next_cycle_end'), datetime(2021, 4, 20, tzinfo=UTC))
        self.assertEqual(result.get('cycle_start'), datetime(2021, 6, 20, tzinfo=UTC))
        self.assertEqual(result.get('cycle_end'), datetime(2021, 9, 20, tzinfo=UTC))

        self.assertIn('text_message_usage', result)
        sms_details = result['text_message_usage']
        self.assertEqual(sms_details.get('period_start'), '2021-02-20T00:00:00Z')
        self.assertEqual(sms_details.get('period_end'), '2021-03-20T00:00:00Z')

        self.assertIn('additional_staffers_usage', result)
        staffers_details = result['additional_staffers_usage']
        self.assertEqual(staffers_details.get('period_start'), datetime(2021, 2, 20, tzinfo=UTC))
        self.assertEqual(staffers_details.get('period_end'), datetime(2021, 3, 20, tzinfo=UTC))

    def test_with_discount(self, sms_stats_mock):
        sms_stats_mock.return_value = 5

        result = LongSubscriptionNextPaymentDetailsSerializer(
            instance=self.subscription,
            context=dict(
                staff_count=3,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
                offer=self.new_offer,
            ),
        ).data

        # Subscription details
        self.assertEqual(result.get('subscription_id'), self.subscription.id)
        self.assertEqual(result.get('currency'), 'USD')
        self.assertEqual(result.get('next_cycle_start'), datetime(2021, 3, 20, tzinfo=UTC))
        self.assertEqual(result.get('next_cycle_end'), datetime(2021, 4, 20, tzinfo=UTC))
        # Total cost: saas + staff add on + postpaid sms usage
        self.assertEqual(result.get('total_cost'), '109.05')
        # Saas details
        self.assertIn('saas', result)
        saas_details = result['saas']
        self.assertEqual(saas_details.get('amount'), '169.00')
        self.assertEqual(saas_details.get('currency'), 'USD')
        # Discount details
        self.assertIn('discount', result)
        discount_details = result['discount']
        self.assertEqual(discount_details.get('amount'), '-60.00')
        self.assertEqual(discount_details.get('currency'), 'USD')
        # Text message usage
        self.assertIn('text_message_usage', result)
        sms_details = result['text_message_usage']
        self.assertEqual(sms_details.get('amount'), '0.05')
        self.assertEqual(sms_details.get('currency'), 'USD')
        self.assertEqual(sms_details.get('unit_price'), '0.010')
        # Mock calls
        self.assertEqual(sms_stats_mock.call_count, 1)


@pytest.mark.freeze_time(datetime(2021, 3, 15, 12, tzinfo=UTC))
class TestSaasSummaryWithBusinessDiscounts(TestCase):
    def setUp(self):
        business = baker.make(Business)
        self.postpaid_sms = baker.make(
            BillingProduct,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=Decimal('0.01'),
        )
        self.staffer_saas = baker.make(
            BillingProduct,
            product_type=ProductType.STAFFER_SAAS,
            unit_price=Decimal('10.00'),
        )
        self.saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
            unit_price=Decimal('149.00'),
        )
        self.subscription = baker.make(
            BillingSubscription,
            business_id=business.id,
            date_start=datetime(2021, 1, 20, tzinfo=UTC),
            next_billing_date=datetime(2021, 3, 20, tzinfo=UTC),
            paid_through_date=datetime(2021, 3, 20, tzinfo=UTC),
            currency='USD',
        )
        self.subscribed_sms = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            product_id=self.postpaid_sms.id,
            product_type=ProductType.POSTPAID_SMS,
            date_start=self.subscription.date_start,
            unit_price=Decimal('0.01'),
            currency='USD',
            quantity=0,
        )
        self.subscribed_staffer = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            product_id=self.staffer_saas.id,
            product_type=ProductType.STAFFER_SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            # Price will be later calculated for 2
            quantity=1,
            unit_price=Decimal('10.00'),
            discount_type=DiscountType.FIXED,
            discount_amount=Decimal('5.00'),
            discounted_price=Decimal('5.00'),
            free_staff_qty=1,
            max_qty=9,
            sms_amount=25,
        )
        # Saas is always present in subscription
        self.subscribed_saas = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            product_id=self.saas.id,
            product_type=ProductType.SAAS,
            quantity=1,
            sms_add_on_id=self.postpaid_sms.id,
            staff_add_on_id=self.staffer_saas.id,
            date_start=self.subscription.date_start,
            name='Best plan ever',
            discount_type=DiscountType.FIXED,
            discount_amount=Decimal('50.00'),
            unit_price=Decimal('149.00'),
            total_price=Decimal('149.00'),
            final_price=Decimal('99.00'),
            discounted_price=Decimal('99.00'),
            discount_granted=Decimal('50.00'),
            currency='USD',
            sms_amount=200,
        )

    def test_no_discounts(self):
        result, _ = get_saas_summary_with_business_discounts(
            self.subscribed_saas,
            staff_count=3,
            tax_matrix=TaxMatrix.empty(),
        )

        self.assertEqual(result['price_with_add_ons'], Decimal(169))
        self.assertEqual(result['discount_with_add_ons'], Decimal(60))
        self.assertEqual(result['final_price_with_add_ons'], Decimal(109))

    def test_no_discounts__no_add_ons(self):
        result, _ = get_saas_summary_with_business_discounts(
            self.subscribed_saas,
            staff_count=None,
            tax_matrix=TaxMatrix.empty(),
            with_add_ons=False,
        )

        self.assertEqual(result['total_price'], Decimal(149))
        self.assertEqual(result['discount'], Decimal(50))
        self.assertEqual(result['final_price'], Decimal(99))

    def test_used_discounts(self):
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.FIXED,
            amount=Decimal(3),
            used=tznow(),  # used
        )
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            date_start=datetime(2021, 3, 21, tzinfo=UTC),  # incorrect date start
            date_end=datetime(2021, 5, 21, tzinfo=UTC),
            discount_type=DiscountType.FIXED,
            amount=Decimal(3),
            used=None,
        )
        baker.make(
            BillingBusinessDiscount,
            subscription=baker.make(BillingSubscription),  # different business
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.FIXED,
            amount=Decimal(3),
            used=None,
        )

        result, _ = get_saas_summary_with_business_discounts(
            self.subscribed_saas,
            staff_count=3,
            tax_matrix=TaxMatrix.empty(),
        )

        self.assertEqual(result['price_with_add_ons'], Decimal(169))
        self.assertEqual(result['discount_with_add_ons'], Decimal(60))
        self.assertEqual(result['final_price_with_add_ons'], Decimal(109))

    def test_only_staff_discounts(self):
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.STAFFER_SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.FIXED,
            amount=Decimal(3),
            used=None,
        )
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.STAFFER_SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.PERCENTAGE,
            frac=Decimal('0.10'),  # 10%
            used=None,
        )
        result, _ = get_saas_summary_with_business_discounts(
            self.subscribed_saas, staff_count=3, tax_matrix=TaxMatrix.empty()
        )

        self.assertEqual(result['price_with_add_ons'], Decimal(169))
        self.assertEqual(result['discount_with_add_ons'], Decimal(68))
        self.assertEqual(result['final_price_with_add_ons'], Decimal(101))

    def test_multiple_discounts(self):
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.FIXED,
            amount=Decimal(3),
            used=None,
        )
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.STAFFER_SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.FIXED,
            amount=Decimal(3),
            used=None,
        )
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.PERCENTAGE,
            frac=Decimal('0.10'),  # 10%
            used=None,
        )
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.STAFFER_SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.PERCENTAGE,
            frac=Decimal('0.10'),  # 10%
            used=None,
        )
        updated = self.subscribed_saas.updated

        result, _ = get_saas_summary_with_business_discounts(
            self.subscribed_saas,
            staff_count=3,
            tax_matrix=TaxMatrix.empty(),
        )

        self.assertEqual(result['price_with_add_ons'], Decimal(169))
        self.assertEqual(result['discount_with_add_ons'], Decimal('85.90'))
        self.assertEqual(result['final_price_with_add_ons'], Decimal('83.1'))

        self.subscribed_saas.refresh_from_db()
        self.assertEqual(updated, self.subscribed_saas.updated)

    def test_100_percent_discount(self):
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.FIXED,
            amount=Decimal(300),
            used=None,
        )
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.PERCENTAGE,
            frac=Decimal('0.999'),
            used=None,
        )
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.STAFFER_SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.PERCENTAGE,
            frac=Decimal(1),
            used=None,
        )

        result, _ = get_saas_summary_with_business_discounts(
            self.subscribed_saas,
            staff_count=3,
            tax_matrix=TaxMatrix.empty(),
        )

        self.assertEqual(result['price_with_add_ons'], Decimal(169))
        self.assertEqual(result['discount_with_add_ons'], Decimal(169))
        self.assertEqual(result['final_price_with_add_ons'], Decimal(0))

    def test_100_percent_discount__no_add_ons(self):
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.PERCENTAGE,
            frac=Decimal('1'),
            used=None,
        )

        result, _ = get_saas_summary_with_business_discounts(
            self.subscribed_saas,
            staff_count=3,
            tax_matrix=TaxMatrix.empty(),
            with_add_ons=False,
        )

        self.assertEqual(result['total_price'], Decimal(149))
        self.assertEqual(result['discount'], Decimal(149))
        self.assertEqual(result['final_price'], Decimal(0))

    def test_50_percent_discount_saas_and_staffer(self):
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.STAFFER_AND_SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.PERCENTAGE,
            frac=Decimal('0.5'),
            used=None,
        )

        result, _ = get_saas_summary_with_business_discounts(
            self.subscribed_saas,
            staff_count=3,
            tax_matrix=TaxMatrix.empty(),
        )

        self.assertEqual(result['price_with_add_ons'], Decimal(169))
        self.assertEqual(result['discount_with_add_ons'], Decimal(144.5))
        self.assertEqual(result['final_price_with_add_ons'], Decimal(24.5))

    def test_100_percent_discount_saas_and_staffer_no_ads_on(self):
        baker.make(
            BillingBusinessDiscount,
            subscription=self.subscription,
            product_type=DiscountProductType.STAFFER_AND_SAAS,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 5, 20, tzinfo=UTC),
            discount_type=DiscountType.PERCENTAGE,
            frac=Decimal('1'),
            used=None,
        )

        result, _ = get_saas_summary_with_business_discounts(
            self.subscribed_saas,
            staff_count=3,
            tax_matrix=TaxMatrix.empty(),
            with_add_ons=False,
        )

        self.assertEqual(result['total_price'], Decimal(149))
        self.assertEqual(result['discount'], Decimal(149))
        self.assertEqual(result['final_price'], Decimal(0))


@pytest.mark.freeze_time(datetime(2021, 3, 21, tzinfo=UTC))
class TestInvoiceSerializers(ProductMixin, TestCase):
    def setUp(self) -> None:
        super().setUp()
        self.business = baker.make(Business)

        self.billing_cycle2 = baker.make(
            BillingCycle,
            subscription_id=self.subscription.id,
            business_id=self.business.id,
            date_start=datetime(2021, 3, 20, tzinfo=UTC),
            date_end=datetime(2021, 4, 20, tzinfo=UTC),
            number_of_cycle=2,
        )
        baker.make(
            BillingTransaction,
            billing_cycle=self.billing_cycle2,
            status=TransactionStatus.CHARGED,
            payment_method__payment_method_type='P',
        )

        self.charge_saas = baker.make(
            BillingCycleProductCharge,
            billing_cycle=self.billing_cycle2,
            product=self.subscribed_saas,
            name='SaaS test product',
            usage_from=self.billing_cycle2.date_start,
            usage_to=self.billing_cycle2.date_end,
            quantity=1,
            total_price=Decimal('120.00'),
            final_price=Decimal('90.00'),
            discount_granted=Decimal('30.00'),
            currency='USD',
        )
        baker.make(
            BillingCycleProductCharge,
            billing_cycle=self.billing_cycle2,
            product=self.subscribed_staffer,
            name='SaaS staffer',
            usage_from=self.billing_cycle2.date_start,
            usage_to=self.billing_cycle2.date_end,
            quantity=2,
            total_price=Decimal('40.0'),
            final_price=Decimal('40.0'),
            discount_granted=0,
            currency='USD',
        )

        baker.make(
            BillingCycleProductCharge,
            billing_cycle=self.billing_cycle2,
            product=self.subscribed_sms,
            name='Postpaid sms test',
            usage_from=self.billing_cycle.date_start,
            usage_to=self.billing_cycle.date_end,
            quantity=50,
            total_price=Decimal('2.50'),
            final_price=Decimal('2.50'),
            discount_granted=0,
            currency='USD',
        )

        baker.make(
            BillingCycleProductCharge,
            billing_cycle=self.billing_cycle2,
            product=baker.make(
                BillingSubscribedProduct,
                product_type=ProductType.PREPAID_SMS,
            ),
            name='Additional package sms',
            usage_from=self.billing_cycle2.date_start,
            usage_to=self.billing_cycle2.date_end,
            quantity=1,
            total_price=Decimal('35.0'),
            final_price=Decimal('35.0'),
            discount_granted=0,
            currency='USD',
        )
        self.charge = baker.make(
            BillingTransaction,
            business=self.business,
            billing_cycle=self.billing_cycle2,
            status=TransactionStatus.CHARGED,
        )

    # region InvoiceDetailsSerializer
    def test_display_all(self):
        ret = InvoiceDetailsSerializer(instance=self.billing_cycle2).data
        invoice_items = ret.pop('invoice_items')

        self.assertEqual(ret['date_start'], '2021-03-20T00:00:00Z')
        self.assertEqual(ret['date_end'], datetime(2021, 4, 20, tzinfo=UTC))
        self.assertEqual(ret['invoice_number'], f'{self.business.id}/2021/03')
        self.assertEqual(ret['invoice_id'], self.billing_cycle2.id)
        self.assertEqual(ret['issue_date'], '2021-03-20')
        self.assertEqual(ret['sale_date'], '2021-03-21')
        self.assertEqual(ret['status'], 'SUCCESS')
        self.assertEqual(ret['total_gross_amount'], '167.50')
        self.assertEqual(ret['payment_method'], 'P')
        self.assertEqual(ret['total_discount'], '30.00')
        self.assertIn(
            {
                'name': 'SaaS test product',
                'usage_from': '2021-03-20T00:00:00Z',
                'usage_to': '2021-04-20T00:00:00Z',
                'quantity': 1,
                'total_gross_amount': '90.00',
                'base_gross_amount': '120.00',
                'product_type': 'SA',
            },
            invoice_items['SAAS'],
        )
        self.assertIn(
            {
                'name': 'SaaS staffer',
                'usage_from': '2021-03-20T00:00:00Z',
                'usage_to': '2021-04-20T00:00:00Z',
                'quantity': 2,
                'total_gross_amount': '40.00',
                'base_gross_amount': '40.00',
                'product_type': 'SS',
            },
            invoice_items['STAFFERS'],
        )
        self.assertIn(
            {
                'name': 'Postpaid sms test',
                'usage_from': '2021-02-20T00:00:00Z',
                'usage_to': '2021-03-20T00:00:00Z',
                'quantity': 50,
                'total_gross_amount': '2.50',
                'base_gross_amount': '2.50',
                'product_type': 'PS',
            },
            invoice_items['SMS'],
        )
        self.assertIn(
            {
                'name': 'Additional package sms',
                'usage_from': '2021-03-20T00:00:00Z',
                'usage_to': '2021-04-20T00:00:00Z',
                'quantity': 1,
                'total_gross_amount': '35.00',
                'base_gross_amount': '35.00',
                'product_type': 'RS',
            },
            invoice_items['ADDITIONAL'],
        )

    def test_saas_failed_charge(self):
        self.billing_cycle2.transactions.all().update(status=TransactionStatus.FAILED)
        ret = InvoiceDetailsSerializer(instance=self.billing_cycle2).data
        self.assertEqual(ret['status'], 'FAILED')
        self.assertIsNone(ret['payment_method'])
        self.assertIsNone(ret['sale_date'])

    def test_other_transaction_source(self):
        self.billing_cycle2.transactions.all().update(
            transaction_source=TransactionSource.BOOST_OVERDUE.value
        )
        ret = InvoiceDetailsSerializer(instance=self.billing_cycle2).data
        self.assertEqual(ret['status'], 'FAILED')
        self.assertIsNone(ret['payment_method'])
        self.assertIsNone(ret['sale_date'])

    def test_no_charges(self):
        ret = InvoiceDetailsSerializer(instance=self.billing_cycle).data
        self.assertIsNone(ret['total_gross_amount'])
        self.assertEqual(
            ret['invoice_items'],
            dict(
                SAAS=[],
                SMS=[],
                STAFFERS=[],
                ADDITIONAL=[],
            ),
        )

    def test_invoice_details_number_of_queries(self):
        instance = BillingCycle.objects.select_related(
            'subscription',
        ).get(
            pk=self.billing_cycle2.id,
        )
        with self.assertNumQueries(5):
            # 3 for invoice header
            # 1 to filter charges
            # 1 to get transaction
            _ = InvoiceDetailsSerializer(instance=instance).data

    # endregion

    # region InvoiceListHeaderSerializer
    def test_one_object(self):
        business = baker.make(Business)
        billing_cycle = baker.make(
            BillingCycle,
            subscription_id=self.subscription.id,
            business_id=business.id,
            date_start=datetime(2021, 2, 20, tzinfo=UTC),
            date_end=datetime(2021, 3, 20, tzinfo=UTC),
            number_of_cycle=1,
        )
        ret = InvoiceListHeaderSerializer(instance=business).data
        self.assertEqual(len(ret.get('invoices', [])), 1)
        self.assertEqual(ret['invoices'][0].get('date_start'), '2021-02-20T00:00:00Z')
        self.assertEqual(ret['invoices'][0].get('date_end'), datetime(2021, 3, 20, tzinfo=UTC))
        self.assertEqual(ret['invoices'][0].get('invoice_number'), f'{business.id}/2021/02')
        self.assertEqual(ret['invoices'][0].get('invoice_id'), billing_cycle.id)

    def test_one_object_with_long_subscription(self):
        self.subscription.subscription_duration = 3
        self.subscription.date_start = datetime(2021, 2, 20, tzinfo=UTC)
        self.subscription.date_end = datetime(2021, 5, 20, tzinfo=UTC)
        self.subscription.save()
        business = baker.make(Business)
        billing_cycle = baker.make(
            BillingCycle,
            subscription_id=self.subscription.id,
            business_id=business.id,
            date_start=datetime(2021, 2, 20, tzinfo=UTC),
            date_end=datetime(2021, 3, 20, tzinfo=UTC),
            number_of_cycle=1,
        )
        ret = InvoiceListHeaderSerializer(instance=business).data

        self.assertEqual(len(ret.get('invoices', [])), 1)
        self.assertEqual(ret['invoices'][0].get('date_start'), '2021-02-20T00:00:00Z')
        self.assertEqual(ret['invoices'][0].get('date_end'), datetime(2021, 5, 20, tzinfo=UTC))
        self.assertEqual(ret['invoices'][0].get('invoice_number'), f'{business.id}/2021/02')
        self.assertEqual(ret['invoices'][0].get('invoice_id'), billing_cycle.id)

    def test_multiple_objects(self):
        business = baker.make(Business)
        baker.make(
            BillingCycle,
            subscription_id=self.subscription.id,
            business_id=business.id,
            date_start=datetime(2021, 2, 20, tzinfo=UTC),
            date_end=datetime(2021, 3, 20, tzinfo=UTC),
            number_of_cycle=1,
            _quantity=10,
        )
        ret = InvoiceListHeaderSerializer(business).data
        self.assertEqual(len(ret['invoices']), 10)

    def test_list_invoice_header_number_of_queries(self):
        business = baker.make(Business)
        baker.make(
            BillingCycle,
            subscription_id=self.subscription.id,
            business_id=business.id,
            date_start=datetime(2021, 2, 20, tzinfo=UTC),
            date_end=datetime(2021, 3, 20, tzinfo=UTC),
            number_of_cycle=1,
            _quantity=20,
        )
        with self.assertNumQueries(1):
            ret = InvoiceListHeaderSerializer(business).data
            self.assertEqual(len(ret['invoices']), 20)

    # endregion


@pytest.mark.django_db
class TestBillingBusinessSettingsSerializer(TestCase):
    def setUp(self):
        self.navision_settings = navision_settings_recipe.make()
        self.business = baker.make(
            Business,
            has_new_billing=True,
            status=Business.Status.PAID,
            boost_status=Business.BoostStatus.ENABLED,
            boost_payment_source=BoostPaymentSource.OFFLINE,
        )
        self.billing_business_settings = baker.make(
            BillingBusinessSettings,
            business=self.business,
            sms_cost_alert_level=3,
            payment_processor=PaymentProcessorType.BRAINTREE,
        )
        self.payment_method = baker.make(
            BillingPaymentMethod,
            business=self.business,
            payment_method_type=PaymentMethodType.PAYPAL,
            default=True,
        )
        self.killswitch = baker.make(
            KillSwitch,
            name=KillSwitch.System.BILLING_SHOW_PAYPAL_WARNING,
        )
        self.offline_migration = baker.make(
            BillingOfflineMigration,
            business=self.business,
            migration_campaign_valid_to=tznow() + timedelta(days=1),
        )

    @parameterized.expand([(True, False), (False, True)])
    def test_offline_migration_popup__with_killsiwtch(self, is_killed, expected):
        self.business.payment_source = Business.PaymentSource.OFFLINE
        self.business.save()
        killswitch = baker.make(
            KillSwitch,
            name=KillSwitch.System.BILLING_OFFLINE_MIGRATION,
        )
        killswitch.is_killed = is_killed
        killswitch.save()

        result_data = BillingBusinessSettingsSerializer(
            instance=self.billing_business_settings,
            context={'user': self.business.owner},
        ).data
        assert result_data['offline_migration_popup'] == expected

    def test_boost_offline_migration(self):
        self.business.payment_source = Business.PaymentSource.OFFLINE
        self.business.save()
        result_data = BillingBusinessSettingsSerializer(
            instance=self.billing_business_settings,
            context={'user': self.business.owner},
        ).data
        self.assertTrue(result_data['boost_offline_migration_required'])

    @pytest.mark.freeze_time(datetime(2022, 2, 2, tzinfo=UTC))
    @override_settings(API_COUNTRY=Country.PL)
    def test_migration_lockout_counter(self):
        self.business.payment_source = Business.PaymentSource.OFFLINE
        self.business.overdue_till = datetime(2022, 2, 3, tzinfo=UTC)
        self.business.status = Business.Status.OVERDUE
        self.business.save()
        saas = baker.make(BillingProduct, product_type=ProductType.SAAS)
        offer = baker.make(BillingProductOffer, default=False)
        baker.make(BillingBusinessOffer, business=self.business, offer_id=offer.id)
        baker.make(
            BillingProductOfferItem,
            offer_id=offer.id,
            product_id=saas.id,
            active=True,
            discount_type=DiscountType.PERCENTAGE,
            discount_frac=Decimal('0.5'),
            discount_duration=1,
        )

        result_data = BillingBusinessSettingsSerializer(
            instance=self.billing_business_settings,
            context={'user': self.business.owner},
        ).data

        self.assertEqual(result_data['offline_migration_lockout_counter'], 0)

        self.business.overdue_till = datetime(2022, 2, 1, tzinfo=UTC)
        self.business.save()

        result_data = BillingBusinessSettingsSerializer(
            instance=self.billing_business_settings,
            context={'user': self.business.owner},
        ).data

        # everyting below 0 should be displayed as 0
        self.assertEqual(result_data['offline_migration_lockout_counter'], 0)

        self.business.overdue_till = datetime(2022, 2, 22, tzinfo=UTC)
        self.business.save()

        result_data = BillingBusinessSettingsSerializer(
            instance=self.billing_business_settings,
            context={'user': self.business.owner},
        ).data

        self.assertEqual(result_data['offline_migration_lockout_counter'], None)

    def test_migration_display_per_country(self):
        self.business.payment_source = Business.PaymentSource.OFFLINE

        result_data = BillingBusinessSettingsSerializer(
            instance=self.billing_business_settings,
            context={'user': self.business.owner},
        ).data

        self.assertFalse(result_data['offline_migration_discount_offer_display'])


class TestBusinessCancellationReasonSerializer(TestCase):

    def setUp(self):
        self.business = billing_business(status=Business.Status.PAID)
        self.subscription = create_active_subscription(self.business)
        self.valid_data = {
            'business_cancellation_reason': AutoCancellationReasonType.BUSINESS_CHANGE.value,
            'reason_additional_info': 'Some additional info',
        }

    def test_serializer_valid_data_default_cancellation_date(self):
        serializer = BusinessCancellationReasonSerializer(
            data=self.valid_data, context={'business': self.business}
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()
        self.assertEqual(instance.business, self.business)
        self.assertEqual(
            instance.business_cancellation_reason, self.valid_data['business_cancellation_reason']
        )
        self.assertEqual(instance.reason_additional_info, self.valid_data['reason_additional_info'])
        self.assertEqual(instance.churn_type, CancellationType.MADE_BY_BUSINESS.value)
        self.assertEqual(instance.cancellation_date, self.subscription.current_cycle_end)
        self.assertFalse(instance.churn_done)
        self.assertEqual(self.business.payment_source, instance.payment_source_at_churn)

    def test_serializer_invalid_reason(self):
        self.valid_data['business_cancellation_reason'] = 'TEST'
        serializer = BusinessCancellationReasonSerializer(
            data=self.valid_data,
            context={'business': self.business},
        )
        with self.assertRaisesMessage(ServiceError, "is not a valid choice."):
            serializer.is_valid(raise_exception=True)

    def test_serializer_empty_reason(self):
        self.valid_data.pop('business_cancellation_reason')
        serializer = BusinessCancellationReasonSerializer(
            data=self.valid_data,
            context={'business': self.business},
        )
        with self.assertRaisesMessage(ServiceError, "This field is required."):
            serializer.is_valid(raise_exception=True)

    def test_serializer_without_subscription(self):
        self.subscription.delete()
        serializer = BusinessCancellationReasonSerializer(
            data=self.valid_data,
            context={'business': self.business},
        )
        with self.assertRaisesMessage(ServiceError, 'Subscription not found.'):
            serializer.is_valid(raise_exception=True)

    def test_serializer_with_pending_churn_request(self):
        baker.make(
            CancellationReason,
            business=self.business,
            churn_done=False,
        )
        serializer = BusinessCancellationReasonSerializer(
            data=self.valid_data,
            context={'business': self.business},
        )
        with self.assertRaisesMessage(ServiceError, 'There is a pending Churn Request.'):
            serializer.is_valid(raise_exception=True)

    @override_settings(API_COUNTRY=Country.PL)
    def test_serializer_valid_data_pl(self):
        serializer = BusinessCancellationReasonSerializer(
            data=self.valid_data, context={'business': self.business}
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()
        self.assertEqual(instance.business, self.business)
        self.assertEqual(
            instance.business_cancellation_reason, self.valid_data['business_cancellation_reason']
        )
        self.assertEqual(instance.reason_additional_info, self.valid_data['reason_additional_info'])
        self.assertEqual(instance.churn_type, CancellationType.MADE_BY_BUSINESS.value)
        self.assertEqual(
            instance.cancellation_date,
            self.subscription.current_cycle_end + relativedelta(months=1),
        )
        self.assertFalse(instance.churn_done)

        self.assertEqual(self.business.payment_source, instance.payment_source_at_churn)


class TestSubscriptionOverdueSerializers(ChargeProductMixin, TestCase):
    def setUp(self) -> None:
        super().setUp()

        self.expected_data = {
            'product_charges': [
                OrderedDict(
                    [
                        ('type', 'SA'),
                        ('usage_to', '2021-03-20'),
                        ('usage_from', '2021-02-20'),
                        ('quantity', 1),
                        ('gross_final_price', '90.00'),
                    ]
                ),
                OrderedDict(
                    [
                        ('type', 'SS'),
                        ('usage_to', '2021-03-20'),
                        ('usage_from', '2021-02-20'),
                        ('quantity', 2),
                        ('gross_final_price', '40.00'),
                    ]
                ),
                OrderedDict(
                    [
                        ('type', 'PS'),
                        ('usage_to', '2021-03-20'),
                        ('usage_from', '2021-02-20'),
                        ('quantity', 50),
                        ('gross_final_price', '35.00'),
                    ]
                ),
            ],
            'subscription_duration': 1,
        }

    def test_data_ok(self):
        subscription = SubscriptionOverdueDetailsView.get_overdue_subscription(
            business_id=self.business.id,
        )
        serializer = BillingOverdueSubscriptionSerializer(
            instance=subscription,
        )
        self.assertEqual(serializer.data, self.expected_data)

    def test_transaction_without_billing_cycle(self):
        self.transaction.billing_cycle_id = None
        self.transaction.save()
        subscription = SubscriptionOverdueDetailsView.get_overdue_subscription(
            business_id=self.business.id,
        )
        serializer = BillingOverdueSubscriptionSerializer(instance=subscription)
        self.assertFalse(serializer.data)

    def test_last_transaction_success(self):
        baker.make(
            BillingTransaction,
            billing_cycle=self.billing_cycle,
            status=TransactionStatus.CHARGED,
            subscription=self.subscription,
            transaction_source=TransactionSource.BILLING_SUBSCRIPTION,
        )
        subscription = SubscriptionOverdueDetailsView.get_overdue_subscription(
            business_id=self.business.id,
        )
        serializer = BillingOverdueSubscriptionSerializer(instance=subscription)
        self.assertFalse(serializer.data)

    def test_long_subscription_data(self):
        self.subscription.date_end = datetime(2023, 3, 20, tzinfo=UTC)
        self.subscription.subscription_duration = BillingLongSubscriptionDuration.THREE_MONTHS
        self.subscription.save()

        subscription = SubscriptionOverdueDetailsView.get_overdue_subscription(
            business_id=self.business.id,
        )
        serializer = BillingOverdueSubscriptionSerializer(instance=subscription)
        self.expected_data['subscription_duration'] = (
            BillingLongSubscriptionDuration.THREE_MONTHS.value
        )
        self.expected_data['product_charges'][0]['usage_to'] = '2023-03-20'
        self.assertEqual(serializer.data, self.expected_data)


class TestBusinessOfferSerializer(BaseOfferItemsSetup):
    def test_staff_add_on__no_discount(self):
        baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.staffer_saas.id,
            active=True,
        )
        result = BusinessOfferSerializer(
            instance=self.offer,
            context=dict(
                staff_count=3,
                business_id=self.business.id,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
            ),
        ).data

        self.assertEqual(self.offer.id, result.get('offer_id'))
        self.assertEqual(settings.DEFAULT_SUBSCRIPTION_DURATION, result.get('subscription_period'))
        self.assertEqual(3, result.get('staff_count'))
        self.assertIsNone(result.get('offer_valid_to'))
        self.assertIsNone(result.get('discount_code'))

        # Staff add-on should be nested in main product
        self.assertIn(ProductType.SAAS.value, result['products'])
        saas_data = result['products'][ProductType.SAAS.value]
        self.assertEqual(len(saas_data), 1)
        saas_entry = saas_data[0]
        self.assertEqual(saas_entry.get('product_id'), self.saas.id)
        self.assertEqual(saas_entry.get('product_type'), self.saas.product_type)
        self.assertEqual(saas_entry.get('name'), 'Best plan ever')
        self.assertEqual(saas_entry.get('full_price'), '149.99')
        self.assertEqual(saas_entry.get('full_price_gross'), '184.49')
        self.assertEqual(saas_entry.get('currency'), self.saas.currency)
        self.assertEqual(saas_entry.get('discount_type'), DiscountType.NO_DISCOUNT.value)
        self.assertIsNone(saas_entry.get('discount_amount'))
        self.assertIsNone(saas_entry.get('discount_percentage'))
        self.assertIsNone(saas_entry.get('discount_duration'))
        self.assertFalse(saas_entry.get('has_infinite_discount'))
        self.assertEqual(saas_entry.get('staff_add_on_id'), self.staffer_saas.id)
        self.assertEqual(saas_entry.get('staffers_included'), 1)
        self.assertEqual(saas_entry.get('staff_charge_limit'), 9)
        self.assertEqual(saas_entry.get('staff_full_price'), '10.00')
        self.assertEqual(saas_entry.get('staff_full_price_gross'), '12.30')
        self.assertEqual(saas_entry.get('staff_discount_type'), DiscountType.NO_DISCOUNT.value)
        self.assertIsNone(saas_entry.get('staff_discount_amount'))
        self.assertIsNone(saas_entry.get('staff_discount_amount_gross'))
        self.assertIsNone(saas_entry.get('staff_discount_percentage'))
        self.assertEqual(saas_entry.get('sms_price'), '0.010')
        self.assertEqual(saas_entry.get('sms_price_gross'), '0.0123')
        self.assertEqual(saas_entry.get('sms_add_on_id'), self.postpaid_sms.id)
        self.assertEqual(saas_entry.get('sms_amount'), 200)
        self.assertEqual(saas_entry.get('staff_sms_amount'), 25)
        self.assertEqual(saas_entry.get('discounted_price'), '149.99')
        self.assertEqual(saas_entry.get('discounted_price_gross'), '184.49')
        self.assertEqual(saas_entry.get('staff_discounted_price'), '10.00')
        self.assertEqual(saas_entry.get('staff_discounted_price_gross'), '12.30')
        self.assertEqual(saas_entry.get('total_price'), '169.99')
        # Tax-free SaaS for 149.99 dollars + 2 x paid staffers for (10 dollars + 1.50 tax)
        self.assertEqual(saas_entry.get('total_price_gross'), '209.09')
        self.assertEqual(saas_entry.get('total_final_price'), '169.99')
        self.assertEqual(saas_entry.get('total_final_price_gross'), '209.09')
        self.assertEqual(saas_entry.get('total_sms_amount'), 250)
        self.assertEqual(saas_entry.get('total_discount'), '0.00')

    def test_staff_add_on__with_discount(self):
        offer_valid_to = tznow() + timedelta(days=1)
        baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.staffer_saas.id,
            active=True,
        )
        baker.make(
            BillingDiscountCodeUsage,
            business=self.business,
            offer=self.offer,
            discount_code__discount_code='SPECIAL_OFFER',
        )

        result = BusinessOfferSerializer(
            instance=self.offer,
            context=dict(
                staff_count=3,
                business_id=self.business.id,
                tax_matrix=TaxMatrix.for_business_id(self.business.id),
                offer_valid_to=offer_valid_to,
            ),
        ).data

        self.assertEqual(self.offer.id, result.get('offer_id'))
        self.assertEqual(settings.DEFAULT_SUBSCRIPTION_DURATION, result.get('subscription_period'))
        self.assertEqual(3, result.get('staff_count'))
        self.assertEqual(offer_valid_to, result.get('offer_valid_to'))
        self.assertEqual('SPECIAL_OFFER', result.get('discount_code'))
