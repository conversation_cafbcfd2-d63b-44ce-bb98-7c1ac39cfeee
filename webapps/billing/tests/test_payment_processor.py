from unittest.mock import (
    call,
    MagicMock,
    patch,
)
from decimal import Decimal

import braintree
from django.core.exceptions import ValidationError
from django.test import TestCase, override_settings
from model_bakery import baker
from parameterized import parameterized

from lib.enums import PaymentMethodType
from webapps.billing.enums import (
    CreditCardType,
    PaymentProcessorType,
    TransactionResponseType,
    TransactionSource,
    TransactionStatus,
)
from webapps.billing.error_handling.errors import BillingErrorGroupEnum
from webapps.billing.models import (
    BillingCreditCardInfo,
    BillingCycle,
    BillingPaymentMethod,
    BillingSubscription,
    BillingTransaction,
)
from webapps.billing.payment_processor import (
    PaymentProcessorBridge,
    get_payment_processor,
    PaymentMethodResult,
    PaymentProcessor,
    PaymentProcessorResult,
    StripePaymentProcessor,
)
from webapps.billing.services.charge import (
    BillingTransactionService,
    StripeChargeService,
)
from webapps.billing.tasks import add_or_update_billing_info_task
from webapps.braintree_app.payment_processor import (
    BraintreeCustomerResult,
    BraintreePaymentMethodResult,
    BraintreePaymentProcessor,
)
from webapps.braintree_app.tests.utils import (
    customer_create,
    payment_method_credit_card_success,
    payment_method_credit_card_without_verification,
    payment_method_paypal_success,
    transaction_successful,
    transaction_failed_without_transaction,
)
from webapps.braintree_app.utils import (
    BraintreeError,
    BraintreeErrorResult,
)
from webapps.business.models import Business


class TestBraintreePaymentProcessor(TestCase):
    def setUp(self):
        self.business = baker.make(Business)
        self.subscription = baker.make(
            BillingSubscription,
            business_id=self.business.id,
        )
        self.billing_cycle = baker.make(
            BillingCycle,
            business_id=self.business.id,
            subscription_id=self.subscription.id,
        )
        self.payment_method = baker.make(
            BillingPaymentMethod,
            payment_processor=PaymentProcessorType.BRAINTREE,
            token='toooken!',
        )
        self.transaction_successful = transaction_successful(self.payment_method.token)

    @patch.object(BraintreePaymentProcessor, 'find_payment_method')
    def test_save_transaction_ok(self, find_payment_method_mock):
        PaymentProcessor.save_transaction(
            business_id=self.business.id,
            transaction=self.transaction_successful.transaction,
            transaction_source=TransactionSource.BILLING_SUBSCRIPTION.value,
            subscription_id=self.subscription.id,
            billing_cycle_id=self.billing_cycle.id,
        )
        self.assertEqual(BillingTransaction.objects.count(), 1)
        billing_transaction = BillingTransaction.objects.first()
        self.assertEqual(billing_transaction.external_id, '12332ab')
        self.assertEqual(billing_transaction.status, TransactionStatus.CHARGED)
        self.assertEqual(billing_transaction.amount, Decimal('99.99'))
        self.assertEqual(billing_transaction.currency, 'USD')
        self.assertEqual(billing_transaction.business_id, self.business.id)
        self.assertEqual(billing_transaction.payment_processor, PaymentProcessorType.BRAINTREE)
        self.assertEqual(billing_transaction.payment_method_id, self.payment_method.id)
        self.assertEqual(billing_transaction.subscription_id, self.subscription.id)
        self.assertEqual(billing_transaction.billing_cycle_id, self.billing_cycle.id)
        self.assertEqual(billing_transaction.response_code, '1000')
        self.assertEqual(billing_transaction.response_type, TransactionResponseType.APPROVED)
        self.assertEqual(billing_transaction.response_text, 'Processor rt')
        self.assertEqual(billing_transaction.transaction_source, 'billing_subscription')
        self.assertEqual(find_payment_method_mock.call_count, 0)

    @patch.object(BraintreePaymentProcessor, 'find_payment_method')
    def test_save_transaction_no_payment_method(self, find_payment_method_mock):
        find_payment_method_mock.return_value = BraintreePaymentMethodResult(
            is_success=False,
        )

        self.payment_method.token = 'Non matching token'
        self.payment_method.save()
        PaymentProcessor.save_transaction(
            business_id=self.business.id,
            transaction=self.transaction_successful.transaction,
            transaction_source=TransactionSource.BILLING_SUBSCRIPTION.value,
            subscription_id=self.subscription.id,
            billing_cycle_id=self.billing_cycle.id,
        )
        self.assertEqual(BillingTransaction.objects.count(), 1)
        billing_transaction = BillingTransaction.objects.first()
        self.assertIsNone(billing_transaction.payment_method_id)
        self.assertEqual(billing_transaction.external_id, '12332ab')
        self.assertEqual(billing_transaction.business_id, self.business.id)
        self.assertEqual(find_payment_method_mock.call_count, 1)

    @patch.object(BraintreePaymentProcessor, 'find_payment_method')
    def test_save_transaction_payment_method_from_braintree(
        self,
        find_payment_method_mock,
    ):
        find_payment_method_mock.return_value = BraintreePaymentMethodResult(
            is_success=True,
            payment_method=(payment_method_credit_card_success().payment_method),
        )

        self.payment_method.token = 'Non matching token'
        self.payment_method.save()

        PaymentProcessor.save_transaction(
            business_id=self.business.id,
            transaction=self.transaction_successful.transaction,
            transaction_source=TransactionSource.BILLING_SUBSCRIPTION.value,
            subscription_id=self.subscription.id,
            billing_cycle_id=self.billing_cycle.id,
        )
        self.assertEqual(BillingTransaction.objects.count(), 1)
        billing_transaction = BillingTransaction.objects.first()
        self.assertEqual(
            billing_transaction.payment_method_id,
            BillingPaymentMethod.objects.exclude(pk=self.payment_method.pk).get().pk,
        )
        self.assertEqual(find_payment_method_mock.call_count, 1)

    def test_save_transaction_null_response_type(self):
        self.transaction_successful.transaction.processor_response_type = None
        self.transaction_successful.transaction.status = (
            braintree.Transaction.Status.GatewayRejected
        )
        PaymentProcessor.save_transaction(
            business_id=self.business.id,
            transaction=self.transaction_successful.transaction,
            transaction_source=TransactionSource.BILLING_SUBSCRIPTION.value,
            subscription_id=self.subscription.id,
            billing_cycle_id=self.billing_cycle.id,
        )
        self.assertEqual(BillingTransaction.objects.count(), 1)
        billing_transaction = BillingTransaction.objects.first()
        self.assertIsNone(billing_transaction.response_type)
        self.assertEqual(billing_transaction.status, TransactionStatus.FAILED)

    @patch.object(BraintreePaymentProcessor, 'get_or_create_customer')
    def test_get_or_create_customer_success(self, get_or_create_customer_mock):
        get_or_create_customer_mock.return_value = BraintreeCustomerResult(
            is_success=True,
            customer=customer_create(),
        )
        results = PaymentProcessor.get_or_create_customer(self.business)
        self.assertEqual(
            results,
            PaymentProcessorResult(is_success=True, message='Success!'),
        )

    @patch.object(BraintreePaymentProcessor, 'get_or_create_customer')
    def test_get_or_create_customer_failed(self, get_or_create_customer_mock):
        error_group = BillingErrorGroupEnum.CONTACT_WITH_US
        get_or_create_customer_mock.return_value = BraintreeCustomerResult(
            is_success=False,
            message=customer_create(False).message,
            errors=BraintreeError(customer_create(False)).errors,
        )
        results = PaymentProcessor.get_or_create_customer(self.business)
        self.assertEqual(
            results,
            PaymentProcessorResult(
                is_success=False,
                message=error_group.label,
                internal_errors=[
                    BraintreeErrorResult(
                        description='Test error message', field='Test error attr', code=10000
                    )
                ],
                internal_message=10000,
                error_code=error_group.value,
            ),
        )

    @patch.object(BraintreePaymentProcessor, 'create_payment_method')
    def test_create_payment_method_successful_credit_card(self, create_payment_method_mock):
        create_payment_method_mock.return_value = BraintreePaymentMethodResult(
            is_success=True,
            payment_method=(payment_method_credit_card_success().payment_method),
        )
        result = PaymentProcessor.create_payment_method(
            nonce="tokencc_xx_xxxxxx_xxxxxx_xxxxxx_xxxxxx_xxx",
            business_id=self.business.id,
            billing_address={
                "street_address": "Sesame street 123",
                "extended_address": "1st Floor",
                "postal_code": "90210",
                "locality": "Los Angeles",
                "country_name": "Some Country",
            },
            make_default=True,
        )
        self.assertIsInstance(result, PaymentMethodResult)
        self.assertTrue(result.is_success)
        payment_method = result.payment_method
        self.assertIsInstance(payment_method, BillingPaymentMethod)
        self.assertEqual(payment_method.business_id, self.business.id)
        self.assertEqual(payment_method.payment_method_type, PaymentMethodType.CREDIT_CARD)
        self.assertEqual(payment_method.payment_processor, PaymentProcessorType.BRAINTREE)
        self.assertEqual(payment_method.token, 'token!')
        self.assertTrue(payment_method.default)
        cc_info = payment_method.credit_card
        self.assertEqual(cc_info.card_type, CreditCardType.VISA)
        self.assertEqual(cc_info.cardholder_name, 'John Kowalsky')
        self.assertEqual(cc_info.first_6_digits, '411111')
        self.assertEqual(cc_info.last_4_digits, '1111')
        self.assertEqual(cc_info.expiration_date.year, 2025)
        self.assertEqual(cc_info.expiration_date.month, 12)
        self.assertEqual(cc_info.country_of_issuance, 'Test Country of Issuance')
        self.assertEqual(cc_info.address_line_1, 'Test 88')
        self.assertEqual(cc_info.address_line_2, 'flat 9')
        self.assertEqual(cc_info.city, 'Test city')
        self.assertEqual(cc_info.zipcode, '99999')
        self.assertEqual(cc_info.country, 'Test country')

    @patch.object(BraintreePaymentProcessor, 'create_payment_method')
    def test_create_payment_method_successful_credit_card_without_verification(
        self, create_payment_method_mock
    ):
        create_payment_method_mock.return_value = BraintreePaymentMethodResult(
            is_success=True,
            payment_method=(payment_method_credit_card_without_verification().payment_method),
        )
        result = PaymentProcessor.create_payment_method(
            nonce="tokencc_xx_xxxxxx_xxxxxx_xxxxxx_xxxxxx_xxx",
            business_id=self.business.id,
            make_default=True,
        )
        cc_info = result.payment_method.credit_card
        self.assertEqual(cc_info.card_type, CreditCardType.VISA)
        self.assertEqual(cc_info.cardholder_name, 'John Kowalsky')
        self.assertEqual(cc_info.first_6_digits, '411111')
        self.assertEqual(cc_info.last_4_digits, '1111')
        self.assertEqual(cc_info.expiration_date.year, 2025)
        self.assertEqual(cc_info.expiration_date.month, 12)
        for field in [
            'country_of_issuance',
            'address_line_1',
            'address_line_2',
            'city',
            'zipcode',
            'country',
        ]:
            self.assertIsNone(getattr(cc_info, field))

    @patch.object(BraintreePaymentProcessor, 'create_payment_method')
    def test_create_payment_method_successful_paypal(self, create_payment_method_mock):
        create_payment_method_mock.return_value = BraintreePaymentMethodResult(
            is_success=True,
            payment_method=(payment_method_paypal_success().payment_method),
        )
        result = PaymentProcessor.create_payment_method(
            nonce="tokencc_xx_xxxxxx_xxxxxx_xxxxxx_xxxxxx_xxx",
            business_id=self.business.id,
            billing_address={
                "street_address": "Sesame street 123",
                "extended_address": "1st Floor",
                "postal_code": "90210",
                "locality": "Los Angeles",
                "country_name": "Some Country",
            },
            make_default=True,
        )
        self.assertIsInstance(result, PaymentMethodResult)
        self.assertTrue(result.is_success)
        payment_method = result.payment_method
        self.assertIsInstance(payment_method, BillingPaymentMethod)
        self.assertEqual(payment_method.business_id, self.business.id)
        self.assertEqual(payment_method.payment_method_type, PaymentMethodType.PAYPAL)
        self.assertEqual(payment_method.payment_processor, PaymentProcessorType.BRAINTREE)
        self.assertEqual(payment_method.token, 'token!')
        self.assertTrue(payment_method.default)
        self.assertIsNone(payment_method.credit_card)
        self.assertEqual(BillingCreditCardInfo.objects.count(), 0)

    @patch.object(BraintreePaymentProcessor, 'create_payment_method')
    def test_create_payment_method_successful_no_billing_address(self, create_payment_method_mock):
        create_payment_method_mock.return_value = BraintreePaymentMethodResult(
            is_success=True,
            payment_method=(payment_method_paypal_success().payment_method),
        )
        result = PaymentProcessor.create_payment_method(
            nonce="tokencc_xx_xxxxxx_xxxxxx_xxxxxx_xxxxxx_xxx",
            business_id=self.business.id,
            make_default=True,
        )
        self.assertIsInstance(result, PaymentMethodResult)
        self.assertTrue(result.is_success)
        payment_method = result.payment_method
        self.assertIsInstance(payment_method, BillingPaymentMethod)
        self.assertEqual(payment_method.business_id, self.business.id)
        self.assertEqual(payment_method.payment_method_type, PaymentMethodType.PAYPAL)
        self.assertEqual(payment_method.payment_processor, PaymentProcessorType.BRAINTREE)
        self.assertEqual(payment_method.token, 'token!')
        self.assertTrue(payment_method.default)
        self.assertIsNone(payment_method.credit_card)
        self.assertEqual(BillingCreditCardInfo.objects.count(), 0)

    @patch.object(BraintreePaymentProcessor, 'create_payment_method')
    def test_create_payment_method_failed(self, create_payment_method_mock):
        error_group = BillingErrorGroupEnum.CONTACT_WITH_US
        error_code = error_group.codes[0]
        create_payment_method_mock.return_value = BraintreePaymentMethodResult(
            is_success=False,
            message=error_group.label,
            errors=[
                BraintreeErrorResult(
                    description='Test error',
                    field='test field',
                    code=error_code,
                )
            ],
        )
        result = PaymentProcessor.create_payment_method(
            nonce="tokencc_xx_xxxxxx_xxxxxx_xxxxxx_xxxxxx_xxx",
            business_id=self.business.id,
            billing_address={
                "street_address": "Sesame street 123",
                "extended_address": "1st Floor",
                "postal_code": "90210",
                "locality": "Los Angeles",
                "country_name": "Some Country",
            },
            make_default=True,
        )
        self.assertEqual(
            result,
            PaymentProcessorResult(
                is_success=False,
                message=error_group.label,
                internal_errors=[
                    BraintreeErrorResult(
                        description='Test error', field='test field', code=error_code
                    )
                ],
                internal_message=error_code,
                error_code=error_group.value,
            ),
        )

    @patch.object(PaymentProcessor, 'create_payment_method')
    @patch.object(PaymentProcessor, 'get_or_create_customer')
    def test_add_or_update_billing_info_task_success(
        self,
        get_or_create_customer_mock,
        create_payment_method_mock,
    ):
        payment_method = baker.make(BillingPaymentMethod)
        get_or_create_customer_mock.return_value = PaymentProcessorResult(
            is_success=True, message='Success!'
        )
        create_payment_method_mock.return_value = PaymentMethodResult(
            is_success=True,
            message='Your payment method has been saved.',
            payment_method=payment_method,
        )
        result = add_or_update_billing_info_task.run(
            self.business.id,
            dict(
                nonce="tokencc_xx_xxxxxx_xxxxxx_xxxxxx_xxxxxx_xxx",
                business_id=self.business.id,
                billing_address={
                    "street_address": "Sesame street 123",
                    "extended_address": "1st Floor",
                    "postal_code": "90210",
                    "locality": "Los Angeles",
                    "country_name": "Some Country",
                },
            ),
        )
        self.assertEqual(
            result,
            dict(
                message='Your payment method has been saved.',
                payment_method_id=payment_method.id,
            ),
        )

    @patch.object(PaymentProcessor, 'create_payment_method')
    @patch.object(PaymentProcessor, 'get_or_create_customer')
    def test_add_or_update_billing_info_task_success_no_billing_address(
        self,
        get_or_create_customer_mock,
        create_payment_method_mock,
    ):
        payment_method = baker.make(BillingPaymentMethod)
        get_or_create_customer_mock.return_value = PaymentProcessorResult(
            is_success=True, message='Success!'
        )
        create_payment_method_mock.return_value = PaymentMethodResult(
            is_success=True,
            message='Your payment method has been saved.',
            payment_method=payment_method,
        )
        result = add_or_update_billing_info_task.run(
            self.business.id,
            dict(
                nonce="tokencc_xx_xxxxxx_xxxxxx_xxxxxx_xxxxxx_xxx",
                business_id=self.business.id,
            ),
        )
        self.assertEqual(
            result,
            dict(
                message='Your payment method has been saved.',
                payment_method_id=payment_method.id,
            ),
        )

    @patch.object(PaymentProcessor, 'create_payment_method', MagicMock())
    @patch.object(PaymentProcessor, 'get_or_create_customer')
    def test_add_or_update_billing_info_task_customer_failed(
        self,
        get_or_create_customer_mock,
    ):
        get_or_create_customer_mock.return_value = PaymentProcessorResult(
            is_success=False,
            message='Error message',
            internal_errors=[
                BraintreeErrorResult(description='Test error', field='Test field', code=10000)
            ],
            internal_message='Internal error message',
            error_code=100,
        )
        result = add_or_update_billing_info_task.run(
            self.business.id,
            dict(
                nonce="tokencc_xx_xxxxxx_xxxxxx_xxxxxx_xxxxxx_xxx",
                business_id=self.business.id,
                billing_address={
                    "street_address": "Sesame street 123",
                    "extended_address": "1st Floor",
                    "postal_code": "90210",
                    "locality": "Los Angeles",
                    "country_name": "Some Country",
                },
            ),
        )
        self.assertEqual(
            result,
            {
                'is_success': False,
                'message': 'Error message',
                'internal_errors': [
                    {
                        'description': 'Test error',
                        'field': 'Test field',
                        'code': 10000,
                    }
                ],
                'internal_message': 'Internal error message',
                'error_code': 100,
            },
        )

    @patch.object(PaymentProcessor, 'create_payment_method')
    @patch.object(PaymentProcessor, 'get_or_create_customer')
    def test_add_or_update_billing_info_task_payment_method_failed(
        self,
        get_or_create_customer_mock,
        create_payment_method_mock,
    ):
        get_or_create_customer_mock.return_value = PaymentProcessorResult(
            is_success=True, message='Success!'
        )
        create_payment_method_mock.return_value = PaymentProcessorResult(
            is_success=False,
            message='Error message',
            internal_errors=[
                BraintreeErrorResult(description='Test error', field='Test field', code=10000)
            ],
            internal_message='Internal error message',
            error_code=100,
        )
        result = add_or_update_billing_info_task.run(
            self.business.id,
            dict(
                nonce="tokencc_xx_xxxxxx_xxxxxx_xxxxxx_xxxxxx_xxx",
                business_id=self.business.id,
                billing_address={
                    "street_address": "Sesame street 123",
                    "extended_address": "1st Floor",
                    "postal_code": "90210",
                    "locality": "Los Angeles",
                    "country_name": "Some Country",
                },
            ),
        )
        self.assertEqual(
            result,
            {
                'is_success': False,
                'message': 'Error message',
                'internal_errors': [
                    {
                        'description': 'Test error',
                        'field': 'Test field',
                        'code': 10000,
                    }
                ],
                'internal_message': 'Internal error message',
                'error_code': 100,
            },
        )

    @patch.object(BraintreePaymentProcessor, 'find_payment_method')
    def test_create_payment_method_from_braintree_credit_card(
        self,
        find_payment_method_mock,
    ):
        find_payment_method_mock.return_value = BraintreePaymentMethodResult(
            is_success=True,
            payment_method=(payment_method_credit_card_success().payment_method),
        )

        result = PaymentProcessor.create_payment_method_from_braintree(
            business_id=self.business.id, payment_method_token='token!'
        )

        self.assertIsInstance(result, PaymentMethodResult)
        self.assertTrue(result.is_success)
        payment_method = result.payment_method
        self.assertIsInstance(payment_method, BillingPaymentMethod)
        self.assertEqual(payment_method.business_id, self.business.id)
        self.assertEqual(
            payment_method.payment_method_type,
            PaymentMethodType.CREDIT_CARD,
        )
        self.assertEqual(
            payment_method.payment_processor,
            PaymentProcessorType.BRAINTREE,
        )
        self.assertEqual(payment_method.token, 'token!')
        self.assertTrue(payment_method.default)
        cc_info = payment_method.credit_card
        self.assertEqual(cc_info.card_type, CreditCardType.VISA)
        self.assertEqual(cc_info.cardholder_name, 'John Kowalsky')
        self.assertEqual(cc_info.first_6_digits, '411111')
        self.assertEqual(cc_info.last_4_digits, '1111')
        self.assertEqual(cc_info.expiration_date.year, 2025)
        self.assertEqual(cc_info.expiration_date.month, 12)
        self.assertEqual(
            cc_info.country_of_issuance,
            'Test Country of Issuance',
        )
        self.assertEqual(cc_info.address_line_1, 'Test 88')
        self.assertEqual(cc_info.address_line_2, 'flat 9')
        self.assertEqual(cc_info.city, 'Test city')
        self.assertEqual(cc_info.zipcode, '99999')
        self.assertEqual(cc_info.country, 'Test country')

    @patch.object(BraintreePaymentProcessor, 'find_payment_method')
    def test_create_payment_method_from_braintree_successful_paypal(
        self,
        find_payment_method_mock,
    ):
        find_payment_method_mock.return_value = BraintreePaymentMethodResult(
            is_success=True,
            payment_method=(payment_method_paypal_success().payment_method),
        )
        result = PaymentProcessor.create_payment_method_from_braintree(
            business_id=self.business.id, payment_method_token='token!'
        )

        self.assertIsInstance(result, PaymentMethodResult)
        self.assertTrue(result.is_success)
        payment_method = result.payment_method
        self.assertIsInstance(payment_method, BillingPaymentMethod)
        self.assertEqual(payment_method.business_id, self.business.id)
        self.assertEqual(
            payment_method.payment_method_type,
            PaymentMethodType.PAYPAL,
        )
        self.assertEqual(
            payment_method.payment_processor,
            PaymentProcessorType.BRAINTREE,
        )
        self.assertEqual(payment_method.token, 'token!')
        self.assertTrue(payment_method.default)
        self.assertIsNone(payment_method.credit_card)
        self.assertEqual(BillingCreditCardInfo.objects.count(), 0)

    @patch.object(BraintreePaymentProcessor, 'find_payment_method')
    def test_create_payment_method_from_braintree_already_exists(
        self,
        find_payment_method_mock,
    ):
        payment_method = baker.make(BillingPaymentMethod, token='token!')

        result = PaymentProcessor.create_payment_method_from_braintree(
            business_id=self.business.id, payment_method_token='token!'
        )

        self.assertEqual(
            result,
            PaymentMethodResult(
                is_success=True,
                payment_method=payment_method,
            ),
        )
        self.assertEqual(find_payment_method_mock.call_count, 0)

    def test_create_payment_method_from_braintree_change_default(self):
        old_payment_method = baker.make(
            BillingPaymentMethod,
            payment_processor=PaymentProcessorType.BRAINTREE,
            default=True,
            business_id=self.business.id,
            token='abc',
        )
        payment_method = baker.make(
            BillingPaymentMethod,
            payment_processor=PaymentProcessorType.BRAINTREE,
            default=False,
            business_id=self.business.id,
            token='token!',
        )
        stripe_payment_method = baker.make(
            BillingPaymentMethod,
            payment_processor=PaymentProcessorType.STRIPE,
            default=True,
            business_id=self.business.id,
        )
        result = PaymentProcessor.create_payment_method_from_braintree(
            business_id=self.business.id, payment_method_token='token!'
        )
        self.assertEqual(BillingPaymentMethod.objects.count(), 4)

        payment_method.refresh_from_db()
        old_payment_method.refresh_from_db()
        stripe_payment_method.refresh_from_db()

        self.assertTrue(result.payment_method.default)
        self.assertEqual(result.payment_method.id, payment_method.id)
        self.assertTrue(payment_method.default)
        self.assertFalse(old_payment_method.default)
        self.assertTrue(stripe_payment_method.default)

        default_payments = BillingPaymentMethod.objects.filter(
            business_id=self.business.id,
            default=True,
        ).count()
        self.assertEqual(default_payments, 2)

    @parameterized.expand(
        [
            PaymentMethodType.VENMO,
            PaymentMethodType.PAYPAL,
            PaymentMethodType.CREDIT_CARD,
        ]
    )
    @override_settings(BRAINTREE_PAYMENT_METHODS=[])
    def test_payment_method_validator_no_ok(self, payment_type):
        payment_method = baker.make(BillingPaymentMethod, payment_method_type=payment_type)
        self.assertRaises(ValidationError, payment_method.full_clean)

    @override_settings(
        BRAINTREE_PAYMENT_METHODS=[
            PaymentMethodType.CREDIT_CARD,
        ]
    )
    def test_payment_method_validator_ok(self):
        payment_method = baker.make(
            BillingPaymentMethod, payment_method_type=PaymentMethodType.CREDIT_CARD
        )
        self.assertTrue(payment_method.full_clean)

    def test_parse_error__no_code_response(self):
        fallback_error_code = BillingErrorGroupEnum.CONTACT_WITH_US.value
        fallback_error_message = BillingErrorGroupEnum.CONTACT_WITH_US.label
        response = MagicMock(transaction=None, message=fallback_error_message)

        result = PaymentProcessor.parse_error(response=response)
        self.assertDictEqual(
            result,
            {
                'error_code': fallback_error_code,
                'internal_errors': [],
                'internal_message': f'{response.message}, {fallback_error_code}',
                'message': fallback_error_message,
            },
        )

    def test_parse_error__code_response(self):
        error_group = BillingErrorGroupEnum.TRY_AGAIN
        error_code = error_group.codes[0]
        response = MagicMock(
            transaction=MagicMock(processor_response_code=error_code),
            message=error_group.label,
        )

        result = PaymentProcessor.parse_error(response=response)
        self.assertDictEqual(
            result,
            {
                'error_code': error_group.value,
                'internal_errors': {'processor_response_code': error_code},
                'internal_message': f'{response.message}, {error_code}',
                'message': error_group.label,
            },
        )


class TestGetPaymentProcessor(TestCase):
    def setUp(self):
        self.business = baker.make(Business)

    @parameterized.expand(
        [
            (PaymentProcessorType.BRAINTREE, PaymentProcessor),
            (PaymentProcessorType.STRIPE, StripePaymentProcessor),
            ('', PaymentProcessor),
            (None, PaymentProcessor),
        ]
    )
    def test_get_payment_processor(self, payment_processor, result):
        with patch('webapps.billing.payment_processor.get_business_payment_processor') as pp_patch:
            pp_patch.return_value = payment_processor
            payment_processor = get_payment_processor(business_id=self.business.id)
            self.assertEqual(payment_processor, result)


class TestStripePaymentProcessor(TestCase):
    @patch.object(BillingTransactionService, 'create_from_charge')
    def test_save_transaction(self, method_patched):
        transaction = MagicMock(amount=12)

        StripePaymentProcessor.save_transaction(
            business_id=11,
            transaction=transaction,
            transaction_source=TransactionSource.BILLING_SUBSCRIPTION.value,
            subscription_id=12,
            billing_cycle_id=13,
        )
        self.assertEqual(
            method_patched.call_args,
            call(
                business_id=11,
                transaction=transaction,
                transaction_source='billing_subscription',
                subscription_id=12,
                billing_cycle_id=13,
            ),
        )

    @patch.object(StripeChargeService, 'create_charge')
    def test_create_transaction(self, method_patched):
        StripePaymentProcessor.create_transaction(
            business_id=11,
            amount=Decimal(12),
            currency='pln',
            description=TransactionSource.BILLING_SUBSCRIPTION.label,
        )
        self.assertEqual(
            method_patched.call_args,
            call(
                business_id=11,
                amount=Decimal(12),
                currency='pln',
                description='Billing subscription',
                metadata={},
            ),
        )

    @patch.object(StripeChargeService, 'parse_error_response')
    def test_parse_error(self, method_patched):
        response = MagicMock(message='Message ...')

        StripePaymentProcessor.parse_error(response=response)
        self.assertEqual(method_patched.call_args, call(charge_result=response))


class TestPaymentProcessorBridge(TestCase):
    @patch.object(PaymentProcessor, 'create_transaction')
    def test_create_transaction__braintree(self, method_patched):
        with patch('webapps.billing.payment_processor.get_payment_processor') as pp_patch:
            pp_patch.return_value = PaymentProcessor

            PaymentProcessorBridge.create_transaction(
                business_id=1,
                amount=Decimal(12),
                currency='pln',
                description=TransactionSource.BILLING_SUBSCRIPTION.label,
                metadata={},
            )
        self.assertEqual(
            method_patched.call_args,
            call(
                business_id=1,
                amount=Decimal(12),
                currency='pln',
                description='Billing subscription',
                metadata={},
            ),
        )

    @patch.object(StripePaymentProcessor, 'create_transaction')
    def test_create_transaction__stripe(self, method_patched):
        with patch('webapps.billing.payment_processor.get_payment_processor') as pp_patch:
            pp_patch.return_value = StripePaymentProcessor

            PaymentProcessorBridge.create_transaction(
                business_id=1,
                amount=Decimal(12),
                currency='pln',
                description=TransactionSource.BILLING_SUBSCRIPTION.label,
                metadata={},
            )
        self.assertEqual(
            method_patched.call_args,
            call(
                business_id=1,
                amount=Decimal(12),
                currency='pln',
                description='Billing subscription',
                metadata={},
            ),
        )

    @patch.object(PaymentProcessor, 'save_transaction')
    def test_save_transaction__braintree(self, method_patched):
        with patch('webapps.billing.payment_processor.get_payment_processor') as pp_patch:
            pp_patch.return_value = PaymentProcessor
            transaction = MagicMock(amount=12)

            PaymentProcessorBridge.save_transaction(
                business_id=11,
                transaction=transaction,
                subscription_id=12,
                billing_cycle_id=13,
                transaction_source=TransactionSource.BILLING_SUBSCRIPTION.value,
            )
        self.assertEqual(
            method_patched.call_args,
            call(
                business_id=11,
                transaction=transaction,
                transaction_source='billing_subscription',
                subscription_id=12,
                billing_cycle_id=13,
            ),
        )

    @patch.object(StripePaymentProcessor, 'save_transaction')
    def test_save_transaction__stripe(self, method_patched):
        with patch('webapps.billing.payment_processor.get_payment_processor') as pp_patch:
            pp_patch.return_value = StripePaymentProcessor
            transaction = MagicMock(amount=12)

            PaymentProcessorBridge.save_transaction(
                business_id=11,
                transaction=transaction,
                subscription_id=12,
                billing_cycle_id=13,
                transaction_source=TransactionSource.BILLING_SUBSCRIPTION.value,
            )
        self.assertEqual(
            method_patched.call_args,
            call(
                business_id=11,
                transaction=transaction,
                transaction_source='billing_subscription',
                subscription_id=12,
                billing_cycle_id=13,
            ),
        )

    @patch.object(PaymentProcessor, 'save_transaction')
    def test_save_transaction__no_optional_args(self, method_patched):
        with patch('webapps.billing.payment_processor.get_payment_processor') as pp_patch:
            pp_patch.return_value = PaymentProcessor
            transaction = MagicMock(amount=12)

            PaymentProcessorBridge.save_transaction(
                business_id=11,
                transaction=transaction,
            )
        self.assertEqual(
            method_patched.call_args,
            call(
                business_id=11,
                transaction=transaction,
                transaction_source='billing_subscription',
                subscription_id=None,
                billing_cycle_id=None,
            ),
        )

    @patch.object(PaymentProcessor, 'parse_error')
    def test_parse_error__braintree(self, method_patched):
        with patch('webapps.billing.payment_processor.get_payment_processor') as pp_patch:
            pp_patch.return_value = PaymentProcessor
            response = MagicMock(message='Message ...')

            PaymentProcessorBridge.parse_error(business_id=11, response=response)

        self.assertEqual(
            method_patched.call_args, call(response=response, serializable_result=False)
        )

    @patch.object(StripePaymentProcessor, 'parse_error')
    def test_parse_error__stripe(self, method_patched):
        with patch('webapps.billing.payment_processor.get_payment_processor') as pp_patch:
            pp_patch.return_value = StripePaymentProcessor
            response = MagicMock(message='Message ...')

            PaymentProcessorBridge.parse_error(business_id=11, response=response)

        self.assertEqual(
            method_patched.call_args, call(response=response, serializable_result=False)
        )

    def test_parse_error__braintree_not_serializable(self):
        with patch('webapps.billing.payment_processor.get_payment_processor') as pp_patch:
            pp_patch.return_value = PaymentProcessor
            response = transaction_failed_without_transaction()

            parsed_error = PaymentProcessorBridge.parse_error(business_id=11, response=response)
            self.assertIsInstance(parsed_error['internal_errors'][0], BraintreeErrorResult)

    def test_parse_error__braintree_serializable(self):
        with patch('webapps.billing.payment_processor.get_payment_processor') as pp_patch:
            pp_patch.return_value = PaymentProcessor
            response = transaction_failed_without_transaction()

            parsed_error = PaymentProcessorBridge.parse_error(
                business_id=11, response=response, serializable_result=True
            )
            self.assertIsInstance(parsed_error['internal_errors'][0], dict)
