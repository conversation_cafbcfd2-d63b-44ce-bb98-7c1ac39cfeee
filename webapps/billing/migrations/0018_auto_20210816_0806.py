# Generated by Django 3.1.13 on 2021-08-16 08:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('billing', '0017_billingbusinessdiscount_product_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='billingcreditcardinfo',
            name='card_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('A', 'American Express'),
                    ('V', 'Visa'),
                    ('M', 'MasterCard'),
                    ('E', 'Maestro'),
                    ('C', 'Carte Blanche'),
                    ('H', 'China UnionPay'),
                    ('D', 'Discover'),
                    ('L', 'Elo'),
                    ('J', 'JCB'),
                    ('S', 'Laser'),
                    ('O', 'Solo'),
                    ('W', 'Switch'),
                    ('U', 'Unknown'),
                ],
                max_length=1,
                null=True,
            ),
        ),
    ]
