# Generated by Django 3.2.10 on 2022-01-27 08:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('billing', '0027_externalcustomer'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='billingcreditcardinfo',
            name='card_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('A', 'American Express'),
                    ('V', 'Visa'),
                    ('M', 'MasterCard'),
                    ('E', '<PERSON><PERSON>'),
                    ('C', 'Carte Blanche'),
                    ('H', 'China UnionPay'),
                    ('D', 'Discover'),
                    ('L', 'Elo'),
                    ('J', 'JCB'),
                    ('S', 'Laser'),
                    ('O', 'Solo'),
                    ('W', 'Switch'),
                    ('I', 'Cartes Bancaires'),
                    ('R', 'Diners Club'),
                    ('P', 'Unionpay'),
                    ('U', 'Unknown'),
                ],
                max_length=1,
                null=True,
            ),
        ),
    ]
