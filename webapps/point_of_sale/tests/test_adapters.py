from datetime import datetime, timedelta, timezone
from unittest.mock import MagicMock, patch

import pytest

from lib.payment_providers.entities import (
    AccountHolderSettingsData,
    PortResponse,
    StripeAccountHolderSettingsEntity,
)
from lib.payment_providers.enums import ResponseEntityType
from webapps.point_of_sale.adapters import get_tap_to_pay_promo_start_adapter


@patch(
    "webapps.payment_providers.ports.account_holder_ports."
    "PaymentProvidersAccountHolderPort.get_account_holder_settings"
)
@patch("webapps.payment_gateway.ports.PaymentGatewayPort.get_business_wallet")
class TestGetTapToPayPromoStartAdapter:
    business_id = 123

    def test_when_no_business_then_returns_none(
        self,
        mock_get_business_wallet: MagicMock,
        mock_get_account_holder_settings: MagicMock,
    ):
        mock_get_business_wallet.return_value = None
        assert get_tap_to_pay_promo_start_adapter(self.business_id) is None

    @pytest.mark.parametrize(
        "entity",
        [
            pytest.param(
                None,
                id="no settings - account",
            ),
            pytest.param(
                AccountHolderSettingsData(stripe=None),
                id="no settings - stripe",
            ),
            pytest.param(
                AccountHolderSettingsData(
                    stripe=StripeAccountHolderSettingsEntity(
                        tap_to_pay_fees_accepted=None,
                        tap_to_pay_fees_accepted_at=None,
                    ),
                ),
                id="none",
            ),
            pytest.param(
                AccountHolderSettingsData(
                    stripe=StripeAccountHolderSettingsEntity(
                        tap_to_pay_fees_accepted=False,
                        tap_to_pay_fees_accepted_at=None,
                    ),
                ),
                id="false",
            ),
        ],
    )
    def test_when_fees_not_accepted_then_returns_none(
        self,
        mock_get_business_wallet: MagicMock,
        mock_get_account_holder_settings: MagicMock,
        entity: AccountHolderSettingsData | None,
    ):
        mock_get_account_holder_settings.return_value = PortResponse(
            entity_type=ResponseEntityType.ACCOUNT_HOLDER_SETTING_ENTITY,
            entity=entity,
        )
        assert get_tap_to_pay_promo_start_adapter(self.business_id) is None

    @pytest.mark.parametrize(
        "mods",
        [
            pytest.param({}, id="localtime"),
            pytest.param(dict(tzinfo=timezone.utc), id="UTC"),
            pytest.param(dict(tzinfo=timezone(timedelta(hours=2))), id="UTC+2"),
        ],
    )
    def test_when_fees_accepted_then_returns_when_as_datetime(
        self,
        mock_get_business_wallet: MagicMock,
        mock_get_account_holder_settings: MagicMock,
        mods: dict,
    ):
        accepted_at = datetime(2024, 7, 15, 6, 58, 33).replace(**mods)
        mock_get_account_holder_settings.return_value = PortResponse(
            entity_type=ResponseEntityType.ACCOUNT_HOLDER_SETTING_ENTITY,
            entity=AccountHolderSettingsData(
                stripe=StripeAccountHolderSettingsEntity(
                    tap_to_pay_fees_accepted=True,
                    tap_to_pay_fees_accepted_at=accepted_at,
                ),
            ),
        )
        assert get_tap_to_pay_promo_start_adapter(self.business_id) == accepted_at
