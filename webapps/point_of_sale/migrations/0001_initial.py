# Generated by Django 4.0.6 on 2022-08-10 13:51

import uuid

import django.core.serializers.json
import django.db.models.deletion
from django.db import migrations, models

import lib.models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='Basket',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('business_id', models.IntegerField()),
                (
                    'type',
                    models.CharField(
                        choices=[('payment', 'PAYMENT'), ('deposit', 'DEPOSIT')],
                        default='payment',
                        max_length=20,
                    ),
                ),
                ('archived', models.DateTimeField(default=None, null=True)),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='BasketHistory',
            fields=[
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder)),
                ('traceback', models.TextField()),
            ],
            options={
                'ordering': ('-created',),
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BasketItem',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    'type',
                    models.CharField(
                        choices=[
                            ('addon', 'Add-On'),
                            ('service', 'Service'),
                            ('product', 'Product'),
                            ('deposit', 'Deposit'),
                            ('voucher', 'Voucher'),
                            ('travel_fee', 'Travel Fee'),
                        ],
                        default='service',
                        max_length=20,
                    ),
                ),
                ('order', models.PositiveSmallIntegerField(default=0)),
                ('name_line_1', models.CharField(blank=True, max_length=255)),
                ('name_line_2', models.CharField(blank=True, max_length=255)),
                ('quantity', models.PositiveSmallIntegerField(default=1)),
                (
                    'item_price',
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    'discount_rate',
                    models.PositiveSmallIntegerField(
                        default=0, help_text='Row discount rate (only for this row)'
                    ),
                ),
                (
                    'discounted_item_price',
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text='Item price after row discount, but before global discount',
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    'tax_amount',
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    'tax_rate',
                    models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
                ),
                (
                    'tax_type',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('excluded', 'Tax excluded, added to subtotal (US specific)'),
                            ('included', 'Tax included in the product price (EU specific)'),
                        ],
                        default=None,
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    'total',
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text='Total before global discount but with excluded tax added (as displayed to the User)',
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    'net_total',
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text='Total after global discount, but without all taxes (used in sales report)',
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    'gross_total',
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text='Total after global discount, but with all taxes (used in sales report)',
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    'discounted_total',
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text='Total after global discount, but without excluded tax (used in commissions)',
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    'net_total_wo_discount',
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text='What would a net total be if there were no discounts (used in sales report)',
                        max_digits=10,
                        null=True,
                    ),
                ),
            ],
            options={
                'ordering': ('order', 'id'),
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='BasketPayment',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    'payment_method',
                    models.CharField(
                        choices=[
                            ('terminal', 'Terminal'),
                            ('card', 'Card (Mobile Payment)'),
                            ('square', 'Square'),
                            ('cash', 'Cash'),
                            ('check', 'Check'),
                            ('credit_card', 'Credit Card'),
                            ('subscription', 'Subscription Card'),
                            ('store_credit', 'Store Credit'),
                            ('bank_transfer', 'Bank Transfer'),
                            ('american_express', 'American Express'),
                            ('paypal', 'PayPal'),
                            ('egift_card', 'Gift Card'),
                            ('membership', 'Membership'),
                            ('package', 'Package'),
                            ('direct_payment', 'Direct Payment'),
                            ('pba_donations', 'Donations'),
                            ('giftcard', 'Own GiftCard'),
                            ('voucher', 'Voucher'),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    'payment_provider_code',
                    models.CharField(
                        blank=True,
                        choices=[('adyen', 'Adyen'), ('stripe', 'Stripe')],
                        max_length=20,
                        null=True,
                    ),
                ),
                ('balance_transaction_id', models.UUIDField(blank=True, null=True, unique=True)),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('pending', 'Pending'),
                            ('action_required', 'Action required'),
                            ('success', 'Success'),
                            ('failed', 'Failed'),
                            ('canceled', 'Canceled'),
                        ],
                        default='pending',
                        max_length=20,
                    ),
                ),
                ('sender_user_id', models.IntegerField(null=True)),
                ('receiver_user_id', models.IntegerField(null=True)),
                (
                    'type',
                    models.CharField(
                        choices=[
                            ('payment', 'PAYMENT'),
                            ('refund', 'REFUND'),
                            ('dispute', 'CHARGEBACK'),
                            ('chargeback_reversed', 'CHARGEBACK_REVERSED'),
                            ('second_chargeback', 'SECOND_CHARGEBACK'),
                        ],
                        default='payment',
                        max_length=20,
                    ),
                ),
                (
                    'error_code',
                    models.CharField(
                        blank=True,
                        choices=[
                            (
                                'generic_error',
                                'The payment has been declined for an unknown reason.',
                            ),
                            ('bank_refusal', 'The issuing bank did not approve this action'),
                            (
                                'authentication_required',
                                'The card was declined as the transaction requires authentication.',
                            ),
                            (
                                'card_not_supported',
                                'The card does not support this type of purchase.',
                            ),
                            (
                                'currency_not_supported',
                                'The card does not support the specified currency.',
                            ),
                            ('expired_card', 'The card has expired.'),
                            (
                                'fraudulent',
                                'The payment has been declined as we suspect it is fraudulent.',
                            ),
                            ('incorrect_cvc', 'The CVC number is incorrect.'),
                            ('incorrect_number', 'The card number is incorrect.'),
                            ('incorrect_pin', 'The PIN entered is incorrect.'),
                            ('incorrect_zip', 'The ZIP/postal code is incorrect.'),
                            (
                                'insufficient_funds',
                                'The card has insufficient funds to complete the purchase.',
                            ),
                            (
                                'invalid_amount',
                                'The payment amount is invalid, or exceeds the amount that is allowed.',
                            ),
                            (
                                'issuer_not_available',
                                'The card issuer could not be reached, so the payment could not be authorized.',
                            ),
                            (
                                'offline_pin_required',
                                'The card has been declined as it requires a PIN.',
                            ),
                            (
                                'pin_try_exceeded',
                                'The allowable number of PIN tries has been exceeded.',
                            ),
                            (
                                'restricted_card',
                                'The card cannot be used to make this payment (it is possible it has been reported lost or stolen).',
                            ),
                        ],
                        default=None,
                        max_length=100,
                        null=True,
                    ),
                ),
                ('action_required_details', models.JSONField(null=True)),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='BasketTip',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    'rate',
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    'type',
                    models.CharField(
                        choices=[('percent', 'PERCENT'), ('amount', 'AMOUNT')],
                        default='percent',
                        max_length=10,
                    ),
                ),
                (
                    'amount',
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ('staffer_id', models.IntegerField(null=True)),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='CancellationFeeAuth',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('appointment_id', models.IntegerField()),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('pending', 'Pending'),
                            ('success', 'Success'),
                            ('failed', 'Failed'),
                            ('canceled', 'Canceled'),
                        ],
                        default='pending',
                        max_length=20,
                    ),
                ),
                ('balance_transaction_id', models.UUIDField(null=True)),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='PaymentMethodVariant',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    'payment_method_type',
                    models.CharField(
                        choices=[
                            ('terminal', 'Terminal'),
                            ('card', 'Card (Mobile Payment)'),
                            ('square', 'Square'),
                            ('cash', 'Cash'),
                            ('check', 'Check'),
                            ('credit_card', 'Credit Card'),
                            ('subscription', 'Subscription Card'),
                            ('store_credit', 'Store Credit'),
                            ('bank_transfer', 'Bank Transfer'),
                            ('american_express', 'American Express'),
                            ('paypal', 'PayPal'),
                            ('egift_card', 'Gift Card'),
                            ('membership', 'Membership'),
                            ('package', 'Package'),
                            ('direct_payment', 'Direct Payment'),
                            ('pba_donations', 'Donations'),
                            ('giftcard', 'Own GiftCard'),
                            ('voucher', 'Voucher'),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    'payment_provider_code',
                    models.CharField(
                        blank=True,
                        choices=[('adyen', 'Adyen'), ('stripe', 'Stripe')],
                        max_length=20,
                        null=True,
                    ),
                ),
                ('default', models.BooleanField(default=False)),
                ('available', models.BooleanField(default=False)),
                ('enabled', models.BooleanField(default=False)),
            ],
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='POS',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('business_id', models.IntegerField(null=True, unique=True)),
                ('default', models.BooleanField(default=False)),
                ('statement_name', models.CharField(max_length=255)),
            ],
            options={
                'verbose_name_plural': 'POSes',
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='POSPlan',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    'provider_code',
                    models.CharField(
                        choices=[('adyen', 'Adyen'), ('stripe', 'Stripe')], max_length=20
                    ),
                ),
                (
                    'payment_method_type',
                    models.CharField(
                        choices=[
                            ('terminal', 'Terminal'),
                            ('card', 'Card (Mobile Payment)'),
                            ('square', 'Square'),
                            ('cash', 'Cash'),
                            ('check', 'Check'),
                            ('credit_card', 'Credit Card'),
                            ('subscription', 'Subscription Card'),
                            ('store_credit', 'Store Credit'),
                            ('bank_transfer', 'Bank Transfer'),
                            ('american_express', 'American Express'),
                            ('paypal', 'PayPal'),
                            ('egift_card', 'Gift Card'),
                            ('membership', 'Membership'),
                            ('package', 'Package'),
                            ('direct_payment', 'Direct Payment'),
                            ('pba_donations', 'Donations'),
                            ('giftcard', 'Own GiftCard'),
                            ('voucher', 'Voucher'),
                        ],
                        max_length=20,
                    ),
                ),
                ('default', models.BooleanField(default=False)),
                ('payment_provision_percentage', models.FloatField()),
                ('payment_provision_fee', models.IntegerField()),
                ('refund_provision_percentage', models.FloatField()),
                ('refund_provision_fee', models.IntegerField()),
                ('dispute_provision_percentage', models.FloatField()),
                ('dispute_provision_fee', models.IntegerField()),
            ],
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='RelatedBasketItem',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    'type',
                    models.CharField(
                        choices=[
                            ('service_variant', 'SERVICE_VARIANT'),
                            ('commodity', 'COMMODITY'),
                            ('voucher', 'VOUCHER'),
                            ('addon', 'ADDON'),
                            ('appointment', 'APPOINTMENT'),
                            ('subbooking', 'SUBBOOKING'),
                        ],
                        max_length=20,
                    ),
                ),
                ('external_id', models.IntegerField()),
                (
                    'basket_item',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='related_items',
                        to='point_of_sale.basketitem',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.AddConstraint(
            model_name='posplan',
            constraint=models.UniqueConstraint(
                condition=models.Q(('default', True)),
                fields=('provider_code', 'payment_method_type'),
                name='pos_plan_unique_default',
            ),
        ),
        migrations.AddField(
            model_name='pos',
            name='custom_pos_plans',
            field=models.ManyToManyField(related_name='poses', to='point_of_sale.posplan'),
        ),
        migrations.AddField(
            model_name='paymentmethodvariant',
            name='pos',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='payment_method_variants',
                to='point_of_sale.pos',
            ),
        ),
        migrations.AddField(
            model_name='cancellationfeeauth',
            name='basket',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='auths',
                to='point_of_sale.basket',
            ),
        ),
        migrations.AddField(
            model_name='baskettip',
            name='basket',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='tips',
                to='point_of_sale.basket',
            ),
        ),
        migrations.AddField(
            model_name='basketpayment',
            name='basket',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='payments',
                to='point_of_sale.basket',
            ),
        ),
        migrations.AddField(
            model_name='basketpayment',
            name='parent_basket_payment',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to='point_of_sale.basketpayment',
            ),
        ),
        migrations.AddField(
            model_name='basketitem',
            name='basket',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='items',
                to='point_of_sale.basket',
            ),
        ),
        migrations.AddField(
            model_name='baskethistory',
            name='basket',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='history',
                to='point_of_sale.basket',
            ),
        ),
        migrations.AddConstraint(
            model_name='pos',
            constraint=models.UniqueConstraint(
                condition=models.Q(('default', True)),
                fields=('default',),
                name='only_one_default_pos',
            ),
        ),
        migrations.AddConstraint(
            model_name='paymentmethodvariant',
            constraint=models.UniqueConstraint(
                fields=('pos', 'payment_method_type', 'payment_provider_code'),
                name='unique_payment_method_variant',
            ),
        ),
        migrations.AddConstraint(
            model_name='paymentmethodvariant',
            constraint=models.UniqueConstraint(
                condition=models.Q(('default', True)),
                fields=('default', 'pos'),
                name='only_one_default_pmv_per_pos',
            ),
        ),
    ]
