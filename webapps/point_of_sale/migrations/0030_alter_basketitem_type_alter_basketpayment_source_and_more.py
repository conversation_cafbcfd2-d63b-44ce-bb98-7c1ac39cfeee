# Generated by Django 4.2.13 on 2024-07-16 13:00

from django.db import migrations, models
import lib.point_of_sale.enums


class Migration(migrations.Migration):

    dependencies = [
        ("point_of_sale", "0029_alter_basketpayment_error_code"),
    ]

    operations = [
        migrations.AlterField(
            model_name="basketitem",
            name="type",
            field=models.CharField(
                choices=[
                    ("addon", "Add-On"),
                    ("service", "Service"),
                    ("product", "Product"),
                    ("deposit", "Cancellation Fee"),
                    ("voucher", "Voucher"),
                    ("travel_fee", "Travel Fee"),
                ],
                default=lib.point_of_sale.enums.BasketItemType["SERVICE"],
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="basketpayment",
            name="source",
            field=models.CharField(
                choices=[
                    ("prepayment", "Deposit"),
                    ("cancellation_fee", "Cancellation Fee"),
                    ("payment", "Payment"),
                    ("booksy_pay", "Booksy Pay"),
                ],
                default=lib.point_of_sale.enums.BasketPaymentSource["PAYMENT"],
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="basketpaymentanalytics",
            name="trigger",
            field=models.CharField(
                choices=[
                    ("customer__prepayment_auth", "Customer deposit"),
                    ("business__prepayment_request", "Business deposit request"),
                    ("business__prepayment_confirm", "Customer deposit"),
                    ("business__cancellation_fee_charge", "Cancellation Fee Charge"),
                    ("business__mobile_payment_cfp", "Mobile Payment CFP"),
                    ("customer__mobile_payment_confirm", "Mobile Payment Confirm"),
                    (
                        "business__mobile_payment_auto_accept",
                        "Mobile Payment Auto Accept",
                    ),
                    ("customer__donation", "Donation"),
                    ("business__booksy_card_reader_cfp", "Booksy Card Reader CFP"),
                    ("business__offline_payment", "Offline Payment"),
                    ("customer__voucher_online_purchase", "Voucher Online Purchase"),
                    ("business__tap_to_pay", "Tap to Pay"),
                    ("customer__booksy_pay", "Booksy Pay"),
                ],
                max_length=255,
            ),
        ),
        migrations.AlterField(
            model_name="baskettip",
            name="source",
            field=models.CharField(
                blank=True,
                choices=[
                    ("prepayment", "Deposit"),
                    ("checkout", "Checkout"),
                    ("customer_app", "Customer App"),
                    ("bcr", "BCR"),
                    ("booksy_pay", "Booksy Pay"),
                ],
                max_length=30,
                null=True,
            ),
        ),
    ]
