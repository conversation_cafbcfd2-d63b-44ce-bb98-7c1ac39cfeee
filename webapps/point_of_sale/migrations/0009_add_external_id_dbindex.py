# Generated by Django 4.0.7 on 2022-10-10 15:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('point_of_sale', '0008_alter_basket_options_alter_basketitem_options_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='basket',
            name='business_id',
            field=models.IntegerField(db_index=True),
        ),
        migrations.AlterField(
            model_name='basketpayment',
            name='user_id',
            field=models.IntegerField(db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name='baskettip',
            name='staffer_id',
            field=models.IntegerField(db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name='cancellationfeeauth',
            name='appointment_id',
            field=models.IntegerField(db_index=True),
        ),
        migrations.AlterField(
            model_name='cancellationfeeauth',
            name='business_id',
            field=models.IntegerField(blank=True, db_index=True, null=True),
        ),
    ]
