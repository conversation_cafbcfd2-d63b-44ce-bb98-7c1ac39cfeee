# Generated by Django 4.2.18 on 2025-02-28 14:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business_consents", "0023_alter_businessconsent_consent_code"),
    ]

    operations = [
        migrations.AlterField(
            model_name="businessconsent",
            name="consent_code",
            field=models.CharField(
                choices=[
                    ("FRANCE_MIGRATE_TO_STRIPE", "Migrate to Stripe on France"),
                    (
                        "FRANCE_MIGRATE_TO_STRIPE_RETRY",
                        "Migrate to Stripe on France retry",
                    ),
                    (
                        "POLAND_MIGRATE_TO_STRIPE",
                        "Migrate from Adyen to Stripe in Poland",
                    ),
                    (
                        "POLAND_MIGRATE_TO_STRIPE_V2",
                        "Migrate from Adyen to Stripe in Poland (version 2)",
                    ),
                    (
                        "POLAND_MIGRATE_TO_STRIPE_V3",
                        "Migrate from Adyen to Stripe in Poland (version 3 - 27.05)",
                    ),
                    (
                        "POLAND_MIGRATE_TO_STRIPE_V4",
                        "Migrate from Adyen to Stripe in Poland (version 4 - 03.06)",
                    ),
                    (
                        "POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL",
                        "Onboard to Stripe - Individuals (peselowcy)",
                    ),
                    (
                        "POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL_V2",
                        "Onboard to Stripe - Individuals (peselowcy) V2",
                    ),
                    (
                        "POLAND_ONBOARD_TO_STRIPE_JDG",
                        "Onboard to Stripe - JDGs (from CEIDG)",
                    ),
                    (
                        "POLAND_ONBOARD_TO_STRIPE_JDG_V2",
                        "Onboard to Stripe - JDGs (from CEIDG) V2",
                    ),
                    (
                        "POLAND_ONBOARD_TO_STRIPE_KRS",
                        "Onboard to Stripe - Companies (from KRS)",
                    ),
                    (
                        "POLAND_ONBOARD_TO_STRIPE_KRS_V2",
                        "Onboard to Stripe - Companies (from KRS) V2",
                    ),
                    (
                        "STRIPE_ACCOUNT_RESTRICTED_SOON",
                        "Stripe account will be restricted soon",
                    ),
                    ("US_USER_INPUT_KYC", "User Input (Effortless) KYC in US (Stripe)"),
                    (
                        "GB_MIGRATE_TO_STRIPE",
                        "Migrate from Adyen to Stripe in Great Britain",
                    ),
                    (
                        "TEST_POLAND_MIGRATE_TO_STRIPE",
                        "Migrate to Stripe in Poland (TEST)",
                    ),
                    ("SIMPLE_TEST_CONSENT", "Simple test consent"),
                    ("COMPLEX_TEST_CONSENT", "Complex test consent"),
                    ("CUSTOM_TEST_CONSENT", "Custom test consent (webview)"),
                    ("DAC7_PESEL_CUSTOM", "DAC7 PESEL consent"),
                    ("DAC7_PESEL_CUSTOM_FORCE", "DAC7 PESEL consent [force]"),
                    ("DAC7_BIRTHDAY_CUSTOM", "DAC7 birthday consent"),
                    ("DAC7_BIRTHDAY_CUSTOM_FORCE", "DAC7 birthday consent [force]"),
                    ("DAC7_COMPANY_PL_CUSTOM", "DAC7 company PL consent"),
                    ("DAC7_COMPANY_PL_CUSTOM_FORCE", "DAC7 company PL consent [force]"),
                    (
                        "DAC7_COMPANY_PL_CUSTOM_FORCE_V2",
                        "DAC7 company PL consent v2 [force]",
                    ),
                    ("DAC7_COMPANY_ES_CUSTOM", "DAC7 company ES consent"),
                    ("DAC7_COMPANY_ES_CUSTOM_FORCE", "DAC7 company ES consent [force]"),
                    (
                        "DAC7_COMPANY_ES_CUSTOM_FORCE_V2",
                        "DAC7 company ES consent v2 [force]",
                    ),
                    ("DAC7_COMPANY_FR_CUSTOM", "DAC7 company FR consent"),
                    ("DAC7_COMPANY_FR_CUSTOM_FORCE", "DAC7 company FR consent [force]"),
                    (
                        "DAC7_COMPANY_FR_CUSTOM_FORCE_V2",
                        "DAC7 company FR consent v2 [force]",
                    ),
                    ("SEMI_EFFORTLESS_KYC", "Semi Effortless KYC consent"),
                    ("SEMI_EFFORTLESS_KYC_ES", "Semi Effortless KYC consent ES"),
                ],
                max_length=64,
            ),
        ),
    ]
