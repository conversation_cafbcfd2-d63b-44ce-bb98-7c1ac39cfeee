from datetime import date
import re

import stdnum.exceptions as stdnum_exceptions
from dateutil.relativedelta import relativedelta
from django.utils.text import format_lazy
from django.utils.translation import gettext_lazy as _, pgettext_lazy
from stdnum.es.nif import validate as es_nif_validate
from stdnum.fr.nif import validate as fr_nif_validate
from stdnum.fr.nir import validate as fr_nir_validate
from stdnum.fr.siren import validate as fr_siren_validate
from stdnum.pl.pesel import validate as pl_pesel_validate

from rest_framework import serializers

from lib.feature_flag.bug import DAC7PeselValidationFixFlag
from lib.fields.date_field import MinAgeValidator, MinMaxAgeField
from lib.serializers import any_zip_code_validator
from lib.tools import tznow
from webapps.business_consents.enums import (
    ConsentCode,
    ConsentAction,
    ConsentFieldType,
    ConsentIcon,
    USUserInputKYCEntityType,
)
from webapps.business_consents.entities import (
    <PERSON>sentField,
    GenericConsentData,
    CustomConsentData,
)


def siren_validation(value: str) -> str | None:
    """Returns error string or None if the value is correct"""
    if len(value) != 9:
        return _("Siren number should consist of 9 digits")

    if any(not char.isdigit() for char in value):
        return _("Siren number should consist of digits only!")


def nip_validation(value: str) -> str | None:
    """Returns error string or None if the value is correct"""
    if len(value) != 10:
        return _("NIP number should consist of 10 digits")

    if any(not char.isdigit() for char in value):
        return _("NIP number should consist of digits only!")

    digits = [int(i) for i in value]
    weights = (6, 5, 7, 2, 3, 4, 5, 6, 7)
    check_sum = sum(d * w for d, w in zip(digits, weights)) % 11
    if not check_sum == digits[9]:
        return _("This NIP number is incorrect")


def regon_validation(regon: str) -> str | None:
    if not regon.isdigit():
        return _('REGON number should consist of digits only!')

    length = len(regon)
    if length not in (9, 14):
        return _('REGON number should consist of 9 or 14 digits')

    if length == 9:
        weights = (8, 9, 2, 3, 4, 5, 6, 7)
    else:
        weights = (2, 4, 8, 5, 0, 9, 7, 3, 6, 1, 2, 4, 8)
    digits = [int(i) for i in regon]
    sum_control = sum(digits[i] * weights[i] for i in range(len(weights)))
    if sum_control % 11 % 10 != digits[-1]:
        return _('This REGON number is incorrect')


def pesel_validation(pesel: str) -> str | None:
    """Returns string on error, otherwise None"""
    if DAC7PeselValidationFixFlag():
        _validate(pl_pesel_validate, pesel, 'PESEL')
    else:
        if len(pesel) != 11:
            return _('PESEL number should consist of 11 digits')

        if not pesel.isdigit():
            return _('PESEL number should consist of digits only!')

        digits = [int(i) for i in pesel]
        weights = (1, 3, 7, 9, 1, 3, 7, 9, 1, 3)
        sum_control = sum(digits[i] * weights[i] for i in range(10))
        if sum_control % 10 != 10 - digits[10]:
            return _('This PESEL number is incorrect')

        if not _validate_adult_age(pesel):
            return _('You must be at least %(limit_value)s years old.') % {'limit_value': 18}


def _get_birth_date(pesel: str) -> date | None:
    year = int(pesel[0:2])
    month = int(pesel[2:4])
    day = int(pesel[4:6])
    if month > 20:
        month %= 20
        year += 2000
    else:
        year += 1900

    try:
        return date(year, month, day)
    except ValueError:
        return None


def _validate_adult_age(pesel: str) -> bool:
    """Check if birth date is between 1900 and current date - 18 years"""
    today = tznow().date()
    if birth_date := _get_birth_date(pesel):
        return birth_date <= today - relativedelta(years=18)


def pesel_validator(pesel: str):
    if DAC7PeselValidationFixFlag():
        _validate(pl_pesel_validate, pesel, 'PESEL')
    else:
        if error_msg := pesel_validation(pesel):
            raise serializers.ValidationError(error_msg)


def regon_validator(regon: str):
    regon = regon.strip().replace('-', '')
    if error_msg := regon_validation(regon):
        raise serializers.ValidationError(error_msg)


def nip_or_pesel_validator(nip_or_pesel: str):
    nip_or_pesel = nip_or_pesel.strip().replace('-', '')
    is_pesel = len(nip_or_pesel) == 11
    validator = pesel_validation if is_pesel else nip_validation
    if error_msg := validator(nip_or_pesel):
        raise serializers.ValidationError(error_msg)


def _validate(validator: callable, number: str, number_name: str) -> str:
    try:
        return validator(number)
    except stdnum_exceptions.InvalidLength:
        error_msg = _('The %(number_name)s number has an invalid length.')
    except stdnum_exceptions.InvalidFormat:
        error_msg = _('The %(number_name)s number has an invalid format.')
    except (stdnum_exceptions.InvalidChecksum, stdnum_exceptions.InvalidComponent):
        error_msg = _('The %(number_name)s number is incorrect.')

    raise serializers.ValidationError(error_msg % {'number_name': number_name})


def nif_validator(nif_or_dni: str):
    # NIF validation includes DNI, NIE and CIF validation
    return _validate(es_nif_validate, nif_or_dni, 'NIF')


def nif_or_nir_or_siren_validator(nif_or_nir_or_siren: str):
    try:
        return fr_siren_validate(nif_or_nir_or_siren)
    except stdnum_exceptions.ValidationError:
        pass
    try:
        return fr_nir_validate(nif_or_nir_or_siren)
    except stdnum_exceptions.ValidationError:
        pass
    try:
        return fr_nif_validate(nif_or_nir_or_siren)
    except stdnum_exceptions.ValidationError:
        pass
    raise serializers.ValidationError(
        _('The %(number_name)s number is incorrect.') % {'number_name': 'NIF/NIR/SIREN'}
    )


def siren_validator(siren: str):
    return _validate(fr_siren_validate, siren, 'SIREN')


FRANCE_MIGRATION_RULES_URL = (
    "https://information-obligation.booksy.com/fr-information-obligation.pdf"
)


class CustomTestConsentPayloadSerializer(serializers.Serializer):
    date_of_birth = serializers.DateField()


class IndividualConsentPayloadSerializer(serializers.Serializer):
    date_of_birth = serializers.DateField()
    first_name = serializers.CharField(min_length=1, max_length=255)
    last_name = serializers.CharField(min_length=1, max_length=255)


class JDGConsentPayloadSerializer(serializers.Serializer):
    date_of_birth = serializers.DateField()
    nip = serializers.CharField(min_length=10, max_length=10)


class CompanyConsentPayloadSerializer(serializers.Serializer):
    # Sp.zoo etc
    nip = serializers.CharField(min_length=10, max_length=10)


class USUserInputKYCPayloadSerializer(serializers.Serializer):
    type = serializers.ChoiceField(required=True, choices=USUserInputKYCEntityType.choices())
    first_name = serializers.CharField(required=False, min_length=1, max_length=255)
    last_name = serializers.CharField(required=False, min_length=1, max_length=255)
    company_name = serializers.CharField(required=False, min_length=1, max_length=100)

    def validate(self, attrs):
        super().validate(attrs)
        if attrs['type'] == USUserInputKYCEntityType.INDIVIDUAL:
            if 'first_name' not in attrs or 'last_name' not in attrs:
                raise serializers.ValidationError('Missing fields')
        elif attrs['type'] == USUserInputKYCEntityType.COMPANY:
            if 'company_name' not in attrs:
                raise serializers.ValidationError('Missing fields')

        return attrs


class AdultAgeRequiredField(serializers.DateField):
    validators = [MinAgeValidator(years=18)]


class Dac7PeselPayloadSerializer(serializers.Serializer):
    pesel = serializers.CharField(required=False, validators=[pesel_validator])


class BaseCompanyPayloadSerializer(serializers.Serializer):
    name = serializers.CharField(required=True, min_length=3, max_length=250)
    address = serializers.CharField(required=True, min_length=3, max_length=250)
    zip_code = serializers.CharField(validators=[any_zip_code_validator])
    city = serializers.CharField(required=True, min_length=3, max_length=250)
    country = serializers.CharField(required=True, min_length=3, max_length=250)
    country_tax_residence = serializers.CharField(required=True, min_length=3, max_length=250)
    birthday = AdultAgeRequiredField()


class Dac7CompanyPlPayloadSerializer(BaseCompanyPayloadSerializer):
    nip_or_pesel = serializers.CharField(required=True, validators=[nip_or_pesel_validator])
    regon = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        validators=[regon_validator],
    )

    def validate(self, attrs):
        attrs = super().validate(attrs)
        self._validate_match_birth_dates(attrs.get('birthday'), attrs['nip_or_pesel'])
        return attrs

    @staticmethod
    def _validate_match_birth_dates(birthday, nip_or_pesel):
        is_pesel = len(nip_or_pesel) == 11
        if is_pesel and birthday and birthday != _get_birth_date(nip_or_pesel):
            raise serializers.ValidationError(
                {'birthday': _('The PESEL and date of birth do not match.')}
            )


class Dac7CompanyExtraPayloadSerializer(serializers.Serializer):
    business_type = serializers.ChoiceField(choices=['individual', 'entity'])
    first_name = serializers.CharField(required=True, min_length=2, max_length=250)
    last_name = serializers.CharField(required=True, min_length=2, max_length=250)
    name = serializers.CharField(required=True, min_length=3, max_length=250)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if 'business_type' not in kwargs['data']:
            return

        is_company = kwargs['data']['business_type'] == 'entity'
        not_required_fields = ['first_name', 'last_name'] if is_company else ['name']
        for field_name in not_required_fields:
            field = self.fields[field_name]
            field.validators = []
            field.required = False
            field.allow_null = True
            field.allow_blank = True


class Dac7CompanyPlV2PayloadSerializer(
    Dac7CompanyExtraPayloadSerializer,
    Dac7CompanyPlPayloadSerializer,
    serializers.Serializer,
):
    pass


class Dac7CompanyEsPayloadSerializer(BaseCompanyPayloadSerializer):
    nif_or_dni = serializers.CharField(required=True, validators=[nif_validator])
    nif_iva = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        validators=[nif_validator],
    )


class Dac7CompanyESV2PayloadSerializer(
    Dac7CompanyExtraPayloadSerializer,
    Dac7CompanyEsPayloadSerializer,
    serializers.Serializer,
):
    pass


class Dac7CompanyFrPayloadSerializer(BaseCompanyPayloadSerializer):
    business_type = serializers.CharField(required=False)
    nif_or_nir_or_siren = serializers.CharField(
        required=True, validators=[nif_or_nir_or_siren_validator]
    )


class Dac7CompanyFrV2PayloadSerializer(
    Dac7CompanyExtraPayloadSerializer,
    Dac7CompanyFrPayloadSerializer,
    serializers.Serializer,
):
    pass


class Dac7BirthdayPayloadSerializer(serializers.Serializer):
    birthday = AdultAgeRequiredField()


class Dac7IEBaseSerializer(serializers.Serializer):
    type_of_business = serializers.ChoiceField(choices=['company', 'individual'])
    address = serializers.CharField(min_length=3, max_length=250)
    city = serializers.CharField(min_length=3, max_length=250)
    eri_code = serializers.RegexField(
        regex=re.compile(r'(?:^[AC-FHKNPRTV-Y][0-9]{2}|D6W)[ -]?[0-9AC-FHKNPRTV-Y]{4}$')
    )


class Dac7IECompanySerializer(Dac7IEBaseSerializer):
    company_name = serializers.CharField(min_length=3, max_length=250)
    crn = serializers.RegexField(regex=re.compile(r'^[a-zA-Z0-9]{6,8}$'))


class Dac7IEIndividualSerializer(Dac7IEBaseSerializer):
    first_name = serializers.CharField(min_length=2, max_length=250)
    last_name = serializers.CharField(min_length=2, max_length=250)
    pps = serializers.RegexField(regex=re.compile(r'^[0-9]{7}[a-zA-Z]{1,2}$'))
    date_of_birth = MinMaxAgeField(min_years=18, max_years=100)


class Dac7IESerializer(serializers.Serializer):
    type_of_business = serializers.ChoiceField(choices=['company', 'individual'])

    def to_internal_value(self, data):
        type_of_business = data.get('type_of_business')

        if not type_of_business:
            raise serializers.ValidationError({'type_of_business': 'This field is required.'})

        match type_of_business:
            case 'company':
                serializer = Dac7IECompanySerializer
            case 'individual':
                serializer = Dac7IEIndividualSerializer
            case _:
                raise serializers.ValidationError(
                    {'type_of_business': "This field has to be one of ['company', 'individual']."}
                )

        serializer = serializer(data=data, context=self.context)
        serializer.is_valid(raise_exception=True)

        return serializer.validated_data


CONSENT_DATA_MAP = {
    ConsentCode.FRANCE_MIGRATE_TO_STRIPE: GenericConsentData(
        consent_code=ConsentCode.FRANCE_MIGRATE_TO_STRIPE,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        title=_("Payment processor update"),
        info_text=format_lazy(
            _(
                """We are changing our Payment Processor to Stripe - a leader in payment solutions.
This will allow Booksy to:<br>
<br>
<strong>• Offer next business day payouts</strong><br>
<strong>• Improve your overall experience</strong><br>
<strong>• Simplify how you access Payments within the app</strong><br>
<br>
To make this transition as seamless as possible for you, 
we're here to assist you with the data transfer process. 
Therefore, by providing your SIREN number and clicking <strong>“Agree”</strong>, you:<br>
<br>
• authorize us to verify your SIREN number in the official registry 
(<a target="_blank" href="https://www.pappers.fr"><strong>https://www.pappers.fr</strong></a>), 
access your disclosed information, and share it with Stripe for the Know Your Customer process
(Booksy's data processing rules are available 
<a target="_blank" href="{processing_url}"><strong>here</strong></a>);<br>
• authorize us to create a Stripe account for you;<br>
• accept Stripe's Terms of Service.<br>
<br>
Failure to complete this process by 31.12.2023 will result in the loss 
of your access to Mobile Payments and No-Show Protection."""
            ),
            processing_url=FRANCE_MIGRATION_RULES_URL,
        ),
        fields=[
            ConsentField(
                code='siren',
                text=_('Siren number'),
                error_msg=_('The SIREN number is incorrect.'),
                required=True,
                type=ConsentFieldType.STRING,
                custom_validation_func=siren_validation,
            )
        ],
        disclaimer=_(
            'By clicking “Agree” you accept '
            '<strong><a target="_blank" href="https://stripe.com/fr/legal/ssa">'
            'Stripe Terms and Conditions.</a></strong>'
        ),
        disclaimer_markdown=_(
            'By clicking "Agree" you accept '
            '<strong>[Stripe Terms and Conditions.](https://stripe.com/fr/legal/ssa)</strong>'
        ),
        success_title=_("Processing data"),
        success_text=_(
            "Your data will be transferred to our payment processor, Stripe. "
            "Once the transition is complete, you will be notified via email."
            "<br><br>In the meantime, keep doing what you do best."
        ),
        success_icon=ConsentIcon.INFO,
        is_custom=False,
    ),
    ConsentCode.FRANCE_MIGRATE_TO_STRIPE_RETRY: GenericConsentData(
        consent_code=ConsentCode.FRANCE_MIGRATE_TO_STRIPE_RETRY,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        title=_("Payment processor update"),
        info_text=format_lazy(
            _(
                """<strong>The previously provided SIREN number is incorrect.</strong><br>
<strong>Please re-enter it carefully. This is necessary to complete the data 
transfer process to Stripe, our new payment processor.</strong><br>
<br>
To make this transition as seamless as possible for you, 
we're here to assist you with the data transfer process. 
Therefore, by providing your SIREN number and clicking <strong>“Agree”</strong>, you:<br>
<br>
• authorize us to verify your SIREN number in the official registry 
(<a target="_blank" href="https://www.pappers.fr"><strong>https://www.pappers.fr</strong></a>), 
access your disclosed information, and share it with Stripe for the Know Your Customer process
(Booksy's data processing rules are available 
<a target="_blank" href="{processing_url}"><strong>here</strong></a>);<br>
• authorize us to create a Stripe account for you;<br>
• accept Stripe's Terms of Service.<br>
<br>
Failure to complete this process by 31.12.2023 will result in the loss 
of your access to Mobile Payments and No-Show Protection."""
            ),
            processing_url=FRANCE_MIGRATION_RULES_URL,
        ),
        fields=[
            ConsentField(
                code='siren',
                text=_('Siren number'),
                error_msg=_('The SIREN number is incorrect.'),
                required=True,
                type=ConsentFieldType.STRING,
                custom_validation_func=siren_validation,
            )
        ],
        disclaimer=_(
            'By clicking “Agree” you accept '
            '<strong><a target="_blank" href="https://stripe.com/fr/legal/ssa">'
            'Stripe Terms and Conditions.</a></strong>'
        ),
        disclaimer_markdown=_(
            'By clicking "Agree" you accept '
            '<strong>[Stripe Terms and Conditions.](https://stripe.com/fr/legal/ssa)</strong>'
        ),
        success_title=_("Processing data"),
        success_text=_(
            "Your data will be transferred to our payment processor, Stripe. "
            "Once the transition is complete, you will be notified via email."
            "<br><br>In the meantime, keep doing what you do best."
        ),
        success_icon=ConsentIcon.INFO,
        is_custom=False,
    ),
    ConsentCode.TEST_POLAND_MIGRATE_TO_STRIPE: GenericConsentData(
        # TODO DISCLAIMER: this is a dummy consent, should be converted into a proper one later
        consent_code=ConsentCode.TEST_POLAND_MIGRATE_TO_STRIPE,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        title=_("Zmiana procesora płatności"),
        info_text=format_lazy(
            _(
                """Zmieniamy naszego dostawcę usług płatniczych na Stripe - 
                lidera rozwiązań płatniczych.
Pozwoli to Booksy na:<br>
<br>
<strong>• Oferowanie wypłat na następny dzień roboczy</strong><br>
<strong>• Poprawę ogólnego doświadczenia</strong><br>
<strong>• Uproszczenie sposobu dostępu do Płatności w aplikacji</strong><br>
<br>
Aby przejście to było dla Ciebie jak najmniej problematyczne,
jesteśmy tutaj, aby pomóc Ci w procesie transferu danych.
Dlatego, podając swój numer NIP i klikając <strong>„Zgadzam się”</strong>:<br>
<br>
• upoważniasz nas do weryfikacji Twojego numeru NIP w oficjalnym rejestrze
(<a target="_blank" href="https://ceidg.gov.pl"><strong>https://ceidg.gov.pl</strong></a>),
dostępu do Twoich ujawnionych informacji oraz ich udostępniania Stripe w procesie KYC
(reguły przetwarzania danych Booksy są dostępne
<a target="_blank" href="{processing_url}"><strong>tutaj</strong></a>);<br>
• upoważniasz nas do utworzenia dla Ciebie konta Stripe;<br>
• akceptujesz Warunki Świadczenia Usług Stripe.<br>
<br>
Nieukończenie tego procesu do 31.XX.2024 spowoduje utratę dostępu do 
Płatności Mobilnych oraz No-Show Protection."""
            ),
            processing_url=FRANCE_MIGRATION_RULES_URL,
        ),
        fields=[
            ConsentField(
                code='nip',
                text=_('Numer NIP'),
                error_msg=_('Numer NIP jest niepoprawny'),
                required=True,
                type=ConsentFieldType.STRING,
                custom_validation_func=nip_validation,
            )
        ],
        disclaimer=_(
            'Klikając „Zgadzam się”, akceptujesz '
            '<strong><a target="_blank" href="https://stripe.com/fr/legal/ssa">'
            'Warunki świadczenia usług Stripe.</a></strong>'
        ),
        disclaimer_markdown=_(
            'Klikając „Zgadzam się”, akceptujesz '
            '<strong>[Warunki świadczenia usług Stripe.](https://stripe.com/fr/legal/ssa)</strong>'
        ),
        success_title=_("Procesowanie"),
        success_text=_(
            "Twoje dane zostaną przesłane do procesora płatności Stripe. "
            "Gdy ten proces się zakończy, zostaniesz powiadomiony wiadomością email."
            "<br><br>W międzyczasie, kontynuuj robienie tego, w czym jesteś najlepszy."
        ),
        success_icon=ConsentIcon.INFO,
        is_custom=False,
    ),
    # below are test consents, not used in production, thus they don't need to be translated
    ConsentCode.SIMPLE_TEST_CONSENT: GenericConsentData(
        consent_code=ConsentCode.SIMPLE_TEST_CONSENT,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        title="Simple test consent",
        info_text="""Nothing too fancy here""",
        fields=[],
        disclaimer='By clicking “Agree” you accept TOS',
        disclaimer_markdown='By clicking “Agree” you accept TOS',
        success_title="Success",
        success_text="Thank you! Info icon below",
        success_icon=ConsentIcon.INFO,
        is_custom=False,
    ),
    ConsentCode.COMPLEX_TEST_CONSENT: GenericConsentData(
        consent_code=ConsentCode.COMPLEX_TEST_CONSENT,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.DISAGREE, ConsentAction.ASK_ME_LATER],
        title="Complex test consent",
        info_text="""Very complex consent. <br><br><br><br><br>
        <li>
        <ul>Element 1</ul>
        <ul>Element 2</ul>
        <ul>Element 3</ul>
        <br/>
        This is normal text and <b>this is bold</b> and <strong>this is strong</strong>. 
        </li>
        """,
        fields=[
            ConsentField(
                code='street',
                text='Street',
                error_msg='This street does not exist',
                required=True,
                type=ConsentFieldType.STRING,
            ),
            ConsentField(
                code='marketing1',
                text='I want email spam (not required)',
                error_msg='Invalid',
                required=False,
                type=ConsentFieldType.BOOLEAN,
            ),
            ConsentField(
                code='city',
                text='City',
                error_msg='This city is incorrect.',
                required=True,
                type=ConsentFieldType.STRING,
            ),
            ConsentField(
                code='marketing2',
                text='I want email spam (required)',
                error_msg='Invalid',
                required=False,
                type=ConsentFieldType.BOOLEAN,
            ),
        ],
        disclaimer='By clicking “Agree” you accept <a href="https://booksy.com/">TOS</a>',
        disclaimer_markdown='By clicking "Agree" you accept [TOS](https://booksy.com/)',
        success_title="Success",
        success_text="Thank you! Warning icon below",
        success_icon=ConsentIcon.WARNING,
        is_custom=False,
    ),
    ConsentCode.POLAND_MIGRATE_TO_STRIPE: GenericConsentData(
        consent_code=ConsentCode.POLAND_MIGRATE_TO_STRIPE,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        title=_("Important information"),
        info_text=_(
            """<strong>We are changing our Payment Processor to Stripe - a global leader 
in payment solutions.</strong><br><br>
In order to provide you with access to Mobile Payments and No-Show Protection, 
we require your consent to transfer your data to Stripe. 
This will grant your clients access to the latest payment solutions.<br><br>
Just one click and we'll securely transfer your data!<br><br>
By clicking “Agree” you accept the 
<a target="_blank" href="https://stripe.com/en-pl/legal/connect-account">
<strong>Stripe Terms and Conditions.</strong></a><br><br>"""
        ),
        fields=[],
        disclaimer='',
        disclaimer_markdown='',
        success_title=_('Thank you!'),
        success_text=_(
            'Your data will soon be transferred to our new '
            'payment processor, Stripe. The data transfer process may '
            'take up to 4 weeks. Once the transition is complete, '
            'you will be notified via email.'
        ),
        success_icon=ConsentIcon.SUCCESS,
        is_custom=False,
    ),
    ConsentCode.CUSTOM_TEST_CONSENT: CustomConsentData(
        consent_code=ConsentCode.CUSTOM_TEST_CONSENT,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=CustomTestConsentPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.POLAND_MIGRATE_TO_STRIPE_V2: GenericConsentData(
        consent_code=ConsentCode.POLAND_MIGRATE_TO_STRIPE_V2,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        title=_("Important information! Make sure you don't lose access to mobile payments!"),
        info_text=_(
            """<strong>We are changing our Payment Processor to Stripe - a global leader 
in payment solutions.</strong><br><br>
In order to provide you with access to Mobile Payments and No-Show Protection, we require 
your consent to transfer your data to Stripe. If we don't receive your consent by 
<strong>May 13, 2024</strong> - your Mobile Payments and No-show Protection 
will be disabled.<br><br><strong>Switching the provider will not result in any changes 
in fees.</strong> Just one click and we'll securely transfer your data!<br><br>
By clicking <strong>“Agree”</strong> you accept the 
<a target="_blank" href="https://stripe.com/en-pl/legal/connect-account">
<strong>Stripe Terms and Conditions.</strong></a><br><br>"""
        ),
        fields=[],
        disclaimer='',
        disclaimer_markdown='',
        success_title=_('Thank you!'),
        success_text=_(
            'Your data will soon be transferred to our new payment processor, Stripe. '
            'Once the transition is complete, you will be notified via email.'
        ),
        success_icon=ConsentIcon.SUCCESS,
        is_custom=False,
    ),
    ConsentCode.POLAND_MIGRATE_TO_STRIPE_V3: GenericConsentData(
        consent_code=ConsentCode.POLAND_MIGRATE_TO_STRIPE_V3,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        title=pgettext_lazy(
            'consent-27-may',
            "Important information! Make sure you don't lose access to mobile payments!",
        ),
        info_text=_(
            """<strong>We are changing our Payment Processor to Stripe - a global leader 
in payment solutions.</strong><br><br>
In order to provide you with access to Mobile Payments and No-Show Protection, we require 
your consent to transfer your data to Stripe. If we don't receive your consent by 
<strong>May 27, 2024</strong> - your Mobile Payments and No-show Protection 
will be disabled.<br><br><strong>Switching the provider will not result in any changes 
in fees.</strong> Just one click and we'll securely transfer your data!<br><br>
By clicking <strong>“Agree”</strong> you accept the 
<a target="_blank" href="https://stripe.com/en-pl/legal/connect-account">
<strong>Stripe Terms and Conditions.</strong></a><br><br>"""
        ),
        fields=[],
        disclaimer='',
        disclaimer_markdown='',
        success_title=_('Thank you!'),
        success_text=_(
            'Your data will soon be transferred to our new payment processor, Stripe. '
            'Once the transition is complete, you will be notified via email.'
        ),
        success_icon=ConsentIcon.SUCCESS,
        is_custom=False,
    ),
    ConsentCode.POLAND_MIGRATE_TO_STRIPE_V4: GenericConsentData(
        consent_code=ConsentCode.POLAND_MIGRATE_TO_STRIPE_V4,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        title=pgettext_lazy(
            'consent-03-june',
            "Important information! Make sure you don't lose access to mobile payments!",
        ),
        info_text=_(
            """<strong>We are changing our Payment Processor to Stripe - a global leader 
in payment solutions.</strong><br><br>
In order to provide you with access to Mobile Payments and No-Show Protection, we require 
your consent to transfer your data to Stripe. If we don't receive your consent by 
<strong>June 3, 2024</strong> - your Mobile Payments and No-show Protection 
will be disabled.<br><br><strong>Switching the provider will not result in any changes 
in fees.</strong> Just one click and we'll securely transfer your data!<br><br>
By clicking <strong>“Agree”</strong> you accept the 
<a target="_blank" href="https://stripe.com/en-pl/legal/connect-account">
<strong>Stripe Terms and Conditions.</strong></a><br><br>"""
        ),
        fields=[],
        disclaimer='',
        disclaimer_markdown='',
        success_title=_('Thank you!'),
        success_text=_(
            'Your data will soon be transferred to our new payment processor, Stripe. '
            'Once the transition is complete, you will be notified via email.'
        ),
        success_icon=ConsentIcon.SUCCESS,
        is_custom=False,
    ),
    ConsentCode.POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL: CustomConsentData(
        consent_code=ConsentCode.POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=IndividualConsentPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.POLAND_ONBOARD_TO_STRIPE_JDG: CustomConsentData(
        consent_code=ConsentCode.POLAND_ONBOARD_TO_STRIPE_JDG,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=JDGConsentPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.POLAND_ONBOARD_TO_STRIPE_KRS: CustomConsentData(
        consent_code=ConsentCode.POLAND_ONBOARD_TO_STRIPE_KRS,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=CompanyConsentPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL_V2: CustomConsentData(
        consent_code=ConsentCode.POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL_V2,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=IndividualConsentPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.POLAND_ONBOARD_TO_STRIPE_JDG_V2: CustomConsentData(
        consent_code=ConsentCode.POLAND_ONBOARD_TO_STRIPE_JDG_V2,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=JDGConsentPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.POLAND_ONBOARD_TO_STRIPE_KRS_V2: CustomConsentData(
        consent_code=ConsentCode.POLAND_ONBOARD_TO_STRIPE_KRS_V2,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=CompanyConsentPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.STRIPE_ACCOUNT_RESTRICTED_SOON: CustomConsentData(
        consent_code=ConsentCode.STRIPE_ACCOUNT_RESTRICTED_SOON,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=None,
        is_custom=True,
    ),
    ConsentCode.US_USER_INPUT_KYC: CustomConsentData(
        consent_code=ConsentCode.US_USER_INPUT_KYC,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=USUserInputKYCPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.GB_MIGRATE_TO_STRIPE: GenericConsentData(
        consent_code=ConsentCode.GB_MIGRATE_TO_STRIPE,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        title=_("Don't lose access to Booksy Payments!"),
        info_text=_(
            """<strong>We are changing our Payment Processor to Stripe - a global leader 
in payment solutions.</strong><br><br>
To secure your access to all Booksy Payments features including
Mobile Payments and No-Show Protection, we require your consent
to transfer your data to Stripe. If we don't receive your consent by 
<strong>March 16th, 2025</strong>, you will no longer be able
to use Booksy Payments.<br><br>
Once you provide your consent, we'll securely transfer all your data,
<strong>except your bank account details for payouts, which you will need
to add when prompted to do so.</strong><br><br>
This will not result in any changes in Booksy fees.<br><br>
By clicking <strong>“Agree”</strong> you accept the 
<a target="_blank" href="https://stripe.com/gb/legal/connect-account">
<strong>Stripe Terms and Conditions</strong></a>
and an account will then be created for you on the Stripe platform.<br><br>
Your data will be processed as described in <strong>Stripe's Privacy Policy</strong>
and <a target="_blank" href="https://biz.booksy.com/en-gb/privacy-policy#privacy-policy?">
<strong>Booksy's Privacy Policy.</strong></a><br><br>"""
        ),
        fields=[],
        disclaimer='',
        disclaimer_markdown='',
        success_title=_('Thank you!'),
        success_text=_(
            'Your data will soon be transferred to our new '
            'payment processor, Stripe. The data transfer process may '
            'take up to 4 weeks. Once the transition is complete, '
            'you will be notified via email.'
        ),
        success_icon=ConsentIcon.SUCCESS,
        is_custom=False,
    ),
    ConsentCode.DAC7_PESEL: CustomConsentData(
        consent_code=ConsentCode.DAC7_PESEL,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=Dac7PeselPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.DAC7_COMPANY_PL: CustomConsentData(
        consent_code=ConsentCode.DAC7_COMPANY_PL,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=Dac7CompanyPlPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.DAC7_BIRTHDAY: CustomConsentData(
        consent_code=ConsentCode.DAC7_BIRTHDAY,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=Dac7BirthdayPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.DAC7_COMPANY_ES: CustomConsentData(
        consent_code=ConsentCode.DAC7_COMPANY_ES,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=Dac7CompanyEsPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.DAC7_COMPANY_FR: CustomConsentData(
        consent_code=ConsentCode.DAC7_COMPANY_FR,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=Dac7CompanyFrPayloadSerializer,
        is_custom=True,
    ),
    # the same as above, but without "ask me later"
    ConsentCode.DAC7_PESEL_FORCE: CustomConsentData(
        consent_code=ConsentCode.DAC7_PESEL_FORCE,
        allowed_actions=[ConsentAction.AGREE],
        payload_serializer=Dac7PeselPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.DAC7_COMPANY_PL_FORCE: CustomConsentData(
        consent_code=ConsentCode.DAC7_COMPANY_PL_FORCE,
        allowed_actions=[ConsentAction.AGREE],
        payload_serializer=Dac7CompanyPlPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.DAC7_COMPANY_PL_FORCE_V2: CustomConsentData(
        consent_code=ConsentCode.DAC7_COMPANY_PL_FORCE_V2,
        allowed_actions=[ConsentAction.AGREE],
        payload_serializer=Dac7CompanyPlV2PayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.DAC7_BIRTHDAY_FORCE: CustomConsentData(
        consent_code=ConsentCode.DAC7_BIRTHDAY_FORCE,
        allowed_actions=[ConsentAction.AGREE],
        payload_serializer=Dac7BirthdayPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.DAC7_COMPANY_ES_FORCE: CustomConsentData(
        consent_code=ConsentCode.DAC7_COMPANY_ES_FORCE,
        allowed_actions=[ConsentAction.AGREE],
        payload_serializer=Dac7CompanyEsPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.DAC7_COMPANY_ES_FORCE_V2: CustomConsentData(
        consent_code=ConsentCode.DAC7_COMPANY_ES_FORCE_V2,
        allowed_actions=[ConsentAction.AGREE],
        payload_serializer=Dac7CompanyESV2PayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.DAC7_COMPANY_FR_FORCE: CustomConsentData(
        consent_code=ConsentCode.DAC7_COMPANY_FR_FORCE,
        allowed_actions=[ConsentAction.AGREE],
        payload_serializer=Dac7CompanyFrPayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.DAC7_COMPANY_FR_FORCE_V2: CustomConsentData(
        consent_code=ConsentCode.DAC7_COMPANY_FR_FORCE_V2,
        allowed_actions=[ConsentAction.AGREE],
        payload_serializer=Dac7CompanyFrV2PayloadSerializer,
        is_custom=True,
    ),
    ConsentCode.SEMI_EFFORTLESS_KYC: CustomConsentData(
        consent_code=ConsentCode.SEMI_EFFORTLESS_KYC,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=None,
        is_custom=True,
    ),
    ConsentCode.SEMI_EFFORTLESS_KYC_ES: CustomConsentData(
        consent_code=ConsentCode.SEMI_EFFORTLESS_KYC_ES,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=None,
        is_custom=True,
    ),
    ConsentCode.DAC7_IE: CustomConsentData(
        consent_code=ConsentCode.DAC7_IE,
        allowed_actions=[ConsentAction.AGREE, ConsentAction.ASK_ME_LATER],
        payload_serializer=Dac7IESerializer,
        is_custom=True,
    ),
}
