# Generated by Django 2.2.13 on 2020-10-21 12:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0043_new_agreements'),
        ('booking', '0105_auto_20201009_0638'),
        ('segment', '0008_zipcodestourbanareasmapper_zip_partial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppUsedByUserByDate',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created',
                    models.DateTimeField(
                        auto_now_add=True,
                        verbose_name='Created (UTC)',
                    ),
                ),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True,
                        db_index=True,
                        verbose_name='Updated (UTC)',
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        verbose_name='Deleted (UTC)',
                    ),
                ),
                ('date_used', models.DateField()),
                (
                    'booking_source',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='booking.BookingSources',
                    ),
                ),
                (
                    'user',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='app_used',
                        to='user.User',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
    ]
