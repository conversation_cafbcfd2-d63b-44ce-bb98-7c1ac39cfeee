from django.db import transaction
from rest_framework import serializers
from webapps.metrics.models import BusinessApplication, ApplicationDefinition


class ApplicationDefinitionSerializer(serializers.ModelSerializer):

    code = serializers.CharField()

    class Meta:
        model = ApplicationDefinition
        fields = ('code',)

    def validate(self, attrs):
        return ApplicationDefinition.objects.filter(code__in=attrs)

    def to_internal_value(self, data):
        return data


class BusinessApplicationsSerializer(serializers.ModelSerializer):

    business_id = serializers.IntegerField(allow_null=False, min_value=1)
    items = ApplicationDefinitionSerializer(source='applications')

    class Meta:
        model = BusinessApplication
        fields = ('business_id', 'fingerprint', 'items')

    @transaction.atomic
    def create(self, validated_data):
        if not self.validated_data['applications']:
            return None

        business_application = BusinessApplication.objects.create(
            business_id=self.validated_data['business_id'],
            fingerprint=self.validated_data['fingerprint'],
        )
        business_application.applications.set(
            self.validated_data['applications'],
            clear=True,
        )
        return business_application
