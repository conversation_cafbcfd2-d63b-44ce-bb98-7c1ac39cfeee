# Generated by Django 4.1.7 on 2023-06-16 14:21

from django.db import migrations, models
import lib.models


class Migration(migrations.Migration):
    dependencies = [
        ('versum_migration', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='VersumInitialSetPasswordAttempt',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('ip_addr', models.GenericIPAddressField(blank=True, null=True)),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
    ]
