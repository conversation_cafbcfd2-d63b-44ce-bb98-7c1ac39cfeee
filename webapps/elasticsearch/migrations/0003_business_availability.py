# Generated by Django 1.10.5 on 2017-02-09 15:46
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('elasticsearch', '0002_denormalize_service_id'),
        ('admin_extra', '0017_rm_boundary_2'),
        ('business', '0077_merge_20170208_0931'),
        ('booking', '0023_auto_20161024_1347'),
        ('pos', '0083_auto_20170127_1303'),
        ('user', '0014_auto_20160921_1217'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessAvailability',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('business_id', models.IntegerField()),
            ],
        ),
    ]
