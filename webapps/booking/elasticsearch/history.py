import elasticsearch_dsl as dsl
from django.conf import settings

from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.document import ILMDocument
from lib.elasticsearch.index import LifecycleManagementIndex
from lib.es_history.document import HistoryChangesMeta, HistoryDocumentMixin, RawField
from webapps.booking.serializers.history import AppointmentHistorySerializer


class SubBookingChangesDoc(dsl.InnerDoc, metaclass=HistoryChangesMeta):
    pk = RawField(dsl.Integer(index=True))

    service_variant_id = dsl.Integer(index=False)
    staffer = dsl.Object(
        properties={
            'id': dsl.Integer(index=False),
            'name': dsl.Text(index=False),
            'staff_email': dsl.Text(index=False),
            'staff_cell_phone': dsl.Text(index=False),
            'staff_user_id': dsl.Integer(index=False),
        },
    )
    appliance = dsl.Object(
        properties={
            'id': dsl.Integer(index=False),
            'name': dsl.Text(index=False),
        },
    )
    booked_from = dsl.Date(index=False)
    booked_till = dsl.Date(index=False)
    autoassign = dsl.Boolean(index=False)
    service_name = dsl.Text(index=False)
    chargeable = dsl.Boolean(index=False)
    payable = dsl.Boolean(index=False)
    is_highlighted = dsl.Boolean(index=False)
    resolved_promotion_id = dsl.Integer(index=False)
    resolved_price = dsl.Keyword(index=False)
    resolved_promotion_type = dsl.Keyword(index=False)
    resolved_discount = dsl.Integer(index=False)


class AppointmentHistoryChangesDoc(dsl.InnerDoc, metaclass=HistoryChangesMeta):
    archived = dsl.Boolean(index=False)
    booked_from = dsl.Date(index=False)
    booked_till = dsl.Date(index=False)
    customer = dsl.Object(
        properties={
            'id': dsl.Integer(index=False),
            'cell_phone': dsl.Text(index=False),
            'email': dsl.Text(index=False),
            'full_name': dsl.Text(index=False),
        }
    )
    type = dsl.Keyword(index=False)
    customer_note = dsl.Text(index=False)
    business_note = dsl.Text(index=False)
    business_secret_note = dsl.Text(index=False)
    repeating = dsl.Object(
        properties={
            'id': dsl.Integer(index=False),
            'repeat': dsl.Keyword(index=False),
            'end_type': dsl.Keyword(index=False),
            'repeat_till': dsl.Date(index=False),
            'repeat_number': dsl.Integer(index=False),
            'parent': dsl.Integer(index=False),
        },
    )
    status = dsl.Keyword(index=False)
    traveling = dsl.Object(
        properties={
            'price': dsl.Keyword(index=False),
            'address_line_1': dsl.Text(index=False),
            'address_line_2': dsl.Text(index=False),
            'apartment_number': dsl.Text(index=False),
            'city': dsl.Text(index=False),
            'zipcode': dsl.Keyword(index=False),
            'latitude': dsl.Float(index=False),
            'longitude': dsl.Float(index=False),
        },
    )

    subbookings = RawField(dsl.Nested(SubBookingChangesDoc))


class AppointmentHistoryDocument(HistoryDocumentMixin, ILMDocument):
    class Meta:
        serializer = AppointmentHistorySerializer
        routing = 'id'

    changes = dsl.Object(AppointmentHistoryChangesDoc)

    @classmethod
    def get_model(cls):
        # History document cannot be reindexed
        return None


class AppointmentHistoryIndex(LifecycleManagementIndex):
    no_suffix_name = 'appointment_history_index'
    name = f'{no_suffix_name}_{settings.ES_SUFFIX}'
    index_settings = {
        'max_ngram_diff': 20,
    }
    documents = {
        ESDocType.APPOINTMENT_HISTORY: AppointmentHistoryDocument,
    }
    cluster_settings = {}
    policy = {
        'phases': {
            'hot': {
                'actions': {
                    'rollover': {'max_size': '1GB', 'max_age': '1d'},
                },
            },
            'warm': {
                'min_age': '5d',
                'actions': {
                    'shrink': {'number_of_shards': 1},
                },
            },
            'cold': {
                'min_age': '30d',
                'actions': {
                    'freeze': {},
                },
            },
        },
    }
