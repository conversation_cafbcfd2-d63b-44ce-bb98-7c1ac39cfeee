from datetime import datetime, timedelta
from dateutil.tz import gettz

import pytest

from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import business_recipe
from webapps.booking.notifications.contexts import AppointmentContext

UTC = gettz('UTC')


@pytest.mark.django_db
def test_appointment_context():
    booked_from = datetime(2021, 5, 15, 12, tzinfo=UTC)
    business = business_recipe.make()
    appointment = create_appointment(
        [
            dict(
                booked_from=booked_from,
                booked_till=booked_from + timedelta(minutes=30),
            ),
            dict(
                booked_from=booked_from + timedelta(minutes=45),
                booked_till=booked_from + timedelta(minutes=45 + 30),
            ),
        ],
        business=business,
    )

    class Notification:
        pass

    notification = Notification()
    notification.appointment = appointment

    context = AppointmentContext(notification).get_context()
    assert context['appointment'].subbookings[1].wait_time == '15m'
