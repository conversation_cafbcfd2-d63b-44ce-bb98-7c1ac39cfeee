import uuid
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

import freezegun
from dateutil import tz
from django.http.response import HttpResponse
from django.urls import reverse
from rest_framework import status

from domain_services.booking.src.domains.appointment.errors.draft import (
    DraftNotFoundError,
)
from domain_services.booking.src.domains.calendar.dto import StaffersAvailability, TimeSlotStart
from domain_services.booking.src.domains.staffer.dto import Staffer
from webapps.booking.v2.application.http_api.tests.utils.decorator import tornado_in_drf
from webapps.booking.v2.application.http_api.tests.utils.draft_test_case import (
    DraftTestCase,
)

TZ = 'UTC'
DRAFT_ID = uuid.uuid4()
BOOKED_FROM = datetime(2024, 2, 12, 9, 0, tzinfo=tz.gettz(TZ))
BOOKED_TILL = datetime(2024, 2, 12, 9, 0, tzinfo=tz.gettz(TZ))
SERVICE_ID = 1
SV_ID = 1

(
    STAFFER_1_ID,
    STAFFER_2_ID,
    STAFFER_3_ID,
    STAFFER_4_ID,
) = range(1, 4 + 1)
STAFFERS = [
    Staffer(
        id=STAFFER_1_ID,
        name='with_photo',
        photo_url='photo_url',
    ),
    Staffer(
        id=STAFFER_2_ID,
        name='no_photo_1',
        photo_url=None,
    ),
    Staffer(
        id=STAFFER_3_ID,
        name='no_photo_2',
        photo_url=None,
    ),
    Staffer(
        id=STAFFER_4_ID,
        name='no_photo_2',
        photo_url=None,
    ),
]

DRAFT_ITEM_ID = uuid.uuid4()

APPLICATION_SERVICE_IMPL_PATH = 'webapps.booking.v2.application.http_api.view.base'
APPOINTMENT_SERVICE_IMPL_PATH = 'domain_services.booking.src.domains.appointment.service'


@patch(f'{APPLICATION_SERVICE_IMPL_PATH}.StafferServiceImpl.get_staffers')
@patch(f'{APPOINTMENT_SERVICE_IMPL_PATH}.AppointmentService.check_staffers_availability')
@tornado_in_drf()
@freezegun.freeze_time(datetime(2024, 2, 12, 9, 0, tzinfo=tz.gettz(TZ)))
class TestGetDraftCalendar(DraftTestCase):
    def test_not_existing_draft(
        self,
        get_draft_mock: MagicMock,
        *_,
    ) -> None:
        draft_id = uuid.uuid4()
        get_draft_mock.side_effect = DraftNotFoundError(draft_id)

        response = self._send_request(
            draft_id=draft_id,
            subbooking_id=DRAFT_ITEM_ID,
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_happy_path(
        self,
        check_staffers_availability_mock: MagicMock,
        get_staffers_mock: MagicMock,
    ) -> None:
        check_staffers_availability_mock.return_value = StaffersAvailability(
            customer_selected_slot=TimeSlotStart(date=BOOKED_FROM.date(), day_minutes=10 * 60),
            staffers_slots={
                STAFFER_1_ID: TimeSlotStart(
                    date=BOOKED_FROM.date(),
                    day_minutes=8 * 60,
                ),
                STAFFER_2_ID: TimeSlotStart(
                    date=BOOKED_FROM.date(),
                    day_minutes=10 * 60,
                ),
                STAFFER_3_ID: None,
                STAFFER_4_ID: TimeSlotStart(
                    date=BOOKED_FROM.date() + timedelta(days=1),
                    day_minutes=8 * 60,
                ),
            },
        )
        get_staffers_mock.return_value = STAFFERS

        response = self._send_request(
            draft_id=DRAFT_ID,
            subbooking_id=DRAFT_ITEM_ID,
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.data == {
            'staffers': [
                {
                    'staffer': {
                        'id': STAFFER_2_ID,
                        'name': 'no_photo_1',
                        'photo_url': None,
                    },
                    'slot': {
                        'd': BOOKED_FROM.date().isoformat(),
                        't': '10:00',
                    },
                },
                {
                    'staffer': {
                        'id': STAFFER_1_ID,
                        'name': 'with_photo',
                        'photo_url': 'photo_url',
                    },
                    'slot': {
                        'd': BOOKED_FROM.date().isoformat(),
                        't': '08:00',
                    },
                },
                {
                    'staffer': {
                        'id': STAFFER_4_ID,
                        'name': 'no_photo_2',
                        'photo_url': None,
                    },
                    'slot': {
                        'd': (BOOKED_FROM.date() + timedelta(days=1)).isoformat(),
                        't': '08:00',
                    },
                },
                {
                    'staffer': {
                        'id': STAFFER_3_ID,
                        'name': 'no_photo_2',
                        'photo_url': None,
                    },
                },
            ],
        }

    def _send_request(
        self,
        draft_id: uuid.UUID,
        subbooking_id: uuid.UUID,
    ) -> HttpResponse:
        url = reverse('get_staffers', kwargs={'draft_id': str(draft_id)})

        return self.client.post(
            url,
            data={
                'subbooking_id': str(subbooking_id),
            },
        )
