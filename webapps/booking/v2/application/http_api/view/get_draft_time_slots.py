from uuid import UUID

from rest_framework import status
from rest_framework.exceptions import ValidationError as SerializerValidationError
from rest_framework.request import Request
from rest_framework.response import Response

from webapps.booking.v2.application.http_api.request.get_draft_time_slots import (
    GetDraftTimeslotsRequest,
)
from webapps.booking.v2.application.http_api.response.calendar import TimeSlotsResponse
from webapps.booking.v2.application.http_api.view.base import BaseView
from webapps.booking.v2.application.http_api.view.utils import with_error_handling
from webapps.booking.v2.commons.errors import ValidationError


# pylint: disable=duplicate-code
class GetDraftTimeSlotsView(BaseView):
    serializer_class = GetDraftTimeslotsRequest
    response_serializer_class = TimeSlotsResponse

    @with_error_handling
    def post(self, request: Request, draft_id: str) -> Response:
        user_id = self.request.user.id if self.request and self.request.user else None
        request_serializer = self.get_serializer(data=self.request.data)

        try:
            draft_id = UUID(draft_id)
        except ValueError as e:
            raise ValidationError from e

        try:
            request_serializer.is_valid(raise_exception=True)
        except SerializerValidationError as e:
            raise ValidationError from e

        start = request_serializer.validated_data['start']
        end = request_serializer.validated_data['end']
        response_serializer = self._application_service.get_time_slots(
            user_id=user_id,
            draft_id=draft_id,
            start=start,
            end=end,
            request=request,
        )

        return Response(
            status=status.HTTP_200_OK,
            data=response_serializer.data,
        )
