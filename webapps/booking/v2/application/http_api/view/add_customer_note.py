from uuid import UUID

from rest_framework import status
from rest_framework.exceptions import ValidationError as SerializerValidationError
from rest_framework.request import Request
from rest_framework.response import Response

from webapps.booking.v2.application.http_api.request.add_customer_note import (
    AddCustomerNoteRequest,
)
from webapps.booking.v2.application.http_api.response.draft import DraftResponse
from webapps.booking.v2.application.http_api.view.base import BaseView
from webapps.booking.v2.application.http_api.view.utils import with_error_handling
from webapps.booking.v2.commons.errors import ValidationError


# pylint: disable=duplicate-code
class AddCustomerNoteView(BaseView):
    serializer_class = AddCustomerNoteRequest
    response_serializer_class = DraftResponse

    @with_error_handling
    def post(self, _: Request, draft_id: str) -> Response:
        user_id = self.request.user.id if self.request and self.request.user else None
        request_serializer = self.get_serializer(data=self.request.data)

        try:
            draft_id = UUID(draft_id)
        except ValueError as e:
            raise ValidationError from e

        try:
            request_serializer.is_valid(raise_exception=True)
        except SerializerValidationError as e:
            raise ValidationError from e

        version = request_serializer.validated_data['version']
        customer_note = request_serializer.validated_data['customer_note']
        response_serializer = self._application_service.add_customer_note(
            user_id=user_id,
            draft_id=draft_id,
            version=version,
            customer_note=customer_note,
        )

        return Response(
            status=status.HTTP_200_OK,
            data=response_serializer.data,
        )
