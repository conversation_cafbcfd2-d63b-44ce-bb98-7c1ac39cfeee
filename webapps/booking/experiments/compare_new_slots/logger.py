import logging
from typing import Any

from webapps.booking.timeslots.v2.generator import TimeSlotServiceError

_logger = logging.getLogger('booksy.time_slot_service')


class TimeslotComparisonExperimentLogger:
    @classmethod
    def log_slot_differences(cls, business_id: int, new_slots: Any, old_slots: Any):
        _logger.warning(
            "Old slots for business %s differs from new slots. New slots: %s Old slots: %s",
            business_id,
            new_slots,
            old_slots,
        )

    @classmethod
    def log_warning(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        cls, business_id: int, inputs: Any, start: str, end: str, err: TimeSlotServiceError
    ):
        _logger.warning(
            "Timeslot service didn't return timeslots for business_id %s, "
            "input: %s, start: %s, end: %s. "
            "Error code 400: %s, %s",
            business_id,
            inputs,
            start,
            end,
            err.error_code,
            err.message,
        )

    @classmethod
    def log_exception(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        cls, business_id: int, inputs: Any, start: str, end: str, err: TimeSlotServiceError
    ):
        _logger.exception(
            "Timeslot service end up with error for business_id %s, "
            "input: %s, start: %s, end: %s. "
            "Error code %s: %s, %s",
            business_id,
            inputs,
            start,
            end,
            err.status_code,
            err.error_code,
            err.message,
        )

    @classmethod
    def log_unexpected_exception(cls, err: Exception):
        _logger.exception("Error while processing new timeslots %s", str(err))

    @classmethod
    def log_comparison_started(cls, task_name: str, start: str, end: str, inputs: Any):
        _logger.info(
            '%s: inputs: %s, start: %s, end %s',
            task_name,
            inputs,
            start,
            end,
        )
