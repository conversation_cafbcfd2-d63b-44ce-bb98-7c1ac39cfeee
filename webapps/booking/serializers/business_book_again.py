from rest_framework import serializers
from webapps.booking.enums import WhoMakes<PERSON>hange
from webapps.booking.models import SubBooking, Appointment
from webapps.booking.serializers.appointment import (
    SubbookingServiceVariantSerializer,
    AppointmentTravelingSerializer,
)
from webapps.booking.serializers.book_again import (
    SubbookingAddOnsUsesSerializer,
    ComboChildSerializer,
)
from webapps.booking.serializers.booking import ExtendedBookingServiceSerializer
from webapps.booking.serializers.fields import (
    BookingDateTimeField,
    ServiceQuestionsWithAnswersField,
)
from webapps.booking.subbooking_wrapper import BookAgainSubbookingWrapper
from webapps.business.serializers.fields import ServicePriceField
from webapps.business.serializers.serializers import ExtendedResourceSerializer


class BookAgainSubbookingWrapperSerializer(serializers.Serializer):
    class Meta:
        model = SubBooking
        fields = (
            'booked_from',
            'booked_till',
            'staffer_id',
            'staffer',
            'appliance_id',
            'appliance',
            'service',
            'service_variant',
            'service_price',
            'addons',
        )

    booked_from = BookingDateTimeField(read_only=True)
    booked_till = BookingDateTimeField(read_only=True)
    staffer_id = serializers.IntegerField()
    staffer = ExtendedResourceSerializer(read_only=True)
    appliance_id = serializers.IntegerField()
    appliance = ExtendedResourceSerializer(read_only=True)
    service_variant = SubbookingServiceVariantSerializer(read_only=True)
    service = ExtendedBookingServiceSerializer(read_only=True, source='*')
    service_price = ServicePriceField()
    addons = SubbookingAddOnsUsesSerializer(many=True)
    combo_children = ComboChildSerializer(many=True, required=False)


class BookAgainAppointmentSerializer(serializers.Serializer):
    class Meta:
        model = Appointment
        fields = (
            'booked_from',
            'booked_till',
            'subbookings',
            'traveling',
            'business_note',
            'business_secret_note',
            'customer_note',
            'service_questions',
        )

    # set for traveling troubleshooting
    WHO_MAKES_CHANGE = WhoMakesChange.BUSINESS

    booked_from = BookingDateTimeField(read_only=True)
    booked_till = BookingDateTimeField(read_only=True)
    subbookings = serializers.SerializerMethodField(read_only=True)
    business_note = serializers.CharField(read_only=True)
    business_secret_note = serializers.CharField(read_only=True)
    customer_note = serializers.CharField(read_only=True)
    traveling = AppointmentTravelingSerializer(read_only=True)
    service_questions = ServiceQuestionsWithAnswersField(read_only=True)

    def get_subbookings(self, instance):
        wrapped_subbookings = (BookAgainSubbookingWrapper(s) for s in instance.subbookings)
        available_subbookings = (
            subbooking for subbooking in wrapped_subbookings if subbooking.available
        )
        return BookAgainSubbookingWrapperSerializer(available_subbookings, many=True).data
