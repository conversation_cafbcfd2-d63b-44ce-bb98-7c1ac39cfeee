from functools import reduce

from django.db.models import Q
from django.utils.translation import gettext as _
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from lib.tools import tznow
from webapps.booking.exceptions import BookingConflict, BOOKING_CONFLICT_MESSAGE
from webapps.business.enums import CustomData
from webapps.business.models import Resource
from webapps.business.resources import AnyResource
from webapps.family_and_friends.factory import add_family_and_friends_customer_only_filters


def validate_subbookings_first_booked_from_required(attrs):
    if attrs['subbookings'][0]['booked_from'] is None:
        raise serializers.ValidationError(
            {
                'subbookings': [{'booked_from': [_('This field is required.')]}],
            }
        )


def required_for_commit(field_names):
    """Every field with name in `field_names` should be not None, when not dry_run

    It must be on serializer, not on field, because field with allow_null
    would skip such validation.
    """

    def required_for_commit_validator(attrs, serializer):
        if not serializer.context['dry_run']:
            invalid = [name for name in field_names if attrs.get(name) is None]
            if invalid:
                raise serializers.ValidationError(
                    {name: _('This field is required.') for name in invalid}
                )

    required_for_commit_validator.requires_context = True
    return required_for_commit_validator


def validate_appointment_version(value, field):
    instance = field.root.instance
    if instance is not None:
        if not value:
            raise serializers.ValidationError(_('This field is required.'))
        if instance.version != value:
            raise serializers.ValidationError(
                _('This appointment has been changed in the meantime. Please refresh its details.')
            )

    return value


validate_appointment_version.requires_context = True


def validate_booking_time(attrs):
    """Basic checks for booking times."""
    booked_from = attrs.get('booked_from')
    booked_till = attrs.get('booked_till')
    if booked_from and booked_till:
        if booked_from >= booked_till:
            raise serializers.ValidationError(
                {
                    'booked_till': [_('End time cannot be earlier than or equal to start date.')],
                }
            )

        if booked_from.date() != booked_till.date():
            raise serializers.ValidationError(
                {
                    'booked_till': [_('End date has to be the same day as start date.')],
                }
            )


def validate_gap_hole(attrs):
    booked_from = attrs.get('booked_from')
    booked_till = attrs.get('booked_till')
    variant = attrs.get('service_variant')

    if not (booked_from and booked_till and variant and variant.gap_hole):
        return

    # calculate gap hole and validate duration
    gap_hole_start = booked_from + variant.gap_hole_start_after
    gap_hole_end = gap_hole_start + variant.gap_hole_duration
    if booked_till <= gap_hole_end:
        raise serializers.ValidationError(
            {
                'booked_till': [
                    _(
                        'Duration of the service cannot be shorter '
                        'than total processing time during the service.'
                    ),
                ],
            }
        )


def validate_is_highlighted(attrs):
    """Copy `is_highlighted` for combo children from their parent"""
    if 'is_highlighted' not in attrs:
        return

    for child in attrs.get('combo_children', []):
        child['is_highlighted'] = attrs['is_highlighted']


def validate_existing_customers_only(business, user, bci, member=None):
    """Validate existing customers can book for business with flag."""
    # no user - check not possible
    if not user:
        return
    # no check needed
    if not business.custom_data.get(
        CustomData.ALLOW_BOOKINGS_ONLY_FOR_EXISTING_CLIENTS,
    ):
        return
    # client exists in business - positive
    if bci and bci.id:
        if bci.visible_in_business:
            return

    filters = []
    if user.email:
        filters.extend(
            [
                Q(user__email__iexact=user.email),
                Q(email__iexact=user.email),
            ]
        )
    if user.cell_phone:
        filters.extend(
            [
                Q(user__cell_phone=user.cell_phone),
                Q(cell_phone=user.cell_phone),
            ]
        )

    add_family_and_friends_customer_only_filters(filters, member)
    if not filters:
        return
    # join filters with OR operator
    filters = reduce(lambda x, y: x | y, filters[1:], filters[0])

    if (
        not business.business_customer_infos.filter(
            visible_in_business=True,
            blacklisted=False,
        )
        .filter(filters)
        .exists()
    ):
        raise serializers.ValidationError(
            {
                'booked_for': _(
                    "We're so sorry, "
                    "this merchant is not currently accepting new clients.\n"
                    "\n"
                    "If you feel you're receiving this message in error, "
                    "please email the merchant directly at:\n{email}"
                ).format(name=business.name, email=business.owner.email)
            },
            code='only_existing_clients',
        )


def validate_staffer_with_service_variant(attrs, serializer):
    """
    Validate selected staffer perform given service_variant.
    For customer appointment only.

    Appliance is not considered - customer cannot set it.
    Skip dry_run: wrong staffer will be replaced in factory
    Skip combo_parent: resources apply to children only
    """
    # skip dry_run
    if serializer.context['dry_run']:
        return

    service_variant = attrs['service_variant']
    # skip combo_parent
    if service_variant and service_variant.combo_children_list:
        return

    if staffer := attrs['staffer']:
        if staffer is not AnyResource and staffer.id not in service_variant.staffer_ids:
            raise serializers.ValidationError(
                {
                    'staffer_id': [
                        _(
                            'This service is not provided by chosen '
                            'staff member. Please select another one.'
                        )
                    ]
                }
            )
    elif service_variant.staffer_ids:
        raise serializers.ValidationError(
            {'staffer_id': [_('Selected service requires a staffer.')]}
        )


validate_staffer_with_service_variant.requires_context = True


def validate_dry_run_custom_service(attrs, serializer):
    if not serializer.context['dry_run']:
        return
    if not attrs['service_variant'] and not (attrs['booked_till'] or attrs.get('duration')):
        raise serializers.ValidationError('booked_till or duration is required')
    if not attrs['service_variant'] and not attrs['staffer'] and not attrs['appliance']:
        raise serializers.ValidationError(
            _('Staffer or appliance in the custom service is required')
        )


validate_dry_run_custom_service.requires_context = True


def validate_combo_children_with_parent(attrs):
    combo_children = attrs.get('combo_children')
    if not combo_children:
        return

    service_variant = attrs['service_variant']
    if not service_variant:
        raise ValidationError(
            {'combo_children': _('Custom service with combo children not allowed')}
        )

    if len(combo_children) != len(service_variant.combo_children_list):
        raise ValidationError({'combo_children': _('Wrong combo children')})

    for child, child_service_variant in zip(combo_children, service_variant.combo_children_list):
        if child['service_variant'].id != child_service_variant.id:
            raise ValidationError({'combo_children': _('Wrong combo children')})
        # replace bare service_variant with service_variant as child
        child['service_variant'] = child_service_variant


def validate_lead_time(attrs, serializer):
    if serializer.context['dry_run'] and serializer.context.get('force_incomplete'):
        return
    booked_from = attrs.get('booked_from')
    if not booked_from:
        return

    business = serializer.context['business']
    if booked_from < tznow() + business.booking_min_lead_time:
        raise BookingConflict(BOOKING_CONFLICT_MESSAGE, notices={'lead_time': ['min_lead_time']})
    if booked_from > tznow() + business.booking_max_lead_time:
        raise BookingConflict(BOOKING_CONFLICT_MESSAGE, notices={'lead_time': ['max_lead_time']})


validate_lead_time.requires_context = True


def validate_requested_staffer(attrs):
    is_requested = attrs.get('is_staffer_requested_by_client')
    if not (staffer := attrs.get('staffer')):
        return
    if is_requested and staffer.id == AnyResource.id:
        raise serializers.ValidationError(
            {'is_staffer_requested_by_client': [_('any_staffer cannot be requested')]}
        )


def validate_staffer_selects_staffer(attrs, serializer):
    staffer = attrs.get('staffer')
    user_staffer = serializer.context.get('user_staffer')

    if not (staffer and user_staffer):
        return

    if (
        user_staffer.staff_access_level == Resource.STAFF_ACCESS_LEVEL_STAFF
        and staffer.id != user_staffer.id
    ):
        raise serializers.ValidationError(
            {'staffer_id': _('Staff member cannot assign bookings to other staff.')},
        )


validate_staffer_selects_staffer.requires_context = True
