from decimal import Decimal
from typing import NewType

from django.utils.translation import gettext_lazy as _

from webapps.booking.no_show_protection.business_appointment.validation_rules import validation_rule

AppointmentTotalValue = NewType('AppointmentTotalValue', int | Decimal | None)


class DepositValueTooBig(validation_rule.ValidationError):
    pass


class DepositValueNotBiggerThanAppointmentTotal(validation_rule.ValidationRule):

    def __init__(self, appointment_total_value: AppointmentTotalValue, deposit_value: Decimal):
        super().__init__()
        self._appointment_total_value = appointment_total_value
        self._deposit_value = deposit_value

    def is_valid(self) -> bool:
        if self._appointment_total_value is None:
            return False
        if self._deposit_value <= self._appointment_total_value:
            return True
        self._errors = [
            DepositValueTooBig(
                _(
                    "Deposit of {value} can't be higher than "
                    + "the total appointment value of {appointment_total}."
                ).format(value=self._deposit_value, appointment_total=self._appointment_total_value)
            )
        ]
        return False
