from unittest import TestCase

from webapps.booking.no_show_protection.business_appointment.business_appointment_can_have_deposit_validator import (  # pylint: disable=line-too-long
    BusinessAppointmentCanHaveDepositValidationRules,
    BusinessAppointmentCanHaveDepositValidator,
)
from webapps.booking.no_show_protection.business_appointment.validation_rules.validation_rule import (  # pylint: disable=line-too-long
    ValidationRule,
)


class TrueValidationRule(ValidationRule):
    def is_valid(self):
        return True


class FalseValidationRule(ValidationRule):
    def is_valid(self):
        return False


class TestBusinessAppointmentCanHaveDepositValidator(TestCase):
    def test_can_have_deposit__all_conditions_met(self):
        # given
        conditions = BusinessAppointmentCanHaveDepositValidationRules(
            business_can_pay_deposit=TrueValidationRule(),
            subbookings_at_least_one_price_set=TrueValidationRule(),
            appointment_is_not_repeating=TrueValidationRule(),
            appointment_in_the_future=TrueValidationRule(),
            business_appointment_deposit_feature_enabled=TrueValidationRule(),
        )

        # #when
        can_have_deposit = BusinessAppointmentCanHaveDepositValidator(conditions).validate()
        # #then
        self.assertTrue(can_have_deposit)

    def test_can_have_deposit__non_condition_met(self):
        # given
        conditions = BusinessAppointmentCanHaveDepositValidationRules(
            business_can_pay_deposit=FalseValidationRule(),
            subbookings_at_least_one_price_set=FalseValidationRule(),
            appointment_is_not_repeating=FalseValidationRule(),
            appointment_in_the_future=FalseValidationRule(),
            business_appointment_deposit_feature_enabled=FalseValidationRule(),
        )

        # #when
        can_have_deposit = BusinessAppointmentCanHaveDepositValidator(conditions).validate()
        # #then
        self.assertFalse(can_have_deposit)

    def test_can_have_deposit__some_but_not_all_conditions_met(self):
        # given
        conditions = BusinessAppointmentCanHaveDepositValidationRules(
            business_can_pay_deposit=FalseValidationRule(),
            subbookings_at_least_one_price_set=TrueValidationRule(),
            appointment_is_not_repeating=TrueValidationRule(),
            appointment_in_the_future=FalseValidationRule(),
            business_appointment_deposit_feature_enabled=FalseValidationRule(),
        )

        # #when
        can_have_deposit = BusinessAppointmentCanHaveDepositValidator(conditions).validate()
        # #then
        self.assertFalse(can_have_deposit)
