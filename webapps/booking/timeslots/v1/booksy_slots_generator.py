import pandas as pd

from webapps.booking.timeslots.v1.booksy_slots import BooksySlots
from webapps.booking.timeslots.v1.promotions import (
    merge_slots_and_promotions,
    slots_promotions_create,
)
from webapps.booking.timeslots.v1.serializers import RequestData
from webapps.booking.timeslots.v1.use_cases import customer_slots_create


class BooksySlotsGenerator:
    @classmethod
    def generate(cls, request_data: RequestData) -> BooksySlots:
        slots_scope = request_data.to_slots_scope()
        if slots_scope is None:
            return BooksySlots(merged_slots=None, staff_slots=None)

        promotion_scope = request_data.to_promotion_scope()

        slots_result = customer_slots_create(
            slots_scope=slots_scope,
            business_services=request_data.business_services,
        )

        if slots_result.is_empty:
            return BooksySlots(merged_slots=None, staff_slots=None)

        slots_promotions = slots_promotions_create(
            promotion_scope=promotion_scope,
            merged_slots=slots_result.all_slots,
        )

        staff_slots = slots_result.staff_slots
        if staff_slots is not None:
            staff_slots = merge_slots_and_promotions(staff_slots, slots_promotions)
        merged_slots = merge_slots_and_promotions(slots_result.merged_slots, slots_promotions)

        if request_data.include_empty_days:
            merged_slots, staff_slots = cls._add_working_day_to_slots(
                merged_slots=merged_slots,
                staff_slots=staff_slots,
                working_hours=slots_result.working_hours,
                all_days=request_data.get_all_days(),
            )

        return BooksySlots(merged_slots=merged_slots, staff_slots=staff_slots)

    @classmethod
    def _add_working_day_to_slots(cls, merged_slots, staff_slots, working_hours, all_days):
        def determine_staff_working_day(resource_id, date, working_hours):
            try:
                hours = working_hours.loc[(resource_id, date)]
            except KeyError:
                return False
            return (hours['hour_till'] > hours['hour_from']).any()

        if staff_slots is not None and working_hours is not None:
            merged_slots, staff_slots = cls._expand_slots(merged_slots, staff_slots, all_days)

            unique_pairs = staff_slots.reset_index()[['resource_id', 'date']].drop_duplicates()
            unique_pairs['working_day'] = unique_pairs.apply(
                lambda row: determine_staff_working_day(
                    row['resource_id'], row['date'], working_hours
                ),
                axis=1,
            )
            staff_slots = (
                staff_slots.reset_index()
                .merge(unique_pairs, how='left', on=['resource_id', 'date'])
                .set_index(['resource_id', 'date'])
            )
            working_day_by_date = staff_slots.reset_index().groupby('date')['working_day'].any()
            merged_slots['working_day'] = working_day_by_date.reindex(
                merged_slots.index, fill_value=False
            )
        else:
            merged_slots['working_day'] = True

        return merged_slots, staff_slots

    @staticmethod
    def _expand_slots(merged_slots, staff_slots, all_days):
        # Expand slots so they will contain even dates with no slots available
        all_days = pd.to_datetime(all_days)

        if staff_slots is not None:
            resource_ids = staff_slots.index.get_level_values('resource_id').unique()
            all_combinations = pd.MultiIndex.from_product(
                [resource_ids, all_days], names=['resource_id', 'date']
            )
            staff_slots = staff_slots.reindex(all_combinations, fill_value=[])
        merged_slots = merged_slots.reindex(all_days, fill_value=[])

        return merged_slots, staff_slots
