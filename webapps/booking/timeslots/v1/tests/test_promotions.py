from datetime import date, datetime
from decimal import Decimal

import pandas as pd
import pytest
from freezegun import freeze_time

from webapps.booking.timeslots.v1.core import MergedSlots
from webapps.business.enums import DiscountType, PriceType
from webapps.business.models import (
    SERVICE_VARIANT_FLASH_SALE,
    SERVICE_VARIANT_HAPPY_HOURS,
    SERVICE_VARIANT_LAST_MINUTE,
    ServicePromotion,
)
from webapps.business.service_promotions import PromotionScope, PromotionServiceVariant
from ..promotions import slots_promotions_create

START_DATE = date(2021, 8, 1)  # sunday
END_DATE = date(2021, 8, 2)  # monday

SLOTS = [
    (START_DATE, [120, 150, 180]),
    (END_DATE, [600, 660]),
]
NO_DISCOUNT = 0
DISCOUNT_5 = 5


@pytest.mark.parametrize(
    'promotions, now, client_discount, expected',
    [
        (
            [],
            datetime(2021, 7, 15, 12),
            NO_DISCOUNT,
            ['', '', '', '', ''],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_LAST_MINUTE,
                    promotion_options=dict(
                        service_variant_ids=[110],
                    ),
                )
            ],
            datetime(2021, 7, 15, 12),
            NO_DISCOUNT,
            ['', '', '', '', ''],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_LAST_MINUTE,
                    promotion_options=dict(
                        service_variant_ids=[100],
                    ),
                )
            ],
            datetime(2021, 7, 15, 12),
            NO_DISCOUNT,
            ['', '', '', '', ''],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_LAST_MINUTE,
                    promotion_options=dict(
                        service_variant_ids=[100],
                        discount_rate=25,
                    ),
                    last_minute_hours=2,
                )
            ],
            datetime(2021, 8, 1, 12),
            NO_DISCOUNT,
            ['', '', '', '', ''],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_LAST_MINUTE,
                    promotion_options=dict(
                        service_variant_ids=[100],
                        discount_rate=25,
                    ),
                    last_minute_hours=2,
                )
            ],
            datetime(2021, 8, 1, 0, 30),
            NO_DISCOUNT,
            ['25%', '', '', '', ''],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_FLASH_SALE,
                    promotion_options=dict(
                        service_variant_ids=[110],
                    ),
                )
            ],
            datetime(2021, 7, 15, 12),
            NO_DISCOUNT,
            ['', '', '', '', ''],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_FLASH_SALE,
                    promotion_options=dict(
                        service_variant_ids=[100],
                    ),
                    promotion_start=date(2021, 7, 20),
                    promotion_end=date(2021, 7, 21),
                )
            ],
            datetime(2021, 7, 15, 12),
            NO_DISCOUNT,
            ['', '', '', '', ''],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_FLASH_SALE,
                    promotion_options=dict(
                        service_variant_ids=[100],
                        discount_rate=15,
                    ),
                    promotion_start=date(2021, 7, 15),
                    promotion_end=date(2021, 7, 16),
                    booking_start=date(2021, 8, 5),
                    booking_end=date(2021, 8, 6),
                )
            ],
            datetime(2021, 7, 15, 12),
            NO_DISCOUNT,
            ['', '', '', '', ''],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_FLASH_SALE,
                    promotion_options=dict(
                        service_variant_ids=[100],
                        discount_rate=15,
                    ),
                    promotion_start=date(2021, 7, 15),
                    promotion_end=date(2021, 7, 16),
                    booking_start=date(2021, 8, 2),
                    booking_end=date(2021, 8, 3),
                )
            ],
            datetime(2021, 7, 15, 12),
            NO_DISCOUNT,
            ['', '', '', '15%', '15%'],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_HAPPY_HOURS,
                    promotion_options=dict(
                        service_variants=[
                            dict(
                                service_variant_id=110,
                            )
                        ],
                    ),
                )
            ],
            datetime(2021, 7, 15, 12),
            NO_DISCOUNT,
            ['', '', '', '', ''],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_HAPPY_HOURS,
                    promotion_options=dict(
                        service_variants=[
                            dict(
                                service_variant_id=100,
                                discount_amount=Decimal(12),
                                discount_type=DiscountType.AMOUNT,
                                hour_from='8:00',
                                hour_till='9:00',
                            )
                        ],
                    ),
                    happy_hours_week_day=3,
                )
            ],
            datetime(2021, 7, 15, 12),
            NO_DISCOUNT,
            ['', '', '', '', ''],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_HAPPY_HOURS,
                    promotion_options=dict(
                        service_variants=[
                            dict(
                                service_variant_id=100,
                                discount_amount=Decimal(12),
                                discount_type=DiscountType.AMOUNT,
                                hour_from='8:00',
                                hour_till='9:00',
                            )
                        ],
                    ),
                    happy_hours_week_day=1,
                )
            ],
            datetime(2021, 7, 15, 12),
            NO_DISCOUNT,
            ['', '', '', '', ''],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_HAPPY_HOURS,
                    promotion_options=dict(
                        service_variants=[
                            dict(
                                service_variant_id=100,
                                discount_amount=Decimal(12),
                                discount_type=DiscountType.AMOUNT,
                                hour_from='9:00',
                                hour_till='10:30',
                            )
                        ],
                    ),
                    happy_hours_week_day=1,
                )
            ],
            datetime(2021, 7, 15, 12),
            NO_DISCOUNT,
            ['', '', '', '$12.00', ''],
        ),
        (
            [],
            datetime(2021, 7, 15, 12),
            DISCOUNT_5,
            ['5%', '5%', '5%', '5%', '5%'],
        ),
        (
            [
                ServicePromotion(
                    type=SERVICE_VARIANT_HAPPY_HOURS,
                    promotion_options=dict(
                        service_variants=[
                            dict(
                                service_variant_id=100,
                                discount_amount=Decimal(12),
                                discount_type=DiscountType.AMOUNT,
                                hour_from='9:00',
                                hour_till='10:30',
                            )
                        ],
                    ),
                    happy_hours_week_day=1,
                )
            ],
            datetime(2021, 7, 15, 12),
            DISCOUNT_5,
            ['5%', '5%', '5%', '$12.00', '5%'],
        ),
    ],
)
def test_append_slots_promotions(promotions, now, client_discount, expected):
    promotion_scope = PromotionScope(
        business_id=1,
        service_variants=(
            PromotionServiceVariant(
                service_variant_id=100,
                price=Decimal(100),
                type=PriceType.FIXED,
            ),
        ),
        start_date=START_DATE,
        end_date=END_DATE,
        client_discount=client_discount,
    )
    merged_slots = MergedSlots(
        pd.DataFrame(SLOTS, columns=['date', 'slot'])
        .astype(dict(date='datetime64[ns]'))
        .set_index('date')
        .explode('slot')
    )

    with freeze_time(now):
        result = slots_promotions_create(
            promotion_scope=promotion_scope, merged_slots=merged_slots, promotions=promotions
        )
        print(result)
        assert result.promotion.tolist() == expected
