import datetime

import tornado
from dateutil import parser as dateutil_parser
from django import forms
from django.utils.translation import gettext as _

from lib import fields as booksy_fields
from lib import tools
from lib.forms import ErrorHandlingForm


class TimeSlotsForm(ErrorHandlingForm, forms.Form):
    """Used for both business and customer backed"""

    service_variant_id = booksy_fields.ObjectIDField(
        app='business',
        model='ServiceVariant',
        error_messages={
            'invalid': _('Requested service is no longer available.'),
        },
        exception_codes={'does_not_exist': 'invalid'},
        raise404=False,
    )
    start_date = forms.fields.CharField(
        error_messages={'required': _('Start date is required')},
        required=True,
    )
    end_date = forms.fields.CharField(
        error_messages={'required': _('End date is required')},
        required=True,
    )
    booking_id = booksy_fields.ObjectIDField(
        app='booking',
        model='Booking',
        raise404=True,
        required=False,
    )

    def clean_service_variant_id(self):
        # pylint: disable=missing-function-docstring
        service_variant = self.cleaned_data['service_variant_id']
        if service_variant is not None:
            business = service_variant.service.business
            if not business.active:
                raise tornado.web.HTTPError(404)
            self.business_tz = business.get_timezone()

        return service_variant

    def clean(self):
        cleaned_data = super().clean()

        if not hasattr(self, 'business_tz'):
            # error: service_variant_id was not validated properly
            return cleaned_data

        now = tools.tznow(tz=self.business_tz)
        self.now_date = datetime.datetime.combine(now.date(), datetime.time()).replace(
            tzinfo=self.business_tz
        )

        start_date = cleaned_data.get('start_date')
        if start_date is None:
            return cleaned_data

        try:
            start_date = dateutil_parser.parse(
                start_date, dateutil_parser.parserinfo(yearfirst=True)
            ).replace(tzinfo=self.business_tz)
        except ValueError:
            raise forms.ValidationError(
                _('Date is invalid'),
                params={'type': 'validation', 'code': 'invalid', 'field': 'start_date'},
            ) from ValueError

        cleaned_data['start_date'] = start_date

        end_date = cleaned_data.get('end_date')
        if end_date is None:
            return cleaned_data

        try:
            end_date = dateutil_parser.parse(
                end_date,
                dateutil_parser.parserinfo(yearfirst=True),
            )
        except ValueError:
            raise forms.ValidationError(
                _('Date is invalid'),
                params={'type': 'validation', 'code': 'invalid', 'field': 'start_date'},
            ) from ValueError
        # set end_date to midnight of the next day
        end_date = datetime.datetime.combine(
            end_date.date() + datetime.timedelta(days=1),
            datetime.time(0, 0, 0),
        ).replace(tzinfo=self.business_tz)

        cleaned_data['end_date'] = end_date

        if start_date and end_date:
            if start_date >= end_date:
                raise forms.ValidationError(
                    _('End time cannot be earlier than or equal to start time.'),
                    params={'type': 'validation', 'code': 'invalid', 'field': 'end_date'},
                )
        return cleaned_data


class BusinessTimeSlotsForm(TimeSlotsForm):
    service_variant_id = booksy_fields.ObjectIDField(
        app='business',
        model='ServiceVariant',
        error_messages={'invalid': _('Requested service is no longer available.')},
        exception_codes={'does_not_exist': 'invalid'},
        raise404=False,
        required=False,
    )

    def __init__(self, *args, **kwargs):
        self.business_tz = kwargs.get('business_tz')  # It's optional
        if self.business_tz:
            kwargs.pop('business_tz')
        super().__init__(*args, **kwargs)
