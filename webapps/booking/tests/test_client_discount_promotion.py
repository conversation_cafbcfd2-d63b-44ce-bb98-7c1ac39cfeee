from datetime import datetime, time
from decimal import Decimal
from typing import Union
from dateutil.relativedelta import relativedelta

from freezegun import freeze_time
from model_bakery import baker
from pytz import UTC

from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.models import BookingChange
from webapps.booking.serializers.appointment import (
    CustomerAppointmentSerializer,
    AppointmentSerializer,
)
from webapps.booking.tests.utils import create_appointment
from webapps.business.enums import (
    ComboPricing,
    ComboType,
    PriceType,
)
from webapps.business.models.service import (
    SERVICE_VARIANT_CLIENT_DISCOUNT,
    ComboMembership,
    ServicePrice,
    ServiceVariant,
)
from webapps.business.tests.test_service_promotions_CD_FS_HH_LM import (
    BasePromotionTest,
)
from webapps.pos.enums import CASH
from webapps.pos.models import Transaction
from webapps.pos.serializers import TransactionSerializer


AppointmentSerializers = Union[
    AppointmentSerializer,
    CustomerAppointmentSerializer,
]


@freeze_time(datetime(2020, 3, 16, 8, 42, tzinfo=UTC))
class TestClientDiscountPromotion(BasePromotionTest):
    def setUp(self):
        super().setUp()

        self.cash = baker.make_recipe(
            'webapps.pos.payment_type_recipe',
            pos=self.pos,
            code=CASH,
        )

        self.customer_user(discount=Decimal(0))

    def create_appointment(self) -> CustomerAppointmentSerializer:
        return self.customer_appointment_serializer(
            booked_from=time(10, 0),
            dry_run=False,
        )[0]

    @property
    def transaction_context(self) -> dict:
        return {
            'pos': self.pos,
            'operator': self.business.owner,
            'compatibilities': {
                'new_checkout': True,
            },
        }

    def update_bci_discount(self, discount: Decimal) -> None:
        self.customer_bci.discount = discount
        self.customer_bci.save()

    def checkout_appointment(
        self,
        serializer: AppointmentSerializers,
    ) -> TransactionSerializer:
        booking = serializer.data['subbookings'][0]

        if booking['service_promotion']:
            booking_price = booking['service_promotion']['_price']
        else:
            booking_price = booking['service']['variant']['price']

        data = {
            'dry_run': False,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'bookings': [
                {
                    'booking_id': booking['id'],
                    'item_price': booking_price,
                }
            ],
            'payment_rows': [{'payment_type_code': CASH, 'amount': booking_price}],
        }

        t_serializer = TransactionSerializer(
            data=data,
            instance=None,
            context=self.transaction_context,
        )

        assert t_serializer.is_valid(), t_serializer.errors
        t_serializer.save()

        self.assertEqual('100.00', t_serializer.data['total_unformatted'])

        return t_serializer

    def test_subbooking_has_client_discount_promotion(self):
        appointment_serializer = self.create_appointment()
        booking = appointment_serializer.instance.subbookings[0]
        self.assertEqual(
            SERVICE_VARIANT_CLIENT_DISCOUNT,
            booking.resolved_promotion_type,
        )

    def test_subbooking_has_client_discount_promotion_combo(self):
        combo = baker.make(
            ServiceVariant,
            service__business=self.business,
            service__combo_type=ComboType.SEQUENCE,
            combo_pricing=ComboPricing.CUSTOM,
            duration=relativedelta(minutes=60),
        )
        membership_1 = baker.make(
            ComboMembership,
            combo=combo,
            child__service__business=self.business,
            child__duration=relativedelta(minutes=20),
            price=Decimal('20'),
            type=PriceType.FIXED,
        )
        membership_2 = baker.make(
            ComboMembership,
            combo=combo,
            child__service__business=self.business,
            child__duration=relativedelta(minutes=40),
            price=Decimal('40'),
            type=PriceType.FIXED,
        )
        membership_1.child.service.add_staffers([self.staffer])
        membership_2.child.service.add_staffers([self.staffer])
        combo.service.add_staffers([self.staffer])
        self.update_bci_discount(discount=Decimal('10'))

        appointment_serializer = self.customer_appointment_serializer(
            booked_from=time(10, 0),
            dry_run=True,
            variants=[combo],
        )[0]
        appointment_wrapper = appointment_serializer.instance

        booking = appointment_wrapper.subbookings[0]
        combo_children = booking.combo_children

        self.assertEqual(combo_children[0].resolved_promotion_type, SERVICE_VARIANT_CLIENT_DISCOUNT)
        self.assertEqual(combo_children[0].resolved_price, Decimal('18'))

        self.assertEqual(combo_children[1].resolved_promotion_type, SERVICE_VARIANT_CLIENT_DISCOUNT)
        self.assertEqual(combo_children[1].resolved_price, Decimal('36'))

        self.assertEqual(booking.resolved_promotion_type, SERVICE_VARIANT_CLIENT_DISCOUNT)
        self.assertEqual(booking.resolved_price, Decimal('54'))

        self.assertEqual(
            appointment_wrapper.total,
            ServicePrice(
                value=Decimal('54'),
                price_type=PriceType.FIXED,
                discount=Decimal('6'),
            ),
        )

    def test_transaction_discount_not_mutated_after_checkout(self):
        appointment_serializer = self.create_appointment()
        transaction = self.checkout_appointment(
            appointment_serializer,
        ).instance

        self.update_bci_discount(discount=Decimal('50'))

        transaction_serializer = TransactionSerializer(
            instance=Transaction.objects.get(pk=transaction.id),
            context=self.transaction_context,
        )
        self.assertEqual(
            '100.00',
            transaction_serializer.data['total_unformatted'],
        )

    def test_customer_appointment_discount_not_mutated_after_checkout(self):
        appointment_serializer = self.create_appointment()
        self.checkout_appointment(appointment_serializer)
        self.update_bci_discount(discount=Decimal('50'))

        booking = appointment_serializer.instance.subbookings[0]
        appointment = AppointmentWrapper.get_by_appointment_id(
            booking.appointment_id,
            business_id=booking.appointment.business_id,
        )
        ca_serializer = CustomerAppointmentSerializer(
            instance=appointment,
            context={
                'business': booking.appointment.business,
                'user': self.customer_bci.user,
            },
        )

        self.assertEqual('$100.00', ca_serializer.data['total'])
        self.assertEqual(
            Decimal('0.00'),
            ca_serializer.data['total_discount_amount'],
        )
        self.assertIsNone(ca_serializer.data['subbookings'][0]['service_promotion'])

    def test_business_appointment_discount_not_mutated_after_checkout(self):
        appointment_serializer = self.create_appointment()
        self.checkout_appointment(appointment_serializer)
        self.update_bci_discount(discount=Decimal('50'))

        booking = appointment_serializer.instance.subbookings[0]
        appointment = AppointmentWrapper.get_by_appointment_id(
            booking.appointment_id,
            business_id=booking.appointment.business_id,
        )
        appt_serializer = AppointmentSerializer(
            instance=appointment,
            context={'business': booking.appointment.business},
        )

        self.assertEqual('$100.00', appt_serializer.data['total'])
        self.assertEqual(
            Decimal('0.00'),
            appt_serializer.data['total_discount_amount'],
        )
        self.assertIsNone(appt_serializer.data['subbookings'][0]['service_promotion'])

    def test_business_appointment_discount_update_on_checkout(self):
        expected_discount_rate = Decimal('50')

        self.update_bci_discount(discount=Decimal('10'))
        appointment_serializer = self.create_appointment()

        self.update_bci_discount(discount=expected_discount_rate)

        booking = appointment_serializer.instance.subbookings[0]
        transaction_serializer = TransactionSerializer(
            data={
                'dry_run': False,
                'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
                'bookings': [
                    {
                        'booking_id': booking.id,
                        'item_price': booking.service_variant.price,
                    }
                ],
                'payment_rows': [
                    {
                        'payment_type_code': CASH,
                        'amount': Decimal('50'),
                    }
                ],
            },
            instance=None,
            context=self.transaction_context,
        )
        assert transaction_serializer.is_valid(), transaction_serializer.errors
        transaction_serializer.save()

        self.update_bci_discount(discount=Decimal('25'))
        booking = appointment_serializer.instance.subbookings[0]
        appointment = AppointmentWrapper.get_by_appointment_id(
            booking.appointment_id,
            business_id=booking.appointment.business_id,
        )

        self.assertEqual(
            expected_discount_rate,
            appointment.subbookings[0].resolved_discount,
            appointment.subbookings[0].resolved_discount,
        )

        ca_serializer = CustomerAppointmentSerializer(
            instance=appointment,
            context=self.customer_appointment_context,
        )
        ca_data = ca_serializer.data['subbookings'][0]['service_promotion']
        assert ca_data['promotion_type'] == SERVICE_VARIANT_CLIENT_DISCOUNT
        assert ca_data['discount_value'] == expected_discount_rate

        ba_serializer = AppointmentSerializer(
            instance=appointment, context=self._appointment_context
        )
        ba_data = ba_serializer.data['subbookings'][0]['service_promotion']
        assert ba_data['promotion_type'] == SERVICE_VARIANT_CLIENT_DISCOUNT
        assert ba_data['discount_value'] == expected_discount_rate

    def test_appointment_resolve_discount_booking_history(self):
        booked_from = datetime(2020, 10, 13, 8, 30, tzinfo=UTC)
        appointment = create_appointment(
            [
                dict(
                    booked_from=booked_from,
                    service_variant=self.variant,
                )
            ],
            booked_for=self.customer_bci,
            business=self.business,
        )
        self.update_bci_discount(discount=Decimal('50'))
        booking = appointment.subbookings[0]

        transaction_serializer = TransactionSerializer(
            data={
                'dry_run': False,
                'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
                'bookings': [
                    {
                        'booking_id': booking.id,
                        'item_price': booking.service_variant.price,
                    }
                ],
                'payment_rows': [
                    {
                        'payment_type_code': CASH,
                        'amount': Decimal('50'),
                    }
                ],
            },
            instance=None,
            context=self.transaction_context,
        )
        self.assertTrue(transaction_serializer.is_valid())

        with freeze_time(booked_from):
            transaction_serializer.save()
            for change in BookingChange.objects.filter(subbooking=booking):
                self.assertEqual(change.created, change.subbooking.updated)
                self.assertEqual(change.created, booked_from)

    def test_appointment_discount_0_change_to_100(self):
        appointment = create_appointment(
            [
                dict(
                    booked_from=self.booking_time(10, 0),
                    booked_till=self.booking_time(11, 0),
                    service_variant=self.variant,
                ),
            ],
            booked_for=self.customer_bci,
            business=self.business,
        )
        subbooking = appointment.subbookings[0]
        assert subbooking.id
        assert subbooking.resolved_promotion_type is None

        appointment = AppointmentWrapper.get_by_appointment_id(
            appointment_id=subbooking.appointment_id,
            customer_user_id=subbooking.appointment.booked_for.user_id,
        )
        appointment_serializer = AppointmentSerializer(
            instance=appointment,
            context=self.business_appointment_context,
        )
        transaction = self.checkout_appointment(
            appointment_serializer,
        ).instance

        transaction_serializer = TransactionSerializer(
            instance=Transaction.objects.prefetch_all().get(pk=transaction.id),
            context=self.transaction_context,
        )
        self.assertEqual('$100.00', transaction_serializer.data['total'])

        self.update_bci_discount(discount=Decimal('100'))

        appointment = AppointmentWrapper.get_by_appointment_id(
            appointment_id=subbooking.appointment_id,
            customer_user_id=subbooking.appointment.booked_for.user_id,
        )

        sbk = appointment.subbookings[0]
        assert sbk.resolved_promotion_type == SERVICE_VARIANT_CLIENT_DISCOUNT
        assert sbk.resolved_discount == 0
        assert sbk.resolved_price == Decimal(100)

        appointment_serializer = AppointmentSerializer(
            instance=appointment,
            context=self.business_appointment_context,
        )

        subbooking = appointment_serializer.data['subbookings'][0]
        assert subbooking['service_promotion'] is None
