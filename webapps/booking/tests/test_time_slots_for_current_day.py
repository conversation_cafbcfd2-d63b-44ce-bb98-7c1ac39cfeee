import json

import pytest
from dateutil.relativedelta import relativedelta
from django.conf import settings
from model_bakery import baker
from pytz import UTC

from lib.tools import (
    l_b,
    tznow,
)
from service.tests import BaseAsyncHTTPTest
from webapps.booking.tests.test_appointment_base import BaseTestAppointment
from webapps.business.models import (
    Service,
    ServiceVariant,
)

# SELECTED TIME AND DATE
# date and time selected
# 1)corresponding to test-case from ticket #40073
#   we need any time point in working hours of business and
#   with minutes as 01, 11, 12, 21 etc.
# 2) we can't use test time zone because
#   pytz.timezone(timezone_for_tests()) method will give a big inaccuracy
#   in converting between timezones
#   Some comments from official docs:
#   'Unfortunately using the tzinfo argument of the standard
#   datetime constructors ‘’does not work’’ with pytz for many timezones.'
#   link to docs:
#   http://pytz.sourceforge.net/#localized-times-and-date-arithmetic

# create datetime object
test_date_time = tznow(tz=UTC)
test_date_time = test_date_time.replace(hour=11, minute=1)
TEST_TIMEZONE = 'UTC'

# format to string datetime object
formatted_test_datetime = test_date_time.astimezone(UTC).strftime(settings.DATETIME_FORMAT)


# freeze time during the test
@pytest.mark.freeze_time(formatted_test_datetime)
@pytest.mark.django_db
class TestTimeSlotForCurrentDay(BaseAsyncHTTPTest, BaseTestAppointment):
    url = '/customer_api/me/bookings/time_slots/'

    def setUp(self):
        # all attributes
        # as business and owner we get from BaseTestAppointment
        super().setUp()
        # change timezone for current test
        self.business.time_zone_name = TEST_TIMEZONE
        region = self.business.region
        region.time_zone_name = TEST_TIMEZONE
        region.save()
        self.tz = self.business.get_timezone()
        # create service for appliance
        self.service_appliance = baker.make(
            Service,
            business=self.business,
            active=True,
        )
        self.service_appliance.add_appliances([self.appliance])
        self.variant_appliance = baker.make(
            ServiceVariant,
            service=self.service_appliance,
            active=True,
            duration=relativedelta(minutes=30),
            time_slot_interval=relativedelta(minutes=15),
        )

    def get_formatted_start_end_time(self):
        start_date = self.business.tznow
        end_date = start_date + relativedelta(days=1)
        formatted_start = start_date.astimezone(self.tz).strftime(settings.DATE_FORMAT)
        formatted_end = end_date.astimezone(self.tz).strftime(settings.DATE_FORMAT)
        return formatted_start, formatted_end

    def get_appliance_service_args(self):
        formatted_start, formatted_end = self.get_formatted_start_end_time()
        return dict(
            service_variant_id=self.variant_appliance.id,
            start_date=formatted_start,
            end_date=formatted_end,
            resource=self.appliance.id,
        )

    def get_staffer_service_args(self):
        formatted_start, formatted_end = self.get_formatted_start_end_time()
        return dict(
            service_variant_id=self.variant_appliance.id,
            start_date=formatted_start,
            end_date=formatted_end,
            resource=self.staffer.id,
        )

    @staticmethod
    def split_and_check(time, interval):
        _, minutes = time.split(':')
        assert minutes.isdigit()
        assert (int(minutes) % interval) == 0

    def check_time_slots(self, time_slots):
        for time_slot in time_slots:
            for time_stamp in time_slot['time_slots']:
                # assert that minutes in time have module 0
                # divided without remaining part
                self.split_and_check(time_stamp['from'], time_stamp['interval'])
                self.split_and_check(time_stamp['to'], time_stamp['interval'])

    def test_time_slots_current_day_appliance(self):
        args = self.get_appliance_service_args()
        response = self.fetch(self.url, method='GET', args=args)

        assert response.code == 200

        body = json.loads(l_b(response.body))
        self.check_time_slots(body['time_slots'])

    def test_time_slots_current_day_staffer(self):
        args = self.get_staffer_service_args()
        response = self.fetch(self.url, method='GET', args=args)

        assert response.code == 200
        body = json.loads(l_b(response.body))
        self.check_time_slots(body['time_slots'])
