from decimal import Decimal

import pytest
from dateutil.relativedelta import relativedelta

from lib.feature_flag.feature import ServiceNameReplicationFlag
from lib.tests.utils import override_feature_flag
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.factory.service_questions import QuestionsList
from webapps.booking.models import SubBooking
from webapps.booking.serializers.booking import BookingServiceSerializer
from webapps.booking.serializers.my_booksy import _ServiceSerializer
from webapps.booking.service_data import (
    ComboChildData,
    SubBookingServiceData,
)
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import (
    business_recipe,
    service_variant_recipe,
    staffer_recipe,
)
from webapps.business.enums import (
    ComboPricing,
    ComboType,
    NoShowProtectionType,
    PriceType,
    RateType,
)
from webapps.business.models.service import (
    ComboMembership,
    ServiceVariant,
)
from webapps.business.service_price import ServicePrice
from webapps.notification.scenarios.scenarios_booking_mixin import BookingMixin
from webapps.pos.baker_recipes import pos_recipe


@pytest.mark.django_db
@override_feature_flag({ServiceNameReplicationFlag: True})
def test_subbooking_service_data__build():
    """
    Check if all data for SubBookingServiceData is collected
    """
    service_variant: ServiceVariant = service_variant_recipe.make(
        label='Variant',
        type=PriceType.STARTS_AT,
        price=Decimal('49.99'),
        duration=relativedelta(hours=1, minutes=30),
        service__questions=QuestionsList(['Question 1', 'Question 2']),
    )
    booking = SubBooking(service_variant=service_variant)

    service_data = SubBookingServiceData.build(booking)
    assert service_data.service_name == service_variant.service.name
    assert service_data.service_variant_id == service_variant.id
    assert service_data.service_variant_label == service_variant.label
    assert service_data.service_variant_type == service_variant.type
    assert service_data.service_variant_price == service_variant.price
    assert service_data.service_variant_duration == service_variant.duration
    assert service_data.service_questions == service_variant.service.questions


def test_subbooking_service_data__build__empty():
    booking = SubBooking(service_variant=None)

    service_data = SubBookingServiceData.build(booking)
    assert service_data.service_name is None
    assert service_data.service_variant_id is None
    assert service_data.service_variant_label is None
    assert service_data.service_variant_type is None
    assert service_data.service_variant_price is None
    assert service_data.service_variant_duration is None
    assert service_data.service_questions == QuestionsList([])


@pytest.mark.django_db
def test_subbooking_service_data__serialize():
    """
    Check serialize/deserialize
    """
    service_variant: ServiceVariant = service_variant_recipe.make(
        label='Variant',
        type=PriceType.STARTS_AT,
        price=Decimal('49.99'),
        duration=relativedelta(hours=1, minutes=30),
        service__questions=QuestionsList(['Question 1', 'Question 2']),
    )
    booking = SubBooking(service_variant=service_variant)
    service_data = SubBookingServiceData.build(booking)

    data = service_data.serialize()
    assert service_data.deserialize(data) == service_data


@pytest.mark.django_db
def test_subbooking__build_service_data():
    """
    Check if data is collected upon booking creation
    """
    service_variant = service_variant_recipe.make()
    (booking,) = create_appointment(
        [dict(service_variant=service_variant)],
        business=service_variant.service.business,
    ).subbookings

    assert booking.service_data.service_variant_id == service_variant.id


@pytest.mark.django_db
@override_feature_flag({ServiceNameReplicationFlag: True})
def test_subbooking_service_data__combo_children():
    business = business_recipe.make()
    staffer = staffer_recipe.make(business=business)

    service_variant_child_1 = service_variant_recipe.make(
        service__business=business,
        price=Decimal('20.00'),
        duration=relativedelta(hours=1),
    )
    service_variant_child_2 = service_variant_recipe.make(
        service__business=business,
        price=Decimal('30.00'),
        duration=relativedelta(minutes=25),
        payment__payment_amount=Decimal('10.00'),
        payment__payment_type=NoShowProtectionType.CANCELLATION_FEE,
        payment__saving_type=RateType.PERCENTAGE,
    )
    service_variant = service_variant_recipe.make(
        service__combo_type=ComboType.SEQUENCE,
        type=None,
        price=None,
        duration=relativedelta(),
        combo_pricing=ComboPricing.CUSTOM,
    )
    service_variant_child_1.add_staffers([staffer])
    service_variant.add_staffers([staffer])
    ComboMembership.objects.bulk_create(
        [
            ComboMembership(
                combo=service_variant,
                child=service_variant_child_1,
                order=1,
                type=PriceType.FIXED,
                price=Decimal('15.00'),
                gap_time=relativedelta(minutes=5),
            ),
            ComboMembership(
                combo=service_variant,
                child=service_variant_child_2,
                order=2,
                type=PriceType.STARTS_AT,
                price=Decimal('25.00'),
                gap_time=relativedelta(minutes=0),
            ),
        ]
    )

    booking = SubBooking(service_variant=service_variant)

    service_data = SubBookingServiceData.build(booking)
    assert service_data.service_name == service_variant.service.name
    assert service_data.service_variant_id == service_variant.id
    assert service_data.service_variant_label == service_variant.label
    assert service_data.service_variant_type == service_variant.service_price.price_type
    assert service_data.service_variant_price == service_variant.service_price.value
    assert service_data.service_variant_duration == service_variant.service_duration
    assert service_data.service_questions == service_variant.service.questions
    assert service_data.combo_type == ComboType.SEQUENCE
    assert len(service_data.combo_children) == 2

    serialized_data = service_data.serialize()
    assert serialized_data['combo_type'] == ComboType.SEQUENCE.value

    combo_child_1_data = serialized_data['combo_children'][0]
    assert combo_child_1_data == {
        'gap_time': 5,
        'payment_amount': None,
        'payment_type': None,
        'payment_saving_type': None,
        'service_name': service_variant_child_1.service.name,
        'service_questions': [],
        'service_variant_duration': 60,
        'service_variant_id': service_variant_child_1.id,
        'service_variant_label': service_variant_child_1.label,
        'service_variant_price': '15.00',
        'service_variant_type': PriceType.FIXED.value,
        'service_variant_version': service_variant_child_1.version,
    }
    assert ComboChildData.deserialize(combo_child_1_data).serialize() == combo_child_1_data

    combo_child_2_data = service_data.combo_children[1].serialize()
    assert combo_child_2_data == {
        'gap_time': 0,
        'payment_amount': '8.33',
        'payment_type': NoShowProtectionType.CANCELLATION_FEE.value,
        'payment_saving_type': RateType.PERCENTAGE.value,
        'service_name': service_variant_child_2.service.name,
        'service_questions': [],
        'service_variant_duration': 25,
        'service_variant_id': service_variant_child_2.id,
        'service_variant_label': service_variant_child_2.label,
        'service_variant_price': '25.00',
        'service_variant_type': PriceType.STARTS_AT.value,
        'service_variant_version': service_variant_child_2.version,
    }
    assert ComboChildData.deserialize(combo_child_2_data).serialize() == combo_child_2_data


@pytest.mark.django_db
def test_subbooking__change_variant():
    """
    Check whether service_data gets rebuilt when new service_variant is selected.
    """
    old_service_variant, new_service_variant = service_variant_recipe.make(_quantity=2)

    (booking,) = create_appointment(
        [dict(service_variant=old_service_variant)],
        business=old_service_variant.service.business,
    ).subbookings

    booking.service_variant = new_service_variant
    booking.save(override=True)
    booking.refresh_from_db()
    assert booking.service_data.service_variant_id == new_service_variant.id


@pytest.mark.random_failure
@pytest.mark.django_db
@override_feature_flag({ServiceNameReplicationFlag: True})
def test_subbooking__variant_and_service_updated():
    """
    Check whether changes in service and service_variant don't affect frozen data
    """
    service_variant = service_variant_recipe.make(
        label='Variant',
    )
    (booking,) = create_appointment(
        [dict(service_variant=service_variant)],
        business=service_variant.service.business,
    ).subbookings

    service_variant.service.name = 'Service_1 (changed)'
    service_variant.service.save()

    service_variant.label = 'Variant (changed)'
    service_variant.save()

    booking.refresh_from_db()
    assert booking.service_data.service_variant_id == service_variant.id
    assert booking.service_data.service_variant_label == 'Variant'
    assert booking.service_data.service_name == 'Service_1'

    booking.save(override=True)
    assert booking.service_data.service_variant_id == service_variant.id
    assert booking.service_data.service_variant_label == 'Variant'
    assert booking.service_data.service_name == 'Service_1'

    # appointment details
    booking_serializer = BookingServiceSerializer(instance=booking)
    assert booking_serializer.data['name'] == 'Service_1'

    # my_booksy
    box_serializer = _ServiceSerializer(instance=booking)
    assert box_serializer.data['name'] == 'Service_1'

    # Emails etc. (BookingMixin from notification.scenarios)
    booking_info = BookingMixin.get_booking_info(booking=booking, language='en', extra_params={})
    assert booking_info['bookings_info']['subbookings'][0]['name'] == 'Service_1'


@pytest.mark.django_db
@override_feature_flag({ServiceNameReplicationFlag: False})
def test_subbooking__variant_and_service_updated_service_replication_flag():
    """
    Testing flag: ServiceNameReplicationFlag=False - to be removed with the flag.
    """
    service_variant = service_variant_recipe.make(
        label='Variant',
    )

    (booking,) = create_appointment(
        [dict(service_variant=service_variant)],
        business=service_variant.service.business,
    ).subbookings

    service_variant.service.name = 'Service_1 (changed)'
    service_variant.service.save()
    booking.refresh_from_db()

    # appointment details
    booking_serializer = BookingServiceSerializer(instance=booking)
    assert booking_serializer.data['name'] == 'Service_1 (changed)'

    # my_booksy
    box_serializer = _ServiceSerializer(instance=booking)
    assert box_serializer.data['name'] == 'Service_1 (changed)'

    # Emails etc. (BookingMixin from notification.scenarios)
    booking_info = BookingMixin.get_booking_info(booking=booking, language='en', extra_params={})
    assert booking_info['bookings_info']['subbookings'][0]['name'] == 'Service_1 (changed)'


@pytest.mark.django_db
def test_subbooking_service_data__repeating_service_variant_in_combo():
    business = business_recipe.make()
    staffer = staffer_recipe.make(business=business)
    pos_recipe.make(business=business)

    service_variant_child_1 = service_variant_recipe.make(
        service__business=business,
        service__resources=[staffer],
        price=Decimal('20.00'),
        duration=relativedelta(hours=1),
    )
    service_variant_child_2 = service_variant_recipe.make(
        service__business=business,
        price=Decimal('30.00'),
        duration=relativedelta(minutes=25),
        payment__payment_amount=Decimal('10.00'),
        payment__payment_type=NoShowProtectionType.CANCELLATION_FEE,
        payment__saving_type=RateType.PERCENTAGE,
    )
    service_variant = service_variant_recipe.make(
        service__combo_type=ComboType.SEQUENCE,
        service__resources=[staffer],
        type=None,
        price=None,
        duration=relativedelta(),
        combo_pricing=ComboPricing.CUSTOM,
    )
    ComboMembership.objects.bulk_create(
        [
            ComboMembership(
                combo=service_variant,
                child=service_variant_child_1,
                order=1,
                type=PriceType.FIXED,
                price=Decimal('15.00'),
                gap_time=relativedelta(minutes=5),
            ),
            ComboMembership(
                combo=service_variant,
                child=service_variant_child_2,
                order=2,
                type=PriceType.FIXED,
                price=Decimal('100.00'),
                gap_time=relativedelta(minutes=10),
            ),
            ComboMembership(
                combo=service_variant,
                child=service_variant_child_2,
                order=2,
                type=PriceType.FIXED,
                price=Decimal('50.00'),
                gap_time=relativedelta(minutes=0),
            ),
        ]
    )

    appointment = create_appointment(
        [
            {
                'service_variant': service_variant,
                'combo_children': [
                    {'service_variant': service_variant_child_1},
                    {'service_variant': service_variant_child_2},
                    {'service_variant': service_variant_child_2},
                ],
            },
        ],
        business=business,
    )
    appointment_wrapper = AppointmentWrapper(appointment.subbookings)
    assert appointment_wrapper.total == ServicePrice(
        Decimal('165.00'),
        PriceType.FIXED,
        should_display_price_types=PriceType.should_display().union({PriceType.VARIES}),
    )
