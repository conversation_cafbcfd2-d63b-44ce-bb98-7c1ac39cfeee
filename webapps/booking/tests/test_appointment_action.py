import json
from datetime import timed<PERSON>ta

import pytest
from django.db.models import Count
from mock import (
    call,
    patch,
)
from model_bakery import baker
from rest_framework import status

from lib.tools import (
    l_b,
    tznow,
)
from service.tests import BaseAsyncHTTPTest
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import AppointmentType as AT, AppointmentTypeSM as AT
from webapps.booking.models import (
    Appointment,
    AppointmentChange,
)
from webapps.booking.tests.utils import create_appointment
from webapps.business.models import (
    Business,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.baker_recipes import bci_recipe
from webapps.business.searchables.tests.utils import generate_business_name
from webapps.business.staffer_name_generator import generate_user_name
from webapps.family_and_friends.baker_recipes import (
    member_recipe,
    mbci_recipe,
    member_appointment_recipe,
)
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.public_partners.models import PartnerPermissionBusiness
from webapps.user.models import (
    User,
    UserSessionCache,
)

BOOKING_DATA = (
    ('+', {'minutes': 30}, 1),
    ('-', {'days': 3}, 1),
    ('+', {'days': 3, 'minutes': 25}, 2),
    ('-', {'days': 3, 'minutes': 30}, 2),
)


class CustomerAppointmentActionTestHandler(BaseAsyncHTTPTest):
    url_template = '/customer_api/me/appointments/{}/{}/action'

    BODY_TEMPLATE = {
        'action': '',
        '_version': 0,
        'business_note': '',
        'release_deposit': False,
        '_notify_about_cancel': True,
        '_notify_about_cancel_email': False,
        '_notify_about_cancel_sms': False,
        '_update_future_bookings': False,
    }

    def setUp(self):
        super().setUp()
        source = self.customer_booking_src
        business = baker.make(
            Business,
            name=generate_business_name(),
            active=True,
            visible=True,
            status=Business.Status.PAID,
            latitude=39.828127,
            longitude=-98.579404,
        )
        now = tznow()
        full_name = generate_user_name()
        first_name = full_name.split()[0]
        last_name = full_name.split()[-1]
        user = baker.make(
            User,
            first_name=first_name,
            last_name=last_name,
        )
        self.user = user
        bci = baker.make(
            BusinessCustomerInfo,
            user=user,
        )
        self.bci = bci
        for data in BOOKING_DATA:
            action, booking_data, count = data
            if action == '+':
                booked_from = now + timedelta(**booking_data)
                appointment_status = Appointment.STATUS.ACCEPTED
            elif action == '-':
                booked_from = now - timedelta(**booking_data)
                appointment_status = Appointment.STATUS.FINISHED
            else:
                continue

            booked_till = booked_from + timedelta(minutes=15)
            create_appointment(
                [dict(booked_from=booked_from, booked_till=booked_till)] * count,
                business=business,
                source=source,
                status=appointment_status,
                booked_for=bci,
            )

    def get_response(self, appointment_type, appointment_id, business, action, user):
        appointment = AppointmentWrapper.get_appointment(
            appointment_type=appointment_type,
            appointment_id=appointment_id,
            business_id=business.id,
            prefetch_all=False,
        )
        body = self.BODY_TEMPLATE
        body['action'] = action
        body['_version'] = appointment._version  # pylint: disable=protected-access
        url = self.url_template.format(appointment_type, appointment_id)
        # get headers
        headers = self.get_headers(url)
        token = headers['X-ACCESS-TOKEN']
        # obfuscate session for suitable user
        UserSessionCache.objects.filter(session_id=token).update(user=user)
        self.session.clear_get_user_cache()
        # return response
        return self.fetch(url, method='POST', body=body)

    @staticmethod
    def _get_booking(**filters):
        booking = (
            Appointment.objects.annotate(bookings_count=Count('bookings'))
            .filter(
                **filters,
            )
            .first()
            .bookings.first()
        )
        return booking

    def _create_family_and_friends_data(self, appointment):
        parent = member_recipe.make(user_profile=self.user.customer_profile)
        member = member_recipe.make()
        parent.members.add(member)
        mbci_recipe.make(member=parent, bci=self.bci)
        mbci = bci_recipe.make(business=self.bci.business, user=member.user_profile.user)
        mbci_recipe.make(member=member, bci=mbci)

        member_appointment_recipe.make(appointment=appointment, booked_for=mbci, booked_by=self.bci)
        appointment.booked_for = mbci
        appointment.save()

    @pytest.mark.django_db
    def test_appointment_action_cancel_future_single__booking(self):
        now = tznow()

        # get single booking in future
        booking = self._get_booking(
            bookings__booked_till__gte=now,
            status=Appointment.STATUS.ACCEPTED,
            bookings_count=1,
        )

        response = self.get_response(
            AT.SINGLE,
            booking.id,
            booking.appointment.business,
            'cancel',
            booking.appointment.booked_for.user,
        )
        assert response.code == status.HTTP_200_OK
        updated_booking = Appointment.objects.get(id=booking.appointment_id)

        assert updated_booking.status == Appointment.STATUS.CANCELED

    @pytest.mark.django_db
    def test_appointment_cancel_should_include_user_full_name_in_notification(self):
        now = tznow()
        booking = self._get_booking(
            bookings__booked_till__gte=now,
            status=Appointment.STATUS.ACCEPTED,
            bookings_count__gt=1,
        )

        self.bci.first_name = ''
        self.bci.last_name = ''
        self.bci.save()
        self.assertEqual(self.bci.full_name, '')
        self.assertNotEqual(self.bci.user.full_name, '')

        response = self.get_response(
            AT.MULTI,
            booking.appointment_id,
            booking.appointment.business,
            'cancel',
            booking.appointment.booked_for.user,
        )
        self.assertEqual(response.code, status.HTTP_200_OK)
        notifications = NotificationHistoryDocument.tasks_get(
            business_id=booking.appointment.business_id
        )
        self.assertIn(self.bci.user.full_name, notifications[0].content)

    @patch('webapps.public_partners.actions.send_signal_public_api_task.delay')
    @pytest.mark.django_db
    def test_appointment_action_cancel_future_multi_booking(self, mock_send):
        now = tznow()
        # get first booking that is part of  multibooking
        booking = self._get_booking(
            bookings__booked_till__gte=now,
            status=Appointment.STATUS.ACCEPTED,
            bookings_count__gt=1,
        )
        baker.make(PartnerPermissionBusiness, business_id=booking.appointment.business_id)
        baker.make(
            AppointmentChange,
            appointment_id=booking.appointment_id,
            public_api_data_hash='old_hash',
        )

        response = self.get_response(
            AT.MULTI,
            booking.appointment_id,
            booking.appointment.business,
            'cancel',
            booking.appointment.booked_for.user,
        )
        assert response.code == status.HTTP_200_OK
        updated_bookings = Appointment.objects.filter(id=booking.appointment_id)
        for updated_booking in updated_bookings:
            assert updated_booking.status == Appointment.STATUS.CANCELED

        assert mock_send.call_count == 1
        assert mock_send.call_args == call(
            appointment_id=booking.appointment_id, action='cancelled'
        )

    @pytest.mark.django_db
    def test_appointment_action_cancel_past_single_booking(self):
        now = tznow()
        # get single booking in past
        booking = self._get_booking(
            bookings_count=1,
            bookings__booked_till__lte=now,
            status=Appointment.STATUS.FINISHED,
        )

        response = self.get_response(
            AT.SINGLE,
            booking.id,
            booking.appointment.business,
            'cancel',
            booking.appointment.booked_for.user,
        )
        assert response.code == status.HTTP_400_BAD_REQUEST
        errors = response.body
        error_data = json.loads(l_b(errors))
        error = error_data['errors'][0]
        assert error['field'] == 'action'
        assert error['type'] == 'validation'
        updated_booking = Appointment.objects.get(id=booking.appointment_id)
        assert updated_booking.status == Appointment.STATUS.FINISHED

    @patch('webapps.public_partners.actions.send_signal_public_api_task.delay')
    @pytest.mark.django_db
    def test_appointment_action_cancel_past_multi_booking(self, mock_send):
        now = tznow()
        # get single booking in past
        booking = self._get_booking(
            bookings__booked_till__lte=now,
            status=Appointment.STATUS.FINISHED,
            bookings_count__gt=1,
        )
        baker.make(PartnerPermissionBusiness, business_id=booking.appointment.business_id)
        baker.make(
            AppointmentChange,
            appointment_id=booking.appointment_id,
            public_api_data_hash='old_hash',
        )

        response = self.get_response(
            AT.MULTI,
            booking.appointment_id,
            booking.appointment.business,
            'cancel',
            booking.appointment.booked_for.user,
        )
        assert response.code == status.HTTP_400_BAD_REQUEST
        errors = response.body
        error_data = json.loads(l_b(errors))
        error = error_data['errors'][0]
        assert error['field'] == 'action'
        assert error['type'] == 'validation'
        updated_bookings = Appointment.objects.filter(
            id=booking.appointment_id,
        )
        for updated_booking in updated_bookings:
            assert updated_booking.status == Appointment.STATUS.FINISHED

        assert mock_send.call_count == 0

    @pytest.mark.django_db
    def test_appointment_action_cancel_future_single_booking_family_and_friends(self):
        now = tznow()

        # get single booking in future
        booking = self._get_booking(
            bookings__booked_till__gte=now,
            status=Appointment.STATUS.ACCEPTED,
            bookings_count=1,
        )
        self._create_family_and_friends_data(booking.appointment)

        response = self.get_response(
            AT.SINGLE, booking.id, booking.appointment.business, 'cancel', self.user
        )
        assert response.code == status.HTTP_200_OK
        updated_booking = Appointment.objects.get(id=booking.appointment_id)

        assert updated_booking.status == Appointment.STATUS.CANCELED

    @patch('webapps.public_partners.actions.send_signal_public_api_task.delay')
    @pytest.mark.django_db
    def test_appointment_action_cancel_future_multi_booking_family_and_friends(self, mock_send):
        now = tznow()
        # get first booking that is part of  multibooking
        booking = self._get_booking(
            bookings__booked_till__gte=now,
            status=Appointment.STATUS.ACCEPTED,
            bookings_count__gt=1,
        )
        self._create_family_and_friends_data(booking.appointment)
        baker.make(PartnerPermissionBusiness, business_id=booking.appointment.business_id)
        baker.make(
            AppointmentChange,
            appointment_id=booking.appointment_id,
            public_api_data_hash='old_hash',
        )

        response = self.get_response(
            AT.MULTI,
            booking.appointment_id,
            booking.appointment.business,
            'cancel',
            self.user,
        )
        assert response.code == status.HTTP_200_OK
        updated_bookings = Appointment.objects.filter(id=booking.appointment_id)
        for updated_booking in updated_bookings:
            assert updated_booking.status == Appointment.STATUS.CANCELED

        assert mock_send.call_count == 1
        assert mock_send.call_args == call(
            appointment_id=booking.appointment_id, action='cancelled'
        )
