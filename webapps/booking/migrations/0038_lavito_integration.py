# Generated by Django 1.11.11 on 2018-07-27 07:10
from django.conf import settings
from django.db import migrations, models
from django.db.models.query import Q


BUSINESS_DEV_KEY = 'lavito-84809d6a-792f-486f-b0df-2aac12273923'
CUSTOMER_DEV_KEY = 'lavito-e050619f-b382-48fc-81cf-f89611d62498'
INTERNAL_DEV_KEY = 'lavito-99b7e296-94b3-4ff1-a195-0188759efa2c'

BUSINESS_LIVE_KEY = 'lavito-94e95389-34c9-49ed-9077-5bb8ee414e44'
CUSTOMER_LIVE_KEY = 'lavito-e3fba03e-4c94-4bf7-9725-9eda62619a94'
INTERNAL_LIVE_KEY = 'lavito-c17dfc4f-c9d6-4650-b803-2f1f5043befb'


def create_keys(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    BookingSources = apps.get_model('booking', 'BookingSources')

    if settings.LIVE_DEPLOYMENT:
        BookingSources.objects.using(db_alias).get_or_create(
            name='Lavito', app_type='B', api_key=BUSINESS_LIVE_KEY
        )
        BookingSources.objects.using(db_alias).get_or_create(
            name='Lavito', app_type='C', api_key=CUSTOMER_LIVE_KEY
        )
        BookingSources.objects.using(db_alias).get_or_create(
            name='Lavito', app_type='I', api_key=INTERNAL_LIVE_KEY
        )

    else:
        BookingSources.objects.using(db_alias).get_or_create(
            name='Lavito', app_type='B', api_key=BUSINESS_DEV_KEY
        )
        BookingSources.objects.using(db_alias).get_or_create(
            name='Lavito', app_type='C', api_key=CUSTOMER_DEV_KEY
        )
        BookingSources.objects.using(db_alias).get_or_create(
            name='Lavito', app_type='I', api_key=INTERNAL_DEV_KEY
        )


def delete_keys(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    BookingSources = apps.get_model('booking', 'BookingSources')

    BookingSources.objects.using(db_alias).filter(
        Q(api_key=BUSINESS_DEV_KEY)
        | Q(api_key=CUSTOMER_DEV_KEY)
        | Q(api_key=INTERNAL_DEV_KEY)
        | Q(api_key=BUSINESS_LIVE_KEY)
        | Q(api_key=CUSTOMER_LIVE_KEY)
        | Q(api_key=INTERNAL_LIVE_KEY)
    ).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('booking', '0037_repeating_booking_end_type'),
        ('business', '0148_merge_20180725_0625'),
    ]

    operations = [
        migrations.AlterField(
            model_name='booking',
            name='status',
            field=models.CharField(
                choices=[
                    ('A', 'Confirmed'),
                    ('C', 'Cancelled'),
                    ('D', 'Declined'),
                    ('F', 'Finished'),
                    ('M', 'Modified'),
                    ('P', 'Proposed'),
                    ('N', 'No show'),
                    ('W', 'Waiting for confirmation (by business)'),
                    ('R', 'Rejected'),
                    ('V', 'Awaiting email (account) verification'),
                ],
                db_index=True,
                max_length=1,
            ),
        ),
        migrations.AlterField(
            model_name='bookingchange',
            name='status',
            field=models.CharField(
                choices=[
                    ('A', 'Confirmed'),
                    ('C', 'Cancelled'),
                    ('D', 'Declined'),
                    ('F', 'Finished'),
                    ('M', 'Modified'),
                    ('P', 'Proposed'),
                    ('N', 'No show'),
                    ('W', 'Waiting for confirmation (by business)'),
                    ('R', 'Rejected'),
                    ('V', 'Awaiting email (account) verification'),
                ],
                max_length=1,
            ),
        ),
        migrations.AlterField(
            model_name='bookingsources',
            name='name',
            field=models.CharField(
                choices=[
                    ('Web', 'Web'),
                    ('Widget', 'Widget'),
                    ('Android', 'Android'),
                    ('iPhone', 'iPhone'),
                    ('Internal', 'Internal'),
                    ('ImporterPARP', 'ImporterPARP'),
                    ('GoogleFeeds', 'GoogleFeeds'),
                    ('Facebook', 'Facebook'),
                    ('YelpFeeds', 'YelpFeeds'),
                    ('Instagram', 'Instagram'),
                    ('Lavito', 'Lavito'),
                ],
                max_length=20,
            ),
        ),
        migrations.RunPython(create_keys, delete_keys),
    ]
