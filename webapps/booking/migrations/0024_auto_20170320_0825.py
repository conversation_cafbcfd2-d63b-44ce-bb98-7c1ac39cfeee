# Generated by Django 1.10.5 on 2017-03-20 08:25

from django.db import migrations


def create_google_feed_source(apps, schema_editor):
    db_name = schema_editor.connection.alias
    BookingSources = apps.get_model('booking', 'BookingSources')
    BookingSources.objects.using(db_name).create(
        name='GoogleFeeds',
        app_type='C',
        api_key='google-feeds-76dc5c97-994e-4c77-a536-79455222c6e4',
    )


class Migration(migrations.Migration):

    dependencies = [
        ('booking', '0023_auto_20161024_1347'),
    ]

    operations = [
        migrations.RunPython(
            code=create_google_feed_source,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
