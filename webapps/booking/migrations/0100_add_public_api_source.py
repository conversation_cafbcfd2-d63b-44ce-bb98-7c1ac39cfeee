# Generated by Django 2.2.13 on 2020-07-27 11:53

from django.conf import settings
from django.db import migrations
from django.db.models import Q

DEV_KEY = 'booksy-public-fyykptme-8gte-krfx-ugnp-d2uih4ihmolv'
LIVE_KEY = 'booksy-public-10r44wa5-looa-ltrk-ieoo-ujfgis1wgcqb'


def create_booksy_public_key(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    BookingSources = apps.get_model('booking', 'BookingSources')
    params = dict(name='Public API KEY', app_type='P')
    if settings.LIVE_DEPLOYMENT:
        params['api_key'] = LIVE_KEY
    else:
        params['api_key'] = DEV_KEY

    BookingSources.objects.using(db_alias).get_or_create(**params)


def delete_booksy_public_key(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    BookingSources = apps.get_model('booking', 'BookingSources')
    BookingSources.objects.using(db_alias).filter(Q(api_key=DEV_KEY) | Q(api_key=LIVE_KEY)).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('booking', '0099_merge_20200722_0847'),
    ]

    operations = [
        migrations.RunPython(
            create_booksy_public_key,
            delete_booksy_public_key,
        )
    ]
