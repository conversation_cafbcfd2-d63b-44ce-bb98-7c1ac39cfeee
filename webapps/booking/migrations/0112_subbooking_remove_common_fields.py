# Generated by Django 2.2.13 on 2020-09-15 11:59

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('booking', '0111_drop_views_booking_multibooking'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='subbooking',
            name='archived',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='booked_for',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='business',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='business_note',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='business_secret_note',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='customer_email',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='customer_name',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='customer_note',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='customer_phone',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='import_uid',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='last_edit',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='multibooking_import_uid',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='repeating',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='source',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='status',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='status_changed',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='type',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='subbooking',
            name='service_questions',
        ),
    ]
