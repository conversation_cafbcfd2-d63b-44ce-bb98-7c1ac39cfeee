# Generated by Django 1.11.17 on 2018-12-18 20:00
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0200_service_promotion_model'),
        ('booking', '0052_on_delete_policy'),
    ]

    operations = [
        migrations.CreateModel(
            name='BookingPromotions',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('id', models.AutoField(primary_key=True, serialize=False)),
            ],
            options={
                'abstract': False,
                'get_latest_by': 'updated',
            },
        ),
        migrations.AddField(
            model_name='bookingpromotions',
            name='bookings',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='promotions',
                to='booking.Booking',
            ),
        ),
        migrations.AddField(
            model_name='bookingpromotions',
            name='promotions',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='promotions',
                to='business.ServicePromotion',
            ),
        ),
    ]
