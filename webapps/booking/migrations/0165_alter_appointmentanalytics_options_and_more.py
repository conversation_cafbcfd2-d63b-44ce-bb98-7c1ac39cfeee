# Generated by Django 4.2.13 on 2024-06-12 12:56

from django.db import migrations, models
import django.utils.timezone
import lib.models


class Migration(migrations.Migration):

    dependencies = [
        ("booking", "0164_appointment_is_booksy_gift_card_appointment"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="appointmentanalytics",
            options={"get_latest_by": "updated"},
        ),
        migrations.AlterModelManagers(
            name="appointmentanalytics",
            managers=[
                ("objects", lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.AddField(
            model_name="appointmentanalytics",
            name="created",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="Created (UTC)",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="appointmentanalytics",
            name="deleted",
            field=models.DateTimeField(blank=True, null=True, verbose_name="Deleted (UTC)"),
        ),
        migrations.AddField(
            model_name="appointmentanalytics",
            name="updated",
            field=models.DateTimeField(auto_now=True, db_index=True, verbose_name="Updated (UTC)"),
        ),
    ]
