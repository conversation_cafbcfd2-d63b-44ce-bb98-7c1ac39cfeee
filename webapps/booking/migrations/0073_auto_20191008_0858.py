# Generated by Django 2.0.13 on 2019-10-08 08:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('booking', '0072_auto_20191007_1102'),
    ]

    operations = [
        migrations.AlterField(
            model_name='booking',
            name='type',
            field=models.CharField(
                choices=[
                    ('B', 'Created by business'),
                    ('C', 'Created by customer'),
                    ('T', 'Resource time off (full days)'),
                    ('R', 'Resource time reservation (like booking)'),
                ],
                max_length=1,
            ),
        ),
        migrations.AlterField(
            model_name='bookingchange',
            name='repeat',
            field=models.CharField(
                blank=True,
                choices=[
                    ('D', 'Every day'),
                    ('W', 'Every week'),
                    ('F', 'Every two weeks'),
                    ('M', 'Every month'),
                    ('C', 'Custom'),
                ],
                max_length=1,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='repeatingbooking',
            name='repeat',
            field=models.CharField(
                choices=[
                    ('D', 'Every day'),
                    ('W', 'Every week'),
                    ('F', 'Every two weeks'),
                    ('M', 'Every month'),
                    ('C', 'Custom'),
                ],
                max_length=1,
            ),
        ),
    ]
