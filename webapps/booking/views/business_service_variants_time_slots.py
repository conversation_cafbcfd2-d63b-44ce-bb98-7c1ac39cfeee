import logging

from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.mixins import ResponseSerializerMixin
from drf_api.permissions.authentication import OptionalLogin
from webapps.booking.commands.timeslots.get_business_service_variants import (
    GetBusinessServiceVariantsTimeSlotsCommand,
    GetBusinessServiceVariantsTimeSlotsCommandInput,
)
from webapps.booking.exceptions import (
    BusinessDoesNotExistException,
    TimeSlotsException,
    UserIsBlacklistedException,
)
from webapps.booking.views.request.business_service_variants_time_slots import (
    BusinessServiceVariantsTimeSlotsRequest,
)
from webapps.booking.views.response.business_service_variants_time_slots import (
    BusinessServiceVariantsTimeSlotsResponse,
)

_logger = logging.getLogger('booksy.business_service_variants_time_slots')


class BusinessServiceVariantsTimeSlotsView(  # nosemgrep: no-is-authenticated-permission-for-drf
    BaseBooksySessionGenericAPIView,
    ResponseSerializerMixin,
):
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)
    permission_classes = (OptionalLogin,)
    serializer_class = BusinessServiceVariantsTimeSlotsRequest
    response_serializer_class = BusinessServiceVariantsTimeSlotsResponse
    _command = GetBusinessServiceVariantsTimeSlotsCommand()

    def post(  # pylint: disable=too-many-return-statements
        self,
        _: Request,
        business_pk: int,
    ) -> Response:
        user = self.user if self.user and not self.user.is_anonymous else None
        request_serializer = self.get_serializer(data=self.request.data)

        request_serializer.is_valid(raise_exception=True)

        try:
            command_output = self._command.execute(
                GetBusinessServiceVariantsTimeSlotsCommandInput(
                    business_id=business_pk,
                    user=user,
                    start=request_serializer.validated_data["start"],
                    end=request_serializer.validated_data["end"],
                    service_variant_ids=request_serializer.validated_data["service_variant_ids"],
                )
            )
        except BusinessDoesNotExistException:
            _logger.warning("Business %s does not exist", business_pk)
            return Response(status=status.HTTP_404_NOT_FOUND)
        except UserIsBlacklistedException:
            _logger.warning("User %s is blacklisted in business %s", user.id, business_pk)
            return Response(status=status.HTTP_400_BAD_REQUEST)
        except TimeSlotsException:
            _logger.warning(
                "Failed to calculate time slots in business %s for service variant %s",
                business_pk,
                request_serializer.validated_data["service_variant_ids"],
            )
            return Response(status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:  # pylint: disable=broad-except
            _logger.exception("Something went wrong: %s", e)
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(
            status=status.HTTP_200_OK,
            data=self.get_response_serializer(
                {
                    "timeslots": command_output.slots,
                },
            ).data,
        )
