from django.conf import settings
from django.utils.translation import gettext_lazy as _
from lib.email.tools import format_email_address

from lib.email import prepare_and_send_email
from webapps.contact_us.recipients import get_contact_us_system_recipients, RECIPIENTS_DATA

CustomerEmail = str
CustomerName = str
FromData = (CustomerName, CustomerEmail)


def send_contact_us_email(from_data: FromData, message: str, **kwargs) -> None:
    recipients = get_contact_us_system_recipients(
        country=settings.API_COUNTRY,
        recipients_data=RECIPIENTS_DATA,
    )
    customer_name, customer_email = from_data
    recipients['bcc'] = recipients.get('bcc', ()) + (customer_email,)
    params = (
        kwargs
        | recipients
        | {
            'body': message,
            'subject': _('[Contact Us] message from {customer_name}').format(
                customer_name=customer_name,
            ),
            'from_addr': format_email_address(customer_name, customer_email),
            'reply_name': customer_name,
            'reply_to': customer_email,
        }
    )
    prepare_and_send_email(**params)
