from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.mixins import BusinessViewValidatorMixin, QuerySerializerMixin
from lib.enums import AppType
from service.mixins.validation import validate_serializer
from webapps.business.models import Resource
from webapps.hints.helpers import get_hints, save_user_dismissed_hint
from webapps.hints.models import Hint
from webapps.hints.serializers import (
    DismissedHintsRequestSerializer,
    HintSerializer,
    HintsRequestSerializer,
    StoryRequestSerializer,
    StoryResponseSerializer,
)


class HintsListView(
    BusinessViewValidatorMixin, QuerySerializerMixin, BaseBooksySessionGenericAPIView
):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)
    permission_classes = (IsAuthenticated,)
    serializer_class = HintSerializer
    query_serializer_class = HintsRequestSerializer

    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_STAFF

    def get(self, request: Request, business_pk: int) -> Response:
        """List of application features' hints.
        swagger:
            summary: Listing of features' hints
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: include_visited
                  description: Returns all the hints even though they have already been seen
                  type: boolean
                  paramType: query
                  required: false
            type: HintMessagesResponse
        """
        business = self.get_business(business_pk)
        query_serializer = self.query_serializer_class(data=self.request.query_params)
        query_serializer.is_valid(raise_exception=True)

        hints = get_hints(
            business=business,
            language=self.language,
            booking_source=self.booking_source,
            user=self.user,
            include_visited=query_serializer.validated_data['include_visited'],
        )

        return Response(hints, status=status.HTTP_200_OK)

    def put(self, request: Request, business_pk: int) -> Response:
        """
        swagger:
            summary: Save user dismissed hints
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  description: payload
                  paramType: body
                  type: DismissedHintsRequest

        """
        business = self.get_business(business_pk)

        request_serializer = DismissedHintsRequestSerializer(data=request.data)
        request_serializer.is_valid(raise_exception=True)

        save_user_dismissed_hint(self.user, request_serializer.data['hint_id'])

        result = get_hints(
            business=business,
            language=self.language,
            booking_source=self.booking_source,
            user=self.user,
        )

        return Response(result, status=status.HTTP_201_CREATED)


class StoriesListView(
    BusinessViewValidatorMixin, QuerySerializerMixin, BaseBooksySessionGenericAPIView
):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)
    permission_classes = (IsAuthenticated,)

    serializer_class = StoryResponseSerializer
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_STAFF
    query_serializer_class = StoryRequestSerializer

    def get(self, request, business_pk):
        self.get_business(business_pk)

        serializer = self.query_serializer_class(data=request.query_params)
        validate_serializer(serializer)
        data = serializer.validated_data

        app_type = AppType.from_source(self.booking_source)
        stories = Hint.objects.active_stories(
            language=self.language,
            app_type=app_type,
            hint_keys=data['story_key'],
        )

        return Response(
            self.serializer_class({'stories': stories}).data,
            status=status.HTTP_200_OK,
        )
