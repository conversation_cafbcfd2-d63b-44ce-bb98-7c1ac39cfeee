from django.core.files.uploadedfile import SimpleUploadedFile
from model_bakery import seq
from model_bakery.recipe import Recipe, foreign_key

from webapps.hints.enums import HintType

hint_recipe = Recipe(
    'hints.Hint',
    hint_key=seq('route-key.'),
    name=seq('Hint name '),
    parent=None,
)
hint_message_recipe = Recipe(
    'hints.HintMessage',
    hint=foreign_key(hint_recipe),
)


feature_recipe = hint_recipe.extend(hint_type=HintType.FEATURE)
recommendation_recipe = hint_recipe.extend(
    hint_type=HintType.RECOMMENDATION,
)

feature_message_recipe = hint_message_recipe.extend(
    hint=foreign_key(feature_recipe),
)
recommendation_recipe = hint_message_recipe.extend(
    hint=foreign_key(
        recommendation_recipe,
    )
)

avatar_recipe = Recipe(
    'hints.BusinessCategoryAvatar',
    image=SimpleUploadedFile('default-avatar.jpg', b'file content'),
)
default_avatar_recipe = avatar_recipe.extend(category=None)

story_recipe = Recipe(
    'hints.Hint',
    hint_type=HintType.STORY,
    name=seq('Hint name '),
    parent=None,
)
story_message_recipe = Recipe(
    'hints.HintMessage',
    hint=foreign_key(story_recipe),
)
