"""
overrides default django classes to corresponding ones inheriting from
PermissionRequiredMixin
"""

from django.conf import settings
from django.contrib.auth.mixins import AccessMixin, PermissionRequiredMixin
from django.core.exceptions import ImproperlyConfigured
from django.views import generic

from country_config import Country
from webapps.user.groups import GroupName


class View(PermissionRequiredMixin, generic.View):
    def visible(self):
        return self.has_permission()


class FormView(PermissionRequiredMixin, generic.edit.FormView):
    def visible(self):
        return self.has_permission()


class TemplateView(PermissionRequiredMixin, generic.TemplateView):
    def visible(self):
        return self.has_permission()


class BaseIsInGroupMixin:
    groups = None  # if there is more than one group, checks if you belong to at least one

    @classmethod
    def get_required_groups(cls):
        if cls.groups is None:
            raise ImproperlyConfigured(
                f'{cls.__name__} is missing the groups attribute.'
                f' Define {cls.__name__}.group, or override '
                f'{cls.__name__}.get_required_groups().'
            )
        return cls.groups

    @classmethod
    def is_user_in_groups(cls, user):
        if not settings.GROUP_PERMISSIONS_ENABLED:
            return True

        groups = cls.get_required_groups()
        return user.is_authenticated and (
            user.is_superuser
            or user.groups.filter(name__in=[groups] if isinstance(groups, str) else groups).exists()
        )


class GroupPermissionMixin(BaseIsInGroupMixin, AccessMixin):
    """View mixin that restricts access based on user's groups."""

    def dispatch(self, request, *args, **kwargs):
        if not self.is_user_in_groups(request.user):
            return self.handle_no_permission()
        return super().dispatch(request, *args, **kwargs)


class BillingAdminGroupPermissionMixin(GroupPermissionMixin):
    groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
    )


class USIsInGroupPermissionMixin(BaseIsInGroupMixin):
    @classmethod
    def is_user_in_groups(cls, user):
        if settings.API_COUNTRY == Country.US:
            return super().is_user_in_groups(user)
        return True
