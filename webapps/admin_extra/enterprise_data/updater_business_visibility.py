from typing import (
    List,
    Dict,
)

from django.db.transaction import atomic

from lib.tools import sget
from webapps.business.elasticsearch import BusinessDocument
from webapps.business.enums import CustomData
from webapps.business.models import Business
from webapps.business.models.business_change import BusinessChange
from webapps.user.tools import get_system_user


def update_business_visibility(business_ids: List[int], visibility_data: Dict):
    old_values = {}
    new_values = {
        'default': {},
    }

    visibility = extract_flag(visibility_data, 'visibility')
    validity = extract_flag(visibility_data, 'validity')
    allow_blisting = extract_flag(visibility_data, 'allow_blisting')

    if visibility is not None:
        new_values['default']['visible'] = visibility

    if validity is not None:
        new_values['default']['active'] = validity
        if validity:
            new_values['default']['status'] = Business.Status.PAID
        else:
            new_values['default']['status'] = Business.Status.BLOCKED

    if allow_blisting is not None:
        new_values['default']['custom_data'] = {
            CustomData.CAN_TRANSFORM_INTO_B_LISTING: allow_blisting,
        }

    updated_business_ids = []

    businesses = Business.objects.filter(id__in=business_ids)
    for business in businesses:
        updated_business_ids.append(business.id)

        old_values[business.id] = {}

        if visibility is not None:
            old_values[business.id]['visible'] = business.visible

            business.visible = visibility

        if validity is not None:
            old_values[business.id]['active'] = business.active
            old_values[business.id]['status'] = business.status

            business.active = validity
            if validity:
                business.status = Business.Status.PAID
            else:
                business.status = Business.Status.BLOCKED

        if allow_blisting is not None:
            old_allow_blisting = sget(
                business,
                ['custom_data', CustomData.CAN_TRANSFORM_INTO_B_LISTING],
            )
            old_values[business.id] = {
                'custom_data': {
                    CustomData.CAN_TRANSFORM_INTO_B_LISTING: old_allow_blisting,
                },
            }

            business.custom_data[CustomData.CAN_TRANSFORM_INTO_B_LISTING] = allow_blisting

    with atomic():
        Business.objects.bulk_update(
            businesses,
            ['active', 'custom_data', 'status', 'visible'],
        )
        BusinessChange.bulk_change(
            business_ids=updated_business_ids,
            operator=get_system_user(),
            old_obj_vars=old_values,
            new_obj_vars=new_values,
        )

    BusinessDocument.reindex(ids=updated_business_ids)

    return dict(
        affected_business_ids=updated_business_ids,
    )


def extract_flag(visibility_data, field_name):
    flag_value = visibility_data.get(field_name)
    if isinstance(flag_value, bool):
        return flag_value
    if flag_value == 'True':
        return True
    if flag_value == 'False':
        return False
    return None
