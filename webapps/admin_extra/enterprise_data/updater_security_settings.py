from typing import List

from django.core.exceptions import ValidationError
from django.db.transaction import atomic

from lib.tools import tznow
from service.tools import is_allowed_request_for_business_ids
from webapps.admin_extra.bank_importer_utils import (
    EnterpriseDataHandlingException,
)
from webapps.business.models import Business
from webapps.business_related.models import SecuritySettings
from webapps.business_related.validators import validate_ip_addresses


@atomic
def update_security_settings(
    business_ids: List[int],
    allowed_ips_raw: str,
    switch_off_ip_checking: bool,
):
    if switch_off_ip_checking:
        run_delete_business_security_settings(business_ids)
    else:
        if not allowed_ips_raw.strip():
            raise EnterpriseDataHandlingException("Allowed IPs shouldn't be empty!")

        try:
            allowed_ips = allowed_ips_raw.split(';')
            validate_ip_addresses(allowed_ips)
            run_update_business_security_settings(business_ids, allowed_ips)
        except ValidationError as ex:
            raise EnterpriseDataHandlingException(
                f"Error occurred while validating IPs: {ex}"
            ) from ex

    is_allowed_request_for_business_ids.cache_clear()

    return dict(affected_business_ids=business_ids)


def run_update_business_security_settings(business_ids, allowed_ips):
    businesses = Business.objects.filter(
        id__in=business_ids,
    ).only('security_settings')

    security_settings_to_create = []
    security_settings_to_update = []

    for business in businesses:
        if hasattr(business, 'security_settings'):
            business.security_settings.allowed_ips = allowed_ips
            business.security_settings.deleted = None
            security_settings_to_update.append(business.security_settings)
        else:
            security_settings = SecuritySettings(
                business=business,
                allowed_ips=allowed_ips,
            )
            security_settings_to_create.append(security_settings)

    SecuritySettings.objects.bulk_create(security_settings_to_create)
    SecuritySettings.objects.bulk_update(
        security_settings_to_update,
        ['allowed_ips', 'deleted'],
    )


def run_delete_business_security_settings(business_ids):
    security_settings = SecuritySettings.objects.filter(
        business_id__in=business_ids,
    )

    for security_setting in security_settings:
        security_setting.deleted = tznow()

    SecuritySettings.objects.bulk_update(
        security_settings,
        ['deleted'],
    )
