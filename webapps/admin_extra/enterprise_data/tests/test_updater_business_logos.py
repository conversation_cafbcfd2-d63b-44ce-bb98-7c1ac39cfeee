from io import BytesIO

import mock
import PIL.Image as PILImage
from django.test import TestCase

from webapps.admin_extra.enterprise_data.tests.test_common import (
    EnterpriseUpdateTestUtils,
)
from webapps.admin_extra.enterprise_data.updater_business_logos import (
    update_business_logos,
)
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image


class UpdateBusinessLogosTest(
    EnterpriseUpdateTestUtils,
    TestCase,
):

    def setUp(self):
        self.businesses = self.set_up_businesses(5, visible=False)

    @staticmethod
    def prepare_test_image():
        file = BytesIO()
        image = PILImage.new('RGBA', size=(50, 50), color=(155, 0, 0))
        image.save(file, 'png')
        file.name = 'log.png'
        file.seek(0)
        return file

    def assert_logo_for_businesses_added_correctly(self, business_ids):
        for business in self.businesses:
            business_id = business.id

            if business_id in business_ids:
                image = Image.objects.filter(business_id=business_id).first()
                self.assertEqual(image.category, ImageTypeEnum.LOGO)
            else:
                self.assertFalse(
                    Image.objects.filter(
                        business_id=business_id,
                    ).exists()
                )

    def run_update_business_logos(self, business_ids):
        with mock.patch(
            'webapps.images.mixins.PhotoHelperMixin.upload_file_obj',
            return_value='',
        ):
            update_business_logos(self.prepare_test_image(), business_ids)

            self.assert_logo_for_businesses_added_correctly(business_ids)

    def test_update_business_logos_empty_business_ids(self):
        business_ids = []

        self.run_update_business_logos(business_ids)

    def test_update_business_logos_with_one_business_id(self):
        business_ids = [self.businesses[0].id]

        self.run_update_business_logos(business_ids)

    def test_update_business_logos_with_two_business_ids(self):
        business_ids = [self.businesses[0].id, self.businesses[4].id]

        self.run_update_business_logos(business_ids)

    def test_update_business_logos_with_five_business_ids(self):
        business_ids = [business.id for business in self.businesses]

        self.run_update_business_logos(business_ids)
