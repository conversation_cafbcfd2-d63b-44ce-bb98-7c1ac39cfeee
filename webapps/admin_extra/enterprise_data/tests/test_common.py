from dateutil.relativedelta import relativedelta
from model_bakery import baker

from webapps.admin_extra.enterprise_data.common import (
    CommonAttrEnum,
    ResourceAttrEnum,
    ResourceUpdateEntry,
    ServiceAttrEnum,
    ServiceMappingUpdateEntry,
    ServiceUpdateEntry,
)
from webapps.business.models import (
    Business,
    Resource,
)
from webapps.user.models import (
    User,
    UserProfile,
)


class EnterpriseUpdateTestUtils:

    def setUp(self):  # pylint: disable=invalid-name
        self.service_ordinal_counter = 0
        self.resource_ordinal_counter = 0

    @classmethod
    def get_current_businesses(cls, businesses, fields):
        business_ids = [business.id for business in businesses]

        return {
            business.id: business
            for business in Business.objects.filter(id__in=business_ids).only(*fields)
        }

    @classmethod
    def get_current_resources(cls, resources):
        resource_ids = [resource.id for resource in resources]

        return Resource.objects.filter(id__in=resource_ids)

    @classmethod
    def handle_minutes(cls, minutes):
        if minutes:
            return relativedelta(minutes=minutes)

        return relativedelta()

    def prepare_resource_update_entry(
        self,
        action,
        booksy_business_id=None,
        booksy_resource_id=None,
        name=None,
        phone=None,
        email=None,
        resource_id=None,
        service_numbers=None,
        resource_type=Resource.STAFF_ACCESS_LEVEL_ADVANCED,
        visibility=True,
    ):
        self.resource_ordinal_counter += 1

        result = ResourceUpdateEntry()
        setattr(result, CommonAttrEnum.ORDINAL, self.resource_ordinal_counter)
        setattr(result, CommonAttrEnum.ACTION_TYPE, action)
        setattr(result, CommonAttrEnum.BOOKSY_BUSINESS_ID, booksy_business_id)
        setattr(result, CommonAttrEnum.BOOKSY_RESOURCE_ID, booksy_resource_id)
        setattr(result, ResourceAttrEnum.NAME, name)
        setattr(result, ResourceAttrEnum.PHONE_NUMBER, phone)
        setattr(result, ResourceAttrEnum.EMAIL, email)
        setattr(result, ResourceAttrEnum.ID, resource_id)
        setattr(result, ResourceAttrEnum.SERVICE_NUMBERS, service_numbers)
        setattr(result, ResourceAttrEnum.TYPE, resource_type)
        setattr(result, ResourceAttrEnum.VISIBILITY, visibility)
        return result

    @classmethod
    def prepare_service_mapping_entry(cls, number, name, booksy_id):
        result = ServiceMappingUpdateEntry()
        setattr(result, ServiceAttrEnum.NUMBER, number)
        setattr(result, ServiceAttrEnum.NAME, name)
        setattr(result, ServiceAttrEnum.BOOKSY_ID, booksy_id)
        return result

    def prepare_service_update_entry(
        self,
        action,
        find_by_id=None,
        find_by_name=None,
        order=None,
        name=None,
        category=None,
        duration=None,
        padding_time=None,
        interval=None,
    ):
        self.service_ordinal_counter += 1

        result = ServiceUpdateEntry()
        setattr(result, ServiceAttrEnum.ORDINAL, self.service_ordinal_counter)
        setattr(result, CommonAttrEnum.ACTION_TYPE, action)
        setattr(result, ServiceAttrEnum.FIND_BY_ID, find_by_id)
        setattr(result, ServiceAttrEnum.FIND_BY_NAME, find_by_name)
        setattr(result, ServiceAttrEnum.ORDER, order)
        setattr(result, ServiceAttrEnum.NAME, name)
        setattr(result, ServiceAttrEnum.CATEGORY, category)

        if not isinstance(duration, relativedelta) and duration is not None:
            duration = relativedelta(minutes=duration)
        setattr(result, ServiceAttrEnum.DURATION, duration)

        if not isinstance(padding_time, relativedelta) and (padding_time is not None):
            padding_time = relativedelta(minutes=padding_time)
        setattr(result, ServiceAttrEnum.PADDING_TIME, padding_time)

        if not isinstance(interval, relativedelta) and interval is not None:
            interval = relativedelta(minutes=interval)
        setattr(result, ServiceAttrEnum.INTERVAL_TIME, interval)

        return result

    @classmethod
    def set_up_businesses(cls, number_of_businesses, **kwargs):
        if 'owner' not in kwargs:
            owner = baker.make(User)
            baker.make(UserProfile, user=owner)
            kwargs['owner'] = owner
        return baker.make(Business, _quantity=number_of_businesses, **kwargs)

    @classmethod
    def set_up_resources(cls, number_of_resources):
        return baker.make(Resource, _quantity=number_of_resources)
