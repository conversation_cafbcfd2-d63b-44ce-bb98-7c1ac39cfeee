from unittest.mock import patch

from django.urls import reverse
from rest_framework.status import HTTP_200_OK

from lib.geocoding.here_maps import REVERSE_GEOCODE_URL
from webapps.admin_extra.tests import DjangoTestCase


class TestReverseGeocodeView(DjangoTestCase):
    GEO_DATA = {'latitude': 27.50067848, 'longitude': -99.51067828}

    def setUp(self):
        super().setUp()
        self.url = reverse('admin:reverse_geocode')
        self.login_admin()

    def test_reverse_geocode_passes_correct_args(self):
        with patch('lib.geocoding.here_maps.here_api_call') as here_api_call_mock:
            response = self.client.get(
                self.url,
                data={
                    'latitude': self.GEO_DATA['latitude'],
                    'longitude': self.GEO_DATA['longitude'],
                },
            )

        self.assertEqual(response.status_code, HTTP_200_OK)
        here_api_call_mock.assert_called_once_with(
            REVERSE_GEOCODE_URL,
            {'limit': 1, 'at': f"{self.GEO_DATA['latitude']:.6f},{self.GEO_DATA['longitude']:.6f}"},
        )
