from datetime import datetime, timedelta, timezone
from unittest.mock import ANY

import pytest
from django.contrib import messages
from django.urls import reverse
from model_bakery import baker
from pytest_django import asserts

from webapps.business.models import Business
from webapps.stats_and_reports.report_keys import ReportKeys
from webapps.stats_and_reports.reports.time_data import TimeScopeType


class TestDownloadStatisticsReportsView:
    MAGIC_BYTES_XLSX = b"\x50\x4b\x03\x04"  # actually it's ZIP but XLSX is ZIP-based
    REPORT_KEY_FORM_FULFILLED = ReportKeys.SalesSummary
    REPORT_KEY_FORM_UNFULFILLED = ReportKeys.StaffPerformancePerStaffer

    @pytest.fixture(autouse=True)
    def setup(self, admin_user, client):
        client.force_login(admin_user)

        self.today = datetime.now(timezone.utc).date()

        self.client = client
        self.user = admin_user
        self.url = reverse("admin:download_reports")

        self.provider_business = baker.make(Business)

    def test_when_form_invalid_then_no_message_visible(self):
        resp = self._submit(report_key="this report key does not exist")
        assert resp.status_code == 200
        assert self._get_messages(resp) == []
        asserts.assertTemplateUsed(resp, ANY)

    def test_when_report_data_fulfilled_with_form_then_report_generated(self):
        resp = self._submit(report_key=self.REPORT_KEY_FORM_FULFILLED)
        assert resp.status_code == 200
        assert resp.content.startswith(self.MAGIC_BYTES_XLSX)
        asserts.assertTemplateNotUsed(resp, ANY)

    def test_when_report_data_not_fulfilled_with_form_then_message_visible(self):
        message = ("Unsupported report", "warning")

        resp = self._submit(report_key=self.REPORT_KEY_FORM_UNFULFILLED)

        assert resp.status_code == 200
        assert self._get_messages(resp) == [message]
        asserts.assertTemplateUsed(resp, ANY)

    def _submit(
        self,
        *,
        report_key: ReportKeys | str,
    ):
        return self.client.post(
            self.url,
            data={
                "report_key": (
                    report_key.value if isinstance(report_key, ReportKeys) else report_key
                ),
                "business_id": self.provider_business.id,
                "date_from": (self.today - timedelta(days=7)).isoformat(),
                "date_till": self.today.isoformat(),
                "time_span": TimeScopeType.LAST_7_DAYS.value,
            },
            follow=True,
        )

    @staticmethod
    def _get_messages(resp) -> list[tuple[str, str]]:
        return [(m.message, m.tags) for m in messages.get_messages(resp.wsgi_request)]
