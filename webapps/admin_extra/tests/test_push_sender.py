from unittest.mock import patch

from django.test import TestCase
from model_bakery import baker

from webapps.admin_extra.tasks.push_and_notification import (
    get_push_receivers,
    push_sender_task,
)
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.notification.models import (
    Reciever,
    UserNotification,
)
from webapps.user.baker_recipes import customer_profile, user_recipe


class PushSenderTestCase(TestCase):
    @classmethod
    def setUpTestData(cls) -> None:
        cls.mapping = [
            [
                (UserNotification.PUSH_NOTIFICATION, [Reciever.ANDROID]),
            ],
            [
                (UserNotification.PUSH_NOTIFICATION, [Reciever.ANDROID, Reciever.IOS]),
            ],
            [
                (UserNotification.PUSH_NOTIFICATION, [Reciever.ANDROID]),
                (UserNotification.EMAIL_NOTIFICATION, [Reciever.ANDROID]),
            ],
            [
                (UserNotification.PUSH_NOTIFICATION, [Reciever.ANDROID]),
            ],
            [
                (UserNotification.SMS_NOTIFICATION, [Reciever.IOS]),
                (UserNotification.PUSH_NOTIFICATION, [Reciever.IOS]),
            ],
        ]
        cls.users = user_recipe.make(_quantity=len(cls.mapping))
        cls.user_ids = [user.id for user in cls.users]
        cls.androids = 0
        cls.ios_devices = 0
        for user, user_mapping in zip(cls.users, cls.mapping):
            profile = customer_profile.make(user=user)
            for notification_type, devices in user_mapping:
                user_notification = baker.make(
                    'notification.UserNotification',
                    type=notification_type,
                    profile=profile,
                )
                for device in devices:
                    if notification_type == UserNotification.PUSH_NOTIFICATION:
                        cls.androids += device == Reciever.ANDROID
                        cls.ios_devices += device == Reciever.IOS
                    baker.make(
                        'notification.Reciever',
                        customer_notifications=user_notification,
                        device=device,
                    )

    @patch('webapps.admin_extra.admin_import_s3.AdminImporterS3.get_client')
    @patch('webapps.admin_extra.admin_import_s3.AdminImporterS3.download_fileobj')
    def test_get_push_receivers(self, download_fileobj_mock, get_client_mock):
        androids, _ = get_push_receivers(self.user_ids, dict(), Reciever.ANDROID)
        ios_devices, _ = get_push_receivers(self.user_ids, dict(), Reciever.IOS)

        self.assertEqual(len(androids), self.androids)
        self.assertEqual(len(ios_devices), self.ios_devices)

    @patch('webapps.admin_extra.admin_import_s3.AdminImporterS3.get_client')
    @patch('webapps.admin_extra.admin_import_s3.AdminImporterS3.download_fileobj')
    @patch('webapps.admin_extra.tasks.push_and_notification.send_push_notification')
    @patch('webapps.admin_extra.import_utils.strip_xlsx_data')
    @patch('webapps.admin_extra.tasks.push_and_notification.load_spreadsheet_with_pandas')
    def test_push_sender_task(
        self,
        load_spreadsheet_with_pandas_mock,
        strip_xlsx_data_mock,
        send_push_notification_mock,
        download_fileobj_mock,
        get_client_mock,
    ):
        NotificationHistoryDocument.tasks_clear()
        strip_xlsx_data_mock.return_value = [[idx] for idx in self.user_ids]
        message = 'test_message'
        email = '<EMAIL>'
        destination = 'some/destination'
        user_email = '<EMAIL>'
        push_sender_task(user_email, '', message, email, destination)
        total_devices = self.androids + self.ios_devices
        total_receivers = self._get_total_receivers(send_push_notification_mock)
        self.assertEqual(total_receivers, total_devices)
        self.assertEqual(
            NotificationHistoryDocument.task_count(),
            total_devices + 1,  # + 1 because of prepare_and_send_email()
        )

    @patch('webapps.admin_extra.admin_import_s3.AdminImporterS3.get_client')
    @patch('webapps.admin_extra.admin_import_s3.AdminImporterS3.download_fileobj')
    @patch('webapps.admin_extra.tasks.push_and_notification.send_push_notification')
    @patch('webapps.admin_extra.import_utils.strip_xlsx_data')
    @patch('webapps.admin_extra.tasks.push_and_notification.load_spreadsheet_with_pandas')
    def test_only_android_push_test(
        self,
        load_spreadsheet_with_pandas_mock,
        strip_xlsx_data_mock,
        send_push_notification_mock,
        download_fileobj_mock,
        get_client_mock,
    ):
        NotificationHistoryDocument.tasks_clear()
        # user with id 1 has 1 android and 1 ios device
        strip_xlsx_data_mock.return_value = [[self.user_ids[idx]] for idx in [0, 1, 2, 3]]
        message = 'test_message'
        email = '<EMAIL>'
        destination = 'some/destination'
        user_email = '<EMAIL>'
        push_sender_task(user_email, '', message, email, destination)
        total_receivers = self._get_total_receivers(send_push_notification_mock)
        self.assertEqual(total_receivers, 5)
        self.assertEqual(NotificationHistoryDocument.task_count(), 6)

    @patch('webapps.admin_extra.admin_import_s3.AdminImporterS3.get_client')
    @patch('webapps.admin_extra.admin_import_s3.AdminImporterS3.download_fileobj')
    @patch('webapps.admin_extra.tasks.push_and_notification.send_push_notification')
    @patch('webapps.admin_extra.import_utils.strip_xlsx_data')
    @patch('webapps.admin_extra.tasks.push_and_notification.load_spreadsheet_with_pandas')
    def test_only_ios_push_test(
        self,
        load_spreadsheet_with_pandas_mock,
        strip_xlsx_data_mock,
        send_push_notification_mock,
        download_fileobj_mock,
        get_client_mock,
    ):
        NotificationHistoryDocument.tasks_clear()
        # user with id 1 has 1 android and 1 ios device
        strip_xlsx_data_mock.return_value = [[self.user_ids[idx]] for idx in [1, 4]]
        message = 'test_message'
        email = '<EMAIL>'
        destination = 'some/destination'
        user_email = '<EMAIL>'
        push_sender_task(user_email, '', message, email, destination)
        total_receivers = self._get_total_receivers(send_push_notification_mock)
        self.assertEqual(total_receivers, 3)
        self.assertEqual(NotificationHistoryDocument.task_count(), 4)

    @patch('webapps.admin_extra.admin_import_s3.AdminImporterS3.get_client')
    @patch('webapps.admin_extra.admin_import_s3.AdminImporterS3.download_fileobj')
    @patch('webapps.admin_extra.tasks.push_and_notification.send_push_notification')
    @patch('webapps.admin_extra.import_utils.strip_xlsx_data')
    @patch('webapps.admin_extra.tasks.push_and_notification.load_spreadsheet_with_pandas')
    def test_send_empty_file(
        self,
        load_spreadsheet_with_pandas_mock,
        strip_xlsx_data_mock,
        send_push_notification_mock,
        download_fileobj_mock,
        get_client_mock,
    ):
        NotificationHistoryDocument.tasks_clear()
        strip_xlsx_data_mock.return_value = []
        message = 'test_message'
        email = '<EMAIL>'
        destination = 'some/destination'
        user_email = '<EMAIL>'
        push_sender_task(user_email, '', message, email, destination)
        total_receivers = self._get_total_receivers(send_push_notification_mock)
        self.assertEqual(total_receivers, 0)
        self.assertEqual(NotificationHistoryDocument.task_count(), 1)

    @patch('webapps.admin_extra.admin_import_s3.AdminImporterS3.get_client')
    @patch('webapps.admin_extra.admin_import_s3.AdminImporterS3.download_fileobj')
    @patch('webapps.admin_extra.tasks.push_and_notification.send_push_notification')
    @patch('lib.email.tasks.send_email_task.delay')
    @patch('webapps.admin_extra.import_utils.strip_xlsx_data')
    @patch('webapps.admin_extra.tasks.push_and_notification.load_spreadsheet_with_pandas')
    def test_email_body(
        self,
        load_spreadsheet_with_pandas_mock,
        strip_xlsx_data_mock,
        send_email_mock,
        send_push_notification_mock,
        download_fileobj_mock,
        get_client_mock,
    ):
        NotificationHistoryDocument.tasks_clear()
        strip_xlsx_data_mock.return_value = [[idx] for idx in self.user_ids]
        message = 'test_message'
        email = '<EMAIL>'
        destination = 'some/destination'
        user_email = '<EMAIL>'
        push_sender_task(user_email, '', message, email, destination)
        self.assertEqual(send_email_mock.call_count, 1)
        body = send_email_mock.call_args[0][0]['content']
        expected_body = (
            'Successfully sent mass customer push, '
            'scheduled for 5 devices, '
            'sender <EMAIL>'
        )
        self.assertEqual(body, expected_body)

    @staticmethod
    def _get_total_receivers(send_push_notification_mock):
        return sum(len(call[1]['receivers']) for call in send_push_notification_mock.call_args_list)
