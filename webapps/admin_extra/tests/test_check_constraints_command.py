import pytest
from django.conf import settings
from django.db import connection, models
from model_bakery import baker

from lib.models import ArchiveManager, ArchiveModel
from lib.tools import tznow
from webapps.admin_extra.management.commands.check_constraints import (
    BrokenConstraintException,
    Command,
)
from webapps.business.models import Business, Resource
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.schedule.models import ResourceHours
from webapps.user.models import User


@pytest.mark.django_db
def test_check_constraints_no_data():
    Command().handle()


@pytest.mark.django_db
def test_check_constraints_with_valid_data():
    business = baker.make(Business)
    user = baker.make(User)
    baker.make(BusinessCustomerInfo, business=business, user=user)
    baker.make(BusinessCustomerInfo, business=business)
    baker.make(
        BusinessCustomerInfo,
        business=business,
        deleted=tznow(),
        user=user,
    )

    Command().handle()


@pytest.mark.django_db
def test_check_constraints_with_invalid_data_single_table():
    business = baker.make(Business)
    user = baker.make(User)
    __create_two_bcis_with_broken_constraint(business, user)
    with pytest.raises(BrokenConstraintException) as exc:
        Command().handle()
    assert str(exc.value) == (
        'Found some db models with broken constraints: webapps.business.models.bci '
        'BusinessCustomerInfo constraint name: business_user_unique'
    )


@pytest.mark.django_db
def test_check_constraints_with_invalid_data_two_tables():
    business = baker.make(Business)
    __create_two_bcis_with_broken_constraint(business, baker.make(User))
    __create_two_resource_hours_with_broken_constraint(business)
    with pytest.raises(BrokenConstraintException) as exc:
        Command().handle()
    assert str(exc.value) == (
        'Found some db models with broken constraints: webapps.business.models.bci'
        ' BusinessCustomerInfo constraint name: business_user_unique, webapps.schedule.models'
        ' ResourceHours constraint name: unique_resource_hours_valid_from'
    )


class DummyModelConstraintsCheckTest(ArchiveModel):
    name = models.CharField(max_length=256)
    value = models.IntegerField()
    some_value = models.IntegerField()

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=('name', 'value'),
                name='unique_name_and_value_for_non_deleted',
            ),
        ]
        unique_together = ('value', 'some_value')

    objects = ArchiveManager()
    all_objects = models.Manager()


@pytest.mark.django_db
def test_check_with_overrided_objects_manager():
    now = tznow()
    __remove_constraint_from_test_db(
        DummyModelConstraintsCheckTest,
        'unique_name_and_value_for_non_deleted',
    )

    baker.make(
        DummyModelConstraintsCheckTest,
        name='xxx',
        value=3,
        deleted=now,
    )
    baker.make(
        DummyModelConstraintsCheckTest,
        name='xxx',
        value=3,
    )
    baker.make(
        DummyModelConstraintsCheckTest,
        name='xxx',
        value=3,
        deleted=now,
    )

    with pytest.raises(BrokenConstraintException):
        Command().handle()


@pytest.mark.django_db
def test_check_unique_together():
    __remove_constraint_from_test_db(
        DummyModelConstraintsCheckTest,
        'unique_name_and_value_for_non_deleted',
    )

    cursor = connection.cursor()
    table_name = DummyModelConstraintsCheckTest._meta.db_table
    unique_together_constraint_name = _get_unique_together_constraint_name(
        django_db_model=DummyModelConstraintsCheckTest,
        unique_together_tuple=('value', 'some_value'),
    )
    cursor.execute(
        f"""
        ALTER TABLE "{table_name}" DROP CONSTRAINT "{unique_together_constraint_name}"
    """
    )

    baker.make(
        DummyModelConstraintsCheckTest,
        name='xxx',
        value=3,
        some_value=3,
    )
    baker.make(
        DummyModelConstraintsCheckTest,
        name='yyy',
        value=3,
        some_value=3,
    )

    with pytest.raises(BrokenConstraintException) as err:
        Command().handle()
    assert str(err.value) == (
        "Found some db models with broken constraints:"
        " webapps.admin_extra.tests.test_check_constraints_command "
        "DummyModelConstraintsCheckTest constraint name: unique_together ('value', 'some_value')"
    )


@pytest.mark.django_db
def test_check_unique_primary_key():
    cursor = connection.cursor()
    table_name = DummyModelConstraintsCheckTest._meta.db_table
    constraint_name = 'admin_extra_dummymodelconstraintschecktest_pkey'
    cursor.execute(
        f"""
        ALTER TABLE "{table_name}" DROP CONSTRAINT "{constraint_name}"
    """
    )
    baker.make(DummyModelConstraintsCheckTest, id=3)
    DummyModelConstraintsCheckTest.objects.create(id=3, created=tznow(), value=1, some_value=4)
    assert DummyModelConstraintsCheckTest.objects.all().count() == 2

    with pytest.raises(BrokenConstraintException) as err:
        Command().handle()
    assert str(err.value) == (
        "Found some db models with broken constraints:"
        " webapps.admin_extra.tests.test_check_constraints_command"
        " DummyModelConstraintsCheckTest for field 'id' which should be unique."
    )


def __create_two_bcis_with_broken_constraint(business, user):
    first_bci = baker.prepare(BusinessCustomerInfo, business=business, user=user)
    second_bci = baker.prepare(BusinessCustomerInfo, business=business, user=user)
    __remove_constraint_from_test_db(BusinessCustomerInfo, 'business_user_unique')
    first_bci.save()
    second_bci.save()


def __create_two_resource_hours_with_broken_constraint(business):
    resource_0 = baker.make(Resource, business=business)
    today_date = tznow().date()
    resource_hours_0 = baker.prepare(
        ResourceHours,
        resource=resource_0,
        business=business,
        valid_from=today_date,
    )
    resource_hours_1 = baker.prepare(
        ResourceHours,
        resource=resource_0,
        business=business,
        valid_from=today_date,
    )
    __remove_constraint_from_test_db(ResourceHours, 'unique_resource_hours_valid_from')
    resource_hours_0.save()
    resource_hours_1.save()


def __get_constraint_by_name(db_model, constraint_name):
    for constraint in db_model._meta.constraints:
        if constraint.name == constraint_name:
            return constraint


def __remove_constraint_from_test_db(db_model, constraint_name):
    __check_deployment_level()
    cursor = connection.cursor()
    specific_constraint = __get_constraint_by_name(db_model, constraint_name)
    remove_sql_obj = specific_constraint.remove_sql(
        model=db_model,
        schema_editor=connection.schema_editor(),
    )
    cursor.execute(str(remove_sql_obj))


def __check_deployment_level():
    if not settings.PYTEST or settings.LIVE_DEPLOYMENT:
        raise RuntimeError(
            "It's not your local instance! Are you mad!? Use this feature during tests only!"
        )


def _get_unique_together_constraint_name(django_db_model, unique_together_tuple):
    table_name = django_db_model._meta.db_table
    cursor = connection.cursor()
    cursor.execute(
        f"""
    SELECT con.*
       FROM pg_catalog.pg_constraint con
            INNER JOIN pg_catalog.pg_class rel
                       ON rel.oid = con.conrelid
            INNER JOIN pg_catalog.pg_namespace nsp
                       ON nsp.oid = connamespace
       WHERE rel.relname = '{table_name}';
    """
    )
    constaints_names = [x[1] for x in cursor.cursor.fetchall()]
    for constrain_name in constaints_names:
        valid = True
        for value in unique_together_tuple:
            if value not in constrain_name:
                valid = False
        if valid:
            return constrain_name
    return None
