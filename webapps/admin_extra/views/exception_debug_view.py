import logging

from webapps.admin_extra.custom_permissions_classes import TemplateView

_logger = logging.getLogger('booksy.pwa3log')
a_logger = logging.getLogger('booksy')


class ExceptionDebugTestView(TemplateView):
    view_base_name = 'exception_debug_view'
    permission_required = ()
    template_name = 'admin/custom_views/generic_form_template.html'

    def get(self, request, *args, **kwargs):
        _logger.error('this is a test of logging admin system logger')
        a_logger.error('this is a test of logging admin system alogger')
        raise NotImplementedError('this is a test of admin logging system')
