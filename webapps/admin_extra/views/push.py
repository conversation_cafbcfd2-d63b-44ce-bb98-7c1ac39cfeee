from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
)
from webapps.admin_extra.forms.push import PushSendForm
from webapps.admin_extra.tasks.push_and_notification import push_sender_task
from webapps.admin_extra.views.utils import CeleryFileTaskMixin
from webapps.user.groups import GroupName


class PushSendView(GroupPermissionMixin, CeleryFileTaskMixin, FormView):
    form_class = PushSendForm
    template_name = 'admin/custom_views/push_sender.html'
    import_directory = 'push_sender'
    import_file_names = ('import_file',)
    celery_task = push_sender_task
    celery_task_kwargs = ('message', 'email', 'destination')
    success_url = 'push_sender'
    permission_required = ()
    groups = GroupName.CUSTOM_PUSH_ALLOWED

    def post(self, request, *args, **kwargs):
        self.celery_task = self.celery_task.s(request.user.email)
        return super().post(request, *args, **kwargs)
