import io
import logging

import numpy as np
import pandas as pd
from django.conf import settings

from lib.celery_tools import (
    celery_task,
)
from lib.email import (
    send_email,
)
from lib.tools import tznow
from webapps.admin_extra.tasks.utils import BaseCSVLoader
from webapps.admin_extra.tasks.switch_merchants_to_billing import SwitchBusinessResult
from webapps.business.models import Business, ReTrialAttempt
from webapps.segment.tasks import analytics_business_re_trial_eligible_task
from webapps.user.models import User


_logger = logging.getLogger('booksy.admin_extra')

CSV_READ_FIELD_NAMES = ('business_id',)

CSV_WRITE_FIELDS = list(CSV_READ_FIELD_NAMES) + ['result', 'message']

IMPORT_NAME = f'Business ReTrial switch [{settings.API_COUNTRY}]'


class CSVLoader(BaseCSVLoader):
    dtype_mapping = {
        'business_id': int,
    }
    max_records_in_batch = 500


def business_validate(
    business: Business,
    active_retrial_attempt: bool,
) -> SwitchBusinessResult:
    if business is None:
        return SwitchBusinessResult(message='Business does not exist')

    if business.status != Business.Status.TRIAL_BLOCKED:
        return SwitchBusinessResult(
            message=f'Business status different from: TRIAL_BLOCKED '
            f'(current status: {business.get_status_display()})'
        )

    if active_retrial_attempt:
        return SwitchBusinessResult(message='Business already has an active ReTrial record')
    return SwitchBusinessResult(success=True)


@celery_task
def mass_business_retrial_switch_task(
    file_path: str,
    email: str,
    operator_id: int,
    dry_run: bool = False,
) -> None:
    loading_dt = tznow().strftime('%d_%m_%Y_%H_%M_%S')
    dry_run_info = '[Dry Run!]' if dry_run else ''

    try:
        data_frames = CSVLoader.load_from_file(file_path)
        input_records = CSVLoader.clean_data_frames(data_frames, business_index=True)

    except Exception as err:  # pylint: disable=broad-except
        send_email(
            to_addr=email,
            body=f'{IMPORT_NAME}{dry_run_info} error: {err}',
            subject=f'{IMPORT_NAME}{dry_run_info} - error loading file {loading_dt}',
        )
        return

    operator = User.objects.get(id=operator_id)
    business_ids = input_records.keys()
    businesses = {
        business.id: business for business in Business.objects.filter(pk__in=business_ids)
    }
    active_retrial_attempts = ReTrialAttempt.objects.filter(
        business_id__in=business_ids,
        used__isnull=True,
    ).values_list('business_id', flat=True)

    to_create = []
    for business_id, row in input_records.items():
        result = business_validate(
            business=businesses.get(business_id),
            active_retrial_attempt=business_id in active_retrial_attempts,
        )

        if not result.success:
            row.update(
                {
                    'result': 'ERROR',
                    'message': result.message,
                }
            )
            continue

        to_create.append(ReTrialAttempt(business_id=business_id, operator=operator))

    if to_create and not dry_run:
        ReTrialAttempt.objects.bulk_create(to_create)
        for re_trial_attempt in to_create:
            if re_trial_attempt.business.status == Business.Status.TRIAL_BLOCKED:
                analytics_business_re_trial_eligible_task.delay(
                    context={'business_id': re_trial_attempt.business_id}
                )

    df_output = pd.DataFrame.from_dict(input_records, orient='index', columns=CSV_WRITE_FIELDS)
    df_output = df_output.replace(np.nan, 'NULL')

    writer = io.BytesIO()
    writer.write(df_output.to_csv(index=False).encode())
    writer.seek(0)

    send_email(
        to_addr=email,
        body=f'{IMPORT_NAME}{dry_run_info} report in attachment',
        subject=f'{IMPORT_NAME}{dry_run_info} - report {loading_dt}',
        attachments=[
            (
                f'business_retrial_switch_{settings.API_COUNTRY}_{loading_dt}.csv',
                writer.getvalue(),
                'text/csv',
            )
        ],
    )
