import csv
import io
from unittest import TestCase
from unittest.mock import MagicMock, patch

import pandas as pd
import pytest
from django.core import mail
from django.test import override_settings
from parameterized import parameterized

from webapps.admin_extra.tasks.mass_business_old_fizjo_disable import (
    CSVLoader,
    business_validate,
    mass_business_old_fizjo_disable_task,
)
from webapps.admin_extra.tasks.tests.test_subscription_buyer_update_task import (
    get_csv_attachment_data,
)
from webapps.admin_extra.tasks.utils import (
    CSVLoaderMaxRecordsInBatchError,
    CSVLoaderParseError,
    DataFrameUniqueIndexError,
)

from webapps.business.baker_recipes import business_recipe
from webapps.business.enums import CustomData

from webapps.business.models import Business


def csv_load_mocked(rows: list[dict]):
    output = io.StringIO()
    fieldnames = ['business_id']
    writer = csv.DictWriter(output, fieldnames=fieldnames, delimiter=',')
    writer.writeheader()
    writer.writerows(rows)

    output.seek(0)
    return CSVLoader.read_csv(output)


@pytest.mark.django_db
class CSVLoaderTestCase(TestCase):
    @patch('webapps.admin_extra.tasks.utils.AdminImporterS3.open_file', MagicMock())
    @patch.object(CSVLoader, 'read_csv')
    def test_load_too_many_records_data_data(self, data_frame_mock):
        data_frame_mock.return_value = pd.DataFrame(range(CSVLoader.max_records_in_batch + 1))

        with self.assertRaisesRegex(CSVLoaderMaxRecordsInBatchError, 'There is more than*'):
            CSVLoader.load_from_file('file')

    @parameterized.expand([('NULL',), ('abc',)])
    @patch.object(CSVLoader, 'load_from_file', csv_load_mocked)
    def test_load_data_when_parsing_error(self, entry_data):
        with self.assertRaisesRegex(CSVLoaderParseError, 'CSV loading error.*'):
            CSVLoader.load_from_file(
                [
                    {
                        'business_id': entry_data,
                    }
                ]
            )

    @patch.object(CSVLoader, 'load_from_file', csv_load_mocked)
    def test_load_data_when_index_error(self):
        with self.assertRaisesRegex(DataFrameUniqueIndexError, '.*Duplicated Business ID.*'):
            data_frames = CSVLoader.load_from_file(
                [
                    {
                        'business_id': '11',
                    },
                    {
                        'business_id': '11',
                    },
                ]
            )

            CSVLoader.clean_data_frames(data_frames, business_index=True)

    @patch.object(CSVLoader, 'load_from_file', csv_load_mocked)
    def test_clean_data_frames_ok(self):
        data_frames = CSVLoader.load_from_file(
            [
                {
                    'business_id': '11',
                },
                {
                    'business_id': '12',
                },
            ]
        )

        records = CSVLoader.clean_data_frames(data_frames, business_index=True)

        self.assertEqual(len(records), 2)

        self.assertEqual(records[11]['result'], 'SUCCESS')
        self.assertFalse(records[11]['message'])

        self.assertEqual(records[12]['result'], 'SUCCESS')
        self.assertFalse(records[12]['message'])


class BusinessMixin:
    def setUp(self):  # pylint: disable=invalid-name
        super().setUp()
        self.business1_physio_enabled = business_recipe.make()
        self.business1_physio_enabled.custom_data[CustomData.PHYSIOTHERAPY_ENABLED] = True
        self.business1_physio_enabled.custom_data[CustomData.DISABLE_GOOGLE_RESERVE] = True
        self.business1_physio_enabled.save()
        self.business2_physio_enabled = business_recipe.make()
        self.business2_physio_enabled.custom_data[CustomData.PHYSIOTHERAPY_ENABLED] = True
        self.business2_physio_enabled.save()
        self.business3_physio_enabled = business_recipe.make()
        self.business3_physio_enabled.custom_data[CustomData.PHYSIOTHERAPY_ENABLED] = True
        self.business3_physio_enabled.save()
        self.business4_physio_enabled = business_recipe.make()
        self.business4_physio_enabled.custom_data[CustomData.PHYSIOTHERAPY_ENABLED] = True
        self.business4_physio_enabled.save()
        self.business5_physio_disabled = business_recipe.make()
        self.business5_physio_disabled.custom_data[CustomData.PHYSIOTHERAPY_ENABLED] = False
        self.business5_physio_disabled.save()
        self.business6_physio_not_set = business_recipe.make()


@pytest.mark.django_db
class BusinessValidationTestCase(BusinessMixin, TestCase):
    def test_business_is_null(self):
        result = business_validate(business=None)
        self.assertFalse(result.success)
        self.assertEqual(result.message, 'Business does not exist')

    def test_business_has_disabled_physiotherapy(self):
        result = business_validate(business=self.business5_physio_disabled)
        self.assertFalse(result.success)
        self.assertEqual(
            result.message, 'Business already has an inactive Old Fizjo (physiotherapy_enabled)'
        )

    def test_business_has_not_set_physiotherapy(self):
        result = business_validate(business=self.business6_physio_not_set)
        self.assertFalse(result.success)
        self.assertEqual(
            result.message, 'Business does not have Old Fizjo (physiotherapy_enabled) set'
        )

    def test_ok(self):
        result = business_validate(business=self.business1_physio_enabled)

        self.assertTrue(result.success)


@pytest.mark.django_db
class MassBusinessOldFizjoDisableTaskTestCase(BusinessMixin, TestCase):
    @patch.object(CSVLoader, 'load_from_file')
    def test_data_frame_unique_index_error(self, load_mocked):
        load_mocked.return_value = csv_load_mocked(
            [
                {
                    'business_id': self.business1_physio_enabled.id,
                },
                {
                    'business_id': self.business1_physio_enabled.id,
                },
            ]
        )

        mass_business_old_fizjo_disable_task.run(
            file_path='path',
            email='<EMAIL>',
        )

        self.assertIn('error loading file', mail.outbox[0].subject)
        self.assertIn('Duplicated Business ID. This row must be unique!', mail.outbox[0].body)

    @patch.object(CSVLoader, 'load_from_file')
    def test_validation_errors(self, load_mocked):
        load_mocked.return_value = csv_load_mocked(
            [
                {
                    'business_id': self.business5_physio_disabled.id,
                },
                {
                    'business_id': self.business6_physio_not_set.id,
                },
                {
                    'business_id': 1234,
                },
            ]
        )
        mass_business_old_fizjo_disable_task.run(
            file_path='path',
            email='<EMAIL>',
        )
        self.assertIn('report', mail.outbox[0].subject)
        self.assertIn('report in attachment', mail.outbox[0].body)

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(4, len(report))
        self.assertEqual('ERROR', report[1][-2])
        self.assertEqual(
            'Business already has an inactive Old Fizjo (physiotherapy_enabled)',
            report[1][-1],
        )
        self.assertEqual('ERROR', report[2][-2])
        self.assertEqual(
            'Business does not have Old Fizjo (physiotherapy_enabled) set',
            report[2][-1],
        )
        self.assertEqual('ERROR', report[3][-2])
        self.assertEqual(
            'Business does not exist',
            report[3][-1],
        )

    @patch.object(CSVLoader, 'load_from_file')
    def test_business_other_custom_data_not_affected(self, load_mocked):
        load_mocked.return_value = csv_load_mocked(
            [
                {
                    'business_id': self.business1_physio_enabled.id,
                },
            ]
        )
        mass_business_old_fizjo_disable_task.run(
            file_path='path',
            email='<EMAIL>',
            dry_run=False,
        )
        self.business1_physio_enabled.refresh_from_db()
        self.assertFalse(
            self.business1_physio_enabled.custom_data[CustomData.PHYSIOTHERAPY_ENABLED]
        )
        self.assertTrue(
            self.business1_physio_enabled.custom_data[CustomData.DISABLE_GOOGLE_RESERVE]
        )

    @parameterized.expand(
        [
            (True,),
            (False,),
        ],
    )
    @override_settings(SAVE_HISTORY=True)
    @patch.object(CSVLoader, 'load_from_file')
    def test_run_the_entire_process_ok(self, dry_run, load_mocked):

        load_mocked.return_value = csv_load_mocked(
            [
                {
                    'business_id': self.business1_physio_enabled.id,
                },
                {
                    'business_id': self.business2_physio_enabled.id,
                },
                {
                    'business_id': self.business3_physio_enabled.id,
                },
                {
                    'business_id': self.business4_physio_enabled.id,
                },
                {
                    'business_id': self.business5_physio_disabled.id,
                },
                {
                    'business_id': self.business6_physio_not_set.id,
                },
            ]
        )

        mass_business_old_fizjo_disable_task.run(
            file_path='path',
            email='<EMAIL>',
            dry_run=dry_run,
        )
        if dry_run:
            self.assertIn('[Dry Run!]', mail.outbox[0].subject)
            self.assertIn('[Dry Run!]', mail.outbox[0].body)

        self.assertIn('report', mail.outbox[0].subject)
        self.assertIn('report in attachment', mail.outbox[0].body)

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 7)

        self.assertEqual('SUCCESS', report[1][-2])
        self.assertEqual('SUCCESS', report[2][-2])
        self.assertEqual('SUCCESS', report[3][-2])
        self.assertEqual('SUCCESS', report[4][-2])
        self.assertEqual('ERROR', report[5][-2])
        self.assertEqual('ERROR', report[6][-2])

        if not dry_run:
            self.assertTrue(
                Business.objects.get(
                    pk=self.business1_physio_enabled.id, custom_data__physiotherapy_enabled=False
                )
            )
            self.assertTrue(
                Business.objects.get(
                    pk=self.business5_physio_disabled.id, custom_data__physiotherapy_enabled=False
                )
            )
