import os
from datetime import datetime

import pytest
from dateutil import tz
from django.conf import settings
from django.core.files import File
from django.db.models import Q
from django.test import TestCase as DjangoTestCase
from django.test.utils import override_settings
from mock import patch
from model_bakery import baker

from country_config.enums import Country
from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature import VersumImporterSplitAppointmentImportFlag
from lib.tests.utils import override_feature_flag
from webapps.admin_extra.tasks import (
    ServiceImporter,
    BookingImporter,
)
from webapps.admin_extra.tasks.versum import import_data_versum_task, versum_file_parser_task
from webapps.admin_extra.tests.versum_consts import (
    CustomerImport,
    ServicesImport,
    BookingImport,
    ComplexImport,
)
from webapps.admin_extra.versum_data_parser import (
    load_versum_customers,
    load_versum_services,
    load_versum_bookings,
    Specification,
)
from webapps.booking.models import (
    Appointment,
    BookingSources,
    SubBooking,
)
from webapps.business.enums import PriceType
from webapps.business_customer_info.bci_import import BCIImporter
from webapps.business.models import (
    Business,
    Resource,
    Service,
    ServiceCategory,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.models.category import BusinessCategory
from webapps.notification.enums import ScheduleState
from webapps.notification.models import NotificationSchedule
from webapps.notification.scenarios.base import reminder_format
from webapps.pos.models import (
    TaxRate,
    Transaction,
)
from webapps.pos.tools import create_pos
from webapps.structure.models import Region
from webapps.user.models import UnsubscribedEmail

FILE_DATA_PATH = os.path.join(
    settings.PROJECT_PATH,
    'data',
    'tests',
    'versum_data',
)
NUMBER_OBFUSCATED_BOOKINGS = 2
TEST_DATETIME = datetime.strptime('2016-05-01', '%Y-%m-%d')


def create_zip_codes():
    baker.make(Region, name='02-321', type='zip')
    baker.make(Region, name='02-112', type='zip')
    baker.make(Region, name='02-115', type='zip')


def loader(file_name, folder, load_fun, spec):
    file_path = os.path.join(
        FILE_DATA_PATH,
        folder,
        file_name,
    )
    with open(file_path, 'rb') as file_buff:
        djangofile = File(file_buff)
        data = b''.join(djangofile.chunks())
        loaded_data = load_fun(data, spec)
    return loaded_data['rows']


# pylint: disable=protected-access
@pytest.mark.freeze_time(TEST_DATETIME)
@pytest.mark.django_db
@pytest.mark.usefixtures('default_pos_fixture')
class VersumImportTestCase(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.business = baker.make_recipe(
            'webapps.business.business_recipe',
        )
        self.owner = baker.make(
            Resource,
            visible=True,
            type=Resource.STAFF,
            business=self.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
            staff_user=self.business.owner,
            staff_email=self.business.owner.email,
        )

    @override_settings(API_COUNTRY=Country.PL)
    def test_customer_import(self):
        create_zip_codes()
        data = loader(
            'customers_1.xls',
            'customers',
            load_versum_customers,
            Specification.CUSTOMERS.value,
        )
        bci_importer = BCIImporter(self.business)
        bci_importer.import_customers(customers=data)
        bcis = BusinessCustomerInfo.objects.filter(business=self.business)

        assert bcis.count() == CustomerImport.NUMBER_VALID_CUSTOMERS

        assert (
            bcis.exclude(Q(cell_phone__isnull=True) | Q(cell_phone__exact='')).count()
            == CustomerImport.NUMBER_CORRECT_CELL_PHONE
        )

        assert (
            bcis.exclude(Q(email__isnull=True) | Q(email__exact='')).count()
            == CustomerImport.NUMBER_EMAILS
        )

        assert bcis.exclude(region_id__isnull=True).count() == CustomerImport.HAS_REGION

        assert bcis.exclude(birthday__isnull=True).count() == CustomerImport.HAS_BIRTHDAY

        assert bcis.exclude(discount=0).count() == CustomerImport.NUMBER_DISCOUNTS

        # cause one customer doesn't have email but was marked as unsubscribe
        assert UnsubscribedEmail.objects.count() == (CustomerImport.NUMBER_UNSUBSCRIBE - 1)

    @override_settings(API_COUNTRY=Country.PL)
    def test_customer_import_with_sketchy_birthdays(self):
        """
        Test importing customers with erroneous birthday dates.
        """
        create_zip_codes()
        data = loader(
            'sketchy_birthday_dates.xls',
            'customers',
            load_versum_customers,
            Specification.CUSTOMERS.value,
        )
        bci_importer = BCIImporter(self.business)
        bci_importer.import_customers(customers=data)
        bcis = BusinessCustomerInfo.objects.filter(business=self.business)

        assert bcis.count() == 5
        # 6 rows: 5 should be imported regardless of warnings, 2 birthday dates are sketchy
        # 1 row should not be imported because of no contact data

        warnings = []
        for customer in data:
            if 'warning' in customer:
                warning = customer['warning']
                warnings.append(warning)
        assert len(warnings) == 2
        assert (
            'Client was imported with some missing data. '
            '2024-13-32 - is invalid format of date! '
            'Please use native date format of this a spreadsheet.'
            'This client was imported without birthday' in warnings
        )
        assert (
            'Client was imported with some missing data. '
            '123456 - is invalid format of date! '
            'Please use native date format of this a spreadsheet.'
            'This client was imported without birthday' in warnings
        )

    @patch.object(Business, 'get_timezone')
    def test_no_category_staffers_service_import(self, get_timezone_mock):
        self.business.primary_category = None
        self.business.save()

        biz_tz = tz.gettz('UTC')
        setattr(biz_tz, '_long_name', 'UTC')
        get_timezone_mock.return_value = biz_tz

        data = loader(
            'services_1.xls',
            'services',
            load_versum_services,
            Specification.SERVICES.value,
        )
        service_importer = ServiceImporter(self.business)
        with pytest.raises(ValueError):
            service_importer.import_services(data)

    @patch.object(Business, 'get_timezone')
    def test_service_import(self, get_timezone_mock):
        business_category = baker.make(
            BusinessCategory,
            name='Awesome Category',
        )
        # business wth category
        self.business.primary_category = business_category
        self.business.save()

        biz_tz = tz.gettz('UTC')
        setattr(biz_tz, '_long_name', 'UTC')
        get_timezone_mock.return_value = biz_tz

        data = loader(
            'services_1.xls',
            'services',
            load_versum_services,
            Specification.SERVICES.value,
        )
        service_importer = ServiceImporter(self.business)
        service_importer.import_services(data)

        manager_c = ServiceCategory.objects.filter(business=self.business)
        manager_s = Service.objects.filter(business=self.business)
        service_ids = manager_s.values_list('id', flat=True)
        manager_sv = ServiceVariant.objects.filter(service_id__in=service_ids)
        assert manager_c.count() == ServicesImport.NUMBER_UNIQ_CATEGORIES
        assert manager_s.count() == (
            ServicesImport.NUMBER_SERVICES - ServicesImport.NUMBER_SERVICES_MERGED
        )
        assert manager_sv.count() == ServicesImport.NUMBER_SERVICES
        assert (
            manager_sv.filter(type=PriceType.FIXED).count() == ServicesImport.NUMBER_FIXED_SERVICES
        )

        assert manager_sv.filter(type=PriceType.FREE).count() == ServicesImport.NUMBER_FREE_SERVICES

    def test_no_booking_source_booking_import(self):
        business_category = baker.make(
            BusinessCategory,
            name='Awesome Category',
        )
        # business wth category
        self.business.primary_category = business_category
        self.business.save()

        data = loader(
            'bookings_1.xls',
            'bookings',
            load_versum_bookings,
            Specification.BOOKINGS.value,
        )
        booking_importer = BookingImporter(business=self.business)
        with pytest.raises(ValueError):
            booking_importer.import_bookings(bookings=data)

    @override_settings(API_COUNTRY=Country.PL)
    @patch.object(Business, 'get_timezone')
    def test_booking_import(self, get_timezone_mock):
        business_category = baker.make(
            BusinessCategory,
            name='Awesome Category',
        )
        # business wth category
        self.business.primary_category = business_category
        self.business.save()

        # POS
        pos = create_pos(business=self.business)
        baker.make(TaxRate, pos=pos, default_for_service=True)

        get_or_create_booking_source(name='Internal', app_type=BookingSources.INTERNAL_APP)
        biz_tz = tz.gettz('UTC')
        setattr(biz_tz, '_long_name', 'UTC')
        get_timezone_mock.return_value = biz_tz

        data = loader(
            'bookings_1.xls',
            'bookings',
            load_versum_bookings,
            Specification.BOOKINGS.value,
        )
        booking_importer = BookingImporter(business=self.business)
        booking_importer.import_bookings(bookings=data)

        bookings = SubBooking.objects.filter(appointment__business=self.business)
        bcis = BusinessCustomerInfo.objects.filter(business=self.business)
        assert bookings.count() == BookingImport.NUMBER_OF_BOOKINGS

        # non service imported
        assert Service.objects.count() == 0
        # because bookings were
        # imported without service file;
        # none of booking will have service variant
        # so all prices will placed in business secret note
        # except of services without price
        assert bookings.exclude(
            Q(appointment__business_secret_note__isnull=True)
            | Q(appointment__business_secret_note__exact='')
        ).count() == (BookingImport.NUMBER_OF_BOOKINGS - BookingImport.NUMBER_FREE_SERVICES)
        number_walk_in = bookings.filter(appointment__booked_for__isnull=True).count()
        # in booking file no email that why only phone
        assert number_walk_in == BookingImport.NUMBER_OF_WALKIN

        assert bcis.count() == BookingImport.NUMBER_UNIQE_CUSTOMERS

        # Check number of accepted bookings
        assert (
            bookings.filter(appointment__status=Appointment.STATUS.ACCEPTED).count()
            == BookingImport.NUMBER_OF_WAITING_BOOKINGS
        )

        # Check number of finished bookings
        assert (
            bookings.filter(appointment__status=Appointment.STATUS.FINISHED).count()
            == BookingImport.NUMBER_OF_FINISHED_BOOKINGS
        )

        # Check if each finished booking has transaction
        assert Transaction.objects.all().count() == (
            BookingImport.NUMBER_OF_FINISHED_BOOKINGS
            # For services without price
            # transactions will be create for 0 price
            # - BookingImport.NUMBER_OF_BOOKINGS_WITHOUT_PRICE
        )
        appointment = Appointment.objects.last()
        assert appointment.import_uid == 'versum:20160501000000000000'

    @override_settings(API_COUNTRY=Country.PL)
    def test_sketchy_booking_dates(self):
        business_category = baker.make(
            BusinessCategory,
            name='Awesome Category',
        )
        # business wth category
        self.business.primary_category = business_category
        self.business.save()

        # POS
        pos = create_pos(business=self.business)
        baker.make(TaxRate, pos=pos, default_for_service=True)

        get_or_create_booking_source(name='Internal', app_type=BookingSources.INTERNAL_APP)
        biz_tz = tz.gettz('UTC')
        setattr(biz_tz, '_long_name', 'UTC')

        data = loader(
            'sketchy_booking_dates.xls',
            'bookings',
            load_versum_bookings,
            Specification.BOOKINGS.value,
        )

        # The first 2 rows have booked_from later than booked_till,
        # Row 3 has a booking on 2024-13-32,
        # Row 4 has a booking on 1969-11-26 (before the epoch - invalid).
        # Finally, row 5 has valid data and it's the only one that should be imported.
        booking_importer = BookingImporter(business=self.business)
        booking_importer.import_bookings(bookings=data)

        bookings = SubBooking.objects.filter(appointment__business=self.business)

        assert bookings.count() == 1

    @override_settings(
        API_COUNTRY=Country.PL,
        CHARGE_FOR_STAFFERS_ENABLED=False,
    )
    @patch.object(Business, 'get_timezone')
    def test_versum_complex_import(self, get_timezone_mock):
        business_category = baker.make(
            BusinessCategory,
            name='Awesome Category',
        )
        # business wth category
        self.business.primary_category = business_category
        self.business.save()

        # POS
        pos = create_pos(business=self.business)
        baker.make(TaxRate, pos=pos, default_for_service=True)

        get_or_create_booking_source(name='Internal', app_type=BookingSources.INTERNAL_APP)
        biz_tz = tz.gettz('UTC')
        setattr(biz_tz, '_long_name', 'UTC')
        get_timezone_mock.return_value = biz_tz

        complex_import_args = (
            (
                'customers.xls',
                'complex_import',
                load_versum_customers,
                Specification.CUSTOMERS.value,
            ),
            (
                'services.xls',
                'complex_import',
                load_versum_services,
                Specification.SERVICES.value,
            ),
            (
                'bookings.xls',
                'complex_import',
                load_versum_bookings,
                Specification.BOOKINGS.value,
            ),
        )
        # import all data
        bci_mapping = {}
        service_mapping = {}
        for import_item in complex_import_args:
            data = loader(*import_item)
            # you wish you can write file_name, * = ..
            file_name, *_ = import_item
            if file_name == 'customers.xls':
                bci_importer = BCIImporter(self.business)
                bci_mapping = bci_importer.import_customers(customers=data, return_mapping=True)
            elif file_name == 'services.xls':
                service_importer = ServiceImporter(self.business)
                service_mapping = service_importer.import_services(data, return_mapping=True)
            else:
                booking_importer = BookingImporter(
                    business=self.business,
                    bci_mapping=bci_mapping,
                    service_mapping=service_mapping,
                )
                booking_importer.import_bookings(bookings=data, notify_clients=True)

        # check
        bcis = BusinessCustomerInfo.objects.filter(business=self.business)
        manager_c = ServiceCategory.objects.filter(business=self.business)

        assert manager_c.count() == ComplexImport.NUMBER_CATEGORIES
        assert bcis.count() == ComplexImport.NUMBER_BCIS

        service = service_mapping.get(ComplexImport.ID_SERVICE_WITH_MULTIPLE_SERVICE_VARIANT)
        assert service.service_variants.count() == (ComplexImport.NUMBER_SERVICE_VARIANTS)

        # + owner
        assert Resource.objects.filter(business=self.business, type=Resource.STAFF).count() == (
            ComplexImport.NUMBER_STAFFERS + 1
        )

        # bookings moved to future
        # number of bcis that should
        # receive notification
        assert (
            NotificationSchedule.objects.filter(
                state=ScheduleState.PENDING,
                task_id__startswith=reminder_format(''),
            ).count()
            == ComplexImport.NUMBER_OF_WAITING_BOOKINGS
        )

        # Check if each finished booking has transaction
        assert Transaction.objects.all().count() == ComplexImport.NUMBER_OF_FINISHED_BOOKINGS

        appointment = Appointment.objects.last()
        assert appointment.import_uid == 'versum:20160501000000000000'


@override_feature_flag({VersumImporterSplitAppointmentImportFlag.flag_name: True})
@patch.object(import_data_versum_task, 'apply_async')
@patch('webapps.admin_extra.versum_data_parser.load_versum_file')
def test_import_data_versum_task_called_multiple_times(load_file_mock, import_data_mock):
    load_file_mock.return_value = {
        'rows': list(range(10000)),
        'omitted': 0,
        'count': 10000,
        'valid': 10000,
    }
    versum_file_parser_task(1, '', False, False, True, True, True)

    assert import_data_mock.call_count == 11  # 1 call for services and customers, 10 for bookings
