import pytest


@pytest.mark.django_db
def test_zip_codes_areas_import_task():
    from webapps.admin_extra.tasks import zip_codes_areas_import_task
    from webapps.segment.models import ZipCodesToUrbanAreasMapper

    csv_file_data = (
        {
            'zip_code': '123',
            'metro_area': 'a',
            'urban_area': 'b',
            'urban_subarea': 'c',
            'state': 'd',
            'country_code': 'e',
            'focus': '1',
        },
        {
            'zip_code': '666',
            'metro_area': 'z',
            'urban_area': 'y',
            'urban_subarea': 'x',
            'state': 'w',
            'country_code': 'v',
            'focus': '1',
        },
    )

    zip_codes_areas_import_task(csv_file_data, None)
    all_mappings = ZipCodesToUrbanAreasMapper.objects.all()
    assert all_mappings
    assert any((element.zip_code == '123' for element in all_mappings))
    assert all((element.focus for element in all_mappings))
