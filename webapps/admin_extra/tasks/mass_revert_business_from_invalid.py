import io
import logging
import json

import numpy as np
import pandas as pd
from django.conf import settings
from django.db.models import Subquery, OuterRef

from lib.celery_tools import celery_task
from lib.email import send_email
from lib.tools import tznow

from webapps.admin_extra.tasks.utils import BaseCSVLoader
from webapps.admin_extra.tasks.mass_switch_merchants_payment_processor import SwitchBusinessResult
from webapps.billing.tasks import compute_business_status_task
from webapps.business.models import Business
from webapps.business.models.business_change import BusinessChange
from webapps.user.models import User

_logger = logging.getLogger('booksy.admin_extra')

CSV_READ_FIELD_NAMES = ('business_id',)

CSV_WRITE_FIELDS = list(CSV_READ_FIELD_NAMES) + ['result', 'message']

IMPORT_NAME = f'Mass revert from invalid [{settings.API_COUNTRY}]'


class ValidationException(Exception):
    pass


class CSVLoader(BaseCSVLoader):
    dtype_mapping = {
        'business_id': int,
    }
    max_records_in_batch = 100


def validate_business(
    business: Business | None,
    last_status: Business.Status | None,
) -> SwitchBusinessResult:
    if business is None:
        return SwitchBusinessResult(message='Business does not exist.')

    if business.status != Business.Status.BLOCKED:
        return SwitchBusinessResult(message='Business does not have blocked status.')

    if last_status is None:
        return SwitchBusinessResult(message='Unable to correlate last status change.')

    return SwitchBusinessResult(success=True)


@celery_task
def mass_business_revert_from_invalid_status_task(
    file_path: str,
    email: str,
    operator_id: int,
    dry_run: bool = False,
) -> None:
    loading_dt = tznow().strftime('%d_%m_%Y_%H_%M_%S')
    dry_run_info = '[Dry Run!]' if dry_run else ''

    try:
        data_frames = CSVLoader.load_from_file(file_path)
        input_records = CSVLoader.clean_data_frames(data_frames, business_index=True)

    except Exception as err:  # pylint: disable=broad-except
        send_email(
            to_addr=email,
            body=f'{IMPORT_NAME}{dry_run_info} error: {err}',
            subject=f'{IMPORT_NAME}{dry_run_info} - error loading file {loading_dt}',
        )
        return

    business_ids = input_records.keys()

    # pylint: disable-next=consider-using-f-string
    regex_q = r'"business.status":\s*\[\s*"[^%s]"\s*,\s*"%s"\s*]' % ((Business.Status.BLOCKED,) * 2)

    businesses = (
        Business.objects.filter(pk__in=business_ids)
        .only('status')
        .annotate(
            latest_change=Subquery(
                BusinessChange.objects.filter(business=OuterRef('pk'), data__regex=regex_q)
                .order_by('-created')
                .values('data')[:1]
            )
        )
        .distinct()
    )

    businesses = {business.id: business for business in businesses}

    operator = User.objects.get(id=operator_id)

    for business_id, row in input_records.items():
        business = businesses.get(business_id)

        last_status = (
            json.loads(business.latest_change)['business.status'][0]
            if business and business.latest_change
            else None
        )

        result = validate_business(business=business, last_status=last_status)

        if not result.success:
            row.update(
                {
                    'result': 'ERROR',
                    'message': result.message,
                }
            )
            continue

        row.update(
            {
                'result': 'SUCCESS',
            }
        )

        if dry_run:
            continue

        business.status = last_status
        business.save(update_fields=['status'])

        compute_business_status_task.delay(
            business_id=business.id,
            metadata={
                'user_email': operator.email,
                'user_id': operator.id,
                'endpoint': 'mass_business_revert_from_invalid_status_task',
            },
        )

    df_output = pd.DataFrame.from_dict(input_records, orient='index', columns=CSV_WRITE_FIELDS)
    df_output = df_output.replace(np.nan, 'NULL')

    writer = io.BytesIO()
    writer.write(df_output.to_csv(index=False).encode())
    writer.seek(0)

    send_email(
        to_addr=email,
        body=f'{IMPORT_NAME}{dry_run_info} report in attachment',
        subject=f'{IMPORT_NAME}{dry_run_info} - report {loading_dt}',
        attachments=[
            (
                f'business_switch_to_invalid{settings.API_COUNTRY}_{loading_dt}.csv',
                writer.getvalue(),
                'text/csv',
            )
        ],
    )
