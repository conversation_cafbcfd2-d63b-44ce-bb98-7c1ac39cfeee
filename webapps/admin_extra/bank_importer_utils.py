import re
import tempfile
import zipfile
from io import By<PERSON><PERSON>
from typing import (
    List,
    NamedTuple,
)

import requests
from django.conf import settings
from django.core.files.base import ContentFile
from openpyxl import Workbook
from PIL import Image as PILImage
import pandas as pd

from lib.email import prepare_and_send_email
from lib.enums import StrEnum
from webapps.admin_extra import consts
from webapps.images.enums import JPEG_EXTENSION
from webapps.images.mixins import PhotoHelperMixin
from webapps.images.models import Image

TEN_MEGABYTES = 10_048_576
SPOOL_FILE_MAX_SIZE = TEN_MEGABYTES


class EnterpriseImportData(NamedTuple):
    primary_category_id: int
    owner_email: str
    staffers_email: str
    add_to_existing_network: bool
    network_name: str
    network_slug: str
    categories_ids: List[int] = []
    partner_uuid: str = None


class EnterpriseDataHandlingBreakingException(Exception):
    pass


class EnterpriseDataHandlingException(Exception):
    def __init__(self, message: str, errors: List[str] = None):
        super().__init__(message)

        self.errors = errors if errors else []


class IncorrectBankDataException(EnterpriseDataHandlingException):
    pass


class ParsingEnterpriseDataException(EnterpriseDataHandlingException):
    pass


class FieldTypeEnum(StrEnum):
    BOOLEAN = 'boolean'
    EMAIL = 'email_type'
    IDENTIFIER = 'identifier_type'
    INTEGER = 'integer_type'
    MINUTES = 'minutes_type'
    PHONE_NUMBER = 'phone_number'
    RELATIVEDELTA = 'relativedelta_type'
    STRING = 'string_type'


class BaseParser:
    warnings = []

    def add_warning(self, warning):
        self.warnings.append(warning)

    @classmethod
    def are_values_unique(cls, entries, fields):
        suspicious_entries = []
        are_unique = True
        fields_per_values = {}

        for entry in entries:
            for field in fields:
                field_value = getattr(entry, field)
                if field_value:
                    if field in fields_per_values.keys():
                        field_values = fields_per_values[field]
                        if field_value in field_values:
                            are_unique = False
                            suspicious_entries.append(entry)
                        else:
                            field_values.append(field_value)
                    else:
                        fields_per_values[field] = [field_value]

        return are_unique, suspicious_entries

    @classmethod
    def check_input_correctness(cls, input_sheet):
        if input_sheet is None:
            raise EnterpriseDataHandlingBreakingException("Input sheet can not be None.")

    @classmethod
    def clean_data(cls, input_sheet):
        input_sheet.rename(columns=str.strip, inplace=True)

    def drop_rows_with_empty_values(self, sheet, fields_names):
        initial_entries_size = len(sheet.index)
        sheet.dropna(subset=fields_names, inplace=True)
        entries_size_after_dropping = len(sheet.index)

        if initial_entries_size > entries_size_after_dropping:
            self.add_warning(
                "We had to drop some entries due to missing values for fields "
                f"'{fields_names}'. Initial size: {initial_entries_size}, size"
                f" after dropping: {entries_size_after_dropping}"
            )

    def get_warnings(self):
        return self.warnings

    def prepare_parser(self):
        self.warnings = []


class SheetField:

    # pylint: disable=too-many-arguments
    def __init__(
        self,
        name,
        label,
        required=True,
        value_type=str,
        default=None,
        choices=None,
        missing_value=None,
        validator=None,
        removable=False,
    ):
        self.name = name
        self.label = label
        self.required = required
        self.value_type = value_type
        self.default = default
        self.choices = choices
        self.missing_value = missing_value
        self.validator = validator
        self.removable = removable


def get_google_image(url):
    # Scrap image from google maps
    headers = {
        'user-agent': (
            'Mozilla/5.0 (X11; Linux x86_64) '
            'AppleWebKit/537.36 (KHTML, like Gecko) '
            'Chrome/79.0.3945.88 Safari/537.36'
        )
    }
    content_html = requests.get(url, headers=headers).text
    regex = (
        re.search(r"window.FALLBACK='(?P<url>.*?)\\", content_html)
        or re.search(
            r'<meta content="(?P<url>https://.*?.googleusercontent.com/.*?)=', content_html
        )
        or re.search(
            r'"(?P<url>https://.*?.googleusercontent.com/.*?)\\u.*?-k-no"',
            content_html,
        )
    )
    img_url = regex.group('url').split('=')[0]
    # =s1536 for maximum image resolution
    r = requests.get(f'{img_url}=s1536', stream=True)

    # convert request response to PILImage
    buffer = tempfile.SpooledTemporaryFile(max_size=SPOOL_FILE_MAX_SIZE)
    for chunk in r.iter_content():
        buffer.write(chunk)

    buffer.seek(0)
    return prepare_image_file(BytesIO(buffer.read()))


def parse_hours(hrs):
    start, end = hrs.split('-')
    start = list(map(int, start.split(':')))
    end = list(map(int, end.split(':')))
    return start, end


def is_correct_url(url_candidate) -> bool:
    if not url_candidate:
        return False

    if isinstance(url_candidate, str):
        return url_candidate.startswith(("https", "http"))

    return False


def open_hours_validator(value, missing_value_label):
    regex = re.compile(r"^\d{1,2}:\d{1,2}-\d{1,2}:\d{1,2}$")
    value_str = str(value).strip()
    return bool(regex.match(value_str)) or value_str == missing_value_label


def prepare_image_file(img_stream):
    image = PILImage.open(img_stream)

    # validate image and convert
    if image.size > settings.IMAGES_MAX_DIMENSIONS:
        image.thumbnail(
            settings.IMAGES_MAX_DIMENSIONS,
            PILImage.LANCZOS,
        )
    if image.mode != 'RGB':
        image = image.convert('RGB')

    with BytesIO() as output:
        image.save(output, format=JPEG_EXTENSION, quality=80)
        contents = output.getvalue()

    image.format = JPEG_EXTENSION
    file = ContentFile(contents)

    return file, image


def save_image_for_business(business_id, image_type, is_cover, file, image):
    img_obj = Image(
        business_id=business_id,
        height=image.height,
        width=image.width,
        category=image_type,
        is_cover_photo=is_cover,
    )
    helper = PhotoHelperMixin(img_obj.category, img_obj)
    image_url = helper.upload_file_obj(
        file,
        image.format.lower(),
        business_id,
    )
    img_obj.image_url = image_url
    img_obj.save(bump_instances_to_es=False)

    return img_obj.id


def correct_delimiter(text, correct_delimiter_str, delimiters_array):
    for delimiter in delimiters_array:
        text = text.replace(delimiter, correct_delimiter_str)
    return text


def send_enterprise_resources_report(staffers_reports, to_addr):
    file = create_enterprise_staffers_report_file(staffers_reports)
    attachments = [
        ("bank_staffers_report.zip", file, 'application/octet-stream'),
    ]
    prepare_and_send_email(
        from_addr=settings.NO_REPLY_EMAIL_PRETTY,
        to_addr=to_addr,
        body='Enterprise Importer - Staff Report',
        subject='Enterprise Importer - Staff Report',
        attachments=attachments,
    )


def send_enterprise_import_report(import_report, to_addr):
    file = create_enterprise_import_report_file(import_report)
    attachments = [
        (
            "bank_import_report.xlsx",
            file,
            'application/vnd.openxmlformats-officedocument' '.spreadsheetml.sheet',
        ),
    ]
    prepare_and_send_email(
        from_addr=settings.NO_REPLY_EMAIL_PRETTY,
        to_addr=to_addr,
        body='Enterprise Importer - Branches Report',
        subject='Enterprise Importer - Branches Report',
        attachments=attachments,
    )


def create_enterprise_import_report_file(import_report):
    work_book = Workbook()
    my_file = BytesIO()
    ws = work_book.active

    for row in import_report:
        ws.append([row['name'], row['data']])

    work_book.save(my_file)

    return my_file.getvalue()


def create_enterprise_staffers_report_file(staffers_reports):
    zip_attachment = BytesIO()

    with zipfile.ZipFile(zip_attachment, mode='w') as zip_file:
        work_book = Workbook()
        my_file = BytesIO()
        ws = work_book.active

        for staffers_report in staffers_reports:
            for row in staffers_report['data']:
                ws.append(row)

        work_book.save(my_file)
        zip_file.writestr('staffers_report.xlsx', my_file.getvalue())

    return zip_attachment.getvalue()


def send_links_report(links_report_dict, to_addr):
    file = create_links_report_file(links_report_dict)
    attachments = [
        (
            'links_to_branches_report.zip',
            file,
            consts.OCTET_MIME,
        ),
    ]
    prepare_and_send_email(
        from_addr=settings.NO_REPLY_EMAIL_PRETTY,
        to_addr=to_addr,
        body='Enterprise Importer - Links Report',
        subject='Enterprise Importer - Links Report',
        attachments=attachments,
    )


def create_links_report_file(links_reports_dict):
    zip_attachment = BytesIO()
    file_name = 'links_report.xlsx'

    with zipfile.ZipFile(zip_attachment, mode='w') as zip_file:
        data_frame = pd.DataFrame(data=links_reports_dict)
        output = BytesIO()
        with pd.ExcelWriter(output) as writer:  # pylint: disable=abstract-class-instantiated
            data_frame.to_excel(writer, sheet_name=file_name, index=False)
        output.seek(0)

        zip_file.writestr(file_name, output.getvalue())

    return zip_attachment.getvalue()
