from datetime import date

from django import forms
from django.template.defaultfilters import filesizeformat

from webapps.admin_extra.import_tools.consts import FileImportConsts


class BaseCalendarImportForm(forms.Form):
    import_clients = forms.BooleanField(
        label='Import Clients',
        initial=True,
        required=False,
    )
    import_services = forms.BooleanField(
        label='Import Services',
        required=False,
        disabled=True,
        help_text='Import services not possible for calendars',
    )
    import_appointments = forms.BooleanField(
        label='Import Appointments',
        required=False,
        initial=True,
        help_text='Clients is required in case of appointments import',
    )
    import_from = forms.DateField(
        label='Date from (UTC)',
        required=True,
        initial=date.today,
    )
    import_to = forms.DateField(
        label='Date to (UTC, inclusive)',
        required=True,
        initial=date.today,
    )

    def clean(self):
        data = self.cleaned_data
        # Validate appointments date range
        import_from = data.get('import_from')
        import_to = data.get('import_to')
        import_appointments = data.get('import_appointments')
        import_clients = data.get('import_clients')

        if not all((import_from, import_to)):
            raise forms.ValidationError('Select date range')
        if import_to < import_from:
            date_range_error = '"Date to" cannot be earlier than "Date from"'
            self.add_error('import_to', date_range_error)
            raise forms.ValidationError(date_range_error)
        if import_appointments and not import_clients:
            import_clients_error = 'import_clients have to be checked for import_appointments'
            self.add_error('import_clients', import_clients_error)
            raise forms.ValidationError(import_clients_error)
        return self.cleaned_data


class GoogleCalendarImportForm(BaseCalendarImportForm):
    email = forms.CharField(required=True, help_text='Shared calendar owner email')

    field_order = ['email']


class ICalendarImportForm(BaseCalendarImportForm):
    ics_url = forms.URLField(
        required=False,
        help_text="URL to *.ics file",
    )
    ics_file = forms.FileField(
        label='Calendar (*.ics) import File.',
        required=False,
    )

    field_order = ['ics_url', 'ics_file']

    def clean(self):
        data = super().clean()
        ics_file = data.get('ics_file')
        ics_url = data.get('ics_url')
        if not ics_file and not ics_url:
            ics_import_error = "Cannot be empty"
            self.add_error('ics_file', ics_import_error)
            self.add_error('ics_url', ics_import_error)
            raise forms.ValidationError("Fullfill *.ics URL field or upload *.ics file")
        if ics_file and ics_url:
            ics_import_error = "One should be empty"
            self.add_error('ics_file', ics_import_error)
            self.add_error('ics_url', ics_import_error)
            raise forms.ValidationError("Fullfill only one field")

        return self.cleaned_data


class VagaroCalendarImportForm(forms.Form):
    """Used for manual file upload.

    As .ics calendar file from Vagaro doesn't contain
    complete info and we have to guess customer name and
    service name from the "summary" field :(."""

    CONTENT_TYPES = {
        'customer_file': FileImportConsts.CONTENT_TYPES_XLSX_XLS,
        'product_file': FileImportConsts.CONTENT_TYPES_XLSX_XLS,
        'service_file': FileImportConsts.CONTENT_TYPES_XLSX_XLS,
        'calendar_file': FileImportConsts.CONTENT_TYPES_ICS,
    }

    MAX_UPLOAD_SIZE = FileImportConsts.FILE_SIZE_20_MB

    email = forms.EmailField(
        label='Email for report',
        required=True,
    )

    # Made both files required otherwise CS would "forget" to import customers
    # or refresh it

    customer_file = forms.FileField(
        label='Customers import file (.xls) - used for customers import only',
        required=False,
    )

    service_file = forms.FileField(
        label='Services import file (.xls) - used for services import only',
        required=False,
    )

    calendar_file = forms.FileField(
        label='Calendar import file (.ics) - used for bookings and services import',
        required=False,
    )

    product_file = forms.FileField(
        label='Products import file (.xls) - used for products import only',
        required=False,
    )

    def _clean_file_field(self, field_name):
        import_file = self.cleaned_data.get(field_name)
        if import_file:
            file_type = import_file.name.split('.')[-1]
            if file_type in self.CONTENT_TYPES[field_name]:
                if import_file.size > self.MAX_UPLOAD_SIZE:
                    error = (
                        f'Please keep file size under {filesizeformat(self.MAX_UPLOAD_SIZE)}.'
                        f' Current file size is {filesizeformat(import_file.size)}'
                    )
                    raise forms.ValidationError(error)
            else:
                raise forms.ValidationError(
                    f'{field_name} - {file_type}: This file type is not supported'
                )

        return import_file

    def clean(self):
        customer_file = self.cleaned_data.get('customer_file')
        service_file = self.cleaned_data.get('service_file')
        calendar_file = self.cleaned_data.get('calendar_file')
        product_file = self.cleaned_data.get('product_file')

        file_fields = [
            customer_file,
            service_file,
            calendar_file,
            product_file,
        ]
        if not any(file_fields):
            raise forms.ValidationError('You have to provide at least 1 file')

        if calendar_file and not (customer_file and service_file):
            raise forms.ValidationError(
                'You have to provide both customer and services file in order to import calendar'
            )
        self._clean_file_field('customer_file')
        self._clean_file_field('service_file')
        self._clean_file_field('calendar_file')
        self._clean_file_field('product_file')
        return self.cleaned_data
