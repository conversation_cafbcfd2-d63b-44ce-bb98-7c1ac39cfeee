from django import forms
from django.contrib.admin import widgets
from django.core.exceptions import ValidationError

from webapps.business.models import Business


class QrCodeRegenerationForm(forms.Form):
    ALLOWED_LANGUAGES = {"en", "en-gb", "pl", "es", "es-es", "pt", "fr", "vi", "uk", "zh"}
    business_id = forms.ModelChoiceField(
        required=True,
        queryset=Business.objects.values_list('id', flat=True),
        widget=widgets.AdminIntegerFieldWidget(),
        help_text='ID of Business object',
    )
    language_code = forms.CharField(
        required=False,
        help_text='Language code to select PDF file, e.g. "en", "es", "pl"',
    )

    def clean(self):
        """Regeneration is allowed only in listed countries."""
        language = self.cleaned_data.get('language_code')
        if language and language not in self.ALLOWED_LANGUAGES:
            raise ValidationError('Wrong PDF language code!')

        return super().clean()
