import csv

from django import forms
from django.core.validators import FileExtensionValidator
from django.utils.translation import gettext_lazy as _
from webapps.admin_extra.views.billing import UploadImportFileForm

from webapps.admin_extra.forms.mixins import ImportFileFormMixin
from webapps.admin_extra.import_tools.consts import FileImportConsts
from webapps.business.enums import (
    ChurnSwitchEnum,
    FreezeSwitchEnum,
)
from webapps.business.models import Business


class BusinessTrialExtendForm(ImportFileFormMixin, forms.Form):
    trial_extend_by = forms.IntegerField(
        label='Extend trial by (days)',
        help_text=(
            'Number of days to extend the trial from today. '
            'For example, entering "30" will extend the trial by one month. '
            'This will apply to all businesses listed in the uploaded file.'
        ),
        required=True,
        min_value=1,
    )


class BusinessChurnSwitchForm(UploadImportFileForm, forms.Form):
    """Churn/undo churn businesses from .xlsx file"""

    CONTENT_TYPES = FileImportConsts.CONTENT_TYPES_XLSX_XLS

    action = forms.ChoiceField(choices=ChurnSwitchEnum.choices())
    email = forms.EmailField(
        label='Email for report',
    )


class BusinessFreezeSwitchForm(ImportFileFormMixin, forms.Form):
    """Freeze/unfreeze businesses from .xls file"""

    CONTENT_TYPES = FileImportConsts.CONTENT_TYPES_XLSX_XLS

    action = forms.ChoiceField(choices=FreezeSwitchEnum.choices())
    email = forms.EmailField(
        label='Email for report (optional)',
        required=False,
    )

    import_file = forms.FileField(
        label='File with businesses ids (.xlsx)',
        help_text=(
            'One business id per row. '
            'Only first column will be used. '
            'Remember to add column header, ex. "Business ID"'
        ),
    )


def transition_choices(transitions):
    choices = []
    for name, transition in reversed(sorted(transitions.items())):
        transition_from = transition['from']
        if len(transition_from) == 1:
            status_from = Business.Status.choices_map()[transition_from[0]]
        elif len(transition_from) > 1:
            status_from = transition['from_name']
        else:
            raise ValueError("status_from is not set which should never be possible")
        status_to = Business.Status.choices_map()[transition['to']]
        active = f'active: {transition["active"]}' if 'active' in transition else ''
        visible = f'; visible: {transition["visible"]}' if 'visible' in transition else ''
        attributes_to_change = f'({active}{visible})' if active or visible else ''
        choice_visible_for_user = f'{status_from} --> {status_to} {attributes_to_change}'
        choice = (name, choice_visible_for_user)
        choices.append(choice)
    return choices


class BusinessStatusChangeForm(forms.Form):
    """
    Allows to change statuses of business from `from` to `to`.
    If `from` is more than 1 status, then `from_name` is necessary,
    so that we know what to display for the user in a dropdown.

    If `from` is 1 status then its label will be displayed for the user.
    """

    TRANSITIONS = {
        'trial_blocked': {
            'from': [Business.Status.TRIAL],
            'to': Business.Status.TRIAL_BLOCKED,
            # Target "active" value.
            # If set to None, active field won't be changed.
            'active': False,
        },
        'overdue': {
            'from': [Business.Status.OVERDUE],
            'to': Business.Status.BLOCKED_OVERDUE,
        },
        'business_invalid': {
            'from': Business.Status.values(),
            'to': Business.Status.BLOCKED,
            'active': False,
            'visible': False,
            'from_name': _("All statuses"),
        },
    }

    transition = forms.ChoiceField(
        choices=transition_choices(TRANSITIONS),
        help_text='Transition from status to status.',
    )
    business_ids = forms.FileField(
        help_text='Please add here csv file with one column named business_id.',
        required=True,
        validators=[FileExtensionValidator(allowed_extensions=['csv'])],
    )

    def clean_transition(self):
        return self.TRANSITIONS[self.cleaned_data.get('transition')]

    def clean(self):
        transition = self.cleaned_data.get('transition')
        business_ids = self.cleaned_data.get('business_ids') or []
        errors = {}
        statuses = transition['from']
        valid_ids = Business.objects.filter(
            id__in=business_ids,
            status__in=statuses,
        ).values_list('id', flat=True)
        invalid_ids = set(business_ids) - set(valid_ids)
        if invalid_ids:
            errors['business_ids'] = (
                f"Following IDs are invalid: {', '.join(str(biz) for biz in sorted(invalid_ids))}"
            )

        if errors:
            raise forms.ValidationError(errors)
        return self.cleaned_data

    def clean_business_ids(self):
        business_file_ids = self.cleaned_data.get('business_ids')

        business_ids = []

        try:
            decoded_file = business_file_ids.read().decode('utf-8')
            csv_data = csv.reader(decoded_file.splitlines())
            headers = next(csv_data)

            if headers[0] != 'business_id':
                raise forms.ValidationError('Incorrect column name. Expected "business_id".')

            for row in csv_data:
                if row:
                    business_ids.append(int(row[0]))

        except UnicodeDecodeError as exc:
            raise forms.ValidationError(f'Unable to decode file {exc}')

        return business_ids
