from datetime import datetime

from django import forms

from lib.tools import tznow


class DryRunInvoicesPerBCStartReportForm(forms.Form):
    start_date = forms.DateField(label='Start date', initial=tznow().date(), required=True)
    end_date = forms.DateField(label='End date (inclusive)', initial=tznow().date(), required=True)
    email = forms.EmailField(label='Email for report', required=True)

    def clean_start_date(self):
        return datetime.combine(self.cleaned_data.get('start_date'), datetime.min.time())

    def clean_end_date(self):
        return datetime.combine(self.cleaned_data.get('end_date'), datetime.min.time())
