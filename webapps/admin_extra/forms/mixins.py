from django import forms
from django.template.defaultfilters import filesizeformat
from webapps.user.models import User

from webapps.admin_extra.import_tools.consts import FileImportConsts
from webapps.business.models import Business


class BusinessIdFormMixin:
    business_id = forms.IntegerField(label='Provide Business id')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make the field first in dict
        self.fields = {'business_id': self.business_id, **self.fields}

    def clean_business_id(self):
        business_id = self.cleaned_data.get('business_id')
        if business_id is None:
            raise forms.ValidationError('Business id is required')

        try:
            business = Business.objects.get(id=business_id)
        except Business.DoesNotExist:
            raise forms.ValidationError(  # pylint: disable=raise-missing-from
                f"Business with id={business_id} doesn't exist. Provide valid business id."
            )
        # in case business is needed later on we set it here to avoid duplicate queries
        self.cleaned_data['business'] = business
        return business_id


class SizeLimitFileField(forms.ClearableFileInput):
    template_name = 'admin_extra/widgets/size_limit_file_widget.html'
    MAX_UPLOAD_SIZE = FileImportConsts.FILE_SIZE_12_MB

    def get_context(self, name, value, attrs=None):
        context = super().get_context(name, value, attrs)
        context['MAX_UPLOAD_SIZE'] = self.MAX_UPLOAD_SIZE
        context['SIZE_TO_BIG_ERROR_MESSAGE'] = (
            f"File can not be bigger than {self.MAX_UPLOAD_SIZE//2**20} MB"
        )
        return context


class ImportFileFormMixin:
    CONTENT_TYPES = FileImportConsts.CONTENT_TYPES_XLSX

    SIZE_ERROR_TEMPLATE = 'Please keep filesize under {}. Current filesize {}'
    TYPE_ERROR_TEMPLATE = 'File type {} is not supported. Supported file types: {}'
    import_file = forms.FileField(label='Import File.', widget=SizeLimitFileField)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make the field first in dict
        self.fields = {'import_file': self.import_file, **self.fields}

    def clean_import_file(self):
        import_file = self.cleaned_data.get('import_file')

        if not import_file:
            raise forms.ValidationError('You must select a file.')

        file_type = import_file.name.split('.')[-1]

        if file_type not in self.CONTENT_TYPES:
            error_msg = self.TYPE_ERROR_TEMPLATE.format(
                file_type,
                self.CONTENT_TYPES,
            )
            raise forms.ValidationError(error_msg)
        if import_file.size > SizeLimitFileField.MAX_UPLOAD_SIZE:
            error_msg = self.SIZE_ERROR_TEMPLATE.format(
                filesizeformat(SizeLimitFileField.MAX_UPLOAD_SIZE),
                filesizeformat(import_file.size),
            )
            raise forms.ValidationError(error_msg)

        return import_file


class DryRunFormMixin:
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['dry_run'] = forms.BooleanField(
            required=False,
            initial=True,
            label='Check if you want to check format of file',
            help_text='Dry-run',
        )


class DryRunWithEmailReportFormMixin:
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['dry_run_with_email_report'] = forms.BooleanField(
            required=False,
            initial=False,
            label='Check if you want to check format of file and receive the report on your email. '
            'Use in case of timeout errors',
            help_text='Dry-run with email report',
        )


class OperatorFormMixin(forms.Form):
    full_name = forms.CharField(
        max_length=255,
        label="Operator full name",
        widget=forms.TextInput(attrs={'readonly': True}),
    )
    email = forms.EmailField(
        max_length=255,
        label="Operator e-mail",
        widget=forms.TextInput(attrs={'readonly': True}),
    )
    user_id = forms.IntegerField(
        widget=forms.HiddenInput(),
        required=True,
    )

    def __init__(self, *args, **kwargs):
        user_id = kwargs.pop('user_id')
        logged_user = User.objects.get(id=user_id)
        super().__init__(*args, **kwargs)
        self.fields['full_name'].initial = logged_user.get_full_name()
        self.fields['email'].initial = logged_user.email
        self.fields['user_id'].initial = user_id
