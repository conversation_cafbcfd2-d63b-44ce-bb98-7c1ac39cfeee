from datetime import date

from django import forms

from webapps.business.models import Business
from webapps.purchase.tasks.apple_google_subs_migration import (
    migrate_apple_google_subscriptions_task,
)


class SubscriptionsMigrationForm(forms.Form):
    payment_source = forms.ChoiceField(
        choices=(
            (None, '-'),
            ('Apple', 'Apple iTunes'),
            ('Google', 'Google Play'),
        ),
        required=True,
    )
    from_date = forms.DateField(
        required=True,
        initial=date(2023, 1, 1),
        help_text='(from 00:00:00:000000)',
        label='from date of Subscription Expiry (format YYYY-MM-DD)',
    )
    till_date = forms.DateField(
        required=True,
        initial=date(2023, 1, 2),
        help_text='(to -1day 23:59:59:999999)',
        label='till date of Subscription Expiry (format YYYY-MM-DD)',
    )

    def clean_payment_source(self):
        payment_source = self.cleaned_data.get('payment_source')
        if not payment_source:
            raise forms.ValidationError('Select payment source')
        return payment_source

    def clean(self):
        if self.cleaned_data.get('from_date') >= self.cleaned_data.get('till_date'):
            raise forms.ValidationError('End of date range cannot be set before start')

    def trigger_task(self):
        dates = {
            'from_date': self.cleaned_data.get('from_date'),
            'till_date': self.cleaned_data.get('till_date'),
        }
        match self.cleaned_data.get('payment_source'):
            case 'Apple':
                migrate_apple_google_subscriptions_task.delay(
                    source=Business.PaymentSource.ITUNES, **dates
                )
            case 'Google':
                migrate_apple_google_subscriptions_task.delay(
                    source=Business.PaymentSource.PLAY, **dates
                )
