# Generated by Django 1.11.17 on 2019-01-25 09:18
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('admin_extra', '0031_auto_20190216_1134'),
    ]

    operations = [
        migrations.CreateModel(
            name='SuperuserLoginEntry',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('user_email', models.EmailField(max_length=254)),
                ('superuser_email', models.EmailField(max_length=254)),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
            ],
            options={
                'ordering': ('-created',),
                'verbose_name': 'log entry',
                'verbose_name_plural': 'superuser login entries',
            },
        ),
    ]
