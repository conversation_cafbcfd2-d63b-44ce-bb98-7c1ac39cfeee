## Image migration

### Copy images to server 

```bash
scp * booksy-us-beta-1:/home/<USER>/media/images/marketing 
```


### Copy images to vagrant

```bash
cp * /opt/booksy/media/images/marketing
```

## Image Interface

### Ports
The interface class (ports.py) should contain signatures (prototypes) of methods that will be implemented by classes inheriting from it.

### Adapters
The repository class (adapters.py), on the other hand, should implement specific methods that use the database or other data sources.

### Services
Services act as coordinators and implement business logic. Services use ports (interfaces) to get the information they need and operate on the data.

Depending on your needs (business conditions) Images can be fetched using either adapters or services.