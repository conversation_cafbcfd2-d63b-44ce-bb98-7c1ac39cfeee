# Generated by Django 4.0.2 on 2022-03-03 12:23

from django.db import migrations, models
import webapps.images.enums


class Migration(migrations.Migration):

    dependencies = [
        ('images', '0080_imagelike_add_foreign_keys'),
    ]

    operations = [
        migrations.AlterField(
            model_name='image',
            name='category',
            field=models.CharField(
                choices=[
                    (webapps.images.enums.ImageTypeEnum['BIZ_PHOTO'], 'Business Photo'),
                    (webapps.images.enums.ImageTypeEnum['INSPIRATION'], 'Inspiration'),
                    (webapps.images.enums.ImageTypeEnum['LOGO'], 'Logo'),
                    (webapps.images.enums.ImageTypeEnum['COVER_DEPRECATED'], 'Cover Image'),
                    (
                        webapps.images.enums.ImageTypeEnum['COVER_ORIGINAL_DEPRECATED'],
                        'Original Cover Image',
                    ),
                    (webapps.images.enums.ImageTypeEnum['MARKETING'], 'Marketing'),
                    (
                        webapps.images.enums.ImageTypeEnum['MARKETING_DIGITAL_FLYER'],
                        'Digital Flyer',
                    ),
                    (
                        webapps.images.enums.ImageTypeEnum['MARKETING_DIGITAL_FLYER__FACEBOOK'],
                        'Digital Flyer FB',
                    ),
                    (
                        webapps.images.enums.ImageTypeEnum['DF_BACKGROUND'],
                        'Digital Flyer Background',
                    ),
                    (
                        webapps.images.enums.ImageTypeEnum['DF_CATEGORY_BACKGROUND'],
                        'Digital Flyer Category BG',
                    ),
                    (
                        webapps.images.enums.ImageTypeEnum['DF_GROUP_BACKGROUND'],
                        'Digital Flyer Group BG',
                    ),
                    (webapps.images.enums.ImageTypeEnum['DF_RENDER'], 'Digital Flyer Render'),
                    (webapps.images.enums.ImageTypeEnum['BOOKSY_AWARD'], 'Best of Booksy Award'),
                    (webapps.images.enums.ImageTypeEnum['BOOST_BACKGROUND'], 'Boost Background'),
                    (
                        webapps.images.enums.ImageTypeEnum['WAREHOUSE_BRAND_PHOTOS'],
                        'Warehouse brand photo',
                    ),
                    (
                        webapps.images.enums.ImageTypeEnum['WAREHOUSE_COMMODITY_PHOTOS'],
                        'Warehouse commodity photo',
                    ),
                    (
                        webapps.images.enums.ImageTypeEnum['MESSAGE_BLAST_PHOTOS'],
                        'Message blast photos',
                    ),
                    (
                        webapps.images.enums.ImageTypeEnum['MESSAGE_BLAST_PHOTO'],
                        'Message blast custom photo',
                    ),
                    (webapps.images.enums.ImageTypeEnum['HINT_AVATAR'], 'Hint Avatar'),
                ],
                default=webapps.images.enums.ImageTypeEnum['BIZ_PHOTO'],
                max_length=50,
            ),
        ),
    ]
