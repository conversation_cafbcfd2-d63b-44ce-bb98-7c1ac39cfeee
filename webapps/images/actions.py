from django.conf import settings

from lib.events import lazy_event_receiver, event_receiver
from lib.feature_flag.killswitch import HintsAndWalkThroughEventsPublishingFlag
from webapps.business.enums import BusinessCategoryEnum
from webapps.images.enums import ImageTypeEnum
from webapps.images.events import (
    portfolio_photo_added_event,
    portfolio_photo_comment_added_event,
    cover_photo_added_event,
)
from webapps.images.messages import ImageAddedMessage
from webapps.images.models import Image, ImageComment
from webapps.images.notifications import (
    Portfolio10thPhotoAddedNotification,
    Portfolio5thPhotoAddedBarberNotification,
    Portfolio5thPhotoAddedNotification,
    PortfolioNthPhotoAddedNotification,
    PortfolioPhotoCommentAddedNotification,
)


# pylint: disable=unused-argument


@event_receiver((portfolio_photo_added_event, cover_photo_added_event))
def photo_added_publisher_handler(instance: Image, **_):
    if HintsAndWalkThroughEventsPublishingFlag():
        ImageAddedMessage(instance).publish()


if settings.POPUP_NOTIFICATIONS_ENABLED:

    @lazy_event_receiver(portfolio_photo_comment_added_event)
    def portfolio_photo_comment_added_handler(instance, **kwargs):
        comment: ImageComment = instance
        PortfolioPhotoCommentAddedNotification(comment).send()

    if settings.POPUP_PHASE2:

        @lazy_event_receiver(portfolio_photo_added_event)
        def portfolio_photo_added_handler(instance, **kwargs):
            image: Image = instance
            business = image.business
            portfolio_images_count = Image.objects.filter(
                pk__lte=image.id,
                business=business,
                category=ImageTypeEnum.INSPIRATION,
            ).count()
            nth_photo = 100

            if portfolio_images_count == 5:
                barbers = BusinessCategoryEnum.BARBERS
                if business.primary_category and business.primary_category.internal_name == barbers:
                    Portfolio5thPhotoAddedBarberNotification(image).send()
                else:
                    Portfolio5thPhotoAddedNotification(image).send()
            elif portfolio_images_count == 10:
                Portfolio10thPhotoAddedNotification(image).send()
            elif portfolio_images_count and (portfolio_images_count % nth_photo) == 0:
                PortfolioNthPhotoAddedNotification(
                    image,
                    images_count=portfolio_images_count,
                ).send()
