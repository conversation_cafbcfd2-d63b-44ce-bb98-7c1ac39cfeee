from dataclasses import asdict

from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.exceptions import NotFound
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionAPIView
from lib.feature_flag.feature.payment import CxPromotedPaymentMethods
from lib.payment_gateway.entities import WalletEntity
from lib.payment_providers.entities import (
    PaymentTokenExternalData,
    PortResponse,
    SetupIntentEntity,
    StripeApplePayPaymentTokenExternalData,
)
from lib.payment_providers.enums import PaymentMethodType
from lib.payments.enums import PaymentProviderCode
from service.mixins.validation import validate_serializer
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.ports.payment_ports import PaymentProvidersPaymentPort
from webapps.payments.errors import map_error_to_response
from webapps.payments.serializers.tokenized_payment_methods import StripePaymentTokenViewSerializer


class GetSetupIntentView(BaseBooksySessionAPIView):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)

    def get(self, request, setup_intent_id):
        wallet_entity: WalletEntity = PaymentGatewayPort.get_customer_wallet(
            user_id=request.user.id
        )
        setup_intent_entity: SetupIntentEntity = PaymentProvidersPaymentPort.get_setup_intent(
            setup_intent_id=setup_intent_id,
            customer_id=wallet_entity.customer_id,
        ).entity
        if not setup_intent_entity:
            raise NotFound
        return Response(data=asdict(setup_intent_entity), status=status.HTTP_200_OK)


class CreateSetupIntentView(BaseBooksySessionAPIView):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        wallet_entity: WalletEntity = PaymentGatewayPort.get_customer_wallet(
            user_id=request.user.id
        )
        setup_intent_entity: SetupIntentEntity = (
            PaymentProvidersPaymentPort.initialize_setup_intent(
                customer_id=wallet_entity.customer_id,
                payment_provider_code=PaymentProviderCode.STRIPE,
                method_type=PaymentMethodType.CARD,
            ).entity
        )
        return Response(data=asdict(setup_intent_entity), status=status.HTTP_200_OK)


class StripeApplePayPaymentTokenView(BaseBooksySessionAPIView, GenericAPIView):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)
    serializer_class = StripePaymentTokenViewSerializer

    def post(self, request):
        serializer = self.get_serializer(
            data=request.data,
        )
        validated_data = validate_serializer(serializer)
        payment_token_entity = PaymentProvidersPaymentPort.create_payment_token(
            payment_provider_code=PaymentProviderCode.STRIPE,
            external_data=PaymentTokenExternalData(
                stripe=StripeApplePayPaymentTokenExternalData(**validated_data)
            ),
        ).entity
        return Response(data=asdict(payment_token_entity), status=status.HTTP_200_OK)


class SetDefaultTokenizedPaymentMethodView(BaseBooksySessionAPIView):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)

    def post(self, request, payment_method_id):
        wallet_entity: WalletEntity = PaymentGatewayPort.get_customer_wallet(
            user_id=request.user.id
        )
        tokenized_pm_entity = PaymentProvidersPaymentPort.set_default_tokenized_pm(
            customer_id=wallet_entity.customer_id,
            tokenized_payment_method_id=payment_method_id,
        ).entity
        return Response(data=asdict(tokenized_pm_entity), status=status.HTTP_200_OK)


class ListTokenizedPaymentMethodsView(BaseBooksySessionAPIView):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)

    def get(self, request, provider_code=None):
        wallet_entity: WalletEntity = PaymentGatewayPort.get_customer_wallet(
            user_id=request.user.id
        )
        tokenized_pm_entities = PaymentProvidersPaymentPort.list_tokenized_pms(
            customer_id=wallet_entity.customer_id,
            payment_provider_code=provider_code,
            method_type=PaymentMethodType.CARD,
        ).entity
        data = {'payment_methods': [asdict(entity) for entity in tokenized_pm_entities]}
        if cx_promoted_payment_methods := CxPromotedPaymentMethods():
            data['cx_promoted_payment_methods'] = cx_promoted_payment_methods
        return Response(
            data=data,
            status=status.HTTP_200_OK,
        )


class TokenizedPaymentMethodView(BaseBooksySessionAPIView):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)

    def delete(self, request, payment_method_id):
        wallet_entity: WalletEntity = PaymentGatewayPort.get_customer_wallet(
            user_id=request.user.id
        )
        port_response: PortResponse = PaymentProvidersPaymentPort.remove_tokenized_pm(
            tokenized_payment_method_id=payment_method_id,
            customer_id=wallet_entity.customer_id,
        )
        if port_response.errors:
            resp_dict = map_error_to_response(port_response.errors[0].code)
            return Response(status=resp_dict['status'], data={})
        return Response(data={}, status=status.HTTP_200_OK)
