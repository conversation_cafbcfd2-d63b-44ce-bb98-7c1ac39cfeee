from dataclasses import asdict

from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionAPIView
from lib.payment_providers.entities import (
    AuthorizePaymentMethodDataEntity,
    DeviceDataEntity,
    AuthAdditionalDataEntity,
    FraudPreventionAuthAdditionalDataEntity,
)
from lib.point_of_sale.enums import BasketPaymentAnalyticsTrigger
from lib.tools import sget_v2
from service.mixins.throttling import get_django_user_ip
from service.mixins.validation import validate_serializer
from webapps.payments.serializers.payment import MakePaymentRequestSerializer
from webapps.payments.views.payment.common import BasketPaymentViewMixin
from webapps.point_of_sale.ports import BasketPaymentPort


class CustomerMakeBasketPaymentView(
    BasketPaymentViewMixin,
    BaseBooksySessionAPIView,
    GenericAPIView,
):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)
    serializer_class = MakePaymentRequestSerializer
    lookup_url_kwarg = 'basket_payment_id'

    def post(self, request, basket_payment_id: str):
        instance, error_response = self._get_balance_transaction_or_error_response(
            basket_payment_id=basket_payment_id,
        )
        if error_response:
            return error_response

        serializer = self.get_serializer(
            data=request.data,
            instance=instance,
            context={'user': self.user},
        )

        validated_data = validate_serializer(serializer)
        payment_method_data = AuthorizePaymentMethodDataEntity(
            payment_token=sget_v2(
                validated_data,
                ['external_payment_method', 'token'],
            ),
            external_token_type=sget_v2(
                validated_data,
                ['external_payment_method', 'partner'],
            ),
            tokenized_pm_id=sget_v2(
                validated_data,
                ['tokenized_payment_method_id'],
            ),
        )

        BasketPaymentPort.make_basket_payment(
            basket_payment_id=basket_payment_id,
            user_id=self.request.user.id,
            payment_method_data=payment_method_data,
            device_data=DeviceDataEntity(
                device_fingerprint=self.fingerprint,
                phone_number=self.user.cell_phone,
                user_agent=self.user_agent,
                ip=str(get_django_user_ip(request)),
            ),
            trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            additional_data=AuthAdditionalDataEntity(
                fraud_prevention=FraudPreventionAuthAdditionalDataEntity(
                    nethone_attempt_reference="",  # todo
                ),
            )
        )

        instance = BasketPaymentPort.get_basket_payment_entity(
            basket_payment_id=basket_payment_id,
            user_id=self.request.user.id,
        )

        return Response(
            data={
                'basket_payment': asdict(
                    instance,
                )
            },
            status=status.HTTP_200_OK,
        )
