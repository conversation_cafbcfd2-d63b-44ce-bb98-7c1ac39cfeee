from django.contrib import admin

from lib.admin_helpers import BaseModelAdmin, ReadOnlyTabular, JsonFormatterMixin
from webapps.stripe_app.models.charge import Charge, Dispute, PaymentIntent, Refund
from webapps.stripe_app.models.customer import Customer
from webapps.stripe_app.models.payment_method import PaymentMethod, SetupIntent


class StripeChargeInline(ReadOnlyTabular, admin.StackedInline):
    model = Charge
    can_delete = False


class StripeRefundInline(ReadOnlyTabular, admin.StackedInline):
    model = Refund
    can_delete = False


class StripePaymentIntentAdmin(ReadOnlyTabular, BaseModelAdmin):
    model = PaymentIntent
    exclude = ('client_secret',)
    list_display = [
        'id',
        'stripe_id',
        'decimal_amount',
        'currency',
        'description',
        'customer',
        'status',
        'subscriber',
        'account_id',
        'created_at',
    ]
    search_fields = [
        'id',
        'stripe_id',
        'customer__booksy_id',
    ]
    list_filter = [
        'status',
        'subscriber',
        'account_id',
    ]
    list_select_related = True
    inlines = [StripeChargeInline, StripeRefundInline]


class StripeChargeAdmin(ReadOnlyTabular, BaseModelAdmin):
    model = Charge
    list_display = [
        'id',
        'payment_intent',
        'customer',
        'created_at',
        'subscriber',
        'status',
        'stripe_id',
    ]
    search_fields = [
        'stripe_id',
        'payment_intent__stripe_id',
    ]
    list_filter = [
        'subscriber',
        'status',
    ]
    list_select_related = True


class StripeRefundAdmin(ReadOnlyTabular, BaseModelAdmin):
    model = Refund
    list_display = [
        'id',
        'stripe_id',
        'decimal_amount',
        'payment_intent',
        'subscriber',
        'status',
        'created_at',
    ]
    search_fields = [
        'stripe_id',
        'payment_intent__stripe_id',
        'payment_intent__customer__booksy_id',
    ]
    list_filter = [
        'subscriber',
        'status',
        'failure_reason',
    ]
    list_select_related = True


class StripeCustomerAdmin(ReadOnlyTabular, BaseModelAdmin):
    model = Customer
    list_display = [
        'id',
        'stripe_id',
        'booksy_id',
        'name',
        'created',
    ]
    search_fields = [
        'id',
        'stripe_id',
        'booksy_id',
    ]


class StripePaymentMethodAdmin(ReadOnlyTabular, BaseModelAdmin):
    model = PaymentMethod
    list_display = [
        'id',
        'stripe_id',
        'type',
        'customer',
        'subscriber',
        'created',
        'detached_at',
    ]
    search_fields = [
        'id',
        'stripe_id',
        'customer__stripe_id',
        'customer__booksy_id',
    ]
    list_filter = [
        'subscriber',
        'type',
    ]
    list_select_related = True


class StripeDisputeAdmin(ReadOnlyTabular, BaseModelAdmin):
    model = Dispute
    list_display = [
        'id',
        'stripe_id',
        'decimal_amount',
        'payment_intent',
        'reason',
        'status',
        'disputed_on',
        'respond_by',
    ]
    search_fields = [
        'stripe_id',
        'payment_intent__stripe_id',
    ]
    list_filter = [
        'status',
        'reason',
    ]


class StripeSetupIntentAdmin(JsonFormatterMixin, ReadOnlyTabular, BaseModelAdmin):
    model = SetupIntent
    exclude = ('client_secret',)
    list_display = [
        'id',
        'stripe_id',
        'customer',
        'status',
        'last_setup_error_code',
        'created',
    ]
    search_fields = [
        'customer__stripe_id',
        'customer__booksy_id',
    ]
    list_filter = [
        'status',
    ]
    list_select_related = True

    fields = [
        'deleted',
        'stripe_id',
        'subscriber',
        'subscriber_key',
        'metadata',
        'cancellation_reason',
        'customer',
        'next_action',
        'payment_method_types',
        'payment_method_options',
        'status',
        'usage',
        '_last_setup_error',
    ]

    readonly_fields = [
        '_last_setup_error',
    ]

    @staticmethod
    def last_setup_error_code(obj):
        if error := obj.last_setup_error:
            return error.get('code', 'undefined')

    @admin.display(description='Last setup error')
    def _last_setup_error(self, obj):
        if error := obj.last_setup_error:
            return self.format_json(error)


admin.site.register(PaymentIntent, StripePaymentIntentAdmin)
admin.site.register(Charge, StripeChargeAdmin)
admin.site.register(Refund, StripeRefundAdmin)
admin.site.register(PaymentMethod, StripePaymentMethodAdmin)
admin.site.register(Customer, StripeCustomerAdmin)
admin.site.register(Dispute, StripeDisputeAdmin)
admin.site.register(SetupIntent, StripeSetupIntentAdmin)
