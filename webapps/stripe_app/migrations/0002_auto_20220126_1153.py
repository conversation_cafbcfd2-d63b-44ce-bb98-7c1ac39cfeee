# Generated by Django 3.2.10 on 2022-01-26 11:53

from django.db import migrations, models
import django.db.models.deletion
import lib.models


class Migration(migrations.Migration):

    dependencies = [
        ('stripe_app', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='customer',
            options={'verbose_name': 'Stripe customer', 'verbose_name_plural': 'Stripe customers'},
        ),
        migrations.AddField(
            model_name='customer',
            name='subscriber',
            field=models.CharField(
                blank=True,
                choices=[('billing', 'BILLING'), ('boost', 'BOOST')],
                max_length=30,
                null=True,
            ),
        ),
        migrations.CreateModel(
            name='SetupIntent',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('stripe_id', models.CharField(max_length=64, unique=True)),
                (
                    'subscriber',
                    models.CharField(
                        blank=True,
                        choices=[('billing', 'BILLING'), ('boost', 'BOOST')],
                        max_length=30,
                        null=True,
                    ),
                ),
                (
                    'cancellation_reason',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('abandoned', 'ABANDONED'),
                            ('requested_by_customer', 'REQUESTED_BY_CUSTOMER'),
                            ('duplicate', 'DUPLICATE'),
                        ],
                        max_length=30,
                        null=True,
                    ),
                ),
                ('client_secret', models.TextField(blank=True, null=True)),
                ('last_setup_error', models.JSONField(blank=True, null=True)),
                ('next_action', models.JSONField(blank=True, null=True)),
                ('payment_method_types', models.JSONField(blank=True, null=True)),
                (
                    'status',
                    models.CharField(
                        choices=[
                            (
                                'requires_payment_method',
                                'Intent created and requires a Payment Method to be attached.',
                            ),
                            ('requires_confirmation', 'Intent is ready to be confirmed.'),
                            (
                                'requires_action',
                                'Payment Method require additional action, such as 3D secure.',
                            ),
                            ('processing', 'Required actions have been handled.'),
                            (
                                'canceled',
                                'Cancellation invalidates the intent for future confirmation and cannot be undone.',
                            ),
                            (
                                'succeeded',
                                'Setup was successful and the payment method is optimized for future payments.',
                            ),
                        ],
                        max_length=30,
                    ),
                ),
                (
                    'usage',
                    models.CharField(
                        choices=[('on_session', 'ON_SESSION'), ('off_session', 'OFF_SESSION')],
                        default='off_session',
                        max_length=15,
                    ),
                ),
                (
                    'customer',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to='stripe_app.customer'
                    ),
                ),
            ],
            options={
                'verbose_name': 'Stripe setup intent',
                'verbose_name_plural': 'Stripe setup intents',
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
    ]
