# Generated by Django 4.0.2 on 2022-04-04 07:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('stripe_app', '0007_paymentmethod_detached_at'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='refund',
            name='receipt_number',
        ),
        migrations.AddField(
            model_name='refund',
            name='canceled_at',
            field=models.DateTimeField(blank=True, default=None, null=True),
        ),
        migrations.AddField(
            model_name='refund',
            name='created_at',
            field=models.DateTimeField(blank=True, default=None, null=True),
        ),
        migrations.AddField(
            model_name='refund',
            name='decimal_amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=9, null=True),
        ),
        migrations.AddField(
            model_name='refund',
            name='next_action',
            field=models.JSO<PERSON>ield(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='refund',
            name='charge',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name='refunds',
                to='stripe_app.charge',
            ),
        ),
        migrations.AlterField(
            model_name='refund',
            name='reason',
            field=models.CharField(
                blank=True,
                choices=[
                    ('duplicate', 'Duplicate charge'),
                    ('fraudulent', 'Fraudulent'),
                    ('requested_by_customer', 'Requested by customer'),
                ],
                max_length=50,
                null=True,
            ),
        ),
    ]
