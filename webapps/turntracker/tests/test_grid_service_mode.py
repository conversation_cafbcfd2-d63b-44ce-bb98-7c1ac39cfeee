from datetime import datetime
from decimal import Decimal
from unittest.mock import Mock

import pytest
import pytz
from freezegun import freeze_time

from webapps.booking.enums import AppointmentStatus, AppointmentType
from webapps.booking.models import Appointment
from webapps.turntracker.data_types import (
    TrackerBusiness,
    GridSource,
    TrackerBooking,
    TrackerStaffer,
    PenTile,
    Grid,
    GridRow,
    Tile,
    TileBooking,
    PenTileInTile,
    PaymentInfo,
)
from webapps.turntracker.enums import TileType, TileSize, TileIcon, TrackerMode
from webapps.turntracker.grid.grid_main import calculate_next_staffers, calculate_grid


@pytest.mark.django_db
def test_beginning_of_the_day():
    tracker = TrackerBusiness(business_id=1, timezone=None, settings=None)
    settings = Mock(
        predict_conflicts=True,
        show_revenue=True,
        mode=TrackerMode.SERVICE_MODE,
        count_in_scheduled=True,
        bonus_turns=False,
        predict_conflicts_interval=30,
    )

    grid_source = GridSource(
        business=tracker,
        bookings=[],
        staffers=[
            TrackerStaffer(staffer_id=1, hours=[(540, 1080)]),  # 9:00 - 18:00
            TrackerStaffer(staffer_id=2, hours=[(540, 1080)]),
            TrackerStaffer(staffer_id=3, hours=[(540, 1080)]),
            TrackerStaffer(staffer_id=9, hours=[(540, 1080)]),
            TrackerStaffer(staffer_id=8, hours=[(540, 1080)]),
        ],
        tiles=[],
    )

    with freeze_time(datetime(2022, 8, 10, 15, 15)):  # 915
        staffers_res = calculate_next_staffers(settings, grid_source)
        assert staffers_res == [1, 2, 3, 9, 8]

        grid_res = calculate_grid(settings, grid_source)
        expected_result = Grid(
            rows=[
                GridRow(
                    staffer_id=1,
                    tiles=[
                        Tile(
                            booked_from=None,
                            type=TileType.NEXT,
                            size=TileSize.FULL,
                            icon=None,
                            amount=None,
                            bookings=None,
                            pen_tile=None,
                        )
                    ],
                    revenue=Decimal('0'),
                ),
                GridRow(staffer_id=2, tiles=[], revenue=Decimal('0')),
                GridRow(staffer_id=3, tiles=[], revenue=Decimal('0')),
                GridRow(staffer_id=9, tiles=[], revenue=Decimal('0')),
                GridRow(staffer_id=8, tiles=[], revenue=Decimal('0')),
            ]
        )

        assert grid_res == expected_result


@pytest.mark.django_db
def test_all_busy():
    tracker = TrackerBusiness(business_id=1, timezone=None, settings=None)
    settings = Mock(
        predict_conflicts=True,
        show_revenue=True,
        mode=TrackerMode.SERVICE_MODE,
        count_in_scheduled=True,
        bonus_turns=False,
        predict_conflicts_interval=30,
    )

    grid_source = GridSource(
        business=tracker,
        bookings=[
            TrackerBooking(
                appointment_type=AppointmentType.BUSINESS,
                subbooking_id=2,
                appointment_id=2,
                appointment_status=AppointmentStatus.ACCEPTED,
                booked_from=910,
                staffer_id=2,
                service_variant_id=2,
                service_name='Service 2',
                service_price=Decimal('10.00'),
                is_settled=False,
                created=datetime(2022, 8, 10, 14, 10, tzinfo=pytz.utc),
                autoassign=True,
                duration=10,
                payment_info=PaymentInfo(checkout_booking_ids=[2], payable=False),
            ),
            TrackerBooking(
                appointment_type=AppointmentType.BUSINESS,
                subbooking_id=3,
                appointment_id=3,
                appointment_status=AppointmentStatus.ACCEPTED,
                booked_from=910,
                staffer_id=3,
                service_variant_id=3,
                service_name='Service 3',
                service_price=Decimal('10.00'),
                is_settled=False,
                created=datetime(2022, 8, 10, 14, 10, tzinfo=pytz.utc),
                autoassign=True,
                duration=10,
                payment_info=PaymentInfo(checkout_booking_ids=[3], payable=False),
            ),
            TrackerBooking(
                appointment_type=AppointmentType.BUSINESS,
                subbooking_id=4,
                appointment_id=4,
                appointment_status=AppointmentStatus.ACCEPTED,
                booked_from=930,
                staffer_id=3,
                service_variant_id=3,
                service_name='Service 3',
                service_price=Decimal('10.00'),
                is_settled=False,
                created=datetime(2022, 8, 10, 14, 10, tzinfo=pytz.utc),
                autoassign=True,
                duration=10,
                payment_info=PaymentInfo(checkout_booking_ids=[4], payable=False),
            ),
        ],
        staffers=[
            TrackerStaffer(staffer_id=3, hours=[(540, 1080)]),  # 9:00 - 18:00
            TrackerStaffer(staffer_id=2, hours=[(540, 1080)]),
        ],
        tiles=[],
    )

    with freeze_time(datetime(2022, 8, 10, 15, 15)):  # 915
        staffers_res = calculate_next_staffers(settings, grid_source)
        assert staffers_res == []

        grid_res = calculate_grid(settings, grid_source)
        expected_result = Grid(
            rows=[
                GridRow(
                    staffer_id=3,
                    tiles=[
                        Tile(
                            booked_from=910,
                            type=TileType.BUSY,
                            size=TileSize.FULL,
                            icon=None,
                            amount=Decimal('10.00'),
                            bookings=[
                                TileBooking(
                                    booked_from=910,
                                    service_name='Service 3',
                                    subbooking_id=3,
                                    appointment_id=3,
                                    service_color=None,
                                    appointment_status=Appointment.STATUS.ACCEPTED,
                                    duration=10,
                                    payment_info=PaymentInfo(
                                        checkout_booking_ids=[3], payable=False
                                    ),
                                )
                            ],
                            pen_tile=None,
                        ),
                        Tile(
                            booked_from=930,
                            type=TileType.BUSY,
                            size=TileSize.FULL,
                            icon=TileIcon.CLOCK,
                            amount=Decimal('10.00'),
                            bookings=[
                                TileBooking(
                                    booked_from=930,
                                    service_name='Service 3',
                                    subbooking_id=4,
                                    appointment_id=4,
                                    service_color=None,
                                    appointment_status=AppointmentStatus.ACCEPTED,
                                    duration=10,
                                    payment_info=PaymentInfo(
                                        checkout_booking_ids=[4], payable=False
                                    ),
                                )
                            ],
                            pen_tile=None,
                        ),
                    ],
                    revenue=Decimal('20.00'),
                ),
                GridRow(
                    staffer_id=2,
                    tiles=[
                        Tile(
                            booked_from=910,
                            type=TileType.BUSY,
                            size=TileSize.FULL,
                            icon=None,
                            amount=Decimal('10.00'),
                            bookings=[
                                TileBooking(
                                    booked_from=910,
                                    service_name='Service 2',
                                    subbooking_id=2,
                                    appointment_id=2,
                                    service_color=None,
                                    appointment_status=Appointment.STATUS.ACCEPTED,
                                    duration=10,
                                    payment_info=PaymentInfo(
                                        checkout_booking_ids=[2], payable=False
                                    ),
                                )
                            ],
                            pen_tile=None,
                        )
                    ],
                    revenue=Decimal('10.00'),
                ),
            ]
        )

        assert grid_res == expected_result


@pytest.mark.django_db
def test_general_case():
    tracker = TrackerBusiness(business_id=1, timezone=None, settings=None)
    settings = Mock(
        predict_conflicts=True,
        show_revenue=True,
        mode=TrackerMode.SERVICE_MODE,
        count_in_scheduled=True,
        bonus_turns=False,
        predict_conflicts_interval=30,
    )

    grid_source = GridSource(
        business=tracker,
        bookings=[
            TrackerBooking(
                subbooking_id=2,
                appointment_id=2,
                appointment_status=AppointmentStatus.ACCEPTED,
                booked_from=910,
                staffer_id=2,
                service_variant_id=2,
                appointment_type=AppointmentType.BUSINESS,
                service_name='Service 2',
                service_price=Decimal('10.00'),
                is_settled=False,
                created=datetime(2022, 8, 10, 14, 10, tzinfo=pytz.utc),
                autoassign=True,
                duration=10,
                payment_info=PaymentInfo(checkout_booking_ids=[2], payable=False),
            ),
            TrackerBooking(
                subbooking_id=3,
                appointment_id=3,
                appointment_status=AppointmentStatus.ACCEPTED,
                booked_from=925,
                staffer_id=3,
                service_variant_id=3,
                appointment_type=AppointmentType.BUSINESS,
                service_name='Service 3',
                service_price=Decimal('10.00'),
                is_settled=False,
                created=datetime(2022, 8, 10, 14, 10, tzinfo=pytz.utc),
                autoassign=True,
                duration=10,
                payment_info=PaymentInfo(checkout_booking_ids=[3], payable=False),
            ),
        ],
        staffers=[
            TrackerStaffer(staffer_id=1, hours=[(540, 1080)]),
            TrackerStaffer(staffer_id=2, hours=[(540, 1080)]),  # 9:00 - 18:00
            TrackerStaffer(staffer_id=3, hours=[(540, 1080)]),
            TrackerStaffer(staffer_id=9, hours=[(540, 1080)]),
            TrackerStaffer(staffer_id=8, hours=[(540, 1080)]),
        ],
        tiles=[
            PenTile(
                pentile_id=1,
                staffer_id=1,
                type=TileType.PENALTY,
                size=TileSize.FULL,
                icon=TileIcon.PEN,
                rotated=None,
            ),
            PenTile(
                pentile_id=3,
                staffer_id=1,
                type=TileType.PENALTY,
                size=TileSize.FULL,
                icon=TileIcon.PEN,
                rotated=None,
            ),
            PenTile(
                pentile_id=5,
                staffer_id=3,
                type=TileType.ADDED,
                size=TileSize.FULL,
                icon=TileIcon.PEN,
                rotated=None,
            ),
            PenTile(
                pentile_id=9,
                staffer_id=9,
                type=TileType.PENALTY,
                size=TileSize.FULL,
                icon=TileIcon.PEN,
                rotated=None,
            ),
            PenTile(
                pentile_id=8,
                staffer_id=8,
                type=TileType.ADDED,
                size=TileSize.FULL,
                icon=TileIcon.PEN,
                rotated=None,
            ),
        ],
    )

    with freeze_time(datetime(2022, 8, 10, 15, 15)):  # 915
        staffers_res = calculate_next_staffers(settings, grid_source)
        assert staffers_res == [9, 8, 1]  # staffer_id == 2 is busy; staffer_id == 3 - upcoming

        grid_res = calculate_grid(settings, grid_source)

        expected_result = Grid(
            rows=[
                GridRow(
                    staffer_id=1,
                    tiles=[
                        Tile(
                            booked_from=None,
                            type=TileType.PENALTY,
                            size=TileSize.FULL,
                            icon=TileIcon.PEN,
                            amount=None,
                            bookings=None,
                            pen_tile=PenTileInTile(pentile_id=1, staffer_id=1, rotated=None),
                        ),
                        Tile(
                            booked_from=None,
                            type=TileType.PENALTY,
                            size=TileSize.FULL,
                            icon=TileIcon.PEN,
                            amount=None,
                            bookings=None,
                            pen_tile=PenTileInTile(pentile_id=3, staffer_id=1, rotated=None),
                        ),
                    ],
                    revenue=Decimal('0'),
                ),
                GridRow(
                    staffer_id=2,
                    tiles=[
                        Tile(
                            booked_from=910,
                            type=TileType.BUSY,
                            size=TileSize.FULL,
                            icon=None,
                            amount=Decimal('10.00'),
                            bookings=[
                                TileBooking(
                                    booked_from=910,
                                    service_name='Service 2',
                                    subbooking_id=2,
                                    appointment_id=2,
                                    appointment_status=Appointment.STATUS.ACCEPTED,
                                    service_color=None,
                                    duration=10,
                                    payment_info=PaymentInfo(
                                        checkout_booking_ids=[2], payable=False
                                    ),
                                )
                            ],
                            pen_tile=None,
                        )
                    ],
                    revenue=Decimal('10.00'),
                ),
                GridRow(
                    staffer_id=3,
                    tiles=[
                        Tile(
                            booked_from=None,
                            type=TileType.ADDED,
                            size=TileSize.FULL,
                            icon=TileIcon.PEN,
                            amount=None,
                            bookings=None,
                            pen_tile=PenTileInTile(pentile_id=5, staffer_id=3, rotated=None),
                        ),
                        Tile(
                            booked_from=925,
                            type=TileType.BUSY,
                            size=TileSize.FULL,
                            icon=TileIcon.CLOCK,
                            amount=Decimal('10.00'),
                            bookings=[
                                TileBooking(
                                    booked_from=925,
                                    service_name='Service 3',
                                    subbooking_id=3,
                                    appointment_id=3,
                                    appointment_status=Appointment.STATUS.ACCEPTED,
                                    service_color=None,
                                    duration=10,
                                    payment_info=PaymentInfo(
                                        checkout_booking_ids=[3], payable=False
                                    ),
                                )
                            ],
                            pen_tile=None,
                        ),
                    ],
                    revenue=Decimal('10.00'),
                ),
                GridRow(
                    staffer_id=9,
                    tiles=[
                        Tile(
                            booked_from=None,
                            type=TileType.PENALTY,
                            size=TileSize.FULL,
                            icon=TileIcon.PEN,
                            amount=None,
                            bookings=None,
                            pen_tile=PenTileInTile(pentile_id=9, staffer_id=9, rotated=None),
                        ),
                        Tile(
                            booked_from=None,
                            type=TileType.NEXT,
                            size=TileSize.FULL,
                            icon=None,
                            amount=None,
                            bookings=None,
                            pen_tile=None,
                        ),
                    ],
                    revenue=Decimal('0'),
                ),
                GridRow(
                    staffer_id=8,
                    tiles=[
                        Tile(
                            booked_from=None,
                            type=TileType.ADDED,
                            size=TileSize.FULL,
                            icon=TileIcon.PEN,
                            amount=None,
                            bookings=None,
                            pen_tile=PenTileInTile(pentile_id=8, staffer_id=8, rotated=None),
                        )
                    ],
                    revenue=Decimal('0'),
                ),
            ]
        )

        assert grid_res == expected_result
