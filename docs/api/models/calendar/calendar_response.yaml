id: CalendarResponse
required:
  - start_date
  - end_date
  - resources
  - resources_per_page
  - resources_count
  - bookings
  - reservations
  - business_version
  - open_hours_customizations
  - agenda
  - timestamp
  - business_local_time
properties:
    start_date:
        type: string
    end_date:
        type: string
    resources:
        type: array
        items:
            type: CalendarResource
    resources_per_page:
        type: integer
    resources_count:
        type: integer
    bookings:
        type: CalendarBookingsMapping
    reservations:
        type: CalendarReservationsMapping
    business_version:
        type: number
    open_hours_customizations:
        type: object
    agenda:
        type: CalendarAgenda
    timestamp:
        type: integer
        description: >
            A UTC timestamp rounded to seconds that marks server time when calendar was generated.
            Send it back to Calendar Changed Dates endpoint to get info on what calendar dates
            were updated since this timestamp.
    business_local_time:
        type: datetime
        description: Local time formatted in business timezone.
