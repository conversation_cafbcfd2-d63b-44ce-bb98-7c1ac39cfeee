id: RepeatingBookingInfo
description: A RepeatingBooking info object without extra_bookings
required:
  - repeat
properties:
  id:
    description: ID of a RepeatingBooking
    type: integer
  _version:
    description: a version timestamp
    type: integer
  repeat:
    description: specifies interval on which booking repeats
    type: string
    enum_from_const: webapps.booking.enums.RepeatType
  end_type:
    description: >
      (required if repeat!='C') specifies how repeating is terminated
    type: string
    enum_from_const: webapps.booking.enums.RepeatEndType
  repeat_till:
    description: >
      (required if end_type=='D') specifies a date on which repeating ends
    type: string
    format: date-time
  repeat_number:
    description: >
      (required if end_type=='A') specifies after how many booking
      repeating ends
    type: integer
