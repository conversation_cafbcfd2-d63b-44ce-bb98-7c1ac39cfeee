id: CompactBusinessCustomer
description: Small representation of BCI.
required:
  - id
  - a_to_z
  - a_to_z_letter
  - first_name
  - last_name
  - cell_phone
  - email
  - photo_url
  - blacklisted
  - recurring
  - visit_frequency
  - no_shows
  - first_visit
  - is_user
properties:
  id:
    type: integer
    description: BCI ID
  a_to_z:
    type: string
    description:
        Customer identifier made from full_name or email
        or cell_phone, depending on which is non empty.
        This field is used to sort Customers lexicographically.
  a_to_z_letter:
    type: string
    description:
        The first letter of the customer a_to_z identifier.
        It is normalized - always an ascii uppercase letter or the '#' character.
  first_name:
    type: string
  last_name:
    type: string
  cell_phone:
    type: string
  email:
    type: string
  photo_url:
    type: string
    description: URL to customer's avatar. May be null.
  blacklisted:
    type: boolean
    description: Whether customer is blacklisted by business.
  recurring:
    type: boolean
    description: Whether customer is recurring.
  visit_frequency:
    type: integer
    description: Number of finished bookings by BusinessCustomer.
  no_shows:
    type: integer
    description: Number of noshow bookings by BusinessCustomer.
  first_visit:
    type: boolean
    description: True if BCI has exactly one booking with status
      Accepted, Unconfirmed, Modified or Proposed and no Finished/No-Shows.
  is_user:
    type: boolean
    description: Whether this user is Booksy user.
  discount:
    type: integer
    description: customer discount
  from_promo:
    type: boolean
    description: is this customer acquired from promo1
  invited:
    type: boolean
