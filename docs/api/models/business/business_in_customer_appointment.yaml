id: BusinessInCustomerAppointment
description: detailed info about a Business
required:
  - id
  - name
  - slug
  - phone
  - thumbnail_photo
  - location
  - reviews_count
  - reviews_stars
  - images
  - active
  - visible
  - partners
properties:
    id:
        type: integer
        description: ID of the Business
    name:
        type: string
        description: name of the Business
    slug:
        type: string
        description: slug of Business name
    phone:
        type: string
        description: phone number of the Business
    thumbnail_photo:
        type: string
        description: URL to Business' thumbnail photo
    location:
        type: BusinessDetailsLocation
        description: Business location data
    reviews_rank:
        type: number
        maximum: 5
        minimum: 1
        description: average rank of Business reviews (can be null)
    reviews_count:
        type: integer
        description: number of Business reviews (can be null)
    reviews_stars:
        type: integer
        maximum: 5
        minimum: 0
    images:
        type: BusinessImages
        description: (cover only!) a container for business images
    active:
        type: boolean
        description: is Business active
    visible:
        type: boolean
        description: is Business publicly visible
    primary_category:
        type: integer
        description: primary category id
    partners:
      type: array
      items:
        type: string
      description: list of Partner names for Business