id: TimeDelta
description: |
    Amount of time between two events (so diff or delta).
    Used i.e. for specifying how late customers can change their bookings.
    Not all endpoints must accept all properties, so this model is rather suggestion than guarantee.
properties:
    minutes:
        description: amount of minutes
        type: integer
    hours:
        description: amount of hours
        type: integer
    days:
        description: amount of days
        type: integer
    months:
        description: amount of months
        type: integer
    years:
        description: amount of years
        type: integer
