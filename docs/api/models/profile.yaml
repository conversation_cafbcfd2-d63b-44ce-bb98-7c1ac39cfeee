id: Profile
description:
    User business data.
    This returned for example after account creation.
required:
    - id
    - active
    - first_name
    - last_name
    - cell_phone
    - email
properties:
    id:
        type: integer
        description: User's ID
    active:
        type: boolean
        description: If user is active

    first_name:
        type: string
        description: User's first name
    last_name:
        type: string
        description: User's last name
    address_line_1:
        type: string
        description: User's address line 1 (street)
        defaultValue: ""
    address_line_2:
        type: string
        description: User's address line 2 (city)
        defaultValue: ""
    zipcode:
        type: string
        description: User's zipcode
    latitude:
        type: number
        description: User's address latitude
    longitude:
        type: number
        description: User's address longitude
    about_me:
        type: string
        description: User's own description of himself or herself
        defaultValue: ~
    gender:
        type: string
        description: User's gender
        enum_from_const: webapps.user.models.GENDERS
        defaultValue: ~
    birthday:
        type: string
        format: date
        description: Birthday (for unknown year use 1917)
    cell_phone:
        type: string
        description: User's cell phone number
    work_phone:
        type: string
        description: User's work phone number
        defaultValue: ""
    home_phone:
        type: string
        description: User's home phone number
        defaultValue: ""

    email:
        type: string
        description: User's email address
    facebook_id:
        type: string
        description: User's avatar
        defaultValue: ~
    photo:
        type: string
        description: User's avatar (full URL)
        defaultValue: ~
    language:
        type: string
        description: User's language
        enum:
            - pl
            - en
    payment_auto_accept:
        type: boolean
        description:
            Auto-accept all incoming call for payments using default
            payment method.
        defaultValue: False
