id: NotificationStatus
description: |
    Contains notification kind (with its human readable description),
    and information if user wants recieve it (and how).
required:
  - code
  - name
  - description
  - types
properties:
    code:
        type: string
        description: |
            code name of notification
            (for example "booking_status_change")
    name:
        type: string
        description: |
            short name of notification, translated
            (for example "Zmiana statusu rezerwacji")
    description:
        type: string
        description: |
            longer, helpful description of notification, translated
            (for example "Powiadom mnie, gdy moja rezerwacja jest potwierdzona, odrzucona lub zmienia status.")
    types:
        type: array
        items:
            type: NotificationType
        description: |
            Information where user wants recieve this kind of notification.
