id: FamilyAndFriendsBCI
description: Information about Member Profile that was used for appointment
properties:
    first_name:
        description: First name of member
        type: string
    last_name:
        description: Last name of member
        type: string
    full_name:
      description: Full name of member
      type: string
    photo:
      type:  string
      descritpion: User's avatar (full URL)
    relationship_type:
      type: string
      description: Type of relationship between booked_by and booked_for MP's
      enum_from_const: webapps.family_and_friends.enums.RelationshipType
    member_id:
        description: Member id
        type: integer
    user_id:
        description: User id
        type: integer
