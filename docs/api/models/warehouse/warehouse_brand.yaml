id: WarehouseBrand
description: |
    Brand
required:
    - id
    - name
    - commodities_count
    - created
properties:
    id:
        type: integer
        description: Brand Id
    name:
        type: string
        description: Brand name
    photo:
        type: Photo
        description: Brand logo
    archived:
        type: boolean
    commodities_count:
        type: integer
        description: count of commodities with this Brand
    created:
        type: string
        format: date-time
        description: creation time in business time zone
