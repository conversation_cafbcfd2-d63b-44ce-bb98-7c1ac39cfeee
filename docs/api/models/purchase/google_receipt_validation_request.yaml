id: GoogleReceiptValidationRequest
description: Response data from an In-app Billing in Google Play.
properties:
  receipt:
    type: string
    format: base64
    description: JSON- and Base64-encoded receipt from Google Play.
  signature:
    type: string
    format: base64
    description: Signature generated by Google, attached to receipt.
  token:
    type: string
    description: Internal token passed with API to verify Purchase request exists.
    required: False
