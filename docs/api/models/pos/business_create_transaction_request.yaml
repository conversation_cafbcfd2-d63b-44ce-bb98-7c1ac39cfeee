id: BusinessCreateTransactionRequest
description:
    Initialize or update POS transaction for booking or services.
required:
    - transaction_type
    - bookings
    - dry_run
properties:
    transaction_type:
        description: type of transaction (payment or deposit)
        type: string
        enum_from_const: pos.Transaction.TRANSACTION_TYPES
        defaultValue: P
    bookings:
        description: booking IDs of the bookings for transaction
        type: array
        items:
            type: BookingItem
    travel_fee:
        description: travel fee row for bookings with traveling services
        type: TravelFeeItem
    products:
        description: list of products
        type: array
        items:
            type: ProductItem
    vouchers:
        description: list of vouchers
        type: array
        items:
            type: Voucher
    tip:
        description: Selected tip
        type: PosTipInfo
    discount_rate:
        description: Discount rate
        type: integer
    payment_type_code:
        description: (DEPRECATED) Payment Type Code. Use only PaymentRows.
        type: string
    customer_card_id:
        type: integer
        description: customer BusinessCustomerInfo ID for walk-in customer
    customer_data:
        type: string
        description: customer name, email, phone for walk-in customer
    force_customer:
        type: boolean
        description: preserve selected (walk-in) customer dont auto assign customer from booking
    dry_run:
        description: set to false to save transaction record and start call for payment process
        type: boolean
    compatibilities:
        type: PosTransactionCompatibilities
    saved_bookings:
        description: list of bookings ids settled by this checkout
        type: array
        items:
            type: integer
    selected_register_id:
        description: If there are shared registers enabled and transaction shoud be added to specific one, put
          id of register here. If value will be valid, in response you will get selected_register object.
        type: integer
    payment_rows:
        type: array
        items:
            type: PaymentRowInfo
        description: Rows containing info about payments connected with transaction
    commodity_usage:
        type: array
        items:
            type: WarehouseReceiptRow
        description: Rows containing info about products/commodities used during booking
    txn_hash:
        type: string
        description: transaction hash used for commodity usage recalculation
