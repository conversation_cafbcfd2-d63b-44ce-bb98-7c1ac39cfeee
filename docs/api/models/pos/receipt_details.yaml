id: ReceiptDetails
description: Receipt for current transaction status
required:
    - id
    - status_code
    - status_type
    - receipt_number
    - card_type
    - card_last_digits
    - pnref
    - provider
    - payment_type
    - created
properties:
    id:
        type: integer
        description: Receipt ID
    status_code:
        type: string
        description: receipt status label
        enum_from_const: pos.Receipt.RECEIPT_STATUSES
    status_type:
        type: string
        description: type of status
        enum_from_const: webapps.pos.enums.receipt_status.STATUS_TYPES
    short_status:
        type: string
        description: minified type of status.
        enum_from_const: webapps.pos.enums.receipt_status.SHORT_STATUS_TYPES
    short_status_description:
        type: string
        description: Some statuses has additional line of text for displaying.
    short_status_label:
        type: string
        description: Label text for short_status.
    receipt_number:
        type: string
        description: receipt reference number
    card_type:
        type: string
        description: (can be null) credit card issuer
        enum_from_const: webapps.pos.enums.CARD_TYPES
    card_last_digits:
        type: string
        description: (can be null) card last 4 digits
    pnref:
        type: string
        description: external reference
    provider:
        type: string
        description: codename of PaymentProvider handling this Receipt
        enum_from_const: webapps.pos.provider.PAYMENT_PROVIDER_CHOICES
    payment_type:
        type: PaymentTypeInfo
        description: Payment type info.
    created:
        type: string
        description: Created as ISO8601 datetime string.
    payment_rows:
        type: array
        items:
            type: ReceiptPaymentRowInfo
        description: Rows containing info about payments connected with transaction
    note:
        type: ReceiptNote
        description: Now attached to Receipt. Now only Adyen reject reason you can find there.
