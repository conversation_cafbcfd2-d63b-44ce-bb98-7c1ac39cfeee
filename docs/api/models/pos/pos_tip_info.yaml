id: PosTipInfo
description: Chosen tip. It replaces tip_rate field.
required:
  - rate
  - type
properties:
  rate:
    type: string
    description: Rate parametr for selected tip. Recomended type - integer.
      For standard, percentage way of calculating tip equals percent rate - ex. 10% - 10.
      For restOf tip type - equals complement amount of cash - ex. client has to pay 103$, he will give 110, so rate = 110.
      For manual tip type - equals amount of desired tip - ex. 100$ tip -> rate = 100
  type:
    type: string
    description: Type of selected tip.
    enum_from_const: webapps.pos.tip_calculations.SimpleTip.TIP_TYPES
  tip_rows:
    description: List of tip rows for splitting tip between staffers
    type: array
    items:
      type: TipRow
  amount:
    type: string
    description: amount of the tip with currency
  amount_unformatted:
    type: string
    description: amount of the tip, decimal, max_digits=10, decimal_places=2
  label:
    type: string
    description: label to display
  amount_remaining:
    type: number
    format: float
    description: Remaining amount of the tip
  already_paid:
    type: string
    description: >
      (read-only) amount of the tip that was already paid with currency
  already_paid_unformatted:
    type: string
    description: (read-only), decimal, max_digits=10, decimal_places=2
