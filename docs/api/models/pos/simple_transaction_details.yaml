id: SimpleTransactionDetails
description: POS Simple Transaction Details
properties:
    id:
        type: integer
        description: Transaction ID
    transaction_type:
        type: string
        enum_from_const: pos.Transaction.TRANSACTION_TYPES
    appointment:
        description: id of appointment
        type: integer
    appointment_type:
        description: appointment type
        enum_from_const: webapps.booking.enums.AppointmentType
        type: string
    amount_without_tip:
        description: amount to be charged for simple checkout
        type: float
    remaining_amount_with_tip:
        description: remaining amount to pay with tip (RO)
        type: float
    remaining_amount_without_tip:
        description: amount to be charged for simple checkout with prepayment
        type: float
    empty_checkout:
        description: create transaction with single predefined service_name for amount_without_tip
        type: boolean
    rows:
        type: array
        items:
            type: TransactionRowDetails
    summaries:
        type: array
        items:
            type: TransactionSummaryRow
    tip:
        description: Selected tip
        type: PosTipInfo
    tip_choices:
        description: Possible tip amounts
        type: array
        items:
            type: PosTipChoices
    total:
        type: string
        description: Total Amount
    total_unformatted:
        type: float
        description: the same value as total, but unformatted
    receipts:
        type: array
        items:
            type: ReceiptDetails
        description: a list of all receipts for transaction
    payment_type_code:
        type: string
        description: selected payment type code
    payment_type_choices:
        type: array
        items:
            type: PaymentType
    business_name:
        type: string
        description: Business name
    business_address:
        type: string
        description: Business address
    customer_data:
        type: string
        description: Customer data
    customer_id:
        type: integer
    customer_card_id:
        type: integer
    customer_info:
        type: TransactionDetailsCustomerInfo

    actions:
        type: TransactionActions
        description: Specify which actions on transaction are available

    booking:
        type: integer
        description:
            (can be null)
            Booking ID of a booking associated with this transaction.
            This booking might be in one of TransactionRows.

    multibooking:
        type: integer
        description:
            (can be null)
            MultiBooking ID of a multibooking associated with this transaction.
            All of bookings in TransactionRows must be a part of this
            multibooking.

    deposit_info:
        type: TransactionInfo
        description:
            (can be null) additional info about deposit associated with
            this payment Transaction

    appointment_status:
        type: string
        description: (read-only) (skipped on listings) Appointment status
        enum_from_const: webapps.booking.enums.AppointmentStatusChoices

    appointment_customer_name:
        type: string
        description: (read-only) (skipped on listings) Appointment customer name

    commissions_enabled:
        type: boolean
        description: are commissions enabled

    operator:
        type: OperatorInfo
        description: (business_api only) Operator of transaction
    parent_txn:
        type: integer
        description: >
            (read-only) (can be null) ID of a parent Transaction in a series
    payment_rows:
        type: array
        items:
            type: PaymentRowInfo
        description: Rows containing info about payments connected with transaction
    split_payment_choices:
        type: array
        items:
            type: SplitPaymentChoicesInfo
    split_payment_remaining:
        type: string
        description: Value of remaining cash to split. Value with currency
    split_payment_ramaining_unformatted:
        type: float
        description: Value of remaining cash to split
    lock:
        type: boolean
        description: Logic describing if transaction can be edited is now moved in API.
            If flag is active you can not edit transaction. Front should only check if
            checkoutEditAllowed and this flag is false.
    receipt_footer_line_1:
        type: string
        description: (read-only) Receipt custom line 1
    receipt_footer_line_2:
        type: string
        description: (read-only) Receipt custom line 2
    tax_id:
        type: string
        description: customers tax_id
    cash_info:
        type: TransactionCashInfo
        description: Information about cash received and change for transaction
