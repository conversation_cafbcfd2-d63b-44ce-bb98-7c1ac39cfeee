id: ServiceVariantResponse
description: Variant of the Service
required:
  - type
  - duration
properties:
    id:
        type: integer
        description: ServiceVariant ID
    active:
        type: boolean
        description: is ServiceVariant still active
    type:
        type: string
        description: |
            ServiceVariant type
        enum_from_const: webapps.business.enums.PriceType
    duration:
        type: integer
        # type: number - in /customer_api/
        description: ServiceVariant duration in minutes
    price:
        type: number
        # type: string - in /customer_api/
        description: price of the ServiceVariant
    deposit:
        type: number
        description:
            Will be deprecated soon. Please use no_show_protection field instead
    prepayment:
        type: number
        description:
            Will be deprecated soon. Please use no_show_protection field instead
    prepayment_tax:
        type: number
        description:
            Will be deprecated soon. Please use no_show_protection field instead.
            Shows amount of tax included into prepayment. Used when business has
            tax_exclude mode turn on.
    time_slot_interval:
        type: integer
        description: interval between each possible time slot
        default_from_const: settings.SERVICE_VARIANT_DEFAULT_INTERVAL
    gap_hole_start_after:
        type: integer
        description:
            minutes after booked from when Gap Hole / Processing Time starts
    gap_hole_duration:
        type: integer
        description: duration of the Gap Hole / Processing Time
    note_to_customer:
        type: string
        description: note to customer from advanced service options
    no_show_protection:
        type: NoShowProtectionFee
        description:
            Type of no show protection fee. Type and percentage, payment_amount.
            Null if this service variant do not have  protection fee
    formula:
      type: array
      items:
        type: WarehouseReceiptRowResponse
      description: Show default sets of commodities needed for service
    combo_children:
        type: array
        items:
            type: ComboChildResponse
