id: MessageBlastTemplateResponse
properties:
    id:
        type: integer
    name:
        type: string
    description:
        type: string
    status:
        type: string
    status_label:
        type: string
    title:
        type: string
    body:
        type: string
    body_short:
        type: string
    image:
        type: ImageResponse
    date_text:
        type: string
        description: Next possible date
    date_default_selected:
        type: boolean
        description: If chosen date is default date
    date_text_recommended:
        type: string
        description: Original next possible recommended date
    channel_priority:
        type: ChannelPriority
    stop_code:
        type: string
