# Errors:
Error: !include docs/api/models/error.yaml
Errors: !include docs/api/models/errors.yaml

# Simple and common structs:
IdAndName: !include docs/api/models/id_and_name.yaml
IdAndNameAndSlug: !include docs/api/models/id_and_name_and_slug.yaml
IdAndNameAndPhoto:  !include docs/api/models/id_and_name_and_photo.yaml
IdAndNameAndType: !include docs/api/models/id_and_name_and_type.yaml
IdAndURL: !include docs/api/models/id_and_url.yaml
IdAndOrder: !include docs/api/models/id_and_order.yaml
Ordering: !include docs/api/models/ordering.yaml
NameAndPhoto: !include docs/api/models/name_and_photo.yaml
Photo: !include docs/api/models/photo.yaml
BizIdNameAndThumb: !include docs/api/models/business/biz_id_name_and_thumb.yaml
CSPhone: !include docs/api/models/cs_phone.yaml
CSWorkingHours: !include docs/api/models/cs_working_hour.yaml
SendEmailResponse: !include docs/api/models/send_email_response.yaml
TaskSchedule: !include docs/api/models/task_schedule.yaml


# Date and time:
AllDayTimeOff: !include docs/api/models/all_day_time_off.yaml

LegacyHours: !include docs/api/models/legacy_hours.yaml
OpenHoursCustomization: !include docs/api/models/open_hours_customization.yaml

TimeDelta: !include docs/api/models/time_delta.yaml
RelativeDelta: !include docs/api/models/relative_delta.yaml


# Geography:
GeoCoordinate: !include docs/api/models/geo_coordinate.yaml
GeoCoordinate2: !include docs/api/models/geo_coordinate_2.yaml
RegionDetails: !include docs/api/models/region_details.yaml
RegionSimple: !include docs/api/models/region_simple.yaml
BusinessLocationEx: !include docs/api/models/business/business_location_ex.yaml

RegionLocation: !include docs/api/models/region_location.yaml
RegionIDAndName: !include docs/api/models/region_id_and_name.yaml

BusinessDetailsLocation: !include docs/api/models/business/business_details_location.yaml


# Account creation, management and login:
Email: !include docs/api/models/email.yaml

EmailAndPassword: !include docs/api/models/email_and_password.yaml

PasswordChangeRequest: !include docs/api/models/password_change_request.yaml
AppleLoginRequest: !include docs/api/models/apple_login_request.yaml
AccessRights: !include docs/api/models/access_rights.yaml
LoginResponse: !include docs/api/models/login_response.yaml
ProfileDetailsResponse: !include docs/api/models/profile_details_response.yaml

Profile: !include docs/api/models/profile.yaml
CustomerProfile: !include docs/api/models/customer_profile.yaml

CreateUserData: !include docs/api/models/create_user_data.yaml
CreateBusinessDetails: !include docs/api/models/business/create_business_details.yaml
MyBusinessesResponse: !include docs/api/models/business/business_businesses.yaml
BusinessSimpleResults: !include docs/api/models/business/business_business_simple_result.yaml
BusinessSimpleResponse: !include docs/api/models/business/business_business_simple_response.yaml
BusinessSimpleDetailsResponse: !include docs/api/models/business/business_business_simple_details_response.yaml
BusinessDetailsResponse: !include docs/api/models/business/business_business_details_response.yaml
BusinessInBizListing: !include docs/api/models/business/business_business_inbiz.yaml
BusinessVisibilityResponse: !include docs/api/models/business/business_visibility.yml
AddressAndCity: !include docs/api/models/business/business_address_and_city.yaml

# SMS
SMSCodeRequest: !include docs/api/models/sms_code_request.yaml
SMSCodeResponse: !include docs/api/models/sms_code_response.yaml
SMSInvitationRequest: !include docs/api/models/sms_invitation_request.yaml
SMSInvitationResponse: !include docs/api/models/sms_invitation_response.yaml
MarketingNightAdjust: !include docs/api/models/marketing_night_adjust.yaml

# Business info
BusinessDetailsResults: !include docs/api/models/business/business_details_results.yaml
BusinessDetails: !include docs/api/models/business/business_details.yaml
BusinessBasicDetailsResults: !include docs/api/models/business/business_basic_details_results.yaml
BusinessBasicDetails: !include docs/api/models/business/business_basic_details.yaml
BusinessImages: !include docs/api/models/business/business_images.yaml
BusinessImage: !include docs/api/models/business/business_image.yaml
BusinessVisibilityStatusUpdateResponse: !include docs/api/models/business/business_visibility_status_update_response.yaml
BusinessVisibilityStatusResponse: !include docs/api/models/business/business_visibility_status_response.yaml
BusinessVisibilityStatusError: !include docs/api/models/business/business_visibility_status_error.yaml
SimplifiedBookingFeatureChecklist: !include docs/api/models/simplified_booking_feature_checklist.yaml

ServiceCategory: !include docs/api/models/service/service_category.yaml
Service: !include docs/api/models/service/service.yaml
ServiceResponse: !include docs/api/models/service/service_response.yaml
ServiceVariant: !include docs/api/models/service/service_variant.yaml
ServiceVariantResponse: !include docs/api/models/service/service_variant_response.yaml
ServiceAddOn: !include docs/api/models/business/service_addon.yaml
ServiceAddOnAppointment: !include docs/api/models/business/service_addon_appointment.yaml
ServicePriceData: !include docs/api/models/service/service_price.yaml
PriceData: !include docs/api/models/service/price.yaml

ServiceWrapper: !include docs/api/models/service/service_wrapper.yaml
ServicesWrapper: !include docs/api/models/service/services_wrapper.yaml
ServiceOrder: !include docs/api/models/service/service_order.yaml
ServicesOrdering: !include docs/api/models/service/services_ordering.yaml
ServiceCategoryWrapper: !include docs/api/models/service/service_category_wrapper.yaml
ServiceCategoriesWrapper: !include docs/api/models/service/service_categories_wrapper.yaml
ServicePhoto: !include docs/api/models/service/service_photo.yaml
ServiceQuestion: !include docs/api/models/service/service_question.yaml
ServiceQuestions: !include docs/api/models/service/service_questions.yaml
ComboChild:  !include docs/api/models/service/combo_child.yaml
ComboChildResponse:  !include docs/api/models/service/combo_child_response.yaml
ComboChildServiceVariant:  !include docs/api/models/service/combo_child_service_variant.yaml
ComboChildServiceVariantResponse:  !include docs/api/models/service/combo_child_service_variant_response.yaml

PhotoWithOrder: !include docs/api/models/photo_with_order.yaml
ShortCustomerInfo: !include docs/api/models/short_customer_info.yaml
ShortAppointmentCustomerInfo: !include docs/api/models/short_appointment_customer_info.yaml
ShortAppointment: !include docs/api/models/short_appointment.yaml
InviteAgainListing: !include docs/api/models/business/invite_again.yaml
InviteAgainListingInviteResponse: !include docs/api/models/business/invite_again_invite_response.yaml

# MARKETING - FEATURE STATUSES
BoostBan: !include docs/api/models/business/feature_status/boost_ban.yaml
BoostWarning: !include docs/api/models/business/feature_status/boost_warning.yaml
BusinessFeatureStatusResponse: !include docs/api/models/business/feature_status/business_features_status_results.yaml
BusinessBoostStatus: !include docs/api/models/business/feature_status/boost_status.yaml
InviteClientsStatus: !include docs/api/models/business/feature_status/invite_clients_status.yaml
MessageBlastStatus: !include docs/api/models/business/feature_status/message_blast.yaml
ServicePromotionsStatus: !include docs/api/models/business/feature_status/service_promotions_status.yaml
SocialMediaStatus: !include docs/api/models/business/feature_status/social_media_status.yaml
DetailedServicePromotionsStatus: !include docs/api/models/business/feature_status/service_promotions_details.yaml
DetailedServicePromotionStatus: !include docs/api/models/business/feature_status/service_promotion_details.yaml
DetailedBoostStatus: !include docs/api/models/business/feature_status/boost_details.yaml

# Quick invite
CustomerQuickInvite: !include docs/api/models/business/customer_quick_invite.yaml
CustomerQuickInviteResponse: !include docs/api/models/business/customer_quick_invite_response.yaml

# Business search models appended from docs/api/models/business/business_search.yaml
ServicesWordcloudsResponse: !include docs/api/models/services_wordclouds_response.yaml
ServiceWordcloud: !include docs/api/models/service_wordcloud.yaml
SuggestServicesResponse: !include docs/api/models/suggest_services_response.yaml

# Business Pop Ups
BusinessPopupResponse: !include docs/api/models/business/business_pop_up.yaml
LateCancellationNotification: !include  docs/api/models/business/late_cancellation_notification.yaml

# Review:
ReviewRequest: !include docs/api/models/review_request.yaml

ReviewResponseCustomer: !include docs/api/models/review_response_customer.yaml
ReviewResponse: !include docs/api/models/review_response.yaml
TopReviewResponse: !include docs/api/models/top_review_response.yaml
ReviewPerRankResponse: !include docs/api/models/review_per_rank_response.yaml
ReviewResponseListWithPagination: !include docs/api/models/review_response_list_with_pagination.yaml
ReviewRatingsResponseListWithPagination: !include docs/api/models/review_ratings_response_list_with_pagination.yaml
ReviewSingleResponse: !include docs/api/models/review_single_response.yaml

ReviewReplyRequest: !include docs/api/models/review_reply_request.yaml
ReviewReplyResponse: !include docs/api/models/review_reply_response.yaml

ReviewFeedbackStatus: !include docs/api/models/review_feedback_status.yaml
ReviewFeedbackWrapper: !include docs/api/models/review_feedback_wrapper.yaml
ReviewFeedbackInformation: !include docs/api/models/review_feedback_information.yaml

# Booking subobjects
BookingResource: !include docs/api/models/booking/booking_resource.yaml
BookingActions: !include docs/api/models/booking/booking_actions.yaml
BookingService: !include docs/api/models/booking/booking_service.yaml
BookingPaymentInfo: !include docs/api/models/booking/booking_payment_info.yaml
BookingCompatibilities: !include docs/api/models/booking/booking_compatibilities.yaml

# Booking in Business API
BookingActionRequest: !include docs/api/models/booking/booking_action_request.yaml
BusinessBookingCreate: !include docs/api/models/booking/business_booking_create.yaml
BusinessBookingUpdate: !include docs/api/models/booking/business_booking_update.yaml
BusinessBooking: !include docs/api/models/booking/business_booking.yaml
BusinessBookingResponse: !include docs/api/models/booking/business_booking_response.yaml
BusinessBookingCustomer: !include docs/api/models/booking/business_booking_customer.yaml

# Booking in Customer API
CustomerBooking: !include docs/api/models/booking/customer_booking.yaml
BusinessInCustomerBooking: !include docs/api/models/business/business_in_customer_booking.yaml
BusinessInCustomerAppointment: !include docs/api/models/business/business_in_customer_appointment.yaml

# Booking in Partner API
GrouponAvailability: !include docs/api/models/booking/groupon_availability.yaml
GrouponAvailabilityDetails: !include docs/api/models/booking/groupon_availability_details.yaml
GrouponBookingDetails: !include docs/api/models/booking/groupon_booking_details.yaml
GrouponCustomerDetails: !include docs/api/models/booking/groupon_customer_details.yaml
GrouponBookingIds: !include docs/api/models/booking/groupon_booking_ids.yaml
GrouponBookingResponse: !include docs/api/models/booking/groupon_booking_response.yaml
GrouponAppointmentResponse: !include docs/api/models/booking/groupon_appointment_response.yaml
GrouponBookingCreate: !include docs/api/models/booking/groupon_booking_create.yaml
GrouponBookingCancel: !include docs/api/models/booking/groupon_booking_cancel.yaml

# My Booksy in Customer API
MyBooksyResponse: !include docs/api/models/my_booksy/my_booksy_reponse.yaml
MyBooksyStaffer: !include docs/api/models/my_booksy/my_booksy_staffer.yaml
MyBooksyNotification: !include docs/api/models/my_booksy/my_booksy_notification.yaml
PopUpNotificationResponse: !include docs/api/models/my_booksy/pop_up_notification_response.yaml
RewardC2BStatusChangeNotification: !include docs/api/models/my_booksy/reward_c2b_status_change_pop_up_notification.yaml
ShortReviewNotification: !include docs/api/models/my_booksy/short_review_notification.yaml
FamilyAndFriendsInvitePopUpNotification: !include docs/api/models/my_booksy/family_and_friends_invite_notification.yaml
FamilyAndFriendsInvitationResponsePopUpNotification: !include docs/api/models/my_booksy/family_and_friends_invitation_response_notification.yaml
FamilyAndFriendsUnlinkPopUpNotification: !include docs/api/models/my_booksy/family_and_friends_unlink_notification.yaml

# MultiBooking
MultibookingInfo: !include docs/api/models/booking/multibooking_info.yaml
MultibookingResponseItem: !include docs/api/models/booking/multibooking_response_item.yaml
MultibookingStaffAvailability: !include docs/api/models/booking/multibooking_staff_availability.yaml

# RepeatingBooking
RepeatingBookingInfo: !include docs/api/models/booking/repeating_booking_info.yaml
BusinessRepeatingBooking: !include docs/api/models/booking/business_repeating_booking.yaml
BusinessRepeatingBookingResponse: !include docs/api/models/booking/business_repeating_booking_response.yaml

# Resource:
ResourceSimple: !include docs/api/models/resource_simple.yaml
ResourceDetails: !include docs/api/models/resource_details.yaml
ResourceDetailsWrapper: !include docs/api/models/resource_details_wrapper.yaml
ResourceDetailsListing: !include docs/api/models/resource_details_listing.yaml
ResourceDetailsWrapperListing: !include docs/api/models/resource_details_wrapper_listing.yaml


# Notification:
NotificationType: !include docs/api/models/notification_type.yaml
NotificationRecieverOptions: !include docs/api/models/notification_reciever_options.yaml
NotificationStatus: !include docs/api/models/notification_status.yaml
NotificationReciever: !include docs/api/models/notification_reciever.yaml
NotificationRecieverWrapper: !include docs/api/models/notification_reciever_wrapper.yaml
NotificationsResponse: !include docs/api/models/notifications_response.yaml
SendEmailData: !include docs/api/models/send_email_data.yaml


# Config:
ConfigCurrency: !include docs/api/models/config_currency.yaml
ConfigCurrencyDict: !include docs/api/models/config_currency_dict.yaml
ConfigDaytimeThresholds: !include docs/api/models/config_daytime_thresholds.yaml
ConfigMassUnit: !include docs/api/models/config_mass_unit.yaml
ConfigMassUnitDict: !include docs/api/models/config_mass_unit_dict.yaml
ConfigLocale: !include docs/api/models/config_locale.yaml
ConfigLocaleDict: !include docs/api/models/config_locale_dict.yaml
ConfigExternalApp: !include docs/api/models/config_external_app.yaml
ConfigAdyen: !include docs/api/models/config_adyen.yaml
ConfigGoogle: !include docs/api/models/config_google.yaml
ConfigCountry: !include docs/api/models/config_country.yaml
ConfigMaster: !include docs/api/models/config_master.yaml
ConfigResponse: !include docs/api/models/config_response.yaml
ServerTimeResponse: !include docs/api/models/server_time_response.yaml

# MP
TileSlotMarketPlace: !include docs/api/models/marketplace_tile_slots.yaml
SingleSlotMarketPlace: !include docs/api/models/single_promoted_business_slot.yaml
ClaimBListingRequest: !include docs/api/models/marketplace/claim_b_listing_request.yaml
# Customer TimeSlots
TimeSlotsCustomerResponse: !include docs/api/models/timeslots/time_slots_customer_response.yaml
TimeSlotsDate: !include docs/api/models/timeslots/time_slots_date.yaml
TimeSlotsRange: !include docs/api/models/timeslots/time_slots_range.yaml
TimeSlotsSlotedResponse: !include docs/api/models/timeslots/time_slots_sloted_response.yaml
TimeSlotsSlotedDate: !include docs/api/models/timeslots/time_slots_sloted_date.yaml
TimeSlotsSlot: !include docs/api/models/timeslots/time_slots_slot.yaml
TimeSlotsStaffTimeSlots: !include docs/api/models/timeslots/time_slots_staff_time_slots.yaml

# CalendarResponse
CalendarResponse: !include docs/api/models/calendar/calendar_response.yaml
CalendarResource: !include docs/api/models/calendar/calendar_resource.yaml
DragBooking: !include docs/api/models/calendar/drag_booking.yaml
CalendarBooking: !include docs/api/models/calendar/calendar_booking.yaml
CalendarBookingsMapping: !include docs/api/models/calendar/calendar_bookings_mapping.yaml
CalendarBookingResponse: !include docs/api/models/calendar/calendar_booking_response.yaml
CalendarBookingsResponse: !include docs/api/models/calendar/calendar_bookings_response.yaml
CalendarReservation: !include docs/api/models/calendar/calendar_reservation.yaml
CalendarReservationsMapping: !include docs/api/models/calendar/calendar_reservations_mapping.yaml
CalendarTimeOff: !include docs/api/models/calendar/calendar_timeoff.yaml
CalendarTimeOffsMapping: !include docs/api/models/calendar/calendar_timeoffs_mapping.yaml
CalendarAgenda: !include docs/api/models/calendar/calendar_agenda.yaml


# BookingChange
BookingChangesResponse: !include docs/api/models/booking_changes/booking_changes_response.yaml
BookingChangeGeneral: !include docs/api/models/booking_changes/booking_change_general.yaml
BookingChangeNotification: !include docs/api/models/booking_changes/booking_change_notification.yaml
BookingChangeBooking: !include docs/api/models/booking_changes/booking_change_booking.yaml
BookingChangeSubbooking: !include docs/api/models/booking_changes/booking_change_subbooking.yaml

# POS
BusinessCreateTransactionRequest: !include docs/api/models/pos/business_create_transaction_request.yaml
BusinessCreateSimpleTransactionRequest: !include docs/api/models/pos/business_create_simple_transaction_request.yaml
BusinessPOSSettings: !include docs/api/models/pos/business_pos_settings.yaml
BusinessPOSSettingsResponse: !include docs/api/models/pos/business_pos_settings_response.yaml
POSTaxRate: !include docs/api/models/pos/pos_tax_rate.yaml
POSTip: !include docs/api/models/pos/pos_tip.yaml
# POS NO_SHOW_PROTECTION
NoShowProtectionFeeResponse: !include docs/api/models/pos/no_show_protection_fee_response.yaml
ServiceCategoryWithPayment: !include docs/api/models/pos/service_category_with_payment.yaml
ServiceWithPayment: !include docs/api/models/pos/service_with_payment.yaml
CancelTimeOption: !include docs/api/models/pos/cancel_time_option.yaml
BsxLog: !include docs/api/models/pos/bsx_log.yaml
BsxSettings: !include docs/api/models/pos/bsx_settings.yaml
FormattedPrice: !include docs/api/models/pos/formatted_price.yaml
NoShowProtectionFee: !include docs/api/models/pos/no_show_protection_fee.yaml
NoShowProtectionFeeServiceRequest: !include docs/api/models/pos/no_show_protection_fee_service_request.yaml

BookingItem: !include docs/api/models/pos/booking_item.yaml
AppointmentBooksyPay: !include docs/api/models/pos/appointment_booksy_pay.yaml
BooksyPayDetails: !include docs/api/models/pos/booksy_pay.yaml
TravelFeeItem: !include docs/api/models/pos/travel_fee_item.yaml
ProductItem: !include docs/api/models/pos/product_item.yaml
ServicesTransactionRequest: !include docs/api/models/pos/services_transaction_request.yaml
ServiceDetailsTransactionRequest: !include docs/api/models/pos/service_details_transaction_request.yaml
SearchTransactionsResponse: !include docs/api/models/pos/search_transactions_response.yaml
TransactionSeriesResponse: !include docs/api/models/pos/transaction_series_response.yaml
TransactionDetailsResponse: !include docs/api/models/pos/transaction_details_response.yaml
TransactionDetails: !include docs/api/models/pos/transaction_details.yaml
SimpleTransactionDetailsResponse: !include docs/api/models/pos/simple_transaction_details_response.yaml
SimpleTransactionDetails: !include docs/api/models/pos/simple_transaction_details.yaml
TransactionActions: !include docs/api/models/pos/transaction_actions.yaml
TransactionRowDetails: !include docs/api/models/pos/transaction_row_details.yaml
TransactionSummaryRow: !include docs/api/models/pos/transaction_summary_row.yaml
BusinessTransactionActionRequest: !include docs/api/models/pos/business_transaction_action_request.yaml
CustomerTransactionActionRequest: !include docs/api/models/pos/customer_transaction_action_request.yaml
TransactionActionResponse: !include docs/api/models/pos/transaction_action_response.yaml
TransactionDetailsCustomerInfo: !include docs/api/models/pos/transaction_details_customer_info.yaml
ReceiptDetails: !include docs/api/models/pos/receipt_details.yaml
ReceiptNote: !include docs/api/models/pos/receipt_note.yaml
PaymentType: !include docs/api/models/pos/payment_type.yaml
PaymentTypeInfo: !include docs/api/models/pos/payment_type_info.yaml
TransactionInfo: !include docs/api/models/pos/transaction_info.yaml
POSPayByAppInfo: !include docs/api/models/pos/pos_pay_by_app_info.yaml
TransactionCommissionEditRequest: !include docs/api/models/pos/transaction_commission_edit_request.yaml
TransactionRowCommissionDetails: !include docs/api/models/pos/transaction_row_commission_details.yaml
PosTipInfo: !include docs/api/models/pos/pos_tip_info.yaml
PosTipChoices: !include docs/api/models/pos/pos_tip_choices.yaml
TipRow: !include docs/api/models/pos/tip_row.yaml
PosTransactionCompatibilities: !include docs/api/models/pos/pos_transaction_compatibilities.yaml
PaymentRowInfo: !include docs/api/models/pos/payment_row_info.yaml
PaymentRowsSummaryPaymentType: !include docs/api/models/pos/payment_rows_summary_payment_type.yaml
PaymentRowsSummaryResponse: !include docs/api/models/pos/payment_rows_summary_response.yaml
PaymentRowsSummaryTile: !include docs/api/models/pos/payment_rows_summary_tile.yaml
ReceiptPaymentRowInfo: !include docs/api/models/pos/receipt_payment_row_info.yaml
SplitPaymentChoices: !include docs/api/models/pos/split_payment_choices.yaml
TransactionCashInfo: !include docs/api/models/pos/transaction_cash_info.yaml
CashFlow: !include docs/api/models/pos/cash_flow.yaml
TransactionPaymentRow: !include docs/api/models/pos/transaction_payment_row.yaml
FundTransfer: !include docs/api/models/pos/fund_transfer.yaml
TaxRateSummaryDetails: !include docs/api/models/pos/tax_summary_details.yaml

BankAccountType: !include docs/api/models/pos/bank_account_type.yaml

POSFaceting: !include docs/api/models/pos/pos_faceting.yaml

PaymentMethodsListing: !include docs/api/models/pos/payment_methods_listing.yaml
PaymentMethodDetails: !include docs/api/models/pos/payment_method_details.yaml
PaymentMethodDetailsResponse: !include docs/api/models/pos/payment_method_details_response.yaml
PaymentMethodCreate: !include docs/api/models/pos/payment_method_create.yaml
PaymentMethodCreditCard: !include docs/api/models/pos/payment_method_credit_card.yaml
PaymentMethodBillingAddress: !include docs/api/models/pos/payment_method_billing_address.yaml

ThreeDData: !include docs/api/models/adyen/three_d_data.yaml

# Stripe Terminal
StripeTerminalHardwareFeatureDetails: !include docs/api/models/stripe_terminal/hardware_feature_details.yaml
StripeTerminalHardwareFeaturesList: !include docs/api/models/stripe_terminal/hardware_features_list.yaml
StripeTerminalHardwareDetails: !include docs/api/models/stripe_terminal/hardware_details.yaml
StripeTerminalHardwareList: !include docs/api/models/stripe_terminal/hardware_list.yaml
StripeTerminalOrderItemDetails: !include docs/api/models/stripe_terminal/order_item_details.yaml
StripeTerminalOrderPaymentDetails: !include docs/api/models/stripe_terminal/order_payment_details.yaml
StripeTerminalOrderDetails: !include docs/api/models/stripe_terminal/order_details.yaml
StripeTerminalOrdersList: !include docs/api/models/stripe_terminal/orders_list.yaml
StripeTerminalOrderItemRequest: !include docs/api/models/stripe_terminal/order_item_request.yaml
StripeTerminalOrderRequest: !include docs/api/models/stripe_terminal/order_request.yaml
StripeTerminalHardwareOrder: !include docs/api/models/stripe_terminal/stripe_hardware_order.yaml
StripeTerminalHardwareOrderObject: !include docs/api/models/stripe_terminal/stripe_hardware_order_object.yaml
StripeTerminalHardwareOrderWebhook: !include docs/api/models/stripe_terminal/stripe_hardware_order_webhook.yaml
StripeTerminalPaymentIntent: !include docs/api/models/stripe_terminal/stripe_payment_intent.yaml
StripeTerminalPaymentIntentObject: !include docs/api/models/stripe_terminal/stripe_payment_intent_object.yaml
StripeTerminalPaymentIntentWebhook: !include docs/api/models/stripe_terminal/stripe_payment_intent_webhook.yaml

# External Partners
ExternalPartnersDetails: !include docs/api/models/pos/external_partners_details.yaml
ExternalPaymentMethodDetails: !include docs/api/models/pos/external_payment_method_details.yaml

# Printer API
DiscountDetails: !include docs/api/models/printer_api/discount.yaml
ReceiptLine: !include docs/api/models/printer_api/receipt_line.yaml
PaymentLine: !include docs/api/models/printer_api/payment_line.yaml
PrintoutReceiptDetails: !include docs/api/models/printer_api/receipt_details.yaml
PrintoutDetails: !include docs/api/models/printer_api/printout_details.yaml
PrintoutResponse: !include docs/api/models/printer_api/printout_response.yaml
PrintoutListingResponse: !include docs/api/models/printer_api/printout_listening_reponse.yaml
PrinterLoginResponse: !include docs/api/models/printer_api/printer_login_response.yaml

# Register
RegisterCashInOutRequest: !include docs/api/models/register/register_cash_in_out_request.yaml
RegisterCashSummary: !include docs/api/models/register/register_cash_summary.yaml
RegisterCloseRequest: !include docs/api/models/register/register_close_request.yaml
RegisterCountedCashSummary: !include docs/api/models/register/register_counted_cash_summary.yaml
RegisterCountedSummary: !include docs/api/models/register/register_counted_summary.yaml
RegisterDetails: !include docs/api/models/register/register_details.yaml
RegisterDetailsResponse: !include docs/api/models/register/register_details_response.yaml
RegisterListingResponse: !include docs/api/models/register/register_listing_response.yaml
RegisterOnListing: !include docs/api/models/register/register_on_listing.yaml
RegisterOpenRequest: !include docs/api/models/register/register_open_request.yaml
RegisterOperation: !include docs/api/models/register/register_operation.yaml
RegisterOperationResponse: !include docs/api/models/register/register_operation_response.yaml
RegisterOperationsListingResponse: !include docs/api/models/register/register_operations_listing_response.yaml
RegisterSummary: !include docs/api/models/register/register_summary.yaml
RegisterTotalSummary: !include docs/api/models/register/register_total_summary.yaml
TransactionIdAndType: !include docs/api/models/register/transaction_id_and_type.yaml
OperatorInfo: !include docs/api/models/register/operator_info.yaml

# Purchases
SubscriptionListing: !include docs/api/models/purchase/subscription_listing.yaml
SubscriptionListingWithSms: !include docs/api/models/purchase/subscription_listing_with_sms.yaml
Subscription: !include docs/api/models/purchase/subscription.yaml
SubscriptionHistory: !include docs/api/models/purchase/subscription_history.yaml
SubscriptionPaymentMethod: !include docs/api/models/purchase/subscription_payment_method.yaml
SubscriptionBillingCycle: !include docs/api/models/purchase/subscription_billing_cycle.yaml
TrialInfo: !include docs/api/models/purchase/trial_info.yaml
GooglePurchaseRequest: !include docs/api/models/purchase/google_purchase_request.yaml
GoogleReceipt: !include docs/api/models/purchase/google_receipt.yaml
GoogleReceiptValidationRequest: !include docs/api/models/purchase/google_receipt_validation_request.yaml
AppleReceiptValidationRequest: !include docs/api/models/purchase/apple_receipt_validation_request.yaml
BraintreeSubscription: !include docs/api/models/purchase/braintree_subscription.yaml

# BCI Groups
BusinessCustomerGroupsResponse: !include docs/api/models/business/bci_groups/response.yaml
BusinessCustomerGroup: !include docs/api/models/business/bci_groups/group.yaml

# BCI
BusinessCustomer: !include docs/api/models/business/business_customer.yaml
BusinessCustomerData: !include docs/api/models/business/business_customer_data.yaml
BusinessCustomerUpdate: !include docs/api/models/business/business_customer_update.yaml
BCITypeData: !include docs/api/models/business/bci_type_data.yaml
BCITypeAdditionalData: !include docs/api/models/business/bci_type_additional_data.yaml
BCIPatientFileUpdate: !include docs/api/models/business/business_customer_patient_file_update.yaml
CustomerProfileData: !include docs/api/models/business/customer_profile_data.yaml
MergedCustomerData: !include docs/api/models/business/merged_customer_data.yaml
CustomerPhoto: !include docs/api/models/business/customer_photo.yaml
CompactBusinessCustomer: !include docs/api/models/business/compact_business_customer.yaml
ThinBusinessCustomer: !include docs/api/models/business/thin_business_customer.yaml
BusinessCustomerThinListing: !include docs/api/models/business/business_customer_thin_listing.yaml
BusinessCustomerExistsListing: !include docs/api/models/business/business_customer_exists_listing.yaml
BusinessCustomerManualMergeRequest: !include docs/api/models/business/business_customer_manual_merge_request.yaml
BusinessCustomerLastImportResult: !include docs/api/models/business/business_customer_last_import_result.yaml

# BCI Patient File Fields
BCIPatientFileFields: !include docs/api/models/bci_patient_file_fields.yaml
BCIPatientFileField: !include docs/api/models/bci_patient_file_field.yaml
BCIPatientFileFieldOption: !include docs/api/models/bci_patient_file_field_option.yaml

# Photo
PublishPhotoRequest: !include docs/api/models/photo/publish_photo_request.yaml
PublishPhotoResponse: !include docs/api/models/photo/publish_photo_response.yaml

# Appointments
AppointmentId: !include docs/api/models/appointment/appointment_id.yaml
AppointmentRepeatingInfo: !include docs/api/models/appointment/appointment_repeating_info.yaml
AppointmentConsentForm: !include docs/api/models/appointment/appointment_consent_form.yaml
ServiceVariantMultiMode: !include docs/api/models/appointment/service_variant_multi_mode.yaml
BookingDate: !include docs/api/models/appointment/booking_date.yaml
BookingBoxBusiness: !include docs/api/models/appointment/booking_box_business.yaml
BookingBox: !include docs/api/models/appointment/booking_box.yaml

# Business Appointment
AppointmentResponse: !include docs/api/models/appointment/appointment_response.yaml
AppointmentDetails: !include docs/api/models/appointment/appointment_details.yaml
SubbookingDetails: !include docs/api/models/appointment/subbooking_details.yaml
CustomerMultiMode: !include docs/api/models/appointment/customer_multi_mode.yaml
SubbookingAvailability: !include docs/api/models/appointment/subbooking_availability.yaml
ResourcesAvailabilityMapping: !include docs/api/models/appointment/resources_availability_mapping.yaml
ResourceAvailability: !include docs/api/models/appointment/resource_availability.yaml
BookAgainBusinessResponse: !include docs/api/models/appointment/book_again_business_response.yaml
BookAgainSubbookingDetails: !include docs/api/models/appointment/book_again_subbooking_details.yaml

# Customer Appointment
CustomerAppointmentResponse: !include docs/api/models/appointment/customer_appointment_response.yaml
CustomerAppointmentDetails: !include docs/api/models/appointment/customer_appointment_details.yaml
CustomerAppointmentPaymentDetails: !include docs/api/models/appointment/customer_appointment_payment_details.yaml
CustomerSubbookingDetails: !include docs/api/models/appointment/customer_subbooking_details.yaml
CreateAppointmentMetadata: !include docs/api/models/appointment/create_appointment_metadata.yaml
CreateAppointmentAnalytics: !include docs/api/models/appointment/create_appointment_analytics.yaml

# TravelingAppointment
AppointmentTraveling: !include docs/api/models/appointment/appointment_traveling.yaml

# Marketing
TemplateImageResource: !include docs/api/models/marketing/template_image_resource.yaml
UnsubscribeBlastRequest: !include docs/api/models/marketing/unsubscribe_blast_request.yaml
AppsFlyerRequest: !include docs/api/models/business/business_apps_flyer_request.yaml
C2bRewardResponse: !include docs/api/models/c2b_reward.yaml

# Digital Flyer Creator
DFCategoriesResponse: !include docs/api/models/df_creator/df_categories_response.yaml
DFCategoryResponse: !include docs/api/models/df_creator/df_category_response.yaml
DFBackground: !include docs/api/models/df_creator/df_backgrounds.yaml
DFTexts: !include docs/api/models/df_creator/df_texts.yaml
DFGroup: !include docs/api/models/df_creator/df_group.yaml
DFData: !include docs/api/models/df_creator/df_data.yaml
DFGeneralData: !include docs/api/models/df_creator/df_general_data.yaml

# Feedback
FeedbackRequest: !include docs/api/models/feedback_request.yaml
FeedbackResponse: !include docs/api/models/feedback_response.yaml

# Renting-Venue
ChangeDetailsRequest: !include docs/api/models/change_details_request.yaml
RentingVenue: !include docs/api/models/business/renting_venue/renting_venue.yaml
RentingVenueResponse: !include docs/api/models/business/renting_venue/renting_venue_response.yaml
RentingVenueLastUpdated: !include docs/api/models/business/renting_venue/renting_venue_last_updated.yaml
VenueContractorsListResponse: !include docs/api/models/business/renting_venue/venue_contractors_respone.yaml
VenueContractorDetails: !include docs/api/models/business/renting_venue/venue_contractor_details.yaml

# External business
ExternalBusinessCustomerContactRequest: !include docs/api/models/external_business/customer_contact.yaml
ExternalBusinessContactBusinessOwnerRequest: !include docs/api/models/external_business/business_contact.yaml

# Other and extras
ExperimentResponse: !include docs/api/models/experiment_response.yaml
ExperimentSlotExtraBody: !include docs/api/models/experiment_slot_extra_body.yaml

# Work Schedule aka Shiftworks
ScheduleDate: !include docs/api/models/schedule/schedule_date.yaml
BusinessScheduleItem: !include docs/api/models/schedule/business_schedule_item.yaml
ResourceScheduleItem: !include docs/api/models/schedule/resource_schedule_item.yaml
WeekdayHours: !include docs/api/models/schedule/weekday_hours.yaml
HoursRange: !include docs/api/models/schedule/hours_range.yaml
DateHoursRange: !include docs/api/models/schedule/date_hours_range.yaml
ResourceTimeOff: !include docs/api/models/schedule/resource_time_off.yaml

# Instagram updates
InstagramUpdateLink: !include docs/api/models/instagram_update_link.yaml
BusinessInstagramLink: !include docs/api/models/business_instagram_link.yaml

# Privacy Agreement
PrivacyAgreement: !include docs/api/models/privacy_agreement.yaml
BCIAgreementsDetails: !include docs/api/models/bci_agreements.yaml

# Business Agreements Listing
BusinessAgreementsListing: !include docs/api/models/business/service_usage/agreements/business_agreements_listing.yaml
BusinessAgreementDetails: !include docs/api/models/business/service_usage/agreements/business_agreement_details.yaml

# ServicePromotions
ServiceVariantPrice: !include docs/api/models/promotions/service_variant_price.yaml
ServiceVariantsCategory: !include docs/api/models/promotions/service_variants_category.yaml
PromotionServiceVariant: !include docs/api/models/promotions/promotion_service_variant.yaml
PromotionServiceVariants: !include docs/api/models/promotions/promotion_service_variants.yaml
ServcePromotionBookingCounter: !include docs/api/models/promotions/promotion_bookings_counter.yaml
LastMinutePromotionRequest: !include docs/api/models/promotions/last_minute_request.yaml
LastMinutePromotionResponse: !include docs/api/models/promotions/last_minute_response.yaml
FlashSalePromotionRequest: !include docs/api/models/promotions/flash_sale_request.yaml
FlashSalePromotionResponse: !include docs/api/models/promotions/flash_sale_response.yaml
HappyHoursServiceVariant: !include docs/api/models/promotions/happy_hours_service_variant.yaml
HappyHoursDay: !include docs/api/models/promotions/happy_hours_day.yaml
HappyHoursPromotionRequest: !include docs/api/models/promotions/happy_hours_request.yaml
HappyHoursPromotionResponse: !include docs/api/models/promotions/happy_hours_response.yaml
HappyHoursPromotionDeleteRequest: !include docs/api/models/promotions/happy_hours_delete_request.yaml
AppointmentServicePromotionDetails: !include docs/api/models/promotions/appointment_service_promotion_details.yaml
SubBookingSurchargeDetails: !include docs/api/models/promotions/subbooking_surcharge_details.yaml

# ServiceUsage
ServiceUsageResponse: !include docs/api/models/business/service_usage/service_usage_response.yaml
ServiceVoucherInfo: !include docs/api/models/business/service_usage/service_voucher_info.yaml
ServiceVariantIdStatus: !include docs/api/models/business/service_usage/service_variant_id_and_status.yaml

# Other
GDPRDescriptionsChoice: !include docs/api/models/gdpr_descriptions_choice.yaml
GDPRDescriptionsProperties: !include docs/api/models/gdpr_descriptions_properties.yaml
GDPRDescriptions: !include docs/api/models/gdpr_descriptions.yaml

# Coupon
Voucher: !include docs/api/models/coupon/voucher.yaml

#Boost
BoostDashboardResponse: !include docs/api/models/boost/boost_dashboard_response.yaml

# Market Pay
MarketPayAccountHolder: !include docs/api/models/market_pay/account_holder.yaml
MarketPayAccountHolderResponse: !include docs/api/models/market_pay/account_holder_response.yaml
MarketPayBusiness: !include docs/api/models/market_pay/business.yaml
MarketPayIndividual: !include docs/api/models/market_pay/individual.yaml
MarketPayPersonalDetails: !include docs/api/models/market_pay/personal_details.yaml
MarketPayBusinessDetails: !include  docs/api/models/market_pay/business_details.yaml
MarketPayContactDetails: !include docs/api/models/market_pay/contact_details.yaml
MarketPayAddress: !include docs/api/models/market_pay/address.yaml
MarketPayBankAccount: !include docs/api/models/market_pay/bank_account.yaml
MarketPayShareholderDetails: !include docs/api/models/market_pay/shareholder_details.yaml
MarketPayStatus:  !include docs/api/models/market_pay/status.yaml

# Consents
Consent: !include docs/api/models/consents/consent.yaml
ConsentCreate: !include docs/api/models/consents/consent_create.yaml
ConsentUpdate: !include docs/api/models/consents/consent_update.yaml
ConsentForm: !include docs/api/models/consents/consent_form.yaml
ConsentFormCreate: !include docs/api/models/consents/consent_form_create.yaml
ConsentFormUpdate: !include docs/api/models/consents/consent_form_update.yaml
ConsentFormField: !include docs/api/models/consents/consent_form_field.yaml
ConsentFormFieldCreate: !include docs/api/models/consents/consent_form_field_create.yaml
ConsentFormFieldUpdate: !include docs/api/models/consents/consent_form_field_update.yaml
ConsentDetailsResponse: !include docs/api/models/consents/consent_details_response.yaml
ConsentListingResponse: !include docs/api/models/consents/consent_listing_response.yaml
ConsentFormDetailsResponse: !include docs/api/models/consents/consent_form_details_response.yaml
ConsentFormListingResponse: !include docs/api/models/consents/consent_form_listing_response.yaml

# Metrics
BusinessApplicationsRequest: !include docs/api/models/metrics/business_applications_request.yaml

# Warehouse
WarehouseSupplier: !include docs/api/models/warehouse/supplier.yaml
WarehouseSuppliersList: !include docs/api/models/warehouse/suppliers_list.yaml
WarehousesResponse: !include docs/api/models/warehouse/warehouses_response.yaml
Warehouse: !include docs/api/models/warehouse/warehouse.yaml
CreateWarehouseDetails: !include docs/api/models/warehouse/create_warehouse_details.yaml
CreatedWarehousesResponse: !include docs/api/models/warehouse/created_warehouse_details_response.yaml
WarehouseResponse: !include docs/api/models/warehouse/warehouse_response.yaml
WarehouseBrand: !include docs/api/models/warehouse/warehouse_brand.yaml
WarehouseBrandsResponse: !include docs/api/models/warehouse/warehouse_brands_response.yaml
CreateWarehouseBrand: !include docs/api/models/warehouse/create_warehouse_brand.yaml
WarehouseBrandResponse: !include docs/api/models/warehouse/warehouse_brand_response.yaml
CreatedWarehouseBrandsResponse: !include docs/api/models/warehouse/created_warehouse_brand_response.yaml
SupplyDocumentRow: !include docs/api/models/warehouse/documents/supply_row.yaml
SupplyDocument: !include docs/api/models/warehouse/documents/supply_document.yaml
WarehouseDocumentRow: !include docs/api/models/warehouse/documents/document_row.yaml
WarehouseDocument: !include docs/api/models/warehouse/documents/document.yaml
WarehouseDocumentsList: !include docs/api/models/warehouse/documents/documents_list.yaml
CommodityDetails: !include docs/api/models/warehouse/commodity_details.yaml
CategoryCommodityGeneral: !include docs/api/models/warehouse/category_commodity_general.yaml
CommodityListDetails: !include docs/api/models/warehouse/commodity_list_details.yaml
CommodityDetailsRequest: !include docs/api/models/warehouse/commodity_details_request.yaml
CommodityDetailsResponse: !include docs/api/models/warehouse/commodity_details_response.yaml
CommodityDetailsListResponse: !include docs/api/models/warehouse/commodity_details_list_response.yaml
VolumeMeasure: !include docs/api/models/warehouse/volume_measure.yaml
VolumeMeasuresResponse: !include docs/api/models/warehouse/volume_measure_response.yaml
BarcodeDetails: !include docs/api/models/warehouse/barcode_details.yaml
BarcodesListResponse: !include docs/api/models/warehouse/barcodes_list_response.yaml
BarcodeDetailsResponse: !include docs/api/models/warehouse/barcode_details_response.yaml
BarcodeDetailsRequest: !include docs/api/models/warehouse/barcode_details_request.yaml
CategoryDetailsResponse: !include docs/api/models/warehouse/category_details_response.yaml
WarehouseCategoryDetails: !include docs/api/models/warehouse/category_details.yaml
CategoryDetailsRequest: !include docs/api/models/warehouse/category_details_request.yaml
CommodityCategoryDetailsListResponse: !include docs/api/models/warehouse/category_details_list_request.yaml
CommodityCategoryDetailsTreeResponse: !include docs/api/models/warehouse/category_details_tree_response.yaml
WarehouseRecipeDetails: !include docs/api/models/warehouse/warehouse_recipe_details.yaml
WarehouseReceiptRow: !include docs/api/models/warehouse/warehouse_recipe_row.yaml
WarehouseReceiptRowResponse: !include docs/api/models/warehouse/warehouse_recipe_row_response.yaml
CommodityDetailsTransactionRow: !include docs/api/models/warehouse/commodity_details_transaction_row.yaml
FormulaCreateEditBodyRequest: !include docs/api/models/warehouse/formula_create_edit_body_request.yaml
CommodityDetailsTransactionRowRequest: !include docs/api/models/warehouse/commodity_details_transaction_row_request.yaml
WarehouseCommoditiesResponse: !include docs/api/models/warehouse/warehouse_commodities_response.yaml
WarehouseCommoditiesStockLevel: !include docs/api/models/warehouse/warehouse_commodities_stock_level.yaml
CommoditiesListArchiveRequest: !include docs/api/models/warehouse/warehouse_commodities_list_archive_request.yaml
CommoditiesListDeleteRequest: !include docs/api/models/warehouse/warehouse_commodities_list_delete_request.yaml

# Voucher
SimpleCustomer: !include docs/api/models/voucher/simple_customer.yaml
VoucherTemplateDetailRequest: !include docs/api/models/voucher/voucher_template_detail_request.yaml
VoucherTemplateDetailResponse: !include docs/api/models/voucher/voucher_template_detail_response.yaml
VoucherTemplateSearchRequest: !include docs/api/models/voucher/voucher_template_search_request.yaml
VoucherTemplateService: !include docs/api/models/voucher/voucher_template_service.yaml
VoucherSearchRequest: !include docs/api/models/voucher/voucher_search_request.yaml
VoucherService: !include docs/api/models/voucher/voucher_service.yaml
VoucherShortResponse: !include docs/api/models/voucher/voucher_short_response.yaml
VoucherDetailResponse: !include docs/api/models/voucher/voucher_detail_response.yaml
VoucherDetailRequest: !include docs/api/models/voucher/voucher_detail_request.yaml
VoucherAggregatedResponse: !include docs/api/models/voucher/voucher_aggregated_response.yaml
VoucherAggregatedService: !include docs/api/models/voucher/voucher_aggregated_service_variant.yaml

# Invoices
BusinessInvoiceDocument: !include docs/api/models/invoice/invoice_document.yaml
BusinessInvoicesListing: !include docs/api/models/invoice/invoice_listing.yaml
BusinessInvoicesDownloadUrl: !include docs/api/models/invoice/download_url.yaml

# Navision
InvoiceDescription: !include docs/api/models/navision/invoice_description.yaml
InvoiceListResponse: !include docs/api/models/navision/invoice_list.yaml
InvoiceItemDataResponse: !include docs/api/models/navision/invoice_item_data.yaml
InvoiceDataResponse: !include docs/api/models/navision/invoice_data.yaml

# Salon Network
SalonNetworkMembers: !include docs/api/models/business/salon_network_members.yaml
BusinessSalonNetwork: !include docs/api/models/business/business_salon_network.yaml

# B2BReferral
B2BReferralCodeValidationResponse: !include docs/api/models/b2b_referral/b2b_referral_code_validation_response.yaml
B2BReferralDetails: !include docs/api/models/b2b_referral/b2b_referral_details.yaml
B2BReferralInvite: !include docs/api/models/b2b_referral/b2b_referral_invite.yaml
B2BReferralListingResponse: !include docs/api/models/b2b_referral/b2b_referral_listing_reponse.yaml
B2BReferralTermsAndConditionsResponse: !include docs/api/models/b2b_referral/b2b_referral_terms_and_conditions_response.yaml

# Invoicing
InvoiceItem: !include docs/api/models/invoicing/invoice_item.yaml
CustomerInvoice: !include docs/api/models/invoicing/customer_invoice.yaml
CustomerInvoiceList: !include docs/api/models/invoicing/customer_invoice_list.yaml
CreateCustomerInvoice: !include docs/api/models/invoicing/create_customer_invoice.yaml
InvoicingSeller: !include docs/api/models/invoicing/invoicing_seller.yaml
InvoicingSellerResponse: !include docs/api/models/invoicing/invoicing_seller_response.yaml
CreateInvoicingSeller: !include docs/api/models/invoicing/invoicing_seller_create_request.yaml
CreatedInvoicingSellerResponse: !include docs/api/models/invoicing/invoicing_seller_created_response.yaml
InvoiceBuyerCustomerInfo: !include docs/api/models/invoicing/invoicing_buier_customer_info.yaml
InvoicingBuyer: !include docs/api/models/invoicing/invoicing_buyer.yaml
InvoicingBuyersList: !include docs/api/models/invoicing/invoicing_buyer_list.yaml
InvoicingBCIBuyersList: !include docs/api/models/invoicing/invoicing_buyer_bci_list.yaml
InvoicingBuyerResponse: !include docs/api/models/invoicing/invoicing_buyer_response.yaml
CreateInvoicingBuyer: !include docs/api/models/invoicing/invoicing_buyer_create_request.yaml
CreatedInvoicingBuyerResponse: !include docs/api/models/invoicing/invoicing_buyer_created_response.yaml
BCIBuyerDetails: !include docs/api/models/invoicing/bci_buyer_details.yaml
BCIWithBuyer: !include docs/api/models/invoicing/bci_with_buyer.yaml
BCIWithBuyersList: !include docs/api/models/invoicing/bci_with_buyer_list.yaml


CashRegisterDocumentItem: !include docs/api/models/invoicing/cash_register_document_item.yaml
CashRegisterDocument: !include docs/api/models/invoicing/cash_register_document.yaml
CashRegisterDocumentList: !include docs/api/models/invoicing/cash_register_document_list.yaml
CashRegisterDocumentCreate: !include docs/api/models/invoicing/cash_register_document_create.yaml

TravelingToClients: !include docs/api/models/business/traveling_to_clients.yaml

# MessageBlast
ChannelPriority: !include docs/api/models/message_blast/channel_priority.yaml
CountMessageBlastRecipientResponse: !include docs/api/models/message_blast/count_message_blast_recipient_response.yaml
GroupItem: !include docs/api/models/message_blast/group_item.yaml
ImageCategoryItem: !include docs/api/models/message_blast/image_category_item.yaml
ImageResponse: !include docs/api/models/message_blast/image_response.yaml
ImageRespone: !include docs/api/models/message_blast/image_response.yaml
MessageBlastListingTemplate: !include docs/api/models/message_blast/message_blast_listing_template.yaml
MessageBlastImageCategoryTreeResponse: !include docs/api/models/message_blast/message_blast_image_category_tree_response.yaml
MessageBlastTemplateListingResponse: !include docs/api/models/message_blast/message_blast_template_listing_response.yaml
MessageBlastTemplateRequest: !include docs/api/models/message_blast/message_blast_template_request.yaml
MessageBlastScheduledResponse: !include docs/api/models/message_blast/message_blast_schedule_response.yaml
MessageBlastContentRequest: !include docs/api/models/message_blast/message_blast_content_template_request.yaml
MessageBlastContentTemplateResponse: !include docs/api/models/message_blast/message_blast_content_template_response.yaml
MessageBlastTemplateResponse: !include docs/api/models/message_blast/message_blast_template_response.yaml
ManualMessageBlastCostEstimationResponse: !include docs/api/models/message_blast/manual_message_blast_manual_cost_estimation_response.yaml
MessageBlastCostEstimationResponse: !include docs/api/models/message_blast/message_blast_manual_cost_estimation_response.yaml
MessageBlastCostEstimationReceivers: !include docs/api/models/message_blast/message_blast_manual_cost_estimation_receivers.yaml
MessageBlastCostEstimationGDPR: !include docs/api/models/message_blast/message_blast_manual_cost_estimation_gdpr.yaml
MessageBlastTestSendRequest: !include docs/api/models/message_blast/message_blast_test_send_request.yaml
OneTimeMessageBlastRequest: !include docs/api/models/message_blast/one_time_message_blast_request.yaml
OneTimeMessageBlastResponse: !include docs/api/models/message_blast/one_time_message_blast_response.yaml
CostRecipientsRequest: !include docs/api/models/message_blast/cost_recipients_request.yaml
RecipientsRequest: !include docs/api/models/message_blast/recipients_request.yaml
FlatRequest: !include docs/api/models/message_blast/flat_request.yaml

# Notification
Notification: !include docs/api/models/notification/notification.yaml
NotificationList: !include docs/api/models/notification/notification_list.yaml

# OTHER - phone number checks
PhoneNumberValidationRequest: !include docs/api/models/other/validate_phone_request.yaml
PhoneNumberValidationResponse: !include docs/api/models/other/validate_phone_response.yaml

# IntroScreens
IntroScreensResponse: !include docs/api/models/intro_screens/intro_screens_reponse.yaml
IntroScreens: !include docs/api/models/intro_screens/intro_screens.yaml
SplashScreens: !include docs/api/models/intro_screens/splash_screens.yaml
WelcomeBackScreens: !include docs/api/models/intro_screens/welcome_back_screens.yaml
IntroScreenDetails: !include docs/api/models/intro_screens/intro_screen_details.yaml

# Billing
BillingBusinessAccessResponse: !include docs/api/models/billing/billing_business_access_response.yaml

# Family and friends
FamilyAndFriendsCreateMember: !include docs/api/models/family_and_friends/create_new_member_profile.yaml
FamilyAndFriendsMember: !include docs/api/models/family_and_friends/member_info_response.yaml
FamilyAndFriendsRelationsListing: !include docs/api/models/family_and_friends/relations_listing_response.yaml
FamilyAndFriendsCustomerAppointment: !include docs/api/models/family_and_friends/members_appointments_response.yaml
FamilyAndFriendsBookedForCustomerAppointment: !include docs/api/models/family_and_friends/customer_appointment.yaml
FamilyAndFriendsForAppointment: !include docs/api/models/family_and_friends/family_and_friends_for_appointment.yaml
FamilyAndFriendsBCI: !include docs/api/models/family_and_friends/family_and_friends_bci.yaml

# Whats New
WhatsNewResponse: !include docs/api/models/whats_new/whats_new_listing_response.yaml
WhatsNew: !include docs/api/models/whats_new/whats_new.yaml

# Recaptcha
Recaptcha: !include docs/api/models/recaptcha.yaml

# HCaptcha
HCaptcha: !include docs/api/models/hcaptcha.yaml
HCaptchaEndpoint: !include docs/api/models/hcaptcha_endpoint.yaml

# Partner apps
PartnerApp: !include docs/api/models/partner_app/partner_app.yaml
PartnerAppConfig: !include docs/api/models/partner_app/partner_app_config.yaml
