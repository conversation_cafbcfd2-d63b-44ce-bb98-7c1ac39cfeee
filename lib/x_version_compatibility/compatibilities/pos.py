import operator as op

from lib.x_version_compatibility.compatibilities.base import BaseXVersionCompatibility
from lib.x_version_compatibility.dataclasses import CompatibilityRule
from lib.x_version_compatibility.typing import AppType, SourceName
from lib.x_version_compatibility.utils import parse_x_version
from webapps.booking.models import BookingSources
from webapps.consts import ANDROID, IPHONE


class FilterOutStripeCompatibility(BaseXVersionCompatibility):
    DEFAULT_VALUE: bool = False
    RULES: dict[AppType, dict[SourceName, CompatibilityRule]] = {
        BookingSources.CUSTOMER_APP: {
            ANDROID: CompatibilityRule(
                client_version=parse_x_version('2.6.1_338'),
                operator=op.lt,
            ),
            IPHONE: CompatibilityRule(
                client_version=parse_x_version('2.7.1'),
                operator=op.lt,
            ),
        }
    }
