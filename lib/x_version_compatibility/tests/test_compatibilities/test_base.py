import operator as op
from unittest import TestCase
from unittest.mock import MagicMock, patch

from packaging.version import Version
from parameterized import parameterized

from lib.x_version_compatibility.compatibilities.base import BaseXVersionCompatibility
from lib.x_version_compatibility.dataclasses import CompatibilityRule
from lib.x_version_compatibility.typing import AppType, RequestType, SourceName
from lib.x_version_compatibility.utils import parse_x_version
from webapps.booking.models import BookingSources
from webapps.consts import ANDROID, IPHONE


# pylint: disable=W0212
class TestBaseXVersionCompatibility(TestCase):
    class FakeCompatibility(BaseXVersionCompatibility):
        DEFAULT_VALUE = True
        RULES: dict[AppType, dict[SourceName, CompatibilityRule]] = {
            BookingSources.CUSTOMER_APP: {
                ANDROID: CompatibilityRule(
                    client_version=parse_x_version('1.0.0'),
                    operator=op.ge,
                ),
                IPHONE: CompatibilityRule(
                    client_version=parse_x_version('2.0.0'),
                    operator=op.ge,
                ),
            }
        }

    @staticmethod
    def _get_request() -> RequestType:
        return MagicMock(spec=RequestType, headers={'X-Version': '1.0.0'})

    @parameterized.expand(
        [
            (MagicMock(), None, None, None, True),  # no booking source, no version
            (MagicMock(), None, None, Version('1.5.0'), True),  # just version
            (MagicMock(), BookingSources.CUSTOMER_APP, IPHONE, None, True),  # just booking source
            (None, BookingSources.CUSTOMER_APP, IPHONE, Version('1.5.0'), True),  # no request
            (MagicMock(), BookingSources.CUSTOMER_APP, ANDROID, Version('1.0.0'), True),
            (MagicMock(), BookingSources.CUSTOMER_APP, ANDROID, Version('1.1.0'), True),
            (MagicMock(), BookingSources.CUSTOMER_APP, ANDROID, Version('0.0.9'), False),
            (MagicMock(), BookingSources.CUSTOMER_APP, IPHONE, Version('2.0.0'), True),
            (MagicMock(), BookingSources.CUSTOMER_APP, IPHONE, Version('2.1.0'), True),
            (MagicMock(), BookingSources.CUSTOMER_APP, IPHONE, Version('1.0.9'), False),
        ]
    )
    @patch('lib.x_version_compatibility.compatibilities.base.get_client_version')
    @patch('lib.x_version_compatibility.compatibilities.base.get_rule_path')
    def test_is_compatible(
        self,
        request,
        app_type,
        source_name,
        client_version,
        expected_result,
        mock_get_rule_path,
        mock_get_client_version,
    ):
        mock_get_rule_path.return_value = (app_type, source_name)
        mock_get_client_version.return_value = client_version

        self.assertEqual(self.FakeCompatibility.is_compatible(request), expected_result)

    def test_get_rules(self):
        self.assertEqual(self.FakeCompatibility.get_rules(), self.FakeCompatibility.RULES)

    @patch(
        'lib.x_version_compatibility.compatibilities.base.get_client_version',
        return_value=Version('1.5.0'),
    )
    @patch(
        'lib.x_version_compatibility.compatibilities.base.get_rule_path',
        return_value=(BookingSources.CUSTOMER_APP, IPHONE),
    )
    def test_call(self, mock_get_client_name, mock_get_client_version):
        request = self._get_request()

        # False expected as the provided Iphone v1.5.0 is lower than the defined v2.0.0
        self.assertFalse(self.FakeCompatibility(request))
        mock_get_client_name.assert_called_once_with(request)
        mock_get_client_version.assert_called_once_with(request)
