import os

from bo_obs.datadog import set_apm_tag_in_root_span, set_pod_name_in_root_span
from bo_obs.datadog.enums import BooksyTeams
from celery.signals import task_postrun, task_prerun
from ddtrace import tracer

from lib.datadog.celery.celery_tasks import celery_tasks_with_booksy_teams
from lib.dispatch_context import dispatch_context


def get_request_path():
    if handler := dispatch_context.handler:  # Tornado context have handler
        return (handler.__class__.__module__ + '.' + handler.__class__.__name__).lower()
    if request := dispatch_context.request:
        if hasattr(request, 'resolver_match'):
            return request.resolver_match._func_path.lower()  # pylint: disable=protected-access


def get_request_path_id():
    if dispatch_context.request and hasattr(dispatch_context.request, '_request_uuid'):
        return dispatch_context.request._request_uuid  # pylint: disable=protected-access


def propagate_task_sequence_number_to_context(
    task_id, task, *args, **kwargs
):  # pylint: disable=unused-argument
    metadata = getattr(task.request, '__metadata__', {})
    dispatch_context.no_task = metadata.get('task_number', 1)


def propagate_handler_scheduled_task_to_context(
    task_id, task, *args, **kwargs
):  # pylint: disable=unused-argument
    metadata = getattr(task.request, '__metadata__', {})
    dispatch_context.root_handler = metadata.get('root_handler', None)
    if metadata.get('root_handler', None) and os.getenv("DD_AGENT_HOST"):
        # This will only work if datadog instrumentation will be registered
        # before task_activate_context signal listener.
        span = tracer.current_span()
        if span.name == 'celery.run':
            span.set_tag_str('root_handler', metadata['root_handler'])
            return
        root_span = tracer.current_root_span()
        if root_span.name == 'celery.run':
            root_span.set_tag_str('root_handler', metadata['root_handler'])


def check_task_booksy_teams(task_name):
    booksy_teams_value = celery_tasks_with_booksy_teams.get(task_name)
    if not booksy_teams_value:
        booksy_teams_value = BooksyTeams.UNASSIGNED
    if len(booksy_teams_value) == 1:
        # to get a meaningful span tag (not the tupled value) from (BooksyTeams.TEAM,):
        booksy_teams_value = booksy_teams_value[0]
    return booksy_teams_value


def add_booksy_team_tag_to_root_span(task):
    booksy_teams = check_task_booksy_teams(task.name)
    if isinstance(booksy_teams, (set, tuple, list)):
        for team in booksy_teams:
            set_apm_tag_in_root_span(f'booksy_teams.{team}', True)
    else:
        set_apm_tag_in_root_span(f'booksy_teams.{booksy_teams}', True)


@task_prerun.connect
def task_activate_context(task_id, task, *args, **kwargs):
    dispatch_context.activate_in_celery(task_id, task)

    add_booksy_team_tag_to_root_span(task)
    set_pod_name_in_root_span()

    propagate_task_sequence_number_to_context(task_id=task_id, task=task, *args, **kwargs)
    propagate_handler_scheduled_task_to_context(task_id=task_id, task=task, *args, **kwargs)


@task_postrun.connect
def task_deactivate_context(
    task, task_id, state, *args, **kwargs
):  # pylint: disable=unused-argument
    dispatch_context.deactivate(in_celery=True)
