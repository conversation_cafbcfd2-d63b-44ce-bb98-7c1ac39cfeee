import os
import sys
import logging
import time
from datetime import datetime, timezone

from billiard.compat import mem_rss
from celery.signals import (
    after_task_publish,
    before_task_publish,
    celeryd_after_setup,
    celeryd_init,
    task_received,
    task_postrun,
    task_prerun,
    worker_process_init,
    worker_process_shutdown,
)
from datadog import statsd
from lib.dispatch_context import dispatch_context
from lib.celery_utils.context_helpers import get_request_path


POOL_TYPE = ''
WORKER_STARTUP_MEMORY = None
NTH_TASK_METRIC_TRESHOLD = 50
CELERY_TASK_PAYLOAD_METRIC_THRESHOLD = 500_000
celery_host = None  # pylint: disable=invalid-name
worker_type = None  # pylint: disable=invalid-name
current_worker_task_executed = 0  # pylint: disable=invalid-name

log_celery = logging.getLogger('booksy.celery')
log_task_oom = logging.getLogger('celery_metric')


def get_body_size_in_bytes(body, serializer):
    from kombu.serialization import dumps

    body_serialized = dumps(body, serializer=serializer)[2]
    return sys.getsizeof(body_serialized)


@celeryd_after_setup.connect
def capture_worker_name(sender, instance, **kwargs):  # pylint: disable=unused-argument
    global celery_host  # pylint: disable=global-statement
    global worker_type  # pylint: disable=global-statement
    celery_host = str(sender)
    worker_type = celery_host.split('@', maxsplit=1)[0]


@worker_process_init.connect
def set_memory_usage_after_worker_startup(*args, **kwargs):
    global WORKER_STARTUP_MEMORY  # pylint: disable=global-statement
    WORKER_STARTUP_MEMORY = mem_rss()


@celeryd_init.connect
def set_pool_type(conf=None, **kwargs):  # pylint: disable=unused-argument
    global POOL_TYPE  # pylint: disable=global-statement
    POOL_TYPE = kwargs['options']['pool'].__module__.split('.')[-1]
    # This is too late to start profile for leader worker (15 sec from start)


@task_received.connect
def metadata_task_received(sender, request, **kwargs):  # pylint: disable=unused-argument
    metadata = getattr(request._context, '__metadata__', None)  # pylint: disable=protected-access
    if metadata is not None:
        metadata['received_datetime'] = datetime.now(tz=timezone.utc).isoformat()


def propagate_task_sequence_number(sender, metadata):
    no_task = dispatch_context.no_task + 1 if dispatch_context.no_task is not None else 1
    metadata['task_number'] = no_task
    if no_task > NTH_TASK_METRIC_TRESHOLD and os.getenv("DD_AGENT_HOST"):
        statsd.distribution(
            'booksy.celery.task_sequence_number',
            no_task,
            tags=[f"task_name:{sender}"],
        )


def propagate_task_origin_handler(sender, metadata):  # pylint: disable=unused-argument
    metadata['root_handler'] = get_request_path() or dispatch_context.root_handler


@before_task_publish.connect
def metadata_before_task_publish(
    sender=None, headers=None, body=None, **kwargs
):  # pylint: disable=unused-argument
    metadata = {
        'publish_datetime': datetime.now(tz=timezone.utc).isoformat(),
    }
    propagate_task_sequence_number(sender, metadata)
    propagate_task_origin_handler(sender, metadata)
    headers['__metadata__'] = metadata


@after_task_publish.connect
def emit_celery_task_big_payload_metric(*args, **kwargs):
    if 'boost_set_switch_date_task' in kwargs.get('sender', ''):
        serializer = 'pickle'
    else:
        serializer = 'json'
    body_size = get_body_size_in_bytes(kwargs['body'], serializer)
    if body_size > CELERY_TASK_PAYLOAD_METRIC_THRESHOLD:
        if os.getenv("DD_AGENT_HOST"):
            statsd.distribution(
                'booksy.celery.task_payload', body_size, tags=[f"task_name:{kwargs['sender']}"]
            )
        else:
            log_celery.warning(
                'Task %s emited task with big payload %s',
                kwargs["sender"],
                body_size,
            )


@task_postrun.connect
def emit_task_memory_exceeded(
    task, task_id, state, *args, **kwargs
):  # pylint: disable=unused-argument
    global current_worker_task_executed  # pylint: disable=global-statement
    current_worker_task_executed += 1
    mem_max = task.app.conf.worker_max_memory_per_child
    memory_allocated = mem_rss()
    if memory_allocated >= mem_max and os.getenv("DD_AGENT_HOST"):
        statsd.event(
            f'Celery task on {os.getenv("DD_ENV")} exceeded memory.',
            f'{task.name} over {memory_allocated//1000}/{mem_max//1000} MB.',
            alert_type='warning',
            aggregation_key='celery-oom',
            tags=[
                f'celery_host:{task.request.hostname}',
                f'celery_task:{task.name}',
                f'tasks_executed:{current_worker_task_executed}',
            ],
        )


@task_prerun.connect
def set_task_memory_entry(*args, **kwargs):
    if POOL_TYPE in ('prefork', 'solo'):
        kwargs['task']._memory_usage = mem_rss() // 1000  # pylint: disable=protected-access


@task_postrun.connect
def emit_task_memory_usage(*args, **kwargs):
    if os.getenv("DD_AGENT_HOST") and POOL_TYPE in ('prefork', 'solo'):
        start_memory_usage = getattr(kwargs['task'], '_memory_usage', None)
        if start_memory_usage:
            memory_growth = mem_rss() // 1000 - start_memory_usage
            if memory_growth > 300:
                statsd.distribution(
                    'booksy.celery.task_memory_growth',
                    memory_growth,
                    tags=[f"task_name:{kwargs['task'].name}"],
                )


@worker_process_shutdown.connect
def emit_celery_shutdown_metrics(*_args, **kwargs):
    if os.getenv("DD_AGENT_HOST"):
        statsd.distribution(
            'booksy.celery.subworker_executed_tasks',
            current_worker_task_executed,
            tags=[
                f'worker_type:{worker_type}',
                f'exit_code:{kwargs.get("exitcode")}',
            ],
        )


if os.getenv("BOOKSY_COUNTRY_CODE") == 'pl':

    @task_prerun.connect
    def inject_task_process_time(
        *_args, task_id=None, task=None, **_kwargs
    ):  # pylint: disable=unused-argument
        task._process_time_ref = time.process_time()  # pylint: disable=protected-access
        task._start_time = time.perf_counter()  # pylint: disable=protected-access

    @task_postrun.connect
    def finish_task_timer(
        *_args, task_id=None, task=None, **_kwargs
    ):  # pylint: disable=unused-argument
        process_time_ref = getattr(task, '_process_time_ref', None)
        start_time = getattr(task, '_start_time', None)
        if process_time_ref is None or start_time is None:
            logging.getLogger('booksy.celery').error(
                'finish_task_timer do not have all required attributes '
                '_process_time_ref=%s, _start_time=%s',
                process_time_ref,
                start_time,
            )
            return
        try:
            # cpu_usage = (time.process_time() - process_time_ref) / (time.perf_counter() - start_time)
            cpu_usage = time.process_time() - process_time_ref
        except ZeroDivisionError:
            return

        if os.getenv("DD_AGENT_HOST"):
            statsd.histogram('booksy.celery.cpu_usage', cpu_usage, tags=[f"task_name:{task.name}"])


@task_prerun.connect
def metadata_task_prerun(task_id, task, *args, **kwargs):  # pylint: disable=unused-argument
    if os.getenv("DD_AGENT_HOST"):
        start_datetime = datetime.now(tz=timezone.utc)
        metadata = getattr(task.request, '__metadata__', {})
        publish_datetime = metadata.get('publish_datetime')
        received_datetime = metadata.get('received_datetime')
        eta = getattr(task.request, 'eta', None) or getattr(task.request, 'old_eta', None)
        if eta is not None:
            eta = datetime.fromisoformat(eta)
            if eta.tzinfo is None:
                eta = eta.replace(tzinfo=timezone.utc)

        if publish_datetime:
            parsed_publish_datetime = datetime.fromisoformat(publish_datetime)
            if eta is not None and parsed_publish_datetime < eta:
                time_to_start = start_datetime - eta
            else:
                time_to_start = start_datetime - parsed_publish_datetime
            if time_to_start.total_seconds() > 60.0:
                statsd.distribution(
                    'booksy.celery.task_time_to_start',
                    time_to_start.total_seconds(),
                    tags=[
                        f'worker_type:{worker_type}',
                        f'task_name:{task.name}',
                        f'eta:{eta is not None}',
                        f'country:{os.getenv("BOOKSY_COUNTRY_CODE")}',
                    ],
                )

        if received_datetime:
            parsed_received_datetime = datetime.fromisoformat(received_datetime)
            if eta is not None and parsed_received_datetime < eta:
                time_to_pick = start_datetime - eta
            else:
                time_to_pick = start_datetime - parsed_received_datetime
            if time_to_pick.total_seconds() > 60.0:
                statsd.distribution(
                    'booksy.celery.subworker_time_to_pick',
                    time_to_pick.total_seconds(),
                    tags=[
                        f'worker_type:{worker_type}',
                        f'task_name:{task.name}',
                        f'eta:{eta is not None}',
                        f'country:{os.getenv("BOOKSY_COUNTRY_CODE")}',
                    ],
                )
