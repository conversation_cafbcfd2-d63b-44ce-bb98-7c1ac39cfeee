# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from lib.deeplink.deeplinks_ms.protobuf import deeplinks_pb2 as deeplinks__pb2


class DeeplinkServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.get_deeplink = channel.unary_unary(
                '/deeplink_service.DeeplinkService/get_deeplink',
                request_serializer=deeplinks__pb2.DeeplinkRequest.SerializeToString,
                response_deserializer=deeplinks__pb2.DeeplinkResponse.FromString,
                )
        self.get_deeplink_repository = channel.unary_unary(
                '/deeplink_service.DeeplinkService/get_deeplink_repository',
                request_serializer=deeplinks__pb2.DeeplinkRequest.SerializeToString,
                response_deserializer=deeplinks__pb2.DeeplinkResponse.FromString,
                )
        self.create_deeplink = channel.unary_unary(
                '/deeplink_service.DeeplinkService/create_deeplink',
                request_serializer=deeplinks__pb2.DeeplinkRequest.SerializeToString,
                response_deserializer=deeplinks__pb2.DeeplinkResponse.FromString,
                )
        self.batch_create_deeplinks = channel.unary_unary(
                '/deeplink_service.DeeplinkService/batch_create_deeplinks',
                request_serializer=deeplinks__pb2.BatchDeeplinkRequest.SerializeToString,
                response_deserializer=deeplinks__pb2.BatchDeeplinkResponse.FromString,
                )
        self.update_deeplink = channel.unary_unary(
                '/deeplink_service.DeeplinkService/update_deeplink',
                request_serializer=deeplinks__pb2.DeeplinkRequest.SerializeToString,
                response_deserializer=deeplinks__pb2.DeeplinkResponse.FromString,
                )
        self.get_or_create_deeplink = channel.unary_unary(
                '/deeplink_service.DeeplinkService/get_or_create_deeplink',
                request_serializer=deeplinks__pb2.DeeplinkRequest.SerializeToString,
                response_deserializer=deeplinks__pb2.DeeplinkResponse.FromString,
                )


class DeeplinkServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def get_deeplink(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def get_deeplink_repository(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def create_deeplink(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def batch_create_deeplinks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def update_deeplink(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def get_or_create_deeplink(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DeeplinkServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'get_deeplink': grpc.unary_unary_rpc_method_handler(
                    servicer.get_deeplink,
                    request_deserializer=deeplinks__pb2.DeeplinkRequest.FromString,
                    response_serializer=deeplinks__pb2.DeeplinkResponse.SerializeToString,
            ),
            'get_deeplink_repository': grpc.unary_unary_rpc_method_handler(
                    servicer.get_deeplink_repository,
                    request_deserializer=deeplinks__pb2.DeeplinkRequest.FromString,
                    response_serializer=deeplinks__pb2.DeeplinkResponse.SerializeToString,
            ),
            'create_deeplink': grpc.unary_unary_rpc_method_handler(
                    servicer.create_deeplink,
                    request_deserializer=deeplinks__pb2.DeeplinkRequest.FromString,
                    response_serializer=deeplinks__pb2.DeeplinkResponse.SerializeToString,
            ),
            'batch_create_deeplinks': grpc.unary_unary_rpc_method_handler(
                    servicer.batch_create_deeplinks,
                    request_deserializer=deeplinks__pb2.BatchDeeplinkRequest.FromString,
                    response_serializer=deeplinks__pb2.BatchDeeplinkResponse.SerializeToString,
            ),
            'update_deeplink': grpc.unary_unary_rpc_method_handler(
                    servicer.update_deeplink,
                    request_deserializer=deeplinks__pb2.DeeplinkRequest.FromString,
                    response_serializer=deeplinks__pb2.DeeplinkResponse.SerializeToString,
            ),
            'get_or_create_deeplink': grpc.unary_unary_rpc_method_handler(
                    servicer.get_or_create_deeplink,
                    request_deserializer=deeplinks__pb2.DeeplinkRequest.FromString,
                    response_serializer=deeplinks__pb2.DeeplinkResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'deeplink_service.DeeplinkService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DeeplinkService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def get_deeplink(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplink_service.DeeplinkService/get_deeplink',
            deeplinks__pb2.DeeplinkRequest.SerializeToString,
            deeplinks__pb2.DeeplinkResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def get_deeplink_repository(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplink_service.DeeplinkService/get_deeplink_repository',
            deeplinks__pb2.DeeplinkRequest.SerializeToString,
            deeplinks__pb2.DeeplinkResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def create_deeplink(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplink_service.DeeplinkService/create_deeplink',
            deeplinks__pb2.DeeplinkRequest.SerializeToString,
            deeplinks__pb2.DeeplinkResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def batch_create_deeplinks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplink_service.DeeplinkService/batch_create_deeplinks',
            deeplinks__pb2.BatchDeeplinkRequest.SerializeToString,
            deeplinks__pb2.BatchDeeplinkResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def update_deeplink(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplink_service.DeeplinkService/update_deeplink',
            deeplinks__pb2.DeeplinkRequest.SerializeToString,
            deeplinks__pb2.DeeplinkResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def get_or_create_deeplink(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplink_service.DeeplinkService/get_or_create_deeplink',
            deeplinks__pb2.DeeplinkRequest.SerializeToString,
            deeplinks__pb2.DeeplinkResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
