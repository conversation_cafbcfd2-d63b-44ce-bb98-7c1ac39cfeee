import os
import typing as t

import yaml
from django.conf import settings

from country_config import Country
from lib.feature_flag.feature import ExtraAddressInCustomerConsentFlag


class BaseAgreementWrapper:
    FILE_PATH = None
    TYPE = None
    _instance = None
    raw_data = {}
    information = False
    is_blacklisted = False
    CUSTOMER = 'customer'
    BUSINESS = 'business'
    BUSINESS_CUSTOMER = 'business_customer'

    def __new__(cls):
        if not isinstance(cls._instance, cls):
            # init
            if cls.FILE_PATH is None:
                raise NotImplemented(
                    '{} must contain valid FILE_PATH variable'.format(cls.__name__)
                )
            # create singleton
            cls._instance = object.__new__(cls)
            if not os.path.exists(cls.FILE_PATH):
                raise ValueError('File doesn\'t exists {}'.format(cls.FILE_PATH))

            # TODO check file is not corrupted
            with open(cls.FILE_PATH, "r") as stream:
                docs = yaml.load_all(stream, Loader=yaml.SafeLoader)
                cls.raw_data = next(docs)
        return cls._instance

    @staticmethod
    def get_key() -> str:
        """Return key for current server"""
        if settings.API_COUNTRY in settings.GDPR_COUNTRIES:
            return settings.API_COUNTRY
        else:
            return settings.FALLBACK_KEY_GDPR

    @classmethod
    def _get_description(
        cls,
        type_description,
        network,
        business_id,
        business_name,
    ):
        """
        Return description depending on type_description:
            1) cls.BUSINESS
            2) cls.CUSTOMER
            3) cls.BUSINESS_CUSTOMER
        :param type_description: str. enum: cls.CUSTOMER, cls.BUSINESS or
        cls.BUSINESS_CUSTOMER
        :param network: str: optional business network name
        :param business_id: int: optional business id
        :return: str.
        """
        key_d = cls.get_key()

        keys = []
        if network:
            keys.append(f'{key_d}_description_{type_description}_{network}')
        keys.append(f'{key_d}_description_{type_description}')
        if key_d != settings.FALLBACK_KEY_GDPR:
            keys.append(f'{settings.FALLBACK_KEY_GDPR}_description_{type_description}')

        for key in keys:
            description = cls.raw_data.get(key)
            if description:
                return description.format(
                    business_id=business_id or '',
                    business_name=business_name or '',
                )
        return ''

    @classmethod
    def _get_title(
        cls,
        type_title,
        network,
        business_id,
        business_name,
    ):
        """
        Return description depending on type_title:
            1) cls.BUSINESS
            2) cls.CUSTOMER
            3) cls.BUSINESS_CUSTOMER
        :param type_title: str. enum: cls.CUSTOMER, cls.BUSINESS or
        cls.BUSINESS_CUSTOMER
        :param network: str: optional business network name
        :param business_id: int: optional business id
        :param business_name: str: optional business name
        :return: str.
        """
        key_t = cls.get_key()

        # look for title with specified network
        keys = []
        if network:
            keys.append(f'{key_t}_title_{type_title}_{network}')
        keys.append(f'{key_t}_title_{type_title}')
        if key_t != settings.FALLBACK_KEY_GDPR:
            keys.append(f'{settings.FALLBACK_KEY_GDPR}_title_{type_title}')

        for key in keys:
            title = cls.raw_data.get(key)
            if title:
                return title.format(
                    business_id=business_id or '',
                    business_name=business_name or '',
                )
        return ''

    # TITLE
    @classmethod
    def get_title(
        cls,
        network: t.Optional[str] = None,
        business_id: t.Optional[int] = None,
        business_name: t.Optional[str] = None,
    ) -> str:
        """Return title of given agreement"""
        if cls.TYPE is None:
            raise NotImplemented('{} must contain valid TYPE variable'.format(cls.__name__))
        return cls._get_title(
            cls.TYPE,
            network,
            business_id,
            business_name,
        )

    # DESCRIPTION
    @classmethod
    def get_description(
        cls,
        network: t.Optional[str] = None,
        business_id: t.Optional[int] = None,
        business_name: t.Optional[str] = None,
        business_address: t.Optional[str] = None,
    ) -> str:
        """Return description of given agreement"""
        if cls.TYPE is None:
            raise NotImplemented('{} must contain valid TYPE variable'.format(cls.__name__))
        return cls._get_description(
            cls.TYPE,
            network,
            business_id,
            business_name,
        )


# PRIVACY
class PrivacyAgreement(BaseAgreementWrapper):
    FILE_PATH = settings.GDPR_PRIVACY_POLICY_FILE


class PrivacyAgreementCustomer(PrivacyAgreement):
    TYPE = BaseAgreementWrapper.CUSTOMER


class PrivacyAgreementBusiness(PrivacyAgreement):
    TYPE = BaseAgreementWrapper.BUSINESS


# MARKETING
class MarketingAgreement(BaseAgreementWrapper):
    FILE_PATH = settings.GDPR_MARKETING_FILE


class MarketingAgreementCustomer(MarketingAgreement):
    TYPE = BaseAgreementWrapper.CUSTOMER


class MarketingAgreementBusiness(MarketingAgreement):
    TYPE = BaseAgreementWrapper.BUSINESS


# PARTNERS MARKETING
class PartnersMarketingAgreement(BaseAgreementWrapper):
    FILE_PATH = settings.GDPR_PARTNERS_MARKETING_FILE


class PartnersMarketingAgreementCustomer(PartnersMarketingAgreement):
    TYPE = BaseAgreementWrapper.CUSTOMER
    is_blacklisted = settings.API_COUNTRY not in (
        Country.PL,
        Country.GB,
        Country.ES,
        Country.IE,
        Country.PT,
        Country.FR,
        Country.NL,
    )


class PartnersMarketingAgreementBusiness(PartnersMarketingAgreement):
    TYPE = BaseAgreementWrapper.BUSINESS


class ReceivingMessages(BaseAgreementWrapper):
    FILE_PATH = settings.GDPR_RECEIVING_MESSAGES_CONSENT_FILE


class ReceivingMessagesBusiness(ReceivingMessages):
    TYPE = BaseAgreementWrapper.BUSINESS


# WEB COMMUNICATION
class WebCommunicationAgreement(BaseAgreementWrapper):
    FILE_PATH = settings.GDPR_WEB_COMMUNICATION_FILE


class WebCommunicationAgreementBusinessCustomer(WebCommunicationAgreement):
    TYPE = BaseAgreementWrapper.BUSINESS_CUSTOMER

    @classmethod
    def get_description(
        cls,
        network: t.Optional[str] = None,
        business_id: t.Optional[int] = None,
        business_name: t.Optional[str] = None,
        business_address: t.Optional[str] = None,
    ) -> str:
        if ExtraAddressInCustomerConsentFlag():
            business_name = ' '.join(filter(None, [business_name, business_address]))
        return super().get_description(network, business_id, business_name)


# DISCLOSURE OBLIGATION
class DisclosureObligationAgreement(BaseAgreementWrapper):
    FILE_PATH = settings.GDPR_DISCLOSURE_OBLIGATION_FILE
    information = True


class DisclosureObligationAgreementCustomer(DisclosureObligationAgreement):
    TYPE = BaseAgreementWrapper.CUSTOMER


class DisclosureObligationAgreementBusinessCustomer(DisclosureObligationAgreement):
    TYPE = BaseAgreementWrapper.BUSINESS_CUSTOMER


class DisclosureObligationAgreementBusiness(DisclosureObligationAgreement):
    TYPE = BaseAgreementWrapper.BUSINESS


# PROCESSING CONSENT
class ProcessingConsentAgreement(BaseAgreementWrapper):
    FILE_PATH = settings.GDPR_PROCESSING_CONSENT_FILE


class ProcessingConsentAgreementBusinessCustomer(ProcessingConsentAgreement):
    TYPE = BaseAgreementWrapper.BUSINESS_CUSTOMER
    is_blacklisted = True


# MAPPINGS
BUSINESS_AGREEMENTS = {
    settings.PP_AGREEMENT: PrivacyAgreementBusiness(),
    settings.MA_AGREEMENT: MarketingAgreementBusiness(),
    settings.PM_AGREEMENT: PartnersMarketingAgreementBusiness(),
}

if settings.API_COUNTRY == Country.US:
    BUSINESS_AGREEMENTS[settings.RM_AGREEMENT] = ReceivingMessagesBusiness()

BUSINESS_CUSTOMER_AGREEMENTS = {
    settings.WB_AGREEMENT: WebCommunicationAgreementBusinessCustomer(),
    settings.DO_AGREEMENT: DisclosureObligationAgreementBusinessCustomer(),
    settings.PC_AGREEMENT: ProcessingConsentAgreementBusinessCustomer(),
}

CUSTOMER_AGREEMENTS = {
    settings.PP_AGREEMENT: PrivacyAgreementCustomer(),
    settings.MA_AGREEMENT: MarketingAgreementCustomer(),
    settings.PM_AGREEMENT: PartnersMarketingAgreementCustomer(),
}
