import os

from mock import patch

from lib.lifecycle_utils import gracefully_drain_requests
from lib.lifecycle_utils.probe_const import LifecycleFiles
from lib.lifecycle_utils.tests.stat_example import (
    stat_request_only_in_workers,
    stat_with_request,
    stat_without_request,
)


@patch.object(os, 'environ', new={'UWSGI_STAT_ADDRESS': ':1234'})
@patch.object(gracefully_drain_requests, 'sleep')
@patch('lib.lifecycle_utils.lifecycle_helpers.Path')
def test_gracefully_max_wait(mock_path, mock_sleep):
    with patch('lib.lifecycle_utils.uwsgi_stats.get_uwsgi_stats') as mock_stats:
        mock_stats.side_effect = [
            stat_with_request,
            stat_with_request,
            stat_with_request,
            stat_request_only_in_workers,
            stat_without_request,
        ]
        gracefully_drain_requests.main(2)
        assert mock_stats.call_count == 2
        assert mock_sleep.call_count == 2
        mock_path.assert_called_once_with(LifecycleFiles.POD_REQUESTS_DRAINED)

    mock_path.reset_mock()
    mock_sleep.reset_mock()

    with patch('lib.lifecycle_utils.uwsgi_stats.get_uwsgi_stats') as mock_stats:
        mock_stats.side_effect = [
            stat_with_request,
            stat_with_request,
            stat_with_request,
            stat_request_only_in_workers,
            stat_without_request,
        ]
        gracefully_drain_requests.main(30)
        assert mock_stats.call_count == 5
        assert mock_sleep.call_count == 4
        mock_path.assert_called_once_with(LifecycleFiles.POD_REQUESTS_DRAINED)
