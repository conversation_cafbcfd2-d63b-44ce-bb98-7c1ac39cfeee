import pytest
from django.test import override_settings

from country_config.enums import Country
from lib.email_internal import generate_private_email
from lib.email_internal import is_private_email
from lib.email_internal import PRIVATE_EMAIL_DOMAIN
from lib.email_internal import PRIVATE_EMAIL_USERNAME


@pytest.mark.parametrize('phone', [' ', '', None])
def test_generate_empty_value(phone):
    assert generate_private_email(phone) == ''


@override_settings(API_COUNTRY=Country.PL)
@pytest.mark.parametrize(
    'phone',
    (
        '500600700',
        '+48500600700',
        '48500600700',
        '500 600 700',
        ' 500  600  700 ',
        '500-600-700',
        '48 500 600 700',
        '+48 500 600 700',
    ),
)
def test_generate(phone):
    # hashed phone '48500600700' with md5
    expected_email = '<EMAIL>'

    assert generate_private_email(phone) == expected_email


@override_settings(PRIVATE_EMAIL_HASH_SALT='some-salt')
def test_generate_with_salt():
    expected_email = '<EMAIL>'

    assert generate_private_email('+48 500 600 700') == expected_email


def test_generate_different_country():
    phone_without_country_code = '500 600 700'
    phone_with_country_code = '+48 500 600 700'
    with override_settings(API_COUNTRY=Country.PL):
        email_pl_no_cc = generate_private_email(phone_without_country_code)
        email_pl_with_cc = generate_private_email(phone_with_country_code)  # prefix +48

    with override_settings(API_COUNTRY=Country.US):
        email_us_no_cc = generate_private_email(phone_without_country_code)
        email_us_with_cc = generate_private_email(phone_with_country_code)  # prefix +1

    assert email_us_with_cc == email_pl_with_cc
    assert email_us_no_cc != email_pl_no_cc


def test_generate_different_phones():
    assert generate_private_email('500600700') != generate_private_email('600700800')


def test_is_private_email():
    internal_email = generate_private_email('600700800')

    assert is_private_email(internal_email) is True
    assert is_private_email(internal_email.upper()) is True
    assert is_private_email('<EMAIL>') is False
    assert is_private_email(f'some.email@{PRIVATE_EMAIL_DOMAIN}') is False
    assert is_private_email(f'{PRIVATE_EMAIL_USERNAME}.email@{PRIVATE_EMAIL_DOMAIN}') is False


def test_is_private_email_empty_value():
    assert is_private_email('') is False
    assert is_private_email(None) is False
