from __future__ import annotations

import inspect
from typing import Type

from dataclasses_json.api import DataClassJsonMixin
from django.db.models.fields.json import JSONField


class DataClassField(JSONField):
    """
    Field for classes implementing dataclasses_json.api.DataClassJsonMixin
    """

    dataclass_type: Type

    def __init__(self, dataclass_type, **kwargs):
        kwargs.setdefault('default', dataclass_type)
        kwargs.setdefault('blank', True)

        if not inspect.isclass(dataclass_type):
            raise TypeError(f'{dataclass_type} is not a class')

        if not issubclass(dataclass_type, DataClassJsonMixin):
            raise TypeError(f'{dataclass_type} should inherit from DataClassJsonMixin')

        self.dataclass_type = dataclass_type

        super().__init__(**kwargs)

    def deconstruct(self):
        name, path, args, kwargs = super().deconstruct()
        args = [self.dataclass_type]

        return name, path, args, kwargs

    def from_db_value(self, value, expression, connection):
        value = super().from_db_value(value, expression, connection)

        return self.dataclass_type.from_dict(value)

    def to_python(self, value):
        if isinstance(value, self.dataclass_type):
            return value

        if value is not None:
            value = super().to_python(value)
            value = self.dataclass_type.from_dict(value)

        return value

    def get_prep_value(self, value):
        if isinstance(value, self.dataclass_type):
            value = value.to_dict(encode_json=False)

        return super().get_prep_value(value)
