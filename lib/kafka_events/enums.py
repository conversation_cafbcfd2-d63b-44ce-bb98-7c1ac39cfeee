from enum import StrEnum

from settings import (
    BOOKSY_DOMAIN,
    API_COUNTRY,
    LOCAL_DEPLOYMENT,
)


class KafkaSecurityProtocol(StrEnum):
    SASL_PLAINTEXT = 'SASL_PLAINTEXT'
    SASL_SSL = 'SASL_SSL'


class KafkaSaslMechanism(StrEnum):
    SCRAM_SHA_256 = 'SCRAM-SHA-256'


class BooksyKafkaTopics(StrEnum):
    BALANCE_TRANSACTION = 'payments.balance_transaction-v1-avro'

    def get_full_topic_name(self):
        domain = BOOKSY_DOMAIN.split(':')[0] if LOCAL_DEPLOYMENT else BOOKSY_DOMAIN
        return f'{".".join(domain.split(".")[::-1])}.{API_COUNTRY}-{self.value}'
