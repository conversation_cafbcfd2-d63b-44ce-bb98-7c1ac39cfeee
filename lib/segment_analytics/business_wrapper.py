from datetime import timedelta

from lib.segment_analytics.enums import BUSINESS_STATUS_MAP
from lib.tools import (
    get_from_time_conversion,
    id_to_external_api,
    tznow,
)


class BusinessWrapper:
    """Helper wrapper for business ORM instance"""

    STAGE_NEW = 'New'
    STAGE_MATURE = 'Mature'

    def __init__(self, business):
        self.obj = business

    @property
    def user_id(self):
        segment_business_id = self.obj.integrations.get('segment_business_id')
        if segment_business_id:
            return segment_business_id

        # fallback to old style id schema
        return self.merchant_id

    @property
    def merchant_id(self):
        return id_to_external_api(self.obj.id)

    @property
    def phone(self):
        return self.obj.phone

    @property
    def location(self):
        region = self.obj.region
        city = self.obj.city_or_region_city
        address = self.obj.address
        return {
            'zipcode': region.name if region else None,
            'city': city if city else None,
            'address': address if address else None,
            'coordinate': {
                'latitude': self.obj.latitude,
                'longitude': self.obj.latitude,
            },
        }

    @property
    def timezone(self):
        return self.obj.get_timezone()._long_name  # pylint: disable=protected-access

    @property
    def email(self):
        return self.obj.owner.email if self.obj.owner.email else None

    @property
    def primary_category(self):
        return self.obj.primary_category.internal_name if self.obj.primary_category else ''

    @property
    def categories(self):
        return list(self.obj.categories.values_list('internal_name', flat=True))

    @property
    def open_hours(self):
        format_t24 = get_from_time_conversion()
        return sorted(
            [
                {
                    'day_of_week': bh.day_of_the_week,
                    'hour_from': format_t24(hour_from),
                    'hour_till': format_t24(hour_till),
                }
                for bh in self.obj.business_opening_hours.all()
                for hour_from, hour_till in bh.hours
            ],
            key=lambda bh: (bh['day_of_week'], bh['hour_from']),
        )

    @property
    def service_variants(self):
        from webapps.business.models import ServiceVariant

        return ServiceVariant.objects.filter(
            service__business=self.obj,
            # Service can be soft deleted, its variants should be excluded
            service__active=True,
            # Variant itself can be soft deleted as well, exclude it as well
            active=True,
        )

    def get_staffers(self, count=False):
        from webapps.business.models import Resource

        qs = self.obj.resources.filter(type=Resource.STAFF, active=True)
        if count:
            return qs.count()
        return qs

    @property
    def merchant_stage(self):
        if not self.obj.active_from:
            return None

        mature_date = (tznow() - timedelta(days=90)).date()
        if self.obj.active_from.date() >= mature_date:
            return self.STAGE_NEW
        return self.STAGE_MATURE

    def get_merchant_status(self, status=None, show_source=False):
        from webapps.business.models import Business

        if show_source and self.obj.status == Business.Status.PAID:
            return self.obj.get_payment_source_display()
        return BUSINESS_STATUS_MAP.get(status or self.obj.status)
