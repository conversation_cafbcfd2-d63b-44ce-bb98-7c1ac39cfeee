#!/usr/bin/env python
from time import monotonic

from datetime import date, datetime, time, timedelta

import typing as t
from dateutil.tz import gettz

from django.conf import settings
from django.db.models.expressions import Func
from django.db.models.fields import IntegerField
from django.db.models.functions import Cast, Coalesce


TZ_UTC = gettz('UTC')
EPOCH = datetime.fromtimestamp(0, tz=TZ_UTC)
DATE_EPOCH = EPOCH.date()


def datetime_to_timestamp(date_time):
    return (date_time - EPOCH).total_seconds()


def timestamp_to_datetime(timestamp, tz=None):
    date_time = EPOCH + timedelta(seconds=timestamp)

    if tz is None:
        return date_time
    return date_time.astimezone(tz)


def date_to_datestamp(value: date):
    return (value - DATE_EPOCH).days


def datestamp_to_date(stamp: int):
    return DATE_EPOCH + timedelta(days=stamp)


def version_timestamp(value):
    # UTC timezone is important: should business change its region
    # it won't affect booking versions
    value = value.astimezone(TZ_UTC)
    # time.mktime(updated.timetuple()) has resolution only in seconds
    # so we use strftime('%s.%f') to get millisecond resolution
    return int(float(value.strftime('%s.%f')) * 1000)


def format_timedelta(delta):
    value = int(delta.total_seconds())
    sign, value = ('-', -value) if value <= 0 else ('', value)
    value, seconds = divmod(value, 60)
    value, minutes = divmod(value, 60)
    days, hours = divmod(value, 24)
    if days != 0:
        return "%s%d_%.2d:%.2d:%.2d" % (sign, days, hours, minutes, seconds)
    return "%s%.2d:%.2d:%.2d" % (sign, hours, minutes, seconds)


def datetime_as_utc_str(date_time):
    """
    :param dt: datetime or None
    :return: str or None
    """
    if date_time:
        return date_time.astimezone(TZ_UTC).strftime(settings.DATETIME_FORMAT)
    return None


def extract_seconds(expression):
    return Cast(
        Coalesce(
            Func(
                expression,
                function='EXTRACT',
                template="%(function)s('epoch' from %(expressions)s)",
            ),
            0,
        ),
        IntegerField(),
    )


# nosemgrep: extends-custom-expression
class DatePartDay(Func):
    function = 'DATE_PART'
    template = "%(function)s('day', %(expressions)s)"

    def __and__(self, other):
        pass

    def __or__(self, other):
        pass

    def __rand__(self, other):
        pass

    def __ror__(self, other):
        pass


# nosemgrep: extends-custom-expression
class DateTruncDay(Func):
    function = 'DATE_TRUNC'
    template = "%(function)s('day', %(expressions)s)"

    def __and__(self, other):
        pass

    def __or__(self, other):
        pass

    def __rand__(self, other):
        pass

    def __ror__(self, other):
        pass


def min_max_range_from_date(date_to_extend, tzinfo=None):
    """Returns min and max for datetime for given date."""
    tzinfo = tzinfo or TZ_UTC
    date_from = datetime.combine(
        date_to_extend,
        time.min,
        tzinfo=tzinfo,
    )
    date_till = datetime.combine(
        date_to_extend,
        time.max,
        tzinfo=tzinfo,
    )

    return date_from, date_till


def get_week_day(value: t.Union[date, datetime]) -> int:
    return value.isoweekday() % 7


def ms_passed(start_time: float) -> int:
    """How many milliseconds have passed since a point in time."""
    return int(1000 * (monotonic() - start_time))
