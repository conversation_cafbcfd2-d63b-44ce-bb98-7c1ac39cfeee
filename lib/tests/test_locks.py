import time
import unittest

from lib.locks import AbstractLock


class AbstractLockTest(unittest.TestCase):

    def test_lock(self):
        class TestLock(AbstractLock):
            """BEWARE
            Lock expiration has been set to 500 ms.
            It has been observed that values below 400 ms do not work (!).
            It seems that for some reason <PERSON><PERSON> ignores entries with PX
            below 400 ms and such entries disappear immediately, what makes
            possible to create multiple locks on the same resource
            subsequently.
            """

            lock_name = 'test-lock'
            lock_expiration_time_ms = 500

        # Lock on Resource(id=1)
        assert TestLock.lock(1)

        # Lock on the same resource should fail
        assert not TestLock.lock(1)

        # Wait until lock has expired
        time.sleep(0.5)  # 500 ms

        # Lock should have expired and should be available again
        assert TestLock.lock(1)

        # Lock on another resource should succeed
        assert TestLock.lock(2)
