from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import StringFlag, BooleanFlag


class CancelChurnCustomerBookingExperiment(StringFlag):
    flag_name = 'Experiment_CancelChurnCustomerBookingExperiment'
    adapter = FeatureFlagAdapter.EPPO


class ExperimentOnboardingSpace(BooleanFlag):
    flag_name = 'Experiment_OnboardingSpace'
    adapter = FeatureFlagAdapter.EPPO
