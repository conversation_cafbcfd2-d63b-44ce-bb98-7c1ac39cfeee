from django.test import override_settings

from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import CustomUserAttributes


@override_settings(DEPLOYMENT_LEVEL='fake')
def test_default_user_data_object():
    user = UserData()

    assert user.custom['deployment_level'] == 'fake'
    assert user.key == 'fake-us'
    assert user.anonymous is True


@override_settings(DEPLOYMENT_LEVEL='fake')
def test_deployment_level_not_overwritten():
    user = UserData(custom={'deployment_level': 'aaa'})
    assert user.custom['deployment_level'] == 'fake'


@override_settings(API_COUNTRY='xd')
def test_default_country():
    user = UserData()
    assert user.country == 'xd'


@override_settings(API_COUNTRY='xd')
def test_overwrite_country():
    user = UserData(country='aaa')
    assert user.country == 'aaa'


@override_settings(DEPLOYMENT_LEVEL='fake')
def test_custom_user_key():
    user = UserData(key=123)
    assert user.key == 'fake-us-123'


def test_custom_attributes():
    user = UserData(custom={CustomUserAttributes.PHONE_NO: '123456789'})
    assert user.privateAttrs == [CustomUserAttributes.PHONE_NO]
