"""Flags related to bug fixes."""

from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag


class AddChannelsParametersToDeeplink(BooleanFlag):
    flag_name = 'Bug_AddChannelsParametersToDeeplink'
    adapter = FeatureFlagAdapter.LD


class AnyResourceAssignedFlag(BooleanFlag):
    flag_name = 'Bug_AnyResourceAssigned'
    adapter = FeatureFlagAdapter.LD


class DAC7PeselValidationFixFlag(BooleanFlag):
    flag_name = 'Fix_DAC7PeselValidationFlag'
    adapter = FeatureFlagAdapter.EPPO


class DeeplinkToMyBooksyInBookingNotficiations(BooleanFlag):
    flag_name = 'Bug_DeeplinkToMyBooksyInBookingNotificaions'
    adapter = FeatureFlagAdapter.LD


class DoNotEvaluateBciQueryForFalsyUserEmailOrPhoneFlag(BooleanFlag):
    flag_name = 'Bug_DoNotEvaluateBciQueryForFalsyUserEmailOrPhone'
    adapter = FeatureFlagAdapter.LD


class DoNotRemoveEmptyTaxGroupInInvoiceDetailsForm(BooleanFlag):
    flag_name = 'Bug_DoNotRemoveEmptyTaxGroupInInvoiceDetailsForm'
    adapter = FeatureFlagAdapter.EPPO


class FilterAppointmentsAndSubbookingsByTime(BooleanFlag):
    flag_name = 'Bug_FilterAppointmentsAndSubbookingsByTime'
    adapter = FeatureFlagAdapter.EPPO


class FixEmptyBusinessCoordinates(BooleanFlag):
    flag_name = 'Bug_FixEmptyBusinessCoordinates'
    adapter = FeatureFlagAdapter.LD


class FixErrorInReleaseDepositOnPaymentTask(BooleanFlag):
    flag_name = 'Bug_FixErrorInReleaseDepositOnPaymentTask'
    adapter = FeatureFlagAdapter.LD


class FixMissingReminderForRepeatingBooking(BooleanFlag):
    flag_name = 'Bug_FixMissingReminderForRepeatingBooking'
    adapter = FeatureFlagAdapter.LD


class FixRevenueForecastAlgorithm(BooleanFlag):
    flag_name = 'Bug_FixRevenueForecastAlgorithm'
    adapter = FeatureFlagAdapter.LD


class FixSlowAppointmentAdminQuery(BooleanFlag):
    flag_name = 'Bug_FixSlowAppointmentAdminQuery'
    adapter = FeatureFlagAdapter.EPPO


class FixTotalPriceInAppointmentListReport(BooleanFlag):
    flag_name = 'Bug_FixTotalPriceInAppointmentListReport'
    adapter = FeatureFlagAdapter.EPPO


class FrenchCertificationYearPeriodsRefactor(BooleanFlag):
    flag_name = 'Bug_FrenchCertificationYearPeriodsRefactor'
    adapter = FeatureFlagAdapter.EPPO


class InvitesValidationNotificationScheduleCount(BooleanFlag):
    flag_name = 'Bug_InvitesValidationNotificationScheduleCount'
    adapter = FeatureFlagAdapter.LD


class InvokeBCIPostSaveInUpdateUserBCITask(BooleanFlag):
    flag_name = 'Bug_InvokeBCIPostSaveInUpdateUserBCITask'
    adapter = FeatureFlagAdapter.EPPO


class MatcherDelaysProductFix(BooleanFlag):
    flag_name = 'Bug_MatcherDelaysProductFix'
    adapter = FeatureFlagAdapter.LD


class MissingStyleseatAppHeaderFlag(BooleanFlag):
    flag_name = 'Bug_MissingStyleseatAppHeaderFlag'
    adapter = FeatureFlagAdapter.LD


class NoRepeatingTextForGroupBookingFlag(BooleanFlag):
    flag_name = 'Bug_NoRepeatingTextForGroupBookingFlag'
    adapter = FeatureFlagAdapter.LD


class OverloadFakeBookAgainNoValidSlotFlag(BooleanFlag):
    flag_name = 'Bug_OverloadFakeBookAgainNoValidSlotFlag'
    adapter = FeatureFlagAdapter.EPPO


class PartnerOnlineBookingPreventMaskingCustomerNameFlag(BooleanFlag):
    flag_name = 'Bug_PartnerOnlineBookingPreventMaskingCustomerName'
    adapter = FeatureFlagAdapter.LD


class ReindexResourcesOnBatchResourceUpdateToolFlag(BooleanFlag):
    flag_name = 'Bug_ReindexResourcesOnBatchUpdateToolFlag'
    adapter = FeatureFlagAdapter.LD


class RemoveApplianceDataInCustomerAPIFlag(BooleanFlag):
    flag_name = 'Bug_RemoveApplianceDataInCustomerAPI'
    adapter = FeatureFlagAdapter.LD


class SendPushAfterAppointmentCancellationFlag(BooleanFlag):
    flag_name = 'Bug_SendPushAfterAppointmentCancellation'
    adapter = FeatureFlagAdapter.LD


class UseNoReplyAddressInReplyToFieldFlag(BooleanFlag):
    flag_name = 'Bug_UseNoReplyAddressInReplyToFieldFlag'
    adapter = FeatureFlagAdapter.LD


class UseTodayDateInBusinessTZForSimpleStatsChart(BooleanFlag):
    flag_name = 'Bug_UseTodayDateInBusinessTZForSimpleStatsChart'
    adapter = FeatureFlagAdapter.EPPO
