from lib.feature_flag.enums import ClientApp, CustomUserAttributes
from webapps.consts import WEB, ANDROID, IPHONE


# Private attributes are attributes containing potentially sensitive information which should not
# be accessible from Launch Darkly panel, example being auto-complete on filter settings.
USER_DATA_PRIVATE_ATTRS = {
    CustomUserAttributes.PHONE_NO,
}

CUSTOMER_BOOKING_SOURCE_TO_CLIENT_APP = {
    ANDROID: ClientApp.ANDROID_CUST,
    IPHONE: ClientApp.IOS_CUST,
    WEB: ClientApp.WEB_CUSTOMER_2019,
}
