from dataclasses import dataclass
from typing import Any

from lib.abc import ThreadSafeSingletonMeta
from lib.feature_flag.adapter import Kill<PERSON><PERSON><PERSON>dapter, LaunchDarklyAdapter, UserData, EppoAdapter
from lib.feature_flag.enums import FeatureFlagAdapter, FlagType
from lib.feature_flag.exceptions import InvalidKillSwitchChoiceError
from lib.feature_flag.typing import KillS<PERSON>Choices


@dataclass(slots=True)
class FeatureFlagFacade(metaclass=ThreadSafeSingletonMeta):
    """Feature flag facade.

    PLS remember to add new attributes to __slots__ but do not redefine existing.
    It is recommended not to inherit from this class.
    """

    kill_switch_adapter: KillSwitchAdapter
    ld_adapter: LaunchDarklyAdapter
    eppo_adapter: EppoAdapter

    def resolve(  # pylint: disable=too-many-arguments
        self,
        flag_name: KillSwitchChoices | str,
        adapter: FeatureFlagAdapter,
        user_data: UserData = None,
        default=False,
        flag_type: FlagType | None = None,
    ) -> Any:
        """Adapters are handled in specific order.
        The goal is to remove all KillSwitch usage.
        """
        if not user_data:
            user_data = UserData()
        if adapter == FeatureFlagAdapter.KILL_SWITCH:
            try:
                return self.kill_switch_adapter.evaluate(flag_name)
            except InvalidKillSwitchChoiceError:
                adapter = FeatureFlagAdapter.LD
        if adapter == FeatureFlagAdapter.LD:
            return self.ld_adapter.evaluate(flag_name, user_data, default)
        return self.eppo_adapter.evaluate(flag_name, user_data, flag_type, default)
