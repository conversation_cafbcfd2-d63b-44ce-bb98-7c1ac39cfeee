import base64
import io
import os
import re
import zipfile
from contextlib import closing

import lokalise
import requests

from lib.enums import TranslationLanguageCode
from lib.lokalise.utils import get_locale_dir, get_locale_full_path

PO_REVISION_DATE_RE = re.compile(rb'^"PO-Revision-Date:.*\n', flags=re.MULTILINE)


class LokaliseClient:
    PROJECT_ID = '82759371615ea98ce0a478.59365774'
    PROJECT_NAME = 'Booksy Core API'
    TOKEN = '41db93a4fc602dda267561b6241ef1c2afa45618'

    def __init__(self):
        self.client = lokalise.Client(self.TOKEN)
        self.project = self.client.project(self.PROJECT_ID)

    def upload_all_files(self):
        """
        should be used only for a new project (without any translations)
        """
        for lang in TranslationLanguageCode.values():
            self.upload_file(lang)

    def update_files(self):
        files = self.download_files()
        self._update_files(files)

    def download_files(self):
        bundle_url = self.get_bundle_url()
        response = requests.get(bundle_url, timeout=10)
        with closing(response), zipfile.ZipFile(io.BytesIO(response.content)) as file:
            files = {member.filename: file.read(member) for member in file.infolist()}
        return files

    @staticmethod
    def _update_files(files):
        for file, content in files.items():
            if content:
                lang = file.split('/')[0]
                if lang not in TranslationLanguageCode.values():
                    continue
                locale_dir = get_locale_dir(lang)
                locale_full_path = get_locale_full_path(lang)
                if not os.path.exists(locale_dir):
                    os.makedirs(locale_dir)

                # remove timestamps so the file won't be updated if there were no new translations
                content = PO_REVISION_DATE_RE.sub(b'', content)

                with open(locale_full_path, 'wb') as f:
                    f.write(content)

    def get_bundle_url(self):
        response = self.client.download_files(
            self.project.project_id,
            {
                'format': 'po',
                'original_filenames': True,
                'replace_breaks': True,
            },
        )
        return response['bundle_url']

    def upload_file(self, lang):
        locale_full_path = get_locale_full_path(lang)
        with open(locale_full_path, 'rb') as file:
            byte_content = file.read()
        base64_bytes = base64.b64encode(byte_content)
        base64_string = base64_bytes.decode('utf-8')
        process = self.client.upload_file(
            self.project.project_id,
            {
                'data': str(base64_string),
                'filename': 'django.po',
                'lang_iso': 'en_US' if lang == 'en' else lang,
                'convert_placeholders': False,
            },
        )
        process = self.client.queued_process(self.project.project_id, process.process_id)
        return process.status
