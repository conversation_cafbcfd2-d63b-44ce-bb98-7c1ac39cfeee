#!/usr/bin/env python
import datetime
import re

from django.conf import settings


class time24hour(datetime.time):
    """
    This class extends Python's time object to support 24 hour format,
    i.e. to accept 0:00 and 24:00 hours.
    """

    _24_hour = False

    def __new__(cls, *args, **kwargs):
        hour = 0

        if len(args) > 0:
            hour = args[0]
        if 'hour' in kwargs:
            hour = kwargs['hour']

        if hour == 24:
            minute = 0
            second = 0
            microsecond = 0
            if len(args) >= 2:
                minute = args[1]
            if len(args) >= 3:
                second = args[2]
            if len(args) >= 4:
                microsecond = args[3]

            ret = datetime.time.__new__(cls, 0, minute, second, microsecond)
            setattr(ret, '_24_hour', True)
            return ret

        ret = datetime.time.__new__(cls, *args, **kwargs)
        setattr(ret, '_24_hour', False)

        return ret

    def strftime(self, format_, zerodisp=False):
        if self._24_hour and '%H' in format_ and not zerodisp:
            format_ = format_.replace('%H', '24')

        return super(time24hour, self).strftime(format_)

    def __str__(self):
        return self.strftime('%H:%M:%S')

    def __repr__(self):
        ret = super(time24hour, self).__repr__()

        if self._24_hour:
            return ret.replace('(0', '(24')

        return ret

    def __eq__(self, other):
        if self._24_hour:
            return getattr(other, '_24_hour', False)

        if getattr(other, '_24_hour', False):
            return False

        return super(time24hour, self).__eq__(other)

    def __ne__(self, other):
        return not self.__eq__(other)

    def __lt__(self, other):
        if self._24_hour:
            return (self.minute, self.second, self.microsecond) < (
                other.minute,
                other.second,
                other.microsecond,
            )

        if getattr(other, '_24_hour', False):
            return True

        return super(time24hour, self).__lt__(other)

    def __le__(self, other):
        return self.__lt__(other) or self.__eq__(other)

    def __gt__(self, other):
        if self._24_hour:
            if getattr(other, '_24_hour', False):
                return (self.minute, self.second, self.microsecond) > (
                    other.minute,
                    other.second,
                    other.microsecond,
                )

            return True

        if getattr(other, '_24_hour', False):
            return False

        return super(time24hour, self).__gt__(other)

    def __ge__(self, other):
        return self.__gt__(other) or self.__eq__(other)

    def __sub__(self, other):
        my_hour = 24 if self._24_hour else self.hour
        other_hour = 24 if getattr(other, '_24_hour', False) else other.hour

        return datetime.timedelta(
            hours=my_hour,
            minutes=self.minute,
            seconds=self.second,
            microseconds=self.microsecond,
        ) - datetime.timedelta(
            hours=other_hour,
            minutes=other.minute,
            seconds=other.second,
            microseconds=other.microsecond,
        )


class PsycoPGTime24HourException(Exception):
    pass


def cast_time24hour(value, cur):
    if value is None:
        return None

    m = re.match(r"(\d+):(\d+):(\d+)", value)
    if m:
        return time24hour(int(m.group(1)), int(m.group(2)), int(m.group(3)))
    else:
        raise PsycoPGTime24HourException('Wrong format: %r' % value)


# Ugly fix but working and safe
UGLY_FIX_PL_MONTHS_NAMES_TABLE = [
    (u' styczeń ', u' stycznia '),
    (u' luty ', u' lutego '),
    (u' marzec ', u' marca '),
    (u' kwiecień ', u' kwietnia '),
    (u' maj ', u' maja '),
    (u' czerwiec ', u' czerwca '),
    (u' lipiec ', u' lipca '),
    (u' sierpień ', u' sierpnia '),
    (u' wrzesień ', u' września '),
    (u' październik ', u' października '),
    (u' listopad ', u' listopada '),
    (u' grudzień ', u' grudnia '),
]


def ugly_fix_pl_months_names(text, language):
    if 'pl' not in (language or ''):
        return text
    for wrong, right in UGLY_FIX_PL_MONTHS_NAMES_TABLE:
        text = text.replace(wrong, right)
    return text


def format_datetime(value, format_name, language, locale_code=None):
    """A wrapper for babel.dates.format_* for legacy code.

    Legacy code used stdlib.locale, which doesn't work on modern systems.

    """
    from lib.tools import get_locale_from_language

    if locale_code is None:
        locale_code = get_locale_from_language(language)

    from babel.dates import (
        format_time as babel_format_time,
        format_date as babel_format_date,
    )

    supported_formats = {
        'long_date_ymwd': lambda val, locale: babel_format_date(
            val,
            locale=locale_code,
            format='full',
        ),
        'long_date_ymd': lambda val, locale: babel_format_date(
            val,
            locale=locale_code,
            format='long',
        ),
        'date_ymd': lambda val, locale: babel_format_date(
            val,
            locale=locale_code,
            format='short',
        ),
        'medium_date_ymd': lambda val, locale: babel_format_date(
            val,
            locale=locale_code,
            format='medium',
        ),
        'time_hm': lambda val, locale: babel_format_time(
            val,
            locale=locale_code,
            format='short',
        ),
        'time_hms': lambda val, locale: babel_format_time(
            val,
            locale=locale_code,
            format='medium',
        ),
    }
    if format_name not in supported_formats:
        return str(value)

    formatted_value = supported_formats[format_name](value, locale_code)

    return ugly_fix_pl_months_names(
        formatted_value,
        locale_code,
    )


def combine(date, time24):
    ret = datetime.datetime.combine(date, time24)
    if getattr(time24, '_24_hour', False):
        ret += datetime.timedelta(days=1)
    return ret
