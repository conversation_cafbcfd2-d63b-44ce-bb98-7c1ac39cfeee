import elasticsearch_dsl as dsl

from lib.searchables.searchables import FunctionScore, Searchable, V


class TestFunctionScore:
    def test_with_query(self):
        class SampleSearchable(Searchable):
            text = dsl.query.Match(name=V('text'))
            extra_score = FunctionScore(
                query=dsl.query.Term(param_1=V('param_1')),
                functions=[
                    dsl.function.RandomScore(),
                ],
            )

        query_dict_1 = SampleSearchable().build_query_dict(
            {
                'text': 'foo',
                'param_1': 'bar',
            }
        )
        assert query_dict_1 == {
            'query': {
                'bool': {
                    'filter': [{'match': {'name': 'foo'}}],
                    'must': [
                        {
                            'function_score': {
                                'query': {'term': {'param_1': 'bar'}},
                                'functions': [{'random_score': {}}],
                            },
                        },
                    ],
                }
            }
        }

        query_dict_2 = SampleSearchable().build_query_dict(
            {
                'text': 'foo',
            }
        )
        assert query_dict_2 == {
            'query': {
                'bool': {
                    'filter': [{'match': {'name': 'foo'}}],
                }
            }
        }

    def test_without_query(self):
        class SampleSearchable(Searchable):
            text = dsl.query.Match(name=V('text'))
            scoring = FunctionScore(
                functions=[
                    dsl.function.RandomScore(),
                ],
            )

        query_dict = SampleSearchable().build_query_dict(
            {
                'text': 'foo',
            }
        )
        assert query_dict == {
            'query': {
                'function_score': {
                    'query': {
                        'bool': {
                            'filter': [{'match': {'name': 'foo'}}],
                        }
                    },
                    'functions': [{'random_score': {}}],
                }
            }
        }

    def test_append_field(self):
        class SampleSearchable(Searchable):
            scoring = FunctionScore(
                functions=[
                    dsl.function.RandomScore(),
                ],
            )
            text = dsl.query.Match(name=V('text'))

        query_dict = SampleSearchable().build_query_dict(
            {
                'text': 'foo',
            }
        )
        assert query_dict == {
            'query': {
                'function_score': {
                    'query': {
                        'bool': {
                            'filter': [{'match': {'name': 'foo'}}],
                        }
                    },
                    'functions': [{'random_score': {}}],
                }
            }
        }

    def test_functions(self):
        class SampleSearchable(Searchable):
            text = dsl.query.Match(name=V('text'))
            scoring = FunctionScore(
                functions=[
                    dsl.function.Linear(
                        param_1={
                            'origin': V('origin_1'),
                        },
                    ),
                    dsl.function.Linear(
                        param_2={
                            'origin': V('origin_2'),
                        }
                    ),
                ],
            )

        query_dict_1 = SampleSearchable().build_query_dict(
            {
                'text': 'foo',
                'origin_1': 100,
                'origin_2': 200,
            }
        )
        assert query_dict_1 == {
            'query': {
                'function_score': {
                    'query': {
                        'bool': {
                            'filter': [{'match': {'name': 'foo'}}],
                        }
                    },
                    'functions': [
                        {'linear': {'param_1': {'origin': 100}}},
                        {'linear': {'param_2': {'origin': 200}}},
                    ],
                }
            }
        }

        query_dict_2 = SampleSearchable().build_query_dict(
            {
                'text': 'foo',
                'origin_2': 200,
            }
        )
        assert query_dict_2 == {
            'query': {
                'function_score': {
                    'query': {
                        'bool': {
                            'filter': [{'match': {'name': 'foo'}}],
                        }
                    },
                    'functions': [
                        {'linear': {'param_2': {'origin': 200}}},
                    ],
                }
            }
        }

        query_dict_3 = SampleSearchable().build_query_dict(
            {
                'text': 'foo',
            }
        )
        assert query_dict_3 == {
            'query': {
                'function_score': {
                    'query': {
                        'bool': {
                            'filter': [{'match': {'name': 'foo'}}],
                        }
                    },
                }
            }
        }
