# coding=utf-8
import unicodedata as ud

from unidecode import unidecode

# $, € wouldn't be transliterated #41514
latin_letters = {'$': '$', '€': '€'}


def is_latin(uchr):
    try:
        return latin_letters[uchr]
    except KeyError:
        return latin_letters.setdefault(uchr, 'LATIN' in ud.name(uchr))


def transliterate_latin(unistr):
    result = []
    for uchr in unistr:
        if uchr.isalpha() and is_latin(uchr):
            result.append(unidecode(uchr))
        else:
            result.append(uchr)
    return u''.join(result)
