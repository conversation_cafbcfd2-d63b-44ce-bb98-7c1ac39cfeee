import builtins

from facebook_business.adobjects.serverside.gender import Gender as FBGender

from webapps.user.const import Gender as BooksyG<PERSON>


def convert_booksy_gender_to_facebook_gender(booksy_gender: BooksyGender | str | None):
    if isinstance(booksy_gender, str):
        try:
            booksy_gender = BooksyGender(booksy_gender)
        except ValueError as e:
            raise builtins.RuntimeError(
                f"Failed to convert Booksy Gender to Facebook Gender: "
                f"approached to convert to {BooksyGender} from invalid value {booksy_gender}."
            ) from e
    elif not isinstance(booksy_gender, (BooksyGender, type(None))):
        raise builtins.RuntimeError(
            f"Failed to convert Booksy Gender to Facebook Gender: "
            f"object of invalid type {type(booksy_gender)} has been provided."
        )

    match booksy_gender:
        case BooksyGender.Male:
            return FBGender.MALE
        case BooksyGender.Female:
            return FBGender.FEMALE
        case _:
            return
