from facebook_business.adobjects.serverside.custom_data import CustomData
from facebook_business.adobjects.serverside.user_data import UserData

from lib.facebook.client import FacebookConversionAPIClient
from lib.facebook.enums import EventName
from lib.tools import id_to_external_api
from webapps.business.models import Business
import settings


class FacebookEventService:
    """
    Service class for sending basic conversion events to Facebook Conversion API.
    """

    @staticmethod
    def get_user_data(business_id: int) -> UserData:
        """
        Retrieves user data for the specified business.

        Args:
            business_id (int): The ID of the business.

        Returns:
            UserData: User data object.
        """
        business = Business.objects.get(id=business_id)
        return UserData(
            external_id=id_to_external_api(business.owner_id),
            email=business.owner.email,
            phone=business.owner.cell_phone,
            first_name=business.owner.first_name,
            last_name=business.owner.last_name,
            city=business.city_or_region_city,
            state=business.state,
            zip_code=business.zip,
            country_code=settings.API_COUNTRY,
        )

    @classmethod
    def send_basic_event_for_business(
        cls, business_id: int, event_name: EventName, event_time: int
    ):
        """
        Sends a basic conversion event to the Facebook Conversion API for the specified business.

        Args:
            business_id (int): The ID of the business.
            event_name (FBEventName): The name of the conversion event.
            event_time (int): The time of the event in UNIX timestamp format.

        Returns:
            dict: Response from the Facebook Conversion API.

        """
        api_client = FacebookConversionAPIClient(pixel_id=settings.FB_CONVERSION_API_DATASET_ID)

        return api_client.send_event(
            event_name=event_name,
            event_time=event_time,
            user_data=cls.get_user_data(business_id=business_id),
        )

    @classmethod
    def send_event_with_custom_data(
        cls,
        data: dict,
        event_name: EventName,
        event_time: int,
        *,
        user_data: dict | None = None,
    ):
        if user_data is None:
            user_data = UserData(
                email=data.get('email', None),
                external_id=data.get('user_id', None),
            )
        else:
            user_data = UserData(
                **user_data,
            )
        custom_data = CustomData(custom_properties=data)
        api_client = FacebookConversionAPIClient(pixel_id=settings.FB_CONVERSION_API_DATASET_ID)
        return api_client.send_event(
            user_data=user_data,
            event_name=event_name,
            event_time=event_time,
            custom_data=custom_data,
        )
