import pytest
import responses
from model_bakery import baker
from rest_framework import status

from lib.search_service.client import (
    Endpoint,
    get_category_and_treatment_suggestions,
    get_url,
    make_request,
)
from lib.test_utils import get_in_memory_img
from webapps.business.baker_recipes import category_recipe
from webapps.business.models.category import get_business_category_image_url
from webapps.images.enums import BusinessCategoryPlaceholderType
from webapps.images.models import BusinessPlaceholderImage

_SUGGESTIONS_URL = 'http://search_service:8080/v1/suggestions'


def test_get_url():
    assert get_url(endpoint=Endpoint.SUGGESTIONS) == _SUGGESTIONS_URL


@responses.activate
def test_make_request():
    responses.add(responses.GET, _SUGGESTIONS_URL, json=dict())
    response = make_request(endpoint=Endpoint.SUGGESTIONS)
    assert response.status_code == status.HTTP_200_OK


@responses.activate
@pytest.mark.django_db
def test_get_category_and_treatment_suggestions():
    category = category_recipe.make()
    baker.make(
        BusinessPlaceholderImage,
        type=BusinessCategoryPlaceholderType.ICON_V2,
        category=category,
        image=get_in_memory_img(),
    )

    responses.add(
        responses.GET,
        _SUGGESTIONS_URL,
        json={
            'suggestions': [
                {
                    'object_type': 'category',
                    'label': category.full_name,
                    'query_parameters': {
                        'id': category.id,
                        'name': category.name,
                        'slug': category.slug,
                        'type': category.type,
                    },
                }
            ]
        },
    )

    response = get_category_and_treatment_suggestions(query='foo')

    assert response == {
        'suggestions': [
            {
                'object_type': 'category',
                'label': category.full_name,
                'query_parameters': {
                    'id': category.id,
                    'name': category.name,
                    'slug': category.slug,
                    'type': category.type,
                    'icon_v2': get_business_category_image_url(
                        category.internal_name,
                        BusinessCategoryPlaceholderType.ICON_V2,
                    ),
                },
            }
        ]
    }
