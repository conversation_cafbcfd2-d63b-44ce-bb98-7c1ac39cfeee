from urllib.parse import urljoin

from django.conf import settings
from elasticsearch_dsl import (
    <PERSON><PERSON><PERSON>,
    Field,
    Integer,
    Keyword,
    Nested,
    Text,
)

from lib.elasticsearch import analyzers as a
from settings import elasticsearch as es_settings

WITH_POSITION_OFFSETS = 'with_positions_offsets'


class IcuKeyword(Keyword):
    name = 'icu_collation_keyword'


class UnparsedDate(Field):
    name = 'date'
    _coerce = False


class BusinessPhotoUrl(Text):
    _coerce = True

    def deserialize(self, data):
        if not data:
            return None
        return urljoin(settings.MEDIA_URL, data.lstrip('/'))


# pylint: disable=invalid-name
text_deprecated = Text(
    index=True,
    analyzer=a.ngram_starts_analyzer_deprecated,
    store=False,
    term_vector=WITH_POSITION_OFFSETS,
)

text_nosplit = Text(
    index=True,
    analyzer=a.ngram_starts_analyzer_no_split,
    store=False,
    term_vector=WITH_POSITION_OFFSETS,
)

text_standard = Text(
    index=True,
    analyzer=a.standard,
    store=False,
    term_vector=WITH_POSITION_OFFSETS,
)

text_alfanum = Text(
    index=True,
    analyzer=a.alfanum,
    store=False,
)

text_keyword_region = Text(
    index=True,
    analyzer=a.keyword_region,
    store=False,
    term_vector=WITH_POSITION_OFFSETS,
)

text_completion = Text(
    index=True,
    analyzer=a.ngram_starts_analyzer_lowercase,
    store=False,
    term_vector=WITH_POSITION_OFFSETS,
)
text_stop = Text(
    index=True,
    analyzer=a.stop,
    store=False,
    term_vector=WITH_POSITION_OFFSETS,
)

text_sort = IcuKeyword(
    index=True,
    store=False,
)

text_raw = Text(
    index=False,
    store=False,
    # 'ignore_above': 32000,
)

text_synonym = Text(
    index=True,
    analyzer=a.synonym,
    store=False,
)

_multi_fields = {
    'completion': text_completion,
    'stop': text_stop,
    'untouched': text_raw,
    'nosplit': text_nosplit,
    'standard': text_standard,
    'sort': text_sort,
    'synonym': text_synonym,
}

_multi_fields.update(
    {
        key: Text(
            index=True,
            store=False,
            term_vector=WITH_POSITION_OFFSETS,
            **val,
        )
        for key, val in es_settings.ES_TEXT_MULTI_FIELDS.items()  # pylint: disable=no-member
    }
)

text_multi = Text(fields=_multi_fields)

text_simple_multi = Text(
    fields={
        'keyword': Keyword(),
        'completion': text_completion,
    },
)

_region_multi_fields = _multi_fields.copy()
_region_multi_fields['keyword_region'] = text_keyword_region

region_multi = Text(fields=_region_multi_fields)

boolean = Boolean(index=True, store=False)

datetime_field = UnparsedDate(
    index=True,
    store=False,
    format="yyyy-MM-dd'T'HH:mm:ss",
)

date_field = UnparsedDate(index=True, store=False, format='yyyy-MM-dd')
time_field = UnparsedDate(index=True, store=False, format='HH:mm:ss')

datetime_range = Nested(
    properties={
        'time_from': datetime_field,
        'time_to': datetime_field,
    }
    # 'store': 'no',
    # 'index': 'not_analyzed',
)

time_range = Nested(
    properties={
        'time_from': Integer(index=True),
        'time_to': Integer(index=True),
    }
)

date_time_range = Nested(
    properties={
        'date': date_field,
        'time_from': time_field,
        'time_to': time_field,
    }
)
