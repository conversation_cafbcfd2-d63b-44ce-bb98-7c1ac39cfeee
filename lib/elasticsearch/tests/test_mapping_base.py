import unittest


class MappingTestCaseBase(unittest.TestCase):
    @classmethod
    def index_setup(cls, index):
        # replay config from Elastic
        for Document in index.documents.values():  # pylint: disable=invalid-name
            Document._index = index  # pylint: disable=protected-access

        index.delete(ignore=404, force=True)  # DocIndex created in test can be safely deleted
        index.save()
        index.open()
