import copy
import datetime
import functools
from decimal import Decimal

import elasticsearch
import numpy as np
import simplejson as json
from django.conf import settings
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.utils.encoding import force_bytes, force_str
from django.utils.functional import Promise
from elasticsearch.exceptions import (
    ConflictError,
    ConnectionError as ElasticConnectionError,
    ConnectionTimeout,
    TransportError,
)
from elasticsearch_dsl import AttrDict
from elasticsearch_dsl.utils import AttrList
from requests import Request, Session
from requests.adapters import HTTPAdapter
from urllib3.util import Retry

from simplejson import JSONEncoder

from lib.elasticsearch.consts import ESDocType
from lib.feature_flag.killswitch import DisableForceRefreshInToolsFlag
from lib.rivers import River, bump_document
from lib.tools import tznow
from webapps.business.service_price import ServicePrice


class ESRuntimeError(Exception):
    pass


def simple_settings_switch(func):
    """
    Decorator for to stiwtch API_COUNTRY settings.
    Analog to override_settings but will work with class method because
    accept new settings.API_COUNTRY in argument of decorated function
    as a key-word argument under api_country key.
    :param func: decorated function
    :return: result of function call
    """

    @functools.wraps(func)
    def inner(*args, **kwargs):
        # get api_country as argument to func
        new_settings = kwargs.get('api_country')
        # get last used argument
        old_api_setting = settings.API_COUNTRY

        if new_settings and new_settings != old_api_setting:
            settings.API_COUNTRY = new_settings
            try:
                result = func(*args, **kwargs)
            except Exception as e:
                settings.API_COUNTRY = old_api_setting
                raise e
            settings.API_COUNTRY = old_api_setting
            return result
        # nothing to do
        return func(*args, **kwargs)

    return inner


def get_sorting_from_query(query, meta):
    """_meta in search results returns {'sort': [sort1, sort2]}. Given query
    we return sort as:
    {'sort': {'sort1_name': sort1, 'sort2_name': sort2}}
    This way we know which sort value came from which sort name.
    """
    if meta.sort is None:
        return {}

    ret = {}
    for i, sqr in enumerate(query.get('sort', [])):
        sname = list(sqr.keys())[0]
        ret[sname] = meta.sort[i]

    return ret


def get_by_id(iid, document_type: ESDocType, **kwargs):
    """Get document from ES by its id."""
    from webapps.elasticsearch.elastic import ELASTIC

    document = ELASTIC.documents.get(document_type)
    if not document:
        raise ESRuntimeError('Index with given document type does not exists')

    if document_type == 'business' and 'routing' not in kwargs:
        kwargs['routing'] = iid

    try:
        return document.get(iid, **kwargs)
    except TransportError:
        return None


def es_delete_by_id(document_type: ESDocType, doc_id):
    """Safely delete document."""
    from webapps.elasticsearch.elastic import ELASTIC

    Document = ELASTIC.documents[document_type]  # pylint: disable=invalid-name
    Document(meta={'id': Document.get_es_id(doc_id)}).delete()


def date_to_int(date):
    if not date:
        return None
    return date.month * 100 + date.day


def _deep_to_dict_value(value):
    if isinstance(value, dict):
        return deep_to_dict(value)
    if isinstance(value, (list, tuple)):
        return [deep_to_dict(elem) for elem in value]
    if hasattr(value, 'to_dict'):
        return value.to_dict()
    return value


def deep_to_dict(doc, callback=None):
    """
    Evaluate deeply nested values

    i.e. in dslObj(param=dict(value=V('value')))
    Simple .to_dict() will not do
    """
    if isinstance(doc, dict):
        if callback:
            callback(doc)
        doc.pop('_required_value', None)
        return {key: _deep_to_dict_value(val) for key, val in doc.items()}
    return doc


class ESJSONEncoder(JSONEncoder):
    def default(self, o):  # pylint: disable=too-many-return-statements, method-hidden
        if isinstance(o, AttrList):
            return list(o)
        if isinstance(o, AttrDict):
            return o.to_dict()
        if isinstance(o, datetime.datetime):
            return o.strftime(settings.ES_DATETIME_FORMAT)
        if isinstance(o, datetime.date):
            return o.strftime(settings.ES_DATE_FORMAT)
        if isinstance(o, Promise):
            return force_str(o)
        if isinstance(o, Decimal):
            return float(o)
        if isinstance(o, ServicePrice):
            return float(o.value)
        if isinstance(o, np.bool_):
            return bool(o)
        return JSONEncoder.default(self, o)


ES_JSONField = functools.partial(JSONField, encoder=ESJSONEncoder)


def index_document(
    document_type: ESDocType, ids, refresh_index: bool = False, _origin=None, **kwargs
):
    from webapps.elasticsearch.elastic import ELASTIC

    document = ELASTIC.documents[document_type]
    success, __errors = document.reindex(
        ids, use_celery=False, verbose=False, refresh_index=refresh_index, **kwargs
    )
    origin = ':'.join([_f for _f in [document_type, _origin] if _f])
    return origin, success


def delete_document(document_type: ESDocType, ids, _origin=None, refresh_index: bool = True):
    from lib.searchables.common import IdsSearchable

    refresh_index = refresh_index if not DisableForceRefreshInToolsFlag() else False
    res = (
        IdsSearchable(document_type)
        .params(
            request_timeout=settings.ELASTIC_TIMEOUT * len(ids),
            refresh=refresh_index,
        )
        .search({'ids': ids})
        .delete()
    )
    origin = ':'.join([_f for _f in [document_type, _origin] if _f])
    return origin, res


def update_document(
    document_type: ESDocType, ids, updates, refresh_index: bool = True, _origin=None, **kwargs
):
    from webapps.elasticsearch.elastic import ELASTIC

    refresh_index = refresh_index if not DisableForceRefreshInToolsFlag() else False
    document = ELASTIC.documents[document_type]
    success, __errors = document.bulk_update(
        ids, updates, refresh_index=refresh_index, use_celery=False, verbose=False, **kwargs
    )
    origin = ':'.join([_f for _f in [document_type, _origin] if _f])
    return origin, success


def delete_documents_with_retries(es_doc_type, ids):
    # pylint: disable=cyclic-import
    from lib.elasticsearch.tasks import delete_document_with_backoff_task

    try:
        delete_document(es_doc_type, ids)
    except (TransportError, ElasticConnectionError, ConnectionTimeout, ConflictError):
        delete_document_with_backoff_task.delay(
            str(es_doc_type),
            ids,
        )


class ESDocMixin:
    """This class requires a subclass to implement two attributes:

    es_doc_type: ESDocType
    extra_es_doc_types: List[ESDocType] (optional)

    """

    es_doc_type: ESDocType = None
    extra_es_doc_types = []
    river: River = None

    def reindex(self, doc_types=None, refresh_index=False, verbose=False):
        allowed_doc_types = [self.es_doc_type] + self.extra_es_doc_types
        doc_types = allowed_doc_types if doc_types is None else doc_types
        if any(doc_type not in allowed_doc_types for doc_type in doc_types):
            raise ESRuntimeError(f'invalid doc_types: {doc_types}')

        from webapps.elasticsearch.elastic import ELASTIC

        res = []
        for doc_type in doc_types:
            res.append(
                (
                    doc_type,
                    ELASTIC.documents[doc_type].reindex(
                        [self.id],
                        refresh_index=refresh_index,
                        verbose=verbose,
                    ),
                )
            )
        return res

    def reindex_or_bump_river(self, doc_types=None):
        """Calling reindex() directly may raise elasticsearch.ConnectionTimeout
        this function will fall back to bumping such document via River.
        """
        if not self.river:
            raise ValueError(f'{self.__class__.__name__} does not define River')
        try:
            self.reindex(doc_types)
        except elasticsearch.ConnectionTimeout:
            bump_document(self.river, self.id)

    def get_document(self, refresh=False, routing=None):
        if routing is None:
            routing = getattr(self, 'business_id', None)

        doc = None
        doc_type = self.es_doc_type
        if not refresh:
            doc = get_by_id(self.id, doc_type, routing=routing)
        if doc is None:
            from webapps.elasticsearch.elastic import ELASTIC

            Document = ELASTIC.documents[doc_type]  # pylint: disable=invalid-name
            instance = self
            queryset = Document.get_queryset()
            # WARNING! Never do bool(queryset) - it will fetch all!
            if queryset is not None:
                instance = queryset.filter(id=self.id).first()
            doc = Document(instance)

        return doc


class ESDefaultBumpOnSaveDeleteMixin:
    """
    Mixin should be used in django model, and should be before models.Model.
    Mixin which inject in save and delete method, default update to ES.
    All objects should be indexed in ES, but not always developer remember to
    schedule it. Often code which update models to ES is repeated.
    Purpose of this mixin is to remove boilerplate in model usages and mitigate
    problem of not indexed models due to oversight.
    """

    def save(self, *args, **kwargs):
        """
        Keywords controlling behaviour:
            bump_intance_to_es -> pass False to disable auto update to ES, user
                                  want to do it later on his own,
            bump_later -> pass True to use only bump_river, don't try to reindex
                          document synchronously
        """
        bump_instances_to_es = kwargs.pop('bump_instances_to_es', True)
        bump_later = kwargs.pop('bump_later', False)

        super().save(*args, **kwargs)

        if bump_instances_to_es:
            if bump_later:
                bump_document(self.river, [self.id])
            else:
                self.reindex_or_bump_river()

    def soft_delete(self):
        row_id = self.id
        self.deleted = tznow()
        self.active = False
        self.save(bump_instances_to_es=False)
        delete_documents_with_retries(self.es_doc_type, [row_id])

    def delete(self, using=None, keep_parents=False):
        row_id = self.id
        delete_return = super().delete(using=using, keep_parents=keep_parents)
        delete_documents_with_retries(self.es_doc_type, [row_id])
        return delete_return


class ESDefaultBumpOnCreateUpdateDeleteQuerysetMixin:
    """
    Mixin should be used in queryset evaluated by manager and should be before
    models.QuerySet.
            WARNING! Model using queryset with this mixin need to inherit from
                     ESDefaultBumpOnSaveMixin.
    Mixin which inject in create, update and delete methods,
    default update to ES.
    All objects should be indexed in ES, but not always developer remember to
    schedule it. Often code which update models to ES is repeated.
    Purpose of this mixin is to remove boilerplate in model usages and mitigate
    problem of not indexed model due to oversight.
    """

    def create(self, **kwargs):
        """
        Create a new object with the given kwargs, saving it to the database
        and returning the created object.

        Keywords controlling behaviour:
            bump_intance_to_es -> pass False to disable auto update to ES, user
                                  want to do it later on his own,
            bump_later -> pass True to use only bump_river, don't try to reindex
                          document synchronously
        """
        bump_instances_to_es = kwargs.pop('bump_instances_to_es', True)
        bump_later = kwargs.pop('bump_later', False)
        obj = self.model(**kwargs)
        self._for_write = True
        obj.save(
            force_insert=True,
            using=self.db,
            bump_instances_to_es=bump_instances_to_es,
            bump_later=bump_later,
        )
        return obj

    def update(self, **kwargs):
        """
        Keywords controlling behaviour:
            bump_intance_to_es -> pass False to disable auto update to ES, user
                                  want to do it later on his own,
            bump_later -> pass True to use only bump_river, don't try to reindex
                          document synchronously
        """
        failed_ids = []
        bump_instances_to_es = kwargs.pop('bump_instances_to_es', True)
        bump_later = kwargs.pop('bump_later', False)
        if bump_instances_to_es:
            ids_to_update = list(self.values_list('id', flat=True))
            if not ids_to_update:
                return 0  # No ids to update, return number of updated rows

        obj = super().update(**kwargs)

        if bump_instances_to_es and ids_to_update:
            if bump_later:
                bump_document(self.model.river, ids_to_update)
            else:
                for updated_id in ids_to_update:
                    try:
                        index_document(self.model.es_doc_type, [updated_id])
                    except elasticsearch.ConnectionTimeout:
                        failed_ids.append(updated_id)
                if failed_ids:
                    bump_document(self.model.river, failed_ids)

        return obj

    def soft_delete(self):
        rows_ids_to_delete = list(self.values_list('id', flat=True))
        if rows_ids_to_delete:
            deleted_rows = self.update(deleted=tznow(), bump_instances_to_es=False)
            delete_documents_with_retries(self.model.es_doc_type, rows_ids_to_delete)
            return deleted_rows, {}  # For backward compatibility
        return 0, {}

    def delete(self):
        rows_ids_to_delete = list(self.values_list('id', flat=True))
        if rows_ids_to_delete:
            delete_return = super().delete()
            delete_documents_with_retries(
                self.model.es_doc_type,
                rows_ids_to_delete,
            )
            return delete_return
        return 0, {}


def set_attribute(instance, attrs, value):
    """Create parent attribute if needed"""
    attrs_path, attr = attrs[:-1], attrs[-1]
    if attrs_path:
        for k in attrs_path:
            try:
                instance = getattr(instance, k)
            except AttributeError:
                setattr(instance, k, AttrDict({}))
                instance = getattr(instance, k)
    setattr(instance, attr, value)


class StoredScriptsAPI:
    """Simplifed api version to create stored functions in elastics search
    docs:
    https://www.elastic.co/guide/en/elasticsearch/reference/current/modules-scripting-using.html#_request_examples
    """

    def __init__(self, timeout: float | None = None):
        if timeout is None:
            timeout = settings.ELASTIC_TIMEOUT
        self.session = self._prepare_session(timeout)
        if isinstance(settings.ELASTIC_HOSTS, list):
            self.host = settings.ELASTIC_HOSTS[0]
        else:
            self.host = settings.ELASTIC_HOSTS

    @staticmethod
    def _prepare_session(timeout: float):
        retries = Retry(
            total=3,
            backoff_factor=0.1,
        )
        adapter = HTTPAdapter(max_retries=retries)
        session = TimeoutSession(timeout=timeout)
        session.mount('', adapter)
        return session

    def _prepare_request(self, method, name, data=None):
        request_data = {
            'method': method,
            'data': data,
            'headers': {'content-type': 'application/json'},
        }
        protocol = 'http'
        request_data['url'] = f'{protocol}://{self.host}/_scripts/{name}'
        return Request(**request_data)

    def create_functions(self, scripts):
        """
        Create stored functions in elasticsearch cluster
        :param scripts: dict.
            key name of function
            value: body_function
                {
                    "lang": "painless",
                    "source": "Math.log(_score * 2) + params.my_modifier"
                }
        :return: None
        """
        ch_to_remove = (
            '\n',
            '\t',
        )
        for name, script in list(scripts.items()):
            # delete space and
            compressed = copy.deepcopy(script)
            # TO DO possible regex
            for char in ch_to_remove:
                compressed['script']['source'] = compressed['script']['source'].replace(char, '')
            request = self._prepare_request('POST', name, force_bytes(json.dumps(compressed)))
            self.session.send(request.prepare())

    def delete_function(self, name):
        request = self._prepare_request('DELETE', name)
        response = self.session.send(request.prepare())
        print(f'Script name {name}. Deleted {response.status_code == 200}')
        print(f'Response STATUS CODE {response.status_code}')

    def get_function(self, name):
        request = self._prepare_request('GET', name)
        response = self.session.send(request.prepare())
        success = response.status_code == 200
        print(f'Script name {name}. Function exists {success} ')
        body = response.json()
        print(f'Response {body}')
        if success:
            print('')
            print(f'Lang Fun __\'{body["script"]["lang"]}\'__')
            print(f'Function Body \'{body["script"]["source"]}\'')
        print('')


class TimeoutSession(Session):
    def __init__(self, timeout=None):
        super().__init__()
        self.timeout = timeout

    def send(self, *args, **kwargs):
        kwargs.setdefault('timeout', self.timeout)
        return super().send(*args, **kwargs)
