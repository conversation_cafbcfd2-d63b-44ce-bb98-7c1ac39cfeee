from lib.firebase import FireBaseModelBase


# pylint: disable=abstract-method
class ChurnedMerchantBusinessesProcessed(FireBaseModelBase):
    OBJ_NAME = 'churned_merchant_businesses_processed'
    COLLECTION_NAME = 'notification'

    def save(self, businesses_id):  # pylint: disable=arguments-differ
        self.reference(
            'notifications',
            self.OBJ_NAME,
            'businesses',
            self.country,
        ).update({b_id: True for b_id in businesses_id})

    def get(self, only_business_ids=False):  # pylint: disable=arguments-differ
        ret = self.reference(
            'notifications',
            self.OBJ_NAME,
            'businesses',
            self.country,
        ).get()
        if not only_business_ids:
            return ret or {}
        if not ret:
            return []
        return [int(x) for x in ret.keys()]

    def delete(self, business_id):  # pylint: disable=arguments-differ
        self.reference(
            'notifications', self.OBJ_NAME, 'businesses', self.country, str(business_id)
        ).delete()


# pylint: disable=abstract-method
class ChurnedMerchantUserReactivation(FireBaseModelBase):
    """FireBase model churned merchant user reactivation notification"""

    OBJ_NAME = 'churned_merchant_user_reactivation'
    COLLECTION_NAME = 'notification'

    @classmethod
    def parse(cls, business_id, users_ids, proposed_businesses_ids):
        return {
            business_id: {
                'users': {u_id: True for u_id in users_ids},
                'proposed_businesses_ids': {u_id: True for u_id in proposed_businesses_ids},
            },
        }

    def save(
        self, business_id, users_ids, proposed_businesses_ids
    ):  # pylint: disable=arguments-differ
        self.reference(
            'notifications',
            'churned_merchant_user_reactivation',
            'businesses',
            self.country,
        ).update(self.parse(business_id, users_ids, proposed_businesses_ids))

    def get(self, business_id=None, only_business_ids=False):  # pylint: disable=arguments-differ
        if business_id:
            only_business_ids = False
        ret = self.reference(
            'notifications',
            'churned_merchant_user_reactivation',
            'businesses',
            self.country,
            '{}'.format(business_id) if business_id else '',
        ).get()

        if not only_business_ids:
            return ret or {}
        if not ret:
            return []
        return [int(x) for x in ret.keys()]


# pylint: disable=abstract-method
class HighVolumeUsers(FireBaseModelBase):
    OBJ_NAME = 'high_volume_users'
    COLLECTION_NAME = 'notification'

    def save(self, users_ids):  # pylint: disable=arguments-differ
        self.reference(
            'notifications',
            self.OBJ_NAME,
            'users',
            self.country,
        ).update({u_id: True for u_id in users_ids})

    def get(self, only_ids=False):  # pylint: disable=arguments-differ
        ret = self.reference(
            'notifications',
            self.OBJ_NAME,
            'users',
            self.country,
        ).get(
            shallow=True
        )  # experimental shallow parameter
        if not only_ids:
            return ret or {}
        if not ret:
            return []
        return [int(x) for x in ret.keys()]


# pylint: disable=abstract-method
class FollowUpUsers(FireBaseModelBase):
    OBJ_NAME = 'follow_up_users'
    COLLECTION_NAME = 'notification'

    def save(self, users_ids):  # pylint: disable=arguments-differ
        self.reference(
            'notifications',
            self.OBJ_NAME,
            'users',
            self.country,
        ).update({u_id: True for u_id in users_ids})

    def get(self, only_ids=False):  # pylint: disable=arguments-differ
        ret = self.reference(
            'notifications',
            self.OBJ_NAME,
            'users',
            self.country,
        ).get()
        if not only_ids:
            return ret or {}
        if not ret:
            return []
        return [int(x) for x in ret.keys()]
