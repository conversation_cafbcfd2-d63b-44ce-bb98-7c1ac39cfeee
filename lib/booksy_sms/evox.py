from uuid import uuid4

import phonenumbers
import requests
from bo_obs.datadog.enums import DatadogOperationNames
from django.conf import settings

from country_config import Country
from lib.booksy_sms.enums import EvoxRouteType, SMSServiceStatus, SmsGroup
from lib.booksy_sms.loggers.utils import set_current_sms_service_as_dd_serivce_name
from lib.booksy_sms.result import SMSServiceResult
from lib.tools import id_to_external_api
from webapps.notification.enums import NotificationService

TIMEOUT = 30.0


class EvoxSMSServiceResult(SMSServiceResult):
    service = NotificationService.EVOX


class EvoxDriver:
    def __init__(self, api_login, api_password, api_url, sender_name, route_type):
        self.api_login = api_login
        self.api_password = api_password
        self.api_url = api_url
        self.sender_name = sender_name
        self.route_type = route_type

    def send_sms(
        self,
        recipient,
        content,
        priority_queue,
        flash=False,
        external_id=None,
        custom_data1=None,
        custom_data2=None,
        custom_data3=None,
        message_type=None,
        webhook_id=None,
    ) -> EvoxSMSServiceResult:
        """

        :param recipient: The end-customer MSISDN with countrycode
        :param content: The sms content to be sent.
        :param priority_queue: Flag true/false if the message should be
                               enqueued in a priority queue
        :param flash: Flag true/false if the message should be in flash mode.
        :param external_id: Some service-generated string identifier of the
                            message, that will be available in notifications
                            and from get_sms_info
        :param custom_data1: Custom string data provided by service
                                      to be shown in reports and stats panel
        :param custom_data2: Custom string data provided by service
                                      to be shown in reports and stats panel
        :param custom_data3: Custom string data provided by service
                                      to be shown in reports and stats panel
        :param message_type: Custom string used for external reports, !IMPORTANT! for France it is
                                      also used to determine proper opt-out message
        :param webhook_id: Associated NotificationHistoryDocument's metadata webhook_id

        :return: EvoxSMSServiceResult
        """
        json_params = {
            'content': content,
            'to': recipient.replace('+', ''),
            'external_id': id_to_external_api(external_id or uuid4().hex),
            'custom_data1': custom_data1,
            'custom_data2': custom_data2,
            'custom_data3': custom_data3,
        }
        # For or details check: https://redmine.booksy.pm/issues/66781
        if self.route_type is None:
            json_params.update(
                {
                    'from': self.sender_name,
                    'flash': flash,
                    'priority_queue': priority_queue,
                }
            )

        route_type = self.route_type
        if message_type is not None:
            json_params['message_type'] = message_type
            if new_route_type := self.get_proper_route_type(json_params):
                route_type = new_route_type

        if route_type:
            json_params['route_type'] = route_type

        try:
            response = requests.post(
                url=self.api_url,
                json=json_params,
                auth=(self.api_login, self.api_password),
                timeout=TIMEOUT,
            )
        except requests.RequestException as e:
            return EvoxSMSServiceResult(
                phone=recipient,
                status=SMSServiceStatus.ERROR,
                webhook_id=webhook_id,
                error_type='http',
                error_details=repr(e),
            )
        if int(response.status_code) in (400, 401, 404, 405):
            return EvoxSMSServiceResult(
                phone=recipient,
                status=SMSServiceStatus.ERROR,
                webhook_id=webhook_id,
                error_type='evox',
                error_details=f'HTTP {response.status_code}',
                api_response=response.text,
            )
        try:
            response_json = response.json()
        except ValueError as e:
            return EvoxSMSServiceResult(
                phone=recipient,
                status=SMSServiceStatus.ERROR,
                webhook_id=webhook_id,
                error_type='evox',
                error_details=repr(e),
                api_response=response.text,
            )

        # Here we have some hacks. Name external_id in ServiceResults describes id of message
        # set by external_provider. Acctualy we set message_id here as external_id.

        # As this name is already in use and we need to have own identifier of this message, which
        # is country-based in metadat we save real 'external_id' as 'external_reference'.
        # So summarizing:
        # message_id (EVOX) -> external_id (Booksy)
        # external_id (EVOX) -> metadata.external_reference (Booksy)
        message_id = response_json.get('message_id')
        sms_parts = response_json.get('message_parts', 0)
        return EvoxSMSServiceResult(
            phone=recipient,
            status=SMSServiceStatus.SUCCESS,
            webhook_id=webhook_id,
            sms_parts=sms_parts,
            external_id=message_id,  # It external_id set by provider
            metadata=dict(
                # It's our identifier, which is used to route events on webhook.
                external_reference=json_params['external_id'],
            ),
        )

    def get_sms_info(self, message_id):
        """
        Gives information regarding specific message_id in evoxsms system

        :param message_id: The message_id returned after send_sms call
        :return: Dictionary with delivery_status field, especially with
                 DELIVERED and UNDELIVERED values.
        """
        try:
            response = requests.get(
                url="{}/{}".format(self.api_url, message_id),
                auth=(self.api_login, self.api_password),
                timeout=TIMEOUT,
            )
        except requests.RequestException as e:
            return {
                'to': None,
                'status': 'error',
                'sms_parts': None,
                'error_type': 'http',
                'error_details': repr(e),
                'api_response': repr(e),
            }
        if int(response.status_code) in (400, 401, 404, 405):
            return {
                'to': None,
                'status': 'error',
                'sms_parts': None,
                'error_type': 'evox',
                'error_details': response.text,
                'api_response': response.text,
            }
        try:
            response_json = response.json()
        except Exception as e:
            return {
                'to': None,
                'status': 'error',
                'sms_parts': None,
                'error_type': 'evox',
                'error_details': response.text,
                'api_response': response.text,
            }

        return {
            'to': response_json.get('to'),
            'status': 'success',
            'delivery_status': response_json.get('status'),
            'sms_parts': response_json.get('message_parts'),
            'error_type': response_json.get('error_code'),
            'error_details': response_json.get('error_description'),
            'api_response': response.text,
        }

    def cancel_sms(self, message_id):
        """
        Cancels specific message_id sending

        :param message_id: The message_id returned after send_sms call
        :return: Dictionary with delivery_status field, especially with
                 DELIVERED and UNDELIVERED values.
        """
        try:
            response = requests.delete(
                url="{}/{}".format(self.api_url, message_id),
                auth=(self.api_login, self.api_password),
                timeout=TIMEOUT,
            )
        except requests.RequestException as e:
            return {
                'status': 'error',
                'error_type': 'http',
                'error_details': repr(e),
                'api_response': repr(e),
            }

        if response.status_code.ok:  # message canceled
            return {
                'status': 'success',
                'api_response': response.text,
            }

        return {  # probably message not found or cannot be canceled
            'status': 'error',
            'error_type': 'evox',
            'error_details': response.text,
            'api_response': response.text,
        }

    def get_proper_route_type(self, json_params):
        message_type = json_params['message_type']
        if (
            settings.API_COUNTRY == Country.US
            and self.route_type is not None
            and message_type == SmsGroup.CUSTOMER_REGISTRATION.value
        ):
            return EvoxRouteType.ONE_TIME_PASSWORD
        phone_number = f'+{json_params["to"]}'
        numobj = phonenumbers.parse(phone_number)
        if phonenumbers.is_valid_number_for_region(numobj, Country.FR):
            if message_type == SmsGroup.MARKETING.value:
                return EvoxRouteType.MARKETING
            return EvoxRouteType.TRANSACTIONAL


def evox_send_sms(
    service_settings, phone_number, message, *_, fast=False, metadata=None, webhook_id=None, **__
) -> EvoxSMSServiceResult:
    set_current_sms_service_as_dd_serivce_name(DatadogOperationNames.SEND_SMS_EVOX)
    sms_driver = build_evox_driver(service_settings)

    metadata = metadata or {}
    task_parts = metadata.get('task_id', '::').split(':')[:2]
    scenario_name, task_name = task_parts + [''] * (2 - len(task_parts))
    message_type = metadata.get('message_type')
    return sms_driver.send_sms(
        recipient=phone_number.global_short,
        content=message,
        priority_queue=fast,
        flash=metadata.get('flash', False),
        external_id=metadata.get('external_id'),
        custom_data1=str(metadata.get('business_id', '')),
        custom_data2=''.join([scenario_name, task_name]),
        custom_data3=str(metadata.get('sender', '')),
        message_type=message_type,
        webhook_id=webhook_id,
    )


def build_evox_driver(service_settings):
    return EvoxDriver(
        api_login=service_settings.get('api_login'),
        api_password=service_settings.get('api_password'),
        api_url=service_settings.get('api_url'),
        sender_name=service_settings.get('from'),
        route_type=service_settings.get('route_type'),
    )
