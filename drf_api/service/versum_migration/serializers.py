from rest_framework import serializers


class VersumBillingDataSerializer(serializers.Serializer):
    saas_price = serializers.FloatField()
    staffer_price = serializers.FloatField()


class VersumDiscountedPeriodBillingDataSerializer(VersumBillingDataSerializer):
    period = serializers.IntegerField()


class VersumInitialPasswordSetSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    billing_data = VersumBillingDataSerializer(required=True)
    discounted_billing_data = VersumDiscountedPeriodBillingDataSerializer(required=False)
    paid_upfront_period = serializers.IntegerField(required=False)
