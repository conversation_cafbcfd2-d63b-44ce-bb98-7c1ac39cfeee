import jwt

from django.conf import settings
from django.test import override_settings
from django.urls import reverse

from drf_api.lib.iterable import generate_iterable_jwt_token
from drf_api.service.tests.base import AuthenticatedBusinessAPITestCase
from webapps.admin_extra.tests import DjangoTestCase
from webapps.user.baker_recipes import user_recipe


class TestIterableIntegration(DjangoTestCase):

    @override_settings(
        ITERABLE_SECRET_KEY='test_secret',
    )
    def test_generate_jwt_token(self):
        user = user_recipe.make()
        token = generate_iterable_jwt_token(user.email, settings.ITERABLE_JWT_SECRET_KEY)
        decoded_token = jwt.decode(token, settings.ITERABLE_JWT_SECRET_KEY, algorithms="HS256")
        self.assertEqual(decoded_token["email"], user.email)
        self.assertTrue(isinstance(decoded_token["iat"], int))
        self.assertTrue(isinstance(decoded_token["exp"], int))


class TestIterableIntegrationAPIView(AuthenticatedBusinessAPITestCase):

    @override_settings(
        ITERABLE_SECRET_KEY='test_secret',
    )
    def test_generate_jwt_token_api(self):
        response = self.client.get(reverse('iterable_jwt_token'), **self.headers)
        self.assertIn('token', response.data)
        token = response.data['token']
        decoded_token = jwt.decode(token, settings.ITERABLE_JWT_SECRET_KEY, algorithms="HS256")
        self.assertEqual(decoded_token["email"], self.user.email)
