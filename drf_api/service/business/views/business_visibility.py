from typing import List

from django.db import transaction
from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionAPIView
from drf_api.service.business.validators.access import get_business_view_validator
from service.business.serializers.business_visibility import (
    BusinessVisibilityQuerySerializer,
    BusinessVisibilitySerializer,
)
from service.mixins.validation import validate_serializer
from webapps.business.business_visibility.exceptions import BusinessVisibilityError
from webapps.business.business_visibility.validations import check_errors_business_visibility_change
from webapps.business.models import Business, Resource
from webapps.business.models.business_change import BusinessChange
from webapps.segment.tasks import analytics_business_info_updated_task


class MyBusinessVisibilityView(BaseBooksySessionAPIView):
    permission_classes = (IsAuthenticated,)
    query_serializer_class = BusinessVisibilityQuerySerializer

    def get(self, request, business_pk):
        """
        swagger:
          summary: Get current business visibility status.
          parameters:
            - name: business_id
              description: ID of the business.
              type: integer
              paramType: path
              required: True
            - name: value_to_check
              description: Visibility value for which we want to check errors.
              type: string
              paramType: query
          type: BusinessVisibilityStatusResponse
        :swagger
        """
        query_serializer = self.query_serializer_class(
            data=request.query_params.dict(),
        )
        validated_data = validate_serializer(query_serializer)
        business = get_object_or_404(Business, id=business_pk)

        errors = [
            err.asdict()
            for err in check_errors_business_visibility_change(
                business=business,
                new_visibility_value=validated_data['value_to_check'],
            )
        ]
        return Response(
            data={
                'errors': errors,
                'is_visible_in_marketplace': business.is_visible_in_marketplace,
                'hidden_in_search': business.hidden_in_search,
                'visible': business.visible,
            },
            status=status.HTTP_200_OK if not errors else status.HTTP_400_BAD_REQUEST,
        )

    def put(self, request, business_pk):
        """
        swagger:
          summary: Get current business visibility status.
          parameters:
            - name: business_id
              description: ID of the business.
              type: integer
              paramType: path
              required: True
            - name: visible
              type: boolean
              paramType: body
              required: False
            - name: hidden_in_search
              type: boolean
              paramType: body
              required: False
          type: BusinessVisibilityStatusUpdateResponse
        :swagger
        """
        validator = get_business_view_validator(
            business=business_pk,
            request=self.request,
            required_minimum_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
            user=self.request.user,
        )
        validator.validate()
        business = validator.fetcher.business
        serializer = BusinessVisibilitySerializer(
            instance=business,
            data=request.data,
        )
        business_before_change = BusinessChange.extract_vars(business)
        errors: List[BusinessVisibilityError] = []

        with transaction.atomic():
            validate_serializer(serializer)
            if errors:
                return self.return_error([er.asdict() for er in errors])
            serializer.save()
            BusinessChange.add(
                business,
                business,
                business_before_change,
                operator=self.request.user,
                handler=self,
                metadata={
                    'endpoint': f'{self.__class__.__name__}',
                },
            )

        # <editor-fold desc="early finish section">
        analytics_business_info_updated_task.delay(
            business_id=business_pk,
            context={
                'business_id': business_pk,
                'source_id': self.booking_source.id,
            },
        )
        # </editor-fold>

        return Response(
            data=serializer.data,
            status=status.HTTP_200_OK,
        )
