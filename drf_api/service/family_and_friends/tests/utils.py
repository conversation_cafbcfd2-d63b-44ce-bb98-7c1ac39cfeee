# pylint: disable=unreachable
from typing import NoReturn

import mock
import pytest
from rest_framework.test import APITestCase

from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature.security import HC<PERSON>tchaFlag
from lib.tests.utils import override_feature_flag
from webapps.booking.models import BookingSources
from webapps.consts import WEB
from webapps.family_and_friends.baker_recipes import (
    inactive_member_recipe,
    member_recipe,
)
from webapps.family_and_friends.enums import InvitationStatus, RelationshipType
from webapps.family_and_friends.models import (
    MemberInvitation,
    MemberProfile,
)
from webapps.family_and_friends.tests.utils import add_child, send_invite
from webapps.user.baker_recipes import customer_user
from webapps.user.enums import AuthOriginEnum


@override_feature_flag({HCaptchaFlag.flag_name: False})
@pytest.mark.django_db
class FamilyAndFriendsMixinTestCase(APITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.customer_web_source = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            name=WEB,
        )
        cls.headers = {'HTTP_X_API_KEY': cls.customer_web_source.api_key, 'format': 'json'}

    def setUp(self):
        super().setUp()
        self.user = customer_user.make(
            first_name='Bruce',
            last_name='Willis',
            email='<EMAIL>',
            cell_phone='+48 500 122 122',
        )
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.client.credentials(HTTP_X_ACCESS_TOKEN=self.session.session_key)


class MembersMixinTestCase:
    def get_response(self):
        self.invited = [
            {
                'id': mock.ANY,
                'full_name': 'Maciek',
                'first_name': 'Maciek',
                'last_name': None,
                'birthday': None,
                'email': None,
                'cell_phone': '+48 12 312 34 56',
                'relationship_type': RelationshipType.FRIEND,
                'relationship_label': RelationshipType.FRIEND.label,
                'photo': None,
                'is_booksy_user': False,
                'confirmed_member': False,
                'is_parent': False,
                'can_be_edited': False,
                'can_be_reinvited': False,
            },
        ]
        self.members = [
            {
                'id': mock.ANY,
                'full_name': 'Bruce Wayne',
                'first_name': 'Bruce',
                'last_name': 'Wayne',
                'birthday': None,
                'email': None,
                'cell_phone': '',
                'photo': None,
                'relationship_type': RelationshipType.CHILD,
                'relationship_label': RelationshipType.get_for_parent(RelationshipType.CHILD).label,
                'is_booksy_user': True,
                'confirmed_member': False,
                'is_parent': True,
                'can_be_edited': False,
                'can_be_reinvited': False,
            },
            {
                'id': mock.ANY,
                'full_name': 'burek',
                'first_name': 'burek',
                'last_name': None,
                'birthday': None,
                'relationship_type': RelationshipType.PET,
                'relationship_label': RelationshipType.PET.label,
                'photo': None,
                'is_booksy_user': False,
                'confirmed_member': True,
                'is_parent': False,
                'can_be_edited': True,
                'breed': None,
                'pet_type': 'kotek',
                'additional_info': None,
                'weight': None,
                'can_be_reinvited': False,
            },
            {
                'id': mock.ANY,
                'full_name': 'Franek Kimono',
                'first_name': 'Franek',
                'last_name': 'Kimono',
                'birthday': None,
                'email': None,
                'cell_phone': '',
                'relationship_type': RelationshipType.FRIEND,
                'relationship_label': RelationshipType.FRIEND.label,
                'photo': None,
                'is_booksy_user': True,
                'confirmed_member': True,
                'is_parent': False,
                'can_be_edited': False,
                'can_be_reinvited': False,
            },
            {
                'id': mock.ANY,
                'full_name': 'Stefan Siarzewski',
                'first_name': 'Stefan',
                'last_name': 'Siarzewski',
                'birthday': None,
                'email': None,
                'cell_phone': '',
                'relationship_type': RelationshipType.FRIEND,
                'relationship_label': RelationshipType.FRIEND.label,
                'photo': None,
                'is_booksy_user': False,
                'confirmed_member': True,
                'is_parent': False,
                'can_be_edited': True,
                'can_be_reinvited': False,
            },
        ]

    def create_member_profile_for_user(self, user) -> NoReturn:
        self.current_member = MemberProfile.from_user_profile(user.customer_profile)

    def create_members(self):
        self.member_1: MemberProfile = member_recipe.make(
            first_name='Franek',
            last_name='Kimono',
            cell_phone='+48123123123',
        )
        self.member_2: MemberProfile = inactive_member_recipe.make(
            first_name='Stefan',
            last_name='Siarzewski',
        )
        self.users_parent: MemberProfile = member_recipe.make(
            first_name='Bruce',
            last_name='Wayne',
            email='<EMAIL>',
            cell_phone='+48512345678',
        )

    def prepare_family_data(self) -> NoReturn:
        self.create_members()
        add_child(self.current_member, self.member_1)
        send_invite(self.current_member, self.member_1)
        self.member_1.received_invitations.filter(parent=self.current_member).update(
            status=InvitationStatus.ACCEPTED
        )
        add_child(self.current_member, self.member_2)
        add_child(self.users_parent, self.current_member, RelationshipType.CHILD)
        send_invite(self.users_parent, self.current_member)
        self.current_member.received_invitations.filter(parent=self.users_parent).update(
            status=InvitationStatus.ACCEPTED
        )

        inactive_member_1: MemberProfile = inactive_member_recipe.make(
            first_name='Maciek', cell_phone='+48123123456'
        )
        add_child(self.current_member, inactive_member_1)
        send_invite(self.current_member, inactive_member_1)

        inactive_member_2: MemberProfile = inactive_member_recipe.make(
            first_name='burek', additional_data={'pet_type': 'kotek'}
        )
        add_child(self.current_member, inactive_member_2, RelationshipType.PET)

        inactive_member_3: MemberProfile = inactive_member_recipe.make(first_name='Romek')
        add_child(self.member_1, inactive_member_3)
        self.get_response()

    def prepare_inactive_pet(self):
        inactive_member_2: MemberProfile = inactive_member_recipe.make(first_name='burek')
        add_child(self.current_member, inactive_member_2, RelationshipType.PET)

    @staticmethod
    def invitation_exist(member_id: int) -> bool:
        return MemberInvitation.objects.filter(member__id=member_id).first() is not None
