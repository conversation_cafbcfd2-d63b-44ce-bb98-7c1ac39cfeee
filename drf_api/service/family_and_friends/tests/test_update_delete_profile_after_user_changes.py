# pylint: disable=unreachable
import base64
import os

import pytest
from django.conf import settings
from model_bakery import baker
from rest_framework import status

from service.tests import BaseAsyncHTTPTest
from webapps.business.baker_recipes import bci_no_user_recipe, bci_recipe
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.family_and_friends.baker_recipes import (
    inactive_member_recipe,
    mbci_recipe,
    member_recipe,
)
from webapps.family_and_friends.enums import RelationshipType
from webapps.family_and_friends.models import (
    MemberBusinessCustomerInfo,
    MemberProfile,
)
from webapps.family_and_friends.tests.utils import add_child
from webapps.notification.models import NotificationSMSCodes
from webapps.user.baker_recipes import customer_user
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User


class UpdateDeleteUserBaseTest(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        user_data = dict(
            first_name='<PERSON>',
            last_name='<PERSON><PERSON><PERSON>',
            cell_phone='+***********',
            email='<EMAIL>',
        )
        self.user = customer_user.make(**user_data)
        self.user_member_profile = MemberProfile.from_user_profile(self.user.customer_profile)
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        for _ in range(3):
            bci = bci_recipe.make(
                user=self.user, email=user_data['email'], cell_phone=user_data['cell_phone']
            )
            mbci_recipe.make(member=self.user_member_profile, bci=bci)
        assert self.user_member_profile.bcis.count() == 3


@pytest.mark.django_db
class UpdateMemberAfterUserChangesTestCase(UpdateDeleteUserBaseTest):
    url = '/customer_api/me/?'

    def test_no_member_profile(self):
        self.user = customer_user.make(
            first_name='Bruce',
            last_name='Willis',
            email='<EMAIL>',
            cell_phone='+48511232232',
        )
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        body = {'first_name': 'Michael'}
        resp = self.fetch(self.url, body=body, method='PUT')
        assert resp.code == status.HTTP_200_OK
        assert User.objects.get(email='<EMAIL>').first_name == body['first_name']
        assert not hasattr(self.user.customer_profile, 'member_profile')

    def test_update_first_name(self):
        body = {'first_name': 'Michael'}
        resp = self.fetch(self.url, body=body, method='PUT')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['customer']['first_name'] == 'Michael'
        assert self.user.customer_profile.member_profile.first_name == body['first_name']
        assert (
            MemberProfile.objects.get(id=self.user_member_profile.id).first_name
            == body['first_name']
        )
        bcis = BusinessCustomerInfo.objects.filter(user_id=self.user.id)
        assert bcis.count() == 3
        for bci in bcis:
            assert bci.first_name == body['first_name']
        # todo: sprawdzić czy zmieniły się appointmenty, transakcje,

    def test_blank_first_name(self):
        body = {'first_name': ''}
        resp = self.fetch(self.url, body=body, method='PUT')
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json == {
            'errors': [
                {
                    'field': 'first_name',
                    'description': 'This field may not be blank.',
                    'code': 'blank',
                }
            ]
        }

    def test_update_phone(self):
        body = {'cell_phone': '+************', 'sms_code': '123123'}
        baker.make(
            NotificationSMSCodes,
            sms_code=body['sms_code'],
            phone=body['cell_phone'],
        )
        resp = self.fetch(self.url, body=body, method='PUT')
        new_cell_phone_format = '+**************'
        assert resp.code == status.HTTP_200_OK
        assert resp.json['customer']['cell_phone'] == new_cell_phone_format
        assert self.user.customer_profile.member_profile.cell_phone == new_cell_phone_format
        assert (
            MemberProfile.objects.get(id=self.user_member_profile.id).cell_phone
            == new_cell_phone_format
        )
        bcis = BusinessCustomerInfo.objects.filter(user_id=self.user.id)
        assert bcis.count() == 3
        for bci in bcis:
            assert bci.cell_phone == new_cell_phone_format
        # todo: sprawdzić czy zmieniły się appointmenty, transakcje,
        # todo: bci dla inactive member jest bez danych kontaktowych?
        # todo: zmiana danych kontaktowych rodzica == zmiana tych danych w bcis nieaktywnych dzieci?

    def test_update_email(self):
        body = {'email': '<EMAIL>'}
        self.user.email = body['email']
        self.user.save()
        assert self.user.customer_profile.member_profile.email == body['email']
        bcis = BusinessCustomerInfo.objects.filter(user_id=self.user.id)
        assert MemberProfile.objects.get(id=self.user_member_profile.id).email == body['email']
        assert bcis.count() == 3
        for bci in bcis:
            assert bci.email == body['email']
        # todo: sprawdzić czy zmieniły się appointmenty, transakcje,

    def test_update_photo(self):
        img_path = os.path.join(settings.PROJECT_PATH, 'statics', 'img', 'userBlank.png')
        with open(img_path, 'rb') as img_file:
            encoded_string = base64.b64encode(img_file.read())

        body = {'photo': encoded_string}
        resp = self.fetch(self.url, body=body, method='PUT')
        assert resp.code == 200
        img = 'img.booksy.pm'
        assert img in resp.json['customer']['photo']
        photo = MemberProfile.objects.filter(id=self.user_member_profile.id).first().photo
        assert photo is not None
        assert img in photo.image_url


@pytest.mark.django_db
class DeleteMemberAfterUserDeletingProfileTestCase(UpdateDeleteUserBaseTest):
    url = '/customer_api/me/?'
    deleted_email = 'deleted <EMAIL>'

    def _assert_after_delete(self, resp):
        assert resp.code == status.HTTP_200_OK
        assert not resp.json
        assert User.objects.get(first_name='Mike').email == self.deleted_email
        assert self.user.customer_profile.member_profile.email == self.deleted_email
        assert MemberBusinessCustomerInfo.objects.count() == 4
        # todo: appointmenty, transakcje,

    def test_delete_member_inactive_child_without_bcis(self):
        inactive_profile = inactive_member_recipe.make(first_name='Anthony')
        add_child(self.user_member_profile, inactive_profile, RelationshipType.CHILD)
        resp = self.fetch(self.url, method='DELETE')
        assert resp.code == status.HTTP_200_OK
        assert not resp.json
        assert User.objects.get(first_name='Mike').email == self.deleted_email
        assert self.user.customer_profile.member_profile.email == self.deleted_email
        inactive_profile.refresh_from_db()
        assert inactive_profile.deleted

    def test_delete_member_inactive_child_with_bcis(self):
        inactive_profile = inactive_member_recipe.make(first_name='Anthony')
        add_child(self.user_member_profile, inactive_profile, RelationshipType.CHILD)
        mbci_recipe.make(member=inactive_profile, bci=bci_no_user_recipe.make())
        resp = self.fetch(self.url, method='DELETE')
        self._assert_after_delete(resp)
        inactive_profile.refresh_from_db()
        assert inactive_profile.deleted
        assert BusinessCustomerInfo.objects.count() == 4
        assert inactive_profile.bcis.exists()

    def test_delete_member_active_child_with_bcis(self):
        child_profile = member_recipe.make(
            first_name='Mary', last_name='Corleone', cell_phone='+***********', email='<EMAIL>'
        )
        add_child(self.user_member_profile, child_profile)
        mbci_recipe.make(
            member=child_profile, bci=bci_recipe.make(user=child_profile.user_profile.user)
        )
        assert child_profile.bcis.count() == 1
        resp = self.fetch(self.url, method='DELETE')
        child_profile.refresh_from_db()
        assert resp.code == status.HTTP_200_OK
        assert not resp.json
        assert User.objects.get(first_name='Mike').email == self.deleted_email
        assert self.user.customer_profile.member_profile.email == self.deleted_email
        assert MemberBusinessCustomerInfo.objects.count() == 4
        assert MemberProfile.objects.filter(first_name='Mary').exists()
        assert BusinessCustomerInfo.objects.count() == 4
        assert child_profile.bcis.count() == 1

    def test_delete_member_check_parent(self):
        parent_profile = member_recipe.make(
            first_name='Vito', last_name='Corleone', cell_phone='+***********', email='<EMAIL>'
        )
        add_child(parent_profile, self.user_member_profile)
        mother_profile = member_recipe.make(
            first_name='Carmi', last_name='Corleone', cell_phone='+***********', email='<EMAIL>'
        )
        add_child(parent_profile, mother_profile, RelationshipType.SPOUSE)
        mbci_recipe.make(
            member=parent_profile, bci=bci_recipe.make(user=parent_profile.user_profile.user)
        )
        resp = self.fetch(self.url, method='DELETE')
        assert resp.code == status.HTTP_200_OK
        assert not resp.json
        assert User.objects.get(first_name='Mike').email == self.deleted_email
        assert MemberProfile.objects.filter(first_name='Vito').exists()
        assert BusinessCustomerInfo.objects.count() == 4

    def test_delete_member_without_member_profile(self):
        self.user = customer_user.make(
            first_name='Bruce', last_name='Willis', email='<EMAIL>'
        )
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        bci_recipe.make(user=self.user)
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(self.url, method='DELETE')
        assert resp.code == status.HTTP_200_OK
        assert not resp.json
        assert not hasattr(self.user.customer_profile, 'member_profile')
        deleted_email = 'deleted <EMAIL>'
        user = User.objects.get(id=self.user.id)
        assert user.email == deleted_email
        assert user.business_customer_infos.count() == 1
        assert BusinessCustomerInfo.objects.get(user_id=self.user.id).email == deleted_email
