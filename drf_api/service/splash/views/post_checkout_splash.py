from bo_obs.datadog.enums import BooksyTeams
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionAPIView
from drf_api.service.business.validators.access import get_business_staffer_validator
from drf_api.service.splash.serializers import (
    get_splash_checkout_serializer,
)
from webapps.pos.models import Transaction


class PostCheckoutSplashView(BaseBooksySessionAPIView):
    permission_classes = (IsAuthenticated,)
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    def get(self, request, business_pk, transaction_pk):
        validator = get_business_staffer_validator(
            business=business_pk,
            request=request,
            user=request.user,
        )
        validator.validate()

        transaction = Transaction.objects.filter(id=transaction_pk).first()
        serializer_class = get_splash_checkout_serializer(transaction.pos)
        serializer = serializer_class(
            instance=transaction,
            context={
                'user': self.request.user,
                'business_pk': business_pk,
            },
        )
        return Response(serializer.data)
