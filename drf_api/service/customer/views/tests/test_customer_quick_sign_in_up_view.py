from django.shortcuts import reverse
from django.test.utils import override_settings
from mock import patch
from model_bakery import baker
from rest_framework import status
from rest_framework.test import APITestCase

from country_config.enums import Country
from drf_api.service.customer.serializers import generate_creation_token
from lib.baker_utils import get_or_create_booking_source
from lib.tests.utils import override_eppo_feature_flag
from lib.email_internal import PRIVATE_EMAIL_USERNAME
from lib.email_internal import PRIVATE_EMAIL_DOMAIN
from lib.email_internal import generate_private_email
from lib.feature_flag.feature.customer import PhoneRegistrationNameFlag
from webapps.booking.models import BookingSources
from webapps.user.baker_recipes import customer_user
from webapps.user.const import FIXED_DEV_SMS_CODE
from webapps.user.enums import RegistrationSource
from webapps.user.models import User
from webapps.user.models import UserInternalData
from webapps.user.models import UserPolicyAgreement
from webapps.user.models import UserSessionCache


class TestCustomerQuickSignInUp(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.url = reverse('customer_quick_sign_in_up')
        cls.customer_booking_source = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )

    def setUp(self):
        self.client.credentials(HTTP_X_API_KEY=self.customer_booking_source.api_key)

    def post(self, data):
        return self.client.post(self.url, data=data, format='json')

    @override_settings(API_COUNTRY=Country.PL)
    def _test_signup_user(self, user_agreements=None):
        data = {
            'cell_phone': '500 600 700',
            'sms_code': FIXED_DEV_SMS_CODE,
        }
        if user_agreements:
            data['user_agreements'] = user_agreements

        response = self.post(data=data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        user = User.objects.last()
        self.assertTrue(user.password.startswith('!'))
        self.assertTrue(user.email.endswith(PRIVATE_EMAIL_DOMAIN))
        self.assertTrue(user.email.startswith(PRIVATE_EMAIL_USERNAME))
        self.assertEqual(user.cell_phone, data['cell_phone'])
        response_data = response.json()
        account = response_data.pop('customer')
        self.assertEqual(account['email'], user.email)
        creation_token = response_data.pop('creation_token')
        self.assertTrue(len(creation_token), 32)
        session = UserSessionCache.objects.filter(user=user).last()
        self.assertTrue(user.internal_data.registration_source, RegistrationSource.PHONE_NUMBER)
        self.assertEqual(
            response_data,
            {
                'access_rights': None,
                'access_token': session.session_id,
                'superuser': False,
                'password_change_required': False,
            },
        )
        return user

    def test_signup_user(self):
        self._test_signup_user()
        self.assertFalse(UserPolicyAgreement.objects.exists())

    @patch('webapps.user.models.User.create_session', lambda *args, **kwargs: None)
    def test_signup_user_with_booksy_auth_fail(self):
        data = {
            'cell_phone': '+***********',
            'sms_code': FIXED_DEV_SMS_CODE,
        }
        response = self.post(data=data)

        response_data = response.json()
        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response_data)
        self.assertTrue(response_data['creation_token'])
        self.assertIsNone(response_data['access_token'])

    def test_signup_user_with_user_agreements(self):
        user_agreements = [
            {
                'name': 'privacy_policy_agreement',
                'value': True,
            },
            {
                'name': 'marketing_agreement',
                'value': True,
            },
            {
                'name': 'partner_marketing_agreement',
                'value': True,
            },
        ]
        user = self._test_signup_user(user_agreements=user_agreements)
        self.assertTrue(UserPolicyAgreement.objects.filter(user_id=user.id).exists())

    def test_invalid_user_agreements(self):
        data = {
            'user_agreements': 'invalid',
            'cell_phone': '+48 500 600 700',
            'sms_code': FIXED_DEV_SMS_CODE,
        }

        response = self.post(data)

        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'code': 'not_a_list',
                        'description': 'Expected a list of items but got type "str".',
                        'field': 'user_agreements.non_field_errors',
                    },
                ],
            },
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_empty_body(self):
        response = self.client.post(self.url)

        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'cell_phone',
                        'description': 'This field is required.',
                        'code': 'required',
                    },
                    {
                        'field': 'sms_code',
                        'description': 'This field is required.',
                        'code': 'required',
                    },
                ]
            },
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_empty_invalid_phone(self):
        data = {
            'cell_phone': 50,
            'sms_code': 0,
        }

        response = self.post(data=data)

        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'cell_phone',
                        'description': (
                            'We don\'t recognize this number. Add your country code and try again.'
                        ),
                        'code': 'invalid',
                    }
                ]
            },
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_signin_user_invalid_creation_token(self):
        cell_phone = '+***********'
        customer_user.make(
            email=generate_private_email(cell_phone),
            cell_phone=cell_phone,
        )
        data = {
            'cell_phone': cell_phone,
            'sms_code': FIXED_DEV_SMS_CODE,
        }

        response = self.post(data=data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        msg_err_used = 'Phone number already registered. Please use email or social login.'
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'creation_token',
                        'description': msg_err_used,
                        'code': 'required',
                    }
                ]
            },
        )
        data['creation_token'] = None
        response = self.post(data=data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'creation_token',
                        'description': msg_err_used,
                        'code': 'null',
                    }
                ]
            },
        )
        data['creation_token'] = 'to_short'
        response = self.post(data=data)

        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'creation_token',
                        'description': 'Ensure this field has at least 32 characters.',
                        'code': 'min_length',
                    }
                ]
            },
        )

        data['creation_token'] = 'x' * 32
        response = self.post(data=data)

        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'creation_token',
                        'description': 'Incorrect verification token',
                        'code': 'invalid',
                    }
                ]
            },
        )

    @patch('drf_api.mixins.logger_account.warning')
    def test_signin_user(self, mocked_log):
        cell_phone = '+***********'
        user = customer_user.make(
            email=generate_private_email(cell_phone),
            cell_phone=cell_phone,
        )
        data = {
            'cell_phone': cell_phone,
            'sms_code': FIXED_DEV_SMS_CODE,
            'creation_token': generate_creation_token(user),
        }

        response = self.post(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        account = response_data.pop('customer')
        self.assertEqual(account['email'], user.email)
        session = UserSessionCache.objects.filter(user=user).last()
        self.assertEqual(
            response_data,
            {
                'access_rights': None,
                'access_token': session.session_id,
                'superuser': False,
                'password_change_required': False,
            },
        )
        self._check_log_event_success(mocked_log, account['id'], cell_phone)

    def _check_log_event_success(self, mocked_log, user_id, cell_phone):
        self.assertEqual(mocked_log.call_count, 2)
        call_0, call_1 = mocked_log.call_args_list
        self.assertEqual(call_0[1]['msg'], '[LOGIN - ATTEMPT] ')
        call_kwargs = call_1[1]
        self.assertEqual(call_kwargs['msg'], f'[LOGIN - LOGIN SUCCESS] User {user_id} logged in')
        self.assertEqual(
            call_kwargs['extra']['login_data'],
            f'LoginData: EMAIL: None, FINGERPRINT: None, PROFILE_TYPE: C, PHONE: {cell_phone}',
        )
        self.assertEqual(call_kwargs['extra']['request_url'], self.url)
        self.assertIsNotNone(call_kwargs['extra']['private_email'])

    def test_signin_existing_not_private_email_phone_source(self):
        # user create account in quick registration, changed email and try to use this endpoint
        cell_phone = '+***********'
        baker.make(
            UserInternalData,
            registration_source=RegistrationSource.PHONE_NUMBER,
            user=customer_user.make(
                email='<EMAIL>',
                cell_phone=cell_phone,
            ),
        )
        data = {
            'cell_phone': cell_phone,
            'sms_code': FIXED_DEV_SMS_CODE,
        }

        response = self.post(data=data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'non_field_errors',
                        'description': (
                            'Phone number already registered. Please use email or social login.'
                        ),
                        'code': 'quick_sign_in_up_not_allowed',
                    },
                ]
            },
        )

    def test_signin_existing_not_private_email_other_source(self):
        cell_phone = '+48600700801'
        user = customer_user.make(
            email='<EMAIL>',
            cell_phone=cell_phone,
        )
        baker.make(
            UserInternalData,
            user=user,
            registration_source=RegistrationSource.APPLE,
        )
        data = {
            'cell_phone': cell_phone,
            'sms_code': FIXED_DEV_SMS_CODE,
        }

        response = self.post(data=data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    @override_eppo_feature_flag({PhoneRegistrationNameFlag.flag_name: True})
    def test_signup_user_with_name(self):
        for name, cell_phone, expected_first_name, expected_last_name in self._generate_test_data():
            data = {
                'cell_phone': cell_phone,
                'sms_code': FIXED_DEV_SMS_CODE,
                'name': name,
            }

            response = self.post(data=data)

            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            user = User.objects.last()
            self.assertEqual(user.first_name, expected_first_name)
            self.assertEqual(user.last_name, expected_last_name)
            response_data = response.json()
            self.assertEqual(response_data['customer']['first_name'], user.first_name)
            self.assertEqual(response_data['customer']['last_name'], user.last_name)

    def _generate_test_data(self):
        return [
            ('John Books', '+48123456789', 'John', 'Books'),
            ('John', '+48234567891', 'John', ''),
            ('Alejandro Carlos Jose', '+48800900101', 'Alejandro', 'Carlos Jose'),
            (None, '+48800900102', '', ''),
            ('', '+48800900103', '', ''),
            (' John Books', '+48800900104', 'John', 'Books'),
            (' Alejandro Carlos Jose', '+48800900105', 'Alejandro', 'Carlos Jose'),
        ]

    @override_eppo_feature_flag({PhoneRegistrationNameFlag.flag_name: False})
    def test_sigup_user_with_name_flag_turned_off(self):
        data = {
            'cell_phone': '+48800900104',
            'sms_code': FIXED_DEV_SMS_CODE,
            'name': 'John Books',
        }

        response = self.post(data=data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        user = User.objects.last()
        self.assertEqual(user.first_name, '')
        self.assertEqual(user.last_name, '')
        response_data = response.json()
        self.assertEqual(response_data['customer']['first_name'], '')
        self.assertEqual(response_data['customer']['last_name'], '')
