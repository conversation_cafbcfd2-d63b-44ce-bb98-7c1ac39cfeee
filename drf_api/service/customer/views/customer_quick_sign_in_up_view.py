from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from rest_framework import status
from rest_framework.views import Request, Response

from drf_api.base_views import BaseBooksyNoSessionGenericAPIView
from drf_api.mixins import ExtendedLogsMixin
from drf_api.service.customer.serializers import (
    CustomerQuickSignInSerializer,
    CustomerQuickSignInUpRequestSerializer,
    CustomerQuickSignUpSerializer,
    generate_creation_token,
)
from lib.feature_flag.feature import CustomerQuickSignInUpFlag
from service.mixins.throttling import BooksyDrfScopedThrottle, ThrottleScopeEnum
from webapps.segment.tasks import analytics_customer_registration_completed_task
from webapps.user.enums import AuthOriginEnum, RegistrationSource
from webapps.user.models import User, UserInternalData, UserProfile
from webapps.user.tasks.sync import sync_user_booksy_auth_proxy
from webapps.versum_migration.user_connection.customer_registration import (
    versum_migration_handle_customer_registration_completed,
)


class CustomerQuickSignInUpView(BaseBooksyNoSessionGenericAPIView, ExtendedLogsMixin):
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)
    throttle_scope = ThrottleScopeEnum.ACCOUNT_CREATE
    throttle_classes = (BooksyDrfScopedThrottle,)
    serializer_class = CustomerQuickSignInUpRequestSerializer

    def post(self, request):
        if not CustomerQuickSignInUpFlag():
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={'detail': 'Disabled view by feature flag'},
            )

        self.log_sign_in_attempt()
        validated_data = self._get_validated_data(request)
        user, created = self._get_or_create_user(validated_data)
        self.log_sign_in_success(user.id, private_email=user.email)
        profile = user.profiles.get(profile_type=self.profile_type)
        sync_user_booksy_auth_proxy([user.id])  # only sync user allow for trash password
        session = user.create_session(origin=AuthOriginEnum.SMS, fingerprint=self.fingerprint)

        response = {
            'access_rights': None,
            'access_token': session and session.session_key,
            'customer': user.format_account(profile),
            'password_change_required': False,
            'superuser': False,
        }
        if created:
            response['creation_token'] = generate_creation_token(user)
            self._registration_completed_tasks(
                user, profile, request.data.get('invited_by_business_id')
            )

        return Response(
            status=status.HTTP_201_CREATED if created else status.HTTP_200_OK,
            data=response,
        )

    def _get_or_create_user(self, validated_data):
        user = User.objects.filter(email=validated_data['email']).first()
        serializer_cls = CustomerQuickSignInSerializer if user else CustomerQuickSignUpSerializer
        validated_data['cell_phone'] = validated_data['cell_phone'].db_format

        serializer = serializer_cls(
            instance=user,
            data=validated_data,
            context={
                'booking_source': self.booking_source,
                'language': self.language or settings.LANGUAGE_CODE[:2].lower(),
                'user_agreements': validated_data.pop('user_agreements', None),
            },
        )
        serializer.is_valid(raise_exception=True)
        if created := not user:
            user = serializer.save()
            UserInternalData.objects.create(
                user=user,
                registration_source=RegistrationSource.PHONE_NUMBER,
            )

        return user, created

    def _get_validated_data(self, request: Request) -> dict:
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data

    def _registration_completed_tasks(
        self,
        user: User,
        profile: UserProfile,
        invited_by_business_id: int,
    ):
        analytics_customer_registration_completed_task.delay(
            user_id=profile.user_id,
            invited_by_business_id=invited_by_business_id,
            context={
                'session_user_id': profile.user_id,
                'source_id': self.booking_source.id,
            },
        )
        versum_migration_handle_customer_registration_completed(user_id=user.id)
