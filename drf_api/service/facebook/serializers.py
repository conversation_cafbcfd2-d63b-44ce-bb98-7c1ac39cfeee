from django.core.validators import ProhibitNullCharactersValidator
from django.utils.translation import gettext as _
from rest_framework import serializers

from drf_api.base_serializers import BooksyValidationMixin
from lib.fields.name_field import NameField


class BaseFacebookSignInRequestSerializer(BooksyValidationMixin, serializers.Serializer):
    id_token = serializers.CharField(
        validators=[ProhibitNullCharactersValidator()],
        required=True,
        help_text='A JSON web token containing the user’s identify information.',
        max_length=2_000,
    )
    nonce = serializers.CharField(
        validators=[ProhibitNullCharactersValidator()],
        required=True,
        help_text=(
            'The value that associates a client session and an ID token. '
            'This value mitigates replay attacks and is present only if passed during the '
            'authorization request.'
        ),
    )


class FacebookSignInCustomerRequestSerializer(BaseFacebookSignInRequestSerializer):
    cell_phone = serializers.Char<PERSON>ield(
        required=False,
        help_text=' Cell phone number. Required only for registration',
    )
    sms_code = serializers.CharField(
        required=False,
        help_text='SMS code (for confirming cell phone number). Required only for registration',
    )
    invited_by_business_id = serializers.IntegerField(
        required=False,
        help_text='The ID of the inviting business.',
        allow_null=True,
        default=None,
    )


class FacebookGraphSignInCustomerRequestSerializer(BooksyValidationMixin, serializers.Serializer):
    id = serializers.CharField(max_length=128)
    first_name = NameField(
        error_messages={'invalid': _('Invalid first name.')},
        allow_blank=True,
    )
    last_name = NameField(
        error_messages={'invalid': _('Invalid last name.')},
        allow_blank=True,
    )
    email = serializers.EmailField(required=True)
    cell_phone = serializers.CharField(
        required=False,
        help_text='Cell phone number. Required only for registration',
    )
    sms_code = serializers.CharField(
        required=False,
        help_text='SMS code (for confirming cell phone number). Required only for registration',
    )
    invited_by_business_id = serializers.IntegerField(
        required=False,
        help_text='The ID of the inviting business.',
        allow_null=True,
        default=None,
    )


class FacebookConnectCustomerRequestSerializer(BaseFacebookSignInRequestSerializer):
    pass


class FacebookConnectBusinessRequestSerializer(BaseFacebookSignInRequestSerializer):
    pass


class FacebookSignInBusinessRequestSerializer(BaseFacebookSignInRequestSerializer):
    pass
