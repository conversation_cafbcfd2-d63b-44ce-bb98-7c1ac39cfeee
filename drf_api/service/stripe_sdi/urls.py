from django.urls import path

from drf_api.service.stripe_sdi.views import (
    StripeCancelReaderActionView,
    StripeChangeIntegrationModeView,
    StripeProcessPaymentIntentView,
    StripeGetPaymentStatusAPIView,
)

urlpatterns = [
    path(
        'terminal/<str:reader_id>/payment_intent/<int:intent_pk>/process/',  # pylint: disable=line-too-long
        StripeProcessPaymentIntentView.as_view(),
        name='stripe_sdi_process_payment_intent',
    ),
    path(
        'terminal/<str:reader_id>/action/cancel/',
        StripeCancelReaderActionView.as_view(),
        name='stripe_sdi_cancel_reader_action',
    ),
    path(
        'integration_mode/change/',
        StripeChangeIntegrationModeView.as_view(),
        name='stripe_sdi_change_integration_mode',
    ),
    path(
        'payment_intent/<int:intent_pk>/status/',
        StripeGetPaymentStatusAPIView.as_view(),
        name='stripe_sdi_payment_intent_status',
    ),
]
