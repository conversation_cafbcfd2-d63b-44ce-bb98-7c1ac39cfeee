<!DOCTYPE html>
<html>
<style>
    .name-selector {
        margin-top: 20px;
        float: left;
        margin-left: 1000px;
}
     .booking-sources-selector {
        float: left;
        margin-top: 18px;
        margin-left: 3px;
        margin-bottom: 10px;
}
</style>
  <head>
    <title>Swagger Booksy DRF API</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" type="text/css" href="//unpkg.com/swagger-ui-dist@3/swagger-ui.css" />
  </head>
  <body>
  <div class="swagger-ui name-selector"><b>Please pick your application source </b></div>
    <div class="swagger-ui booking-sources-selector">
      <select name="booking_sources" id="id-booking-sources">
        <option value="">-- No API KEY --</option>
        {% for e in booking_sources %}
        <option value="{{ e.api_key }}">{{ e.name }}</option>
        {% endfor %}
      </select>
  </div>
    <div id="swagger-ui" style="clear: both;"></div>
    <script src="//unpkg.com/swagger-ui-dist@3/swagger-ui-bundle.js"></script>
    <script>
    const ui = SwaggerUIBundle({
        url: "{% url schema_url %}",
        dom_id: '#swagger-ui',
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIBundle.SwaggerUIStandalonePreset
        ],
        layout: "BaseLayout",
        requestInterceptor: (request) => {
          request.headers['X-CSRFToken'] = "{{ csrf_token }}";
          request.headers['X-API-KEY'] = document.getElementById("id-booking-sources").value;
          return request;
        }
      })
    </script>
  </body>
</html>
