from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet, ViewSet

from bo_obs.datadog.mixins import DatadogAPMTagDRFMixin

from lib.datadog.mixins import BooksyTeamInRootSpanDRFMixin

from drf_api.mixins import (
    BookingSourceMixin,
    BooksyViewSessionMixin,
    InsertUserProfileMixin,
    ProfileTypePathMixin,
    QuerySerializerMixin,
    ResponseSerializerMixin,
    ViewPropertyMixin,
)


class BaseBooksyNoSessionRequiredMixin(
    DatadogAPMTagDRFMixin,
    BooksyTeamInRootSpanDRFMixin,
    BookingSourceMixin,
    ProfileTypePathMixin,
    ViewPropertyMixin,
):
    """Reserved for future enhancements"""


class BaseBooksySessionRequiredMixin(
    DatadogAPMTagDRFMixin,
    BooksyTeamInRootSpanDRFMixin,
    BookingSourceMixin,
    ProfileTypePathMixin,
    InsertUserProfileMixin,
    ViewPropertyMixin,
    BooksyViewSessionMixin,
):
    """Reserved for future enhancements"""


class BaseBooksySessionAPIView(
    BaseBooksySessionRequiredMixin,
    APIView,
):
    """Reserved for future enhancements"""


class BaseBooksyNoSessionApiView(
    BaseBooksyNoSessionRequiredMixin,
    APIView,
):
    """Reserved for future enhancements"""

    authentication_classes = ()


class BaseBooksySessionGenericAPIView(BaseBooksySessionRequiredMixin, GenericAPIView):
    """Reserved for future enhancements"""


class BaseBooksyNoSessionGenericAPIView(BaseBooksyNoSessionRequiredMixin, GenericAPIView):
    """Reserved for future enhancements"""

    authentication_classes = ()


class BaseBooksySessionGenericViewSet(
    BaseBooksySessionRequiredMixin,
    GenericViewSet,
):
    """Reserved for future enhancements"""


class BaseBooksyNoSessionGenericViewSet(
    BaseBooksyNoSessionRequiredMixin,
    GenericViewSet,
):
    """Reserved for future enhancements"""

    authentication_classes = ()


class BaseBooksySessionViewSet(BaseBooksySessionRequiredMixin, ViewSet):
    """Reserved for future enhancements"""


class BaseBooksyNoSessionViewSet(BaseBooksyNoSessionRequiredMixin, ViewSet):
    """Reserved for future enhancements"""

    authentication_classes = ()


class RetrieveQueryGenericAPIView(GenericAPIView, QuerySerializerMixin):
    def validate_query(self):
        data = self.kwargs.copy()
        data.update(self.request.query_params.dict())
        query_serializer = self.get_query_serializer(data=data)
        query_serializer.is_valid(raise_exception=True)
        self.validated_data = query_serializer.validated_data

    def query(self, request, *args, **kwargs):
        self.validate_query()
        instance = self.get_object()
        data = self.get_serializer(instance=instance).data
        return Response(status=status.HTTP_200_OK, data=data)

    def get(self, request, *args, **kwargs):
        return self.query(request, *args, **kwargs)


class RetrievePostQueryAPIView(GenericAPIView, ResponseSerializerMixin):
    def validate_body(self):
        data = self.kwargs.copy()
        data.update(self.data)
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.validated_data = serializer.validated_data

    def query(self, request, *args, **kwargs):
        self.validate_body()
        instance = self.get_object()
        data = self.get_response_serializer(instance=instance)
        return Response(status=status.HTTP_200_OK, data=data)
