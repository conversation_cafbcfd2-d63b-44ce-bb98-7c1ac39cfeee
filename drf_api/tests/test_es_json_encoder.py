from datetime import date, datetime, timedelta

from django.shortcuts import reverse
from django.urls import path
from rest_framework.status import HTTP_200_OK
from rest_framework.test import APITestCase, URLPatternsTestCase
from rest_framework.views import Response

from drf_api.base_views import BaseBooksyNoSessionApiView
from webapps.business.baker_recipes import business_recipe
from webapps.business.models.models import Business


class MyView(BaseBooksyNoSessionApiView):
    API_KEY_REQUIRED = False

    def get(self, request):
        business_document = Business.objects.get(
            id=request.query_params.get('business_id')
        ).get_document()
        response = {
            'datetime': datetime(2022, 10, 11, 5, 6),
            'date': date(2022, 2, 3),
            'timedelta': timedelta(0, 5, 2),
            'AttrList': business_document.availability_utt,
            'AttrDict': business_document.amenities,
        }
        return Response(response)


class TestJsonEncoder(APITestCase, URLPatternsTestCase):
    urlpatterns = [path('view/', MyView.as_view(), name='my_view')]

    @classmethod
    def setUpTestData(cls):
        cls.business = business_recipe.make()

    def test_encode_special_es_json_encoder_types(self):
        business_document = self.business.get_document()
        expected_data = {
            'datetime': '2022-10-11T05:06:00',
            'date': '2022-02-03',
            'timedelta': '5.000002',
            'AttrList': list(business_document.availability_utt),
            'AttrDict': business_document.amenities.to_dict(),
        }
        response = self.client.get(
            reverse('my_view'),
            data={'business_id': self.business.id},
            format='json',
        )
        assert response.status_code == HTTP_200_OK
        assert response.json() == expected_data
