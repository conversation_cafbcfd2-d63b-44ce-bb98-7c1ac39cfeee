from sre_performance.tools import set_performance_test_flag


def test_performance_test_flag_correct():
    assert set_performance_test_flag('test_perf') is True
    assert set_performance_test_flag('test_performancetesting1-t1') is True
    assert set_performance_test_flag('test_47f5a20b-141118-ci') is True


def test_performance_test_flag_incorrect():
    assert set_performance_test_flag('live') is False
    assert set_performance_test_flag('dev') is False
    assert set_performance_test_flag('test_abc-123-ci') is False
